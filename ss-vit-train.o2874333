==================== NQS 训练任务 ====================
Job start at: Thu Nov 13 12:53:44 +08 2025
Running on node: hpc-pinaki-gpu2
Working directory: /home/<USER>/Repositories/Shastry-Sutherland_ViT
GPU Information:
Thu Nov 13 12:53:45 2025       
+-----------------------------------------------------------------------------------------+
| NVIDIA-SMI 580.65.06              Driver Version: 580.65.06      CUDA Version: 13.0     |
+-----------------------------------------+------------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id          Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |           Memory-Usage | GPU-Util  Compute M. |
|                                         |                        |               MIG M. |
|=========================================+========================+======================|
|   0  NVIDIA H200 NVL                On  |   00000000:B0:00.0 Off |                    0 |
| N/A   36C    P0             70W /  600W |       0MiB / 143771MiB |      0%      Default |
|                                         |                        |             Disabled |
+-----------------------------------------+------------------------+----------------------+

+-----------------------------------------------------------------------------------------+
| Processes:                                                                              |
|  GPU   GI   CI              PID   Type   Process name                        GPU Memory |
|        ID   ID                                                               Usage      |
|=========================================================================================|
|  No running processes found                                                             |
+-----------------------------------------------------------------------------------------+
读取配置文件: workflows/configs/train.yaml
Loading modules...
Anaconda 2025 python3 module loaded.

Please run the following commands (include the quote): eval "$(/usr/local/anaconda2025/bin/conda shell.bash hook)"

Loading anaconda2025/2025
  Loading requirement: cuda/12.2
Using GPU device: 0
Python path: /home/<USER>/.conda/envs/netket/bin/python
Python version: Python 3.12.11
Current conda environment: netket
==================== 开始训练任务 ====================
训练参数配置:
L values: 6
J2 values: 1.0
J1 values: 0.8
Model configs: 4,32,4,2
Learning rate: [1e-7, 0.05]
Warm-up ratio: 0.03
Samples: 8192
Parallel chains: 8192
Max exchange distance (d_max): auto (from lattice)
Dropout rate: 0
Mixed precision: true
Checkpoint args: --enable_checkpoint --save_interval 100 --keep_history --resume_from /home/<USER>/Repositories/Shastry-Sutherland_ViT/saved_models/L=6/J2=1.00/J1=0.80/checkpoints/checkpoint_iter_002400.pkl
==================== 模型: Layers=4, d_model=32, n_heads=4, patch_size=2 ====================
Starting training L=6, J2=1.0, J1=0.8, Layers=4, d_model=32 at: Thu Nov 13 12:53:50 +08 2025
W1113 12:58:17.516992 1667708 gemm_fusion_autotuner.cc:1160] Compiling 36 configs for gemm_fusion_dot on a single thread.
W1113 12:58:19.804158 1667708 gemm_fusion_autotuner.cc:1160] Compiling 36 configs for gemm_fusion_dot on a single thread.
============================================================
Shastry-Sutherland模型训练 (ViT + MinSR)
============================================================
系统参数: L=6, J1=0.8, J2=1.0
ViT模型: d_model=32, layers=4, heads=4, patch=2
优化器: diag_shift=0.005, grad_clip=1.0
学习率调度: CosineAnnealing([1e-07, 0.05])
退火参数: 周期数=3, 初始周期=100, 倍增=2.0
Checkpoint: 启用
Sharding: True
设备: [CudaDevice(id=0)]
============================================================
[2025-11-13 12:54:06] ✓ 从checkpoint恢复: saved_models/L=6/J2=1.00/J1=0.80/checkpoints/checkpoint_iter_002400.pkl
[2025-11-13 12:54:06]   - 迭代次数: 2400
[2025-11-13 12:54:06]   - 能量: -63.791477+0.013207j ± 0.017566, Var: 2.527700
[2025-11-13 12:54:06]   - 接受率: 0.2838
[2025-11-13 12:54:06]   - 时间戳: 2025-11-12T11:46:13.553942+08:00
[2025-11-13 12:54:17] ✓ 创建部分等变ViT模型（嵌入层等变）
[2025-11-13 12:54:17]   对称性组大小: 8 (C4v点群)
[2025-11-13 12:54:17]   嵌入层使用等变权重共享，后续Transformer层使用普通结构
L=6, J2=1.0, J1=0.8 的模拟失败: 
The structure of the parameters does not match the expected structure.

Expected structure: PyTreeDef({'Encoder_0': {'layers_0': {'attn': {'k_proj': {'kernel': *}, 'out_proj': {'kernel': *}, 'q_proj': {'kernel': *}, 'relative_position_bias': *, 'v_proj': {'kernel': *}}, 'dense1': {'bias': *, 'kernel': *}, 'dense2': {'bias': *, 'kernel': *}, 'layer_norm_1': {'bias': *, 'scale': *}, 'layer_norm_2': {'bias': *, 'scale': *}}, 'layers_1': {'attn': {'k_proj': {'kernel': *}, 'out_proj': {'kernel': *}, 'q_proj': {'kernel': *}, 'relative_position_bias': *, 'v_proj': {'kernel': *}}, 'dense1': {'bias': *, 'kernel': *}, 'dense2': {'bias': *, 'kernel': *}, 'layer_norm_1': {'bias': *, 'scale': *}, 'layer_norm_2': {'bias': *, 'scale': *}}, 'layers_2': {'attn': {'k_proj': {'kernel': *}, 'out_proj': {'kernel': *}, 'q_proj': {'kernel': *}, 'relative_position_bias': *, 'v_proj': {'kernel': *}}, 'dense1': {'bias': *, 'kernel': *}, 'dense2': {'bias': *, 'kernel': *}, 'layer_norm_1': {'bias': *, 'scale': *}, 'layer_norm_2': {'bias': *, 'scale': *}}, 'layers_3': {'attn': {'k_proj': {'kernel': *}, 'out_proj': {'kernel': *}, 'q_proj': {'kernel': *}, 'relative_position_bias': *, 'v_proj': {'kernel': *}}, 'dense1': {'bias': *, 'kernel': *}, 'dense2': {'bias': *, 'kernel': *}, 'layer_norm_1': {'bias': *, 'scale': *}, 'layer_norm_2': {'bias': *, 'scale': *}}}, 'HyperGNNEquivariantEmbed_0': {'gnn_0_edge_mlp_b_0': *, 'gnn_0_edge_mlp_b_1': *, 'gnn_0_edge_mlp_b_10': *, 'gnn_0_edge_mlp_b_11': *, 'gnn_0_edge_mlp_b_12': *, 'gnn_0_edge_mlp_b_13': *, 'gnn_0_edge_mlp_b_14': *, 'gnn_0_edge_mlp_b_15': *, 'gnn_0_edge_mlp_b_16': *, 'gnn_0_edge_mlp_b_17': *, 'gnn_0_edge_mlp_b_18': *, 'gnn_0_edge_mlp_b_19': *, 'gnn_0_edge_mlp_b_2': *, 'gnn_0_edge_mlp_b_20': *, 'gnn_0_edge_mlp_b_21': *, 'gnn_0_edge_mlp_b_22': *, 'gnn_0_edge_mlp_b_23': *, 'gnn_0_edge_mlp_b_24': *, 'gnn_0_edge_mlp_b_25': *, 'gnn_0_edge_mlp_b_26': *, 'gnn_0_edge_mlp_b_27': *, 'gnn_0_edge_mlp_b_28': *, 'gnn_0_edge_mlp_b_29': *, 'gnn_0_edge_mlp_b_3': *, 'gnn_0_edge_mlp_b_30': *, 'gnn_0_edge_mlp_b_31': *, 'gnn_0_edge_mlp_b_4': *, 'gnn_0_edge_mlp_b_5': *, 'gnn_0_edge_mlp_b_6': *, 'gnn_0_edge_mlp_b_7': *, 'gnn_0_edge_mlp_b_8': *, 'gnn_0_edge_mlp_b_9': *, 'gnn_0_edge_mlp_w_0': *, 'gnn_0_edge_mlp_w_1': *, 'gnn_0_edge_mlp_w_10': *, 'gnn_0_edge_mlp_w_11': *, 'gnn_0_edge_mlp_w_12': *, 'gnn_0_edge_mlp_w_13': *, 'gnn_0_edge_mlp_w_14': *, 'gnn_0_edge_mlp_w_15': *, 'gnn_0_edge_mlp_w_16': *, 'gnn_0_edge_mlp_w_17': *, 'gnn_0_edge_mlp_w_18': *, 'gnn_0_edge_mlp_w_19': *, 'gnn_0_edge_mlp_w_2': *, 'gnn_0_edge_mlp_w_20': *, 'gnn_0_edge_mlp_w_21': *, 'gnn_0_edge_mlp_w_22': *, 'gnn_0_edge_mlp_w_23': *, 'gnn_0_edge_mlp_w_24': *, 'gnn_0_edge_mlp_w_25': *, 'gnn_0_edge_mlp_w_26': *, 'gnn_0_edge_mlp_w_27': *, 'gnn_0_edge_mlp_w_28': *, 'gnn_0_edge_mlp_w_29': *, 'gnn_0_edge_mlp_w_3': *, 'gnn_0_edge_mlp_w_30': *, 'gnn_0_edge_mlp_w_31': *, 'gnn_0_edge_mlp_w_4': *, 'gnn_0_edge_mlp_w_5': *, 'gnn_0_edge_mlp_w_6': *, 'gnn_0_edge_mlp_w_7': *, 'gnn_0_edge_mlp_w_8': *, 'gnn_0_edge_mlp_w_9': *, 'gnn_0_hyperedge_mlp_b_0': *, 'gnn_0_hyperedge_mlp_b_1': *, 'gnn_0_hyperedge_mlp_b_10': *, 'gnn_0_hyperedge_mlp_b_11': *, 'gnn_0_hyperedge_mlp_b_12': *, 'gnn_0_hyperedge_mlp_b_13': *, 'gnn_0_hyperedge_mlp_b_14': *, 'gnn_0_hyperedge_mlp_b_15': *, 'gnn_0_hyperedge_mlp_b_16': *, 'gnn_0_hyperedge_mlp_b_17': *, 'gnn_0_hyperedge_mlp_b_18': *, 'gnn_0_hyperedge_mlp_b_19': *, 'gnn_0_hyperedge_mlp_b_2': *, 'gnn_0_hyperedge_mlp_b_20': *, 'gnn_0_hyperedge_mlp_b_21': *, 'gnn_0_hyperedge_mlp_b_22': *, 'gnn_0_hyperedge_mlp_b_23': *, 'gnn_0_hyperedge_mlp_b_24': *, 'gnn_0_hyperedge_mlp_b_25': *, 'gnn_0_hyperedge_mlp_b_26': *, 'gnn_0_hyperedge_mlp_b_27': *, 'gnn_0_hyperedge_mlp_b_28': *, 'gnn_0_hyperedge_mlp_b_29': *, 'gnn_0_hyperedge_mlp_b_3': *, 'gnn_0_hyperedge_mlp_b_30': *, 'gnn_0_hyperedge_mlp_b_31': *, 'gnn_0_hyperedge_mlp_b_4': *, 'gnn_0_hyperedge_mlp_b_5': *, 'gnn_0_hyperedge_mlp_b_6': *, 'gnn_0_hyperedge_mlp_b_7': *, 'gnn_0_hyperedge_mlp_b_8': *, 'gnn_0_hyperedge_mlp_b_9': *, 'gnn_0_hyperedge_mlp_w_0': *, 'gnn_0_hyperedge_mlp_w_1': *, 'gnn_0_hyperedge_mlp_w_10': *, 'gnn_0_hyperedge_mlp_w_11': *, 'gnn_0_hyperedge_mlp_w_12': *, 'gnn_0_hyperedge_mlp_w_13': *, 'gnn_0_hyperedge_mlp_w_14': *, 'gnn_0_hyperedge_mlp_w_15': *, 'gnn_0_hyperedge_mlp_w_16': *, 'gnn_0_hyperedge_mlp_w_17': *, 'gnn_0_hyperedge_mlp_w_18': *, 'gnn_0_hyperedge_mlp_w_19': *, 'gnn_0_hyperedge_mlp_w_2': *, 'gnn_0_hyperedge_mlp_w_20': *, 'gnn_0_hyperedge_mlp_w_21': *, 'gnn_0_hyperedge_mlp_w_22': *, 'gnn_0_hyperedge_mlp_w_23': *, 'gnn_0_hyperedge_mlp_w_24': *, 'gnn_0_hyperedge_mlp_w_25': *, 'gnn_0_hyperedge_mlp_w_26': *, 'gnn_0_hyperedge_mlp_w_27': *, 'gnn_0_hyperedge_mlp_w_28': *, 'gnn_0_hyperedge_mlp_w_29': *, 'gnn_0_hyperedge_mlp_w_3': *, 'gnn_0_hyperedge_mlp_w_30': *, 'gnn_0_hyperedge_mlp_w_31': *, 'gnn_0_hyperedge_mlp_w_4': *, 'gnn_0_hyperedge_mlp_w_5': *, 'gnn_0_hyperedge_mlp_w_6': *, 'gnn_0_hyperedge_mlp_w_7': *, 'gnn_0_hyperedge_mlp_w_8': *, 'gnn_0_hyperedge_mlp_w_9': *, 'gnn_0_node_update_b1_0': *, 'gnn_0_node_update_b1_1': *, 'gnn_0_node_update_b1_10': *, 'gnn_0_node_update_b1_11': *, 'gnn_0_node_update_b1_12': *, 'gnn_0_node_update_b1_13': *, 'gnn_0_node_update_b1_14': *, 'gnn_0_node_update_b1_15': *, 'gnn_0_node_update_b1_16': *, 'gnn_0_node_update_b1_17': *, 'gnn_0_node_update_b1_18': *, 'gnn_0_node_update_b1_19': *, 'gnn_0_node_update_b1_2': *, 'gnn_0_node_update_b1_20': *, 'gnn_0_node_update_b1_21': *, 'gnn_0_node_update_b1_22': *, 'gnn_0_node_update_b1_23': *, 'gnn_0_node_update_b1_24': *, 'gnn_0_node_update_b1_25': *, 'gnn_0_node_update_b1_26': *, 'gnn_0_node_update_b1_27': *, 'gnn_0_node_update_b1_28': *, 'gnn_0_node_update_b1_29': *, 'gnn_0_node_update_b1_3': *, 'gnn_0_node_update_b1_30': *, 'gnn_0_node_update_b1_31': *, 'gnn_0_node_update_b1_4': *, 'gnn_0_node_update_b1_5': *, 'gnn_0_node_update_b1_6': *, 'gnn_0_node_update_b1_7': *, 'gnn_0_node_update_b1_8': *, 'gnn_0_node_update_b1_9': *, 'gnn_0_node_update_b2_0': *, 'gnn_0_node_update_b2_1': *, 'gnn_0_node_update_b2_10': *, 'gnn_0_node_update_b2_11': *, 'gnn_0_node_update_b2_12': *, 'gnn_0_node_update_b2_13': *, 'gnn_0_node_update_b2_14': *, 'gnn_0_node_update_b2_15': *, 'gnn_0_node_update_b2_16': *, 'gnn_0_node_update_b2_17': *, 'gnn_0_node_update_b2_18': *, 'gnn_0_node_update_b2_19': *, 'gnn_0_node_update_b2_2': *, 'gnn_0_node_update_b2_20': *, 'gnn_0_node_update_b2_21': *, 'gnn_0_node_update_b2_22': *, 'gnn_0_node_update_b2_23': *, 'gnn_0_node_update_b2_24': *, 'gnn_0_node_update_b2_25': *, 'gnn_0_node_update_b2_26': *, 'gnn_0_node_update_b2_27': *, 'gnn_0_node_update_b2_28': *, 'gnn_0_node_update_b2_29': *, 'gnn_0_node_update_b2_3': *, 'gnn_0_node_update_b2_30': *, 'gnn_0_node_update_b2_31': *, 'gnn_0_node_update_b2_4': *, 'gnn_0_node_update_b2_5': *, 'gnn_0_node_update_b2_6': *, 'gnn_0_node_update_b2_7': *, 'gnn_0_node_update_b2_8': *, 'gnn_0_node_update_b2_9': *, 'gnn_0_node_update_w1_0': *, 'gnn_0_node_update_w1_1': *, 'gnn_0_node_update_w1_10': *, 'gnn_0_node_update_w1_11': *, 'gnn_0_node_update_w1_12': *, 'gnn_0_node_update_w1_13': *, 'gnn_0_node_update_w1_14': *, 'gnn_0_node_update_w1_15': *, 'gnn_0_node_update_w1_16': *, 'gnn_0_node_update_w1_17': *, 'gnn_0_node_update_w1_18': *, 'gnn_0_node_update_w1_19': *, 'gnn_0_node_update_w1_2': *, 'gnn_0_node_update_w1_20': *, 'gnn_0_node_update_w1_21': *, 'gnn_0_node_update_w1_22': *, 'gnn_0_node_update_w1_23': *, 'gnn_0_node_update_w1_24': *, 'gnn_0_node_update_w1_25': *, 'gnn_0_node_update_w1_26': *, 'gnn_0_node_update_w1_27': *, 'gnn_0_node_update_w1_28': *, 'gnn_0_node_update_w1_29': *, 'gnn_0_node_update_w1_3': *, 'gnn_0_node_update_w1_30': *, 'gnn_0_node_update_w1_31': *, 'gnn_0_node_update_w1_4': *, 'gnn_0_node_update_w1_5': *, 'gnn_0_node_update_w1_6': *, 'gnn_0_node_update_w1_7': *, 'gnn_0_node_update_w1_8': *, 'gnn_0_node_update_w1_9': *, 'gnn_0_node_update_w2_0': *, 'gnn_0_node_update_w2_1': *, 'gnn_0_node_update_w2_10': *, 'gnn_0_node_update_w2_11': *, 'gnn_0_node_update_w2_12': *, 'gnn_0_node_update_w2_13': *, 'gnn_0_node_update_w2_14': *, 'gnn_0_node_update_w2_15': *, 'gnn_0_node_update_w2_16': *, 'gnn_0_node_update_w2_17': *, 'gnn_0_node_update_w2_18': *, 'gnn_0_node_update_w2_19': *, 'gnn_0_node_update_w2_2': *, 'gnn_0_node_update_w2_20': *, 'gnn_0_node_update_w2_21': *, 'gnn_0_node_update_w2_22': *, 'gnn_0_node_update_w2_23': *, 'gnn_0_node_update_w2_24': *, 'gnn_0_node_update_w2_25': *, 'gnn_0_node_update_w2_26': *, 'gnn_0_node_update_w2_27': *, 'gnn_0_node_update_w2_28': *, 'gnn_0_node_update_w2_29': *, 'gnn_0_node_update_w2_3': *, 'gnn_0_node_update_w2_30': *, 'gnn_0_node_update_w2_31': *, 'gnn_0_node_update_w2_4': *, 'gnn_0_node_update_w2_5': *, 'gnn_0_node_update_w2_6': *, 'gnn_0_node_update_w2_7': *, 'gnn_0_node_update_w2_8': *, 'gnn_0_node_update_w2_9': *, 'gnn_1_edge_mlp_b_0': *, 'gnn_1_edge_mlp_b_1': *, 'gnn_1_edge_mlp_b_10': *, 'gnn_1_edge_mlp_b_11': *, 'gnn_1_edge_mlp_b_12': *, 'gnn_1_edge_mlp_b_13': *, 'gnn_1_edge_mlp_b_14': *, 'gnn_1_edge_mlp_b_15': *, 'gnn_1_edge_mlp_b_16': *, 'gnn_1_edge_mlp_b_17': *, 'gnn_1_edge_mlp_b_18': *, 'gnn_1_edge_mlp_b_19': *, 'gnn_1_edge_mlp_b_2': *, 'gnn_1_edge_mlp_b_20': *, 'gnn_1_edge_mlp_b_21': *, 'gnn_1_edge_mlp_b_22': *, 'gnn_1_edge_mlp_b_23': *, 'gnn_1_edge_mlp_b_24': *, 'gnn_1_edge_mlp_b_25': *, 'gnn_1_edge_mlp_b_26': *, 'gnn_1_edge_mlp_b_27': *, 'gnn_1_edge_mlp_b_28': *, 'gnn_1_edge_mlp_b_29': *, 'gnn_1_edge_mlp_b_3': *, 'gnn_1_edge_mlp_b_30': *, 'gnn_1_edge_mlp_b_31': *, 'gnn_1_edge_mlp_b_4': *, 'gnn_1_edge_mlp_b_5': *, 'gnn_1_edge_mlp_b_6': *, 'gnn_1_edge_mlp_b_7': *, 'gnn_1_edge_mlp_b_8': *, 'gnn_1_edge_mlp_b_9': *, 'gnn_1_edge_mlp_w_0': *, 'gnn_1_edge_mlp_w_1': *, 'gnn_1_edge_mlp_w_10': *, 'gnn_1_edge_mlp_w_11': *, 'gnn_1_edge_mlp_w_12': *, 'gnn_1_edge_mlp_w_13': *, 'gnn_1_edge_mlp_w_14': *, 'gnn_1_edge_mlp_w_15': *, 'gnn_1_edge_mlp_w_16': *, 'gnn_1_edge_mlp_w_17': *, 'gnn_1_edge_mlp_w_18': *, 'gnn_1_edge_mlp_w_19': *, 'gnn_1_edge_mlp_w_2': *, 'gnn_1_edge_mlp_w_20': *, 'gnn_1_edge_mlp_w_21': *, 'gnn_1_edge_mlp_w_22': *, 'gnn_1_edge_mlp_w_23': *, 'gnn_1_edge_mlp_w_24': *, 'gnn_1_edge_mlp_w_25': *, 'gnn_1_edge_mlp_w_26': *, 'gnn_1_edge_mlp_w_27': *, 'gnn_1_edge_mlp_w_28': *, 'gnn_1_edge_mlp_w_29': *, 'gnn_1_edge_mlp_w_3': *, 'gnn_1_edge_mlp_w_30': *, 'gnn_1_edge_mlp_w_31': *, 'gnn_1_edge_mlp_w_4': *, 'gnn_1_edge_mlp_w_5': *, 'gnn_1_edge_mlp_w_6': *, 'gnn_1_edge_mlp_w_7': *, 'gnn_1_edge_mlp_w_8': *, 'gnn_1_edge_mlp_w_9': *, 'gnn_1_hyperedge_mlp_b_0': *, 'gnn_1_hyperedge_mlp_b_1': *, 'gnn_1_hyperedge_mlp_b_10': *, 'gnn_1_hyperedge_mlp_b_11': *, 'gnn_1_hyperedge_mlp_b_12': *, 'gnn_1_hyperedge_mlp_b_13': *, 'gnn_1_hyperedge_mlp_b_14': *, 'gnn_1_hyperedge_mlp_b_15': *, 'gnn_1_hyperedge_mlp_b_16': *, 'gnn_1_hyperedge_mlp_b_17': *, 'gnn_1_hyperedge_mlp_b_18': *, 'gnn_1_hyperedge_mlp_b_19': *, 'gnn_1_hyperedge_mlp_b_2': *, 'gnn_1_hyperedge_mlp_b_20': *, 'gnn_1_hyperedge_mlp_b_21': *, 'gnn_1_hyperedge_mlp_b_22': *, 'gnn_1_hyperedge_mlp_b_23': *, 'gnn_1_hyperedge_mlp_b_24': *, 'gnn_1_hyperedge_mlp_b_25': *, 'gnn_1_hyperedge_mlp_b_26': *, 'gnn_1_hyperedge_mlp_b_27': *, 'gnn_1_hyperedge_mlp_b_28': *, 'gnn_1_hyperedge_mlp_b_29': *, 'gnn_1_hyperedge_mlp_b_3': *, 'gnn_1_hyperedge_mlp_b_30': *, 'gnn_1_hyperedge_mlp_b_31': *, 'gnn_1_hyperedge_mlp_b_4': *, 'gnn_1_hyperedge_mlp_b_5': *, 'gnn_1_hyperedge_mlp_b_6': *, 'gnn_1_hyperedge_mlp_b_7': *, 'gnn_1_hyperedge_mlp_b_8': *, 'gnn_1_hyperedge_mlp_b_9': *, 'gnn_1_hyperedge_mlp_w_0': *, 'gnn_1_hyperedge_mlp_w_1': *, 'gnn_1_hyperedge_mlp_w_10': *, 'gnn_1_hyperedge_mlp_w_11': *, 'gnn_1_hyperedge_mlp_w_12': *, 'gnn_1_hyperedge_mlp_w_13': *, 'gnn_1_hyperedge_mlp_w_14': *, 'gnn_1_hyperedge_mlp_w_15': *, 'gnn_1_hyperedge_mlp_w_16': *, 'gnn_1_hyperedge_mlp_w_17': *, 'gnn_1_hyperedge_mlp_w_18': *, 'gnn_1_hyperedge_mlp_w_19': *, 'gnn_1_hyperedge_mlp_w_2': *, 'gnn_1_hyperedge_mlp_w_20': *, 'gnn_1_hyperedge_mlp_w_21': *, 'gnn_1_hyperedge_mlp_w_22': *, 'gnn_1_hyperedge_mlp_w_23': *, 'gnn_1_hyperedge_mlp_w_24': *, 'gnn_1_hyperedge_mlp_w_25': *, 'gnn_1_hyperedge_mlp_w_26': *, 'gnn_1_hyperedge_mlp_w_27': *, 'gnn_1_hyperedge_mlp_w_28': *, 'gnn_1_hyperedge_mlp_w_29': *, 'gnn_1_hyperedge_mlp_w_3': *, 'gnn_1_hyperedge_mlp_w_30': *, 'gnn_1_hyperedge_mlp_w_31': *, 'gnn_1_hyperedge_mlp_w_4': *, 'gnn_1_hyperedge_mlp_w_5': *, 'gnn_1_hyperedge_mlp_w_6': *, 'gnn_1_hyperedge_mlp_w_7': *, 'gnn_1_hyperedge_mlp_w_8': *, 'gnn_1_hyperedge_mlp_w_9': *, 'gnn_1_node_update_b1_0': *, 'gnn_1_node_update_b1_1': *, 'gnn_1_node_update_b1_10': *, 'gnn_1_node_update_b1_11': *, 'gnn_1_node_update_b1_12': *, 'gnn_1_node_update_b1_13': *, 'gnn_1_node_update_b1_14': *, 'gnn_1_node_update_b1_15': *, 'gnn_1_node_update_b1_16': *, 'gnn_1_node_update_b1_17': *, 'gnn_1_node_update_b1_18': *, 'gnn_1_node_update_b1_19': *, 'gnn_1_node_update_b1_2': *, 'gnn_1_node_update_b1_20': *, 'gnn_1_node_update_b1_21': *, 'gnn_1_node_update_b1_22': *, 'gnn_1_node_update_b1_23': *, 'gnn_1_node_update_b1_24': *, 'gnn_1_node_update_b1_25': *, 'gnn_1_node_update_b1_26': *, 'gnn_1_node_update_b1_27': *, 'gnn_1_node_update_b1_28': *, 'gnn_1_node_update_b1_29': *, 'gnn_1_node_update_b1_3': *, 'gnn_1_node_update_b1_30': *, 'gnn_1_node_update_b1_31': *, 'gnn_1_node_update_b1_4': *, 'gnn_1_node_update_b1_5': *, 'gnn_1_node_update_b1_6': *, 'gnn_1_node_update_b1_7': *, 'gnn_1_node_update_b1_8': *, 'gnn_1_node_update_b1_9': *, 'gnn_1_node_update_b2_0': *, 'gnn_1_node_update_b2_1': *, 'gnn_1_node_update_b2_10': *, 'gnn_1_node_update_b2_11': *, 'gnn_1_node_update_b2_12': *, 'gnn_1_node_update_b2_13': *, 'gnn_1_node_update_b2_14': *, 'gnn_1_node_update_b2_15': *, 'gnn_1_node_update_b2_16': *, 'gnn_1_node_update_b2_17': *, 'gnn_1_node_update_b2_18': *, 'gnn_1_node_update_b2_19': *, 'gnn_1_node_update_b2_2': *, 'gnn_1_node_update_b2_20': *, 'gnn_1_node_update_b2_21': *, 'gnn_1_node_update_b2_22': *, 'gnn_1_node_update_b2_23': *, 'gnn_1_node_update_b2_24': *, 'gnn_1_node_update_b2_25': *, 'gnn_1_node_update_b2_26': *, 'gnn_1_node_update_b2_27': *, 'gnn_1_node_update_b2_28': *, 'gnn_1_node_update_b2_29': *, 'gnn_1_node_update_b2_3': *, 'gnn_1_node_update_b2_30': *, 'gnn_1_node_update_b2_31': *, 'gnn_1_node_update_b2_4': *, 'gnn_1_node_update_b2_5': *, 'gnn_1_node_update_b2_6': *, 'gnn_1_node_update_b2_7': *, 'gnn_1_node_update_b2_8': *, 'gnn_1_node_update_b2_9': *, 'gnn_1_node_update_w1_0': *, 'gnn_1_node_update_w1_1': *, 'gnn_1_node_update_w1_10': *, 'gnn_1_node_update_w1_11': *, 'gnn_1_node_update_w1_12': *, 'gnn_1_node_update_w1_13': *, 'gnn_1_node_update_w1_14': *, 'gnn_1_node_update_w1_15': *, 'gnn_1_node_update_w1_16': *, 'gnn_1_node_update_w1_17': *, 'gnn_1_node_update_w1_18': *, 'gnn_1_node_update_w1_19': *, 'gnn_1_node_update_w1_2': *, 'gnn_1_node_update_w1_20': *, 'gnn_1_node_update_w1_21': *, 'gnn_1_node_update_w1_22': *, 'gnn_1_node_update_w1_23': *, 'gnn_1_node_update_w1_24': *, 'gnn_1_node_update_w1_25': *, 'gnn_1_node_update_w1_26': *, 'gnn_1_node_update_w1_27': *, 'gnn_1_node_update_w1_28': *, 'gnn_1_node_update_w1_29': *, 'gnn_1_node_update_w1_3': *, 'gnn_1_node_update_w1_30': *, 'gnn_1_node_update_w1_31': *, 'gnn_1_node_update_w1_4': *, 'gnn_1_node_update_w1_5': *, 'gnn_1_node_update_w1_6': *, 'gnn_1_node_update_w1_7': *, 'gnn_1_node_update_w1_8': *, 'gnn_1_node_update_w1_9': *, 'gnn_1_node_update_w2_0': *, 'gnn_1_node_update_w2_1': *, 'gnn_1_node_update_w2_10': *, 'gnn_1_node_update_w2_11': *, 'gnn_1_node_update_w2_12': *, 'gnn_1_node_update_w2_13': *, 'gnn_1_node_update_w2_14': *, 'gnn_1_node_update_w2_15': *, 'gnn_1_node_update_w2_16': *, 'gnn_1_node_update_w2_17': *, 'gnn_1_node_update_w2_18': *, 'gnn_1_node_update_w2_19': *, 'gnn_1_node_update_w2_2': *, 'gnn_1_node_update_w2_20': *, 'gnn_1_node_update_w2_21': *, 'gnn_1_node_update_w2_22': *, 'gnn_1_node_update_w2_23': *, 'gnn_1_node_update_w2_24': *, 'gnn_1_node_update_w2_25': *, 'gnn_1_node_update_w2_26': *, 'gnn_1_node_update_w2_27': *, 'gnn_1_node_update_w2_28': *, 'gnn_1_node_update_w2_29': *, 'gnn_1_node_update_w2_3': *, 'gnn_1_node_update_w2_30': *, 'gnn_1_node_update_w2_31': *, 'gnn_1_node_update_w2_4': *, 'gnn_1_node_update_w2_5': *, 'gnn_1_node_update_w2_6': *, 'gnn_1_node_update_w2_7': *, 'gnn_1_node_update_w2_8': *, 'gnn_1_node_update_w2_9': *, 'layer_norm': {'bias': *, 'scale': *}, 'orbit_embed_0': *, 'orbit_embed_1': *, 'orbit_embed_10': *, 'orbit_embed_11': *, 'orbit_embed_12': *, 'orbit_embed_13': *, 'orbit_embed_14': *, 'orbit_embed_15': *, 'orbit_embed_16': *, 'orbit_embed_17': *, 'orbit_embed_18': *, 'orbit_embed_19': *, 'orbit_embed_2': *, 'orbit_embed_20': *, 'orbit_embed_21': *, 'orbit_embed_22': *, 'orbit_embed_23': *, 'orbit_embed_24': *, 'orbit_embed_25': *, 'orbit_embed_26': *, 'orbit_embed_27': *, 'orbit_embed_28': *, 'orbit_embed_29': *, 'orbit_embed_3': *, 'orbit_embed_30': *, 'orbit_embed_31': *, 'orbit_embed_4': *, 'orbit_embed_5': *, 'orbit_embed_6': *, 'orbit_embed_7': *, 'orbit_embed_8': *, 'orbit_embed_9': *, 'patch_agg_b_0': *, 'patch_agg_b_1': *, 'patch_agg_b_10': *, 'patch_agg_b_11': *, 'patch_agg_b_12': *, 'patch_agg_b_13': *, 'patch_agg_b_14': *, 'patch_agg_b_15': *, 'patch_agg_b_16': *, 'patch_agg_b_17': *, 'patch_agg_b_18': *, 'patch_agg_b_19': *, 'patch_agg_b_2': *, 'patch_agg_b_20': *, 'patch_agg_b_21': *, 'patch_agg_b_22': *, 'patch_agg_b_23': *, 'patch_agg_b_24': *, 'patch_agg_b_25': *, 'patch_agg_b_26': *, 'patch_agg_b_27': *, 'patch_agg_b_28': *, 'patch_agg_b_29': *, 'patch_agg_b_3': *, 'patch_agg_b_30': *, 'patch_agg_b_31': *, 'patch_agg_b_4': *, 'patch_agg_b_5': *, 'patch_agg_b_6': *, 'patch_agg_b_7': *, 'patch_agg_b_8': *, 'patch_agg_b_9': *, 'patch_agg_w_0': *, 'patch_agg_w_1': *, 'patch_agg_w_10': *, 'patch_agg_w_11': *, 'patch_agg_w_12': *, 'patch_agg_w_13': *, 'patch_agg_w_14': *, 'patch_agg_w_15': *, 'patch_agg_w_16': *, 'patch_agg_w_17': *, 'patch_agg_w_18': *, 'patch_agg_w_19': *, 'patch_agg_w_2': *, 'patch_agg_w_20': *, 'patch_agg_w_21': *, 'patch_agg_w_22': *, 'patch_agg_w_23': *, 'patch_agg_w_24': *, 'patch_agg_w_25': *, 'patch_agg_w_26': *, 'patch_agg_w_27': *, 'patch_agg_w_28': *, 'patch_agg_w_29': *, 'patch_agg_w_3': *, 'patch_agg_w_30': *, 'patch_agg_w_31': *, 'patch_agg_w_4': *, 'patch_agg_w_5': *, 'patch_agg_w_6': *, 'patch_agg_w_7': *, 'patch_agg_w_8': *, 'patch_agg_w_9': *}, 'OutputHead_0': {'norm2': {'bias': *, 'scale': *}, 'norm3': {'bias': *, 'scale': *}, 'out_layer_norm': {'bias': *, 'scale': *}, 'output_layer0': {'bias': *, 'kernel': *}, 'output_layer1': {'bias': *, 'kernel': *}}})
Structure of the parameters: PyTreeDef({'Encoder_0': {'layers_0': {'attn': {'k_proj': {'kernel': *}, 'out_proj': {'kernel': *}, 'q_proj': {'kernel': *}, 'relative_position_bias': *, 'v_proj': {'kernel': *}}, 'dense1': {'bias': *, 'kernel': *}, 'dense2': {'bias': *, 'kernel': *}, 'layer_norm_1': {'bias': *, 'scale': *}, 'layer_norm_2': {'bias': *, 'scale': *}}, 'layers_1': {'attn': {'k_proj': {'kernel': *}, 'out_proj': {'kernel': *}, 'q_proj': {'kernel': *}, 'relative_position_bias': *, 'v_proj': {'kernel': *}}, 'dense1': {'bias': *, 'kernel': *}, 'dense2': {'bias': *, 'kernel': *}, 'layer_norm_1': {'bias': *, 'scale': *}, 'layer_norm_2': {'bias': *, 'scale': *}}, 'layers_2': {'attn': {'k_proj': {'kernel': *}, 'out_proj': {'kernel': *}, 'q_proj': {'kernel': *}, 'relative_position_bias': *, 'v_proj': {'kernel': *}}, 'dense1': {'bias': *, 'kernel': *}, 'dense2': {'bias': *, 'kernel': *}, 'layer_norm_1': {'bias': *, 'scale': *}, 'layer_norm_2': {'bias': *, 'scale': *}}, 'layers_3': {'attn': {'k_proj': {'kernel': *}, 'out_proj': {'kernel': *}, 'q_proj': {'kernel': *}, 'relative_position_bias': *, 'v_proj': {'kernel': *}}, 'dense1': {'bias': *, 'kernel': *}, 'dense2': {'bias': *, 'kernel': *}, 'layer_norm_1': {'bias': *, 'scale': *}, 'layer_norm_2': {'bias': *, 'scale': *}}}, 'EquivariantEmbed_0': {'orbit_bias_0': *, 'orbit_bias_1': *, 'orbit_bias_10': *, 'orbit_bias_11': *, 'orbit_bias_12': *, 'orbit_bias_13': *, 'orbit_bias_14': *, 'orbit_bias_15': *, 'orbit_bias_16': *, 'orbit_bias_17': *, 'orbit_bias_18': *, 'orbit_bias_19': *, 'orbit_bias_2': *, 'orbit_bias_20': *, 'orbit_bias_21': *, 'orbit_bias_22': *, 'orbit_bias_23': *, 'orbit_bias_24': *, 'orbit_bias_25': *, 'orbit_bias_26': *, 'orbit_bias_27': *, 'orbit_bias_28': *, 'orbit_bias_29': *, 'orbit_bias_3': *, 'orbit_bias_30': *, 'orbit_bias_31': *, 'orbit_bias_4': *, 'orbit_bias_5': *, 'orbit_bias_6': *, 'orbit_bias_7': *, 'orbit_bias_8': *, 'orbit_bias_9': *, 'orbit_weight_0': *, 'orbit_weight_1': *, 'orbit_weight_10': *, 'orbit_weight_11': *, 'orbit_weight_12': *, 'orbit_weight_13': *, 'orbit_weight_14': *, 'orbit_weight_15': *, 'orbit_weight_16': *, 'orbit_weight_17': *, 'orbit_weight_18': *, 'orbit_weight_19': *, 'orbit_weight_2': *, 'orbit_weight_20': *, 'orbit_weight_21': *, 'orbit_weight_22': *, 'orbit_weight_23': *, 'orbit_weight_24': *, 'orbit_weight_25': *, 'orbit_weight_26': *, 'orbit_weight_27': *, 'orbit_weight_28': *, 'orbit_weight_29': *, 'orbit_weight_3': *, 'orbit_weight_30': *, 'orbit_weight_31': *, 'orbit_weight_4': *, 'orbit_weight_5': *, 'orbit_weight_6': *, 'orbit_weight_7': *, 'orbit_weight_8': *, 'orbit_weight_9': *}, 'OutputHead_0': {'norm2': {'bias': *, 'scale': *}, 'norm3': {'bias': *, 'scale': *}, 'out_layer_norm': {'bias': *, 'scale': *}, 'output_layer0': {'bias': *, 'kernel': *}, 'output_layer1': {'bias': *, 'kernel': *}}})

This error is because you attempted to modify the ``parameters`` or ``variables`` attribute of a
variational state with a structure that does not match the previous structure.

To fix this error, you should ensure that the structure of the parameters you are trying to assign
matches the structure of the parameters that were already present in the variational state.

If you believe this error was thrown in error, or it prevents you from doing something, please open an issue.


-------------------------------------------------------
For more detailed informations, visit the following link:
	 https://netket.readthedocs.io/en/latest/api/_generated/errors/netket.errors.ParameterMismatchError.html
or the list of all common errors at
	 https://netket.readthedocs.io/en/latest/api/errors.html
-------------------------------------------------------

Traceback (most recent call last):
  File "/home/<USER>/Repositories/Shastry-Sutherland_ViT/scripts/train.py", line 202, in <module>
    main()
  File "/home/<USER>/Repositories/Shastry-Sutherland_ViT/scripts/train.py", line 192, in main
    run_vit_simulation(args)
  File "/home/<USER>/Repositories/Shastry-Sutherland_ViT/scripts/train.py", line 167, in run_vit_simulation
    runner.setup_model()
  File "/home/<USER>/Repositories/Shastry-Sutherland_ViT/src/runner.py", line 300, in setup_model
    self.vqs.parameters = checkpoint_data['parameters']
    ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/netket/vqs/base.py", line 96, in parameters
    raise nkerrors.ParameterMismatchError(
netket.errors.ParameterMismatchError: 
The structure of the parameters does not match the expected structure.

Expected structure: PyTreeDef({'Encoder_0': {'layers_0': {'attn': {'k_proj': {'kernel': *}, 'out_proj': {'kernel': *}, 'q_proj': {'kernel': *}, 'relative_position_bias': *, 'v_proj': {'kernel': *}}, 'dense1': {'bias': *, 'kernel': *}, 'dense2': {'bias': *, 'kernel': *}, 'layer_norm_1': {'bias': *, 'scale': *}, 'layer_norm_2': {'bias': *, 'scale': *}}, 'layers_1': {'attn': {'k_proj': {'kernel': *}, 'out_proj': {'kernel': *}, 'q_proj': {'kernel': *}, 'relative_position_bias': *, 'v_proj': {'kernel': *}}, 'dense1': {'bias': *, 'kernel': *}, 'dense2': {'bias': *, 'kernel': *}, 'layer_norm_1': {'bias': *, 'scale': *}, 'layer_norm_2': {'bias': *, 'scale': *}}, 'layers_2': {'attn': {'k_proj': {'kernel': *}, 'out_proj': {'kernel': *}, 'q_proj': {'kernel': *}, 'relative_position_bias': *, 'v_proj': {'kernel': *}}, 'dense1': {'bias': *, 'kernel': *}, 'dense2': {'bias': *, 'kernel': *}, 'layer_norm_1': {'bias': *, 'scale': *}, 'layer_norm_2': {'bias': *, 'scale': *}}, 'layers_3': {'attn': {'k_proj': {'kernel': *}, 'out_proj': {'kernel': *}, 'q_proj': {'kernel': *}, 'relative_position_bias': *, 'v_proj': {'kernel': *}}, 'dense1': {'bias': *, 'kernel': *}, 'dense2': {'bias': *, 'kernel': *}, 'layer_norm_1': {'bias': *, 'scale': *}, 'layer_norm_2': {'bias': *, 'scale': *}}}, 'HyperGNNEquivariantEmbed_0': {'gnn_0_edge_mlp_b_0': *, 'gnn_0_edge_mlp_b_1': *, 'gnn_0_edge_mlp_b_10': *, 'gnn_0_edge_mlp_b_11': *, 'gnn_0_edge_mlp_b_12': *, 'gnn_0_edge_mlp_b_13': *, 'gnn_0_edge_mlp_b_14': *, 'gnn_0_edge_mlp_b_15': *, 'gnn_0_edge_mlp_b_16': *, 'gnn_0_edge_mlp_b_17': *, 'gnn_0_edge_mlp_b_18': *, 'gnn_0_edge_mlp_b_19': *, 'gnn_0_edge_mlp_b_2': *, 'gnn_0_edge_mlp_b_20': *, 'gnn_0_edge_mlp_b_21': *, 'gnn_0_edge_mlp_b_22': *, 'gnn_0_edge_mlp_b_23': *, 'gnn_0_edge_mlp_b_24': *, 'gnn_0_edge_mlp_b_25': *, 'gnn_0_edge_mlp_b_26': *, 'gnn_0_edge_mlp_b_27': *, 'gnn_0_edge_mlp_b_28': *, 'gnn_0_edge_mlp_b_29': *, 'gnn_0_edge_mlp_b_3': *, 'gnn_0_edge_mlp_b_30': *, 'gnn_0_edge_mlp_b_31': *, 'gnn_0_edge_mlp_b_4': *, 'gnn_0_edge_mlp_b_5': *, 'gnn_0_edge_mlp_b_6': *, 'gnn_0_edge_mlp_b_7': *, 'gnn_0_edge_mlp_b_8': *, 'gnn_0_edge_mlp_b_9': *, 'gnn_0_edge_mlp_w_0': *, 'gnn_0_edge_mlp_w_1': *, 'gnn_0_edge_mlp_w_10': *, 'gnn_0_edge_mlp_w_11': *, 'gnn_0_edge_mlp_w_12': *, 'gnn_0_edge_mlp_w_13': *, 'gnn_0_edge_mlp_w_14': *, 'gnn_0_edge_mlp_w_15': *, 'gnn_0_edge_mlp_w_16': *, 'gnn_0_edge_mlp_w_17': *, 'gnn_0_edge_mlp_w_18': *, 'gnn_0_edge_mlp_w_19': *, 'gnn_0_edge_mlp_w_2': *, 'gnn_0_edge_mlp_w_20': *, 'gnn_0_edge_mlp_w_21': *, 'gnn_0_edge_mlp_w_22': *, 'gnn_0_edge_mlp_w_23': *, 'gnn_0_edge_mlp_w_24': *, 'gnn_0_edge_mlp_w_25': *, 'gnn_0_edge_mlp_w_26': *, 'gnn_0_edge_mlp_w_27': *, 'gnn_0_edge_mlp_w_28': *, 'gnn_0_edge_mlp_w_29': *, 'gnn_0_edge_mlp_w_3': *, 'gnn_0_edge_mlp_w_30': *, 'gnn_0_edge_mlp_w_31': *, 'gnn_0_edge_mlp_w_4': *, 'gnn_0_edge_mlp_w_5': *, 'gnn_0_edge_mlp_w_6': *, 'gnn_0_edge_mlp_w_7': *, 'gnn_0_edge_mlp_w_8': *, 'gnn_0_edge_mlp_w_9': *, 'gnn_0_hyperedge_mlp_b_0': *, 'gnn_0_hyperedge_mlp_b_1': *, 'gnn_0_hyperedge_mlp_b_10': *, 'gnn_0_hyperedge_mlp_b_11': *, 'gnn_0_hyperedge_mlp_b_12': *, 'gnn_0_hyperedge_mlp_b_13': *, 'gnn_0_hyperedge_mlp_b_14': *, 'gnn_0_hyperedge_mlp_b_15': *, 'gnn_0_hyperedge_mlp_b_16': *, 'gnn_0_hyperedge_mlp_b_17': *, 'gnn_0_hyperedge_mlp_b_18': *, 'gnn_0_hyperedge_mlp_b_19': *, 'gnn_0_hyperedge_mlp_b_2': *, 'gnn_0_hyperedge_mlp_b_20': *, 'gnn_0_hyperedge_mlp_b_21': *, 'gnn_0_hyperedge_mlp_b_22': *, 'gnn_0_hyperedge_mlp_b_23': *, 'gnn_0_hyperedge_mlp_b_24': *, 'gnn_0_hyperedge_mlp_b_25': *, 'gnn_0_hyperedge_mlp_b_26': *, 'gnn_0_hyperedge_mlp_b_27': *, 'gnn_0_hyperedge_mlp_b_28': *, 'gnn_0_hyperedge_mlp_b_29': *, 'gnn_0_hyperedge_mlp_b_3': *, 'gnn_0_hyperedge_mlp_b_30': *, 'gnn_0_hyperedge_mlp_b_31': *, 'gnn_0_hyperedge_mlp_b_4': *, 'gnn_0_hyperedge_mlp_b_5': *, 'gnn_0_hyperedge_mlp_b_6': *, 'gnn_0_hyperedge_mlp_b_7': *, 'gnn_0_hyperedge_mlp_b_8': *, 'gnn_0_hyperedge_mlp_b_9': *, 'gnn_0_hyperedge_mlp_w_0': *, 'gnn_0_hyperedge_mlp_w_1': *, 'gnn_0_hyperedge_mlp_w_10': *, 'gnn_0_hyperedge_mlp_w_11': *, 'gnn_0_hyperedge_mlp_w_12': *, 'gnn_0_hyperedge_mlp_w_13': *, 'gnn_0_hyperedge_mlp_w_14': *, 'gnn_0_hyperedge_mlp_w_15': *, 'gnn_0_hyperedge_mlp_w_16': *, 'gnn_0_hyperedge_mlp_w_17': *, 'gnn_0_hyperedge_mlp_w_18': *, 'gnn_0_hyperedge_mlp_w_19': *, 'gnn_0_hyperedge_mlp_w_2': *, 'gnn_0_hyperedge_mlp_w_20': *, 'gnn_0_hyperedge_mlp_w_21': *, 'gnn_0_hyperedge_mlp_w_22': *, 'gnn_0_hyperedge_mlp_w_23': *, 'gnn_0_hyperedge_mlp_w_24': *, 'gnn_0_hyperedge_mlp_w_25': *, 'gnn_0_hyperedge_mlp_w_26': *, 'gnn_0_hyperedge_mlp_w_27': *, 'gnn_0_hyperedge_mlp_w_28': *, 'gnn_0_hyperedge_mlp_w_29': *, 'gnn_0_hyperedge_mlp_w_3': *, 'gnn_0_hyperedge_mlp_w_30': *, 'gnn_0_hyperedge_mlp_w_31': *, 'gnn_0_hyperedge_mlp_w_4': *, 'gnn_0_hyperedge_mlp_w_5': *, 'gnn_0_hyperedge_mlp_w_6': *, 'gnn_0_hyperedge_mlp_w_7': *, 'gnn_0_hyperedge_mlp_w_8': *, 'gnn_0_hyperedge_mlp_w_9': *, 'gnn_0_node_update_b1_0': *, 'gnn_0_node_update_b1_1': *, 'gnn_0_node_update_b1_10': *, 'gnn_0_node_update_b1_11': *, 'gnn_0_node_update_b1_12': *, 'gnn_0_node_update_b1_13': *, 'gnn_0_node_update_b1_14': *, 'gnn_0_node_update_b1_15': *, 'gnn_0_node_update_b1_16': *, 'gnn_0_node_update_b1_17': *, 'gnn_0_node_update_b1_18': *, 'gnn_0_node_update_b1_19': *, 'gnn_0_node_update_b1_2': *, 'gnn_0_node_update_b1_20': *, 'gnn_0_node_update_b1_21': *, 'gnn_0_node_update_b1_22': *, 'gnn_0_node_update_b1_23': *, 'gnn_0_node_update_b1_24': *, 'gnn_0_node_update_b1_25': *, 'gnn_0_node_update_b1_26': *, 'gnn_0_node_update_b1_27': *, 'gnn_0_node_update_b1_28': *, 'gnn_0_node_update_b1_29': *, 'gnn_0_node_update_b1_3': *, 'gnn_0_node_update_b1_30': *, 'gnn_0_node_update_b1_31': *, 'gnn_0_node_update_b1_4': *, 'gnn_0_node_update_b1_5': *, 'gnn_0_node_update_b1_6': *, 'gnn_0_node_update_b1_7': *, 'gnn_0_node_update_b1_8': *, 'gnn_0_node_update_b1_9': *, 'gnn_0_node_update_b2_0': *, 'gnn_0_node_update_b2_1': *, 'gnn_0_node_update_b2_10': *, 'gnn_0_node_update_b2_11': *, 'gnn_0_node_update_b2_12': *, 'gnn_0_node_update_b2_13': *, 'gnn_0_node_update_b2_14': *, 'gnn_0_node_update_b2_15': *, 'gnn_0_node_update_b2_16': *, 'gnn_0_node_update_b2_17': *, 'gnn_0_node_update_b2_18': *, 'gnn_0_node_update_b2_19': *, 'gnn_0_node_update_b2_2': *, 'gnn_0_node_update_b2_20': *, 'gnn_0_node_update_b2_21': *, 'gnn_0_node_update_b2_22': *, 'gnn_0_node_update_b2_23': *, 'gnn_0_node_update_b2_24': *, 'gnn_0_node_update_b2_25': *, 'gnn_0_node_update_b2_26': *, 'gnn_0_node_update_b2_27': *, 'gnn_0_node_update_b2_28': *, 'gnn_0_node_update_b2_29': *, 'gnn_0_node_update_b2_3': *, 'gnn_0_node_update_b2_30': *, 'gnn_0_node_update_b2_31': *, 'gnn_0_node_update_b2_4': *, 'gnn_0_node_update_b2_5': *, 'gnn_0_node_update_b2_6': *, 'gnn_0_node_update_b2_7': *, 'gnn_0_node_update_b2_8': *, 'gnn_0_node_update_b2_9': *, 'gnn_0_node_update_w1_0': *, 'gnn_0_node_update_w1_1': *, 'gnn_0_node_update_w1_10': *, 'gnn_0_node_update_w1_11': *, 'gnn_0_node_update_w1_12': *, 'gnn_0_node_update_w1_13': *, 'gnn_0_node_update_w1_14': *, 'gnn_0_node_update_w1_15': *, 'gnn_0_node_update_w1_16': *, 'gnn_0_node_update_w1_17': *, 'gnn_0_node_update_w1_18': *, 'gnn_0_node_update_w1_19': *, 'gnn_0_node_update_w1_2': *, 'gnn_0_node_update_w1_20': *, 'gnn_0_node_update_w1_21': *, 'gnn_0_node_update_w1_22': *, 'gnn_0_node_update_w1_23': *, 'gnn_0_node_update_w1_24': *, 'gnn_0_node_update_w1_25': *, 'gnn_0_node_update_w1_26': *, 'gnn_0_node_update_w1_27': *, 'gnn_0_node_update_w1_28': *, 'gnn_0_node_update_w1_29': *, 'gnn_0_node_update_w1_3': *, 'gnn_0_node_update_w1_30': *, 'gnn_0_node_update_w1_31': *, 'gnn_0_node_update_w1_4': *, 'gnn_0_node_update_w1_5': *, 'gnn_0_node_update_w1_6': *, 'gnn_0_node_update_w1_7': *, 'gnn_0_node_update_w1_8': *, 'gnn_0_node_update_w1_9': *, 'gnn_0_node_update_w2_0': *, 'gnn_0_node_update_w2_1': *, 'gnn_0_node_update_w2_10': *, 'gnn_0_node_update_w2_11': *, 'gnn_0_node_update_w2_12': *, 'gnn_0_node_update_w2_13': *, 'gnn_0_node_update_w2_14': *, 'gnn_0_node_update_w2_15': *, 'gnn_0_node_update_w2_16': *, 'gnn_0_node_update_w2_17': *, 'gnn_0_node_update_w2_18': *, 'gnn_0_node_update_w2_19': *, 'gnn_0_node_update_w2_2': *, 'gnn_0_node_update_w2_20': *, 'gnn_0_node_update_w2_21': *, 'gnn_0_node_update_w2_22': *, 'gnn_0_node_update_w2_23': *, 'gnn_0_node_update_w2_24': *, 'gnn_0_node_update_w2_25': *, 'gnn_0_node_update_w2_26': *, 'gnn_0_node_update_w2_27': *, 'gnn_0_node_update_w2_28': *, 'gnn_0_node_update_w2_29': *, 'gnn_0_node_update_w2_3': *, 'gnn_0_node_update_w2_30': *, 'gnn_0_node_update_w2_31': *, 'gnn_0_node_update_w2_4': *, 'gnn_0_node_update_w2_5': *, 'gnn_0_node_update_w2_6': *, 'gnn_0_node_update_w2_7': *, 'gnn_0_node_update_w2_8': *, 'gnn_0_node_update_w2_9': *, 'gnn_1_edge_mlp_b_0': *, 'gnn_1_edge_mlp_b_1': *, 'gnn_1_edge_mlp_b_10': *, 'gnn_1_edge_mlp_b_11': *, 'gnn_1_edge_mlp_b_12': *, 'gnn_1_edge_mlp_b_13': *, 'gnn_1_edge_mlp_b_14': *, 'gnn_1_edge_mlp_b_15': *, 'gnn_1_edge_mlp_b_16': *, 'gnn_1_edge_mlp_b_17': *, 'gnn_1_edge_mlp_b_18': *, 'gnn_1_edge_mlp_b_19': *, 'gnn_1_edge_mlp_b_2': *, 'gnn_1_edge_mlp_b_20': *, 'gnn_1_edge_mlp_b_21': *, 'gnn_1_edge_mlp_b_22': *, 'gnn_1_edge_mlp_b_23': *, 'gnn_1_edge_mlp_b_24': *, 'gnn_1_edge_mlp_b_25': *, 'gnn_1_edge_mlp_b_26': *, 'gnn_1_edge_mlp_b_27': *, 'gnn_1_edge_mlp_b_28': *, 'gnn_1_edge_mlp_b_29': *, 'gnn_1_edge_mlp_b_3': *, 'gnn_1_edge_mlp_b_30': *, 'gnn_1_edge_mlp_b_31': *, 'gnn_1_edge_mlp_b_4': *, 'gnn_1_edge_mlp_b_5': *, 'gnn_1_edge_mlp_b_6': *, 'gnn_1_edge_mlp_b_7': *, 'gnn_1_edge_mlp_b_8': *, 'gnn_1_edge_mlp_b_9': *, 'gnn_1_edge_mlp_w_0': *, 'gnn_1_edge_mlp_w_1': *, 'gnn_1_edge_mlp_w_10': *, 'gnn_1_edge_mlp_w_11': *, 'gnn_1_edge_mlp_w_12': *, 'gnn_1_edge_mlp_w_13': *, 'gnn_1_edge_mlp_w_14': *, 'gnn_1_edge_mlp_w_15': *, 'gnn_1_edge_mlp_w_16': *, 'gnn_1_edge_mlp_w_17': *, 'gnn_1_edge_mlp_w_18': *, 'gnn_1_edge_mlp_w_19': *, 'gnn_1_edge_mlp_w_2': *, 'gnn_1_edge_mlp_w_20': *, 'gnn_1_edge_mlp_w_21': *, 'gnn_1_edge_mlp_w_22': *, 'gnn_1_edge_mlp_w_23': *, 'gnn_1_edge_mlp_w_24': *, 'gnn_1_edge_mlp_w_25': *, 'gnn_1_edge_mlp_w_26': *, 'gnn_1_edge_mlp_w_27': *, 'gnn_1_edge_mlp_w_28': *, 'gnn_1_edge_mlp_w_29': *, 'gnn_1_edge_mlp_w_3': *, 'gnn_1_edge_mlp_w_30': *, 'gnn_1_edge_mlp_w_31': *, 'gnn_1_edge_mlp_w_4': *, 'gnn_1_edge_mlp_w_5': *, 'gnn_1_edge_mlp_w_6': *, 'gnn_1_edge_mlp_w_7': *, 'gnn_1_edge_mlp_w_8': *, 'gnn_1_edge_mlp_w_9': *, 'gnn_1_hyperedge_mlp_b_0': *, 'gnn_1_hyperedge_mlp_b_1': *, 'gnn_1_hyperedge_mlp_b_10': *, 'gnn_1_hyperedge_mlp_b_11': *, 'gnn_1_hyperedge_mlp_b_12': *, 'gnn_1_hyperedge_mlp_b_13': *, 'gnn_1_hyperedge_mlp_b_14': *, 'gnn_1_hyperedge_mlp_b_15': *, 'gnn_1_hyperedge_mlp_b_16': *, 'gnn_1_hyperedge_mlp_b_17': *, 'gnn_1_hyperedge_mlp_b_18': *, 'gnn_1_hyperedge_mlp_b_19': *, 'gnn_1_hyperedge_mlp_b_2': *, 'gnn_1_hyperedge_mlp_b_20': *, 'gnn_1_hyperedge_mlp_b_21': *, 'gnn_1_hyperedge_mlp_b_22': *, 'gnn_1_hyperedge_mlp_b_23': *, 'gnn_1_hyperedge_mlp_b_24': *, 'gnn_1_hyperedge_mlp_b_25': *, 'gnn_1_hyperedge_mlp_b_26': *, 'gnn_1_hyperedge_mlp_b_27': *, 'gnn_1_hyperedge_mlp_b_28': *, 'gnn_1_hyperedge_mlp_b_29': *, 'gnn_1_hyperedge_mlp_b_3': *, 'gnn_1_hyperedge_mlp_b_30': *, 'gnn_1_hyperedge_mlp_b_31': *, 'gnn_1_hyperedge_mlp_b_4': *, 'gnn_1_hyperedge_mlp_b_5': *, 'gnn_1_hyperedge_mlp_b_6': *, 'gnn_1_hyperedge_mlp_b_7': *, 'gnn_1_hyperedge_mlp_b_8': *, 'gnn_1_hyperedge_mlp_b_9': *, 'gnn_1_hyperedge_mlp_w_0': *, 'gnn_1_hyperedge_mlp_w_1': *, 'gnn_1_hyperedge_mlp_w_10': *, 'gnn_1_hyperedge_mlp_w_11': *, 'gnn_1_hyperedge_mlp_w_12': *, 'gnn_1_hyperedge_mlp_w_13': *, 'gnn_1_hyperedge_mlp_w_14': *, 'gnn_1_hyperedge_mlp_w_15': *, 'gnn_1_hyperedge_mlp_w_16': *, 'gnn_1_hyperedge_mlp_w_17': *, 'gnn_1_hyperedge_mlp_w_18': *, 'gnn_1_hyperedge_mlp_w_19': *, 'gnn_1_hyperedge_mlp_w_2': *, 'gnn_1_hyperedge_mlp_w_20': *, 'gnn_1_hyperedge_mlp_w_21': *, 'gnn_1_hyperedge_mlp_w_22': *, 'gnn_1_hyperedge_mlp_w_23': *, 'gnn_1_hyperedge_mlp_w_24': *, 'gnn_1_hyperedge_mlp_w_25': *, 'gnn_1_hyperedge_mlp_w_26': *, 'gnn_1_hyperedge_mlp_w_27': *, 'gnn_1_hyperedge_mlp_w_28': *, 'gnn_1_hyperedge_mlp_w_29': *, 'gnn_1_hyperedge_mlp_w_3': *, 'gnn_1_hyperedge_mlp_w_30': *, 'gnn_1_hyperedge_mlp_w_31': *, 'gnn_1_hyperedge_mlp_w_4': *, 'gnn_1_hyperedge_mlp_w_5': *, 'gnn_1_hyperedge_mlp_w_6': *, 'gnn_1_hyperedge_mlp_w_7': *, 'gnn_1_hyperedge_mlp_w_8': *, 'gnn_1_hyperedge_mlp_w_9': *, 'gnn_1_node_update_b1_0': *, 'gnn_1_node_update_b1_1': *, 'gnn_1_node_update_b1_10': *, 'gnn_1_node_update_b1_11': *, 'gnn_1_node_update_b1_12': *, 'gnn_1_node_update_b1_13': *, 'gnn_1_node_update_b1_14': *, 'gnn_1_node_update_b1_15': *, 'gnn_1_node_update_b1_16': *, 'gnn_1_node_update_b1_17': *, 'gnn_1_node_update_b1_18': *, 'gnn_1_node_update_b1_19': *, 'gnn_1_node_update_b1_2': *, 'gnn_1_node_update_b1_20': *, 'gnn_1_node_update_b1_21': *, 'gnn_1_node_update_b1_22': *, 'gnn_1_node_update_b1_23': *, 'gnn_1_node_update_b1_24': *, 'gnn_1_node_update_b1_25': *, 'gnn_1_node_update_b1_26': *, 'gnn_1_node_update_b1_27': *, 'gnn_1_node_update_b1_28': *, 'gnn_1_node_update_b1_29': *, 'gnn_1_node_update_b1_3': *, 'gnn_1_node_update_b1_30': *, 'gnn_1_node_update_b1_31': *, 'gnn_1_node_update_b1_4': *, 'gnn_1_node_update_b1_5': *, 'gnn_1_node_update_b1_6': *, 'gnn_1_node_update_b1_7': *, 'gnn_1_node_update_b1_8': *, 'gnn_1_node_update_b1_9': *, 'gnn_1_node_update_b2_0': *, 'gnn_1_node_update_b2_1': *, 'gnn_1_node_update_b2_10': *, 'gnn_1_node_update_b2_11': *, 'gnn_1_node_update_b2_12': *, 'gnn_1_node_update_b2_13': *, 'gnn_1_node_update_b2_14': *, 'gnn_1_node_update_b2_15': *, 'gnn_1_node_update_b2_16': *, 'gnn_1_node_update_b2_17': *, 'gnn_1_node_update_b2_18': *, 'gnn_1_node_update_b2_19': *, 'gnn_1_node_update_b2_2': *, 'gnn_1_node_update_b2_20': *, 'gnn_1_node_update_b2_21': *, 'gnn_1_node_update_b2_22': *, 'gnn_1_node_update_b2_23': *, 'gnn_1_node_update_b2_24': *, 'gnn_1_node_update_b2_25': *, 'gnn_1_node_update_b2_26': *, 'gnn_1_node_update_b2_27': *, 'gnn_1_node_update_b2_28': *, 'gnn_1_node_update_b2_29': *, 'gnn_1_node_update_b2_3': *, 'gnn_1_node_update_b2_30': *, 'gnn_1_node_update_b2_31': *, 'gnn_1_node_update_b2_4': *, 'gnn_1_node_update_b2_5': *, 'gnn_1_node_update_b2_6': *, 'gnn_1_node_update_b2_7': *, 'gnn_1_node_update_b2_8': *, 'gnn_1_node_update_b2_9': *, 'gnn_1_node_update_w1_0': *, 'gnn_1_node_update_w1_1': *, 'gnn_1_node_update_w1_10': *, 'gnn_1_node_update_w1_11': *, 'gnn_1_node_update_w1_12': *, 'gnn_1_node_update_w1_13': *, 'gnn_1_node_update_w1_14': *, 'gnn_1_node_update_w1_15': *, 'gnn_1_node_update_w1_16': *, 'gnn_1_node_update_w1_17': *, 'gnn_1_node_update_w1_18': *, 'gnn_1_node_update_w1_19': *, 'gnn_1_node_update_w1_2': *, 'gnn_1_node_update_w1_20': *, 'gnn_1_node_update_w1_21': *, 'gnn_1_node_update_w1_22': *, 'gnn_1_node_update_w1_23': *, 'gnn_1_node_update_w1_24': *, 'gnn_1_node_update_w1_25': *, 'gnn_1_node_update_w1_26': *, 'gnn_1_node_update_w1_27': *, 'gnn_1_node_update_w1_28': *, 'gnn_1_node_update_w1_29': *, 'gnn_1_node_update_w1_3': *, 'gnn_1_node_update_w1_30': *, 'gnn_1_node_update_w1_31': *, 'gnn_1_node_update_w1_4': *, 'gnn_1_node_update_w1_5': *, 'gnn_1_node_update_w1_6': *, 'gnn_1_node_update_w1_7': *, 'gnn_1_node_update_w1_8': *, 'gnn_1_node_update_w1_9': *, 'gnn_1_node_update_w2_0': *, 'gnn_1_node_update_w2_1': *, 'gnn_1_node_update_w2_10': *, 'gnn_1_node_update_w2_11': *, 'gnn_1_node_update_w2_12': *, 'gnn_1_node_update_w2_13': *, 'gnn_1_node_update_w2_14': *, 'gnn_1_node_update_w2_15': *, 'gnn_1_node_update_w2_16': *, 'gnn_1_node_update_w2_17': *, 'gnn_1_node_update_w2_18': *, 'gnn_1_node_update_w2_19': *, 'gnn_1_node_update_w2_2': *, 'gnn_1_node_update_w2_20': *, 'gnn_1_node_update_w2_21': *, 'gnn_1_node_update_w2_22': *, 'gnn_1_node_update_w2_23': *, 'gnn_1_node_update_w2_24': *, 'gnn_1_node_update_w2_25': *, 'gnn_1_node_update_w2_26': *, 'gnn_1_node_update_w2_27': *, 'gnn_1_node_update_w2_28': *, 'gnn_1_node_update_w2_29': *, 'gnn_1_node_update_w2_3': *, 'gnn_1_node_update_w2_30': *, 'gnn_1_node_update_w2_31': *, 'gnn_1_node_update_w2_4': *, 'gnn_1_node_update_w2_5': *, 'gnn_1_node_update_w2_6': *, 'gnn_1_node_update_w2_7': *, 'gnn_1_node_update_w2_8': *, 'gnn_1_node_update_w2_9': *, 'layer_norm': {'bias': *, 'scale': *}, 'orbit_embed_0': *, 'orbit_embed_1': *, 'orbit_embed_10': *, 'orbit_embed_11': *, 'orbit_embed_12': *, 'orbit_embed_13': *, 'orbit_embed_14': *, 'orbit_embed_15': *, 'orbit_embed_16': *, 'orbit_embed_17': *, 'orbit_embed_18': *, 'orbit_embed_19': *, 'orbit_embed_2': *, 'orbit_embed_20': *, 'orbit_embed_21': *, 'orbit_embed_22': *, 'orbit_embed_23': *, 'orbit_embed_24': *, 'orbit_embed_25': *, 'orbit_embed_26': *, 'orbit_embed_27': *, 'orbit_embed_28': *, 'orbit_embed_29': *, 'orbit_embed_3': *, 'orbit_embed_30': *, 'orbit_embed_31': *, 'orbit_embed_4': *, 'orbit_embed_5': *, 'orbit_embed_6': *, 'orbit_embed_7': *, 'orbit_embed_8': *, 'orbit_embed_9': *, 'patch_agg_b_0': *, 'patch_agg_b_1': *, 'patch_agg_b_10': *, 'patch_agg_b_11': *, 'patch_agg_b_12': *, 'patch_agg_b_13': *, 'patch_agg_b_14': *, 'patch_agg_b_15': *, 'patch_agg_b_16': *, 'patch_agg_b_17': *, 'patch_agg_b_18': *, 'patch_agg_b_19': *, 'patch_agg_b_2': *, 'patch_agg_b_20': *, 'patch_agg_b_21': *, 'patch_agg_b_22': *, 'patch_agg_b_23': *, 'patch_agg_b_24': *, 'patch_agg_b_25': *, 'patch_agg_b_26': *, 'patch_agg_b_27': *, 'patch_agg_b_28': *, 'patch_agg_b_29': *, 'patch_agg_b_3': *, 'patch_agg_b_30': *, 'patch_agg_b_31': *, 'patch_agg_b_4': *, 'patch_agg_b_5': *, 'patch_agg_b_6': *, 'patch_agg_b_7': *, 'patch_agg_b_8': *, 'patch_agg_b_9': *, 'patch_agg_w_0': *, 'patch_agg_w_1': *, 'patch_agg_w_10': *, 'patch_agg_w_11': *, 'patch_agg_w_12': *, 'patch_agg_w_13': *, 'patch_agg_w_14': *, 'patch_agg_w_15': *, 'patch_agg_w_16': *, 'patch_agg_w_17': *, 'patch_agg_w_18': *, 'patch_agg_w_19': *, 'patch_agg_w_2': *, 'patch_agg_w_20': *, 'patch_agg_w_21': *, 'patch_agg_w_22': *, 'patch_agg_w_23': *, 'patch_agg_w_24': *, 'patch_agg_w_25': *, 'patch_agg_w_26': *, 'patch_agg_w_27': *, 'patch_agg_w_28': *, 'patch_agg_w_29': *, 'patch_agg_w_3': *, 'patch_agg_w_30': *, 'patch_agg_w_31': *, 'patch_agg_w_4': *, 'patch_agg_w_5': *, 'patch_agg_w_6': *, 'patch_agg_w_7': *, 'patch_agg_w_8': *, 'patch_agg_w_9': *}, 'OutputHead_0': {'norm2': {'bias': *, 'scale': *}, 'norm3': {'bias': *, 'scale': *}, 'out_layer_norm': {'bias': *, 'scale': *}, 'output_layer0': {'bias': *, 'kernel': *}, 'output_layer1': {'bias': *, 'kernel': *}}})
Structure of the parameters: PyTreeDef({'Encoder_0': {'layers_0': {'attn': {'k_proj': {'kernel': *}, 'out_proj': {'kernel': *}, 'q_proj': {'kernel': *}, 'relative_position_bias': *, 'v_proj': {'kernel': *}}, 'dense1': {'bias': *, 'kernel': *}, 'dense2': {'bias': *, 'kernel': *}, 'layer_norm_1': {'bias': *, 'scale': *}, 'layer_norm_2': {'bias': *, 'scale': *}}, 'layers_1': {'attn': {'k_proj': {'kernel': *}, 'out_proj': {'kernel': *}, 'q_proj': {'kernel': *}, 'relative_position_bias': *, 'v_proj': {'kernel': *}}, 'dense1': {'bias': *, 'kernel': *}, 'dense2': {'bias': *, 'kernel': *}, 'layer_norm_1': {'bias': *, 'scale': *}, 'layer_norm_2': {'bias': *, 'scale': *}}, 'layers_2': {'attn': {'k_proj': {'kernel': *}, 'out_proj': {'kernel': *}, 'q_proj': {'kernel': *}, 'relative_position_bias': *, 'v_proj': {'kernel': *}}, 'dense1': {'bias': *, 'kernel': *}, 'dense2': {'bias': *, 'kernel': *}, 'layer_norm_1': {'bias': *, 'scale': *}, 'layer_norm_2': {'bias': *, 'scale': *}}, 'layers_3': {'attn': {'k_proj': {'kernel': *}, 'out_proj': {'kernel': *}, 'q_proj': {'kernel': *}, 'relative_position_bias': *, 'v_proj': {'kernel': *}}, 'dense1': {'bias': *, 'kernel': *}, 'dense2': {'bias': *, 'kernel': *}, 'layer_norm_1': {'bias': *, 'scale': *}, 'layer_norm_2': {'bias': *, 'scale': *}}}, 'EquivariantEmbed_0': {'orbit_bias_0': *, 'orbit_bias_1': *, 'orbit_bias_10': *, 'orbit_bias_11': *, 'orbit_bias_12': *, 'orbit_bias_13': *, 'orbit_bias_14': *, 'orbit_bias_15': *, 'orbit_bias_16': *, 'orbit_bias_17': *, 'orbit_bias_18': *, 'orbit_bias_19': *, 'orbit_bias_2': *, 'orbit_bias_20': *, 'orbit_bias_21': *, 'orbit_bias_22': *, 'orbit_bias_23': *, 'orbit_bias_24': *, 'orbit_bias_25': *, 'orbit_bias_26': *, 'orbit_bias_27': *, 'orbit_bias_28': *, 'orbit_bias_29': *, 'orbit_bias_3': *, 'orbit_bias_30': *, 'orbit_bias_31': *, 'orbit_bias_4': *, 'orbit_bias_5': *, 'orbit_bias_6': *, 'orbit_bias_7': *, 'orbit_bias_8': *, 'orbit_bias_9': *, 'orbit_weight_0': *, 'orbit_weight_1': *, 'orbit_weight_10': *, 'orbit_weight_11': *, 'orbit_weight_12': *, 'orbit_weight_13': *, 'orbit_weight_14': *, 'orbit_weight_15': *, 'orbit_weight_16': *, 'orbit_weight_17': *, 'orbit_weight_18': *, 'orbit_weight_19': *, 'orbit_weight_2': *, 'orbit_weight_20': *, 'orbit_weight_21': *, 'orbit_weight_22': *, 'orbit_weight_23': *, 'orbit_weight_24': *, 'orbit_weight_25': *, 'orbit_weight_26': *, 'orbit_weight_27': *, 'orbit_weight_28': *, 'orbit_weight_29': *, 'orbit_weight_3': *, 'orbit_weight_30': *, 'orbit_weight_31': *, 'orbit_weight_4': *, 'orbit_weight_5': *, 'orbit_weight_6': *, 'orbit_weight_7': *, 'orbit_weight_8': *, 'orbit_weight_9': *}, 'OutputHead_0': {'norm2': {'bias': *, 'scale': *}, 'norm3': {'bias': *, 'scale': *}, 'out_layer_norm': {'bias': *, 'scale': *}, 'output_layer0': {'bias': *, 'kernel': *}, 'output_layer1': {'bias': *, 'kernel': *}}})

This error is because you attempted to modify the ``parameters`` or ``variables`` attribute of a
variational state with a structure that does not match the previous structure.

To fix this error, you should ensure that the structure of the parameters you are trying to assign
matches the structure of the parameters that were already present in the variational state.

If you believe this error was thrown in error, or it prevents you from doing something, please open an issue.


-------------------------------------------------------
For more detailed informations, visit the following link:
	 https://netket.readthedocs.io/en/latest/api/_generated/errors/netket.errors.ParameterMismatchError.html
or the list of all common errors at
	 https://netket.readthedocs.io/en/latest/api/errors.html
-------------------------------------------------------

Completed training L=6, J2=1.0, J1=0.8 at: Thu Nov 13 12:58:31 +08 2025
==================== 所有训练任务完成 ====================
Job finished at: Thu Nov 13 12:58:31 +08 2025
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Resource Usage on 2025-11-13 12:58:31.743712:
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	JobId: 2874333.hpc-pbs-sched1
	Project: gs_spms_psengupta
	Exit Status: 0
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	NCPUs: Requested(1), Used(1)
	CPU Time Used: 00:04:13
	Memory: Requested(75gb), Used(665068kb)
	Vmem Used: 665068kb
	Walltime: Requested(1440:00:00), Used(00:04:57)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	Execution Nodes Used: (hpc-pinaki-gpu2:ngpus=1:mem=78643200kb:ncpus=1)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	No GPU-related information available for this job.

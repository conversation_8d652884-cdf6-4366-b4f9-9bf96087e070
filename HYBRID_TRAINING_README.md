# 混合NQS+归一化流训练框架

## 概述

本框架实现了神经网络量子态（NQS）与归一化流（Normalizing Flow, NF）采样器的联合训练。这是一个创新的方法，其中：

- **NQS波函数**：使用Vision Transformer (ViT)架构学习量子态的波函数
- **NF采样器**：使用归一化流学习从简单先验分布到目标概率分布 p(x) = |ψ_θ(x)|^2 的映射
- **解耦训练**：两个模型的参数在每一步训练中被同时但独立地优化

## 核心特性

### 1. 解耦梯度优化
- 能量损失 L_energy 的梯度只作用于 NQS 参数 θ
- NF损失 L_NF 的梯度只作用于 NF 参数 φ
- 总损失：L = L_energy + λ * L_NF

### 2. 归一化流采样器
- 使用JAX原生实现的MaskedCoupling层
- 支持混合精度训练
- 可配置的层数和隐藏层维度

### 3. 混合损失函数
- **能量损失**：标准VMC能量期望值
- **NF损失**：交叉熵损失，使NF输出分布匹配NQS振幅分布

## 文件结构

```
src/
├── models/
│   ├── nf_sampler.py          # 归一化流采样器实现
│   └── vit_state.py           # 现有的NQS模型
├── training/
│   └── hybrid_vmc.py          # 混合训练核心逻辑
scripts/
├── train_hybrid.py            # 混合训练脚本
└── test_hybrid_implementation.py  # 测试脚本
workflows/
├── configs/
│   └── train_hybrid.yaml      # 混合训练配置文件
└── hpc/
    └── submit_train_hybrid.pbs # HPC提交脚本
```

## 使用方法

### 1. 直接运行混合训练

```bash
# 激活环境
conda activate netket

# 运行混合训练
python scripts/train_hybrid.py 6 1.0 0.8 \
    --num_layers 4 --d_model 96 --n_heads 4 --patch_size 2 \
    --nf_layers 8 --nf_hidden_dims 64 64 \
    --n_steps 2000 --nqs_lr 0.001 --nf_lr 0.001 \
    --nf_weight 1.0 --n_samples 4096 \
    --enable_checkpoint --save_interval 100
```

### 2. 通过HPC提交

```bash
# 修改配置文件
vim workflows/configs/train_hybrid.yaml

# 提交任务
qsub workflows/hpc/submit_train_hybrid.pbs
```

## 配置参数

### 系统参数
- `L`: 晶格尺寸
- `J2`: J2耦合强度  
- `J1`: J1耦合强度

### NQS模型参数
- `num_layers`: Transformer层数
- `d_model`: 嵌入维度
- `n_heads`: 注意力头数
- `patch_size`: patch大小
- `use_mixed_precision`: 是否使用混合精度

### NF采样器参数
- `nf_layers`: 归一化流层数
- `nf_hidden_dims`: MLP隐藏层维度列表

### 训练参数
- `n_steps`: 训练步数
- `nqs_lr`: NQS学习率
- `nf_lr`: NF学习率
- `nf_weight`: NF损失权重 (λ)
- `n_samples`: 每步采样数量

## 核心算法

### 训练循环

每一步训练包含以下步骤：

1. **采样**：
   ```python
   # 使用NF采样器生成连续样本
   continuous_samples, _ = nf_sampler.apply(nf_params, key, n_samples)
   # 离散化为自旋构型
   samples = discretize_spins(continuous_samples, method="sign")
   ```

2. **损失计算**：
   ```python
   # 能量损失（只对NQS参数计算梯度）
   energy_loss = compute_energy_loss(nqs_model, nqs_params, hamiltonian, samples)
   
   # NF损失（只对NF参数计算梯度，NQS参数被stop_gradient）
   nf_loss = compute_nf_loss(nqs_model, nqs_params, nf_sampler, nf_params, 
                            samples, continuous_samples)
   ```

3. **解耦梯度更新**：
   ```python
   # 分别计算梯度
   nqs_grads = jax.grad(energy_loss_fn)(nqs_params)
   nf_grads = jax.grad(nf_loss_fn)(nf_params)
   
   # 分别更新参数
   nqs_params = optax.apply_updates(nqs_params, nqs_updates)
   nf_params = optax.apply_updates(nf_params, nf_updates)
   ```

### NF损失函数

NF损失是一个交叉熵损失：

```
L_NF = -Σ_x p_θ(x) log(p̂_φ(x))
```

其中：
- `p_θ(x) = |ψ_θ(x)|^2` 是NQS的振幅分布（归一化）
- `p̂_φ(x)` 是NF采样器为样本x赋予的概率密度

## 测试

运行测试脚本验证实现：

```bash
python test_hybrid_implementation.py
```

测试包括：
1. NF采样器功能测试
2. 混合训练步骤测试

## 输出和监控

### 训练日志
- 能量损失：`energy_loss`
- NF损失：`nf_loss`  
- 总损失：`total_loss`
- 能量方差：`energy_var`

### Checkpoint
- 自动保存NQS和NF参数
- 保存训练历史
- 支持从checkpoint恢复训练

### 文件输出
```
saved_models/L=6/J2=1.00/J1=0.80/
├── checkpoints/
│   ├── hybrid_checkpoint_iter_000100.pkl
│   ├── hybrid_checkpoint_iter_000200.pkl
│   └── ...
└── logs/
    └── hybrid_training_20231112_190000.log
```

## 理论背景

这个混合训练框架基于以下理论：

1. **变分蒙特卡洛**：通过最小化能量期望值来优化量子态波函数
2. **归一化流**：学习复杂概率分布的可逆变换
3. **解耦优化**：同时但独立地优化两个相关的目标函数

关键创新在于使用归一化流作为采样器，而不是传统的MCMC方法，这可能提供：
- 更好的采样效率
- 避免MCMC的自相关问题
- 更灵活的采样策略

## 注意事项

1. **内存使用**：混合训练需要存储两个模型的参数，内存使用量约为单独训练的2倍
2. **计算开销**：每步需要计算两个损失函数，训练时间会增加
3. **超参数调优**：NF损失权重λ需要仔细调节以平衡两个目标
4. **收敛性**：这是一个新的训练范式，收敛行为可能与传统VMC不同

## 扩展和改进

可能的改进方向：
1. 添加JIT编译支持以提高性能
2. 实现更复杂的归一化流架构
3. 添加自适应权重调节机制
4. 支持其他物理系统和哈密顿量

## 引用

如果您使用此框架，请引用相关的理论工作和本实现。

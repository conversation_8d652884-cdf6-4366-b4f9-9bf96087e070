"""
MinSR优化器驱动器模块

提供改进的MinSR驱动器实现，包括：
1. 学习率余弦退火调度（可选）
2. NaN检测和自动恢复
3. Checkpoint保存和管理

MinSR使用神经切线核(NTK)方法进行自然梯度下降优化。
"""

import time
import math
import jax
import jax.numpy as jnp
from jax import tree_util
import numpy as np
import netket as nk
from netket.driver import VMC_SR
from src.utils.logging import log_message


# ============================================================================
# VMC驱动器
# ============================================================================

class ImprovedMinSR(VMC_SR):
    """
    MinSR驱动器（使用NTK方法的自然梯度下降）

    特点：
    1. 学习率余弦退火调度（可选）
    2. NaN检测和自动恢复
    3. 使用NTK方法进行高效的自然梯度计算

    注意：直接使用父类VMC_SR的自然梯度更新，不进行二次更新或梯度裁剪。
    """

    def __init__(self, use_lr_schedule=True, *args, **kwargs):
        """
        Args:
            use_lr_schedule: 是否使用学习率调度
            *args, **kwargs: 传递给NetKet VMC_SR的参数
                自动设置 use_ntk=True 以启用MinSR/NTK方法
        """
        # 确保使用NTK方法（MinSR）
        kwargs['use_ntk'] = True

        # 提取hamiltonian参数，因为NetKet的VMC_SR需要它作为第一个位置参数
        hamiltonian = kwargs.pop('hamiltonian', None)
        if hamiltonian is None and args:
            hamiltonian = args[0]
            args = args[1:]

        # 调用父类构造函数
        super().__init__(hamiltonian, *args, **kwargs)

        self.use_lr_schedule = use_lr_schedule
        self.hamiltonian = hamiltonian

        # 学习率调度相关
        self.initial_lr = None
        self.current_lr = None
    
    def _step_with_state(self, state):
        """
        执行一步优化
        
        直接使用父类VMC_SR的自然梯度更新，避免二次更新问题。
        父类已经完成了SR预条件和参数更新，这里直接返回即可。
        """
        # 直接返回父类的更新结果，不进行二次更新
        return super()._step_with_state(state)


class CustomImprovedMinSR(ImprovedMinSR):
    """
    MinSR驱动器，带完整的训练管理功能

    功能：
    1. Linear Warm-up + 学习率余弦退火 + 热重启
    2. NaN检测和早停
    3. Checkpoint保存
    4. 详细日志输出
    5. 使用NTK方法进行高效的自然梯度计算
    """

    def __init__(self, initial_period=100, period_mult=2.0,
                 max_lr=None, min_lr=None,
                 base_lr=None,  # 新增：基础学习率
                 warmup_ratio=0.0,  # 新增：warm-up百分比（0-1之间）
                 checkpoint_callback=None, checkpoint_interval=500,
                 *args, **kwargs):
        """
        Args:
            initial_period: 初始重启周期
            period_mult: 周期倍增因子
            max_lr: 最大学习率（绝对值）
            min_lr: 最小学习率（绝对值）
            base_lr: 基础学习率（如果max_lr/min_lr未指定，则使用base_lr计算）
            warmup_ratio: warm-up阶段占总训练步数的百分比（0-1之间，默认0表示不使用warm-up）
            checkpoint_callback: checkpoint回调函数
            checkpoint_interval: checkpoint保存间隔
        """
        super().__init__(*args, **kwargs)

        self.max_nan_count = 5  # 最大允许连续NaN次数

        # 学习率调度参数
        self.initial_period = initial_period
        self.period_mult = period_mult
        self.warmup_ratio = warmup_ratio  # warm-up百分比
        self.warmup_iters = 0  # 实际warm-up迭代数（在run方法中计算）

        # 获取基础学习率（从优化器）
        self.base_lr = base_lr
        if self.base_lr is None:
            # 尝试从优化器获取
            self.base_lr = self._get_optimizer_lr()

        # 设置学习率范围
        if max_lr is not None and min_lr is not None:
            self.max_lr = max_lr
            self.min_lr = min_lr
        else:
            # 使用base_lr和因子计算
            self.max_lr = self.base_lr
            self.min_lr = self.base_lr * 0.01  # 默认衰减到1%

        # 记录重启信息
        self.current_restart = 0
        self.current_period = initial_period
        self.iter_since_restart = 0

        # Checkpoint相关
        self.checkpoint_callback = checkpoint_callback
        self.checkpoint_interval = checkpoint_interval

        # 当前学习率（warm-up阶段从0开始）
        self.current_lr = 0.0 if warmup_ratio > 0 else self.max_lr

        # 全局迭代计数器（用于warm-up）
        self.global_iter = 0
    
    def _get_optimizer_lr(self):
        """从优化器获取当前学习率"""
        # NetKet的SGD优化器使用 .learning_rate 属性
        if hasattr(self.optimizer, 'learning_rate'):
            return float(self.optimizer.learning_rate)
        elif hasattr(self.optimizer, '_lr'):
            return float(self.optimizer._lr)
        else:
            # 默认值
            return 0.001
    
    def _linear_warmup(self, iteration):
        """
        Linear warm-up学习率调度

        在warm-up阶段，学习率从0线性增长到max_lr

        Args:
            iteration: 当前全局迭代次数

        Returns:
            当前学习率（绝对值）
        """
        if iteration < self.warmup_iters:
            # Warm-up阶段：线性增长
            return self.max_lr * (iteration + 1) / self.warmup_iters
        else:
            # Warm-up结束，返回None表示使用余弦退火
            return None

    def _cosine_annealing_with_restart(self, iteration):
        """
        带热重启的余弦退火学习率调度

        lr(i) = lr_min + (lr_max - lr_min) * (1 + cos(π * i_cur / T_cur)) / 2

        Args:
            iteration: 当前迭代次数（相对于warm-up结束后）

        Returns:
            当前学习率（绝对值）
        """
        # 检查是否需要重启
        if self.iter_since_restart >= self.current_period:
            self.current_restart += 1
            self.iter_since_restart = 0
            # 更新下一个重启周期长度
            self.current_period = int(self.initial_period * (self.period_mult ** self.current_restart))

        cosine_factor = (1 + math.cos(math.pi * self.iter_since_restart / self.current_period)) / 2
        learning_rate = self.min_lr + (self.max_lr - self.min_lr) * cosine_factor

        self.iter_since_restart += 1
        return learning_rate
    
    def _update_learning_rate(self, learning_rate):
        """
        更新优化器的学习率

        Args:
            learning_rate: 新的学习率（绝对值）
        """
        self.current_lr = learning_rate

        # 更新优化器学习率
        # NetKet的SGD优化器使用 .learning_rate 属性
        if hasattr(self.optimizer, 'learning_rate'):
            self.optimizer.learning_rate = learning_rate
        elif hasattr(self.optimizer, '_lr'):
            self.optimizer._lr = learning_rate


    def run(self, n_iter, energy_log):
        """
        运行优化

        输出信息：
        - Energy: 能量期望值（优化目标）
        - E_var: 能量方差（应该较小）
        - E_err: 能量误差
        - LR: 当前学习率

        Args:
            n_iter: 迭代次数（不包括warm-up）
            energy_log: 日志文件路径
        """
        nan_count = 0  # 连续NaN计数器

        # 根据warmup_ratio计算实际的warmup_iters
        if self.warmup_ratio > 0:
            self.warmup_iters = int(n_iter * self.warmup_ratio)
            # 确保至少有1步warm-up
            if self.warmup_iters == 0 and self.warmup_ratio > 0:
                self.warmup_iters = 1
        else:
            self.warmup_iters = 0

        # 总迭代数 = warm-up迭代数 + 主训练迭代数
        total_iters = self.warmup_iters + n_iter
        
        # 记录开始时间
        start_time = time.time()

        # 记录开始训练的分隔线
        log_message(energy_log, "=" * 102)

        # 如果使用warm-up，记录信息
        if self.warmup_iters > 0:
            log_message(energy_log, f"🔥 Linear Warm-up: {self.warmup_iters} iterations ({self.warmup_ratio*100:.1f}% of {n_iter}) | LR: 0 -> {self.max_lr:.6f}")
            log_message(energy_log, f"   Total iterations: {self.warmup_iters} (warm-up) + {n_iter} (training) = {total_iters}")
        
        # 在第一个迭代前添加时间戳
        log_message(energy_log, f"🚀 Training iterations started at {time.strftime('%Y-%m-%d %H:%M:%S')}")

        for i in range(total_iters):
            prev_lr = self.current_lr  # 使用上一步的学习率

            # 学习率调度：先warm-up，再余弦退火
            if self.use_lr_schedule:
                # 尝试warm-up
                warmup_lr = self._linear_warmup(self.global_iter)

                if warmup_lr is not None:
                    # 在warm-up阶段
                    current_lr = warmup_lr
                    phase_info = f"WARMUP[{self.global_iter+1}/{self.warmup_iters}]"
                else:
                    # warm-up结束，使用余弦退火
                    current_lr = self._cosine_annealing_with_restart(i)
                    phase_info = f"R{self.current_restart}[{self.iter_since_restart-1}/{self.current_period}]"

                # 更新学习率
                self._update_learning_rate(current_lr)

                # 检测warm-up结束
                if self.global_iter == self.warmup_iters - 1:
                    log_message(energy_log, f"✅ Warm-up completed | Starting cosine annealing from LR={self.max_lr:.6f}")

                # 检测重启（学习率突然增大，且不在warm-up阶段）
                if warmup_lr is None and current_lr > prev_lr * 1.5 and i > 0:
                    log_message(energy_log, f"🔄 RESTART #{self.current_restart} | Period: {self.current_period}")
            else:
                current_lr = self.current_lr
                phase_info = f"Iter {i+1}"

            # 增加全局迭代计数
            self.global_iter += 1

            # 执行一步优化
            self.advance(1)

            # 获取能量统计信息
            energy_stats = self.estimate(self.hamiltonian)
            energy_mean = energy_stats.mean
            energy_var = energy_stats.variance
            energy_error = energy_stats.error_of_mean
            
            # 使用NetKet的统计功能获取完整的统计信息（包括R̂）
            additional_stats = self._get_netket_statistics(energy_stats)

            # 获取采样接受率统计信息
            # 根据 NetKet 文档: https://netket.readthedocs.io/en/latest/api/_generated/samplers/netket.sampler.MetropolisSamplerState.html#netket.sampler.MetropolisSamplerState.acceptance
            # MetropolisSamplerState.acceptance 属性返回接受率
            acceptance_rate = None
            try:
                # NetKet 驱动器使用 'state' 属性存储变分状态
                vqs = self.state
                if hasattr(vqs, 'sampler_state') and hasattr(vqs.sampler_state, 'acceptance'):
                    acc_val = vqs.sampler_state.acceptance
                    # acceptance 可能返回 None（如果还没有采样）
                    if acc_val is not None:
                        acceptance_rate = float(acc_val)
            except (AttributeError, TypeError, ValueError):
                # 如果无法获取接受率，保持为 None
                acceptance_rate = None

            # 检查是否为NaN
            if jnp.isnan(energy_mean):
                nan_count += 1
                log_message(energy_log, f"⚠️  NaN Energy at iter {i+1}/{n_iter} ({nan_count}/{self.max_nan_count})")

                # 如果连续NaN次数达到阈值，停止训练
                if nan_count >= self.max_nan_count:
                    log_message(energy_log, f"❌ Stopping: {self.max_nan_count} consecutive NaN values")
                    break
            else:
                # Energy正常，重置NaN计数器
                nan_count = 0

                # 获取当前学习率
                lr_str = f"LR: {self.current_lr:.6f}" if self.current_lr is not None else "LR: N/A"

                # 格式化接受率字符串
                if acceptance_rate is not None:
                    acc_str = f"Acc: {acceptance_rate:.4f}"
                else:
                    acc_str = "Acc: N/A"

                # 计算时间和速度信息
                current_time = time.time()
                elapsed_time = current_time - start_time
                sec_per_iter = elapsed_time / (i + 1) if (i + 1) > 0 else 0
                
                # 估计剩余时间
                if sec_per_iter > 0:
                    remaining_iters = total_iters - (i + 1)
                    eta_seconds = remaining_iters * sec_per_iter
                    eta_str = self._format_time(eta_seconds)
                    elapsed_str = self._format_time(elapsed_time)
                    speed_str = f"{sec_per_iter:.2f}s/it"
                else:
                    eta_str = "??:??"
                    elapsed_str = self._format_time(elapsed_time)
                    speed_str = "?.??s/it"
                
                # 构建进度信息
                progress_str = f"{elapsed_str}<{eta_str}, {speed_str}"
                
                # 构建迭代信息
                iter_str = f"[Iter {i+1:4d}/{total_iters}] {phase_info:13s}"
                
                # 构建能量信息
                energy_real_str = f"E: {jnp.real(energy_mean):10.6f}"
                
                # 构建能量详细统计信息
                energy_stats_parts = []
                if jnp.abs(jnp.imag(energy_mean)) > 1e-10:
                    energy_stats_parts.append(f"E_img: {jnp.imag(energy_mean):+.4f}j")
                energy_stats_parts.append(f"E_var: {energy_var:10.4f}")
                energy_stats_parts.append(f"E_err: {energy_error:10.6f}")
                
                # 添加R̂和τ统计信息
                if additional_stats['r_hat'] is not None:
                    energy_stats_parts.append(f"R̂: {additional_stats['r_hat']:.4f}")
                if additional_stats['tau_corr'] is not None and additional_stats['tau_corr'] > 0:
                    energy_stats_parts.append(f"τ: {additional_stats['tau_corr']:.1f}")
                
                energy_stats_str = " ".join(energy_stats_parts)
                
                # 格式化接受率
                acc_str = f"Acc: {acceptance_rate:.4f}" if acceptance_rate is not None else "Acc: N/A"
                
                # 构建完整的日志行，用 | 分隔
                log_line = f"{progress_str} | {iter_str} | {lr_str} | {energy_real_str} | {energy_stats_str} | {acc_str}"
                
                log_message(energy_log, log_line)

                # 保存checkpoint（仅在warm-up完成后）
                if (self.checkpoint_callback is not None and
                    self.checkpoint_interval > 0 and
                    i >= self.warmup_iters):
                    # 计算主训练阶段的迭代数（不包括warm-up）
                    training_iter = i - self.warmup_iters + 1
                    if training_iter % self.checkpoint_interval == 0:
                        # 传递额外的统计信息到checkpoint
                        extended_stats = {
                            'energy_mean': energy_mean,
                            'energy_error': energy_error,
                            'energy_var': energy_var,
                            'r_hat': additional_stats['r_hat'],
                            'tau_corr': additional_stats['tau_corr'],
                            'acceptance_rate': acceptance_rate
                        }
                        self.checkpoint_callback(training_iter, extended_stats)
        
        # 训练结束总结
        log_message(energy_log, "=" * 102)
        if nan_count >= self.max_nan_count:
            log_message(energy_log, "❌ TRAINING TERMINATED: Persistent NaN values")
        else:
            log_message(energy_log, f"✅ Training completed successfully")
            log_message(energy_log, f"Total restarts: {self.current_restart}")
            
            # 输出最终结果
            final_energy = self.estimate(self.hamiltonian)
            final_additional_stats = self._get_netket_statistics(final_energy)
            
            log_message(energy_log, "Final Training Statistics:")
            if jnp.abs(jnp.imag(final_energy.mean)) > 1e-10:
                log_message(energy_log, f"  Energy: {jnp.real(final_energy.mean):.8f}{jnp.imag(final_energy.mean):+.8f}j ± {final_energy.error_of_mean:.8f}")
            else:
                log_message(energy_log, f"  Energy: {jnp.real(final_energy.mean):.8f} ± {final_energy.error_of_mean:.8f}")
            log_message(energy_log, f"  Energy Variance: {final_energy.variance:.6f}")
            if final_additional_stats['r_hat'] is not None:
                log_message(energy_log, f"  R̂ (convergence): {final_additional_stats['r_hat']:.6f}")
            if final_additional_stats['tau_corr'] is not None and final_additional_stats['tau_corr'] > 0:
                log_message(energy_log, f"  Autocorrelation Time (τ): {final_additional_stats['tau_corr']:.2f}")
            if acceptance_rate is not None:
                log_message(energy_log, f"  Final Acceptance Rate: {acceptance_rate:.4f}")
        log_message(energy_log, "=" * 102)
        
        return self
    
    def _get_netket_statistics(self, energy_stats):
        """使用NetKet的统计功能获取完整的统计信息"""
        additional_stats = {
            'r_hat': None,
            'tau_corr': None,
            'full_stats': None
        }
        
        try:
            # 直接从energy_stats获取统计信息（NetKet的VMC驱动器已经计算了这些）
            if hasattr(energy_stats, 'R_hat') and not jnp.isnan(energy_stats.R_hat):
                additional_stats['r_hat'] = float(energy_stats.R_hat)
            
            if hasattr(energy_stats, 'tau_corr') and not jnp.isnan(energy_stats.tau_corr):
                additional_stats['tau_corr'] = float(energy_stats.tau_corr)
            
            additional_stats['full_stats'] = energy_stats
                
        except Exception as e:
            # 如果计算失败，保持默认值
            pass
            
        return additional_stats
    
    def _format_time(self, seconds):
        """格式化时间为 HH:MM:SS 格式"""
        if seconds < 0 or not np.isfinite(seconds):
            return "??:??:??"
        
        secs_total = int(round(seconds))
        hours = secs_total // 3600
        minutes = (secs_total % 3600) // 60
        secs = secs_total % 60
        
        if hours >= 100:
            return "99:59:59"  # 最大显示99小时59分59秒
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"


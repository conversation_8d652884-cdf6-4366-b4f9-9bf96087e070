#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Shastry-Sutherland_model运行器模块
提供了一种更面向对象的方式来进行Shastry-Sutherland模型模拟
"""

import os
import time
import numpy as np
import jax
import jax.numpy as jnp
import netket as nk
import netket.optimizer as nk_opt
from datetime import datetime
import pytz

from src.optimizers import CustomImprovedMinSR
from src.utils.logging import log_message
from src.physics.shastry_sutherland import (
    shastry_sutherland_lattice,
    shastry_sutherland_hamiltonian,
    shastry_sutherland_all_symmetries,
    shastry_sutherland_point_symmetries
)

class SSRunner:
    """Shastry-Sutherland模型运行器类，提供面向对象的接口"""
    
    def __init__(self, L, J1, J2, 
                 model_class=None, model_config=None, training_config=None, 
                 output_dir=None, checkpoint_config=None):
        """
        初始化运行器
        
        Args:
            L: 晶格尺寸
            J1: J1耦合强度
            J2: J2耦合强度
            model_class: 模型类
            model_config: 模型配置类
            training_config: 训练配置类
            output_dir: 输出目录。如果提供，将覆盖默认的目录结构。
            checkpoint_config: checkpoint配置字典
        """
        self.L = L
        self.J1 = J1
        self.J2 = J2
        self.Q = 1.00 - J2
        
        # 存储模型相关信息
        self.model_class = model_class
        
        # 处理配置（可以是字典、类或实例）
        self.model_config = self._process_config(model_config, 'model')
        self.training_config = self._process_config(training_config, 'training')
        
        # 如果没有提供模型类，使用默认的ViT
        if self.model_class is None:
            self.model_class = "ViT"  # 标识符
            self.model_config = self._dict_to_object({
                'd_model': 60,
                'num_layers': 4,
                'n_heads': 10,
                'patch_size': 2,
                'use_rpe': True  # 使用相对位置编码
            })
        
        # 设置输出目录
        self.output_dir = output_dir or self._create_output_dir()
        
        # 使用train.log作为日志文件
        self.energy_log = os.path.join(self.output_dir, "train.log")
        
        # 初始化checkpoint配置
        self.checkpoint_config = checkpoint_config or {}
        self.checkpoint_dir = os.path.join(self.output_dir, "checkpoints")
        if self.checkpoint_config.get('enable', False):
            os.makedirs(self.checkpoint_dir, exist_ok=True)
        
        # 初始化物理系统
        self.lattice = shastry_sutherland_lattice(L, L)
        self.N = self.lattice.n_nodes
    
    def _process_config(self, config, config_type):
        """
        处理配置，统一转换为支持属性访问的对象
        
        Args:
            config: 配置，可以是字典、类或实例
            config_type: 配置类型 ('model' 或 'training')
        
        Returns:
            支持属性访问的配置对象
        """
        if config is None:
            # 如果没有提供配置，创建默认配置对象
            if config_type == 'model':
                return self._dict_to_object({
                    'd_model': 60,
                    'num_layers': 4,
                    'n_heads': 10,
                    'patch_size': 2,
                    'use_rpe': True  # 使用相对位置编码
                })
            else:
                return self._dict_to_object({
                    'learning_rate': 0.015,
                    'n_cycles': 1,
                    'initial_period': 100,
                    'period_mult': 2.0,
                    'max_lr': 0.03,  # 最大学习率
                    'min_lr': 0.005,  # 最小学习率
                    'max_temperature': 1.0,  # 保留用于向后兼容
                    'min_temperature': 0.0,  # 保留用于向后兼容
                    'n_samples': 2**12,
                    'n_chains': 32,  # 并行链数（默认为 n_samples 的 1/128）
                    'd_max': None,  # 最大交换距离（None表示使用晶格最大距离）
                    'n_discard_per_chain': 0,
                    'chunk_size': 2**10,
                    'diag_shift': 0.20,
                    'grad_clip': 1.0,
                    'n_iters': 1000
                })
        
        if isinstance(config, dict):
            # 如果是字典，转换为支持属性访问的对象
            return self._dict_to_object(config)
        elif isinstance(config, type):
            # 如果是类，创建实例
            return config()
        else:
            # 如果已经是实例，直接使用
            return config
    
    def _dict_to_object(self, config_dict):
        """将字典转换为支持属性访问的对象"""
        class ConfigObject:
            def __init__(self, config_dict):
                for key, value in config_dict.items():
                    setattr(self, key, value)
            
            def get_total_iterations(self):
                """计算总迭代次数（用于训练配置）"""
                try:
                    n_cycles = getattr(self, 'n_cycles', 8)
                    initial_period = getattr(self, 'initial_period', 100)
                    period_mult = getattr(self, 'period_mult', 2.0)
                    
                    total_iters = 0
                    current_period = initial_period
                    for cycle in range(n_cycles):
                        total_iters += int(current_period * (period_mult ** cycle))
                    return total_iters
                except Exception:
                    return getattr(self, 'n_iters', 1000)
        
        return ConfigObject(config_dict)
    
    def _create_output_dir(self):
        """创建默认输出目录，保存到saved_models"""
        # 创建基于物理系统参数的输出目录
        # 格式: saved_models/L=X/J2=Y/J1=Z
        output_dir = f"saved_models/L={self.L}/J2={self.J2:.2f}/J1={self.J1:.2f}"
        os.makedirs(output_dir, exist_ok=True)
        return output_dir

    def setup_model(self):
        """设置模型和优化器"""
        # 检查是否需要从checkpoint恢复
        checkpoint_data = None
        if self.checkpoint_config.get('resume_from'):
            checkpoint_data = self._load_checkpoint()

        # 创建Hamiltonian
        self.H, self.hi = shastry_sutherland_hamiltonian(
            lattice=self.lattice,
            J1=self.J1,
            J2=self.J2,
            spin=0.5,
            Q=self.Q,
            total_sz=0
        )

        # 设置采样器
        distance = self.lattice.distances()
        max_distance = np.max(distance)

        # 获取采样器参数（支持新的可配置参数）
        # 注意：n_chains 应该从配置中读取，如果没有则默认等于 n_samples
        n_chains = getattr(self.training_config, 'n_chains', None)
        if n_chains is None:
            # 如果配置中没有 n_chains，则默认等于 n_samples
            n_chains = self.training_config.n_samples

        d_max = getattr(self.training_config, 'd_max', None)
        if d_max is None:
            d_max = max_distance

        self.sampler = nk.sampler.MetropolisExchange(
            hilbert=self.hi,
            graph=self.lattice,
            n_chains=n_chains,
            d_max=d_max,
            sweep_size=self.lattice.n_nodes  # 每个样本前进行完整格点sweep，确保充分去相关
        )

        # 根据模型类型设置模型
        if self.model_class == "ViT" or self.model_class == "GCNN":
            # 导入ViT模型
            from src.models.vit import ViT, ViTPartialEquivariant

            # 检查是否使用部分等变性
            use_partial_equivariance = getattr(self.model_config, 'use_partial_equivariance', False)
            use_symmetry = getattr(self.model_config, 'use_symmetry', True)

            # 验证配置：use_symmetry和use_partial_equivariance不能同时为True
            if use_symmetry and use_partial_equivariance:
                raise ValueError(
                    "use_symmetry和use_partial_equivariance不能同时为True。\n"
                    "请选择其中一个：\n"
                    "  - use_symmetry=True: 使用SymmExpSum包装整个模型\n"
                    "  - use_partial_equivariance=True: 仅在嵌入层使用等变权重共享"
                )

            # 获取对称性（如果需要）
            symmetries = None
            if use_partial_equivariance:
                # 嵌入层等变性仅使用点群对称性（C4v）
                symmetries = shastry_sutherland_point_symmetries(self.lattice)
            elif use_symmetry:
                # 全模型对称化仍使用空间群（包含平移）
                symmetries = shastry_sutherland_all_symmetries(self.lattice)

            if use_partial_equivariance:
                # 使用部分等变ViT（嵌入层等变，后续层普通）
                self.model = ViTPartialEquivariant(
                    num_layers=getattr(self.model_config, 'num_layers', 4),
                    d_model=getattr(self.model_config, 'd_model', 60),
                    n_heads=getattr(self.model_config, 'n_heads', 10),
                    patch_size=getattr(self.model_config, 'patch_size', 2),
                    symmetries=symmetries,
                    use_rpe=True,
                    dropout_rate=getattr(self.model_config, 'dropout_rate', 0.0),
                    mlp_ratio=getattr(self.model_config, 'mlp_ratio', 4.0),
                    use_mixed_precision=getattr(self.model_config, 'use_mixed_precision', True)
                )
                log_message(self.energy_log, "✓ 创建部分等变ViT模型（嵌入层等变）")
                log_message(self.energy_log, f"  对称性组大小: {len(symmetries)} (C4v点群)")
                log_message(self.energy_log, "  嵌入层使用等变权重共享，后续Transformer层使用普通结构")
            else:
                # 创建普通ViT模型（不带对称性）
                # 注意: 新的ViT实现使用相对位置编码(RPE)自动保持平移不变性
                model_no_symm = ViT(
                    num_layers=getattr(self.model_config, 'num_layers', 4),
                    d_model=getattr(self.model_config, 'd_model', 60),
                    n_heads=getattr(self.model_config, 'n_heads', 10),
                    patch_size=getattr(self.model_config, 'patch_size', 2),
                    use_rpe=True,  # 使用相对位置编码
                    dropout_rate=getattr(self.model_config, 'dropout_rate', 0.0),  # Dropout比率
                    mlp_ratio=getattr(self.model_config, 'mlp_ratio', 4.0),  # MLP隐藏层维度倍数
                    use_mixed_precision=getattr(self.model_config, 'use_mixed_precision', True)  # 混合精度训练
                )

                # 检查是否使用对称性约束
                if use_symmetry:
                    character_id = getattr(self.model_config, 'character_id', None)

                    self.model = nk.nn.blocks.SymmExpSum(
                        module=model_no_symm,
                        symm_group=symmetries,
                        character_id=character_id
                    )
                    log_message(self.energy_log, f"✓ 已应用对称性约束到ViT模型")
                    log_message(self.energy_log, f"  对称性组大小: {len(symmetries)} (space group)")
                    if character_id is not None:
                        log_message(self.energy_log, f"  特征标ID: {character_id}")
                else:
                    self.model = model_no_symm
                    log_message(self.energy_log, "✓ 创建ViT模型（不使用对称性约束）")
        else:
            raise ValueError(f"不支持的模型类型: {self.model_class}")

        # 创建变分量子态
        n_samples = self.training_config.n_samples
        chunk_size = self.training_config.chunk_size

        self.vqs = nk.vqs.MCState(
            sampler=self.sampler,
            model=self.model,
            n_samples=n_samples,
            n_discard_per_chain=self.training_config.n_discard_per_chain,
            chunk_size=chunk_size,
        )

        # 从checkpoint恢复参数（如果有）
        if checkpoint_data is not None:
            # 直接设置参数，不使用replace方法
            self.vqs.parameters = checkpoint_data['parameters']
            log_message(self.energy_log, "✓ 变分状态参数已从checkpoint恢复")
            # 保存起始迭代次数以便后续使用
            # 确保start_iteration是整数类型
            iteration_value = checkpoint_data['iteration']
            if isinstance(iteration_value, str) and iteration_value == 'final':
                # 如果是final状态，从0开始继续训练
                self.start_iteration = 0
                log_message(self.energy_log, "✓ 从final状态恢复, 重置迭代计数为0")
            else:
                # 确保是整数类型
                self.start_iteration = int(iteration_value)
        else:
            self.start_iteration = 0

        # ==================== 模型预热编译 ====================
        # 执行一次前向传播以触发JIT编译并缓存
        # 这样可以避免首次训练迭代时的长时间编译延迟
        log_message(self.energy_log, "🔥 预热编译: 执行模型前向传播以触发JIT编译...")
        warmup_start = time.time()

        # 创建虚拟输入（单个样本）
        dummy_input = jnp.zeros((1, self.N), dtype=jnp.int8)

        # 执行前向传播（触发编译）
        try:
            _ = self.vqs.model.apply(self.vqs.variables, dummy_input)
            warmup_time = time.time() - warmup_start
            log_message(self.energy_log, f"✓ 预热编译完成 | 耗时: {warmup_time:.2f}s")
        except Exception as e:
            log_message(self.energy_log, f"⚠️  预热编译失败（将在首次迭代时编译）: {e}")

        return self

    def _count_model_parameters(self):
        """计算模型总参数数量"""
        param_count = 0
        # 使用新的JAX API (jax.tree_util.tree_leaves 或 jax.tree.leaves)
        try:
            # 尝试使用新的API (JAX >= 0.4.25)
            leaves = jax.tree.leaves(self.vqs.parameters)
        except AttributeError:
            # 回退到旧的API
            leaves = jax.tree_util.tree_leaves(self.vqs.parameters)

        for param in leaves:
            param_count += param.size
        return param_count

    def _log_training_info(self, n_iter):
        """记录训练开始前的详细信息（按照YAML配置文件的分类组织）"""
        # 获取物理模型名称
        physics_model_name = "Shastry-Sutherland Model"

        log_message(self.energy_log, "="*102)
        log_message(self.energy_log, f"ViT for {physics_model_name}")
        log_message(self.energy_log, "="*102)

        # ==================== 系统参数配置 ====================
        log_message(self.energy_log, "System Parameters:")
        log_message(self.energy_log, f"  - Lattice size: L = {self.L}")
        log_message(self.energy_log, f"  - Total sites: N = {self.N}")
        log_message(self.energy_log, f"  - J1 coupling: {self.J1}")
        log_message(self.energy_log, f"  - J2 coupling: {self.J2}")
        log_message(self.energy_log, f"  - Q (4-spin): {self.Q}")

        # ==================== 模型参数 ====================
        log_message(self.energy_log, "-"*102)
        log_message(self.energy_log, "Model Parameters:")

        # ViT架构参数
        num_layers = getattr(self.model_config, 'num_layers', 4)
        d_model = getattr(self.model_config, 'd_model', 60)
        n_heads = getattr(self.model_config, 'n_heads', 10)
        patch_size = getattr(self.model_config, 'patch_size', 2)
        n_patches = self.N // (patch_size ** 2)

        log_message(self.energy_log, f"  ViT Architecture:")
        log_message(self.energy_log, f"    • Layers: {num_layers}")
        log_message(self.energy_log, f"    • Embedding dimension (d_model): {d_model}")
        log_message(self.energy_log, f"    • Attention heads: {n_heads}")
        log_message(self.energy_log, f"    • Patch size: {patch_size}x{patch_size}")
        log_message(self.energy_log, f"    • Number of patches: {n_patches}")
        log_message(self.energy_log, f"    • Config: [{num_layers}, {d_model}, {n_heads}, {patch_size}]")

        # 计算并显示总参数数
        total_params = self._count_model_parameters()
        log_message(self.energy_log, f"    • Total parameters: {total_params:,}")

        # 正则化参数
        use_rpe = getattr(self.model_config, 'use_rpe', True)
        dropout_rate = getattr(self.model_config, 'dropout_rate', 0.0)
        log_message(self.energy_log, f"  Regularization:")
        log_message(self.energy_log, f"    • Relative Position Encoding (RPE): {use_rpe}")
        log_message(self.energy_log, f"    • Dropout rate: {dropout_rate}")

        # 优化器参数
        diag_shift = getattr(self.training_config, 'diag_shift', 0.01)
        log_message(self.energy_log, f"  Optimizer:")
        log_message(self.energy_log, f"    • Diagonal shift (SR): {diag_shift}")

        # 混合精度训练信息
        use_mixed_precision = getattr(self.model_config, 'use_mixed_precision', True)
        log_message(self.energy_log, f"  Mixed Precision Training: {use_mixed_precision}")
        if use_mixed_precision:
            log_message(self.energy_log, f"    • Parameters: float64 (high precision)")
            log_message(self.energy_log, f"    • Computation: bfloat16 (accelerated)")
            log_message(self.energy_log, f"    • Critical ops (LayerNorm/Softmax/Output): float64")
            log_message(self.energy_log, f"    • Expected speedup: 1.5-2x, Memory reduction: ~50%")

        # ==================== 训练超参数 ====================
        log_message(self.energy_log, "-"*102)
        log_message(self.energy_log, "Training Hyperparameters:")

        # 学习率调度
        max_lr = getattr(self.training_config, 'max_lr', None)
        min_lr = getattr(self.training_config, 'min_lr', None)
        n_cycles = getattr(self.training_config, 'n_cycles', 1)
        initial_period = getattr(self.training_config, 'initial_period', 100)
        period_mult = getattr(self.training_config, 'period_mult', 2.0)
        warmup_ratio = getattr(self.training_config, 'warmup_ratio', 0.0)

        log_message(self.energy_log, f"  Learning Rate Schedule:")
        if max_lr is not None and min_lr is not None:
            log_message(self.energy_log, f"    • Max LR: {max_lr}")
            log_message(self.energy_log, f"    • Min LR: {min_lr}")
        else:
            max_factor = getattr(self.training_config, 'max_temperature', 1.0)
            min_factor = getattr(self.training_config, 'min_temperature', 0.0)
            log_message(self.energy_log, f"    • LR factor range: {min_factor} - {max_factor}")

        log_message(self.energy_log, f"    • Annealing cycles: {n_cycles}")
        log_message(self.energy_log, f"    • Initial period: {initial_period}")
        log_message(self.energy_log, f"    • Period multiplier: {period_mult}")

        if warmup_ratio > 0:
            warmup_iters = int(n_iter * warmup_ratio)
            if warmup_iters == 0 and warmup_ratio > 0:
                warmup_iters = 1
            log_message(self.energy_log, f"    • Warm-up: {warmup_iters} iterations ({warmup_ratio*100:.1f}%)")
            log_message(self.energy_log, f"    • Total iterations: {n_iter} + {warmup_iters} (warm-up) = {n_iter + warmup_iters}")
        else:
            log_message(self.energy_log, f"    • Warm-up: disabled")
            log_message(self.energy_log, f"    • Total iterations: {n_iter}")

        # 采样参数
        n_samples = self.training_config.n_samples
        n_chains = getattr(self.training_config, 'n_chains', n_samples)
        d_max = getattr(self.training_config, 'd_max', None)
        chunk_size = self.training_config.chunk_size
        n_discard = getattr(self.training_config, 'n_discard_per_chain', 0)

        log_message(self.energy_log, f"  Sampling Parameters:")
        log_message(self.energy_log, f"    • Samples (n_samples): {n_samples}")
        log_message(self.energy_log, f"    • Parallel chains (n_chains): {n_chains}")

        if d_max is None:
            distance = self.lattice.distances()
            d_max_actual = np.max(distance)
            log_message(self.energy_log, f"    • Max exchange distance (d_max): {d_max_actual:.2f} (auto from lattice)")
        else:
            log_message(self.energy_log, f"    • Max exchange distance (d_max): {d_max}")

        log_message(self.energy_log, f"    • Chunk size: {chunk_size}")
        log_message(self.energy_log, f"    • Discarded samples per chain: {n_discard}")

        # 参数-样本比分析
        param_sample_ratio = total_params / n_samples
        log_message(self.energy_log, f"    • Parameter-to-sample ratio: {param_sample_ratio:.2f}")
        if param_sample_ratio < 10:
            log_message(self.energy_log, f"      ✓ Good ratio (< 10), low overfitting risk")
        elif param_sample_ratio < 50:
            log_message(self.energy_log, f"      ⚠ Moderate ratio (10-50), consider regularization")
        else:
            log_message(self.energy_log, f"      ⚠ High ratio (> 50), high overfitting risk!")

        # ==================== Checkpoint配置 ====================
        log_message(self.energy_log, "-"*102)
        log_message(self.energy_log, "Checkpoint Configuration:")
        if self.checkpoint_config.get('enable', False):
            save_interval = self.checkpoint_config.get('save_interval', 500)
            keep_history = self.checkpoint_config.get('keep_history', True)
            log_message(self.energy_log, f"  • Enabled: Yes")
            log_message(self.energy_log, f"  • Save interval: {save_interval} iterations")
            log_message(self.energy_log, f"  • Keep history: {keep_history}")
            log_message(self.energy_log, f"  • Directory: {os.path.relpath(self.checkpoint_dir)}")
            if hasattr(self, 'start_iteration') and (isinstance(self.start_iteration, (int, float)) and self.start_iteration > 0 or isinstance(self.start_iteration, str) and self.start_iteration == 'final'):
                log_message(self.energy_log, f"  • Resuming from iteration: {self.start_iteration}")
        else:
            log_message(self.energy_log, f"  • Enabled: No")

        # ==================== 设备状态 ====================
        log_message(self.energy_log, "-"*102)
        log_message(self.energy_log, "Device Status:")

        # 获取GPU信息
        devices = jax.devices()
        num_devices = len(devices)

        # 尝试获取GPU型号
        try:
            # 获取第一个GPU设备的平台信息
            if devices and devices[0].platform == 'gpu':
                device_info = str(devices[0].device_kind)
                # 从设备信息中提取GPU型号，通常包含在device_kind中
                if 'H200' in device_info.upper():
                    gpu_model = 'H200'
                elif 'H100' in device_info.upper():
                    gpu_model = 'H100'
                elif 'A100' in device_info.upper():
                    gpu_model = 'A100'
                elif 'V100' in device_info.upper():
                    gpu_model = 'V100'
                elif 'T4' in device_info.upper():
                    gpu_model = 'T4'
                elif 'RTX' in device_info.upper():
                    gpu_model = 'RTX'
                else:
                    # 如果无法识别具体型号，显示原始信息
                    gpu_model = device_info
            else:
                gpu_model = 'CPU'
        except:
            gpu_model = 'Unknown'

        log_message(self.energy_log, f"  • Device type: {gpu_model}")
        log_message(self.energy_log, f"  • Number of devices: {num_devices}")
        log_message(self.energy_log, f"  • Sharding enabled: {nk.config.netket_experimental_sharding}")

        return self

    def run(self):
        """运行模拟"""
        # 运行完整的训练流程
        self._run_training()
        return self

    def _run_training(self):
        """统一的训练流程"""
        # 计算总迭代次数
        n_iter = self.training_config.get_total_iterations()

        # 记录详细的训练信息
        self._log_training_info(n_iter)

        # 记录时间
        start = time.time()

        # 创建优化器（学习率会被MinSR动态调整）
        # 使用max_lr作为初始学习率
        initial_lr = getattr(self.training_config, 'max_lr',
                            getattr(self.training_config, 'learning_rate', 0.01))
        optimizer = nk_opt.Sgd(learning_rate=initial_lr)

        # 准备checkpoint回调
        checkpoint_callback = None
        checkpoint_interval = 0
        if self.checkpoint_config.get('enable', False):
            checkpoint_interval = self.checkpoint_config.get('save_interval', 500)

            # 验证checkpoint间隔能整除总迭代数
            if n_iter % checkpoint_interval != 0:
                raise ValueError(
                    f"Checkpoint间隔 ({checkpoint_interval}) 必须能整除总迭代数 ({n_iter})。"
                    f"当前余数为 {n_iter % checkpoint_interval}。"
                    f"请调整checkpoint间隔或训练周期参数。"
                )

            checkpoint_callback = lambda iter_num, stats_data: self._save_checkpoint(
                iter_num, stats_data
            )

        # 学习率调度参数
        max_lr = getattr(self.training_config, 'max_lr', None)
        min_lr = getattr(self.training_config, 'min_lr', None)

        # 如果没有指定max_lr/min_lr，使用旧的temperature参数计算
        if max_lr is None or min_lr is None:
            base_lr = getattr(self.training_config, 'learning_rate', 0.01)
            max_temp = getattr(self.training_config, 'max_temperature', 1.0)
            min_temp = getattr(self.training_config, 'min_temperature', 0.0)
            max_lr = base_lr * max_temp
            min_lr = base_lr * min_temp

        # 用max_lr作为优化器的学习率（会被动态调整）
        base_lr = max_lr

        # 获取warm-up参数（百分比形式，0-1之间）
        warmup_ratio = getattr(self.training_config, 'warmup_ratio', 0.0)

        vmc = CustomImprovedMinSR(
            initial_period=self.training_config.initial_period,
            period_mult=self.training_config.period_mult,
            max_lr=max_lr,
            min_lr=min_lr,
            base_lr=base_lr,
            warmup_ratio=warmup_ratio,
            use_lr_schedule=True,
            checkpoint_callback=checkpoint_callback,
            checkpoint_interval=checkpoint_interval,
            hamiltonian=self.H,
            optimizer=optimizer,
            diag_shift=self.training_config.diag_shift,
            variational_state=self.vqs
        )

        # 运行优化
        vmc.run(n_iter=n_iter, energy_log=self.energy_log)

        end = time.time()

        runtime = end - start
        log_message(self.energy_log, "="*102)
        log_message(self.energy_log, f"Training completed | Runtime: {runtime:.1f}s")

    def _save_checkpoint(self, iteration, stats_data=None):
        """保存checkpoint到checkpoints子目录
        
        Args:
            iteration: 迭代次数
            stats_data: 统计数据，可以是旧格式的单独参数或新格式的字典
        """
        if not self.checkpoint_config.get('enable', False):
            return

        import pickle

        # 处理统计数据格式兼容性
        energy_data = None
        if stats_data is not None:
            if isinstance(stats_data, dict):
                # 新格式：包含扩展统计信息的字典
                energy_data = {
                    'mean': stats_data.get('energy_mean'),
                    'error': stats_data.get('energy_error'),
                    'variance': stats_data.get('energy_var'),
                    'r_hat': stats_data.get('r_hat'),
                    'tau_corr': stats_data.get('tau_corr'),
                    'acceptance_rate': stats_data.get('acceptance_rate')
                }
            else:
                # 旧格式兼容性：假设是 energy_mean
                energy_data = {
                    'mean': stats_data,
                    'error': None,
                    'variance': None
                }

        # 获取新加坡时区时间戳
        singapore_tz = pytz.timezone('Asia/Singapore')
        singapore_time = datetime.now(singapore_tz)
        
        checkpoint_data = {
            'iteration': iteration,
            'parameters': self.vqs.parameters,
            'model_type': self.model_class,
            'model_config': self.model_config.__dict__ if hasattr(self.model_config, '__dict__') else vars(self.model_config),
            'training_config': self.training_config.__dict__ if hasattr(self.training_config, '__dict__') else vars(self.training_config),
            'system_config': {
                'L': self.L, 'J1': self.J1, 'J2': self.J2, 'Q': self.Q
            },
            'energy': energy_data,
            'timestamp': singapore_time.isoformat()
        }

        # 保存checkpoint文件
        if self.checkpoint_config.get('keep_history', True):
            checkpoint_file = os.path.join(self.checkpoint_dir, f"checkpoint_iter_{iteration:06d}.pkl")
        else:
            checkpoint_file = os.path.join(self.checkpoint_dir, "latest_checkpoint.pkl")

        with open(checkpoint_file, "wb") as f:
            pickle.dump(checkpoint_data, f)

        log_message(self.energy_log, f"✓ Checkpoint saved: {os.path.basename(checkpoint_file)}")

    def _load_checkpoint(self, checkpoint_path=None):
        """从checkpoint恢复训练状态"""
        import pickle

        # 确定checkpoint路径
        if checkpoint_path is None:
            checkpoint_path = self.checkpoint_config.get('resume_from')

        if checkpoint_path is None:
            return None

        # 如果是相对路径，相对于当前工作目录解析
        if not os.path.isabs(checkpoint_path):
            checkpoint_path = os.path.abspath(checkpoint_path)

        # 如果路径是目录，查找最新的checkpoint
        if os.path.isdir(checkpoint_path):
            # 直接查找最新的checkpoint文件
            checkpoint_files = [f for f in os.listdir(checkpoint_path) if f.endswith('.pkl')]
            if not checkpoint_files:
                log_message(self.energy_log, f"⚠️  在目录 {checkpoint_path} 中未找到checkpoint文件")
                return None
            checkpoint_files.sort()
            checkpoint_file = os.path.join(checkpoint_path, checkpoint_files[-1])
        else:
            checkpoint_file = checkpoint_path

        if not os.path.exists(checkpoint_file):
            log_message(self.energy_log, f"⚠️  Checkpoint文件不存在: {checkpoint_file}")
            return None

        try:
            with open(checkpoint_file, "rb") as f:
                checkpoint_data = pickle.load(f)

            # 显示相对路径
            try:
                relative_path = os.path.relpath(checkpoint_file)
                log_message(self.energy_log, f"✓ 从checkpoint恢复: {relative_path}")
            except ValueError:
                # 如果无法计算相对路径（比如在不同驱动器上），则显示绝对路径
                log_message(self.energy_log, f"✓ 从checkpoint恢复: {checkpoint_file}")
            log_message(self.energy_log, f"  - 迭代次数: {checkpoint_data['iteration']}")
            if checkpoint_data.get('energy'):
                energy = checkpoint_data['energy']
                if energy.get('mean') is not None:
                    energy_str = f"  - 能量: {energy['mean']:.6f}"
                    if energy.get('error') is not None:
                        energy_str += f" ± {energy['error']:.6f}"
                    if energy.get('variance') is not None:
                        energy_str += f", Var: {energy['variance']:.6f}"
                    log_message(self.energy_log, energy_str)
                    
                    # 显示额外的统计信息
                    if energy.get('r_hat') is not None:
                        log_message(self.energy_log, f"  - R̂: {energy['r_hat']:.6f}")
                    if energy.get('tau_corr') is not None and energy['tau_corr'] > 0:
                        log_message(self.energy_log, f"  - 自相关时间 (τ): {energy['tau_corr']:.2f}")
                    if energy.get('acceptance_rate') is not None:
                        log_message(self.energy_log, f"  - 接受率: {energy['acceptance_rate']:.4f}")
            log_message(self.energy_log, f"  - 时间戳: {checkpoint_data['timestamp']}")

            return checkpoint_data

        except Exception as e:
            log_message(self.energy_log, f"⚠️  加载checkpoint失败: {e}")
            return None




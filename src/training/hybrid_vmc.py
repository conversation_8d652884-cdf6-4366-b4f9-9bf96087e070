"""
混合变分蒙特卡洛训练模块

实现NQS波函数和归一化流采样器的联合训练框架：
1. 使用NF采样器生成样本
2. NQS使用SR（Stochastic Reconfiguration）优化
3. NF使用Adam优化
4. 实现解耦训练
"""

import jax
import jax.numpy as jnp
import netket as nk
import optax
from typing import Tuple, Dict, Any, Optional, Callable
from functools import partial
import time

from ..models.nf_sampler import NFSampler, discretize_spins
from ..optimizers import CustomImprovedMinSR


class HybridVMCState:
    """
    混合VMC状态类
    
    管理NQS波函数和NF采样器的联合状态
    NQS使用SR优化，NF使用Adam优化
    """
    
    def __init__(self, 
                 nqs_vqs: nk.vqs.MCState,
                 nqs_sr_driver: CustomImprovedMinSR,
                 nf_sampler: NFSampler,
                 nf_params: Dict,
                 nf_optimizer: optax.GradientTransformation):
        """
        初始化混合VMC状态
        
        Args:
            nqs_vqs: NQS变分量子态（MCState）
            nqs_sr_driver: NQS的SR驱动器
            nf_sampler: 归一化流采样器
            nf_params: NF采样器参数
            nf_optimizer: NF优化器（Adam）
        """
        self.nqs_vqs = nqs_vqs
        self.nqs_sr_driver = nqs_sr_driver
        self.nf_sampler = nf_sampler
        self.nf_params = nf_params
        
        # NF优化器状态
        self.nf_opt_state = nf_optimizer.init(nf_params)
        self.nf_optimizer = nf_optimizer
        
        # 统计信息
        self.step = 0
        self.energy_history = []
        self.nf_loss_history = []
        self.total_loss_history = []


# 注意：能量计算现在由SR驱动器处理，不再需要单独的函数


def compute_nf_loss(nqs_model: Any,
                   nqs_params: Dict,
                   nf_sampler: NFSampler,
                   nf_params: Dict,
                   samples: jnp.ndarray,
                   continuous_samples: jnp.ndarray) -> Tuple[jnp.ndarray, Dict]:
    """
    计算NF采样器损失（交叉熵损失）
    
    目标：使NF的输出分布 p_φ(x) 匹配NQS的振幅分布 p_θ(x) = |ψ_θ(x)|^2
    损失：L_NF = -Σ_x p_θ(x) log(p̂_φ(x))
    
    Args:
        nqs_model: NQS波函数模型
        nqs_params: NQS模型参数（作为常数，不计算梯度）
        nf_sampler: NF采样器
        nf_params: NF采样器参数
        samples: 离散自旋构型样本 [n_samples, n_spins]
        continuous_samples: 对应的连续样本 [n_samples, n_spins]
        
    Returns:
        nf_loss: NF损失
        aux_data: 辅助数据
    """
    # 1. 计算NQS的振幅分布 p_θ(x) = |ψ_θ(x)|^2
    # 注意：这里nqs_params被视为常数，不计算梯度
    nqs_params_fixed = jax.lax.stop_gradient(nqs_params)
    log_psi = nqs_model.apply({'params': nqs_params_fixed}, samples)
    
    # 计算归一化的概率分布
    # log p_θ(x) = 2 * Re(log ψ_θ(x)) - log Z
    # 其中 Z 是配分函数，这里用样本近似
    log_prob_nqs = 2.0 * jnp.real(log_psi)
    log_prob_nqs = log_prob_nqs - jax.scipy.special.logsumexp(log_prob_nqs)
    prob_nqs = jnp.exp(log_prob_nqs)
    
    # 2. 计算NF采样器的概率密度 p̂_φ(x)
    # 这里需要通过连续样本计算概率密度
    log_prob_nf = nf_sampler.apply({'params': nf_params}, continuous_samples, method=nf_sampler.log_prob)
    
    # 由于离散化过程，需要考虑雅可比项
    # 这里简化处理，假设离散化不改变相对概率
    log_prob_nf = log_prob_nf - jax.scipy.special.logsumexp(log_prob_nf)
    
    # 3. 计算交叉熵损失
    # L_NF = -Σ_x p_θ(x) log(p̂_φ(x))
    nf_loss = -jnp.sum(prob_nqs * log_prob_nf)
    
    aux_data = {
        'prob_nqs_mean': jnp.mean(prob_nqs),
        'prob_nqs_std': jnp.std(prob_nqs),
        'log_prob_nf_mean': jnp.mean(log_prob_nf),
        'log_prob_nf_std': jnp.std(log_prob_nf),
        'kl_divergence': jnp.sum(prob_nqs * (log_prob_nqs - log_prob_nf))
    }
    
    return nf_loss, aux_data


# compute_hybrid_loss函数已删除，因为现在NQS使用SR优化，不再需要单独的能量损失函数

def update_sr_learning_rate(state: HybridVMCState, step: int, n_iter: int, warmup_ratio: float) -> float:
    """
    更新SR学习率（warm-up + 余弦退火 + 热重启）
    
    Args:
        state: 混合VMC状态
        step: 当前训练步数（从0开始）
        n_iter: 主训练迭代数（不包括warm-up）
        warmup_ratio: Warm-up比例
        
    Returns:
        current_lr: 当前学习率
    """
    sr_driver = state.nqs_sr_driver
    
    # 计算warm-up迭代数
    if warmup_ratio > 0:
        warmup_iters = int(n_iter * warmup_ratio)
        if warmup_iters == 0 and warmup_ratio > 0:
            warmup_iters = 1
    else:
        warmup_iters = 0
    
    # 更新SR驱动器的warmup_iters（确保一致性）
    sr_driver.warmup_iters = warmup_iters
    
    # 学习率调度：先warm-up，再余弦退火
    if sr_driver.use_lr_schedule:
        # 使用step作为全局迭代计数（从0开始）
        if step < warmup_iters:
            # Warm-up阶段：线性增长从0到max_lr
            current_lr = sr_driver.max_lr * (step + 1) / warmup_iters
        else:
            # warm-up结束，使用余弦退火
            # 计算相对于warm-up结束后的迭代数
            training_iter = step - warmup_iters
            
            # 初始化重启相关变量（如果未初始化）
            if not hasattr(sr_driver, 'current_restart'):
                sr_driver.current_restart = 0
                sr_driver.current_period = sr_driver.initial_period
                sr_driver.iter_since_restart = 0
            
            # 检查是否需要重启
            if sr_driver.iter_since_restart >= sr_driver.current_period:
                sr_driver.current_restart += 1
                sr_driver.iter_since_restart = 0
                # 更新下一个重启周期长度
                sr_driver.current_period = int(sr_driver.initial_period * (sr_driver.period_mult ** sr_driver.current_restart))
            
            # 计算余弦退火学习率
            import math
            cosine_factor = (1 + math.cos(math.pi * sr_driver.iter_since_restart / sr_driver.current_period)) / 2
            current_lr = sr_driver.min_lr + (sr_driver.max_lr - sr_driver.min_lr) * cosine_factor
            
            # 增加重启内迭代计数
            sr_driver.iter_since_restart += 1
        
        # 更新学习率
        sr_driver._update_learning_rate(current_lr)
    else:
        current_lr = sr_driver.current_lr if sr_driver.current_lr is not None else sr_driver.max_lr
    
    return current_lr


def hybrid_training_step(state: HybridVMCState,
                        key: jax.random.PRNGKey,
                        n_samples: int,
                        nf_weight: float = 1.0,
                        step: int = 0,
                        n_iter: int = 0) -> Tuple[HybridVMCState, Dict]:
    """
    混合训练步骤
    
    实现解耦优化：
    - NQS使用SR（Stochastic Reconfiguration）优化
    - NF使用Adam优化
    
    Args:
        state: 混合VMC状态
        key: 随机数生成器密钥
        n_samples: 采样数量
        nf_weight: NF损失权重
        step: 当前训练步数（用于学习率调度）
        n_iter: 主训练迭代数（用于学习率调度）
        
    Returns:
        new_state: 更新后的状态
        aux_data: 辅助数据
    """
    # 1. 使用NF采样器生成样本
    continuous_samples, _ = state.nf_sampler.apply({'params': state.nf_params}, key, n_samples)
    samples = discretize_spins(continuous_samples, method="sign")
    
    # 2. 将NF生成的样本设置到NQS的MCState中
    # 注意：需要确保样本格式正确（int8类型）
    samples_int8 = samples.astype(jnp.int8)
    
    # 手动设置MCState的样本（绕过采样器）
    # NetKet的MCState内部使用_samples属性存储样本
    # 我们直接设置这个内部属性
    # 注意：这是NetKet的内部API，但这是设置样本的唯一方法
    if hasattr(state.nqs_vqs, '_samples'):
        # 直接设置内部_samples属性
        state.nqs_vqs._samples = samples_int8
    elif hasattr(state.nqs_vqs, 'samples'):
        # 如果samples是公共属性，直接设置
        state.nqs_vqs.samples = samples_int8
    else:
        # 如果都不存在，尝试通过sampler_state设置
        sampler_state = state.nqs_vqs.sampler_state
        if hasattr(sampler_state, 'samples'):
            sampler_state.samples = samples_int8
        # 强制设置_samples（NetKet内部属性）
        state.nqs_vqs._samples = samples_int8
    
    # 3. 更新SR学习率（warm-up + 余弦退火 + 热重启）
    # 从driver获取warmup_ratio
    warmup_ratio = state.nqs_sr_driver.warmup_ratio if hasattr(state.nqs_sr_driver, 'warmup_ratio') else 0.0
    current_lr = update_sr_learning_rate(state, step, n_iter, warmup_ratio)
    
    # 4. 使用SR更新NQS参数
    # SR驱动器会自动计算能量并使用自然梯度更新NQS参数
    state.nqs_sr_driver.advance(1)
    
    # 获取更新后的NQS参数
    new_nqs_params = state.nqs_vqs.parameters
    
    # 获取能量统计信息
    energy_stats = state.nqs_sr_driver.estimate(state.nqs_sr_driver.hamiltonian)
    # 能量可能是复数，需要取实部
    energy_mean = float(jnp.real(energy_stats.mean))
    energy_var = float(jnp.real(energy_stats.variance))
    energy_std = float(jnp.real(energy_stats.error_of_mean))
    
    # 5. 计算NF损失并更新NF参数
    # 注意：NQS参数必须被stop_gradient，确保NF损失不影响NQS参数
    nqs_params_fixed = jax.lax.stop_gradient(new_nqs_params)
    
    def nf_loss_fn(nf_params_only):
        nf_loss, nf_aux = compute_nf_loss(
            state.nqs_vqs.model, 
            nqs_params_fixed,  # NQS参数被stop_gradient，不计算梯度
            state.nf_sampler, 
            nf_params_only, 
            samples_int8, 
            continuous_samples
        )
        return nf_weight * nf_loss, nf_aux
    
    # 计算NF梯度
    (weighted_nf_loss, nf_aux), nf_grads = jax.value_and_grad(
        nf_loss_fn, has_aux=True
    )(state.nf_params)
    
    # 更新NF参数
    nf_updates, new_nf_opt_state = state.nf_optimizer.update(nf_grads, state.nf_opt_state)
    new_nf_params = optax.apply_updates(state.nf_params, nf_updates)
    
    # 6. 更新状态
    state.nqs_vqs.parameters = new_nqs_params
    state.nf_params = new_nf_params
    state.nf_opt_state = new_nf_opt_state
    state.step += 1
    
    # 7. 收集辅助数据
    aux_data = {
        'energy_loss': energy_mean,
        'energy_mean': energy_mean,
        'energy_var': energy_var,
        'energy_std': energy_std,
        'nf_loss': weighted_nf_loss / nf_weight,  # 原始NF损失
        'weighted_nf_loss': weighted_nf_loss,
        'total_loss': energy_mean + weighted_nf_loss,
        'nf_weight': nf_weight,
        'current_lr': current_lr,  # 当前学习率
        'samples': samples_int8,
        'continuous_samples': continuous_samples,
        **{f'nf_{k}': v for k, v in nf_aux.items()}
    }
    
    # 更新历史记录
    state.energy_history.append(energy_mean)
    state.nf_loss_history.append(weighted_nf_loss / nf_weight)
    state.total_loss_history.append(energy_mean + weighted_nf_loss)
    
    return state, aux_data


def create_hybrid_vmc_state(nqs_vqs: nk.vqs.MCState,
                           hamiltonian: Any,
                           nf_sampler: NFSampler,
                           nf_params: Dict,
                           nf_learning_rate: float = 1e-3,
                           nf_optimizer_name: str = "adam",
                           # SR参数
                           initial_period: int = 100,
                           period_mult: float = 2.0,
                           max_lr: float = 0.01,
                           min_lr: float = 1e-7,
                           warmup_ratio: float = 0.0,
                           diag_shift: float = 0.005) -> HybridVMCState:
    """
    创建混合VMC状态
    
    Args:
        nqs_vqs: NQS变分量子态（MCState）
        hamiltonian: 哈密顿量
        nf_sampler: NF采样器
        nf_params: NF采样器初始参数
        nf_learning_rate: NF学习率
        nf_optimizer_name: NF优化器名称（只支持adam）
        initial_period: SR初始周期
        period_mult: SR周期倍增因子
        max_lr: SR最大学习率
        min_lr: SR最小学习率
        warmup_ratio: SR warm-up比例
        diag_shift: SR对角位移
        
    Returns:
        HybridVMCState实例
    """
    # 创建NF优化器（只支持Adam）
    if nf_optimizer_name.lower() == "adam":
        nf_optimizer = optax.adam(nf_learning_rate)
    else:
        raise ValueError(f"NF优化器只支持adam，不支持{nf_optimizer_name}")
    
    # 创建SR驱动器用于NQS优化
    # 需要一个基础优化器（SR会动态调整学习率）
    base_optimizer = nk.optimizer.Sgd(learning_rate=max_lr)
    
    nqs_sr_driver = CustomImprovedMinSR(
        hamiltonian=hamiltonian,
        variational_state=nqs_vqs,
        optimizer=base_optimizer,
        diag_shift=diag_shift,
        initial_period=initial_period,
        period_mult=period_mult,
        max_lr=max_lr,
        min_lr=min_lr,
        base_lr=max_lr,
        warmup_ratio=warmup_ratio,
        use_lr_schedule=True,
        checkpoint_callback=None,  # checkpoint在外部处理
        checkpoint_interval=0
    )
    
    return HybridVMCState(
        nqs_vqs=nqs_vqs,
        nqs_sr_driver=nqs_sr_driver,
        nf_sampler=nf_sampler,
        nf_params=nf_params,
        nf_optimizer=nf_optimizer
    )


def train_hybrid_vmc(state: HybridVMCState,
                    n_cycles: int = 3,
                    initial_period: int = 200,
                    period_mult: float = 2.0,
                    warmup_ratio: float = 0.0,
                    n_samples: int = 4096,
                    nf_weight: float = 1.0,
                    key: jax.random.PRNGKey = None,
                    log_interval: int = 10,
                    callback: Optional[Callable] = None,
                    energy_log: Optional[str] = None) -> HybridVMCState:
    """
    执行混合VMC训练
    
    Args:
        state: 混合VMC状态
        n_cycles: 退火周期数
        initial_period: 初始周期长度
        period_mult: 周期倍增因子
        warmup_ratio: Warm-up阶段占总训练步数的百分比
        n_samples: 每步采样数量
        nf_weight: NF损失权重
        key: 随机数生成器密钥
        log_interval: 日志输出间隔（未使用，保持兼容性）
        callback: 回调函数
        energy_log: 日志文件路径（用于输出训练信息）
        
    Returns:
        更新后的状态
    """
    from src.utils.logging import log_message
    
    if key is None:
        key = jax.random.PRNGKey(42)
    
    # 计算总迭代数（基于n_cycles）
    total_iters = 0
    current_period = initial_period
    for cycle in range(n_cycles):
        total_iters += int(current_period * (period_mult ** cycle))
    
    # 主训练迭代数（不包括warm-up）
    n_iter = total_iters
    
    # 计算warm-up迭代数（基于主训练迭代数）
    if warmup_ratio > 0:
        warmup_iters = int(n_iter * warmup_ratio)
        if warmup_iters == 0 and warmup_ratio > 0:
            warmup_iters = 1
    else:
        warmup_iters = 0
    
    # 总迭代数 = warm-up + 主训练
    total_iters_with_warmup = warmup_iters + n_iter
    
    # 记录开始时间
    start_time = time.time()
    
    # 记录开始训练的分隔线
    if energy_log:
        log_message(energy_log, "=" * 102)
    
    # 如果使用warm-up，记录信息
    if warmup_iters > 0:
        warmup_info = f"🔥 Linear Warm-up: {warmup_iters} iterations ({warmup_ratio*100:.1f}% of {n_iter}) | LR: 0 -> {state.nqs_sr_driver.max_lr:.6f}"
        print(warmup_info)
        if energy_log:
            log_message(energy_log, warmup_info)
            log_message(energy_log, f"   Total iterations: {warmup_iters} (warm-up) + {n_iter} (training) = {total_iters_with_warmup}")
    
    # 在第一个迭代前添加时间戳
    start_info = f"🚀 Training iterations started at {time.strftime('%Y-%m-%d %H:%M:%S')}"
    print(start_info)
    if energy_log:
        log_message(energy_log, start_info)
    
    # 初始化学习率调度相关变量
    prev_lr = state.nqs_sr_driver.current_lr if state.nqs_sr_driver.current_lr is not None else state.nqs_sr_driver.max_lr
    
    for step in range(total_iters_with_warmup):
        key, subkey = jax.random.split(key)
        
        # 执行训练步骤（NQS用SR，NF用Adam）
        state, aux_data = hybrid_training_step(
            state,
            subkey,
            n_samples,
            nf_weight,
            step=step,
            n_iter=n_iter
        )
        
        # 检测warm-up结束
        if warmup_iters > 0 and step == warmup_iters - 1:
            warmup_complete = f"✅ Warm-up completed | Starting cosine annealing from LR={state.nqs_sr_driver.max_lr:.6f}"
            print(warmup_complete)
            if energy_log:
                log_message(energy_log, warmup_complete)
        
        # 检测重启（学习率突然增大，且不在warm-up阶段）
        current_lr = aux_data.get('current_lr', state.nqs_sr_driver.current_lr)
        if step >= warmup_iters and current_lr > prev_lr * 1.5 and step > 0:
            restart_info = f"🔄 RESTART #{state.nqs_sr_driver.current_restart} | Period: {state.nqs_sr_driver.current_period}"
            print(restart_info)
            if energy_log:
                log_message(energy_log, restart_info)
        
        prev_lr = current_lr
        
        # 日志输出（每步都输出，格式与标准训练一致）
        elapsed = time.time() - start_time
        sec_per_iter = elapsed / (step + 1) if (step + 1) > 0 else 0
        
        # 估计剩余时间
        if sec_per_iter > 0:
            remaining_iters = total_iters_with_warmup - (step + 1)
            eta_seconds = remaining_iters * sec_per_iter
            eta_str = f"{int(eta_seconds//60)}:{int(eta_seconds%60):02d}"
            elapsed_str = f"{int(elapsed//60)}:{int(elapsed%60):02d}"
            speed_str = f"{sec_per_iter:.2f}s/it"
        else:
            eta_str = "??:??"
            elapsed_str = f"{int(elapsed//60)}:{int(elapsed%60):02d}"
            speed_str = "?.??s/it"
        
        progress_str = f"{elapsed_str}<{eta_str}, {speed_str}"
        
        # 构建迭代信息
        phase_info = ""
        if step < warmup_iters:
            phase_info = f"WARMUP[{step+1}/{warmup_iters}]"
        else:
            phase_info = f"R{state.nqs_sr_driver.current_restart}[{state.nqs_sr_driver.iter_since_restart}/{state.nqs_sr_driver.current_period}]"
        iter_str = f"[Iter {step+1:4d}/{total_iters_with_warmup}] {phase_info:13s}"
        
        # 构建能量信息
        energy_real_str = f"E: {aux_data['energy_loss']:10.6f}"
        
        # 构建能量详细统计信息
        energy_stats_parts = [
            f"E_var: {aux_data.get('energy_var', 0):10.4f}",
            f"E_err: {aux_data.get('energy_std', 0):10.6f}"
        ]
        energy_stats_str = " ".join(energy_stats_parts)
        
        # 学习率信息
        lr_str = f"LR: {aux_data.get('current_lr', 0):.6f}"
        
        # NF损失信息
        nf_loss_str = f"NF_loss: {aux_data['nf_loss']:.6f}"
        
        # 构建完整的日志行，用 | 分隔
        log_line = f"{progress_str} | {iter_str} | {lr_str} | {energy_real_str} | {energy_stats_str} | {nf_loss_str}"
        print(log_line)
        if energy_log:
            log_message(energy_log, log_line)
        
        # 调用回调函数（每步都调用，让回调函数决定是否写入日志）
        if callback is not None:
            callback(state, aux_data)
    
    total_time = time.time() - start_time
    complete_info = f"训练完成，总时间：{total_time:.1f}s"
    print(complete_info)
    if energy_log:
        log_message(energy_log, "="*102)
        log_message(energy_log, f"Training completed | Runtime: {total_time:.1f}s")
    
    return state

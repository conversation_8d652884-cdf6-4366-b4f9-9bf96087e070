"""
量子态生成和管理模块，提供创建和重建量子态的功能。
使用Vision Transformer (ViT) 模型
"""

import pickle
import jax
import jax.numpy as jnp
import netket as nk
import numpy as np

class ModelConfig:
    # ViT模型参数
    d_model = 60          # 嵌入维度
    num_layers = 4        # Transformer层数
    n_heads = 10          # 注意力头数
    patch_size = 2        # patch大小
    use_rpe = True        # 使用相对位置编码(自动保持平移不变性)

    # 对称性参数
    use_symmetry = True   # 使用对称性约束（默认启用）
    character_id = None   # 对称性特征标ID（默认None表示全对称表示）

    # 部分等变性参数
    use_partial_equivariance = False  # 使用部分等变性（嵌入层等变，后续层普通）

from src.physics.shastry_sutherland import (
    shastry_sutherland_lattice,
    shastry_sutherland_hamiltonian,
    shastry_sutherland_point_symmetries,
    shastry_sutherland_all_symmetries
)

from src.models.vit import ViT, ViTPartialEquivariant

def create_quantum_state(L, J2, J1, n_samples=None, n_chains=None, n_discard=None,
                        chunk_size=None, d_max=None,
                        num_layers=None, d_model=None, n_heads=None, patch_size=None,
                        use_rpe=None, use_symmetry=None, character_id=None,
                        use_partial_equivariance=None):
    """
    创建Shastry-Sutherland模型的量子态，使用ViT模型

    参数:
    L: 晶格大小
    J2: J2耦合强度
    J1: J1耦合强度
    n_samples: 采样数量，如果为None则使用默认值
    n_chains: 并行链数，如果为None则使用默认值（默认为n_samples的1/128）
    n_discard: 丢弃的样本数，如果为None则使用默认值
    chunk_size: 批处理大小，如果为None则使用默认值
    d_max: 最大交换距离，如果为None则使用晶格最大距离
    num_layers: Transformer层数，如果为None则使用默认值
    d_model: 嵌入维度，如果为None则使用默认值
    n_heads: 注意力头数，如果为None则使用默认值
    patch_size: patch大小，如果为None则使用默认值
    use_rpe: 是否使用相对位置编码，如果为None则使用默认值
    use_symmetry: 是否使用对称性约束，如果为None则使用默认值（True）
    character_id: 对称性特征标ID，如果为None则使用全对称表示
    use_partial_equivariance: 是否使用部分等变性（嵌入层等变），如果为None则使用默认值（False）

    返回:
    vqs: 变分量子态
    lattice: 晶格
    hilbert: 希尔伯特空间
    hamiltonian: 哈密顿量
    """
    # 使用默认值
    if n_samples is None:
        n_samples = 2**12  # 默认样本数量
    if n_chains is None:
        # 默认 n_chains 为 n_samples 的 1/128，与 train.yaml 配置一致
        n_chains = max(1, n_samples // 128)
    if n_discard is None:
        n_discard = 0      # 默认丢弃样本数
    if chunk_size is None:
        chunk_size = 2**10 # 默认批处理大小
    if num_layers is None:
        num_layers = ModelConfig.num_layers  # 默认层数
    if d_model is None:
        d_model = ModelConfig.d_model  # 默认嵌入维度
    if n_heads is None:
        n_heads = ModelConfig.n_heads  # 默认注意力头数
    if patch_size is None:
        patch_size = ModelConfig.patch_size  # 默认patch大小
    if use_rpe is None:
        use_rpe = ModelConfig.use_rpe  # 默认使用RPE
    if use_symmetry is None:
        use_symmetry = ModelConfig.use_symmetry  # 默认使用对称性
    if character_id is None:
        character_id = ModelConfig.character_id  # 默认特征标ID
    if use_partial_equivariance is None:
        use_partial_equivariance = ModelConfig.use_partial_equivariance  # 默认不使用部分等变性

    # 计算Q参数
    Q = 1.00 - J2

    # 创建晶格和哈密顿量
    lattice = shastry_sutherland_lattice(L, L)
    hamiltonian, hilbert = shastry_sutherland_hamiltonian(lattice, J1, J2, Q)

    # 计算最大距离（如果需要）
    if d_max is None:
        distance = lattice.distances()
        d_max = np.max(distance)

    # 创建采样器
    sampler = nk.sampler.MetropolisExchange(
        hilbert=hilbert,
        graph=lattice,
        n_chains=n_chains,
        d_max=d_max,
        sweep_size=lattice.n_nodes  # 每个样本前进行完整格点sweep，确保充分去相关
    )

    # 获取对称性（如果需要）
    symmetries = None
    if use_partial_equivariance:
        # 嵌入层等变性仅使用点群对称性（C4v）
        symmetries = shastry_sutherland_point_symmetries(lattice)
    elif use_symmetry:
        # 全模型对称化仍使用空间群（包含平移）
        symmetries = shastry_sutherland_all_symmetries(lattice)

    # 根据配置创建模型
    if use_partial_equivariance:
        # 使用部分等变ViT（嵌入层等变，后续层普通）
        print("使用部分等变ViT模型（嵌入层等变）")
        model = ViTPartialEquivariant(
            num_layers=num_layers,
            d_model=d_model,
            n_heads=n_heads,
            patch_size=patch_size,
            symmetries=symmetries,
            use_rpe=use_rpe
        )
        print(f"对称性组大小: {len(symmetries)} (C4v点群)")
        print("嵌入层使用等变权重共享，后续Transformer层使用普通结构")
    else:
        # 创建普通ViT模型
        model_no_symm = ViT(
            num_layers=num_layers,
            d_model=d_model,
            n_heads=n_heads,
            patch_size=patch_size,
            use_rpe=use_rpe
        )

        # 如果使用对称性约束，则用SymmExpSum包装模型
        if use_symmetry:
            # 使用SymmExpSum对ViT模型进行对称化
            model = nk.nn.blocks.SymmExpSum(
                module=model_no_symm,
                symm_group=symmetries,
                character_id=character_id
            )
            print(f"已应用点群对称性约束到ViT模型")
            print(f"对称性组大小: {len(symmetries)} (C4v点群)")
            if character_id is not None:
                print(f"特征标ID: {character_id}")
        else:
            model = model_no_symm
            print("未使用对称性约束")

    # 创建变分量子态
    vqs = nk.vqs.MCState(
        sampler=sampler,
        model=model,
        n_samples=n_samples,
        n_discard_per_chain=n_discard,
        chunk_size=chunk_size,
    )

    return vqs, lattice, hilbert, hamiltonian

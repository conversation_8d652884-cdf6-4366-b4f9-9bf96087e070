"""
归一化流采样器模块

实现基于JAX原生功能的归一化流模型，用于从简单先验分布（高斯）
映射到目标概率分布 p(x) = |ψ_θ(x)|^2

该模块支持：
1. 使用MaskedCoupling层构建归一化流
2. 从高斯先验采样并通过流变换得到连续样本
3. 通过离散化函数将连续样本转换为自旋构型
4. 计算样本的对数概率密度

注意：此实现使用JAX原生功能，不依赖distrax库
"""

import jax
import jax.numpy as jnp
import flax.linen as nn
from typing import Tuple, Optional, Callable
from jax import random


class MLP(nn.Module):
    """多层感知机，用于MaskedCoupling的条件网络"""
    
    hidden_dims: Tuple[int, ...]
    output_dim: int
    activation: Callable = nn.relu
    
    @nn.compact
    def __call__(self, x):
        for dim in self.hidden_dims:
            x = nn.Dense(dim)(x)
            x = self.activation(x)
        x = nn.Dense(self.output_dim)(x)
        return x


class MaskedCouplingLayer(nn.Module):
    """
    MaskedCoupling层的JAX原生实现
    
    实现仿射耦合层：y = x * exp(s) + t，其中s和t由条件网络生成
    """
    
    mask: jnp.ndarray              # 掩码数组
    hidden_dims: Tuple[int, ...]   # 条件网络隐藏层维度
    compute_dtype: jnp.dtype = jnp.float64
    
    def setup(self):
        """初始化条件网络"""
        n_masked = jnp.sum(self.mask)
        self.conditioner = MLP(
            hidden_dims=self.hidden_dims,
            output_dim=2 * n_masked,  # scale和shift参数
            activation=nn.relu
        )
    
    def forward_and_log_det(self, x: jnp.ndarray) -> Tuple[jnp.ndarray, jnp.ndarray]:
        """
        前向变换并计算对数雅可比行列式
        
        Args:
            x: 输入 [batch_size, n_dims]
            
        Returns:
            y: 变换后的输出 [batch_size, n_dims]
            log_det: 对数雅可比行列式 [batch_size]
        """
        x = x.astype(self.compute_dtype)
        
        # 分离掩码和非掩码部分
        x_masked = x * self.mask
        x_unmasked = x * (1 - self.mask)
        
        # 使用非掩码部分作为条件输入
        condition = x_unmasked
        
        # 通过条件网络生成scale和shift参数
        params = self.conditioner(condition)
        
        # 分离scale和shift参数
        n_masked = jnp.sum(self.mask)
        scale_params = params[..., :n_masked]
        shift_params = params[..., n_masked:]
        
        # 应用仿射变换到掩码部分
        # 使用tanh限制scale参数的范围，避免数值不稳定
        scale = jnp.tanh(scale_params)
        shift = shift_params
        
        # 重构scale和shift到完整维度
        scale_full = jnp.zeros_like(x)
        shift_full = jnp.zeros_like(x)
        
        # 只对掩码位置应用变换
        mask_indices = jnp.where(self.mask)[0]
        scale_full = scale_full.at[:, mask_indices].set(scale)
        shift_full = shift_full.at[:, mask_indices].set(shift)
        
        # 应用变换：y = x * exp(s) + t
        y = x_unmasked + (x_masked * jnp.exp(scale_full * self.mask) + shift_full * self.mask)
        
        # 计算对数雅可比行列式：log|det J| = sum(s_masked)
        log_det = jnp.sum(scale, axis=-1)
        
        return y, log_det
    
    def inverse_and_log_det(self, y: jnp.ndarray) -> Tuple[jnp.ndarray, jnp.ndarray]:
        """
        逆变换并计算对数雅可比行列式
        
        Args:
            y: 输入 [batch_size, n_dims]
            
        Returns:
            x: 逆变换后的输出 [batch_size, n_dims]
            log_det: 对数雅可比行列式 [batch_size]
        """
        y = y.astype(self.compute_dtype)
        
        # 分离掩码和非掩码部分
        y_unmasked = y * (1 - self.mask)
        
        # 使用非掩码部分作为条件输入
        condition = y_unmasked
        
        # 通过条件网络生成scale和shift参数
        params = self.conditioner(condition)
        
        # 分离scale和shift参数
        n_masked = jnp.sum(self.mask)
        scale_params = params[..., :n_masked]
        shift_params = params[..., n_masked:]
        
        # 应用仿射变换参数
        scale = jnp.tanh(scale_params)
        shift = shift_params
        
        # 重构scale和shift到完整维度
        scale_full = jnp.zeros_like(y)
        shift_full = jnp.zeros_like(y)
        
        mask_indices = jnp.where(self.mask)[0]
        scale_full = scale_full.at[:, mask_indices].set(scale)
        shift_full = shift_full.at[:, mask_indices].set(shift)
        
        # 逆变换：x = (y - t) * exp(-s)
        y_masked = y * self.mask
        x_masked = (y_masked - shift_full * self.mask) * jnp.exp(-scale_full * self.mask)
        x = y_unmasked + x_masked
        
        # 计算对数雅可比行列式：log|det J^{-1}| = -sum(s_masked)
        log_det = -jnp.sum(scale, axis=-1)
        
        return x, log_det


class NFSampler(nn.Module):
    """
    归一化流采样器
    
    使用一系列MaskedCoupling层构建归一化流，
    学习从标准高斯分布到目标分布的映射
    """
    
    n_spins: int                    # 自旋数量
    n_layers: int = 8               # 流层数
    hidden_dims: Tuple[int, ...] = (64, 64)  # MLP隐藏层维度
    use_mixed_precision: bool = True  # 是否使用混合精度
    
    @nn.compact
    def setup_layers(self):
        """设置归一化流层"""
        # 确定计算精度
        compute_dtype = jnp.bfloat16 if self.use_mixed_precision else jnp.float64
        
        # 创建MaskedCoupling层
        layers = []
        
        for i in range(self.n_layers):
            # 交替掩码模式
            mask = jnp.arange(self.n_spins) % 2 == (i % 2)
            
            # 创建MaskedCoupling变换
            coupling = MaskedCouplingLayer(
                mask=mask,
                hidden_dims=self.hidden_dims,
                compute_dtype=compute_dtype,
                name=f'coupling_{i}'
            )
            
            layers.append(coupling)
        
        return layers, compute_dtype
    
    @nn.compact
    def forward_transform(self, z: jnp.ndarray) -> Tuple[jnp.ndarray, jnp.ndarray]:
        """
        前向变换：z ~ N(0,I) -> y (连续样本)
        
        Args:
            z: 来自标准高斯分布的样本 [batch_size, n_spins]
            
        Returns:
            y: 变换后的连续样本 [batch_size, n_spins]
            log_det: 雅可比行列式的对数 [batch_size]
        """
        layers, compute_dtype = self.setup_layers()
        
        y = z.astype(compute_dtype)
        log_det = jnp.zeros(z.shape[0], dtype=compute_dtype)
        
        # 依次通过所有流层
        for layer in layers:
            y, layer_log_det = layer.forward_and_log_det(y)
            log_det += layer_log_det
        
        return y, log_det
    
    @nn.compact
    def inverse_transform(self, y: jnp.ndarray) -> Tuple[jnp.ndarray, jnp.ndarray]:
        """
        逆变换：y -> z ~ N(0,I)
        
        Args:
            y: 连续样本 [batch_size, n_spins]
            
        Returns:
            z: 变换到标准高斯的样本 [batch_size, n_spins]
            log_det: 雅可比行列式的对数 [batch_size]
        """
        layers, compute_dtype = self.setup_layers()
        
        z = y.astype(compute_dtype)
        log_det = jnp.zeros(y.shape[0], dtype=compute_dtype)
        
        # 反向通过所有流层
        for layer in reversed(layers):
            z, layer_log_det = layer.inverse_and_log_det(z)
            log_det += layer_log_det
        
        return z, log_det
    
    @nn.compact
    def sample(self, key: jax.random.PRNGKey, n_samples: int) -> Tuple[jnp.ndarray, jnp.ndarray]:
        """
        从流中采样
        
        Args:
            key: 随机数生成器密钥
            n_samples: 采样数量
            
        Returns:
            y: 连续样本 [n_samples, n_spins]
            log_prob: 样本的对数概率密度 [n_samples]
        """
        _, compute_dtype = self.setup_layers()
        
        # 从标准高斯分布采样
        z = random.normal(key, (n_samples, self.n_spins), dtype=compute_dtype)
        
        # 前向变换
        y, log_det = self.forward_transform(z)
        
        # 计算对数概率密度
        # log p(y) = log p(z) + log |det J|
        # 其中 log p(z) = -0.5 * (z^2 + log(2π))
        log_prob_z = -0.5 * (jnp.sum(z**2, axis=1) + self.n_spins * jnp.log(2 * jnp.pi))
        log_prob_y = log_prob_z + log_det
        
        return y, log_prob_y
    
    @nn.compact
    def log_prob(self, y: jnp.ndarray) -> jnp.ndarray:
        """
        计算样本的对数概率密度
        
        Args:
            y: 连续样本 [batch_size, n_spins]
            
        Returns:
            log_prob: 对数概率密度 [batch_size]
        """
        # 逆变换到标准高斯空间
        z, log_det = self.inverse_transform(y)
        
        # 计算标准高斯的对数概率密度
        log_prob_z = -0.5 * (jnp.sum(z**2, axis=1) + self.n_spins * jnp.log(2 * jnp.pi))
        
        # 变换公式：log p(y) = log p(z) + log |det J^{-1}|
        # 注意：inverse_transform返回的log_det是逆变换的，所以要取负号
        log_prob_y = log_prob_z - log_det
        
        return log_prob_y
    
    def __call__(self, key: jax.random.PRNGKey, n_samples: int) -> Tuple[jnp.ndarray, jnp.ndarray]:
        """调用采样方法"""
        return self.sample(key, n_samples)


def discretize_spins(y: jnp.ndarray, method: str = "sign") -> jnp.ndarray:
    """
    将连续样本离散化为自旋构型
    
    Args:
        y: 连续样本 [batch_size, n_spins]
        method: 离散化方法
            - "sign": x = sign(y) (适用于S=1/2系统)
            - "round": x = round(y) (适用于其他情况)
            
    Returns:
        x: 离散自旋构型 [batch_size, n_spins]
    """
    if method == "sign":
        # 对于S=1/2系统：+1 (up) 和 -1 (down)
        return jnp.sign(y).astype(jnp.int8)
    elif method == "round":
        # 四舍五入到最近整数
        return jnp.round(y).astype(jnp.int8)
    else:
        raise ValueError(f"Unknown discretization method: {method}")


def create_nf_sampler(n_spins: int, 
                     n_layers: int = 8,
                     hidden_dims: Tuple[int, ...] = (64, 64),
                     use_mixed_precision: bool = True) -> NFSampler:
    """
    创建归一化流采样器
    
    Args:
        n_spins: 自旋数量
        n_layers: 流层数
        hidden_dims: MLP隐藏层维度
        use_mixed_precision: 是否使用混合精度
        
    Returns:
        NFSampler实例
    """
    return NFSampler(
        n_spins=n_spins,
        n_layers=n_layers,
        hidden_dims=hidden_dims,
        use_mixed_precision=use_mixed_precision
    )


# 用于测试的辅助函数
def test_nf_sampler():
    """测试归一化流采样器的基本功能"""
    print("测试归一化流采样器...")
    
    # 参数
    n_spins = 16
    n_samples = 100
    key = random.PRNGKey(42)
    
    # 创建采样器
    sampler = create_nf_sampler(n_spins)
    
    # 初始化参数
    key_init, key_sample = random.split(key)
    dummy_key = random.PRNGKey(0)
    params = sampler.init(key_init, dummy_key, n_samples)
    
    # 采样
    y, log_prob = sampler.apply(params, key_sample, n_samples)
    
    print(f"连续样本形状: {y.shape}")
    print(f"对数概率形状: {log_prob.shape}")
    print(f"连续样本范围: [{y.min():.3f}, {y.max():.3f}]")
    
    # 离散化
    x = discretize_spins(y, method="sign")
    print(f"离散自旋构型形状: {x.shape}")
    print(f"离散自旋值: {jnp.unique(x)}")
    
    # 测试对数概率计算
    log_prob_test = sampler.apply(params, y, method=sampler.log_prob)
    print(f"对数概率计算测试通过: {jnp.allclose(log_prob, log_prob_test, atol=1e-5)}")
    
    print("✓ 归一化流采样器测试完成")


if __name__ == "__main__":
    test_nf_sampler()

"""
Vision Transformer (ViT) 模型实现，用于量子态波函数
基于 NetKet 教程: https://netket.readthedocs.io/en/latest/tutorials/ViT-wave-function.html
"""

from typing import Any
import jax
import jax.numpy as jnp
import flax
from flax import linen as nn
import netket as nk


def extract_patches2d(x, patch_size):
    """
    从2D配置中提取patches，专门针对Shastry-Sutherland晶格优化
    
    对于Shastry-Sutherland晶格，每个单元格包含4个格点，编号为：
    - site0 = base (左下角)
    - site1 = base + 1 (右下角) 
    - site2 = base + 2 (右上角)
    - site3 = base + 3 (左上角)
    
    这个函数确保每个单元格的4个格点被分到同一个patch中。
    
    参数:
    x: 输入配置，形状为 (batch, N_sites)，其中N_sites = 4*Lx*Ly
    patch_size: patch的线性大小，对于Shastry-Sutherland应该是2
    
    返回:
    patches: 形状为 (batch, n_patches, patch_size^2)
    """
    batch = x.shape[0]
    N_sites = x.shape[1]
    
    # 对于Shastry-Sutherland晶格，patch_size应该是2，每个patch包含4个格点
    if patch_size != 2:
        raise ValueError("对于Shastry-Sutherland晶格，patch_size必须为2")
    
    # 计算单元格数量（每个单元格4个格点）
    n_unit_cells = N_sites // 4
    
    # 重新排列数据，使每个单元格的4个格点连续排列
    # 输入: (batch, N_sites) 其中格点按 0,1,2,3, 4,5,6,7, ... 排列
    # 输出: (batch, n_unit_cells, 4) 其中每行是一个单元格的4个格点
    x_reshaped = x.reshape(batch, n_unit_cells, 4)
    
    # 对于Shastry-Sutherland晶格，我们需要将每个单元格的4个格点重新排列成2x2的形状
    # 原始编号: 0(左下), 1(右下), 2(右上), 3(左上)
    # 重新排列为2x2矩阵形式: [[0,1], [3,2]] -> 展平为 [0,1,3,2]
    # 这样可以保持空间邻近性
    
    # 创建重排列索引：[0,1,3,2] 对应 [左下,右下,左上,右上]
    reorder_indices = jnp.array([0, 1, 3, 2])
    
    # 应用重排列
    x_reordered = x_reshaped[:, :, reorder_indices]  # (batch, n_unit_cells, 4)
    
    # 最终输出形状
    patches = x_reordered  # (batch, n_patches, patch_size^2) 其中n_patches=n_unit_cells, patch_size^2=4
    
    return patches


class Embed(nn.Module):
    """嵌入层：将patches映射到嵌入空间"""
    d_model: int  # 嵌入维度
    patch_size: int  # patch的线性大小
    dtype: Any = jnp.bfloat16  # 计算精度（默认bfloat16用于混合精度）

    def setup(self):
        param_dtype = jnp.float64  # 参数始终保持float64高精度
        self.embed = nn.Dense(
            self.d_model,
            kernel_init=nn.initializers.xavier_uniform(),
            param_dtype=param_dtype,
            dtype=self.dtype,  # 计算使用指定精度
        )

    def __call__(self, x):
        x = extract_patches2d(x, self.patch_size)
        x = self.embed(x)
        return x


class EquivariantEmbed(nn.Module):
    """
    等变嵌入层：使用对称性约束的权重共享

    实现部分等变性（Partial Equivariance）：
    - 在嵌入阶段使用共享/对称权重（旋转/平移共享）
    - 通过对称群操作实现权重共享
    - 后续层使用普通结构

    参数:
        d_model: 嵌入维度
        patch_size: patch的线性大小
        symmetries: 对称群（PermutationGroup），用于定义权重共享模式
        dtype: 计算精度
    """
    d_model: int
    patch_size: int
    symmetries: Any  # PermutationGroup
    dtype: Any = jnp.bfloat16

    def setup(self):
        param_dtype = jnp.float64

        # 获取对称群的轨道（orbits）
        # 轨道定义了哪些位置在对称操作下是等价的
        self.n_sites = self.symmetries.degree
        self.orbits = self._compute_orbits()

        # 为每个轨道创建一个共享的嵌入权重
        # 这样在对称操作下等价的位置会共享相同的权重
        patch_dim = self.patch_size ** 2
        self.n_orbits = len(self.orbits)

        # 为每个轨道创建独立的嵌入矩阵
        self.orbit_weights = [
            self.param(
                f"orbit_weight_{i}",
                nn.initializers.xavier_uniform(),
                (patch_dim, self.d_model),
                param_dtype
            )
            for i in range(self.n_orbits)
        ]

        # 为每个轨道创建偏置
        self.orbit_biases = [
            self.param(
                f"orbit_bias_{i}",
                nn.initializers.zeros,
                (self.d_model,),
                param_dtype
            )
            for i in range(self.n_orbits)
        ]

    def _compute_orbits(self):
        """
        计算对称群的轨道

        轨道是在群作用下等价的位置集合
        例如，在C4旋转对称下，4个角位置形成一个轨道
        """
        import numpy as np

        n_sites = self.n_sites
        visited = np.zeros(n_sites, dtype=bool)
        orbits = []

        # 获取所有对称操作
        symmetry_ops = []
        for perm in self.symmetries:
            if type(perm).__name__ == 'Identity':
                perm_array = perm(np.arange(n_sites))
            elif hasattr(perm, 'inverse_permutation_array'):
                perm_array = perm.inverse_permutation_array
            else:
                continue
            symmetry_ops.append(perm_array)

        # 对每个未访问的位置，计算其轨道
        for i in range(n_sites):
            if not visited[i]:
                orbit = set()
                # 应用所有对称操作
                for perm_array in symmetry_ops:
                    orbit.add(int(perm_array[i]))

                orbit = sorted(list(orbit))
                orbits.append(orbit)

                # 标记轨道中的所有位置为已访问
                for j in orbit:
                    visited[j] = True

        return orbits

    def __call__(self, x):
        """
        前向传播

        参数:
            x: 输入自旋配置，形状 (batch, n_sites)

        返回:
            嵌入表示，形状 (batch, n_patches, d_model)
        """
        # 提取patches
        x = extract_patches2d(x, self.patch_size)  # (batch, n_patches, patch_dim)
        batch_size, n_patches, patch_dim = x.shape

        # 为每个patch分配对应的轨道权重
        # 首先需要建立patch索引到site索引的映射
        patch_to_sites = self._get_patch_to_sites_mapping(n_patches)

        # 为每个patch确定其所属的轨道
        patch_to_orbit = self._assign_patches_to_orbits(patch_to_sites)

        # 应用等变嵌入
        output = jnp.zeros((batch_size, n_patches, self.d_model), dtype=self.dtype)

        for patch_idx in range(n_patches):
            orbit_idx = patch_to_orbit[patch_idx]
            weight = self.orbit_weights[orbit_idx].astype(self.dtype)
            bias = self.orbit_biases[orbit_idx].astype(self.dtype)

            # 应用线性变换: x @ W + b
            patch_embedding = jnp.dot(x[:, patch_idx, :], weight) + bias
            output = output.at[:, patch_idx, :].set(patch_embedding)

        return output

    def _get_patch_to_sites_mapping(self, n_patches):
        """
        获取每个patch包含的site索引，专门针对Shastry-Sutherland晶格
        
        对于Shastry-Sutherland晶格，每个patch对应一个单元格，包含4个格点：
        - patch_idx对应单元格索引
        - 每个单元格包含sites: [base, base+1, base+2, base+3]
        - 其中base = 4 * patch_idx

        返回:
            列表，每个元素是一个patch包含的site索引列表
        """
        import numpy as np

        patch_to_sites = []
        for patch_idx in range(n_patches):
            # 对于Shastry-Sutherland晶格，每个patch对应一个单元格
            # 单元格内的4个格点编号为: base, base+1, base+2, base+3
            base = 4 * patch_idx
            sites = [base, base + 1, base + 2, base + 3]
            patch_to_sites.append(sites)

        return patch_to_sites

    def _assign_patches_to_orbits(self, patch_to_sites):
        """
        为每个patch分配其所属的轨道

        策略：使用patch中第一个site所属的轨道作为该patch的轨道

        参数:
            patch_to_sites: 每个patch包含的site索引列表

        返回:
            数组，每个patch对应的轨道索引
        """
        import numpy as np

        # 创建site到轨道的映射
        site_to_orbit = np.zeros(self.n_sites, dtype=int)
        for orbit_idx, orbit in enumerate(self.orbits):
            for site in orbit:
                site_to_orbit[site] = orbit_idx

        # 为每个patch分配轨道（使用第一个site的轨道）
        patch_to_orbit = []
        for sites in patch_to_sites:
            # 使用patch中第一个site的轨道
            orbit_idx = site_to_orbit[sites[0]]
            patch_to_orbit.append(orbit_idx)

        return np.array(patch_to_orbit)





def get_relative_position_index(n_patches):
    """
    生成相对位置索引矩阵

    对于2D网格，计算每对patch之间的相对位置
    返回形状为 (n_patches, n_patches, 2) 的索引，最后一维是 (dx, dy)
    """
    sq_n = int(n_patches ** 0.5)
    assert sq_n * sq_n == n_patches, "patches数量必须是完全平方数"

    # 创建坐标网格
    coords = jnp.stack(jnp.meshgrid(jnp.arange(sq_n), jnp.arange(sq_n), indexing='ij'), axis=-1)
    coords_flat = coords.reshape(-1, 2)  # (n_patches, 2)

    # 计算相对位置: relative_coords[i, j] = coords[i] - coords[j]
    relative_coords = coords_flat[:, None, :] - coords_flat[None, :, :]  # (n_patches, n_patches, 2)

    # 将相对坐标转换为非负索引
    # 相对坐标范围: [-(sq_n-1), sq_n-1]，共 2*sq_n-1 个可能值
    relative_coords = relative_coords + (sq_n - 1)  # 现在范围是 [0, 2*sq_n-2]

    # 将2D相对位置映射到1D索引
    # 使用公式: index = dx * (2*sq_n-1) + dy
    relative_position_index = relative_coords[:, :, 0] * (2 * sq_n - 1) + relative_coords[:, :, 1]

    return relative_position_index


class MultiHeadSelfAttention(nn.Module):
    """
    多头自注意力机制 (MHSA) with 相对位置编码

    实现论文公式: A = softmax((Q·K^T + P) / √d) · V
    其中 P 是相对位置编码，保持平移不变性
    """
    d_model: int  # 嵌入空间维度
    n_heads: int  # 注意力头数
    n_patches: int  # 输入序列长度
    use_rpe: bool = True  # 是否使用相对位置编码
    dropout_rate: float = 0.0  # Dropout比率
    dtype: Any = jnp.bfloat16  # 计算精度（默认bfloat16用于混合精度）

    def setup(self):
        assert self.d_model % self.n_heads == 0, "d_model必须能被n_heads整除"
        self.d_head = self.d_model // self.n_heads  # 每个头的维度

        param_dtype = jnp.float64  # 参数始终保持float64高精度

        # Q, K, V 投影矩阵 (使用线性层，如论文推荐)
        self.q_proj = nn.Dense(
            self.d_model,
            kernel_init=nn.initializers.xavier_uniform(),
            param_dtype=param_dtype,
            dtype=self.dtype,  # 计算使用指定精度
            use_bias=False,
        )
        self.k_proj = nn.Dense(
            self.d_model,
            kernel_init=nn.initializers.xavier_uniform(),
            param_dtype=param_dtype,
            dtype=self.dtype,
            use_bias=False,
        )
        self.v_proj = nn.Dense(
            self.d_model,
            kernel_init=nn.initializers.xavier_uniform(),
            param_dtype=param_dtype,
            dtype=self.dtype,
            use_bias=False,
        )

        # 输出投影
        self.out_proj = nn.Dense(
            self.d_model,
            kernel_init=nn.initializers.xavier_uniform(),
            param_dtype=param_dtype,
            dtype=self.dtype,
            use_bias=False,
        )

        # Dropout层
        if self.dropout_rate > 0:
            self.dropout = nn.Dropout(rate=self.dropout_rate)

        # 相对位置编码 (RPE)
        if self.use_rpe:
            sq_n = int(self.n_patches ** 0.5)
            assert sq_n * sq_n == self.n_patches, "使用RPE时patches数量必须是完全平方数"

            # 相对位置偏置表
            # 对于2D网格，相对位置范围是 [-(sq_n-1), sq_n-1]
            # 总共有 (2*sq_n-1) x (2*sq_n-1) 个可能的相对位置
            n_relative_positions = (2 * sq_n - 1) ** 2

            # 每个头有独立的相对位置偏置
            self.relative_position_bias = self.param(
                "relative_position_bias",
                nn.initializers.zeros,
                (self.n_heads, n_relative_positions),
                param_dtype,
            )

            # 预计算相对位置索引 (这是常量，不需要训练)
            self.relative_position_index = get_relative_position_index(self.n_patches)

    def __call__(self, x, deterministic=True):
        """
        参数:
            x: 输入张量，形状 (batch, n_patches, d_model)
            deterministic: 是否在确定性模式（训练时为False，推理时为True）

        返回:
            输出张量，形状 (batch, n_patches, d_model)
        """
        batch_size = x.shape[0]

        # 计算 Q, K, V
        Q = self.q_proj(x)  # (batch, n_patches, d_model)
        K = self.k_proj(x)  # (batch, n_patches, d_model)
        V = self.v_proj(x)  # (batch, n_patches, d_model)

        # 重塑为多头形式: (batch, n_patches, n_heads, d_head)
        Q = Q.reshape(batch_size, self.n_patches, self.n_heads, self.d_head)
        K = K.reshape(batch_size, self.n_patches, self.n_heads, self.d_head)
        V = V.reshape(batch_size, self.n_patches, self.n_heads, self.d_head)

        # 转置为 (batch, n_heads, n_patches, d_head) 以便矩阵乘法
        Q = Q.transpose(0, 2, 1, 3)
        K = K.transpose(0, 2, 1, 3)
        V = V.transpose(0, 2, 1, 3)

        # 计算注意力分数: Q @ K^T / sqrt(d_head)
        # (batch, n_heads, n_patches, d_head) @ (batch, n_heads, d_head, n_patches)
        # -> (batch, n_heads, n_patches, n_patches)
        attn_scores = jnp.matmul(Q, K.transpose(0, 1, 3, 2)) / jnp.sqrt(self.d_head)

        # 添加相对位置编码偏置
        if self.use_rpe:
            # 从偏置表中提取相对位置偏置
            # relative_position_bias: (n_heads, n_relative_positions)
            # relative_position_index: (n_patches, n_patches)
            relative_bias = self.relative_position_bias[:, self.relative_position_index]
            # 现在 relative_bias 形状: (n_heads, n_patches, n_patches)

            # 添加到注意力分数 (广播到batch维度)
            attn_scores = attn_scores + relative_bias[None, :, :, :]

        # Softmax归一化 - 转换到float64以保持数值稳定性
        attn_scores_f64 = attn_scores.astype(jnp.float64)
        attn_weights = jax.nn.softmax(attn_scores_f64, axis=-1)
        # 转回计算精度
        attn_weights = attn_weights.astype(self.dtype)

        # Attention dropout
        if self.dropout_rate > 0:
            attn_weights = self.dropout(attn_weights, deterministic=deterministic)

        # 应用注意力权重到V
        # (batch, n_heads, n_patches, n_patches) @ (batch, n_heads, n_patches, d_head)
        # -> (batch, n_heads, n_patches, d_head)
        attn_output = jnp.matmul(attn_weights, V)

        # 转置回 (batch, n_patches, n_heads, d_head)
        attn_output = attn_output.transpose(0, 2, 1, 3)

        # 合并多头: (batch, n_patches, d_model)
        attn_output = attn_output.reshape(batch_size, self.n_patches, self.d_model)

        # 输出投影
        output = self.out_proj(attn_output)

        # Output dropout
        if self.dropout_rate > 0:
            output = self.dropout(output, deterministic=deterministic)

        return output


class EncoderBlock(nn.Module):
    """
    Transformer编码器块

    结构: LayerNorm -> MHSA -> 残差连接 -> LayerNorm -> FFN -> 残差连接
    """
    d_model: int  # 嵌入空间维度
    n_heads: int  # 注意力头数
    n_patches: int  # 输入序列长度
    use_rpe: bool = True  # 是否使用相对位置编码
    dropout_rate: float = 0.0  # Dropout比率
    mlp_ratio: float = 4.0  # MLP隐藏层维度相对于d_model的倍数（标准Transformer使用4.0）
    dtype: Any = jnp.bfloat16  # 计算精度（默认bfloat16用于混合精度）

    def setup(self):
        param_dtype = jnp.float64  # 参数始终保持float64高精度

        # 多头自注意力
        self.attn = MultiHeadSelfAttention(
            d_model=self.d_model,
            n_heads=self.n_heads,
            n_patches=self.n_patches,
            use_rpe=self.use_rpe,
            dropout_rate=self.dropout_rate,
            dtype=self.dtype,
        )

        # Layer Normalization - 保持float64精度以确保数值稳定性
        self.layer_norm_1 = nn.LayerNorm(param_dtype=param_dtype, dtype=jnp.float64)
        self.layer_norm_2 = nn.LayerNorm(param_dtype=param_dtype, dtype=jnp.float64)

        # 前馈网络 (FFN)
        # 使用mlp_ratio控制隐藏层维度
        mlp_hidden_dim = int(self.d_model * self.mlp_ratio)
        self.dense1 = nn.Dense(
            mlp_hidden_dim,
            kernel_init=nn.initializers.xavier_uniform(),
            param_dtype=param_dtype,
            dtype=self.dtype,
        )
        self.dense2 = nn.Dense(
            self.d_model,
            kernel_init=nn.initializers.xavier_uniform(),
            param_dtype=param_dtype,
            dtype=self.dtype,
        )

        # Dropout层
        if self.dropout_rate > 0:
            self.dropout = nn.Dropout(rate=self.dropout_rate)

    def __call__(self, x, deterministic=True):
        # Pre-LN架构: LayerNorm -> Attention -> 残差
        # LayerNorm在float64下计算，输出转回计算精度
        x_norm = self.layer_norm_1(x.astype(jnp.float64)).astype(self.dtype)
        x = x + self.attn(x_norm, deterministic=deterministic)

        # Pre-LN架构: LayerNorm -> FFN -> 残差
        x_norm = self.layer_norm_2(x.astype(jnp.float64)).astype(self.dtype)
        ffn_out = self.dense1(x_norm)
        ffn_out = nn.gelu(ffn_out)
        if self.dropout_rate > 0:
            ffn_out = self.dropout(ffn_out, deterministic=deterministic)
        ffn_out = self.dense2(ffn_out)
        if self.dropout_rate > 0:
            ffn_out = self.dropout(ffn_out, deterministic=deterministic)
        x = x + ffn_out
        return x


class Encoder(nn.Module):
    """
    Transformer编码器

    由多个EncoderBlock堆叠而成
    """
    num_layers: int  # 层数
    d_model: int  # 嵌入空间维度
    n_heads: int  # 注意力头数
    n_patches: int  # 输入序列长度
    use_rpe: bool = True  # 是否使用相对位置编码
    dropout_rate: float = 0.0  # Dropout比率
    mlp_ratio: float = 4.0  # MLP隐藏层维度相对于d_model的倍数
    dtype: Any = jnp.bfloat16  # 计算精度（默认bfloat16用于混合精度）

    def setup(self):
        self.layers = [
            EncoderBlock(
                d_model=self.d_model,
                n_heads=self.n_heads,
                n_patches=self.n_patches,
                use_rpe=self.use_rpe,
                dropout_rate=self.dropout_rate,
                mlp_ratio=self.mlp_ratio,
                dtype=self.dtype,
            )
            for _ in range(self.num_layers)
        ]

    def __call__(self, x, deterministic=True):
        for l in self.layers:
            x = l(x, deterministic=deterministic)
        return x


class OutputHead(nn.Module):
    """输出层：将编码器输出映射到复数振幅"""
    d_model: int  # 嵌入空间维度
    dtype: Any = jnp.bfloat16  # 计算精度（默认bfloat16用于混合精度）

    def setup(self):
        param_dtype = jnp.float64  # 参数始终保持float64高精度

        # LayerNorm保持float64精度以确保数值稳定性
        self.out_layer_norm = nn.LayerNorm(param_dtype=param_dtype, dtype=jnp.float64)

        self.norm2 = nn.LayerNorm(
            use_scale=True, use_bias=True, param_dtype=param_dtype, dtype=jnp.float64
        )
        self.norm3 = nn.LayerNorm(
            use_scale=True, use_bias=True, param_dtype=param_dtype, dtype=jnp.float64
        )

        self.output_layer0 = nn.Dense(
            self.d_model,
            param_dtype=param_dtype,
            dtype=self.dtype,
            kernel_init=nn.initializers.xavier_uniform(),
            bias_init=jax.nn.initializers.zeros,
        )
        self.output_layer1 = nn.Dense(
            self.d_model,
            param_dtype=param_dtype,
            dtype=self.dtype,
            kernel_init=nn.initializers.xavier_uniform(),
            bias_init=jax.nn.initializers.zeros,
        )

    def __call__(self, x):
        # 对所有patches求和得到隐藏表示 - 在float64下进行求和以保持精度
        x_f64 = x.astype(jnp.float64)
        z = self.out_layer_norm(x_f64.sum(axis=1))

        # 分别计算实部和虚部 - 转回计算精度
        z_compute = z.astype(self.dtype)
        out_real = self.norm2(self.output_layer0(z_compute).astype(jnp.float64))
        out_imag = self.norm3(self.output_layer1(z_compute).astype(jnp.float64))

        # 组合成复数 - 在float64下进行以保持精度
        out = out_real + 1j * out_imag

        # 使用log_cosh激活函数 - 在float64下计算
        log_cosh = nk.nn.activation.log_cosh
        return jnp.sum(log_cosh(out), axis=-1)


class ViT(nn.Module):
    """
    Vision Transformer 量子态波函数

    使用真正的多头自注意力机制和相对位置编码(RPE)
    实现论文公式: A = softmax((Q·K^T + P) / √d) · V

    混合精度训练策略:
    - 参数保持float64高精度
    - 计算使用bfloat16加速（可配置）
    - 关键操作（LayerNorm, Softmax, 最终输出）保持float64精度
    """
    num_layers: int  # Transformer层数
    d_model: int  # 嵌入空间维度
    n_heads: int  # 注意力头数
    patch_size: int  # patch的线性大小
    use_rpe: bool = True  # 是否使用相对位置编码(默认开启)
    dropout_rate: float = 0.0  # Dropout比率（默认0表示不使用dropout）
    mlp_ratio: float = 4.0  # MLP隐藏层维度相对于d_model的倍数（标准Transformer使用4.0）
    use_mixed_precision: bool = True  # 是否使用混合精度训练（默认开启）

    @nn.compact
    def __call__(self, spins, deterministic=True):
        """
        参数:
            spins: 自旋配置
            deterministic: 是否在确定性模式（训练时为False，推理时为True）
                          注意：在量子态采样中，通常设为True以保持确定性
        """
        # 确定计算精度
        compute_dtype = jnp.bfloat16 if self.use_mixed_precision else jnp.float64

        x = jnp.atleast_2d(spins)

        Ns = x.shape[-1]  # 格点数
        n_patches = Ns // self.patch_size**2  # patches数量

        # 嵌入层
        x = Embed(d_model=self.d_model, patch_size=self.patch_size, dtype=compute_dtype)(x)

        # Transformer编码器
        y = Encoder(
            num_layers=self.num_layers,
            d_model=self.d_model,
            n_heads=self.n_heads,
            n_patches=n_patches,
            use_rpe=self.use_rpe,
            dropout_rate=self.dropout_rate,
            mlp_ratio=self.mlp_ratio,
            dtype=compute_dtype,
        )(x, deterministic=deterministic)

        # 输出层
        log_psi = OutputHead(d_model=self.d_model, dtype=compute_dtype)(y)

        return log_psi


class ViTPartialEquivariant(nn.Module):
    """
    部分等变Vision Transformer (Partial Equivariant ViT)

    实现部分等变性（Partial Equivariance）：
    - 在输入嵌入阶段使用等变层（通过权重共享实现对称性）
    - 后续Transformer层使用普通结构

    这种设计在嵌入层引入对称性归纳偏置，同时保持模型的表达能力

    参数:
        num_layers: Transformer层数
        d_model: 嵌入空间维度
        n_heads: 注意力头数
        patch_size: patch的线性大小
        symmetries: 对称群（PermutationGroup），用于等变嵌入层
        use_rpe: 是否使用相对位置编码
        dropout_rate: Dropout比率
        mlp_ratio: MLP隐藏层维度相对于d_model的倍数
        use_mixed_precision: 是否使用混合精度训练
    """
    num_layers: int
    d_model: int
    n_heads: int
    patch_size: int
    symmetries: Any  # PermutationGroup
    use_rpe: bool = True
    dropout_rate: float = 0.0
    mlp_ratio: float = 4.0
    use_mixed_precision: bool = True

    @nn.compact
    def __call__(self, spins, deterministic=True):
        """
        参数:
            spins: 自旋配置
            deterministic: 是否在确定性模式（训练时为False，推理时为True）
        """
        # 确定计算精度
        compute_dtype = jnp.bfloat16 if self.use_mixed_precision else jnp.float64

        x = jnp.atleast_2d(spins)

        Ns = x.shape[-1]  # 格点数
        n_patches = Ns // self.patch_size**2  # patches数量

        # 等变嵌入层（使用对称性约束的权重共享）
        x = EquivariantEmbed(
            d_model=self.d_model,
            patch_size=self.patch_size,
            symmetries=self.symmetries,
            dtype=compute_dtype
        )(x)

        # 普通Transformer编码器（不使用对称性约束）
        y = Encoder(
            num_layers=self.num_layers,
            d_model=self.d_model,
            n_heads=self.n_heads,
            n_patches=n_patches,
            use_rpe=self.use_rpe,
            dropout_rate=self.dropout_rate,
            mlp_ratio=self.mlp_ratio,
            dtype=compute_dtype,
        )(x, deterministic=deterministic)

        # 输出层
        log_psi = OutputHead(d_model=self.d_model, dtype=compute_dtype)(y)

        return log_psi


"""
Vision Transformer (ViT) 模型实现，用于量子态波函数
基于 NetKet 教程: https://netket.readthedocs.io/en/latest/tutorials/ViT-wave-function.html
"""

from typing import Any
import jax
import jax.numpy as jnp
import flax
from flax import linen as nn
import netket as nk


def extract_patches2d(x, patch_size):
    """
    从2D配置中提取patches，专门针对Shastry-Sutherland晶格优化
    
    对于Shastry-Sutherland晶格，每个单元格包含4个格点，编号为：
    - site0 = base (左下角)
    - site1 = base + 1 (右下角) 
    - site2 = base + 2 (右上角)
    - site3 = base + 3 (左上角)
    
    这个函数确保每个单元格的4个格点被分到同一个patch中。
    
    参数:
    x: 输入配置，形状为 (batch, N_sites)，其中N_sites = 4*Lx*Ly
    patch_size: patch的线性大小，对于Shastry-Sutherland应该是2
    
    返回:
    patches: 形状为 (batch, n_patches, patch_size^2)
    """
    batch = x.shape[0]
    N_sites = x.shape[1]
    
    # 对于Shastry-Sutherland晶格，patch_size应该是2，每个patch包含4个格点
    if patch_size != 2:
        raise ValueError("对于Shastry-Sutherland晶格，patch_size必须为2")
    
    # 计算单元格数量（每个单元格4个格点）
    n_unit_cells = N_sites // 4
    
    # 重新排列数据，使每个单元格的4个格点连续排列
    # 输入: (batch, N_sites) 其中格点按 0,1,2,3, 4,5,6,7, ... 排列
    # 输出: (batch, n_unit_cells, 4) 其中每行是一个单元格的4个格点
    x_reshaped = x.reshape(batch, n_unit_cells, 4)
    
    # 对于Shastry-Sutherland晶格，我们需要将每个单元格的4个格点重新排列成2x2的形状
    # 原始编号: 0(左下), 1(右下), 2(右上), 3(左上)
    # 重新排列为2x2矩阵形式: [[0,1], [3,2]] -> 展平为 [0,1,3,2]
    # 这样可以保持空间邻近性
    
    # 创建重排列索引：[0,1,3,2] 对应 [左下,右下,左上,右上]
    reorder_indices = jnp.array([0, 1, 3, 2])
    
    # 应用重排列
    x_reordered = x_reshaped[:, :, reorder_indices]  # (batch, n_unit_cells, 4)
    
    # 最终输出形状
    patches = x_reordered  # (batch, n_patches, patch_size^2) 其中n_patches=n_unit_cells, patch_size^2=4
    
    return patches


class HyperGNNLayer(nn.Module):
    """
    HyperGNN层：处理Shastry-Sutherland晶格的二体边和四体超边

    实现超图神经网络，同时处理：
    1. 二体边：J1 (正方形边) 和 J2 (对角线边)
    2. 四体超边：Q项的plaquette相互作用

    参数:
        d_model: 特征维度
        use_residual: 是否使用残差连接
        dtype: 计算精度
    """
    d_model: int
    use_residual: bool = True
    dtype: Any = jnp.bfloat16

    def setup(self):
        param_dtype = jnp.float64

        # 二体边消息传递网络
        self.edge_mlp = nn.Dense(
            self.d_model,
            kernel_init=nn.initializers.xavier_uniform(),
            param_dtype=param_dtype,
            dtype=self.dtype,
        )

        # 四体超边消息传递网络
        self.hyperedge_mlp = nn.Dense(
            self.d_model,
            kernel_init=nn.initializers.xavier_uniform(),
            param_dtype=param_dtype,
            dtype=self.dtype,
        )

        # 节点更新网络
        self.node_update_mlp = nn.Sequential([
            nn.Dense(self.d_model, param_dtype=param_dtype, dtype=self.dtype),
            nn.gelu,
            nn.Dense(self.d_model, param_dtype=param_dtype, dtype=self.dtype),
        ])

        # Layer normalization
        self.layer_norm = nn.LayerNorm(param_dtype=param_dtype, dtype=jnp.float64)

    def __call__(self, x, lattice_info):
        """
        参数:
            x: 节点特征，形状 (batch, n_sites, d_model)
            lattice_info: 包含晶格连接信息的字典
        """
        batch_size, n_sites, _ = x.shape

        # 1. 二体边消息传递
        edge_messages = self._compute_edge_messages(x, lattice_info['edges'])

        # 2. 四体超边消息传递
        hyperedge_messages = self._compute_hyperedge_messages(x, lattice_info['hyperedges'])

        # 3. 聚合消息
        aggregated_messages = edge_messages + hyperedge_messages

        # 4. 节点更新
        updated_features = self.node_update_mlp(aggregated_messages)

        # 5. 残差连接和层归一化
        if self.use_residual:
            output = x + updated_features
        else:
            output = updated_features

        # Layer norm在float64下计算，然后转回计算精度
        output_f64 = self.layer_norm(output.astype(jnp.float64))
        return output_f64.astype(self.dtype)

    def _compute_edge_messages(self, x, edges):
        """计算二体边的消息传递"""
        batch_size, n_sites, d_model = x.shape
        edge_messages = jnp.zeros_like(x)

        for edge_type, edge_list in edges.items():
            for i, j in edge_list:
                # 计算边特征：节点i和j的特征拼接
                edge_feat = x[:, i, :] + x[:, j, :]  # 简单求和，也可以用拼接
                message = self.edge_mlp(edge_feat)

                # 将消息传递给两个节点
                edge_messages = edge_messages.at[:, i, :].add(message)
                edge_messages = edge_messages.at[:, j, :].add(message)

        return edge_messages

    def _compute_hyperedge_messages(self, x, hyperedges):
        """计算四体超边的消息传递"""
        batch_size, n_sites, d_model = x.shape
        hyperedge_messages = jnp.zeros_like(x)

        for hyperedge in hyperedges:
            # 计算超边特征：四个节点特征的平均
            hyperedge_feat = jnp.mean(x[:, hyperedge, :], axis=1)  # (batch, d_model)
            message = self.hyperedge_mlp(hyperedge_feat)

            # 将消息传递给超边中的所有节点
            for node in hyperedge:
                hyperedge_messages = hyperedge_messages.at[:, node, :].add(message)

        return hyperedge_messages


class Embed(nn.Module):
    """基础嵌入层：将patches映射到嵌入空间"""
    d_model: int  # 嵌入维度
    patch_size: int  # patch的线性大小
    dtype: Any = jnp.bfloat16  # 计算精度（默认bfloat16用于混合精度）

    def setup(self):
        param_dtype = jnp.float64  # 参数始终保持float64高精度
        self.embed = nn.Dense(
            self.d_model,
            kernel_init=nn.initializers.xavier_uniform(),
            param_dtype=param_dtype,
            dtype=self.dtype,  # 计算使用指定精度
        )

    def __call__(self, x):
        x = extract_patches2d(x, self.patch_size)
        x = self.embed(x)
        return x


class HyperGNNEquivariantEmbed(nn.Module):
    """
    融合对称性的HyperGNN嵌入层

    结合了：
    1. HyperGNN：处理Shastry-Sutherland晶格的二体边和四体超边
    2. 等变性：使用对称性约束的权重共享

    架构：
    输入自旋配置 → 初始嵌入 → HyperGNN层(1-2层) → 物理感知特征 → patch聚合

    参数:
        d_model: 嵌入维度
        patch_size: patch的线性大小
        symmetries: 对称群（PermutationGroup），用于定义权重共享模式
        n_gnn_layers: HyperGNN层数（1-2层）
        dtype: 计算精度
    """
    d_model: int
    patch_size: int
    symmetries: Any  # PermutationGroup
    n_gnn_layers: int = 2
    dtype: Any = jnp.bfloat16

    def setup(self):
        param_dtype = jnp.float64

        # 获取对称群的轨道（orbits）
        self.n_sites = self.symmetries.degree
        self.orbits = self._compute_orbits(self.symmetries, self.n_sites)
        self.n_orbits = len(self.orbits)

        # 1. 初始节点嵌入（等变）
        # 为每个轨道创建共享的初始嵌入权重
        self.orbit_embeddings = [
            self.param(
                f"orbit_embed_{i}",
                nn.initializers.xavier_uniform(),
                (1, self.d_model),  # 每个自旋值映射到d_model维
                param_dtype
            )
            for i in range(self.n_orbits)
        ]

        # 2. HyperGNN层（等变）
        # 为每个轨道创建共享的HyperGNN参数
        gnn_layers = []
        for layer_idx in range(self.n_gnn_layers):
            layer_params = {}
            for orbit_idx in range(self.n_orbits):
                # 每个轨道的边MLP权重
                layer_params[f"edge_mlp_w_{orbit_idx}"] = self.param(
                    f"gnn_{layer_idx}_edge_mlp_w_{orbit_idx}",
                    nn.initializers.xavier_uniform(),
                    (self.d_model, self.d_model),
                    param_dtype
                )
                layer_params[f"edge_mlp_b_{orbit_idx}"] = self.param(
                    f"gnn_{layer_idx}_edge_mlp_b_{orbit_idx}",
                    nn.initializers.zeros,
                    (self.d_model,),
                    param_dtype
                )

                # 每个轨道的超边MLP权重
                layer_params[f"hyperedge_mlp_w_{orbit_idx}"] = self.param(
                    f"gnn_{layer_idx}_hyperedge_mlp_w_{orbit_idx}",
                    nn.initializers.xavier_uniform(),
                    (self.d_model, self.d_model),
                    param_dtype
                )
                layer_params[f"hyperedge_mlp_b_{orbit_idx}"] = self.param(
                    f"gnn_{layer_idx}_hyperedge_mlp_b_{orbit_idx}",
                    nn.initializers.zeros,
                    (self.d_model,),
                    param_dtype
                )

                # 每个轨道的节点更新MLP权重
                layer_params[f"node_update_w1_{orbit_idx}"] = self.param(
                    f"gnn_{layer_idx}_node_update_w1_{orbit_idx}",
                    nn.initializers.xavier_uniform(),
                    (self.d_model, self.d_model),
                    param_dtype
                )
                layer_params[f"node_update_b1_{orbit_idx}"] = self.param(
                    f"gnn_{layer_idx}_node_update_b1_{orbit_idx}",
                    nn.initializers.zeros,
                    (self.d_model,),
                    param_dtype
                )
                layer_params[f"node_update_w2_{orbit_idx}"] = self.param(
                    f"gnn_{layer_idx}_node_update_w2_{orbit_idx}",
                    nn.initializers.xavier_uniform(),
                    (self.d_model, self.d_model),
                    param_dtype
                )
                layer_params[f"node_update_b2_{orbit_idx}"] = self.param(
                    f"gnn_{layer_idx}_node_update_b2_{orbit_idx}",
                    nn.initializers.zeros,
                    (self.d_model,),
                    param_dtype
                )

            gnn_layers.append(layer_params)

        # 存储为实例变量
        self.gnn_layers = gnn_layers

        # 3. 最终patch聚合层（等变）
        patch_dim = self.patch_size ** 2
        self.patch_aggregation_weights = [
            self.param(
                f"patch_agg_w_{i}",
                nn.initializers.xavier_uniform(),
                (patch_dim * self.d_model, self.d_model),
                param_dtype
            )
            for i in range(self.n_orbits)
        ]

        self.patch_aggregation_biases = [
            self.param(
                f"patch_agg_b_{i}",
                nn.initializers.zeros,
                (self.d_model,),
                param_dtype
            )
            for i in range(self.n_orbits)
        ]

        # Layer normalization
        self.layer_norm = nn.LayerNorm(param_dtype=param_dtype, dtype=jnp.float64)

    @staticmethod
    def _compute_orbits(symmetries, n_sites):
        """
        计算对称群的轨道

        轨道是在群作用下等价的位置集合
        例如，在C4旋转对称下，4个角位置形成一个轨道
        """
        import numpy as np

        visited = np.zeros(n_sites, dtype=bool)
        orbits = []

        # 获取所有对称操作
        symmetry_ops = []
        for perm in symmetries:
            if type(perm).__name__ == 'Identity':
                perm_array = perm(np.arange(n_sites))
            elif hasattr(perm, 'inverse_permutation_array'):
                perm_array = perm.inverse_permutation_array
            else:
                continue
            symmetry_ops.append(perm_array)

        # 对每个未访问的位置，计算其轨道
        for i in range(n_sites):
            if not visited[i]:
                orbit = set()
                # 应用所有对称操作
                for perm_array in symmetry_ops:
                    orbit.add(int(perm_array[i]))

                orbit = sorted(list(orbit))
                orbits.append(orbit)

                # 标记轨道中的所有位置为已访问
                for j in orbit:
                    visited[j] = True

        return orbits

    @staticmethod
    def _build_lattice_info(n_sites):
        """
        构建Shastry-Sutherland晶格的连接信息（简化版本）

        返回:
            包含edges和hyperedges信息的字典
        """
        import numpy as np

        # 计算晶格尺寸
        n_unit_cells = n_sites // 4
        Lx = int(np.sqrt(n_unit_cells))
        Ly = Lx  # 假设是正方形晶格

        edges = {'J1': [], 'J2': []}  # J1: 正方形边, J2: 对角线边
        hyperedges = []  # Q项的plaquette

        # 遍历所有单元格
        for x in range(Lx):
            for y in range(Ly):
                base = 4 * (y + x * Ly)

                # 当前单元格的四个格点
                site0 = base      # 左下角
                site1 = base + 1  # 右下角
                site2 = base + 2  # 右上角
                site3 = base + 3  # 左上角

                # J1边（正方形边）
                edges['J1'].extend([
                    (site0, site1), (site1, site2),
                    (site2, site3), (site3, site0)
                ])

                # J2边（对角线边）
                edges['J2'].extend([
                    (site0, site2), (site1, site3)
                ])

                # Q项超边（plaquette）- 只包含单元格内部的plaquette
                hyperedges.extend([
                    [site0, site1, site3, site2],  # 水平方向
                    [site0, site3, site1, site2]   # 垂直方向
                ])

        return {'edges': edges, 'hyperedges': hyperedges}

    def __call__(self, x):
        """
        前向传播：输入自旋配置 → HyperGNN处理 → 物理感知特征

        参数:
            x: 输入自旋配置，形状 (batch, n_sites)

        返回:
            物理感知的patch嵌入，形状 (batch, n_patches, d_model)
        """
        batch_size, n_sites = x.shape

        # 1. 初始节点嵌入（等变）
        node_features = self._initial_node_embedding(x)  # (batch, n_sites, d_model)

        # 2. 构建晶格连接信息
        lattice_info = self._build_lattice_info(n_sites)

        # 3. HyperGNN层处理（等变）
        for layer_idx in range(self.n_gnn_layers):
            node_features = self._hypergnn_layer(
                node_features, lattice_info, layer_idx
            )

        # 4. 聚合到patch级别的表示（等变）
        patch_features = self._aggregate_to_patches(node_features)

        return patch_features

    def _initial_node_embedding(self, x):
        """
        初始节点嵌入：将自旋值映射到特征空间（等变）

        参数:
            x: 自旋配置，形状 (batch, n_sites)
        返回:
            节点特征，形状 (batch, n_sites, d_model)
        """
        batch_size, n_sites = x.shape

        # 创建site到轨道的映射
        site_to_orbit = self._create_site_to_orbit_mapping(self.orbits, n_sites)

        # 初始化节点特征
        node_features = jnp.zeros((batch_size, n_sites, self.d_model), dtype=self.dtype)

        for site in range(n_sites):
            orbit_idx = site_to_orbit[site]
            embedding = self.orbit_embeddings[orbit_idx].astype(self.dtype)

            # 将自旋值（-1或+1）映射到嵌入空间
            spin_value = x[:, site:site+1].astype(self.dtype)  # (batch, 1)
            site_embedding = spin_value * embedding  # 广播：(batch, 1) * (1, d_model)

            node_features = node_features.at[:, site, :].set(site_embedding.reshape(batch_size, self.d_model))

        return node_features

    @staticmethod
    def _create_site_to_orbit_mapping(orbits, n_sites):
        """创建site到轨道的映射"""
        import numpy as np

        site_to_orbit = np.zeros(n_sites, dtype=int)
        for orbit_idx, orbit in enumerate(orbits):
            for site in orbit:
                site_to_orbit[site] = orbit_idx
        return site_to_orbit

    def _hypergnn_layer(self, node_features, lattice_info, layer_idx):
        """
        HyperGNN层：处理二体边和四体超边的消息传递（等变）

        参数:
            node_features: 节点特征，形状 (batch, n_sites, d_model)
            lattice_info: 晶格连接信息
            layer_idx: 当前层索引
        返回:
            更新后的节点特征，形状 (batch, n_sites, d_model)
        """
        batch_size, n_sites, d_model = node_features.shape
        site_to_orbit = self._create_site_to_orbit_mapping(self.orbits, n_sites)
        layer_params = self.gnn_layers[layer_idx]

        # 1. 二体边消息传递
        edge_messages = jnp.zeros_like(node_features)
        for edge_type, edge_list in lattice_info['edges'].items():
            for i, j in edge_list:
                orbit_i = site_to_orbit[i]
                orbit_j = site_to_orbit[j]

                # 使用对称的轨道参数（取较小的轨道索引以保证对称性）
                orbit_idx = min(orbit_i, orbit_j)

                # 计算边特征
                edge_feat = node_features[:, i, :] + node_features[:, j, :]

                # 应用等变的边MLP
                weight = layer_params[f"edge_mlp_w_{orbit_idx}"].astype(self.dtype)
                bias = layer_params[f"edge_mlp_b_{orbit_idx}"].astype(self.dtype)
                message = jnp.dot(edge_feat, weight) + bias

                # 传递消息
                edge_messages = edge_messages.at[:, i, :].add(message)
                edge_messages = edge_messages.at[:, j, :].add(message)

        # 2. 四体超边消息传递
        hyperedge_messages = jnp.zeros_like(node_features)
        for hyperedge in lattice_info['hyperedges']:
            # 计算超边中所有节点的轨道
            hyperedge_orbits = [site_to_orbit[node] for node in hyperedge]
            orbit_idx = min(hyperedge_orbits)  # 使用最小轨道索引保证对称性

            # 计算超边特征
            hyperedge_feat = jnp.mean(node_features[:, hyperedge, :], axis=1)

            # 应用等变的超边MLP
            weight = layer_params[f"hyperedge_mlp_w_{orbit_idx}"].astype(self.dtype)
            bias = layer_params[f"hyperedge_mlp_b_{orbit_idx}"].astype(self.dtype)
            message = jnp.dot(hyperedge_feat, weight) + bias

            # 传递消息到超边中的所有节点
            for node in hyperedge:
                hyperedge_messages = hyperedge_messages.at[:, node, :].add(message)

        # 3. 节点更新（等变）
        aggregated_messages = edge_messages + hyperedge_messages
        updated_features = jnp.zeros_like(node_features)

        for site in range(n_sites):
            orbit_idx = site_to_orbit[site]

            # 第一层MLP
            w1 = layer_params[f"node_update_w1_{orbit_idx}"].astype(self.dtype)
            b1 = layer_params[f"node_update_b1_{orbit_idx}"].astype(self.dtype)
            hidden = nn.gelu(jnp.dot(aggregated_messages[:, site, :], w1) + b1)

            # 第二层MLP
            w2 = layer_params[f"node_update_w2_{orbit_idx}"].astype(self.dtype)
            b2 = layer_params[f"node_update_b2_{orbit_idx}"].astype(self.dtype)
            output = jnp.dot(hidden, w2) + b2

            updated_features = updated_features.at[:, site, :].set(output)

        # 4. 残差连接和层归一化
        output = node_features + updated_features
        output_f64 = self.layer_norm(output.astype(jnp.float64))
        return output_f64.astype(self.dtype)

    def _aggregate_to_patches(self, node_features):
        """
        将节点特征聚合到patch级别（等变）

        参数:
            node_features: 节点特征，形状 (batch, n_sites, d_model)
        返回:
            patch特征，形状 (batch, n_patches, d_model)
        """
        batch_size, n_sites, d_model = node_features.shape
        n_patches = n_sites // (self.patch_size ** 2)

        # 获取patch到sites的映射和轨道分配
        patch_to_sites = self._get_patch_to_sites_mapping(n_patches)
        patch_to_orbit = self._assign_patches_to_orbits(patch_to_sites)

        # 聚合patch特征
        patch_features = jnp.zeros((batch_size, n_patches, self.d_model), dtype=self.dtype)

        for patch_idx in range(n_patches):
            orbit_idx = patch_to_orbit[patch_idx]
            sites = patch_to_sites[patch_idx]

            # 提取patch内的节点特征并展平
            patch_node_features = node_features[:, sites, :].reshape(batch_size, -1)

            # 应用等变的聚合权重
            weight = self.patch_aggregation_weights[orbit_idx].astype(self.dtype)
            bias = self.patch_aggregation_biases[orbit_idx].astype(self.dtype)

            patch_embedding = jnp.dot(patch_node_features, weight) + bias
            patch_features = patch_features.at[:, patch_idx, :].set(patch_embedding)

        return patch_features

    def _get_patch_to_sites_mapping(self, n_patches):
        """获取每个patch包含的site索引"""
        import numpy as np

        patch_to_sites = []
        for patch_idx in range(n_patches):
            base = 4 * patch_idx
            sites = [base, base + 1, base + 2, base + 3]
            patch_to_sites.append(sites)
        return patch_to_sites

    def _assign_patches_to_orbits(self, patch_to_sites):
        """为每个patch分配其所属的轨道"""
        import numpy as np

        site_to_orbit = self._create_site_to_orbit_mapping(self.orbits, self.n_sites)
        patch_to_orbit = []
        for sites in patch_to_sites:
            orbit_idx = site_to_orbit[sites[0]]  # 使用第一个site的轨道
            patch_to_orbit.append(orbit_idx)
        return np.array(patch_to_orbit)





def get_relative_position_index(n_patches):
    """
    生成相对位置索引矩阵

    对于2D网格，计算每对patch之间的相对位置
    返回形状为 (n_patches, n_patches, 2) 的索引，最后一维是 (dx, dy)
    """
    sq_n = int(n_patches ** 0.5)
    assert sq_n * sq_n == n_patches, "patches数量必须是完全平方数"

    # 创建坐标网格
    coords = jnp.stack(jnp.meshgrid(jnp.arange(sq_n), jnp.arange(sq_n), indexing='ij'), axis=-1)
    coords_flat = coords.reshape(-1, 2)  # (n_patches, 2)

    # 计算相对位置: relative_coords[i, j] = coords[i] - coords[j]
    relative_coords = coords_flat[:, None, :] - coords_flat[None, :, :]  # (n_patches, n_patches, 2)

    # 将相对坐标转换为非负索引
    # 相对坐标范围: [-(sq_n-1), sq_n-1]，共 2*sq_n-1 个可能值
    relative_coords = relative_coords + (sq_n - 1)  # 现在范围是 [0, 2*sq_n-2]

    # 将2D相对位置映射到1D索引
    # 使用公式: index = dx * (2*sq_n-1) + dy
    relative_position_index = relative_coords[:, :, 0] * (2 * sq_n - 1) + relative_coords[:, :, 1]

    return relative_position_index


class MultiHeadSelfAttention(nn.Module):
    """
    多头自注意力机制 (MHSA) with 相对位置编码

    实现论文公式: A = softmax((Q·K^T + P) / √d) · V
    其中 P 是相对位置编码，保持平移不变性
    """
    d_model: int  # 嵌入空间维度
    n_heads: int  # 注意力头数
    n_patches: int  # 输入序列长度
    use_rpe: bool = True  # 是否使用相对位置编码
    dropout_rate: float = 0.0  # Dropout比率
    dtype: Any = jnp.bfloat16  # 计算精度（默认bfloat16用于混合精度）

    def setup(self):
        assert self.d_model % self.n_heads == 0, "d_model必须能被n_heads整除"
        self.d_head = self.d_model // self.n_heads  # 每个头的维度

        param_dtype = jnp.float64  # 参数始终保持float64高精度

        # Q, K, V 投影矩阵 (使用线性层，如论文推荐)
        self.q_proj = nn.Dense(
            self.d_model,
            kernel_init=nn.initializers.xavier_uniform(),
            param_dtype=param_dtype,
            dtype=self.dtype,  # 计算使用指定精度
            use_bias=False,
        )
        self.k_proj = nn.Dense(
            self.d_model,
            kernel_init=nn.initializers.xavier_uniform(),
            param_dtype=param_dtype,
            dtype=self.dtype,
            use_bias=False,
        )
        self.v_proj = nn.Dense(
            self.d_model,
            kernel_init=nn.initializers.xavier_uniform(),
            param_dtype=param_dtype,
            dtype=self.dtype,
            use_bias=False,
        )

        # 输出投影
        self.out_proj = nn.Dense(
            self.d_model,
            kernel_init=nn.initializers.xavier_uniform(),
            param_dtype=param_dtype,
            dtype=self.dtype,
            use_bias=False,
        )

        # Dropout层
        if self.dropout_rate > 0:
            self.dropout = nn.Dropout(rate=self.dropout_rate)

        # 相对位置编码 (RPE)
        if self.use_rpe:
            sq_n = int(self.n_patches ** 0.5)
            assert sq_n * sq_n == self.n_patches, "使用RPE时patches数量必须是完全平方数"

            # 相对位置偏置表
            # 对于2D网格，相对位置范围是 [-(sq_n-1), sq_n-1]
            # 总共有 (2*sq_n-1) x (2*sq_n-1) 个可能的相对位置
            n_relative_positions = (2 * sq_n - 1) ** 2

            # 每个头有独立的相对位置偏置
            self.relative_position_bias = self.param(
                "relative_position_bias",
                nn.initializers.zeros,
                (self.n_heads, n_relative_positions),
                param_dtype,
            )

            # 预计算相对位置索引 (这是常量，不需要训练)
            self.relative_position_index = get_relative_position_index(self.n_patches)

    def __call__(self, x, deterministic=True):
        """
        参数:
            x: 输入张量，形状 (batch, n_patches, d_model)
            deterministic: 是否在确定性模式（训练时为False，推理时为True）

        返回:
            输出张量，形状 (batch, n_patches, d_model)
        """
        batch_size = x.shape[0]

        # 计算 Q, K, V
        Q = self.q_proj(x)  # (batch, n_patches, d_model)
        K = self.k_proj(x)  # (batch, n_patches, d_model)
        V = self.v_proj(x)  # (batch, n_patches, d_model)

        # 重塑为多头形式: (batch, n_patches, n_heads, d_head)
        Q = Q.reshape(batch_size, self.n_patches, self.n_heads, self.d_head)
        K = K.reshape(batch_size, self.n_patches, self.n_heads, self.d_head)
        V = V.reshape(batch_size, self.n_patches, self.n_heads, self.d_head)

        # 转置为 (batch, n_heads, n_patches, d_head) 以便矩阵乘法
        Q = Q.transpose(0, 2, 1, 3)
        K = K.transpose(0, 2, 1, 3)
        V = V.transpose(0, 2, 1, 3)

        # 计算注意力分数: Q @ K^T / sqrt(d_head)
        # (batch, n_heads, n_patches, d_head) @ (batch, n_heads, d_head, n_patches)
        # -> (batch, n_heads, n_patches, n_patches)
        attn_scores = jnp.matmul(Q, K.transpose(0, 1, 3, 2)) / jnp.sqrt(self.d_head)

        # 添加相对位置编码偏置
        if self.use_rpe:
            # 从偏置表中提取相对位置偏置
            # relative_position_bias: (n_heads, n_relative_positions)
            # relative_position_index: (n_patches, n_patches)
            relative_bias = self.relative_position_bias[:, self.relative_position_index]
            # 现在 relative_bias 形状: (n_heads, n_patches, n_patches)

            # 添加到注意力分数 (广播到batch维度)
            attn_scores = attn_scores + relative_bias[None, :, :, :]

        # Softmax归一化 - 转换到float64以保持数值稳定性
        attn_scores_f64 = attn_scores.astype(jnp.float64)
        attn_weights = jax.nn.softmax(attn_scores_f64, axis=-1)
        # 转回计算精度
        attn_weights = attn_weights.astype(self.dtype)

        # Attention dropout
        if self.dropout_rate > 0:
            attn_weights = self.dropout(attn_weights, deterministic=deterministic)

        # 应用注意力权重到V
        # (batch, n_heads, n_patches, n_patches) @ (batch, n_heads, n_patches, d_head)
        # -> (batch, n_heads, n_patches, d_head)
        attn_output = jnp.matmul(attn_weights, V)

        # 转置回 (batch, n_patches, n_heads, d_head)
        attn_output = attn_output.transpose(0, 2, 1, 3)

        # 合并多头: (batch, n_patches, d_model)
        attn_output = attn_output.reshape(batch_size, self.n_patches, self.d_model)

        # 输出投影
        output = self.out_proj(attn_output)

        # Output dropout
        if self.dropout_rate > 0:
            output = self.dropout(output, deterministic=deterministic)

        return output


class EncoderBlock(nn.Module):
    """
    Transformer编码器块

    结构: LayerNorm -> MHSA -> 残差连接 -> LayerNorm -> FFN -> 残差连接
    """
    d_model: int  # 嵌入空间维度
    n_heads: int  # 注意力头数
    n_patches: int  # 输入序列长度
    use_rpe: bool = True  # 是否使用相对位置编码
    dropout_rate: float = 0.0  # Dropout比率
    mlp_ratio: float = 4.0  # MLP隐藏层维度相对于d_model的倍数（标准Transformer使用4.0）
    dtype: Any = jnp.bfloat16  # 计算精度（默认bfloat16用于混合精度）

    def setup(self):
        param_dtype = jnp.float64  # 参数始终保持float64高精度

        # 多头自注意力
        self.attn = MultiHeadSelfAttention(
            d_model=self.d_model,
            n_heads=self.n_heads,
            n_patches=self.n_patches,
            use_rpe=self.use_rpe,
            dropout_rate=self.dropout_rate,
            dtype=self.dtype,
        )

        # Layer Normalization - 保持float64精度以确保数值稳定性
        self.layer_norm_1 = nn.LayerNorm(param_dtype=param_dtype, dtype=jnp.float64)
        self.layer_norm_2 = nn.LayerNorm(param_dtype=param_dtype, dtype=jnp.float64)

        # 前馈网络 (FFN)
        # 使用mlp_ratio控制隐藏层维度
        mlp_hidden_dim = int(self.d_model * self.mlp_ratio)
        self.dense1 = nn.Dense(
            mlp_hidden_dim,
            kernel_init=nn.initializers.xavier_uniform(),
            param_dtype=param_dtype,
            dtype=self.dtype,
        )
        self.dense2 = nn.Dense(
            self.d_model,
            kernel_init=nn.initializers.xavier_uniform(),
            param_dtype=param_dtype,
            dtype=self.dtype,
        )

        # Dropout层
        if self.dropout_rate > 0:
            self.dropout = nn.Dropout(rate=self.dropout_rate)

    def __call__(self, x, deterministic=True):
        # Pre-LN架构: LayerNorm -> Attention -> 残差
        # LayerNorm在float64下计算，输出转回计算精度
        x_norm = self.layer_norm_1(x.astype(jnp.float64)).astype(self.dtype)
        x = x + self.attn(x_norm, deterministic=deterministic)

        # Pre-LN架构: LayerNorm -> FFN -> 残差
        x_norm = self.layer_norm_2(x.astype(jnp.float64)).astype(self.dtype)
        ffn_out = self.dense1(x_norm)
        ffn_out = nn.gelu(ffn_out)
        if self.dropout_rate > 0:
            ffn_out = self.dropout(ffn_out, deterministic=deterministic)
        ffn_out = self.dense2(ffn_out)
        if self.dropout_rate > 0:
            ffn_out = self.dropout(ffn_out, deterministic=deterministic)
        x = x + ffn_out
        return x


class Encoder(nn.Module):
    """
    Transformer编码器

    由多个EncoderBlock堆叠而成
    """
    num_layers: int  # 层数
    d_model: int  # 嵌入空间维度
    n_heads: int  # 注意力头数
    n_patches: int  # 输入序列长度
    use_rpe: bool = True  # 是否使用相对位置编码
    dropout_rate: float = 0.0  # Dropout比率
    mlp_ratio: float = 4.0  # MLP隐藏层维度相对于d_model的倍数
    dtype: Any = jnp.bfloat16  # 计算精度（默认bfloat16用于混合精度）

    def setup(self):
        self.layers = [
            EncoderBlock(
                d_model=self.d_model,
                n_heads=self.n_heads,
                n_patches=self.n_patches,
                use_rpe=self.use_rpe,
                dropout_rate=self.dropout_rate,
                mlp_ratio=self.mlp_ratio,
                dtype=self.dtype,
            )
            for _ in range(self.num_layers)
        ]

    def __call__(self, x, deterministic=True):
        for l in self.layers:
            x = l(x, deterministic=deterministic)
        return x


class OutputHead(nn.Module):
    """输出层：将编码器输出映射到复数振幅"""
    d_model: int  # 嵌入空间维度
    dtype: Any = jnp.bfloat16  # 计算精度（默认bfloat16用于混合精度）

    def setup(self):
        param_dtype = jnp.float64  # 参数始终保持float64高精度

        # LayerNorm保持float64精度以确保数值稳定性
        self.out_layer_norm = nn.LayerNorm(param_dtype=param_dtype, dtype=jnp.float64)

        self.norm2 = nn.LayerNorm(
            use_scale=True, use_bias=True, param_dtype=param_dtype, dtype=jnp.float64
        )
        self.norm3 = nn.LayerNorm(
            use_scale=True, use_bias=True, param_dtype=param_dtype, dtype=jnp.float64
        )

        self.output_layer0 = nn.Dense(
            self.d_model,
            param_dtype=param_dtype,
            dtype=self.dtype,
            kernel_init=nn.initializers.xavier_uniform(),
            bias_init=jax.nn.initializers.zeros,
        )
        self.output_layer1 = nn.Dense(
            self.d_model,
            param_dtype=param_dtype,
            dtype=self.dtype,
            kernel_init=nn.initializers.xavier_uniform(),
            bias_init=jax.nn.initializers.zeros,
        )

    def __call__(self, x):
        # 对所有patches求和得到隐藏表示 - 在float64下进行求和以保持精度
        x_f64 = x.astype(jnp.float64)
        z = self.out_layer_norm(x_f64.sum(axis=1))

        # 分别计算实部和虚部 - 转回计算精度
        z_compute = z.astype(self.dtype)
        out_real = self.norm2(self.output_layer0(z_compute).astype(jnp.float64))
        out_imag = self.norm3(self.output_layer1(z_compute).astype(jnp.float64))

        # 组合成复数 - 在float64下进行以保持精度
        out = out_real + 1j * out_imag

        # 使用log_cosh激活函数 - 在float64下计算
        log_cosh = nk.nn.activation.log_cosh
        return jnp.sum(log_cosh(out), axis=-1)


class ViT(nn.Module):
    """
    Vision Transformer 量子态波函数

    使用真正的多头自注意力机制和相对位置编码(RPE)
    实现论文公式: A = softmax((Q·K^T + P) / √d) · V

    混合精度训练策略:
    - 参数保持float64高精度
    - 计算使用bfloat16加速（可配置）
    - 关键操作（LayerNorm, Softmax, 最终输出）保持float64精度
    """
    num_layers: int  # Transformer层数
    d_model: int  # 嵌入空间维度
    n_heads: int  # 注意力头数
    patch_size: int  # patch的线性大小
    use_rpe: bool = True  # 是否使用相对位置编码(默认开启)
    dropout_rate: float = 0.0  # Dropout比率（默认0表示不使用dropout）
    mlp_ratio: float = 4.0  # MLP隐藏层维度相对于d_model的倍数（标准Transformer使用4.0）
    use_mixed_precision: bool = True  # 是否使用混合精度训练（默认开启）

    @nn.compact
    def __call__(self, spins, deterministic=True):
        """
        参数:
            spins: 自旋配置
            deterministic: 是否在确定性模式（训练时为False，推理时为True）
                          注意：在量子态采样中，通常设为True以保持确定性
        """
        # 确定计算精度
        compute_dtype = jnp.bfloat16 if self.use_mixed_precision else jnp.float64

        x = jnp.atleast_2d(spins)

        Ns = x.shape[-1]  # 格点数
        n_patches = Ns // self.patch_size**2  # patches数量

        # 嵌入层
        x = Embed(d_model=self.d_model, patch_size=self.patch_size, dtype=compute_dtype)(x)

        # Transformer编码器
        y = Encoder(
            num_layers=self.num_layers,
            d_model=self.d_model,
            n_heads=self.n_heads,
            n_patches=n_patches,
            use_rpe=self.use_rpe,
            dropout_rate=self.dropout_rate,
            mlp_ratio=self.mlp_ratio,
            dtype=compute_dtype,
        )(x, deterministic=deterministic)

        # 输出层
        log_psi = OutputHead(d_model=self.d_model, dtype=compute_dtype)(y)

        return log_psi


class ViTPartialEquivariant(nn.Module):
    """
    融合HyperGNN和对称性的Vision Transformer

    实现架构：
    输入自旋配置 → HyperGNN物理编码器(等变) → Transformer纠缠编码器 → 输出

    核心特性：
    1. HyperGNN物理编码器：处理Shastry-Sutherland晶格的二体边(J1,J2)和四体超边(Q)
    2. 等变性：在HyperGNN层使用对称性约束的权重共享
    3. Transformer纠缠编码器：计算物理感知特征之间的全局长程相关性

    参数:
        num_layers: Transformer层数
        d_model: 嵌入空间维度
        n_heads: 注意力头数
        patch_size: patch的线性大小
        symmetries: 对称群（PermutationGroup），用于等变嵌入层
        n_gnn_layers: HyperGNN层数（1-2层）
        use_rpe: 是否使用相对位置编码
        dropout_rate: Dropout比率
        mlp_ratio: MLP隐藏层维度相对于d_model的倍数
        use_mixed_precision: 是否使用混合精度训练
    """
    num_layers: int
    d_model: int
    n_heads: int
    patch_size: int
    symmetries: Any  # PermutationGroup
    n_gnn_layers: int = 2  # HyperGNN层数
    use_rpe: bool = True
    dropout_rate: float = 0.0
    mlp_ratio: float = 4.0
    use_mixed_precision: bool = True

    @nn.compact
    def __call__(self, spins, deterministic=True):
        """
        前向传播：物理编码器 → 纠缠编码器

        参数:
            spins: 自旋配置
            deterministic: 是否在确定性模式（训练时为False，推理时为True）
        """
        # 确定计算精度
        compute_dtype = jnp.bfloat16 if self.use_mixed_precision else jnp.float64

        x = jnp.atleast_2d(spins)

        Ns = x.shape[-1]  # 格点数
        n_patches = Ns // self.patch_size**2  # patches数量

        # HyperGNN物理编码器（等变）
        # 让每个自旋节点与邻居"对话"，沿着二体边和四体超边进行
        # 输出包含局域哈密顿量环境信息的"物理感知"特征向量
        x = HyperGNNEquivariantEmbed(
            d_model=self.d_model,
            patch_size=self.patch_size,
            symmetries=self.symmetries,
            n_gnn_layers=self.n_gnn_layers,
            dtype=compute_dtype
        )(x)

        # Transformer纠缠编码器（普通）
        # 计算物理感知特征之间的全局长程相关性
        # 自注意力机制捕获量子纠缠和长程关联
        y = Encoder(
            num_layers=self.num_layers,
            d_model=self.d_model,
            n_heads=self.n_heads,
            n_patches=n_patches,
            use_rpe=self.use_rpe,
            dropout_rate=self.dropout_rate,
            mlp_ratio=self.mlp_ratio,
            dtype=compute_dtype,
        )(x, deterministic=deterministic)

        # 输出层
        log_psi = OutputHead(d_model=self.d_model, dtype=compute_dtype)(y)

        return log_psi


# 为了向后兼容，保留原始的EquivariantEmbed类
EquivariantEmbed = HyperGNNEquivariantEmbed


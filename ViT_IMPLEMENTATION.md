# Vision Transformer (ViT) Implementation for Shastry-Sutherland Model

## Overview

This project has been updated to use Vision Transformer (ViT) architecture instead of GCNN for quantum state wave function approximation.

## Changes Made

### 1. New ViT Model Implementation
- **File**: `src/models/vit.py`
- Implements the complete ViT architecture based on NetKet tutorial
- Components:
  - `Embed`: Patch embedding layer
  - `FMHA`: Factored Multi-Head Attention
  - `EncoderBlock`: Transformer encoder block
  - `Encoder`: Full transformer encoder
  - `OutputHead`: Complex-valued output layer
  - `ViT`: Complete ViT model

### 2. Updated Quantum State Creation
- **File**: `src/models/vit_state.py` (renamed from `gcnn.py`)
- `create_quantum_state()` function now creates ViT-based quantum states
- New parameters:
  - `d_model`: Embedding dimension (default: 60)
  - `n_heads`: Number of attention heads (default: 10)
  - `patch_size`: Patch size (default: 2)
  - `transl_invariant`: Translation invariance flag (default: True)

### 3. Updated Runner
- **File**: `src/runner.py`
- `<PERSON><PERSON><PERSON>` now supports ViT model
- Default model changed from GCNN to ViT
- Updated logging to show ViT-specific parameters

### 4. Updated Training Script
- **File**: `scripts/train.py`
- Function renamed: `run_gcnn_simulation()` → `run_vit_simulation()`
- New command-line arguments for ViT parameters:
  - `--d_model`: Embedding dimension
  - `--n_heads`: Number of attention heads
  - `--patch_size`: Patch size
  - `--transl_invariant`: Enable translation invariance

### 5. Updated Analysis Scripts
- `scripts/structure_factor_measurement.py`
- `scripts/run_hybrid_analysis.py`
- `scripts/infidelity_measurement.py`
- All updated to import from `vit_state` instead of `gcnn`

## Model Architecture

The ViT model follows the architecture described in:
- NetKet Tutorial: https://netket.readthedocs.io/en/latest/tutorials/ViT-wave-function.html
- Reference: Viteritti et al., "Transformer variational wave functions for frustrated quantum spin systems"

### Key Features:
1. **Patch Embedding**: Divides spin configuration into patches and embeds them
2. **Factored Attention**: Simplified attention mechanism for quantum states
3. **Translation Invariance**: Optional translation-invariant attention weights
4. **Complex Output**: Produces complex-valued wave function amplitudes

## Default Parameters

```python
ModelConfig:
    d_model = 60              # Embedding dimension
    num_layers = 4            # Number of transformer layers
    n_heads = 10              # Number of attention heads
    patch_size = 2            # Patch size (2x2)
    transl_invariant = True   # Use translation invariance
```

## Usage

### Training with ViT

```bash
python scripts/train.py L J2 J1 \
    --d_model 60 \
    --num_layers 4 \
    --n_heads 10 \
    --patch_size 2 \
    --transl_invariant \
    --n_samples 4096 \
    --chunk_size 1024
```

### Example

```bash
# Train L=4 system with J2=0.5, J1=0.08
python scripts/train.py 4 0.5 0.08 \
    --d_model 60 \
    --num_layers 4 \
    --n_heads 10 \
    --n_samples 4096 \
    --enable_checkpoint \
    --save_interval 500
```

## Testing

The implementation has been tested with:
- Model initialization and forward pass
- Quantum state creation
- Parameter counting

Example test results:
- L=4 system (64 sites)
- Model parameters: ~155,000 (for default configuration)
- Successfully creates variational quantum state

## Compatibility

- All existing checkpoint and analysis functionality remains compatible
- The model interface is the same as before (takes spin configurations, outputs log(ψ))
- Can be used with all existing NetKet optimizers and samplers

## References

1. Viteritti, L. L., Rende, R., & Becca, F. (2023). "Transformer variational wave functions for frustrated quantum spin systems." Physical Review Letters, 130(23), 236401.

2. NetKet ViT Tutorial: https://netket.readthedocs.io/en/latest/tutorials/ViT-wave-function.html

3. Vaswani, A., et al. (2017). "Attention is all you need." Advances in Neural Information Processing Systems.


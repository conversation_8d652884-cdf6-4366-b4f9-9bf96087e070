[2025-10-21 15:03:26] ======================================================================================================
[2025-10-21 15:03:26] GCNN for Shastry-Sutherland Model
[2025-10-21 15:03:26] ======================================================================================================
[2025-10-21 15:03:26] System parameters:
[2025-10-21 15:03:26]   - System size: L=6, N=144
[2025-10-21 15:03:26]   - System parameters: J1=0.76, J2=1.0, Q=0.0
[2025-10-21 15:03:26] ------------------------------------------------------------------------------------------------------
[2025-10-21 15:03:26] Model parameters:
[2025-10-21 15:03:26]   - Number of layers = 4
[2025-10-21 15:03:26]   - Number of features = 4
[2025-10-21 15:03:26]   - Total parameters = 28252
[2025-10-21 15:03:26] ------------------------------------------------------------------------------------------------------
[2025-10-21 15:03:26] Training parameters:
[2025-10-21 15:03:26]   - Total iterations: 2250
[2025-10-21 15:03:27]   - Annealing cycles: 4
[2025-10-21 15:03:27]   - Initial period: 150
[2025-10-21 15:03:27]   - Period multiplier: 2.0
[2025-10-21 15:03:27]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-21 15:03:27]   - Samples: 8192
[2025-10-21 15:03:27]   - Discarded samples: 0
[2025-10-21 15:03:27]   - Chunk size: 4096
[2025-10-21 15:03:27]   - Diagonal shift: 0.15
[2025-10-21 15:03:27]   - Gradient clipping: 1.0
[2025-10-21 15:03:27]   - Checkpoint enabled: interval=225
[2025-10-21 15:03:27]   - Checkpoint directory: results/L=6/J2=1.00/J1=0.76/training/checkpoints
[2025-10-21 15:03:27] ------------------------------------------------------------------------------------------------------
[2025-10-21 15:03:27] Device status:
[2025-10-21 15:03:27]   - Devices model: NVIDIA H200 NVL
[2025-10-21 15:03:27]   - Number of devices: 1
[2025-10-21 15:03:27]   - Sharding: True
[2025-10-21 15:03:27] ======================================================================================================
[2025-10-21 15:04:31] [Iter    1/2250] R0[0/150]     | LR: 0.030000 | E:   72.714989 | E_var:     0.0335 | E_err:   0.002022
[2025-10-21 15:04:58] [Iter    2/2250] R0[1/150]     | LR: 0.029997 | E:   72.712116 | E_var:     0.0438 | E_err:   0.002311
[2025-10-21 15:05:24] [Iter    3/2250] R0[2/150]     | LR: 0.029989 | E:   72.704941 | E_var:     0.0617 | E_err:   0.002744
[2025-10-21 15:05:51] [Iter    4/2250] R0[3/150]     | LR: 0.029975 | E:   72.707840 | E_var:     0.0925 | E_err:   0.003361
[2025-10-21 15:06:18] [Iter    5/2250] R0[4/150]     | LR: 0.029956 | E:   72.700015 | E_var:     0.1433 | E_err:   0.004182
[2025-10-21 15:06:45] [Iter    6/2250] R0[5/150]     | LR: 0.029932 | E:   72.684768 | E_var:     0.2447 | E_err:   0.005466
[2025-10-21 15:07:12] [Iter    7/2250] R0[6/150]     | LR: 0.029901 | E:   72.651442 | E_var:     0.4408 | E_err:   0.007336
[2025-10-21 15:07:39] [Iter    8/2250] R0[7/150]     | LR: 0.029866 | E:   72.574096 | E_var:     0.8960 | E_err:   0.010458
[2025-10-21 15:08:06] [Iter    9/2250] R0[8/150]     | LR: 0.029825 | E:   72.464814 | E_var:     1.8478 | E_err:   0.015019
[2025-10-21 15:08:33] [Iter   10/2250] R0[9/150]     | LR: 0.029779 | E:   72.125460 | E_var:     4.2394 | E_err:   0.022749
[2025-10-21 15:09:00] [Iter   11/2250] R0[10/150]    | LR: 0.029727 | E:   71.398349 | E_var:    10.6880 | E_err:   0.036120
[2025-10-21 15:09:27] [Iter   12/2250] R0[11/150]    | LR: 0.029670 | E:   69.493687 | E_var:    29.9479 | E_err:   0.060463
[2025-10-21 15:09:54] [Iter   13/2250] R0[12/150]    | LR: 0.029607 | E:   64.169308 | E_var:    73.9636 | E_err:   0.095020
[2025-10-21 15:10:21] [Iter   14/2250] R0[13/150]    | LR: 0.029540 | E:   54.828963 | E_var:    97.0489 | E_err:   0.108843
[2025-10-21 15:10:48] [Iter   15/2250] R0[14/150]    | LR: 0.029466 | E:   46.093010 | E_var:    80.2980 | E_err:   0.099005
[2025-10-21 15:11:15] [Iter   16/2250] R0[15/150]    | LR: 0.029388 | E:   38.631789 | E_var:    78.8475 | E_err:   0.098107
[2025-10-21 15:11:41] [Iter   17/2250] R0[16/150]    | LR: 0.029305 | E:   31.381000 | E_var:    88.7885 | E_err:   0.104108
[2025-10-21 15:12:08] [Iter   18/2250] R0[17/150]    | LR: 0.029216 | E:   23.550806 | E_var:    96.8689 | E_err:   0.108742
[2025-10-21 15:12:35] [Iter   19/2250] R0[18/150]    | LR: 0.029122 | E:   15.524891 | E_var:   100.2153 | E_err:   0.110604
[2025-10-21 15:13:02] [Iter   20/2250] R0[19/150]    | LR: 0.029023 | E:    8.108081 | E_var:   102.9044 | E_err:   0.112078
[2025-10-21 15:13:29] [Iter   21/2250] R0[20/150]    | LR: 0.028919 | E:    1.617342 | E_var:    89.1982 | E_err:   0.104348
[2025-10-21 15:13:56] [Iter   22/2250] R0[21/150]    | LR: 0.028810 | E:   -3.469050 | E_var:    76.2456 | E_err:   0.096475
[2025-10-21 15:14:23] [Iter   23/2250] R0[22/150]    | LR: 0.028696 | E:   -7.432742 | E_var:    66.1156 | E_err:   0.089837
[2025-10-21 15:14:50] [Iter   24/2250] R0[23/150]    | LR: 0.028578 | E:  -10.245806 | E_var:    60.3883 | E_err:   0.085858
[2025-10-21 15:15:17] [Iter   25/2250] R0[24/150]    | LR: 0.028454 | E:  -12.448994 | E_var:    54.2806 | E_err:   0.081401
[2025-10-21 15:15:44] [Iter   26/2250] R0[25/150]    | LR: 0.028325 | E:  -14.701372 | E_var:    52.3932 | E_err:   0.079973
[2025-10-21 15:16:11] [Iter   27/2250] R0[26/150]    | LR: 0.028192 | E:  -16.618170 | E_var:    50.4581 | E_err:   0.078482
[2025-10-21 15:16:38] [Iter   28/2250] R0[27/150]    | LR: 0.028054 | E:  -18.445734 | E_var:    50.0394 | E_err:   0.078156
[2025-10-21 15:17:05] [Iter   29/2250] R0[28/150]    | LR: 0.027912 | E:  -19.976350 | E_var:    48.1847 | E_err:   0.076694
[2025-10-21 15:17:31] [Iter   30/2250] R0[29/150]    | LR: 0.027764 | E:  -21.691771 | E_var:    46.6712 | E_err:   0.075480
[2025-10-21 15:17:58] [Iter   31/2250] R0[30/150]    | LR: 0.027613 | E:  -23.309914 | E_var:    45.2232 | E_err:   0.074299
[2025-10-21 15:18:25] [Iter   32/2250] R0[31/150]    | LR: 0.027457 | E:  -24.723444 | E_var:    47.6239 | E_err:   0.076246
[2025-10-21 15:18:52] [Iter   33/2250] R0[32/150]    | LR: 0.027296 | E:  -26.042037 | E_var:    44.0913 | E_err:   0.073364
[2025-10-21 15:19:19] [Iter   34/2250] R0[33/150]    | LR: 0.027131 | E:  -27.500434 | E_var:    41.3460 | E_err:   0.071043
[2025-10-21 15:19:46] [Iter   35/2250] R0[34/150]    | LR: 0.026962 | E:  -28.856426 | E_var:    40.0299 | E_err:   0.069903
[2025-10-21 15:20:13] [Iter   36/2250] R0[35/150]    | LR: 0.026789 | E:  -30.265220 | E_var:    43.7857 | E_err:   0.073109
[2025-10-21 15:20:40] [Iter   37/2250] R0[36/150]    | LR: 0.026612 | E:  -31.408391 | E_var:    46.8269 | E_err:   0.075605
[2025-10-21 15:21:07] [Iter   38/2250] R0[37/150]    | LR: 0.026431 | E:  -32.527951 | E_var:    37.0648 | E_err:   0.067264
[2025-10-21 15:21:34] [Iter   39/2250] R0[38/150]    | LR: 0.026246 | E:  -33.833516 | E_var:    35.7885 | E_err:   0.066096
[2025-10-21 15:22:01] [Iter   40/2250] R0[39/150]    | LR: 0.026057 | E:  -34.809414 | E_var:    37.2284 | E_err:   0.067413
[2025-10-21 15:22:28] [Iter   41/2250] R0[40/150]    | LR: 0.025864 | E:  -35.718807 | E_var:    32.8984 | E_err:   0.063371
[2025-10-21 15:22:54] [Iter   42/2250] R0[41/150]    | LR: 0.025668 | E:  -36.634072 | E_var:    33.5257 | E_err:   0.063973
[2025-10-21 15:23:21] [Iter   43/2250] R0[42/150]    | LR: 0.025468 | E:  -37.445065 | E_var:    32.5163 | E_err:   0.063002
[2025-10-21 15:23:48] [Iter   44/2250] R0[43/150]    | LR: 0.025264 | E:  -38.290227 | E_var:    30.9014 | E_err:   0.061418
[2025-10-21 15:24:15] [Iter   45/2250] R0[44/150]    | LR: 0.025057 | E:  -39.111441 | E_var:    30.3163 | E_err:   0.060833
[2025-10-21 15:24:42] [Iter   46/2250] R0[45/150]    | LR: 0.024847 | E:  -39.778820 | E_var:    27.8100 | E_err:   0.058265
[2025-10-21 15:25:09] [Iter   47/2250] R0[46/150]    | LR: 0.024634 | E:  -40.507309 | E_var:    27.4796 | E_err:   0.057918
[2025-10-21 15:25:36] [Iter   48/2250] R0[47/150]    | LR: 0.024417 | E:  -41.079658 | E_var:    27.4459 | E_err:   0.057882
[2025-10-21 15:26:03] [Iter   49/2250] R0[48/150]    | LR: 0.024198 | E:  -41.589457 | E_var:    27.3814 | E_err:   0.057814
[2025-10-21 15:26:30] [Iter   50/2250] R0[49/150]    | LR: 0.023975 | E:  -42.128036 | E_var:    26.9813 | E_err:   0.057390
[2025-10-21 15:26:57] [Iter   51/2250] R0[50/150]    | LR: 0.023750 | E:  -42.614076 | E_var:    25.2949 | E_err:   0.055568
[2025-10-21 15:27:24] [Iter   52/2250] R0[51/150]    | LR: 0.023522 | E:  -43.082746 | E_var:    25.3646 | E_err:   0.055644
[2025-10-21 15:27:51] [Iter   53/2250] R0[52/150]    | LR: 0.023291 | E:  -43.524614 | E_var:    24.5461 | E_err:   0.054739
[2025-10-21 15:28:17] [Iter   54/2250] R0[53/150]    | LR: 0.023058 | E:  -43.941436 | E_var:    24.4783 | E_err:   0.054663
[2025-10-21 15:28:44] [Iter   55/2250] R0[54/150]    | LR: 0.022822 | E:  -44.374940 | E_var:    24.5253 | E_err:   0.054716
[2025-10-21 15:29:11] [Iter   56/2250] R0[55/150]    | LR: 0.022584 | E:  -44.786960 | E_var:    23.8097 | E_err:   0.053912
[2025-10-21 15:29:38] [Iter   57/2250] R0[56/150]    | LR: 0.022344 | E:  -45.148879 | E_var:    22.7659 | E_err:   0.052717
[2025-10-21 15:30:05] [Iter   58/2250] R0[57/150]    | LR: 0.022102 | E:  -45.513090 | E_var:    22.7395 | E_err:   0.052686
[2025-10-21 15:30:32] [Iter   59/2250] R0[58/150]    | LR: 0.021857 | E:  -45.743464 | E_var:    22.0252 | E_err:   0.051852
[2025-10-21 15:30:59] [Iter   60/2250] R0[59/150]    | LR: 0.021611 | E:  -45.985874 | E_var:    22.1587 | E_err:   0.052009
[2025-10-21 15:31:26] [Iter   61/2250] R0[60/150]    | LR: 0.021363 | E:  -46.283084 | E_var:    21.5319 | E_err:   0.051268
[2025-10-21 15:31:53] [Iter   62/2250] R0[61/150]    | LR: 0.021113 | E:  -46.624008 | E_var:    21.5973 | E_err:   0.051346
[2025-10-21 15:32:20] [Iter   63/2250] R0[62/150]    | LR: 0.020861 | E:  -46.814238 | E_var:    22.0282 | E_err:   0.051855
[2025-10-21 15:32:47] [Iter   64/2250] R0[63/150]    | LR: 0.020609 | E:  -47.143573 | E_var:    20.8651 | E_err:   0.050468
[2025-10-21 15:33:14] [Iter   65/2250] R0[64/150]    | LR: 0.020354 | E:  -47.385002 | E_var:    20.6117 | E_err:   0.050161
[2025-10-21 15:33:40] [Iter   66/2250] R0[65/150]    | LR: 0.020099 | E:  -47.612940 | E_var:    20.2912 | E_err:   0.049769
[2025-10-21 15:34:07] [Iter   67/2250] R0[66/150]    | LR: 0.019842 | E:  -47.784202 | E_var:    19.9612 | E_err:   0.049363
[2025-10-21 15:34:34] [Iter   68/2250] R0[67/150]    | LR: 0.019585 | E:  -48.044260 | E_var:    20.4443 | E_err:   0.049956
[2025-10-21 15:35:01] [Iter   69/2250] R0[68/150]    | LR: 0.019326 | E:  -48.221542 | E_var:    20.0208 | E_err:   0.049436
[2025-10-21 15:35:28] [Iter   70/2250] R0[69/150]    | LR: 0.019067 | E:  -48.464380 | E_var:    19.6471 | E_err:   0.048973
[2025-10-21 15:35:55] [Iter   71/2250] R0[70/150]    | LR: 0.018807 | E:  -48.659672 | E_var:    19.4364 | E_err:   0.048709
[2025-10-21 15:36:22] [Iter   72/2250] R0[71/150]    | LR: 0.018546 | E:  -48.861910 | E_var:    18.9926 | E_err:   0.048150
[2025-10-21 15:36:49] [Iter   73/2250] R0[72/150]    | LR: 0.018285 | E:  -49.086208 | E_var:    18.9874 | E_err:   0.048144
[2025-10-21 15:37:16] [Iter   74/2250] R0[73/150]    | LR: 0.018023 | E:  -49.211193 | E_var:    18.6957 | E_err:   0.047772
[2025-10-21 15:37:43] [Iter   75/2250] R0[74/150]    | LR: 0.017762 | E:  -49.472249 | E_var:    18.5287 | E_err:   0.047558
[2025-10-21 15:38:10] [Iter   76/2250] R0[75/150]    | LR: 0.017500 | E:  -49.589861 | E_var:    18.9071 | E_err:   0.048042
[2025-10-21 15:38:37] [Iter   77/2250] R0[76/150]    | LR: 0.017238 | E:  -49.775697 | E_var:    18.1059 | E_err:   0.047013
[2025-10-21 15:39:03] [Iter   78/2250] R0[77/150]    | LR: 0.016977 | E:  -49.966674 | E_var:    20.3191 | E_err:   0.049803
[2025-10-21 15:39:30] [Iter   79/2250] R0[78/150]    | LR: 0.016715 | E:  -50.169776 | E_var:    17.2387 | E_err:   0.045873
[2025-10-21 15:39:57] [Iter   80/2250] R0[79/150]    | LR: 0.016454 | E:  -50.352792 | E_var:    17.1069 | E_err:   0.045697
[2025-10-21 15:40:24] [Iter   81/2250] R0[80/150]    | LR: 0.016193 | E:  -50.450898 | E_var:    16.5985 | E_err:   0.045013
[2025-10-21 15:40:51] [Iter   82/2250] R0[81/150]    | LR: 0.015933 | E:  -50.669512 | E_var:    16.7707 | E_err:   0.045246
[2025-10-21 15:41:18] [Iter   83/2250] R0[82/150]    | LR: 0.015674 | E:  -50.809737 | E_var:    16.4333 | E_err:   0.044789
[2025-10-21 15:41:45] [Iter   84/2250] R0[83/150]    | LR: 0.015415 | E:  -50.984771 | E_var:    15.8587 | E_err:   0.043999
[2025-10-21 15:42:12] [Iter   85/2250] R0[84/150]    | LR: 0.015158 | E:  -51.134705 | E_var:    15.6100 | E_err:   0.043652
[2025-10-21 15:42:39] [Iter   86/2250] R0[85/150]    | LR: 0.014901 | E:  -51.298699 | E_var:    15.1579 | E_err:   0.043015
[2025-10-21 15:43:06] [Iter   87/2250] R0[86/150]    | LR: 0.014646 | E:  -51.462580 | E_var:    15.0342 | E_err:   0.042840
[2025-10-21 15:43:32] [Iter   88/2250] R0[87/150]    | LR: 0.014391 | E:  -51.616872 | E_var:    14.6603 | E_err:   0.042303
[2025-10-21 15:43:59] [Iter   89/2250] R0[88/150]    | LR: 0.014139 | E:  -51.728086 | E_var:    14.7092 | E_err:   0.042374
[2025-10-21 15:44:26] [Iter   90/2250] R0[89/150]    | LR: 0.013887 | E:  -51.912647 | E_var:    14.1271 | E_err:   0.041527
[2025-10-21 15:44:53] [Iter   91/2250] R0[90/150]    | LR: 0.013637 | E:  -51.996523 | E_var:    14.3508 | E_err:   0.041855
[2025-10-21 15:45:20] [Iter   92/2250] R0[91/150]    | LR: 0.013389 | E:  -52.121178 | E_var:    13.9184 | E_err:   0.041219
[2025-10-21 15:45:47] [Iter   93/2250] R0[92/150]    | LR: 0.013143 | E:  -52.256659 | E_var:    13.7625 | E_err:   0.040988
[2025-10-21 15:46:14] [Iter   94/2250] R0[93/150]    | LR: 0.012898 | E:  -52.397052 | E_var:    13.4549 | E_err:   0.040527
[2025-10-21 15:46:41] [Iter   95/2250] R0[94/150]    | LR: 0.012656 | E:  -52.497913 | E_var:    13.3666 | E_err:   0.040394
[2025-10-21 15:47:08] [Iter   96/2250] R0[95/150]    | LR: 0.012416 | E:  -52.629482 | E_var:    13.0087 | E_err:   0.039849
[2025-10-21 15:47:35] [Iter   97/2250] R0[96/150]    | LR: 0.012178 | E:  -52.770138 | E_var:    12.2855 | E_err:   0.038726
[2025-10-21 15:48:01] [Iter   98/2250] R0[97/150]    | LR: 0.011942 | E:  -52.810161 | E_var:    12.2222 | E_err:   0.038626
[2025-10-21 15:48:28] [Iter   99/2250] R0[98/150]    | LR: 0.011709 | E:  -52.975915 | E_var:    11.9115 | E_err:   0.038132
[2025-10-21 15:48:55] [Iter  100/2250] R0[99/150]    | LR: 0.011478 | E:  -53.061816 | E_var:    11.7881 | E_err:   0.037934
[2025-10-21 15:49:22] [Iter  101/2250] R0[100/150]   | LR: 0.011250 | E:  -53.137113 | E_var:    11.5269 | E_err:   0.037511
[2025-10-21 15:49:49] [Iter  102/2250] R0[101/150]   | LR: 0.011025 | E:  -53.262267 | E_var:    11.7706 | E_err:   0.037906
[2025-10-21 15:50:16] [Iter  103/2250] R0[102/150]   | LR: 0.010802 | E:  -53.272642 | E_var:    11.4765 | E_err:   0.037429
[2025-10-21 15:50:43] [Iter  104/2250] R0[103/150]   | LR: 0.010583 | E:  -53.439333 | E_var:    10.9491 | E_err:   0.036559
[2025-10-21 15:51:10] [Iter  105/2250] R0[104/150]   | LR: 0.010366 | E:  -53.534489 | E_var:    10.7114 | E_err:   0.036160
[2025-10-21 15:51:37] [Iter  106/2250] R0[105/150]   | LR: 0.010153 | E:  -53.619345 | E_var:    11.0267 | E_err:   0.036688
[2025-10-21 15:52:04] [Iter  107/2250] R0[106/150]   | LR: 0.009943 | E:  -53.710405 | E_var:    10.9543 | E_err:   0.036568
[2025-10-21 15:52:30] [Iter  108/2250] R0[107/150]   | LR: 0.009736 | E:  -53.809799 | E_var:    10.3570 | E_err:   0.035557
[2025-10-21 15:52:57] [Iter  109/2250] R0[108/150]   | LR: 0.009532 | E:  -53.936888 | E_var:    10.7899 | E_err:   0.036292
[2025-10-21 15:53:24] [Iter  110/2250] R0[109/150]   | LR: 0.009332 | E:  -53.994182 | E_var:    10.1255 | E_err:   0.035157
[2025-10-21 15:53:51] [Iter  111/2250] R0[110/150]   | LR: 0.009136 | E:  -54.051846 | E_var:     9.7851 | E_err:   0.034561
[2025-10-21 15:54:18] [Iter  112/2250] R0[111/150]   | LR: 0.008943 | E:  -54.154751 | E_var:    10.0902 | E_err:   0.035096
[2025-10-21 15:54:45] [Iter  113/2250] R0[112/150]   | LR: 0.008754 | E:  -54.247402 | E_var:     9.5348 | E_err:   0.034116
[2025-10-21 15:55:12] [Iter  114/2250] R0[113/150]   | LR: 0.008569 | E:  -54.280105 | E_var:     9.2810 | E_err:   0.033659
[2025-10-21 15:55:39] [Iter  115/2250] R0[114/150]   | LR: 0.008388 | E:  -54.383150 | E_var:     9.3312 | E_err:   0.033750
[2025-10-21 15:56:06] [Iter  116/2250] R0[115/150]   | LR: 0.008211 | E:  -54.452709 | E_var:     9.4150 | E_err:   0.033901
[2025-10-21 15:56:32] [Iter  117/2250] R0[116/150]   | LR: 0.008038 | E:  -54.571943 | E_var:     8.9075 | E_err:   0.032975
[2025-10-21 15:56:59] [Iter  118/2250] R0[117/150]   | LR: 0.007869 | E:  -54.611153 | E_var:     9.5545 | E_err:   0.034151
[2025-10-21 15:57:26] [Iter  119/2250] R0[118/150]   | LR: 0.007704 | E:  -54.637058 | E_var:     8.6191 | E_err:   0.032437
[2025-10-21 15:57:53] [Iter  120/2250] R0[119/150]   | LR: 0.007543 | E:  -54.767036 | E_var:     8.7344 | E_err:   0.032653
[2025-10-21 15:58:20] [Iter  121/2250] R0[120/150]   | LR: 0.007387 | E:  -54.817675 | E_var:     8.6417 | E_err:   0.032479
[2025-10-21 15:58:47] [Iter  122/2250] R0[121/150]   | LR: 0.007236 | E:  -54.848598 | E_var:     8.3450 | E_err:   0.031917
[2025-10-21 15:59:14] [Iter  123/2250] R0[122/150]   | LR: 0.007088 | E:  -54.967016 | E_var:     8.1736 | E_err:   0.031587
[2025-10-21 15:59:41] [Iter  124/2250] R0[123/150]   | LR: 0.006946 | E:  -55.052546 | E_var:     8.4270 | E_err:   0.032073
[2025-10-21 16:00:08] [Iter  125/2250] R0[124/150]   | LR: 0.006808 | E:  -55.110825 | E_var:     8.3260 | E_err:   0.031880
[2025-10-21 16:00:34] [Iter  126/2250] R0[125/150]   | LR: 0.006675 | E:  -55.157146 | E_var:     7.8633 | E_err:   0.030982
[2025-10-21 16:01:01] [Iter  127/2250] R0[126/150]   | LR: 0.006546 | E:  -55.233391 | E_var:     7.6589 | E_err:   0.030577
[2025-10-21 16:01:28] [Iter  128/2250] R0[127/150]   | LR: 0.006422 | E:  -55.351874 | E_var:     7.7067 | E_err:   0.030672
[2025-10-21 16:01:55] [Iter  129/2250] R0[128/150]   | LR: 0.006304 | E:  -55.350447 | E_var:     7.7495 | E_err:   0.030757
[2025-10-21 16:02:22] [Iter  130/2250] R0[129/150]   | LR: 0.006190 | E:  -55.437546 | E_var:     8.0085 | E_err:   0.031267
[2025-10-21 16:02:49] [Iter  131/2250] R0[130/150]   | LR: 0.006081 | E:  -55.558691 | E_var:     7.6185 | E_err:   0.030496
[2025-10-21 16:03:16] [Iter  132/2250] R0[131/150]   | LR: 0.005977 | E:  -55.580363 | E_var:     7.3322 | E_err:   0.029917
[2025-10-21 16:03:43] [Iter  133/2250] R0[132/150]   | LR: 0.005878 | E:  -55.595181 | E_var:     7.1936 | E_err:   0.029633
[2025-10-21 16:04:10] [Iter  134/2250] R0[133/150]   | LR: 0.005784 | E:  -55.718110 | E_var:     7.4404 | E_err:   0.030137
[2025-10-21 16:04:36] [Iter  135/2250] R0[134/150]   | LR: 0.005695 | E:  -55.752611 | E_var:     7.1076 | E_err:   0.029456
[2025-10-21 16:05:03] [Iter  136/2250] R0[135/150]   | LR: 0.005612 | E:  -55.786641 | E_var:     6.8437 | E_err:   0.028904
[2025-10-21 16:05:30] [Iter  137/2250] R0[136/150]   | LR: 0.005534 | E:  -55.852448 | E_var:     6.8495 | E_err:   0.028916
[2025-10-21 16:05:57] [Iter  138/2250] R0[137/150]   | LR: 0.005460 | E:  -55.934781 | E_var:     7.4337 | E_err:   0.030124
[2025-10-21 16:06:24] [Iter  139/2250] R0[138/150]   | LR: 0.005393 | E:  -55.980519 | E_var:     6.7403 | E_err:   0.028684
[2025-10-21 16:06:51] [Iter  140/2250] R0[139/150]   | LR: 0.005330 | E:  -56.038129 | E_var:     7.3357 | E_err:   0.029924
[2025-10-21 16:07:18] [Iter  141/2250] R0[140/150]   | LR: 0.005273 | E:  -56.110968 | E_var:     6.3824 | E_err:   0.027912
[2025-10-21 16:07:45] [Iter  142/2250] R0[141/150]   | LR: 0.005221 | E:  -56.139464 | E_var:     6.8999 | E_err:   0.029022
[2025-10-21 16:08:12] [Iter  143/2250] R0[142/150]   | LR: 0.005175 | E:  -56.165722 | E_var:     6.7027 | E_err:   0.028604
[2025-10-21 16:08:38] [Iter  144/2250] R0[143/150]   | LR: 0.005134 | E:  -56.237157 | E_var:     6.5929 | E_err:   0.028369
[2025-10-21 16:09:05] [Iter  145/2250] R0[144/150]   | LR: 0.005099 | E:  -56.320316 | E_var:     6.6799 | E_err:   0.028556
[2025-10-21 16:09:32] [Iter  146/2250] R0[145/150]   | LR: 0.005068 | E:  -56.380398 | E_var:     6.6222 | E_err:   0.028432
[2025-10-21 16:09:59] [Iter  147/2250] R0[146/150]   | LR: 0.005044 | E:  -56.429344 | E_var:     6.4802 | E_err:   0.028125
[2025-10-21 16:10:26] [Iter  148/2250] R0[147/150]   | LR: 0.005025 | E:  -56.468140 | E_var:     6.4718 | E_err:   0.028107
[2025-10-21 16:10:53] [Iter  149/2250] R0[148/150]   | LR: 0.005011 | E:  -56.499413 | E_var:     6.2381 | E_err:   0.027595
[2025-10-21 16:11:20] [Iter  150/2250] R0[149/150]   | LR: 0.005003 | E:  -56.578523 | E_var:     6.1847 | E_err:   0.027477
[2025-10-21 16:11:20] 🔄 RESTART #1 | Period: 300
[2025-10-21 16:11:47] [Iter  151/2250] R1[0/300]     | LR: 0.030000 | E:  -56.608606 | E_var:     6.4958 | E_err:   0.028159
[2025-10-21 16:12:14] [Iter  152/2250] R1[1/300]     | LR: 0.029999 | E:  -56.659798 | E_var:     5.9380 | E_err:   0.026923
[2025-10-21 16:12:40] [Iter  153/2250] R1[2/300]     | LR: 0.029997 | E:  -56.711819 | E_var:     6.0487 | E_err:   0.027173
[2025-10-21 16:13:07] [Iter  154/2250] R1[3/300]     | LR: 0.029994 | E:  -56.757703 | E_var:     6.1695 | E_err:   0.027443
[2025-10-21 16:13:34] [Iter  155/2250] R1[4/300]     | LR: 0.029989 | E:  -56.832418 | E_var:     5.8713 | E_err:   0.026772
[2025-10-21 16:14:01] [Iter  156/2250] R1[5/300]     | LR: 0.029983 | E:  -56.837539 | E_var:     6.1905 | E_err:   0.027490
[2025-10-21 16:14:28] [Iter  157/2250] R1[6/300]     | LR: 0.029975 | E:  -56.946025 | E_var:     6.0269 | E_err:   0.027124
[2025-10-21 16:14:55] [Iter  158/2250] R1[7/300]     | LR: 0.029966 | E:  -56.951155 | E_var:     6.2701 | E_err:   0.027666
[2025-10-21 16:15:22] [Iter  159/2250] R1[8/300]     | LR: 0.029956 | E:  -57.015309 | E_var:     6.1556 | E_err:   0.027412
[2025-10-21 16:15:49] [Iter  160/2250] R1[9/300]     | LR: 0.029945 | E:  -57.105571 | E_var:     6.0293 | E_err:   0.027129
[2025-10-21 16:16:16] [Iter  161/2250] R1[10/300]    | LR: 0.029932 | E:  -57.140020 | E_var:     5.9074 | E_err:   0.026854
[2025-10-21 16:16:42] [Iter  162/2250] R1[11/300]    | LR: 0.029917 | E:  -57.211472 | E_var:     5.8143 | E_err:   0.026641
[2025-10-21 16:17:09] [Iter  163/2250] R1[12/300]    | LR: 0.029901 | E:  -57.241709 | E_var:     6.1140 | E_err:   0.027319
[2025-10-21 16:17:36] [Iter  164/2250] R1[13/300]    | LR: 0.029884 | E:  -57.322700 | E_var:     5.9477 | E_err:   0.026945
[2025-10-21 16:18:03] [Iter  165/2250] R1[14/300]    | LR: 0.029866 | E:  -57.396024 | E_var:     6.3226 | E_err:   0.027781
[2025-10-21 16:18:30] [Iter  166/2250] R1[15/300]    | LR: 0.029846 | E:  -57.452265 | E_var:     5.9134 | E_err:   0.026867
[2025-10-21 16:18:57] [Iter  167/2250] R1[16/300]    | LR: 0.029825 | E:  -57.521355 | E_var:     5.8027 | E_err:   0.026615
[2025-10-21 16:19:24] [Iter  168/2250] R1[17/300]    | LR: 0.029802 | E:  -57.625758 | E_var:     5.8672 | E_err:   0.026762
[2025-10-21 16:19:51] [Iter  169/2250] R1[18/300]    | LR: 0.029779 | E:  -57.660259 | E_var:     5.5126 | E_err:   0.025941
[2025-10-21 16:20:17] [Iter  170/2250] R1[19/300]    | LR: 0.029753 | E:  -57.729183 | E_var:     5.9748 | E_err:   0.027006
[2025-10-21 16:20:44] [Iter  171/2250] R1[20/300]    | LR: 0.029727 | E:  -57.812573 | E_var:     5.4760 | E_err:   0.025854
[2025-10-21 16:21:11] [Iter  172/2250] R1[21/300]    | LR: 0.029699 | E:  -57.902846 | E_var:     5.7234 | E_err:   0.026432
[2025-10-21 16:21:38] [Iter  173/2250] R1[22/300]    | LR: 0.029670 | E:  -57.994773 | E_var:     5.3775 | E_err:   0.025621
[2025-10-21 16:22:05] [Iter  174/2250] R1[23/300]    | LR: 0.029639 | E:  -58.048892 | E_var:     5.7813 | E_err:   0.026566
[2025-10-21 16:22:32] [Iter  175/2250] R1[24/300]    | LR: 0.029607 | E:  -58.115067 | E_var:     5.6633 | E_err:   0.026293
[2025-10-21 16:22:59] [Iter  176/2250] R1[25/300]    | LR: 0.029574 | E:  -58.195198 | E_var:     5.3897 | E_err:   0.025650
[2025-10-21 16:23:26] [Iter  177/2250] R1[26/300]    | LR: 0.029540 | E:  -58.291651 | E_var:     5.3305 | E_err:   0.025509
[2025-10-21 16:23:52] [Iter  178/2250] R1[27/300]    | LR: 0.029504 | E:  -58.369911 | E_var:     5.5264 | E_err:   0.025973
[2025-10-21 16:24:19] [Iter  179/2250] R1[28/300]    | LR: 0.029466 | E:  -58.500048 | E_var:     5.2191 | E_err:   0.025241
[2025-10-21 16:24:46] [Iter  180/2250] R1[29/300]    | LR: 0.029428 | E:  -58.530535 | E_var:     5.5591 | E_err:   0.026050
[2025-10-21 16:25:13] [Iter  181/2250] R1[30/300]    | LR: 0.029388 | E:  -58.679044 | E_var:     5.2129 | E_err:   0.025226
[2025-10-21 16:25:40] [Iter  182/2250] R1[31/300]    | LR: 0.029347 | E:  -58.724326 | E_var:     4.9930 | E_err:   0.024688
[2025-10-21 16:26:07] [Iter  183/2250] R1[32/300]    | LR: 0.029305 | E:  -58.811026 | E_var:     5.0660 | E_err:   0.024868
[2025-10-21 16:26:34] [Iter  184/2250] R1[33/300]    | LR: 0.029261 | E:  -58.886102 | E_var:     5.0212 | E_err:   0.024758
[2025-10-21 16:27:01] [Iter  185/2250] R1[34/300]    | LR: 0.029216 | E:  -58.989324 | E_var:     5.0267 | E_err:   0.024771
[2025-10-21 16:27:28] [Iter  186/2250] R1[35/300]    | LR: 0.029170 | E:  -59.028846 | E_var:     4.8340 | E_err:   0.024292
[2025-10-21 16:27:54] [Iter  187/2250] R1[36/300]    | LR: 0.029122 | E:  -59.132285 | E_var:     4.4059 | E_err:   0.023191
[2025-10-21 16:28:21] [Iter  188/2250] R1[37/300]    | LR: 0.029073 | E:  -59.180332 | E_var:     4.3671 | E_err:   0.023089
[2025-10-21 16:28:48] [Iter  189/2250] R1[38/300]    | LR: 0.029023 | E:  -59.233562 | E_var:     4.7143 | E_err:   0.023989
[2025-10-21 16:29:15] [Iter  190/2250] R1[39/300]    | LR: 0.028972 | E:  -59.289769 | E_var:     4.2188 | E_err:   0.022694
[2025-10-21 16:29:42] [Iter  191/2250] R1[40/300]    | LR: 0.028919 | E:  -59.357938 | E_var:     4.1064 | E_err:   0.022389
[2025-10-21 16:30:09] [Iter  192/2250] R1[41/300]    | LR: 0.028865 | E:  -59.399856 | E_var:     4.0445 | E_err:   0.022220
[2025-10-21 16:30:36] [Iter  193/2250] R1[42/300]    | LR: 0.028810 | E:  -59.464292 | E_var:     3.9734 | E_err:   0.022023
[2025-10-21 16:31:03] [Iter  194/2250] R1[43/300]    | LR: 0.028754 | E:  -59.470095 | E_var:     3.9133 | E_err:   0.021856
[2025-10-21 16:31:30] [Iter  195/2250] R1[44/300]    | LR: 0.028696 | E:  -59.522092 | E_var:     3.6599 | E_err:   0.021137
[2025-10-21 16:31:56] [Iter  196/2250] R1[45/300]    | LR: 0.028638 | E:  -59.526682 | E_var:     3.8287 | E_err:   0.021619
[2025-10-21 16:32:23] [Iter  197/2250] R1[46/300]    | LR: 0.028578 | E:  -59.576330 | E_var:     3.7243 | E_err:   0.021322
[2025-10-21 16:32:50] [Iter  198/2250] R1[47/300]    | LR: 0.028516 | E:  -59.608606 | E_var:     3.4913 | E_err:   0.020644
[2025-10-21 16:33:17] [Iter  199/2250] R1[48/300]    | LR: 0.028454 | E:  -59.655954 | E_var:     4.1043 | E_err:   0.022383
[2025-10-21 16:33:44] [Iter  200/2250] R1[49/300]    | LR: 0.028390 | E:  -59.667127 | E_var:     3.4395 | E_err:   0.020491
[2025-10-21 16:34:11] [Iter  201/2250] R1[50/300]    | LR: 0.028325 | E:  -59.683596 | E_var:     3.3199 | E_err:   0.020131
[2025-10-21 16:34:38] [Iter  202/2250] R1[51/300]    | LR: 0.028259 | E:  -59.727675 | E_var:     3.2250 | E_err:   0.019841
[2025-10-21 16:35:05] [Iter  203/2250] R1[52/300]    | LR: 0.028192 | E:  -59.766216 | E_var:     3.4090 | E_err:   0.020399
[2025-10-21 16:35:32] [Iter  204/2250] R1[53/300]    | LR: 0.028124 | E:  -59.754366 | E_var:     3.2892 | E_err:   0.020038
[2025-10-21 16:35:59] [Iter  205/2250] R1[54/300]    | LR: 0.028054 | E:  -59.736763 | E_var:     3.6072 | E_err:   0.020984
[2025-10-21 16:36:26] [Iter  206/2250] R1[55/300]    | LR: 0.027983 | E:  -59.787041 | E_var:     3.2416 | E_err:   0.019892
[2025-10-21 16:36:52] [Iter  207/2250] R1[56/300]    | LR: 0.027912 | E:  -59.829366 | E_var:     3.5720 | E_err:   0.020881
[2025-10-21 16:37:19] [Iter  208/2250] R1[57/300]    | LR: 0.027839 | E:  -59.821371 | E_var:     3.0640 | E_err:   0.019340
[2025-10-21 16:37:46] [Iter  209/2250] R1[58/300]    | LR: 0.027764 | E:  -59.851370 | E_var:     3.4097 | E_err:   0.020401
[2025-10-21 16:38:13] [Iter  210/2250] R1[59/300]    | LR: 0.027689 | E:  -59.840887 | E_var:     3.1231 | E_err:   0.019525
[2025-10-21 16:38:40] [Iter  211/2250] R1[60/300]    | LR: 0.027613 | E:  -59.906062 | E_var:     2.8436 | E_err:   0.018631
[2025-10-21 16:39:07] [Iter  212/2250] R1[61/300]    | LR: 0.027535 | E:  -59.872982 | E_var:     3.1959 | E_err:   0.019752
[2025-10-21 16:39:34] [Iter  213/2250] R1[62/300]    | LR: 0.027457 | E:  -59.900589 | E_var:     3.0133 | E_err:   0.019179
[2025-10-21 16:40:01] [Iter  214/2250] R1[63/300]    | LR: 0.027377 | E:  -59.929674 | E_var:     3.0632 | E_err:   0.019337
[2025-10-21 16:40:28] [Iter  215/2250] R1[64/300]    | LR: 0.027296 | E:  -59.925610 | E_var:     2.8813 | E_err:   0.018754
[2025-10-21 16:40:55] [Iter  216/2250] R1[65/300]    | LR: 0.027214 | E:  -59.912025 | E_var:     3.0450 | E_err:   0.019280
[2025-10-21 16:41:22] [Iter  217/2250] R1[66/300]    | LR: 0.027131 | E:  -59.936134 | E_var:     2.9280 | E_err:   0.018906
[2025-10-21 16:41:49] [Iter  218/2250] R1[67/300]    | LR: 0.027047 | E:  -59.967499 | E_var:     2.9641 | E_err:   0.019022
[2025-10-21 16:42:15] [Iter  219/2250] R1[68/300]    | LR: 0.026962 | E:  -60.001832 | E_var:     3.0517 | E_err:   0.019301
[2025-10-21 16:42:42] [Iter  220/2250] R1[69/300]    | LR: 0.026876 | E:  -59.974990 | E_var:     2.5587 | E_err:   0.017673
[2025-10-21 16:43:09] [Iter  221/2250] R1[70/300]    | LR: 0.026789 | E:  -60.018634 | E_var:     3.0067 | E_err:   0.019158
[2025-10-21 16:43:36] [Iter  222/2250] R1[71/300]    | LR: 0.026701 | E:  -60.048348 | E_var:     2.5706 | E_err:   0.017714
[2025-10-21 16:44:03] [Iter  223/2250] R1[72/300]    | LR: 0.026612 | E:  -60.022077 | E_var:     2.7628 | E_err:   0.018364
[2025-10-21 16:44:30] [Iter  224/2250] R1[73/300]    | LR: 0.026522 | E:  -60.019928 | E_var:     2.8516 | E_err:   0.018657
[2025-10-21 16:44:57] [Iter  225/2250] R1[74/300]    | LR: 0.026431 | E:  -60.033786 | E_var:     2.8661 | E_err:   0.018705
[2025-10-21 16:44:57] ✓ Checkpoint saved: checkpoint_iter_000225.pkl
[2025-10-21 16:45:24] [Iter  226/2250] R1[75/300]    | LR: 0.026339 | E:  -60.051818 | E_var:     2.8203 | E_err:   0.018555
[2025-10-21 16:45:51] [Iter  227/2250] R1[76/300]    | LR: 0.026246 | E:  -60.083961 | E_var:     3.3481 | E_err:   0.020216
[2025-10-21 16:46:18] [Iter  228/2250] R1[77/300]    | LR: 0.026152 | E:  -60.119146 | E_var:     2.5053 | E_err:   0.017488
[2025-10-21 16:46:44] [Iter  229/2250] R1[78/300]    | LR: 0.026057 | E:  -60.096192 | E_var:     2.6365 | E_err:   0.017940
[2025-10-21 16:47:11] [Iter  230/2250] R1[79/300]    | LR: 0.025961 | E:  -60.130273 | E_var:     2.5879 | E_err:   0.017774
[2025-10-21 16:47:38] [Iter  231/2250] R1[80/300]    | LR: 0.025864 | E:  -60.135380 | E_var:     2.9555 | E_err:   0.018994
[2025-10-21 16:48:05] [Iter  232/2250] R1[81/300]    | LR: 0.025766 | E:  -60.145780 | E_var:     2.7845 | E_err:   0.018436
[2025-10-21 16:48:32] [Iter  233/2250] R1[82/300]    | LR: 0.025668 | E:  -60.173055 | E_var:     2.3976 | E_err:   0.017108
[2025-10-21 16:48:59] [Iter  234/2250] R1[83/300]    | LR: 0.025568 | E:  -60.142756 | E_var:     2.7209 | E_err:   0.018225
[2025-10-21 16:49:26] [Iter  235/2250] R1[84/300]    | LR: 0.025468 | E:  -60.179345 | E_var:     2.4530 | E_err:   0.017304
[2025-10-21 16:49:53] [Iter  236/2250] R1[85/300]    | LR: 0.025367 | E:  -60.180158 | E_var:     2.4134 | E_err:   0.017164
[2025-10-21 16:50:20] [Iter  237/2250] R1[86/300]    | LR: 0.025264 | E:  -60.197771 | E_var:     2.2693 | E_err:   0.016644
[2025-10-21 16:50:46] [Iter  238/2250] R1[87/300]    | LR: 0.025161 | E:  -60.210546 | E_var:     2.1802 | E_err:   0.016314
[2025-10-21 16:51:13] [Iter  239/2250] R1[88/300]    | LR: 0.025057 | E:  -60.222840 | E_var:     2.2444 | E_err:   0.016552
[2025-10-21 16:51:40] [Iter  240/2250] R1[89/300]    | LR: 0.024953 | E:  -60.188637 | E_var:     2.3955 | E_err:   0.017100
[2025-10-21 16:52:07] [Iter  241/2250] R1[90/300]    | LR: 0.024847 | E:  -60.242677 | E_var:     2.4510 | E_err:   0.017297
[2025-10-21 16:52:34] [Iter  242/2250] R1[91/300]    | LR: 0.024741 | E:  -60.259715 | E_var:     2.3068 | E_err:   0.016781
[2025-10-21 16:53:01] [Iter  243/2250] R1[92/300]    | LR: 0.024634 | E:  -60.270257 | E_var:     3.2813 | E_err:   0.020014
[2025-10-21 16:53:28] [Iter  244/2250] R1[93/300]    | LR: 0.024526 | E:  -60.285609 | E_var:     2.5708 | E_err:   0.017715
[2025-10-21 16:53:55] [Iter  245/2250] R1[94/300]    | LR: 0.024417 | E:  -60.267859 | E_var:     2.9883 | E_err:   0.019099
[2025-10-21 16:54:22] [Iter  246/2250] R1[95/300]    | LR: 0.024308 | E:  -60.316406 | E_var:     3.3614 | E_err:   0.020257
[2025-10-21 16:54:48] [Iter  247/2250] R1[96/300]    | LR: 0.024198 | E:  -60.310464 | E_var:     2.0765 | E_err:   0.015921
[2025-10-21 16:55:15] [Iter  248/2250] R1[97/300]    | LR: 0.024087 | E:  -60.294785 | E_var:     2.4218 | E_err:   0.017194
[2025-10-21 16:55:42] [Iter  249/2250] R1[98/300]    | LR: 0.023975 | E:  -60.292966 | E_var:     2.0360 | E_err:   0.015765
[2025-10-21 16:56:09] [Iter  250/2250] R1[99/300]    | LR: 0.023863 | E:  -60.278439 | E_var:     2.1972 | E_err:   0.016377
[2025-10-21 16:56:36] [Iter  251/2250] R1[100/300]   | LR: 0.023750 | E:  -60.340959 | E_var:     3.6045 | E_err:   0.020976
[2025-10-21 16:57:03] [Iter  252/2250] R1[101/300]   | LR: 0.023636 | E:  -60.324825 | E_var:     2.1861 | E_err:   0.016336
[2025-10-21 16:57:30] [Iter  253/2250] R1[102/300]   | LR: 0.023522 | E:  -60.378445 | E_var:     2.2000 | E_err:   0.016387
[2025-10-21 16:57:56] [Iter  254/2250] R1[103/300]   | LR: 0.023407 | E:  -60.371650 | E_var:     2.1148 | E_err:   0.016067
[2025-10-21 16:58:23] [Iter  255/2250] R1[104/300]   | LR: 0.023291 | E:  -60.366512 | E_var:     2.0153 | E_err:   0.015685
[2025-10-21 16:58:50] [Iter  256/2250] R1[105/300]   | LR: 0.023175 | E:  -60.389917 | E_var:     2.0983 | E_err:   0.016004
[2025-10-21 16:59:17] [Iter  257/2250] R1[106/300]   | LR: 0.023058 | E:  -60.373863 | E_var:     2.0328 | E_err:   0.015753
[2025-10-21 16:59:44] [Iter  258/2250] R1[107/300]   | LR: 0.022940 | E:  -60.398984 | E_var:     2.4407 | E_err:   0.017261
[2025-10-21 17:00:11] [Iter  259/2250] R1[108/300]   | LR: 0.022822 | E:  -60.399007 | E_var:     1.9568 | E_err:   0.015455
[2025-10-21 17:00:38] [Iter  260/2250] R1[109/300]   | LR: 0.022704 | E:  -60.416108 | E_var:     2.0437 | E_err:   0.015795
[2025-10-21 17:01:04] [Iter  261/2250] R1[110/300]   | LR: 0.022584 | E:  -60.409999 | E_var:     2.9964 | E_err:   0.019125
[2025-10-21 17:01:31] [Iter  262/2250] R1[111/300]   | LR: 0.022464 | E:  -60.430326 | E_var:     1.9847 | E_err:   0.015565
[2025-10-21 17:01:58] [Iter  263/2250] R1[112/300]   | LR: 0.022344 | E:  -60.453977 | E_var:     2.0877 | E_err:   0.015964
[2025-10-21 17:02:25] [Iter  264/2250] R1[113/300]   | LR: 0.022223 | E:  -60.407965 | E_var:     2.0804 | E_err:   0.015936
[2025-10-21 17:02:52] [Iter  265/2250] R1[114/300]   | LR: 0.022102 | E:  -60.469391 | E_var:     1.8946 | E_err:   0.015208
[2025-10-21 17:03:19] [Iter  266/2250] R1[115/300]   | LR: 0.021980 | E:  -60.459198 | E_var:     2.0053 | E_err:   0.015646
[2025-10-21 17:03:46] [Iter  267/2250] R1[116/300]   | LR: 0.021857 | E:  -60.443870 | E_var:     2.4633 | E_err:   0.017341
[2025-10-21 17:04:13] [Iter  268/2250] R1[117/300]   | LR: 0.021734 | E:  -60.468686 | E_var:     1.8137 | E_err:   0.014879
[2025-10-21 17:04:39] [Iter  269/2250] R1[118/300]   | LR: 0.021611 | E:  -60.473852 | E_var:     1.9156 | E_err:   0.015292
[2025-10-21 17:05:06] [Iter  270/2250] R1[119/300]   | LR: 0.021487 | E:  -60.468458 | E_var:     2.0997 | E_err:   0.016010
[2025-10-21 17:05:33] [Iter  271/2250] R1[120/300]   | LR: 0.021363 | E:  -60.481882 | E_var:     3.4849 | E_err:   0.020625
[2025-10-21 17:06:00] [Iter  272/2250] R1[121/300]   | LR: 0.021238 | E:  -60.508101 | E_var:     1.7421 | E_err:   0.014583
[2025-10-21 17:06:27] [Iter  273/2250] R1[122/300]   | LR: 0.021113 | E:  -60.488000 | E_var:     1.7510 | E_err:   0.014620
[2025-10-21 17:06:54] [Iter  274/2250] R1[123/300]   | LR: 0.020987 | E:  -60.519079 | E_var:     1.7173 | E_err:   0.014479
[2025-10-21 17:07:21] [Iter  275/2250] R1[124/300]   | LR: 0.020861 | E:  -60.474994 | E_var:     2.0480 | E_err:   0.015811
[2025-10-21 17:07:48] [Iter  276/2250] R1[125/300]   | LR: 0.020735 | E:  -60.523680 | E_var:     1.7235 | E_err:   0.014505
[2025-10-21 17:08:14] [Iter  277/2250] R1[126/300]   | LR: 0.020609 | E:  -60.544377 | E_var:     1.6390 | E_err:   0.014145
[2025-10-21 17:08:41] [Iter  278/2250] R1[127/300]   | LR: 0.020482 | E:  -60.543742 | E_var:     2.1265 | E_err:   0.016112
[2025-10-21 17:09:08] [Iter  279/2250] R1[128/300]   | LR: 0.020354 | E:  -60.521190 | E_var:     1.6550 | E_err:   0.014214
[2025-10-21 17:09:35] [Iter  280/2250] R1[129/300]   | LR: 0.020227 | E:  -60.545763 | E_var:     2.3353 | E_err:   0.016884
[2025-10-21 17:10:02] [Iter  281/2250] R1[130/300]   | LR: 0.020099 | E:  -60.553380 | E_var:     1.5788 | E_err:   0.013883
[2025-10-21 17:10:29] [Iter  282/2250] R1[131/300]   | LR: 0.019971 | E:  -60.554136 | E_var:     1.6915 | E_err:   0.014369
[2025-10-21 17:10:56] [Iter  283/2250] R1[132/300]   | LR: 0.019842 | E:  -60.552469 | E_var:     1.6497 | E_err:   0.014191
[2025-10-21 17:11:22] [Iter  284/2250] R1[133/300]   | LR: 0.019714 | E:  -60.553032 | E_var:     1.6027 | E_err:   0.013987
[2025-10-21 17:11:49] [Iter  285/2250] R1[134/300]   | LR: 0.019585 | E:  -60.592250 | E_var:     1.5705 | E_err:   0.013846
[2025-10-21 17:12:16] [Iter  286/2250] R1[135/300]   | LR: 0.019455 | E:  -60.573218 | E_var:     1.5459 | E_err:   0.013737
[2025-10-21 17:12:43] [Iter  287/2250] R1[136/300]   | LR: 0.019326 | E:  -60.556954 | E_var:     1.5593 | E_err:   0.013796
[2025-10-21 17:13:10] [Iter  288/2250] R1[137/300]   | LR: 0.019196 | E:  -60.586504 | E_var:     1.6178 | E_err:   0.014053
[2025-10-21 17:13:37] [Iter  289/2250] R1[138/300]   | LR: 0.019067 | E:  -60.582670 | E_var:     1.5512 | E_err:   0.013761
[2025-10-21 17:14:04] [Iter  290/2250] R1[139/300]   | LR: 0.018937 | E:  -60.589452 | E_var:     1.5098 | E_err:   0.013576
[2025-10-21 17:14:31] [Iter  291/2250] R1[140/300]   | LR: 0.018807 | E:  -60.603825 | E_var:     1.6069 | E_err:   0.014006
[2025-10-21 17:14:57] [Iter  292/2250] R1[141/300]   | LR: 0.018676 | E:  -60.587604 | E_var:     1.6004 | E_err:   0.013977
[2025-10-21 17:15:24] [Iter  293/2250] R1[142/300]   | LR: 0.018546 | E:  -60.598858 | E_var:     2.1840 | E_err:   0.016328
[2025-10-21 17:15:51] [Iter  294/2250] R1[143/300]   | LR: 0.018415 | E:  -60.597975 | E_var:     1.4820 | E_err:   0.013450
[2025-10-21 17:16:18] [Iter  295/2250] R1[144/300]   | LR: 0.018285 | E:  -60.602460 | E_var:     1.9684 | E_err:   0.015501
[2025-10-21 17:16:45] [Iter  296/2250] R1[145/300]   | LR: 0.018154 | E:  -60.610124 | E_var:     1.4126 | E_err:   0.013131
[2025-10-21 17:17:12] [Iter  297/2250] R1[146/300]   | LR: 0.018023 | E:  -60.645290 | E_var:     1.6052 | E_err:   0.013998
[2025-10-21 17:17:39] [Iter  298/2250] R1[147/300]   | LR: 0.017893 | E:  -60.623650 | E_var:     1.3500 | E_err:   0.012837
[2025-10-21 17:18:06] [Iter  299/2250] R1[148/300]   | LR: 0.017762 | E:  -60.625176 | E_var:     1.3741 | E_err:   0.012951
[2025-10-21 17:18:33] [Iter  300/2250] R1[149/300]   | LR: 0.017631 | E:  -60.615182 | E_var:     1.4523 | E_err:   0.013315
[2025-10-21 17:18:59] [Iter  301/2250] R1[150/300]   | LR: 0.017500 | E:  -60.636815 | E_var:     1.2712 | E_err:   0.012457
[2025-10-21 17:19:26] [Iter  302/2250] R1[151/300]   | LR: 0.017369 | E:  -60.625995 | E_var:     2.0291 | E_err:   0.015738
[2025-10-21 17:19:53] [Iter  303/2250] R1[152/300]   | LR: 0.017238 | E:  -60.636883 | E_var:     1.3909 | E_err:   0.013030
[2025-10-21 17:20:20] [Iter  304/2250] R1[153/300]   | LR: 0.017107 | E:  -60.636600 | E_var:     1.5154 | E_err:   0.013601
[2025-10-21 17:20:47] [Iter  305/2250] R1[154/300]   | LR: 0.016977 | E:  -60.645254 | E_var:     1.1257 | E_err:   0.011723
[2025-10-21 17:21:14] [Iter  306/2250] R1[155/300]   | LR: 0.016846 | E:  -60.656078 | E_var:     1.4706 | E_err:   0.013398
[2025-10-21 17:21:41] [Iter  307/2250] R1[156/300]   | LR: 0.016715 | E:  -60.661866 | E_var:     1.2285 | E_err:   0.012246
[2025-10-21 17:22:08] [Iter  308/2250] R1[157/300]   | LR: 0.016585 | E:  -60.638375 | E_var:     1.3792 | E_err:   0.012975
[2025-10-21 17:22:34] [Iter  309/2250] R1[158/300]   | LR: 0.016454 | E:  -60.634280 | E_var:     1.1812 | E_err:   0.012008
[2025-10-21 17:23:01] [Iter  310/2250] R1[159/300]   | LR: 0.016324 | E:  -60.655399 | E_var:     1.4061 | E_err:   0.013101
[2025-10-21 17:23:28] [Iter  311/2250] R1[160/300]   | LR: 0.016193 | E:  -60.649924 | E_var:     1.2857 | E_err:   0.012528
[2025-10-21 17:23:55] [Iter  312/2250] R1[161/300]   | LR: 0.016063 | E:  -60.674090 | E_var:     1.4090 | E_err:   0.013115
[2025-10-21 17:24:22] [Iter  313/2250] R1[162/300]   | LR: 0.015933 | E:  -60.676643 | E_var:     1.1889 | E_err:   0.012047
[2025-10-21 17:24:49] [Iter  314/2250] R1[163/300]   | LR: 0.015804 | E:  -60.662385 | E_var:     1.3022 | E_err:   0.012608
[2025-10-21 17:25:16] [Iter  315/2250] R1[164/300]   | LR: 0.015674 | E:  -60.652284 | E_var:     2.2556 | E_err:   0.016593
[2025-10-21 17:25:43] [Iter  316/2250] R1[165/300]   | LR: 0.015545 | E:  -60.691811 | E_var:     1.3622 | E_err:   0.012895
[2025-10-21 17:26:09] [Iter  317/2250] R1[166/300]   | LR: 0.015415 | E:  -60.672677 | E_var:     1.2011 | E_err:   0.012108
[2025-10-21 17:26:36] [Iter  318/2250] R1[167/300]   | LR: 0.015286 | E:  -60.681185 | E_var:     1.3357 | E_err:   0.012769
[2025-10-21 17:27:03] [Iter  319/2250] R1[168/300]   | LR: 0.015158 | E:  -60.685101 | E_var:     1.2534 | E_err:   0.012369
[2025-10-21 17:27:30] [Iter  320/2250] R1[169/300]   | LR: 0.015029 | E:  -60.680905 | E_var:     1.3873 | E_err:   0.013013
[2025-10-21 17:27:57] [Iter  321/2250] R1[170/300]   | LR: 0.014901 | E:  -60.674322 | E_var:     1.3162 | E_err:   0.012676
[2025-10-21 17:28:24] [Iter  322/2250] R1[171/300]   | LR: 0.014773 | E:  -60.654695 | E_var:     1.2007 | E_err:   0.012107
[2025-10-21 17:28:51] [Iter  323/2250] R1[172/300]   | LR: 0.014646 | E:  -60.690827 | E_var:     1.3037 | E_err:   0.012615
[2025-10-21 17:29:17] [Iter  324/2250] R1[173/300]   | LR: 0.014518 | E:  -60.680838 | E_var:     1.1752 | E_err:   0.011977
[2025-10-21 17:29:44] [Iter  325/2250] R1[174/300]   | LR: 0.014391 | E:  -60.668553 | E_var:     1.1604 | E_err:   0.011902
[2025-10-21 17:30:11] [Iter  326/2250] R1[175/300]   | LR: 0.014265 | E:  -60.691638 | E_var:     1.1862 | E_err:   0.012033
[2025-10-21 17:30:38] [Iter  327/2250] R1[176/300]   | LR: 0.014139 | E:  -60.684653 | E_var:     1.5427 | E_err:   0.013723
[2025-10-21 17:31:05] [Iter  328/2250] R1[177/300]   | LR: 0.014013 | E:  -60.696118 | E_var:     1.2157 | E_err:   0.012182
[2025-10-21 17:31:32] [Iter  329/2250] R1[178/300]   | LR: 0.013887 | E:  -60.685696 | E_var:     1.0388 | E_err:   0.011261
[2025-10-21 17:31:59] [Iter  330/2250] R1[179/300]   | LR: 0.013762 | E:  -60.679313 | E_var:     1.1239 | E_err:   0.011713
[2025-10-21 17:32:26] [Iter  331/2250] R1[180/300]   | LR: 0.013637 | E:  -60.690877 | E_var:     1.0961 | E_err:   0.011567
[2025-10-21 17:32:52] [Iter  332/2250] R1[181/300]   | LR: 0.013513 | E:  -60.714804 | E_var:     1.2533 | E_err:   0.012369
[2025-10-21 17:33:19] [Iter  333/2250] R1[182/300]   | LR: 0.013389 | E:  -60.701637 | E_var:     1.1692 | E_err:   0.011947
[2025-10-21 17:33:46] [Iter  334/2250] R1[183/300]   | LR: 0.013266 | E:  -60.693017 | E_var:     1.1085 | E_err:   0.011633
[2025-10-21 17:34:13] [Iter  335/2250] R1[184/300]   | LR: 0.013143 | E:  -60.714338 | E_var:     1.2482 | E_err:   0.012344
[2025-10-21 17:34:40] [Iter  336/2250] R1[185/300]   | LR: 0.013020 | E:  -60.705287 | E_var:     1.0909 | E_err:   0.011540
[2025-10-21 17:35:07] [Iter  337/2250] R1[186/300]   | LR: 0.012898 | E:  -60.708325 | E_var:     1.1414 | E_err:   0.011804
[2025-10-21 17:35:34] [Iter  338/2250] R1[187/300]   | LR: 0.012777 | E:  -60.704937 | E_var:     1.0772 | E_err:   0.011467
[2025-10-21 17:36:01] [Iter  339/2250] R1[188/300]   | LR: 0.012656 | E:  -60.689331 | E_var:     1.2057 | E_err:   0.012132
[2025-10-21 17:36:27] [Iter  340/2250] R1[189/300]   | LR: 0.012536 | E:  -60.703478 | E_var:     1.2059 | E_err:   0.012133
[2025-10-21 17:36:54] [Iter  341/2250] R1[190/300]   | LR: 0.012416 | E:  -60.702115 | E_var:     1.2794 | E_err:   0.012497
[2025-10-21 17:37:21] [Iter  342/2250] R1[191/300]   | LR: 0.012296 | E:  -60.691514 | E_var:     1.1348 | E_err:   0.011770
[2025-10-21 17:37:48] [Iter  343/2250] R1[192/300]   | LR: 0.012178 | E:  -60.696860 | E_var:     1.2614 | E_err:   0.012409
[2025-10-21 17:38:15] [Iter  344/2250] R1[193/300]   | LR: 0.012060 | E:  -60.720613 | E_var:     1.0776 | E_err:   0.011469
[2025-10-21 17:38:42] [Iter  345/2250] R1[194/300]   | LR: 0.011942 | E:  -60.717782 | E_var:     1.0971 | E_err:   0.011572
[2025-10-21 17:39:09] [Iter  346/2250] R1[195/300]   | LR: 0.011825 | E:  -60.710020 | E_var:     1.3167 | E_err:   0.012678
[2025-10-21 17:39:36] [Iter  347/2250] R1[196/300]   | LR: 0.011709 | E:  -60.712925 | E_var:     1.1638 | E_err:   0.011919
[2025-10-21 17:40:02] [Iter  348/2250] R1[197/300]   | LR: 0.011593 | E:  -60.708210 | E_var:     0.9899 | E_err:   0.010992
[2025-10-21 17:40:29] [Iter  349/2250] R1[198/300]   | LR: 0.011478 | E:  -60.708570 | E_var:     1.1873 | E_err:   0.012039
[2025-10-21 17:40:56] [Iter  350/2250] R1[199/300]   | LR: 0.011364 | E:  -60.722442 | E_var:     1.3371 | E_err:   0.012776
[2025-10-21 17:41:23] [Iter  351/2250] R1[200/300]   | LR: 0.011250 | E:  -60.734516 | E_var:     1.0643 | E_err:   0.011398
[2025-10-21 17:41:50] [Iter  352/2250] R1[201/300]   | LR: 0.011137 | E:  -60.738448 | E_var:     1.0868 | E_err:   0.011518
[2025-10-21 17:42:17] [Iter  353/2250] R1[202/300]   | LR: 0.011025 | E:  -60.705807 | E_var:     1.1871 | E_err:   0.012038
[2025-10-21 17:42:44] [Iter  354/2250] R1[203/300]   | LR: 0.010913 | E:  -60.720991 | E_var:     1.0091 | E_err:   0.011099
[2025-10-21 17:43:10] [Iter  355/2250] R1[204/300]   | LR: 0.010802 | E:  -60.721068 | E_var:     1.0722 | E_err:   0.011440
[2025-10-21 17:43:37] [Iter  356/2250] R1[205/300]   | LR: 0.010692 | E:  -60.726334 | E_var:     0.9999 | E_err:   0.011048
[2025-10-21 17:44:04] [Iter  357/2250] R1[206/300]   | LR: 0.010583 | E:  -60.749075 | E_var:     2.2443 | E_err:   0.016552
[2025-10-21 17:44:31] [Iter  358/2250] R1[207/300]   | LR: 0.010474 | E:  -60.732885 | E_var:     1.2056 | E_err:   0.012131
[2025-10-21 17:44:58] [Iter  359/2250] R1[208/300]   | LR: 0.010366 | E:  -60.733141 | E_var:     1.1431 | E_err:   0.011813
[2025-10-21 17:45:25] [Iter  360/2250] R1[209/300]   | LR: 0.010259 | E:  -60.734225 | E_var:     1.3951 | E_err:   0.013050
[2025-10-21 17:45:52] [Iter  361/2250] R1[210/300]   | LR: 0.010153 | E:  -60.726504 | E_var:     1.7608 | E_err:   0.014661
[2025-10-21 17:46:18] [Iter  362/2250] R1[211/300]   | LR: 0.010047 | E:  -60.725554 | E_var:     1.0335 | E_err:   0.011232
[2025-10-21 17:46:45] [Iter  363/2250] R1[212/300]   | LR: 0.009943 | E:  -60.729404 | E_var:     0.9680 | E_err:   0.010870
[2025-10-21 17:47:12] [Iter  364/2250] R1[213/300]   | LR: 0.009839 | E:  -60.723695 | E_var:     1.0841 | E_err:   0.011504
[2025-10-21 17:47:39] [Iter  365/2250] R1[214/300]   | LR: 0.009736 | E:  -60.741510 | E_var:     1.4972 | E_err:   0.013519
[2025-10-21 17:48:06] [Iter  366/2250] R1[215/300]   | LR: 0.009633 | E:  -60.740249 | E_var:     1.1827 | E_err:   0.012016
[2025-10-21 17:48:33] [Iter  367/2250] R1[216/300]   | LR: 0.009532 | E:  -60.729383 | E_var:     1.2009 | E_err:   0.012108
[2025-10-21 17:49:00] [Iter  368/2250] R1[217/300]   | LR: 0.009432 | E:  -60.742625 | E_var:     1.0744 | E_err:   0.011452
[2025-10-21 17:49:26] [Iter  369/2250] R1[218/300]   | LR: 0.009332 | E:  -60.737760 | E_var:     1.1139 | E_err:   0.011661
[2025-10-21 17:49:53] [Iter  370/2250] R1[219/300]   | LR: 0.009234 | E:  -60.733257 | E_var:     0.9691 | E_err:   0.010876
[2025-10-21 17:50:20] [Iter  371/2250] R1[220/300]   | LR: 0.009136 | E:  -60.731390 | E_var:     1.0306 | E_err:   0.011217
[2025-10-21 17:50:47] [Iter  372/2250] R1[221/300]   | LR: 0.009039 | E:  -60.759705 | E_var:     0.9389 | E_err:   0.010706
[2025-10-21 17:51:14] [Iter  373/2250] R1[222/300]   | LR: 0.008943 | E:  -60.767053 | E_var:     1.2028 | E_err:   0.012117
[2025-10-21 17:51:41] [Iter  374/2250] R1[223/300]   | LR: 0.008848 | E:  -60.734927 | E_var:     0.9784 | E_err:   0.010929
[2025-10-21 17:52:08] [Iter  375/2250] R1[224/300]   | LR: 0.008754 | E:  -60.750033 | E_var:     1.0887 | E_err:   0.011528
[2025-10-21 17:52:34] [Iter  376/2250] R1[225/300]   | LR: 0.008661 | E:  -60.738895 | E_var:     1.3951 | E_err:   0.013050
[2025-10-21 17:53:01] [Iter  377/2250] R1[226/300]   | LR: 0.008569 | E:  -60.752109 | E_var:     0.9860 | E_err:   0.010971
[2025-10-21 17:53:28] [Iter  378/2250] R1[227/300]   | LR: 0.008478 | E:  -60.750833 | E_var:     1.2645 | E_err:   0.012424
[2025-10-21 17:53:55] [Iter  379/2250] R1[228/300]   | LR: 0.008388 | E:  -60.746864 | E_var:     1.0568 | E_err:   0.011358
[2025-10-21 17:54:22] [Iter  380/2250] R1[229/300]   | LR: 0.008299 | E:  -60.752598 | E_var:     0.9985 | E_err:   0.011040
[2025-10-21 17:54:49] [Iter  381/2250] R1[230/300]   | LR: 0.008211 | E:  -60.746611 | E_var:     1.2534 | E_err:   0.012369
[2025-10-21 17:55:16] [Iter  382/2250] R1[231/300]   | LR: 0.008124 | E:  -60.757688 | E_var:     0.9344 | E_err:   0.010680
[2025-10-21 17:55:42] [Iter  383/2250] R1[232/300]   | LR: 0.008038 | E:  -60.757561 | E_var:     0.9647 | E_err:   0.010852
[2025-10-21 17:56:09] [Iter  384/2250] R1[233/300]   | LR: 0.007953 | E:  -60.756926 | E_var:     1.0056 | E_err:   0.011079
[2025-10-21 17:56:36] [Iter  385/2250] R1[234/300]   | LR: 0.007869 | E:  -60.742257 | E_var:     1.0801 | E_err:   0.011483
[2025-10-21 17:57:03] [Iter  386/2250] R1[235/300]   | LR: 0.007786 | E:  -60.762133 | E_var:     0.9464 | E_err:   0.010748
[2025-10-21 17:57:30] [Iter  387/2250] R1[236/300]   | LR: 0.007704 | E:  -60.758187 | E_var:     1.2251 | E_err:   0.012229
[2025-10-21 17:57:57] [Iter  388/2250] R1[237/300]   | LR: 0.007623 | E:  -60.746960 | E_var:     1.1192 | E_err:   0.011688
[2025-10-21 17:58:24] [Iter  389/2250] R1[238/300]   | LR: 0.007543 | E:  -60.763495 | E_var:     1.0918 | E_err:   0.011544
[2025-10-21 17:58:50] [Iter  390/2250] R1[239/300]   | LR: 0.007465 | E:  -60.774382 | E_var:     1.0430 | E_err:   0.011284
[2025-10-21 17:59:17] [Iter  391/2250] R1[240/300]   | LR: 0.007387 | E:  -60.774249 | E_var:     1.0663 | E_err:   0.011409
[2025-10-21 17:59:44] [Iter  392/2250] R1[241/300]   | LR: 0.007311 | E:  -60.760986 | E_var:     1.0521 | E_err:   0.011333
[2025-10-21 18:00:11] [Iter  393/2250] R1[242/300]   | LR: 0.007236 | E:  -60.743244 | E_var:     0.9350 | E_err:   0.010683
[2025-10-21 18:00:38] [Iter  394/2250] R1[243/300]   | LR: 0.007161 | E:  -60.765453 | E_var:     1.0358 | E_err:   0.011245
[2025-10-21 18:01:05] [Iter  395/2250] R1[244/300]   | LR: 0.007088 | E:  -60.763150 | E_var:     1.0180 | E_err:   0.011148
[2025-10-21 18:01:32] [Iter  396/2250] R1[245/300]   | LR: 0.007017 | E:  -60.750541 | E_var:     1.1173 | E_err:   0.011679
[2025-10-21 18:01:58] [Iter  397/2250] R1[246/300]   | LR: 0.006946 | E:  -60.761204 | E_var:     1.0834 | E_err:   0.011500
[2025-10-21 18:02:25] [Iter  398/2250] R1[247/300]   | LR: 0.006876 | E:  -60.751127 | E_var:     0.9099 | E_err:   0.010539
[2025-10-21 18:02:52] [Iter  399/2250] R1[248/300]   | LR: 0.006808 | E:  -60.757518 | E_var:     0.9977 | E_err:   0.011036
[2025-10-21 18:03:19] [Iter  400/2250] R1[249/300]   | LR: 0.006741 | E:  -60.751275 | E_var:     0.8637 | E_err:   0.010268
[2025-10-21 18:03:46] [Iter  401/2250] R1[250/300]   | LR: 0.006675 | E:  -60.766489 | E_var:     1.1002 | E_err:   0.011589
[2025-10-21 18:04:13] [Iter  402/2250] R1[251/300]   | LR: 0.006610 | E:  -60.774865 | E_var:     0.9322 | E_err:   0.010668
[2025-10-21 18:04:40] [Iter  403/2250] R1[252/300]   | LR: 0.006546 | E:  -60.767799 | E_var:     0.8961 | E_err:   0.010459
[2025-10-21 18:05:06] [Iter  404/2250] R1[253/300]   | LR: 0.006484 | E:  -60.764956 | E_var:     1.0599 | E_err:   0.011375
[2025-10-21 18:05:33] [Iter  405/2250] R1[254/300]   | LR: 0.006422 | E:  -60.770839 | E_var:     0.9467 | E_err:   0.010750
[2025-10-21 18:06:00] [Iter  406/2250] R1[255/300]   | LR: 0.006362 | E:  -60.760146 | E_var:     0.8957 | E_err:   0.010457
[2025-10-21 18:06:27] [Iter  407/2250] R1[256/300]   | LR: 0.006304 | E:  -60.758145 | E_var:     0.9004 | E_err:   0.010484
[2025-10-21 18:06:54] [Iter  408/2250] R1[257/300]   | LR: 0.006246 | E:  -60.751767 | E_var:     1.0144 | E_err:   0.011128
[2025-10-21 18:07:21] [Iter  409/2250] R1[258/300]   | LR: 0.006190 | E:  -60.761397 | E_var:     1.1849 | E_err:   0.012027
[2025-10-21 18:07:48] [Iter  410/2250] R1[259/300]   | LR: 0.006135 | E:  -60.774133 | E_var:     1.1528 | E_err:   0.011863
[2025-10-21 18:08:14] [Iter  411/2250] R1[260/300]   | LR: 0.006081 | E:  -60.781905 | E_var:     0.9860 | E_err:   0.010971
[2025-10-21 18:08:41] [Iter  412/2250] R1[261/300]   | LR: 0.006028 | E:  -60.779622 | E_var:     1.1763 | E_err:   0.011983
[2025-10-21 18:09:08] [Iter  413/2250] R1[262/300]   | LR: 0.005977 | E:  -60.771241 | E_var:     0.9596 | E_err:   0.010823
[2025-10-21 18:09:35] [Iter  414/2250] R1[263/300]   | LR: 0.005927 | E:  -60.753980 | E_var:     0.9268 | E_err:   0.010636
[2025-10-21 18:10:02] [Iter  415/2250] R1[264/300]   | LR: 0.005878 | E:  -60.760218 | E_var:     0.9360 | E_err:   0.010689
[2025-10-21 18:10:29] [Iter  416/2250] R1[265/300]   | LR: 0.005830 | E:  -60.759682 | E_var:     1.2921 | E_err:   0.012559
[2025-10-21 18:10:56] [Iter  417/2250] R1[266/300]   | LR: 0.005784 | E:  -60.767408 | E_var:     0.9592 | E_err:   0.010821
[2025-10-21 18:11:22] [Iter  418/2250] R1[267/300]   | LR: 0.005739 | E:  -60.759167 | E_var:     1.0186 | E_err:   0.011151
[2025-10-21 18:11:49] [Iter  419/2250] R1[268/300]   | LR: 0.005695 | E:  -60.769510 | E_var:     0.8915 | E_err:   0.010432
[2025-10-21 18:12:16] [Iter  420/2250] R1[269/300]   | LR: 0.005653 | E:  -60.768754 | E_var:     0.9745 | E_err:   0.010907
[2025-10-21 18:12:43] [Iter  421/2250] R1[270/300]   | LR: 0.005612 | E:  -60.769239 | E_var:     0.8740 | E_err:   0.010329
[2025-10-21 18:13:10] [Iter  422/2250] R1[271/300]   | LR: 0.005572 | E:  -60.751558 | E_var:     0.8900 | E_err:   0.010423
[2025-10-21 18:13:37] [Iter  423/2250] R1[272/300]   | LR: 0.005534 | E:  -60.784254 | E_var:     1.1991 | E_err:   0.012099
[2025-10-21 18:14:04] [Iter  424/2250] R1[273/300]   | LR: 0.005496 | E:  -60.772223 | E_var:     0.8683 | E_err:   0.010295
[2025-10-21 18:14:30] [Iter  425/2250] R1[274/300]   | LR: 0.005460 | E:  -60.761858 | E_var:     0.9892 | E_err:   0.010989
[2025-10-21 18:14:57] [Iter  426/2250] R1[275/300]   | LR: 0.005426 | E:  -60.788021 | E_var:     0.9420 | E_err:   0.010724
[2025-10-21 18:15:24] [Iter  427/2250] R1[276/300]   | LR: 0.005393 | E:  -60.766162 | E_var:     1.0104 | E_err:   0.011106
[2025-10-21 18:15:51] [Iter  428/2250] R1[277/300]   | LR: 0.005361 | E:  -60.777114 | E_var:     0.9054 | E_err:   0.010513
[2025-10-21 18:16:18] [Iter  429/2250] R1[278/300]   | LR: 0.005330 | E:  -60.763215 | E_var:     0.8943 | E_err:   0.010449
[2025-10-21 18:16:45] [Iter  430/2250] R1[279/300]   | LR: 0.005301 | E:  -60.766409 | E_var:     0.8646 | E_err:   0.010274
[2025-10-21 18:17:12] [Iter  431/2250] R1[280/300]   | LR: 0.005273 | E:  -60.767698 | E_var:     0.9731 | E_err:   0.010899
[2025-10-21 18:17:38] [Iter  432/2250] R1[281/300]   | LR: 0.005247 | E:  -60.774473 | E_var:     0.9598 | E_err:   0.010824
[2025-10-21 18:18:05] [Iter  433/2250] R1[282/300]   | LR: 0.005221 | E:  -60.781111 | E_var:     1.0314 | E_err:   0.011221
[2025-10-21 18:18:32] [Iter  434/2250] R1[283/300]   | LR: 0.005198 | E:  -60.774754 | E_var:     0.9586 | E_err:   0.010817
[2025-10-21 18:18:59] [Iter  435/2250] R1[284/300]   | LR: 0.005175 | E:  -60.772538 | E_var:     0.9084 | E_err:   0.010530
[2025-10-21 18:19:26] [Iter  436/2250] R1[285/300]   | LR: 0.005154 | E:  -60.792030 | E_var:     0.8974 | E_err:   0.010467
[2025-10-21 18:19:53] [Iter  437/2250] R1[286/300]   | LR: 0.005134 | E:  -60.804476 | E_var:     0.9868 | E_err:   0.010976
[2025-10-21 18:20:20] [Iter  438/2250] R1[287/300]   | LR: 0.005116 | E:  -60.774558 | E_var:     0.8903 | E_err:   0.010425
[2025-10-21 18:20:46] [Iter  439/2250] R1[288/300]   | LR: 0.005099 | E:  -60.794567 | E_var:     0.9883 | E_err:   0.010983
[2025-10-21 18:21:13] [Iter  440/2250] R1[289/300]   | LR: 0.005083 | E:  -60.791863 | E_var:     1.1481 | E_err:   0.011838
[2025-10-21 18:21:40] [Iter  441/2250] R1[290/300]   | LR: 0.005068 | E:  -60.776736 | E_var:     1.2048 | E_err:   0.012127
[2025-10-21 18:22:07] [Iter  442/2250] R1[291/300]   | LR: 0.005055 | E:  -60.760162 | E_var:     1.0528 | E_err:   0.011337
[2025-10-21 18:22:34] [Iter  443/2250] R1[292/300]   | LR: 0.005044 | E:  -60.785305 | E_var:     0.9090 | E_err:   0.010534
[2025-10-21 18:23:01] [Iter  444/2250] R1[293/300]   | LR: 0.005034 | E:  -60.794324 | E_var:     0.9784 | E_err:   0.010929
[2025-10-21 18:23:28] [Iter  445/2250] R1[294/300]   | LR: 0.005025 | E:  -60.797477 | E_var:     0.9663 | E_err:   0.010861
[2025-10-21 18:23:54] [Iter  446/2250] R1[295/300]   | LR: 0.005017 | E:  -60.788477 | E_var:     0.9558 | E_err:   0.010801
[2025-10-21 18:24:21] [Iter  447/2250] R1[296/300]   | LR: 0.005011 | E:  -60.787636 | E_var:     0.9639 | E_err:   0.010848
[2025-10-21 18:24:48] [Iter  448/2250] R1[297/300]   | LR: 0.005006 | E:  -60.803289 | E_var:     0.9309 | E_err:   0.010660
[2025-10-21 18:25:15] [Iter  449/2250] R1[298/300]   | LR: 0.005003 | E:  -60.795384 | E_var:     0.8957 | E_err:   0.010457
[2025-10-21 18:25:42] [Iter  450/2250] R1[299/300]   | LR: 0.005001 | E:  -60.789341 | E_var:     1.0911 | E_err:   0.011541
[2025-10-21 18:25:42] ✓ Checkpoint saved: checkpoint_iter_000450.pkl
[2025-10-21 18:25:42] 🔄 RESTART #2 | Period: 600
[2025-10-21 18:26:09] [Iter  451/2250] R2[0/600]     | LR: 0.030000 | E:  -60.799951 | E_var:     0.8867 | E_err:   0.010404
[2025-10-21 18:26:36] [Iter  452/2250] R2[1/600]     | LR: 0.030000 | E:  -60.797350 | E_var:     0.9462 | E_err:   0.010747
[2025-10-21 18:27:02] [Iter  453/2250] R2[2/600]     | LR: 0.029999 | E:  -60.781774 | E_var:     0.9730 | E_err:   0.010898
[2025-10-21 18:27:29] [Iter  454/2250] R2[3/600]     | LR: 0.029998 | E:  -60.771904 | E_var:     0.8631 | E_err:   0.010264
[2025-10-21 18:27:56] [Iter  455/2250] R2[4/600]     | LR: 0.029997 | E:  -60.795684 | E_var:     0.9457 | E_err:   0.010744
[2025-10-21 18:28:23] [Iter  456/2250] R2[5/600]     | LR: 0.029996 | E:  -60.780308 | E_var:     0.9138 | E_err:   0.010562
[2025-10-21 18:28:50] [Iter  457/2250] R2[6/600]     | LR: 0.029994 | E:  -60.773759 | E_var:     0.8825 | E_err:   0.010379
[2025-10-21 18:29:17] [Iter  458/2250] R2[7/600]     | LR: 0.029992 | E:  -60.785529 | E_var:     0.8637 | E_err:   0.010268
[2025-10-21 18:29:44] [Iter  459/2250] R2[8/600]     | LR: 0.029989 | E:  -60.791833 | E_var:     0.9343 | E_err:   0.010679
[2025-10-21 18:30:11] [Iter  460/2250] R2[9/600]     | LR: 0.029986 | E:  -60.792629 | E_var:     0.9968 | E_err:   0.011031
[2025-10-21 18:30:37] [Iter  461/2250] R2[10/600]    | LR: 0.029983 | E:  -60.785286 | E_var:     0.8540 | E_err:   0.010210
[2025-10-21 18:31:04] [Iter  462/2250] R2[11/600]    | LR: 0.029979 | E:  -60.786950 | E_var:     0.8674 | E_err:   0.010290
[2025-10-21 18:31:31] [Iter  463/2250] R2[12/600]    | LR: 0.029975 | E:  -60.790764 | E_var:     0.8339 | E_err:   0.010089
[2025-10-21 18:31:58] [Iter  464/2250] R2[13/600]    | LR: 0.029971 | E:  -60.794833 | E_var:     0.9850 | E_err:   0.010965
[2025-10-21 18:32:25] [Iter  465/2250] R2[14/600]    | LR: 0.029966 | E:  -60.784298 | E_var:     1.1469 | E_err:   0.011832
[2025-10-21 18:32:52] [Iter  466/2250] R2[15/600]    | LR: 0.029961 | E:  -60.796263 | E_var:     0.9023 | E_err:   0.010495
[2025-10-21 18:33:18] [Iter  467/2250] R2[16/600]    | LR: 0.029956 | E:  -60.795485 | E_var:     1.1187 | E_err:   0.011686
[2025-10-21 18:33:45] [Iter  468/2250] R2[17/600]    | LR: 0.029951 | E:  -60.786538 | E_var:     1.0619 | E_err:   0.011385
[2025-10-21 18:34:12] [Iter  469/2250] R2[18/600]    | LR: 0.029945 | E:  -60.805211 | E_var:     0.8960 | E_err:   0.010458
[2025-10-21 18:34:39] [Iter  470/2250] R2[19/600]    | LR: 0.029938 | E:  -60.782718 | E_var:     1.0451 | E_err:   0.011295
[2025-10-21 18:35:06] [Iter  471/2250] R2[20/600]    | LR: 0.029932 | E:  -60.789633 | E_var:     0.9738 | E_err:   0.010903
[2025-10-21 18:35:33] [Iter  472/2250] R2[21/600]    | LR: 0.029925 | E:  -60.783574 | E_var:     0.9226 | E_err:   0.010612
[2025-10-21 18:36:00] [Iter  473/2250] R2[22/600]    | LR: 0.029917 | E:  -60.797211 | E_var:     0.9031 | E_err:   0.010499
[2025-10-21 18:36:26] [Iter  474/2250] R2[23/600]    | LR: 0.029909 | E:  -60.810167 | E_var:     0.8947 | E_err:   0.010451
[2025-10-21 18:36:53] [Iter  475/2250] R2[24/600]    | LR: 0.029901 | E:  -60.798678 | E_var:     0.9952 | E_err:   0.011022
[2025-10-21 18:37:20] [Iter  476/2250] R2[25/600]    | LR: 0.029893 | E:  -60.789674 | E_var:     1.1643 | E_err:   0.011922
[2025-10-21 18:37:47] [Iter  477/2250] R2[26/600]    | LR: 0.029884 | E:  -60.803682 | E_var:     0.8504 | E_err:   0.010189
[2025-10-21 18:38:14] [Iter  478/2250] R2[27/600]    | LR: 0.029875 | E:  -60.794512 | E_var:     0.9150 | E_err:   0.010569
[2025-10-21 18:38:41] [Iter  479/2250] R2[28/600]    | LR: 0.029866 | E:  -60.810828 | E_var:     0.8538 | E_err:   0.010209
[2025-10-21 18:39:08] [Iter  480/2250] R2[29/600]    | LR: 0.029856 | E:  -60.799875 | E_var:     0.9180 | E_err:   0.010586
[2025-10-21 18:39:34] [Iter  481/2250] R2[30/600]    | LR: 0.029846 | E:  -60.803733 | E_var:     0.9441 | E_err:   0.010735
[2025-10-21 18:40:01] [Iter  482/2250] R2[31/600]    | LR: 0.029836 | E:  -60.786550 | E_var:     0.8719 | E_err:   0.010316
[2025-10-21 18:40:28] [Iter  483/2250] R2[32/600]    | LR: 0.029825 | E:  -60.795676 | E_var:     1.0596 | E_err:   0.011373
[2025-10-21 18:40:55] [Iter  484/2250] R2[33/600]    | LR: 0.029814 | E:  -60.803018 | E_var:     1.0349 | E_err:   0.011240
[2025-10-21 18:41:22] [Iter  485/2250] R2[34/600]    | LR: 0.029802 | E:  -60.809569 | E_var:     1.0140 | E_err:   0.011126
[2025-10-21 18:41:49] [Iter  486/2250] R2[35/600]    | LR: 0.029791 | E:  -60.798474 | E_var:     0.8095 | E_err:   0.009941
[2025-10-21 18:42:15] [Iter  487/2250] R2[36/600]    | LR: 0.029779 | E:  -60.797431 | E_var:     0.8178 | E_err:   0.009991
[2025-10-21 18:42:42] [Iter  488/2250] R2[37/600]    | LR: 0.029766 | E:  -60.795971 | E_var:     0.8772 | E_err:   0.010348
[2025-10-21 18:43:09] [Iter  489/2250] R2[38/600]    | LR: 0.029753 | E:  -60.782953 | E_var:     0.8955 | E_err:   0.010455
[2025-10-21 18:43:36] [Iter  490/2250] R2[39/600]    | LR: 0.029740 | E:  -60.796798 | E_var:     0.8948 | E_err:   0.010451
[2025-10-21 18:44:03] [Iter  491/2250] R2[40/600]    | LR: 0.029727 | E:  -60.805391 | E_var:     0.9057 | E_err:   0.010515
[2025-10-21 18:44:30] [Iter  492/2250] R2[41/600]    | LR: 0.029713 | E:  -60.790863 | E_var:     1.0346 | E_err:   0.011238
[2025-10-21 18:44:57] [Iter  493/2250] R2[42/600]    | LR: 0.029699 | E:  -60.789020 | E_var:     0.8631 | E_err:   0.010265
[2025-10-21 18:45:24] [Iter  494/2250] R2[43/600]    | LR: 0.029685 | E:  -60.814236 | E_var:     1.0493 | E_err:   0.011317
[2025-10-21 18:45:50] [Iter  495/2250] R2[44/600]    | LR: 0.029670 | E:  -60.808021 | E_var:     1.0410 | E_err:   0.011272
[2025-10-21 18:46:17] [Iter  496/2250] R2[45/600]    | LR: 0.029655 | E:  -60.809574 | E_var:     0.8962 | E_err:   0.010460
[2025-10-21 18:46:44] [Iter  497/2250] R2[46/600]    | LR: 0.029639 | E:  -60.802725 | E_var:     0.9288 | E_err:   0.010648
[2025-10-21 18:47:11] [Iter  498/2250] R2[47/600]    | LR: 0.029623 | E:  -60.813536 | E_var:     0.8833 | E_err:   0.010384
[2025-10-21 18:47:38] [Iter  499/2250] R2[48/600]    | LR: 0.029607 | E:  -60.815104 | E_var:     0.8820 | E_err:   0.010376
[2025-10-21 18:48:05] [Iter  500/2250] R2[49/600]    | LR: 0.029591 | E:  -60.825348 | E_var:     0.8988 | E_err:   0.010475
[2025-10-21 18:48:31] [Iter  501/2250] R2[50/600]    | LR: 0.029574 | E:  -60.798700 | E_var:     0.8363 | E_err:   0.010104
[2025-10-21 18:48:58] [Iter  502/2250] R2[51/600]    | LR: 0.029557 | E:  -60.817286 | E_var:     0.8650 | E_err:   0.010276
[2025-10-21 18:49:25] [Iter  503/2250] R2[52/600]    | LR: 0.029540 | E:  -60.813612 | E_var:     0.8820 | E_err:   0.010376
[2025-10-21 18:49:52] [Iter  504/2250] R2[53/600]    | LR: 0.029522 | E:  -60.809176 | E_var:     0.9265 | E_err:   0.010635
[2025-10-21 18:50:19] [Iter  505/2250] R2[54/600]    | LR: 0.029504 | E:  -60.798171 | E_var:     0.9318 | E_err:   0.010665
[2025-10-21 18:50:46] [Iter  506/2250] R2[55/600]    | LR: 0.029485 | E:  -60.809252 | E_var:     1.0885 | E_err:   0.011527
[2025-10-21 18:51:13] [Iter  507/2250] R2[56/600]    | LR: 0.029466 | E:  -60.808658 | E_var:     0.8422 | E_err:   0.010140
[2025-10-21 18:51:39] [Iter  508/2250] R2[57/600]    | LR: 0.029447 | E:  -60.790496 | E_var:     0.8529 | E_err:   0.010204
[2025-10-21 18:52:06] [Iter  509/2250] R2[58/600]    | LR: 0.029428 | E:  -60.818584 | E_var:     0.8692 | E_err:   0.010301
[2025-10-21 18:52:33] [Iter  510/2250] R2[59/600]    | LR: 0.029408 | E:  -60.809641 | E_var:     0.8700 | E_err:   0.010306
[2025-10-21 18:53:00] [Iter  511/2250] R2[60/600]    | LR: 0.029388 | E:  -60.789299 | E_var:     0.8235 | E_err:   0.010026
[2025-10-21 18:53:27] [Iter  512/2250] R2[61/600]    | LR: 0.029368 | E:  -60.809433 | E_var:     0.9386 | E_err:   0.010704
[2025-10-21 18:53:54] [Iter  513/2250] R2[62/600]    | LR: 0.029347 | E:  -60.791740 | E_var:     0.8455 | E_err:   0.010159
[2025-10-21 18:54:20] [Iter  514/2250] R2[63/600]    | LR: 0.029326 | E:  -60.798320 | E_var:     0.9100 | E_err:   0.010540
[2025-10-21 18:54:47] [Iter  515/2250] R2[64/600]    | LR: 0.029305 | E:  -60.801982 | E_var:     0.9123 | E_err:   0.010553
[2025-10-21 18:55:14] [Iter  516/2250] R2[65/600]    | LR: 0.029283 | E:  -60.819126 | E_var:     0.9427 | E_err:   0.010728
[2025-10-21 18:55:41] [Iter  517/2250] R2[66/600]    | LR: 0.029261 | E:  -60.797284 | E_var:     0.8874 | E_err:   0.010408
[2025-10-21 18:56:08] [Iter  518/2250] R2[67/600]    | LR: 0.029239 | E:  -60.820950 | E_var:     0.9444 | E_err:   0.010737
[2025-10-21 18:56:35] [Iter  519/2250] R2[68/600]    | LR: 0.029216 | E:  -60.789232 | E_var:     1.0262 | E_err:   0.011192
[2025-10-21 18:57:02] [Iter  520/2250] R2[69/600]    | LR: 0.029193 | E:  -60.802056 | E_var:     0.9031 | E_err:   0.010500
[2025-10-21 18:57:28] [Iter  521/2250] R2[70/600]    | LR: 0.029170 | E:  -60.805429 | E_var:     0.9483 | E_err:   0.010759
[2025-10-21 18:57:55] [Iter  522/2250] R2[71/600]    | LR: 0.029146 | E:  -60.820852 | E_var:     0.7811 | E_err:   0.009765
[2025-10-21 18:58:22] [Iter  523/2250] R2[72/600]    | LR: 0.029122 | E:  -60.823160 | E_var:     0.9604 | E_err:   0.010828
[2025-10-21 18:58:49] [Iter  524/2250] R2[73/600]    | LR: 0.029098 | E:  -60.801255 | E_var:     0.7966 | E_err:   0.009861
[2025-10-21 18:59:16] [Iter  525/2250] R2[74/600]    | LR: 0.029073 | E:  -60.804740 | E_var:     0.9639 | E_err:   0.010847
[2025-10-21 18:59:43] [Iter  526/2250] R2[75/600]    | LR: 0.029048 | E:  -60.792966 | E_var:     0.9083 | E_err:   0.010530
[2025-10-21 19:00:10] [Iter  527/2250] R2[76/600]    | LR: 0.029023 | E:  -60.814348 | E_var:     0.9165 | E_err:   0.010577
[2025-10-21 19:00:36] [Iter  528/2250] R2[77/600]    | LR: 0.028998 | E:  -60.812553 | E_var:     0.7839 | E_err:   0.009782
[2025-10-21 19:01:03] [Iter  529/2250] R2[78/600]    | LR: 0.028972 | E:  -60.811237 | E_var:     0.8833 | E_err:   0.010384
[2025-10-21 19:01:30] [Iter  530/2250] R2[79/600]    | LR: 0.028946 | E:  -60.792286 | E_var:     0.7613 | E_err:   0.009640
[2025-10-21 19:01:57] [Iter  531/2250] R2[80/600]    | LR: 0.028919 | E:  -60.798375 | E_var:     0.9898 | E_err:   0.010992
[2025-10-21 19:02:24] [Iter  532/2250] R2[81/600]    | LR: 0.028893 | E:  -60.816012 | E_var:     0.9012 | E_err:   0.010489
[2025-10-21 19:02:51] [Iter  533/2250] R2[82/600]    | LR: 0.028865 | E:  -60.808717 | E_var:     0.8478 | E_err:   0.010173
[2025-10-21 19:03:17] [Iter  534/2250] R2[83/600]    | LR: 0.028838 | E:  -60.798791 | E_var:     0.8978 | E_err:   0.010469
[2025-10-21 19:03:44] [Iter  535/2250] R2[84/600]    | LR: 0.028810 | E:  -60.815047 | E_var:     0.9365 | E_err:   0.010692
[2025-10-21 19:04:11] [Iter  536/2250] R2[85/600]    | LR: 0.028782 | E:  -60.825860 | E_var:     0.9923 | E_err:   0.011006
[2025-10-21 19:04:38] [Iter  537/2250] R2[86/600]    | LR: 0.028754 | E:  -60.835393 | E_var:     0.8563 | E_err:   0.010224
[2025-10-21 19:05:05] [Iter  538/2250] R2[87/600]    | LR: 0.028725 | E:  -60.813257 | E_var:     0.8139 | E_err:   0.009967
[2025-10-21 19:05:32] [Iter  539/2250] R2[88/600]    | LR: 0.028696 | E:  -60.826988 | E_var:     0.9564 | E_err:   0.010805
[2025-10-21 19:05:59] [Iter  540/2250] R2[89/600]    | LR: 0.028667 | E:  -60.810421 | E_var:     0.8641 | E_err:   0.010270
[2025-10-21 19:06:25] [Iter  541/2250] R2[90/600]    | LR: 0.028638 | E:  -60.823015 | E_var:     0.8693 | E_err:   0.010301
[2025-10-21 19:06:52] [Iter  542/2250] R2[91/600]    | LR: 0.028608 | E:  -60.796404 | E_var:     0.9145 | E_err:   0.010566
[2025-10-21 19:07:19] [Iter  543/2250] R2[92/600]    | LR: 0.028578 | E:  -60.813619 | E_var:     0.8241 | E_err:   0.010030
[2025-10-21 19:07:46] [Iter  544/2250] R2[93/600]    | LR: 0.028547 | E:  -60.828737 | E_var:     0.8285 | E_err:   0.010057
[2025-10-21 19:08:13] [Iter  545/2250] R2[94/600]    | LR: 0.028516 | E:  -60.805311 | E_var:     0.9018 | E_err:   0.010492
[2025-10-21 19:08:40] [Iter  546/2250] R2[95/600]    | LR: 0.028485 | E:  -60.824112 | E_var:     0.9880 | E_err:   0.010982
[2025-10-21 19:09:07] [Iter  547/2250] R2[96/600]    | LR: 0.028454 | E:  -60.810832 | E_var:     0.9017 | E_err:   0.010491
[2025-10-21 19:09:33] [Iter  548/2250] R2[97/600]    | LR: 0.028422 | E:  -60.810302 | E_var:     0.8646 | E_err:   0.010273
[2025-10-21 19:10:00] [Iter  549/2250] R2[98/600]    | LR: 0.028390 | E:  -60.834775 | E_var:     0.9669 | E_err:   0.010864
[2025-10-21 19:10:27] [Iter  550/2250] R2[99/600]    | LR: 0.028358 | E:  -60.828095 | E_var:     0.9699 | E_err:   0.010881
[2025-10-21 19:10:54] [Iter  551/2250] R2[100/600]   | LR: 0.028325 | E:  -60.806997 | E_var:     0.7878 | E_err:   0.009807
[2025-10-21 19:11:21] [Iter  552/2250] R2[101/600]   | LR: 0.028292 | E:  -60.808296 | E_var:     0.8706 | E_err:   0.010309
[2025-10-21 19:11:48] [Iter  553/2250] R2[102/600]   | LR: 0.028259 | E:  -60.829201 | E_var:     0.8064 | E_err:   0.009922
[2025-10-21 19:12:14] [Iter  554/2250] R2[103/600]   | LR: 0.028226 | E:  -60.816685 | E_var:     0.9492 | E_err:   0.010764
[2025-10-21 19:12:41] [Iter  555/2250] R2[104/600]   | LR: 0.028192 | E:  -60.823419 | E_var:     0.9084 | E_err:   0.010530
[2025-10-21 19:13:08] [Iter  556/2250] R2[105/600]   | LR: 0.028158 | E:  -60.813773 | E_var:     0.9694 | E_err:   0.010878
[2025-10-21 19:13:35] [Iter  557/2250] R2[106/600]   | LR: 0.028124 | E:  -60.805940 | E_var:     0.9851 | E_err:   0.010966
[2025-10-21 19:14:02] [Iter  558/2250] R2[107/600]   | LR: 0.028089 | E:  -60.828710 | E_var:     0.8016 | E_err:   0.009892
[2025-10-21 19:14:29] [Iter  559/2250] R2[108/600]   | LR: 0.028054 | E:  -60.815714 | E_var:     0.8288 | E_err:   0.010059
[2025-10-21 19:14:56] [Iter  560/2250] R2[109/600]   | LR: 0.028019 | E:  -60.820535 | E_var:     0.8557 | E_err:   0.010220
[2025-10-21 19:15:22] [Iter  561/2250] R2[110/600]   | LR: 0.027983 | E:  -60.809975 | E_var:     0.8073 | E_err:   0.009927
[2025-10-21 19:15:49] [Iter  562/2250] R2[111/600]   | LR: 0.027948 | E:  -60.818611 | E_var:     0.8965 | E_err:   0.010461
[2025-10-21 19:16:16] [Iter  563/2250] R2[112/600]   | LR: 0.027912 | E:  -60.813397 | E_var:     0.8053 | E_err:   0.009915
[2025-10-21 19:16:43] [Iter  564/2250] R2[113/600]   | LR: 0.027875 | E:  -60.820800 | E_var:     0.8868 | E_err:   0.010405
[2025-10-21 19:17:10] [Iter  565/2250] R2[114/600]   | LR: 0.027839 | E:  -60.816873 | E_var:     0.8603 | E_err:   0.010248
[2025-10-21 19:17:37] [Iter  566/2250] R2[115/600]   | LR: 0.027802 | E:  -60.799859 | E_var:     1.0007 | E_err:   0.011053
[2025-10-21 19:18:03] [Iter  567/2250] R2[116/600]   | LR: 0.027764 | E:  -60.804284 | E_var:     0.9175 | E_err:   0.010583
[2025-10-21 19:18:30] [Iter  568/2250] R2[117/600]   | LR: 0.027727 | E:  -60.819187 | E_var:     0.7933 | E_err:   0.009841
[2025-10-21 19:18:57] [Iter  569/2250] R2[118/600]   | LR: 0.027689 | E:  -60.828690 | E_var:     0.9659 | E_err:   0.010859
[2025-10-21 19:19:24] [Iter  570/2250] R2[119/600]   | LR: 0.027651 | E:  -60.827132 | E_var:     0.8482 | E_err:   0.010175
[2025-10-21 19:19:51] [Iter  571/2250] R2[120/600]   | LR: 0.027613 | E:  -60.824755 | E_var:     0.8597 | E_err:   0.010244
[2025-10-21 19:20:18] [Iter  572/2250] R2[121/600]   | LR: 0.027574 | E:  -60.815520 | E_var:     1.0590 | E_err:   0.011370
[2025-10-21 19:20:44] [Iter  573/2250] R2[122/600]   | LR: 0.027535 | E:  -60.822091 | E_var:     0.8475 | E_err:   0.010172
[2025-10-21 19:21:11] [Iter  574/2250] R2[123/600]   | LR: 0.027496 | E:  -60.810149 | E_var:     0.9523 | E_err:   0.010782
[2025-10-21 19:21:38] [Iter  575/2250] R2[124/600]   | LR: 0.027457 | E:  -60.836169 | E_var:     0.8991 | E_err:   0.010476
[2025-10-21 19:22:05] [Iter  576/2250] R2[125/600]   | LR: 0.027417 | E:  -60.833199 | E_var:     0.7298 | E_err:   0.009438
[2025-10-21 19:22:32] [Iter  577/2250] R2[126/600]   | LR: 0.027377 | E:  -60.835988 | E_var:     0.7738 | E_err:   0.009719
[2025-10-21 19:22:59] [Iter  578/2250] R2[127/600]   | LR: 0.027337 | E:  -60.821210 | E_var:     0.7848 | E_err:   0.009787
[2025-10-21 19:23:26] [Iter  579/2250] R2[128/600]   | LR: 0.027296 | E:  -60.812814 | E_var:     0.9208 | E_err:   0.010602
[2025-10-21 19:23:52] [Iter  580/2250] R2[129/600]   | LR: 0.027255 | E:  -60.822030 | E_var:     0.9238 | E_err:   0.010619
[2025-10-21 19:24:19] [Iter  581/2250] R2[130/600]   | LR: 0.027214 | E:  -60.828124 | E_var:     0.8115 | E_err:   0.009953
[2025-10-21 19:24:46] [Iter  582/2250] R2[131/600]   | LR: 0.027173 | E:  -60.829768 | E_var:     1.1103 | E_err:   0.011642
[2025-10-21 19:25:13] [Iter  583/2250] R2[132/600]   | LR: 0.027131 | E:  -60.832385 | E_var:     0.8918 | E_err:   0.010434
[2025-10-21 19:25:40] [Iter  584/2250] R2[133/600]   | LR: 0.027090 | E:  -60.821348 | E_var:     0.7354 | E_err:   0.009475
[2025-10-21 19:26:07] [Iter  585/2250] R2[134/600]   | LR: 0.027047 | E:  -60.828168 | E_var:     0.9090 | E_err:   0.010534
[2025-10-21 19:26:33] [Iter  586/2250] R2[135/600]   | LR: 0.027005 | E:  -60.837787 | E_var:     0.7762 | E_err:   0.009734
[2025-10-21 19:27:00] [Iter  587/2250] R2[136/600]   | LR: 0.026962 | E:  -60.808609 | E_var:     0.7506 | E_err:   0.009572
[2025-10-21 19:27:27] [Iter  588/2250] R2[137/600]   | LR: 0.026920 | E:  -60.820232 | E_var:     0.7938 | E_err:   0.009844
[2025-10-21 19:27:54] [Iter  589/2250] R2[138/600]   | LR: 0.026876 | E:  -60.813760 | E_var:     0.8519 | E_err:   0.010198
[2025-10-21 19:28:21] [Iter  590/2250] R2[139/600]   | LR: 0.026833 | E:  -60.819662 | E_var:     0.8418 | E_err:   0.010137
[2025-10-21 19:28:48] [Iter  591/2250] R2[140/600]   | LR: 0.026789 | E:  -60.827356 | E_var:     0.7367 | E_err:   0.009483
[2025-10-21 19:29:15] [Iter  592/2250] R2[141/600]   | LR: 0.026745 | E:  -60.827457 | E_var:     0.9637 | E_err:   0.010846
[2025-10-21 19:29:41] [Iter  593/2250] R2[142/600]   | LR: 0.026701 | E:  -60.815056 | E_var:     0.8078 | E_err:   0.009930
[2025-10-21 19:30:08] [Iter  594/2250] R2[143/600]   | LR: 0.026657 | E:  -60.826393 | E_var:     0.8194 | E_err:   0.010001
[2025-10-21 19:30:35] [Iter  595/2250] R2[144/600]   | LR: 0.026612 | E:  -60.833054 | E_var:     0.7286 | E_err:   0.009431
[2025-10-21 19:31:02] [Iter  596/2250] R2[145/600]   | LR: 0.026567 | E:  -60.821575 | E_var:     0.7381 | E_err:   0.009492
[2025-10-21 19:31:29] [Iter  597/2250] R2[146/600]   | LR: 0.026522 | E:  -60.819467 | E_var:     0.7999 | E_err:   0.009881
[2025-10-21 19:31:56] [Iter  598/2250] R2[147/600]   | LR: 0.026477 | E:  -60.826559 | E_var:     0.7933 | E_err:   0.009840
[2025-10-21 19:32:22] [Iter  599/2250] R2[148/600]   | LR: 0.026431 | E:  -60.831216 | E_var:     0.8716 | E_err:   0.010315
[2025-10-21 19:32:49] [Iter  600/2250] R2[149/600]   | LR: 0.026385 | E:  -60.835273 | E_var:     0.8218 | E_err:   0.010016
[2025-10-21 19:33:16] [Iter  601/2250] R2[150/600]   | LR: 0.026339 | E:  -60.826357 | E_var:     0.9723 | E_err:   0.010895
[2025-10-21 19:33:43] [Iter  602/2250] R2[151/600]   | LR: 0.026292 | E:  -60.822949 | E_var:     0.8036 | E_err:   0.009904
[2025-10-21 19:34:10] [Iter  603/2250] R2[152/600]   | LR: 0.026246 | E:  -60.818702 | E_var:     0.8929 | E_err:   0.010440
[2025-10-21 19:34:37] [Iter  604/2250] R2[153/600]   | LR: 0.026199 | E:  -60.834044 | E_var:     0.8385 | E_err:   0.010117
[2025-10-21 19:35:04] [Iter  605/2250] R2[154/600]   | LR: 0.026152 | E:  -60.816081 | E_var:     2.0633 | E_err:   0.015871
[2025-10-21 19:35:30] [Iter  606/2250] R2[155/600]   | LR: 0.026104 | E:  -60.832619 | E_var:     0.8008 | E_err:   0.009887
[2025-10-21 19:35:57] [Iter  607/2250] R2[156/600]   | LR: 0.026057 | E:  -60.819551 | E_var:     0.8460 | E_err:   0.010162
[2025-10-21 19:36:24] [Iter  608/2250] R2[157/600]   | LR: 0.026009 | E:  -60.829376 | E_var:     0.8085 | E_err:   0.009934
[2025-10-21 19:36:51] [Iter  609/2250] R2[158/600]   | LR: 0.025961 | E:  -60.819997 | E_var:     0.8236 | E_err:   0.010027
[2025-10-21 19:37:18] [Iter  610/2250] R2[159/600]   | LR: 0.025913 | E:  -60.825188 | E_var:     0.8114 | E_err:   0.009952
[2025-10-21 19:37:45] [Iter  611/2250] R2[160/600]   | LR: 0.025864 | E:  -60.829011 | E_var:     0.8235 | E_err:   0.010026
[2025-10-21 19:38:11] [Iter  612/2250] R2[161/600]   | LR: 0.025815 | E:  -60.845691 | E_var:     0.7180 | E_err:   0.009362
[2025-10-21 19:38:38] [Iter  613/2250] R2[162/600]   | LR: 0.025766 | E:  -60.830986 | E_var:     0.8940 | E_err:   0.010447
[2025-10-21 19:39:05] [Iter  614/2250] R2[163/600]   | LR: 0.025717 | E:  -60.818180 | E_var:     0.8977 | E_err:   0.010468
[2025-10-21 19:39:32] [Iter  615/2250] R2[164/600]   | LR: 0.025668 | E:  -60.828282 | E_var:     0.8783 | E_err:   0.010354
[2025-10-21 19:39:59] [Iter  616/2250] R2[165/600]   | LR: 0.025618 | E:  -60.813120 | E_var:     0.8696 | E_err:   0.010303
[2025-10-21 19:40:26] [Iter  617/2250] R2[166/600]   | LR: 0.025568 | E:  -60.827054 | E_var:     0.7671 | E_err:   0.009677
[2025-10-21 19:40:53] [Iter  618/2250] R2[167/600]   | LR: 0.025518 | E:  -60.840133 | E_var:     0.7596 | E_err:   0.009630
[2025-10-21 19:41:19] [Iter  619/2250] R2[168/600]   | LR: 0.025468 | E:  -60.823367 | E_var:     0.8442 | E_err:   0.010152
[2025-10-21 19:41:46] [Iter  620/2250] R2[169/600]   | LR: 0.025417 | E:  -60.834969 | E_var:     0.7410 | E_err:   0.009511
[2025-10-21 19:42:13] [Iter  621/2250] R2[170/600]   | LR: 0.025367 | E:  -60.817736 | E_var:     0.8035 | E_err:   0.009904
[2025-10-21 19:42:40] [Iter  622/2250] R2[171/600]   | LR: 0.025316 | E:  -60.832857 | E_var:     0.8218 | E_err:   0.010016
[2025-10-21 19:43:07] [Iter  623/2250] R2[172/600]   | LR: 0.025264 | E:  -60.836739 | E_var:     0.8076 | E_err:   0.009929
[2025-10-21 19:43:34] [Iter  624/2250] R2[173/600]   | LR: 0.025213 | E:  -60.831996 | E_var:     0.7789 | E_err:   0.009751
[2025-10-21 19:44:00] [Iter  625/2250] R2[174/600]   | LR: 0.025161 | E:  -60.846641 | E_var:     0.7689 | E_err:   0.009688
[2025-10-21 19:44:27] [Iter  626/2250] R2[175/600]   | LR: 0.025110 | E:  -60.842148 | E_var:     0.7570 | E_err:   0.009613
[2025-10-21 19:44:54] [Iter  627/2250] R2[176/600]   | LR: 0.025057 | E:  -60.838236 | E_var:     0.7975 | E_err:   0.009867
[2025-10-21 19:45:21] [Iter  628/2250] R2[177/600]   | LR: 0.025005 | E:  -60.856855 | E_var:     0.7636 | E_err:   0.009655
[2025-10-21 19:45:48] [Iter  629/2250] R2[178/600]   | LR: 0.024953 | E:  -60.834852 | E_var:     0.8040 | E_err:   0.009907
[2025-10-21 19:46:15] [Iter  630/2250] R2[179/600]   | LR: 0.024900 | E:  -60.838594 | E_var:     0.7631 | E_err:   0.009651
[2025-10-21 19:46:42] [Iter  631/2250] R2[180/600]   | LR: 0.024847 | E:  -60.830988 | E_var:     0.6967 | E_err:   0.009222
[2025-10-21 19:47:08] [Iter  632/2250] R2[181/600]   | LR: 0.024794 | E:  -60.846513 | E_var:     0.7169 | E_err:   0.009355
[2025-10-21 19:47:35] [Iter  633/2250] R2[182/600]   | LR: 0.024741 | E:  -60.848754 | E_var:     0.8252 | E_err:   0.010036
[2025-10-21 19:48:02] [Iter  634/2250] R2[183/600]   | LR: 0.024688 | E:  -60.828378 | E_var:     0.8047 | E_err:   0.009911
[2025-10-21 19:48:29] [Iter  635/2250] R2[184/600]   | LR: 0.024634 | E:  -60.839083 | E_var:     0.7611 | E_err:   0.009639
[2025-10-21 19:48:56] [Iter  636/2250] R2[185/600]   | LR: 0.024580 | E:  -60.835910 | E_var:     0.8768 | E_err:   0.010345
[2025-10-21 19:49:23] [Iter  637/2250] R2[186/600]   | LR: 0.024526 | E:  -60.838579 | E_var:     0.7976 | E_err:   0.009868
[2025-10-21 19:49:49] [Iter  638/2250] R2[187/600]   | LR: 0.024472 | E:  -60.828503 | E_var:     0.8063 | E_err:   0.009921
[2025-10-21 19:50:16] [Iter  639/2250] R2[188/600]   | LR: 0.024417 | E:  -60.847160 | E_var:     0.7474 | E_err:   0.009551
[2025-10-21 19:50:43] [Iter  640/2250] R2[189/600]   | LR: 0.024363 | E:  -60.822352 | E_var:     0.8520 | E_err:   0.010198
[2025-10-21 19:51:10] [Iter  641/2250] R2[190/600]   | LR: 0.024308 | E:  -60.843441 | E_var:     0.7801 | E_err:   0.009759
[2025-10-21 19:51:37] [Iter  642/2250] R2[191/600]   | LR: 0.024253 | E:  -60.836828 | E_var:     0.7984 | E_err:   0.009872
[2025-10-21 19:52:04] [Iter  643/2250] R2[192/600]   | LR: 0.024198 | E:  -60.840584 | E_var:     0.8399 | E_err:   0.010125
[2025-10-21 19:52:31] [Iter  644/2250] R2[193/600]   | LR: 0.024142 | E:  -60.837044 | E_var:     0.8132 | E_err:   0.009963
[2025-10-21 19:52:57] [Iter  645/2250] R2[194/600]   | LR: 0.024087 | E:  -60.838779 | E_var:     0.8889 | E_err:   0.010417
[2025-10-21 19:53:24] [Iter  646/2250] R2[195/600]   | LR: 0.024031 | E:  -60.816296 | E_var:     0.8292 | E_err:   0.010061
[2025-10-21 19:53:51] [Iter  647/2250] R2[196/600]   | LR: 0.023975 | E:  -60.820226 | E_var:     0.8617 | E_err:   0.010256
[2025-10-21 19:54:18] [Iter  648/2250] R2[197/600]   | LR: 0.023919 | E:  -60.829748 | E_var:     0.7469 | E_err:   0.009549
[2025-10-21 19:54:45] [Iter  649/2250] R2[198/600]   | LR: 0.023863 | E:  -60.848550 | E_var:     0.7531 | E_err:   0.009588
[2025-10-21 19:55:12] [Iter  650/2250] R2[199/600]   | LR: 0.023807 | E:  -60.838917 | E_var:     0.8086 | E_err:   0.009935
[2025-10-21 19:55:38] [Iter  651/2250] R2[200/600]   | LR: 0.023750 | E:  -60.846121 | E_var:     0.7913 | E_err:   0.009828
[2025-10-21 19:56:05] [Iter  652/2250] R2[201/600]   | LR: 0.023693 | E:  -60.823676 | E_var:     0.7605 | E_err:   0.009635
[2025-10-21 19:56:32] [Iter  653/2250] R2[202/600]   | LR: 0.023636 | E:  -60.847033 | E_var:     0.8779 | E_err:   0.010352
[2025-10-21 19:56:59] [Iter  654/2250] R2[203/600]   | LR: 0.023579 | E:  -60.843512 | E_var:     0.7901 | E_err:   0.009821
[2025-10-21 19:57:26] [Iter  655/2250] R2[204/600]   | LR: 0.023522 | E:  -60.841364 | E_var:     0.8813 | E_err:   0.010372
[2025-10-21 19:57:53] [Iter  656/2250] R2[205/600]   | LR: 0.023464 | E:  -60.836194 | E_var:     0.8630 | E_err:   0.010264
[2025-10-21 19:58:20] [Iter  657/2250] R2[206/600]   | LR: 0.023407 | E:  -60.825361 | E_var:     0.9293 | E_err:   0.010651
[2025-10-21 19:58:46] [Iter  658/2250] R2[207/600]   | LR: 0.023349 | E:  -60.848288 | E_var:     0.7449 | E_err:   0.009536
[2025-10-21 19:59:13] [Iter  659/2250] R2[208/600]   | LR: 0.023291 | E:  -60.832260 | E_var:     0.7630 | E_err:   0.009651
[2025-10-21 19:59:40] [Iter  660/2250] R2[209/600]   | LR: 0.023233 | E:  -60.840828 | E_var:     0.8321 | E_err:   0.010078
[2025-10-21 20:00:07] [Iter  661/2250] R2[210/600]   | LR: 0.023175 | E:  -60.844185 | E_var:     0.7812 | E_err:   0.009765
[2025-10-21 20:00:34] [Iter  662/2250] R2[211/600]   | LR: 0.023116 | E:  -60.833952 | E_var:     0.8911 | E_err:   0.010430
[2025-10-21 20:01:01] [Iter  663/2250] R2[212/600]   | LR: 0.023058 | E:  -60.835626 | E_var:     0.8202 | E_err:   0.010006
[2025-10-21 20:01:28] [Iter  664/2250] R2[213/600]   | LR: 0.022999 | E:  -60.830482 | E_var:     0.7531 | E_err:   0.009588
[2025-10-21 20:01:54] [Iter  665/2250] R2[214/600]   | LR: 0.022940 | E:  -60.831502 | E_var:     0.8823 | E_err:   0.010378
[2025-10-21 20:02:21] [Iter  666/2250] R2[215/600]   | LR: 0.022881 | E:  -60.821212 | E_var:     0.7460 | E_err:   0.009543
[2025-10-21 20:02:48] [Iter  667/2250] R2[216/600]   | LR: 0.022822 | E:  -60.830742 | E_var:     0.7894 | E_err:   0.009817
[2025-10-21 20:03:15] [Iter  668/2250] R2[217/600]   | LR: 0.022763 | E:  -60.853399 | E_var:     0.8935 | E_err:   0.010443
[2025-10-21 20:03:42] [Iter  669/2250] R2[218/600]   | LR: 0.022704 | E:  -60.833055 | E_var:     0.7968 | E_err:   0.009863
[2025-10-21 20:04:09] [Iter  670/2250] R2[219/600]   | LR: 0.022644 | E:  -60.853639 | E_var:     0.8209 | E_err:   0.010010
[2025-10-21 20:04:36] [Iter  671/2250] R2[220/600]   | LR: 0.022584 | E:  -60.829073 | E_var:     1.0073 | E_err:   0.011089
[2025-10-21 20:05:03] [Iter  672/2250] R2[221/600]   | LR: 0.022524 | E:  -60.832935 | E_var:     0.7991 | E_err:   0.009876
[2025-10-21 20:05:29] [Iter  673/2250] R2[222/600]   | LR: 0.022464 | E:  -60.840513 | E_var:     0.8842 | E_err:   0.010389
[2025-10-21 20:05:56] [Iter  674/2250] R2[223/600]   | LR: 0.022404 | E:  -60.836651 | E_var:     0.8675 | E_err:   0.010291
[2025-10-21 20:06:23] [Iter  675/2250] R2[224/600]   | LR: 0.022344 | E:  -60.851249 | E_var:     0.8694 | E_err:   0.010302
[2025-10-21 20:06:23] ✓ Checkpoint saved: checkpoint_iter_000675.pkl
[2025-10-21 20:06:50] [Iter  676/2250] R2[225/600]   | LR: 0.022284 | E:  -60.829413 | E_var:     0.7473 | E_err:   0.009551
[2025-10-21 20:07:17] [Iter  677/2250] R2[226/600]   | LR: 0.022223 | E:  -60.845453 | E_var:     0.7440 | E_err:   0.009530
[2025-10-21 20:07:44] [Iter  678/2250] R2[227/600]   | LR: 0.022162 | E:  -60.842643 | E_var:     0.8707 | E_err:   0.010309
[2025-10-21 20:08:11] [Iter  679/2250] R2[228/600]   | LR: 0.022102 | E:  -60.840907 | E_var:     0.9592 | E_err:   0.010821
[2025-10-21 20:08:38] [Iter  680/2250] R2[229/600]   | LR: 0.022041 | E:  -60.840289 | E_var:     0.7816 | E_err:   0.009768
[2025-10-21 20:09:05] [Iter  681/2250] R2[230/600]   | LR: 0.021980 | E:  -60.836213 | E_var:     0.7547 | E_err:   0.009598
[2025-10-21 20:09:32] [Iter  682/2250] R2[231/600]   | LR: 0.021918 | E:  -60.840282 | E_var:     0.8037 | E_err:   0.009905
[2025-10-21 20:09:59] [Iter  683/2250] R2[232/600]   | LR: 0.021857 | E:  -60.844100 | E_var:     0.7309 | E_err:   0.009446
[2025-10-21 20:10:25] [Iter  684/2250] R2[233/600]   | LR: 0.021796 | E:  -60.843700 | E_var:     0.7695 | E_err:   0.009692
[2025-10-21 20:10:52] [Iter  685/2250] R2[234/600]   | LR: 0.021734 | E:  -60.848669 | E_var:     0.7636 | E_err:   0.009655
[2025-10-21 20:11:19] [Iter  686/2250] R2[235/600]   | LR: 0.021673 | E:  -60.832872 | E_var:     0.8129 | E_err:   0.009962
[2025-10-21 20:11:46] [Iter  687/2250] R2[236/600]   | LR: 0.021611 | E:  -60.849268 | E_var:     0.8683 | E_err:   0.010296
[2025-10-21 20:12:13] [Iter  688/2250] R2[237/600]   | LR: 0.021549 | E:  -60.832540 | E_var:     1.0242 | E_err:   0.011181
[2025-10-21 20:12:40] [Iter  689/2250] R2[238/600]   | LR: 0.021487 | E:  -60.849904 | E_var:     0.8764 | E_err:   0.010343
[2025-10-21 20:13:07] [Iter  690/2250] R2[239/600]   | LR: 0.021425 | E:  -60.855317 | E_var:     0.7283 | E_err:   0.009429
[2025-10-21 20:13:34] [Iter  691/2250] R2[240/600]   | LR: 0.021363 | E:  -60.848146 | E_var:     0.7129 | E_err:   0.009329
[2025-10-21 20:14:00] [Iter  692/2250] R2[241/600]   | LR: 0.021300 | E:  -60.840798 | E_var:     0.7670 | E_err:   0.009676
[2025-10-21 20:14:27] [Iter  693/2250] R2[242/600]   | LR: 0.021238 | E:  -60.846518 | E_var:     0.8200 | E_err:   0.010005
[2025-10-21 20:14:54] [Iter  694/2250] R2[243/600]   | LR: 0.021176 | E:  -60.847020 | E_var:     0.7713 | E_err:   0.009703
[2025-10-21 20:15:21] [Iter  695/2250] R2[244/600]   | LR: 0.021113 | E:  -60.836828 | E_var:     0.8434 | E_err:   0.010147
[2025-10-21 20:15:48] [Iter  696/2250] R2[245/600]   | LR: 0.021050 | E:  -60.817965 | E_var:     0.9205 | E_err:   0.010600
[2025-10-21 20:16:15] [Iter  697/2250] R2[246/600]   | LR: 0.020987 | E:  -60.855355 | E_var:     0.9083 | E_err:   0.010530
[2025-10-21 20:16:42] [Iter  698/2250] R2[247/600]   | LR: 0.020924 | E:  -60.846820 | E_var:     0.7199 | E_err:   0.009375
[2025-10-21 20:17:09] [Iter  699/2250] R2[248/600]   | LR: 0.020861 | E:  -60.841993 | E_var:     0.7349 | E_err:   0.009472
[2025-10-21 20:17:36] [Iter  700/2250] R2[249/600]   | LR: 0.020798 | E:  -60.839614 | E_var:     0.7871 | E_err:   0.009802
[2025-10-21 20:18:02] [Iter  701/2250] R2[250/600]   | LR: 0.020735 | E:  -60.841812 | E_var:     0.7075 | E_err:   0.009294
[2025-10-21 20:18:29] [Iter  702/2250] R2[251/600]   | LR: 0.020672 | E:  -60.833618 | E_var:     0.7138 | E_err:   0.009334
[2025-10-21 20:18:56] [Iter  703/2250] R2[252/600]   | LR: 0.020609 | E:  -60.838166 | E_var:     0.7595 | E_err:   0.009628
[2025-10-21 20:19:23] [Iter  704/2250] R2[253/600]   | LR: 0.020545 | E:  -60.852334 | E_var:     0.7836 | E_err:   0.009781
[2025-10-21 20:19:50] [Iter  705/2250] R2[254/600]   | LR: 0.020482 | E:  -60.830279 | E_var:     0.7277 | E_err:   0.009425
[2025-10-21 20:20:17] [Iter  706/2250] R2[255/600]   | LR: 0.020418 | E:  -60.844507 | E_var:     0.7721 | E_err:   0.009708
[2025-10-21 20:20:44] [Iter  707/2250] R2[256/600]   | LR: 0.020354 | E:  -60.854196 | E_var:     0.7531 | E_err:   0.009588
[2025-10-21 20:21:11] [Iter  708/2250] R2[257/600]   | LR: 0.020291 | E:  -60.845036 | E_var:     0.8300 | E_err:   0.010066
[2025-10-21 20:21:37] [Iter  709/2250] R2[258/600]   | LR: 0.020227 | E:  -60.851192 | E_var:     0.8612 | E_err:   0.010253
[2025-10-21 20:22:04] [Iter  710/2250] R2[259/600]   | LR: 0.020163 | E:  -60.848309 | E_var:     0.7948 | E_err:   0.009850
[2025-10-21 20:22:31] [Iter  711/2250] R2[260/600]   | LR: 0.020099 | E:  -60.831253 | E_var:     0.8787 | E_err:   0.010357
[2025-10-21 20:22:58] [Iter  712/2250] R2[261/600]   | LR: 0.020035 | E:  -60.824165 | E_var:     0.9673 | E_err:   0.010867
[2025-10-21 20:23:25] [Iter  713/2250] R2[262/600]   | LR: 0.019971 | E:  -60.848318 | E_var:     0.7804 | E_err:   0.009760
[2025-10-21 20:23:52] [Iter  714/2250] R2[263/600]   | LR: 0.019907 | E:  -60.845872 | E_var:     0.7338 | E_err:   0.009464
[2025-10-21 20:24:19] [Iter  715/2250] R2[264/600]   | LR: 0.019842 | E:  -60.834935 | E_var:     0.8312 | E_err:   0.010073
[2025-10-21 20:24:46] [Iter  716/2250] R2[265/600]   | LR: 0.019778 | E:  -60.832614 | E_var:     0.7466 | E_err:   0.009547
[2025-10-21 20:25:12] [Iter  717/2250] R2[266/600]   | LR: 0.019714 | E:  -60.843195 | E_var:     0.8847 | E_err:   0.010392
[2025-10-21 20:25:39] [Iter  718/2250] R2[267/600]   | LR: 0.019649 | E:  -60.853747 | E_var:     0.6973 | E_err:   0.009226
[2025-10-21 20:26:06] [Iter  719/2250] R2[268/600]   | LR: 0.019585 | E:  -60.837202 | E_var:     0.7564 | E_err:   0.009609
[2025-10-21 20:26:33] [Iter  720/2250] R2[269/600]   | LR: 0.019520 | E:  -60.841486 | E_var:     0.8131 | E_err:   0.009963
[2025-10-21 20:27:00] [Iter  721/2250] R2[270/600]   | LR: 0.019455 | E:  -60.856796 | E_var:     0.7785 | E_err:   0.009749
[2025-10-21 20:27:27] [Iter  722/2250] R2[271/600]   | LR: 0.019391 | E:  -60.833422 | E_var:     0.9560 | E_err:   0.010803
[2025-10-21 20:27:54] [Iter  723/2250] R2[272/600]   | LR: 0.019326 | E:  -60.851769 | E_var:     0.7777 | E_err:   0.009743
[2025-10-21 20:28:21] [Iter  724/2250] R2[273/600]   | LR: 0.019261 | E:  -60.835167 | E_var:     0.6673 | E_err:   0.009026
[2025-10-21 20:28:48] [Iter  725/2250] R2[274/600]   | LR: 0.019196 | E:  -60.842764 | E_var:     0.7366 | E_err:   0.009482
[2025-10-21 20:29:14] [Iter  726/2250] R2[275/600]   | LR: 0.019132 | E:  -60.847596 | E_var:     0.9383 | E_err:   0.010702
[2025-10-21 20:29:41] [Iter  727/2250] R2[276/600]   | LR: 0.019067 | E:  -60.836453 | E_var:     0.7783 | E_err:   0.009747
[2025-10-21 20:30:08] [Iter  728/2250] R2[277/600]   | LR: 0.019002 | E:  -60.840992 | E_var:     0.8417 | E_err:   0.010136
[2025-10-21 20:30:35] [Iter  729/2250] R2[278/600]   | LR: 0.018937 | E:  -60.858774 | E_var:     0.7766 | E_err:   0.009737
[2025-10-21 20:31:02] [Iter  730/2250] R2[279/600]   | LR: 0.018872 | E:  -60.843437 | E_var:     0.6973 | E_err:   0.009226
[2025-10-21 20:31:29] [Iter  731/2250] R2[280/600]   | LR: 0.018807 | E:  -60.832074 | E_var:     0.8279 | E_err:   0.010053
[2025-10-21 20:31:56] [Iter  732/2250] R2[281/600]   | LR: 0.018741 | E:  -60.850906 | E_var:     0.7388 | E_err:   0.009497
[2025-10-21 20:32:23] [Iter  733/2250] R2[282/600]   | LR: 0.018676 | E:  -60.848368 | E_var:     0.8391 | E_err:   0.010121
[2025-10-21 20:32:50] [Iter  734/2250] R2[283/600]   | LR: 0.018611 | E:  -60.851631 | E_var:     0.7961 | E_err:   0.009858
[2025-10-21 20:33:16] [Iter  735/2250] R2[284/600]   | LR: 0.018546 | E:  -60.838287 | E_var:     0.8973 | E_err:   0.010466
[2025-10-21 20:33:43] [Iter  736/2250] R2[285/600]   | LR: 0.018481 | E:  -60.853488 | E_var:     0.6824 | E_err:   0.009127
[2025-10-21 20:34:10] [Iter  737/2250] R2[286/600]   | LR: 0.018415 | E:  -60.842631 | E_var:     0.8015 | E_err:   0.009892
[2025-10-21 20:34:37] [Iter  738/2250] R2[287/600]   | LR: 0.018350 | E:  -60.840178 | E_var:     0.8174 | E_err:   0.009989
[2025-10-21 20:35:04] [Iter  739/2250] R2[288/600]   | LR: 0.018285 | E:  -60.836452 | E_var:     0.7294 | E_err:   0.009436
[2025-10-21 20:35:31] [Iter  740/2250] R2[289/600]   | LR: 0.018220 | E:  -60.856464 | E_var:     0.8301 | E_err:   0.010066
[2025-10-21 20:35:58] [Iter  741/2250] R2[290/600]   | LR: 0.018154 | E:  -60.849847 | E_var:     0.7751 | E_err:   0.009727
[2025-10-21 20:36:25] [Iter  742/2250] R2[291/600]   | LR: 0.018089 | E:  -60.856152 | E_var:     0.7372 | E_err:   0.009487
[2025-10-21 20:36:52] [Iter  743/2250] R2[292/600]   | LR: 0.018023 | E:  -60.833885 | E_var:     0.8848 | E_err:   0.010393
[2025-10-21 20:37:18] [Iter  744/2250] R2[293/600]   | LR: 0.017958 | E:  -60.850194 | E_var:     0.7052 | E_err:   0.009278
[2025-10-21 20:37:45] [Iter  745/2250] R2[294/600]   | LR: 0.017893 | E:  -60.845805 | E_var:     0.6833 | E_err:   0.009133
[2025-10-21 20:38:12] [Iter  746/2250] R2[295/600]   | LR: 0.017827 | E:  -60.831556 | E_var:     0.7521 | E_err:   0.009582
[2025-10-21 20:38:39] [Iter  747/2250] R2[296/600]   | LR: 0.017762 | E:  -60.866575 | E_var:     0.7294 | E_err:   0.009436
[2025-10-21 20:39:06] [Iter  748/2250] R2[297/600]   | LR: 0.017696 | E:  -60.851971 | E_var:     0.7352 | E_err:   0.009473
[2025-10-21 20:39:33] [Iter  749/2250] R2[298/600]   | LR: 0.017631 | E:  -60.841785 | E_var:     0.7095 | E_err:   0.009306
[2025-10-21 20:39:59] [Iter  750/2250] R2[299/600]   | LR: 0.017565 | E:  -60.856335 | E_var:     0.7683 | E_err:   0.009684
[2025-10-21 20:40:26] [Iter  751/2250] R2[300/600]   | LR: 0.017500 | E:  -60.864693 | E_var:     0.7426 | E_err:   0.009521
[2025-10-21 20:40:53] [Iter  752/2250] R2[301/600]   | LR: 0.017435 | E:  -60.841435 | E_var:     0.7193 | E_err:   0.009370
[2025-10-21 20:41:20] [Iter  753/2250] R2[302/600]   | LR: 0.017369 | E:  -60.857569 | E_var:     0.8099 | E_err:   0.009943
[2025-10-21 20:41:47] [Iter  754/2250] R2[303/600]   | LR: 0.017304 | E:  -60.850079 | E_var:     0.8647 | E_err:   0.010274
[2025-10-21 20:42:14] [Iter  755/2250] R2[304/600]   | LR: 0.017238 | E:  -60.844011 | E_var:     0.7911 | E_err:   0.009827
[2025-10-21 20:42:41] [Iter  756/2250] R2[305/600]   | LR: 0.017173 | E:  -60.847974 | E_var:     1.1440 | E_err:   0.011817
[2025-10-21 20:43:08] [Iter  757/2250] R2[306/600]   | LR: 0.017107 | E:  -60.839884 | E_var:     0.9557 | E_err:   0.010801
[2025-10-21 20:43:34] [Iter  758/2250] R2[307/600]   | LR: 0.017042 | E:  -60.847006 | E_var:     0.6563 | E_err:   0.008951
[2025-10-21 20:44:01] [Iter  759/2250] R2[308/600]   | LR: 0.016977 | E:  -60.846558 | E_var:     0.7447 | E_err:   0.009535
[2025-10-21 20:44:28] [Iter  760/2250] R2[309/600]   | LR: 0.016911 | E:  -60.848819 | E_var:     0.8152 | E_err:   0.009976
[2025-10-21 20:44:55] [Iter  761/2250] R2[310/600]   | LR: 0.016846 | E:  -60.868014 | E_var:     0.7418 | E_err:   0.009516
[2025-10-21 20:45:22] [Iter  762/2250] R2[311/600]   | LR: 0.016780 | E:  -60.838985 | E_var:     0.6672 | E_err:   0.009024
[2025-10-21 20:45:49] [Iter  763/2250] R2[312/600]   | LR: 0.016715 | E:  -60.845103 | E_var:     0.7493 | E_err:   0.009564
[2025-10-21 20:46:16] [Iter  764/2250] R2[313/600]   | LR: 0.016650 | E:  -60.858336 | E_var:     0.7872 | E_err:   0.009803
[2025-10-21 20:46:43] [Iter  765/2250] R2[314/600]   | LR: 0.016585 | E:  -60.829450 | E_var:     0.8284 | E_err:   0.010056
[2025-10-21 20:47:10] [Iter  766/2250] R2[315/600]   | LR: 0.016519 | E:  -60.848102 | E_var:     0.7516 | E_err:   0.009579
[2025-10-21 20:47:36] [Iter  767/2250] R2[316/600]   | LR: 0.016454 | E:  -60.845566 | E_var:     0.6897 | E_err:   0.009175
[2025-10-21 20:48:03] [Iter  768/2250] R2[317/600]   | LR: 0.016389 | E:  -60.839082 | E_var:     0.7526 | E_err:   0.009585
[2025-10-21 20:48:30] [Iter  769/2250] R2[318/600]   | LR: 0.016324 | E:  -60.844036 | E_var:     0.7134 | E_err:   0.009332
[2025-10-21 20:48:57] [Iter  770/2250] R2[319/600]   | LR: 0.016259 | E:  -60.859274 | E_var:     0.8131 | E_err:   0.009963
[2025-10-21 20:49:24] [Iter  771/2250] R2[320/600]   | LR: 0.016193 | E:  -60.851968 | E_var:     0.8332 | E_err:   0.010085
[2025-10-21 20:49:51] [Iter  772/2250] R2[321/600]   | LR: 0.016128 | E:  -60.838917 | E_var:     0.7762 | E_err:   0.009734
[2025-10-21 20:50:18] [Iter  773/2250] R2[322/600]   | LR: 0.016063 | E:  -60.852164 | E_var:     0.7336 | E_err:   0.009463
[2025-10-21 20:50:45] [Iter  774/2250] R2[323/600]   | LR: 0.015998 | E:  -60.861894 | E_var:     0.9313 | E_err:   0.010662
[2025-10-21 20:51:12] [Iter  775/2250] R2[324/600]   | LR: 0.015933 | E:  -60.841625 | E_var:     0.6787 | E_err:   0.009102
[2025-10-21 20:51:38] [Iter  776/2250] R2[325/600]   | LR: 0.015868 | E:  -60.847605 | E_var:     0.8085 | E_err:   0.009934
[2025-10-21 20:52:05] [Iter  777/2250] R2[326/600]   | LR: 0.015804 | E:  -60.854307 | E_var:     0.7719 | E_err:   0.009707
[2025-10-21 20:52:32] [Iter  778/2250] R2[327/600]   | LR: 0.015739 | E:  -60.858509 | E_var:     0.8600 | E_err:   0.010246
[2025-10-21 20:52:59] [Iter  779/2250] R2[328/600]   | LR: 0.015674 | E:  -60.863947 | E_var:     0.7432 | E_err:   0.009525
[2025-10-21 20:53:26] [Iter  780/2250] R2[329/600]   | LR: 0.015609 | E:  -60.836931 | E_var:     0.9298 | E_err:   0.010653
[2025-10-21 20:53:53] [Iter  781/2250] R2[330/600]   | LR: 0.015545 | E:  -60.862779 | E_var:     0.8528 | E_err:   0.010203
[2025-10-21 20:54:20] [Iter  782/2250] R2[331/600]   | LR: 0.015480 | E:  -60.855491 | E_var:     0.7639 | E_err:   0.009657
[2025-10-21 20:54:47] [Iter  783/2250] R2[332/600]   | LR: 0.015415 | E:  -60.865087 | E_var:     0.8707 | E_err:   0.010310
[2025-10-21 20:55:14] [Iter  784/2250] R2[333/600]   | LR: 0.015351 | E:  -60.855098 | E_var:     0.6906 | E_err:   0.009182
[2025-10-21 20:55:40] [Iter  785/2250] R2[334/600]   | LR: 0.015286 | E:  -60.853922 | E_var:     0.7236 | E_err:   0.009398
[2025-10-21 20:56:07] [Iter  786/2250] R2[335/600]   | LR: 0.015222 | E:  -60.861221 | E_var:     0.7524 | E_err:   0.009584
[2025-10-21 20:56:34] [Iter  787/2250] R2[336/600]   | LR: 0.015158 | E:  -60.832598 | E_var:     0.8982 | E_err:   0.010471
[2025-10-21 20:57:01] [Iter  788/2250] R2[337/600]   | LR: 0.015093 | E:  -60.859409 | E_var:     0.9375 | E_err:   0.010698
[2025-10-21 20:57:28] [Iter  789/2250] R2[338/600]   | LR: 0.015029 | E:  -60.855224 | E_var:     0.7361 | E_err:   0.009479
[2025-10-21 20:57:55] [Iter  790/2250] R2[339/600]   | LR: 0.014965 | E:  -60.832696 | E_var:     0.8420 | E_err:   0.010138
[2025-10-21 20:58:22] [Iter  791/2250] R2[340/600]   | LR: 0.014901 | E:  -60.853866 | E_var:     0.8107 | E_err:   0.009948
[2025-10-21 20:58:49] [Iter  792/2250] R2[341/600]   | LR: 0.014837 | E:  -60.857029 | E_var:     0.7840 | E_err:   0.009783
[2025-10-21 20:59:15] [Iter  793/2250] R2[342/600]   | LR: 0.014773 | E:  -60.844900 | E_var:     0.7380 | E_err:   0.009491
[2025-10-21 20:59:42] [Iter  794/2250] R2[343/600]   | LR: 0.014709 | E:  -60.862313 | E_var:     0.7627 | E_err:   0.009649
[2025-10-21 21:00:09] [Iter  795/2250] R2[344/600]   | LR: 0.014646 | E:  -60.855565 | E_var:     0.7801 | E_err:   0.009759
[2025-10-21 21:00:36] [Iter  796/2250] R2[345/600]   | LR: 0.014582 | E:  -60.848336 | E_var:     0.7177 | E_err:   0.009360
[2025-10-21 21:01:03] [Iter  797/2250] R2[346/600]   | LR: 0.014518 | E:  -60.843268 | E_var:     0.7356 | E_err:   0.009476
[2025-10-21 21:01:30] [Iter  798/2250] R2[347/600]   | LR: 0.014455 | E:  -60.859570 | E_var:     0.8119 | E_err:   0.009955
[2025-10-21 21:01:57] [Iter  799/2250] R2[348/600]   | LR: 0.014391 | E:  -60.850494 | E_var:     1.1107 | E_err:   0.011644
[2025-10-21 21:02:24] [Iter  800/2250] R2[349/600]   | LR: 0.014328 | E:  -60.860866 | E_var:     0.6726 | E_err:   0.009061
[2025-10-21 21:02:51] [Iter  801/2250] R2[350/600]   | LR: 0.014265 | E:  -60.860947 | E_var:     0.7657 | E_err:   0.009668
[2025-10-21 21:03:17] [Iter  802/2250] R2[351/600]   | LR: 0.014202 | E:  -60.857070 | E_var:     0.8110 | E_err:   0.009950
[2025-10-21 21:03:44] [Iter  803/2250] R2[352/600]   | LR: 0.014139 | E:  -60.864733 | E_var:     0.7510 | E_err:   0.009575
[2025-10-21 21:04:11] [Iter  804/2250] R2[353/600]   | LR: 0.014076 | E:  -60.841721 | E_var:     0.8143 | E_err:   0.009970
[2025-10-21 21:04:38] [Iter  805/2250] R2[354/600]   | LR: 0.014013 | E:  -60.870070 | E_var:     0.7144 | E_err:   0.009339
[2025-10-21 21:05:05] [Iter  806/2250] R2[355/600]   | LR: 0.013950 | E:  -60.847888 | E_var:     0.8577 | E_err:   0.010233
[2025-10-21 21:05:32] [Iter  807/2250] R2[356/600]   | LR: 0.013887 | E:  -60.861415 | E_var:     0.7142 | E_err:   0.009337
[2025-10-21 21:05:59] [Iter  808/2250] R2[357/600]   | LR: 0.013824 | E:  -60.843259 | E_var:     0.7101 | E_err:   0.009310
[2025-10-21 21:06:26] [Iter  809/2250] R2[358/600]   | LR: 0.013762 | E:  -60.856637 | E_var:     0.7208 | E_err:   0.009380
[2025-10-21 21:06:52] [Iter  810/2250] R2[359/600]   | LR: 0.013700 | E:  -60.850581 | E_var:     0.7378 | E_err:   0.009490
[2025-10-21 21:07:19] [Iter  811/2250] R2[360/600]   | LR: 0.013637 | E:  -60.866021 | E_var:     0.7660 | E_err:   0.009670
[2025-10-21 21:07:46] [Iter  812/2250] R2[361/600]   | LR: 0.013575 | E:  -60.875656 | E_var:     0.7132 | E_err:   0.009331
[2025-10-21 21:08:13] [Iter  813/2250] R2[362/600]   | LR: 0.013513 | E:  -60.842276 | E_var:     0.9570 | E_err:   0.010809
[2025-10-21 21:08:40] [Iter  814/2250] R2[363/600]   | LR: 0.013451 | E:  -60.850349 | E_var:     0.7284 | E_err:   0.009429
[2025-10-21 21:09:07] [Iter  815/2250] R2[364/600]   | LR: 0.013389 | E:  -60.854175 | E_var:     0.8083 | E_err:   0.009934
[2025-10-21 21:09:34] [Iter  816/2250] R2[365/600]   | LR: 0.013327 | E:  -60.852201 | E_var:     0.7239 | E_err:   0.009401
[2025-10-21 21:10:00] [Iter  817/2250] R2[366/600]   | LR: 0.013266 | E:  -60.849499 | E_var:     0.7308 | E_err:   0.009445
[2025-10-21 21:10:27] [Iter  818/2250] R2[367/600]   | LR: 0.013204 | E:  -60.859235 | E_var:     0.7108 | E_err:   0.009315
[2025-10-21 21:10:54] [Iter  819/2250] R2[368/600]   | LR: 0.013143 | E:  -60.851625 | E_var:     0.8427 | E_err:   0.010142
[2025-10-21 21:11:21] [Iter  820/2250] R2[369/600]   | LR: 0.013082 | E:  -60.852346 | E_var:     0.7016 | E_err:   0.009255
[2025-10-21 21:11:48] [Iter  821/2250] R2[370/600]   | LR: 0.013020 | E:  -60.856160 | E_var:     0.6524 | E_err:   0.008924
[2025-10-21 21:12:15] [Iter  822/2250] R2[371/600]   | LR: 0.012959 | E:  -60.855512 | E_var:     0.6965 | E_err:   0.009221
[2025-10-21 21:12:42] [Iter  823/2250] R2[372/600]   | LR: 0.012898 | E:  -60.849286 | E_var:     0.8284 | E_err:   0.010056
[2025-10-21 21:13:09] [Iter  824/2250] R2[373/600]   | LR: 0.012838 | E:  -60.856974 | E_var:     0.6987 | E_err:   0.009235
[2025-10-21 21:13:35] [Iter  825/2250] R2[374/600]   | LR: 0.012777 | E:  -60.865191 | E_var:     0.8813 | E_err:   0.010372
[2025-10-21 21:14:02] [Iter  826/2250] R2[375/600]   | LR: 0.012716 | E:  -60.860382 | E_var:     0.7597 | E_err:   0.009630
[2025-10-21 21:14:29] [Iter  827/2250] R2[376/600]   | LR: 0.012656 | E:  -60.853371 | E_var:     0.7278 | E_err:   0.009426
[2025-10-21 21:14:56] [Iter  828/2250] R2[377/600]   | LR: 0.012596 | E:  -60.848625 | E_var:     0.7995 | E_err:   0.009879
[2025-10-21 21:15:23] [Iter  829/2250] R2[378/600]   | LR: 0.012536 | E:  -60.864548 | E_var:     0.8272 | E_err:   0.010048
[2025-10-21 21:15:50] [Iter  830/2250] R2[379/600]   | LR: 0.012476 | E:  -60.859069 | E_var:     0.8915 | E_err:   0.010432
[2025-10-21 21:16:17] [Iter  831/2250] R2[380/600]   | LR: 0.012416 | E:  -60.857422 | E_var:     0.7803 | E_err:   0.009760
[2025-10-21 21:16:44] [Iter  832/2250] R2[381/600]   | LR: 0.012356 | E:  -60.850728 | E_var:     0.8861 | E_err:   0.010401
[2025-10-21 21:17:11] [Iter  833/2250] R2[382/600]   | LR: 0.012296 | E:  -60.869639 | E_var:     0.7259 | E_err:   0.009413
[2025-10-21 21:17:37] [Iter  834/2250] R2[383/600]   | LR: 0.012237 | E:  -60.849695 | E_var:     0.6980 | E_err:   0.009231
[2025-10-21 21:18:04] [Iter  835/2250] R2[384/600]   | LR: 0.012178 | E:  -60.875502 | E_var:     0.7413 | E_err:   0.009512
[2025-10-21 21:18:31] [Iter  836/2250] R2[385/600]   | LR: 0.012119 | E:  -60.860919 | E_var:     0.7674 | E_err:   0.009679
[2025-10-21 21:18:58] [Iter  837/2250] R2[386/600]   | LR: 0.012060 | E:  -60.863118 | E_var:     0.8361 | E_err:   0.010103
[2025-10-21 21:19:25] [Iter  838/2250] R2[387/600]   | LR: 0.012001 | E:  -60.849290 | E_var:     0.7837 | E_err:   0.009781
[2025-10-21 21:19:52] [Iter  839/2250] R2[388/600]   | LR: 0.011942 | E:  -60.852919 | E_var:     0.8717 | E_err:   0.010316
[2025-10-21 21:20:19] [Iter  840/2250] R2[389/600]   | LR: 0.011884 | E:  -60.862773 | E_var:     0.6861 | E_err:   0.009151
[2025-10-21 21:20:46] [Iter  841/2250] R2[390/600]   | LR: 0.011825 | E:  -60.878307 | E_var:     0.7497 | E_err:   0.009567
[2025-10-21 21:21:13] [Iter  842/2250] R2[391/600]   | LR: 0.011767 | E:  -60.854627 | E_var:     0.7932 | E_err:   0.009840
[2025-10-21 21:21:39] [Iter  843/2250] R2[392/600]   | LR: 0.011709 | E:  -60.861453 | E_var:     0.7635 | E_err:   0.009654
[2025-10-21 21:22:06] [Iter  844/2250] R2[393/600]   | LR: 0.011651 | E:  -60.870330 | E_var:     0.7492 | E_err:   0.009563
[2025-10-21 21:22:33] [Iter  845/2250] R2[394/600]   | LR: 0.011593 | E:  -60.866888 | E_var:     0.7247 | E_err:   0.009405
[2025-10-21 21:23:00] [Iter  846/2250] R2[395/600]   | LR: 0.011536 | E:  -60.850860 | E_var:     0.8173 | E_err:   0.009988
[2025-10-21 21:23:27] [Iter  847/2250] R2[396/600]   | LR: 0.011478 | E:  -60.838652 | E_var:     0.7637 | E_err:   0.009656
[2025-10-21 21:23:54] [Iter  848/2250] R2[397/600]   | LR: 0.011421 | E:  -60.843419 | E_var:     1.2791 | E_err:   0.012496
[2025-10-21 21:24:21] [Iter  849/2250] R2[398/600]   | LR: 0.011364 | E:  -60.872604 | E_var:     0.7831 | E_err:   0.009777
[2025-10-21 21:24:48] [Iter  850/2250] R2[399/600]   | LR: 0.011307 | E:  -60.850062 | E_var:     0.8167 | E_err:   0.009985
[2025-10-21 21:25:15] [Iter  851/2250] R2[400/600]   | LR: 0.011250 | E:  -60.851189 | E_var:     0.7125 | E_err:   0.009326
[2025-10-21 21:25:41] [Iter  852/2250] R2[401/600]   | LR: 0.011193 | E:  -60.867550 | E_var:     0.7892 | E_err:   0.009815
[2025-10-21 21:26:08] [Iter  853/2250] R2[402/600]   | LR: 0.011137 | E:  -60.860205 | E_var:     0.7106 | E_err:   0.009314
[2025-10-21 21:26:35] [Iter  854/2250] R2[403/600]   | LR: 0.011081 | E:  -60.863946 | E_var:     0.7265 | E_err:   0.009417
[2025-10-21 21:27:02] [Iter  855/2250] R2[404/600]   | LR: 0.011025 | E:  -60.850695 | E_var:     0.7620 | E_err:   0.009645
[2025-10-21 21:27:29] [Iter  856/2250] R2[405/600]   | LR: 0.010969 | E:  -60.857998 | E_var:     0.7627 | E_err:   0.009649
[2025-10-21 21:27:56] [Iter  857/2250] R2[406/600]   | LR: 0.010913 | E:  -60.864515 | E_var:     0.7583 | E_err:   0.009621
[2025-10-21 21:28:23] [Iter  858/2250] R2[407/600]   | LR: 0.010858 | E:  -60.851099 | E_var:     0.8124 | E_err:   0.009958
[2025-10-21 21:28:50] [Iter  859/2250] R2[408/600]   | LR: 0.010802 | E:  -60.856270 | E_var:     0.8583 | E_err:   0.010236
[2025-10-21 21:29:16] [Iter  860/2250] R2[409/600]   | LR: 0.010747 | E:  -60.851953 | E_var:     0.7307 | E_err:   0.009445
[2025-10-21 21:29:43] [Iter  861/2250] R2[410/600]   | LR: 0.010692 | E:  -60.851516 | E_var:     0.8134 | E_err:   0.009965
[2025-10-21 21:30:10] [Iter  862/2250] R2[411/600]   | LR: 0.010637 | E:  -60.843505 | E_var:     0.6940 | E_err:   0.009204
[2025-10-21 21:30:37] [Iter  863/2250] R2[412/600]   | LR: 0.010583 | E:  -60.850520 | E_var:     0.7960 | E_err:   0.009857
[2025-10-21 21:31:04] [Iter  864/2250] R2[413/600]   | LR: 0.010528 | E:  -60.852283 | E_var:     0.7166 | E_err:   0.009353
[2025-10-21 21:31:31] [Iter  865/2250] R2[414/600]   | LR: 0.010474 | E:  -60.868449 | E_var:     0.7698 | E_err:   0.009694
[2025-10-21 21:31:58] [Iter  866/2250] R2[415/600]   | LR: 0.010420 | E:  -60.855801 | E_var:     0.7845 | E_err:   0.009786
[2025-10-21 21:32:25] [Iter  867/2250] R2[416/600]   | LR: 0.010366 | E:  -60.846988 | E_var:     0.7616 | E_err:   0.009642
[2025-10-21 21:32:51] [Iter  868/2250] R2[417/600]   | LR: 0.010312 | E:  -60.849517 | E_var:     0.8137 | E_err:   0.009966
[2025-10-21 21:33:18] [Iter  869/2250] R2[418/600]   | LR: 0.010259 | E:  -60.859552 | E_var:     0.7563 | E_err:   0.009609
[2025-10-21 21:33:45] [Iter  870/2250] R2[419/600]   | LR: 0.010206 | E:  -60.855688 | E_var:     0.7683 | E_err:   0.009684
[2025-10-21 21:34:12] [Iter  871/2250] R2[420/600]   | LR: 0.010153 | E:  -60.850275 | E_var:     0.9489 | E_err:   0.010763
[2025-10-21 21:34:39] [Iter  872/2250] R2[421/600]   | LR: 0.010100 | E:  -60.850728 | E_var:     0.7458 | E_err:   0.009541
[2025-10-21 21:35:06] [Iter  873/2250] R2[422/600]   | LR: 0.010047 | E:  -60.878997 | E_var:     0.7646 | E_err:   0.009661
[2025-10-21 21:35:33] [Iter  874/2250] R2[423/600]   | LR: 0.009995 | E:  -60.862534 | E_var:     1.0906 | E_err:   0.011538
[2025-10-21 21:36:00] [Iter  875/2250] R2[424/600]   | LR: 0.009943 | E:  -60.844224 | E_var:     0.7213 | E_err:   0.009384
[2025-10-21 21:36:27] [Iter  876/2250] R2[425/600]   | LR: 0.009890 | E:  -60.855501 | E_var:     0.6463 | E_err:   0.008883
[2025-10-21 21:36:53] [Iter  877/2250] R2[426/600]   | LR: 0.009839 | E:  -60.859770 | E_var:     0.8128 | E_err:   0.009961
[2025-10-21 21:37:20] [Iter  878/2250] R2[427/600]   | LR: 0.009787 | E:  -60.874755 | E_var:     0.8630 | E_err:   0.010264
[2025-10-21 21:37:47] [Iter  879/2250] R2[428/600]   | LR: 0.009736 | E:  -60.861166 | E_var:     0.7876 | E_err:   0.009805
[2025-10-21 21:38:14] [Iter  880/2250] R2[429/600]   | LR: 0.009684 | E:  -60.852272 | E_var:     0.6756 | E_err:   0.009082
[2025-10-21 21:38:41] [Iter  881/2250] R2[430/600]   | LR: 0.009633 | E:  -60.869754 | E_var:     0.8565 | E_err:   0.010225
[2025-10-21 21:39:08] [Iter  882/2250] R2[431/600]   | LR: 0.009583 | E:  -60.856396 | E_var:     0.7460 | E_err:   0.009543
[2025-10-21 21:39:35] [Iter  883/2250] R2[432/600]   | LR: 0.009532 | E:  -60.852517 | E_var:     0.8309 | E_err:   0.010071
[2025-10-21 21:40:02] [Iter  884/2250] R2[433/600]   | LR: 0.009482 | E:  -60.875134 | E_var:     0.6776 | E_err:   0.009095
[2025-10-21 21:40:29] [Iter  885/2250] R2[434/600]   | LR: 0.009432 | E:  -60.860523 | E_var:     0.6889 | E_err:   0.009170
[2025-10-21 21:40:55] [Iter  886/2250] R2[435/600]   | LR: 0.009382 | E:  -60.874147 | E_var:     0.7434 | E_err:   0.009526
[2025-10-21 21:41:22] [Iter  887/2250] R2[436/600]   | LR: 0.009332 | E:  -60.861398 | E_var:     0.6854 | E_err:   0.009147
[2025-10-21 21:41:49] [Iter  888/2250] R2[437/600]   | LR: 0.009283 | E:  -60.862971 | E_var:     0.7882 | E_err:   0.009809
[2025-10-21 21:42:16] [Iter  889/2250] R2[438/600]   | LR: 0.009234 | E:  -60.863181 | E_var:     0.8449 | E_err:   0.010156
[2025-10-21 21:42:43] [Iter  890/2250] R2[439/600]   | LR: 0.009185 | E:  -60.882050 | E_var:     0.7172 | E_err:   0.009357
[2025-10-21 21:43:10] [Iter  891/2250] R2[440/600]   | LR: 0.009136 | E:  -60.852334 | E_var:     1.1940 | E_err:   0.012073
[2025-10-21 21:43:37] [Iter  892/2250] R2[441/600]   | LR: 0.009087 | E:  -60.855544 | E_var:     0.7026 | E_err:   0.009261
[2025-10-21 21:44:04] [Iter  893/2250] R2[442/600]   | LR: 0.009039 | E:  -60.853416 | E_var:     0.6833 | E_err:   0.009133
[2025-10-21 21:44:31] [Iter  894/2250] R2[443/600]   | LR: 0.008991 | E:  -60.862047 | E_var:     0.7000 | E_err:   0.009244
[2025-10-21 21:44:57] [Iter  895/2250] R2[444/600]   | LR: 0.008943 | E:  -60.875443 | E_var:     0.7782 | E_err:   0.009747
[2025-10-21 21:45:24] [Iter  896/2250] R2[445/600]   | LR: 0.008896 | E:  -60.848868 | E_var:     0.7041 | E_err:   0.009271
[2025-10-21 21:45:51] [Iter  897/2250] R2[446/600]   | LR: 0.008848 | E:  -60.866247 | E_var:     0.7823 | E_err:   0.009772
[2025-10-21 21:46:18] [Iter  898/2250] R2[447/600]   | LR: 0.008801 | E:  -60.861768 | E_var:     0.8058 | E_err:   0.009918
[2025-10-21 21:46:45] [Iter  899/2250] R2[448/600]   | LR: 0.008754 | E:  -60.859132 | E_var:     0.7505 | E_err:   0.009572
[2025-10-21 21:47:12] [Iter  900/2250] R2[449/600]   | LR: 0.008708 | E:  -60.877308 | E_var:     0.7385 | E_err:   0.009494
[2025-10-21 21:47:12] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-21 21:47:39] [Iter  901/2250] R2[450/600]   | LR: 0.008661 | E:  -60.877979 | E_var:     1.0156 | E_err:   0.011135
[2025-10-21 21:48:06] [Iter  902/2250] R2[451/600]   | LR: 0.008615 | E:  -60.857093 | E_var:     0.7353 | E_err:   0.009474
[2025-10-21 21:48:33] [Iter  903/2250] R2[452/600]   | LR: 0.008569 | E:  -60.848745 | E_var:     0.7064 | E_err:   0.009286
[2025-10-21 21:48:59] [Iter  904/2250] R2[453/600]   | LR: 0.008523 | E:  -60.855746 | E_var:     0.7448 | E_err:   0.009535
[2025-10-21 21:49:26] [Iter  905/2250] R2[454/600]   | LR: 0.008478 | E:  -60.865191 | E_var:     0.6673 | E_err:   0.009025
[2025-10-21 21:49:53] [Iter  906/2250] R2[455/600]   | LR: 0.008433 | E:  -60.879926 | E_var:     0.7159 | E_err:   0.009349
[2025-10-21 21:50:20] [Iter  907/2250] R2[456/600]   | LR: 0.008388 | E:  -60.848642 | E_var:     0.7449 | E_err:   0.009535
[2025-10-21 21:50:47] [Iter  908/2250] R2[457/600]   | LR: 0.008343 | E:  -60.876454 | E_var:     0.7520 | E_err:   0.009581
[2025-10-21 21:51:14] [Iter  909/2250] R2[458/600]   | LR: 0.008299 | E:  -60.853937 | E_var:     0.7090 | E_err:   0.009303
[2025-10-21 21:51:41] [Iter  910/2250] R2[459/600]   | LR: 0.008255 | E:  -60.863997 | E_var:     0.9376 | E_err:   0.010698
[2025-10-21 21:52:07] [Iter  911/2250] R2[460/600]   | LR: 0.008211 | E:  -60.851756 | E_var:     0.9055 | E_err:   0.010514
[2025-10-21 21:52:34] [Iter  912/2250] R2[461/600]   | LR: 0.008167 | E:  -60.866033 | E_var:     0.7163 | E_err:   0.009351
[2025-10-21 21:53:01] [Iter  913/2250] R2[462/600]   | LR: 0.008124 | E:  -60.867438 | E_var:     0.6920 | E_err:   0.009191
[2025-10-21 21:53:28] [Iter  914/2250] R2[463/600]   | LR: 0.008080 | E:  -60.860827 | E_var:     0.7027 | E_err:   0.009262
[2025-10-21 21:53:55] [Iter  915/2250] R2[464/600]   | LR: 0.008038 | E:  -60.861891 | E_var:     0.6335 | E_err:   0.008794
[2025-10-21 21:54:22] [Iter  916/2250] R2[465/600]   | LR: 0.007995 | E:  -60.852581 | E_var:     0.7227 | E_err:   0.009392
[2025-10-21 21:54:49] [Iter  917/2250] R2[466/600]   | LR: 0.007953 | E:  -60.873592 | E_var:     0.7021 | E_err:   0.009258
[2025-10-21 21:55:16] [Iter  918/2250] R2[467/600]   | LR: 0.007910 | E:  -60.864903 | E_var:     0.7407 | E_err:   0.009509
[2025-10-21 21:55:42] [Iter  919/2250] R2[468/600]   | LR: 0.007869 | E:  -60.859099 | E_var:     0.7729 | E_err:   0.009713
[2025-10-21 21:56:09] [Iter  920/2250] R2[469/600]   | LR: 0.007827 | E:  -60.867442 | E_var:     0.6668 | E_err:   0.009022
[2025-10-21 21:56:36] [Iter  921/2250] R2[470/600]   | LR: 0.007786 | E:  -60.876035 | E_var:     0.7391 | E_err:   0.009499
[2025-10-21 21:57:03] [Iter  922/2250] R2[471/600]   | LR: 0.007745 | E:  -60.857967 | E_var:     0.7853 | E_err:   0.009791
[2025-10-21 21:57:30] [Iter  923/2250] R2[472/600]   | LR: 0.007704 | E:  -60.860462 | E_var:     0.7110 | E_err:   0.009316
[2025-10-21 21:57:57] [Iter  924/2250] R2[473/600]   | LR: 0.007663 | E:  -60.860694 | E_var:     0.6925 | E_err:   0.009194
[2025-10-21 21:58:24] [Iter  925/2250] R2[474/600]   | LR: 0.007623 | E:  -60.874453 | E_var:     0.7178 | E_err:   0.009361
[2025-10-21 21:58:51] [Iter  926/2250] R2[475/600]   | LR: 0.007583 | E:  -60.877822 | E_var:     0.7915 | E_err:   0.009829
[2025-10-21 21:59:17] [Iter  927/2250] R2[476/600]   | LR: 0.007543 | E:  -60.854871 | E_var:     0.7550 | E_err:   0.009600
[2025-10-21 21:59:44] [Iter  928/2250] R2[477/600]   | LR: 0.007504 | E:  -60.873185 | E_var:     0.6235 | E_err:   0.008724
[2025-10-21 22:00:11] [Iter  929/2250] R2[478/600]   | LR: 0.007465 | E:  -60.868851 | E_var:     0.7412 | E_err:   0.009512
[2025-10-21 22:00:38] [Iter  930/2250] R2[479/600]   | LR: 0.007426 | E:  -60.860302 | E_var:     0.8011 | E_err:   0.009889
[2025-10-21 22:01:05] [Iter  931/2250] R2[480/600]   | LR: 0.007387 | E:  -60.868945 | E_var:     0.9040 | E_err:   0.010505
[2025-10-21 22:01:32] [Iter  932/2250] R2[481/600]   | LR: 0.007349 | E:  -60.877905 | E_var:     0.6466 | E_err:   0.008884
[2025-10-21 22:01:59] [Iter  933/2250] R2[482/600]   | LR: 0.007311 | E:  -60.867617 | E_var:     0.9354 | E_err:   0.010686
[2025-10-21 22:02:26] [Iter  934/2250] R2[483/600]   | LR: 0.007273 | E:  -60.867047 | E_var:     0.6820 | E_err:   0.009124
[2025-10-21 22:02:52] [Iter  935/2250] R2[484/600]   | LR: 0.007236 | E:  -60.865398 | E_var:     0.6841 | E_err:   0.009138
[2025-10-21 22:03:19] [Iter  936/2250] R2[485/600]   | LR: 0.007198 | E:  -60.866441 | E_var:     0.6770 | E_err:   0.009090
[2025-10-21 22:03:46] [Iter  937/2250] R2[486/600]   | LR: 0.007161 | E:  -60.870334 | E_var:     0.7613 | E_err:   0.009640
[2025-10-21 22:04:13] [Iter  938/2250] R2[487/600]   | LR: 0.007125 | E:  -60.850855 | E_var:     0.7464 | E_err:   0.009545
[2025-10-21 22:04:40] [Iter  939/2250] R2[488/600]   | LR: 0.007088 | E:  -60.864680 | E_var:     0.7398 | E_err:   0.009503
[2025-10-21 22:05:07] [Iter  940/2250] R2[489/600]   | LR: 0.007052 | E:  -60.849850 | E_var:     0.7132 | E_err:   0.009331
[2025-10-21 22:05:34] [Iter  941/2250] R2[490/600]   | LR: 0.007017 | E:  -60.874828 | E_var:     0.7020 | E_err:   0.009257
[2025-10-21 22:06:01] [Iter  942/2250] R2[491/600]   | LR: 0.006981 | E:  -60.875316 | E_var:     0.7206 | E_err:   0.009379
[2025-10-21 22:06:28] [Iter  943/2250] R2[492/600]   | LR: 0.006946 | E:  -60.864038 | E_var:     0.7070 | E_err:   0.009290
[2025-10-21 22:06:54] [Iter  944/2250] R2[493/600]   | LR: 0.006911 | E:  -60.868100 | E_var:     0.6911 | E_err:   0.009185
[2025-10-21 22:07:21] [Iter  945/2250] R2[494/600]   | LR: 0.006876 | E:  -60.867077 | E_var:     0.7137 | E_err:   0.009334
[2025-10-21 22:07:48] [Iter  946/2250] R2[495/600]   | LR: 0.006842 | E:  -60.849933 | E_var:     0.8254 | E_err:   0.010038
[2025-10-21 22:08:15] [Iter  947/2250] R2[496/600]   | LR: 0.006808 | E:  -60.870299 | E_var:     0.7150 | E_err:   0.009342
[2025-10-21 22:08:42] [Iter  948/2250] R2[497/600]   | LR: 0.006774 | E:  -60.867894 | E_var:     0.8407 | E_err:   0.010130
[2025-10-21 22:09:09] [Iter  949/2250] R2[498/600]   | LR: 0.006741 | E:  -60.876312 | E_var:     0.7486 | E_err:   0.009559
[2025-10-21 22:09:36] [Iter  950/2250] R2[499/600]   | LR: 0.006708 | E:  -60.876866 | E_var:     0.7478 | E_err:   0.009554
[2025-10-21 22:10:03] [Iter  951/2250] R2[500/600]   | LR: 0.006675 | E:  -60.864353 | E_var:     0.9467 | E_err:   0.010750
[2025-10-21 22:10:30] [Iter  952/2250] R2[501/600]   | LR: 0.006642 | E:  -60.878310 | E_var:     0.8212 | E_err:   0.010012
[2025-10-21 22:10:56] [Iter  953/2250] R2[502/600]   | LR: 0.006610 | E:  -60.865445 | E_var:     0.8270 | E_err:   0.010047
[2025-10-21 22:11:23] [Iter  954/2250] R2[503/600]   | LR: 0.006578 | E:  -60.857736 | E_var:     0.7552 | E_err:   0.009601
[2025-10-21 22:11:50] [Iter  955/2250] R2[504/600]   | LR: 0.006546 | E:  -60.877848 | E_var:     0.6805 | E_err:   0.009114
[2025-10-21 22:12:17] [Iter  956/2250] R2[505/600]   | LR: 0.006515 | E:  -60.868255 | E_var:     0.7214 | E_err:   0.009384
[2025-10-21 22:12:44] [Iter  957/2250] R2[506/600]   | LR: 0.006484 | E:  -60.875088 | E_var:     0.7356 | E_err:   0.009476
[2025-10-21 22:13:11] [Iter  958/2250] R2[507/600]   | LR: 0.006453 | E:  -60.860992 | E_var:     0.7575 | E_err:   0.009616
[2025-10-21 22:13:38] [Iter  959/2250] R2[508/600]   | LR: 0.006422 | E:  -60.854589 | E_var:     0.6932 | E_err:   0.009199
[2025-10-21 22:14:05] [Iter  960/2250] R2[509/600]   | LR: 0.006392 | E:  -60.870033 | E_var:     0.6714 | E_err:   0.009053
[2025-10-21 22:14:32] [Iter  961/2250] R2[510/600]   | LR: 0.006362 | E:  -60.877778 | E_var:     0.7983 | E_err:   0.009872
[2025-10-21 22:14:59] [Iter  962/2250] R2[511/600]   | LR: 0.006333 | E:  -60.887927 | E_var:     0.7734 | E_err:   0.009717
[2025-10-21 22:15:25] [Iter  963/2250] R2[512/600]   | LR: 0.006304 | E:  -60.869625 | E_var:     0.6336 | E_err:   0.008795
[2025-10-21 22:15:52] [Iter  964/2250] R2[513/600]   | LR: 0.006275 | E:  -60.869259 | E_var:     0.7226 | E_err:   0.009392
[2025-10-21 22:16:19] [Iter  965/2250] R2[514/600]   | LR: 0.006246 | E:  -60.875435 | E_var:     0.7105 | E_err:   0.009313
[2025-10-21 22:16:46] [Iter  966/2250] R2[515/600]   | LR: 0.006218 | E:  -60.875828 | E_var:     0.7339 | E_err:   0.009465
[2025-10-21 22:17:13] [Iter  967/2250] R2[516/600]   | LR: 0.006190 | E:  -60.869806 | E_var:     0.6609 | E_err:   0.008982
[2025-10-21 22:17:40] [Iter  968/2250] R2[517/600]   | LR: 0.006162 | E:  -60.874020 | E_var:     0.6687 | E_err:   0.009035
[2025-10-21 22:18:07] [Iter  969/2250] R2[518/600]   | LR: 0.006135 | E:  -60.868538 | E_var:     0.7320 | E_err:   0.009453
[2025-10-21 22:18:33] [Iter  970/2250] R2[519/600]   | LR: 0.006107 | E:  -60.855532 | E_var:     0.7211 | E_err:   0.009382
[2025-10-21 22:19:00] [Iter  971/2250] R2[520/600]   | LR: 0.006081 | E:  -60.890604 | E_var:     0.8089 | E_err:   0.009937
[2025-10-21 22:19:27] [Iter  972/2250] R2[521/600]   | LR: 0.006054 | E:  -60.871993 | E_var:     1.0631 | E_err:   0.011392
[2025-10-21 22:19:54] [Iter  973/2250] R2[522/600]   | LR: 0.006028 | E:  -60.892433 | E_var:     0.7069 | E_err:   0.009289
[2025-10-21 22:20:21] [Iter  974/2250] R2[523/600]   | LR: 0.006002 | E:  -60.876226 | E_var:     0.6944 | E_err:   0.009207
[2025-10-21 22:20:48] [Iter  975/2250] R2[524/600]   | LR: 0.005977 | E:  -60.862332 | E_var:     0.8166 | E_err:   0.009984
[2025-10-21 22:21:15] [Iter  976/2250] R2[525/600]   | LR: 0.005952 | E:  -60.858629 | E_var:     0.6833 | E_err:   0.009133
[2025-10-21 22:21:42] [Iter  977/2250] R2[526/600]   | LR: 0.005927 | E:  -60.879007 | E_var:     0.7303 | E_err:   0.009442
[2025-10-21 22:22:09] [Iter  978/2250] R2[527/600]   | LR: 0.005902 | E:  -60.859365 | E_var:     0.6080 | E_err:   0.008615
[2025-10-21 22:22:36] [Iter  979/2250] R2[528/600]   | LR: 0.005878 | E:  -60.852022 | E_var:     0.8697 | E_err:   0.010304
[2025-10-21 22:23:03] [Iter  980/2250] R2[529/600]   | LR: 0.005854 | E:  -60.854212 | E_var:     0.7206 | E_err:   0.009379
[2025-10-21 22:23:29] [Iter  981/2250] R2[530/600]   | LR: 0.005830 | E:  -60.879917 | E_var:     0.7946 | E_err:   0.009849
[2025-10-21 22:23:56] [Iter  982/2250] R2[531/600]   | LR: 0.005807 | E:  -60.859176 | E_var:     0.7160 | E_err:   0.009349
[2025-10-21 22:24:23] [Iter  983/2250] R2[532/600]   | LR: 0.005784 | E:  -60.855034 | E_var:     0.9878 | E_err:   0.010981
[2025-10-21 22:24:50] [Iter  984/2250] R2[533/600]   | LR: 0.005761 | E:  -60.874018 | E_var:     0.7684 | E_err:   0.009685
[2025-10-21 22:25:17] [Iter  985/2250] R2[534/600]   | LR: 0.005739 | E:  -60.889360 | E_var:     0.6897 | E_err:   0.009176
[2025-10-21 22:25:44] [Iter  986/2250] R2[535/600]   | LR: 0.005717 | E:  -60.879635 | E_var:     0.6535 | E_err:   0.008931
[2025-10-21 22:26:11] [Iter  987/2250] R2[536/600]   | LR: 0.005695 | E:  -60.857730 | E_var:     0.7752 | E_err:   0.009728
[2025-10-21 22:26:38] [Iter  988/2250] R2[537/600]   | LR: 0.005674 | E:  -60.875483 | E_var:     0.8112 | E_err:   0.009951
[2025-10-21 22:27:05] [Iter  989/2250] R2[538/600]   | LR: 0.005653 | E:  -60.865849 | E_var:     0.9332 | E_err:   0.010673
[2025-10-21 22:27:31] [Iter  990/2250] R2[539/600]   | LR: 0.005632 | E:  -60.878475 | E_var:     0.7233 | E_err:   0.009396
[2025-10-21 22:27:58] [Iter  991/2250] R2[540/600]   | LR: 0.005612 | E:  -60.877923 | E_var:     0.9338 | E_err:   0.010677
[2025-10-21 22:28:25] [Iter  992/2250] R2[541/600]   | LR: 0.005592 | E:  -60.865647 | E_var:     0.6892 | E_err:   0.009172
[2025-10-21 22:28:52] [Iter  993/2250] R2[542/600]   | LR: 0.005572 | E:  -60.875658 | E_var:     0.6886 | E_err:   0.009168
[2025-10-21 22:29:19] [Iter  994/2250] R2[543/600]   | LR: 0.005553 | E:  -60.873730 | E_var:     0.7241 | E_err:   0.009401
[2025-10-21 22:29:46] [Iter  995/2250] R2[544/600]   | LR: 0.005534 | E:  -60.855881 | E_var:     0.7054 | E_err:   0.009279
[2025-10-21 22:30:13] [Iter  996/2250] R2[545/600]   | LR: 0.005515 | E:  -60.865163 | E_var:     0.8578 | E_err:   0.010233
[2025-10-21 22:30:40] [Iter  997/2250] R2[546/600]   | LR: 0.005496 | E:  -60.876985 | E_var:     0.6697 | E_err:   0.009042
[2025-10-21 22:31:07] [Iter  998/2250] R2[547/600]   | LR: 0.005478 | E:  -60.884053 | E_var:     0.7370 | E_err:   0.009485
[2025-10-21 22:31:34] [Iter  999/2250] R2[548/600]   | LR: 0.005460 | E:  -60.864518 | E_var:     0.6585 | E_err:   0.008966
[2025-10-21 22:32:01] [Iter 1000/2250] R2[549/600]   | LR: 0.005443 | E:  -60.863075 | E_var:     0.7235 | E_err:   0.009398
[2025-10-21 22:32:28] [Iter 1001/2250] R2[550/600]   | LR: 0.005426 | E:  -60.874023 | E_var:     0.6597 | E_err:   0.008974
[2025-10-21 22:32:54] [Iter 1002/2250] R2[551/600]   | LR: 0.005409 | E:  -60.878673 | E_var:     0.8156 | E_err:   0.009978
[2025-10-21 22:33:21] [Iter 1003/2250] R2[552/600]   | LR: 0.005393 | E:  -60.857995 | E_var:     0.6817 | E_err:   0.009122
[2025-10-21 22:33:48] [Iter 1004/2250] R2[553/600]   | LR: 0.005377 | E:  -60.878689 | E_var:     0.6672 | E_err:   0.009025
[2025-10-21 22:34:15] [Iter 1005/2250] R2[554/600]   | LR: 0.005361 | E:  -60.861700 | E_var:     0.6946 | E_err:   0.009208
[2025-10-21 22:34:42] [Iter 1006/2250] R2[555/600]   | LR: 0.005345 | E:  -60.864506 | E_var:     0.7094 | E_err:   0.009306
[2025-10-21 22:35:09] [Iter 1007/2250] R2[556/600]   | LR: 0.005330 | E:  -60.872001 | E_var:     0.7446 | E_err:   0.009534
[2025-10-21 22:35:36] [Iter 1008/2250] R2[557/600]   | LR: 0.005315 | E:  -60.875713 | E_var:     0.6726 | E_err:   0.009061
[2025-10-21 22:36:03] [Iter 1009/2250] R2[558/600]   | LR: 0.005301 | E:  -60.847648 | E_var:     0.8079 | E_err:   0.009931
[2025-10-21 22:36:30] [Iter 1010/2250] R2[559/600]   | LR: 0.005287 | E:  -60.856738 | E_var:     0.6964 | E_err:   0.009220
[2025-10-21 22:36:56] [Iter 1011/2250] R2[560/600]   | LR: 0.005273 | E:  -60.870825 | E_var:     0.6543 | E_err:   0.008937
[2025-10-21 22:37:23] [Iter 1012/2250] R2[561/600]   | LR: 0.005260 | E:  -60.873728 | E_var:     0.7585 | E_err:   0.009622
[2025-10-21 22:37:50] [Iter 1013/2250] R2[562/600]   | LR: 0.005247 | E:  -60.868472 | E_var:     0.7732 | E_err:   0.009715
[2025-10-21 22:38:17] [Iter 1014/2250] R2[563/600]   | LR: 0.005234 | E:  -60.881228 | E_var:     0.6873 | E_err:   0.009160
[2025-10-21 22:38:44] [Iter 1015/2250] R2[564/600]   | LR: 0.005221 | E:  -60.867459 | E_var:     0.8288 | E_err:   0.010058
[2025-10-21 22:39:11] [Iter 1016/2250] R2[565/600]   | LR: 0.005209 | E:  -60.857310 | E_var:     0.6520 | E_err:   0.008921
[2025-10-21 22:39:38] [Iter 1017/2250] R2[566/600]   | LR: 0.005198 | E:  -60.870964 | E_var:     0.7930 | E_err:   0.009839
[2025-10-21 22:40:05] [Iter 1018/2250] R2[567/600]   | LR: 0.005186 | E:  -60.874448 | E_var:     0.6575 | E_err:   0.008959
[2025-10-21 22:40:31] [Iter 1019/2250] R2[568/600]   | LR: 0.005175 | E:  -60.858837 | E_var:     0.6922 | E_err:   0.009192
[2025-10-21 22:40:58] [Iter 1020/2250] R2[569/600]   | LR: 0.005164 | E:  -60.877870 | E_var:     0.6775 | E_err:   0.009094
[2025-10-21 22:41:25] [Iter 1021/2250] R2[570/600]   | LR: 0.005154 | E:  -60.850427 | E_var:     0.6522 | E_err:   0.008923
[2025-10-21 22:41:52] [Iter 1022/2250] R2[571/600]   | LR: 0.005144 | E:  -60.864974 | E_var:     0.7119 | E_err:   0.009322
[2025-10-21 22:42:19] [Iter 1023/2250] R2[572/600]   | LR: 0.005134 | E:  -60.876451 | E_var:     0.7019 | E_err:   0.009256
[2025-10-21 22:42:46] [Iter 1024/2250] R2[573/600]   | LR: 0.005125 | E:  -60.878062 | E_var:     0.6488 | E_err:   0.008900
[2025-10-21 22:43:13] [Iter 1025/2250] R2[574/600]   | LR: 0.005116 | E:  -60.881004 | E_var:     0.7568 | E_err:   0.009612
[2025-10-21 22:43:40] [Iter 1026/2250] R2[575/600]   | LR: 0.005107 | E:  -60.874006 | E_var:     0.7468 | E_err:   0.009548
[2025-10-21 22:44:07] [Iter 1027/2250] R2[576/600]   | LR: 0.005099 | E:  -60.872995 | E_var:     0.7099 | E_err:   0.009309
[2025-10-21 22:44:33] [Iter 1028/2250] R2[577/600]   | LR: 0.005091 | E:  -60.865694 | E_var:     0.6911 | E_err:   0.009185
[2025-10-21 22:45:00] [Iter 1029/2250] R2[578/600]   | LR: 0.005083 | E:  -60.869300 | E_var:     0.7107 | E_err:   0.009314
[2025-10-21 22:45:27] [Iter 1030/2250] R2[579/600]   | LR: 0.005075 | E:  -60.872127 | E_var:     0.6947 | E_err:   0.009209
[2025-10-21 22:45:54] [Iter 1031/2250] R2[580/600]   | LR: 0.005068 | E:  -60.882224 | E_var:     0.7176 | E_err:   0.009360
[2025-10-21 22:46:21] [Iter 1032/2250] R2[581/600]   | LR: 0.005062 | E:  -60.877377 | E_var:     0.6660 | E_err:   0.009017
[2025-10-21 22:46:48] [Iter 1033/2250] R2[582/600]   | LR: 0.005055 | E:  -60.869236 | E_var:     0.7581 | E_err:   0.009620
[2025-10-21 22:47:15] [Iter 1034/2250] R2[583/600]   | LR: 0.005049 | E:  -60.872282 | E_var:     0.7426 | E_err:   0.009521
[2025-10-21 22:47:42] [Iter 1035/2250] R2[584/600]   | LR: 0.005044 | E:  -60.868853 | E_var:     0.6767 | E_err:   0.009089
[2025-10-21 22:48:08] [Iter 1036/2250] R2[585/600]   | LR: 0.005039 | E:  -60.875701 | E_var:     0.6859 | E_err:   0.009150
[2025-10-21 22:48:35] [Iter 1037/2250] R2[586/600]   | LR: 0.005034 | E:  -60.881677 | E_var:     0.7165 | E_err:   0.009352
[2025-10-21 22:49:02] [Iter 1038/2250] R2[587/600]   | LR: 0.005029 | E:  -60.874996 | E_var:     0.6678 | E_err:   0.009029
[2025-10-21 22:49:29] [Iter 1039/2250] R2[588/600]   | LR: 0.005025 | E:  -60.868536 | E_var:     0.7027 | E_err:   0.009261
[2025-10-21 22:49:56] [Iter 1040/2250] R2[589/600]   | LR: 0.005021 | E:  -60.861479 | E_var:     0.7405 | E_err:   0.009508
[2025-10-21 22:50:23] [Iter 1041/2250] R2[590/600]   | LR: 0.005017 | E:  -60.871828 | E_var:     0.7136 | E_err:   0.009333
[2025-10-21 22:50:50] [Iter 1042/2250] R2[591/600]   | LR: 0.005014 | E:  -60.865359 | E_var:     0.7090 | E_err:   0.009303
[2025-10-21 22:51:17] [Iter 1043/2250] R2[592/600]   | LR: 0.005011 | E:  -60.866223 | E_var:     0.6884 | E_err:   0.009167
[2025-10-21 22:51:44] [Iter 1044/2250] R2[593/600]   | LR: 0.005008 | E:  -60.892542 | E_var:     0.9735 | E_err:   0.010901
[2025-10-21 22:52:10] [Iter 1045/2250] R2[594/600]   | LR: 0.005006 | E:  -60.868304 | E_var:     0.7314 | E_err:   0.009449
[2025-10-21 22:52:37] [Iter 1046/2250] R2[595/600]   | LR: 0.005004 | E:  -60.869942 | E_var:     0.7179 | E_err:   0.009361
[2025-10-21 22:53:04] [Iter 1047/2250] R2[596/600]   | LR: 0.005003 | E:  -60.876844 | E_var:     0.7637 | E_err:   0.009655
[2025-10-21 22:53:31] [Iter 1048/2250] R2[597/600]   | LR: 0.005002 | E:  -60.886674 | E_var:     0.7166 | E_err:   0.009353
[2025-10-21 22:53:58] [Iter 1049/2250] R2[598/600]   | LR: 0.005001 | E:  -60.862049 | E_var:     0.6662 | E_err:   0.009018
[2025-10-21 22:54:25] [Iter 1050/2250] R2[599/600]   | LR: 0.005000 | E:  -60.884931 | E_var:     0.7691 | E_err:   0.009690
[2025-10-21 22:54:25] 🔄 RESTART #3 | Period: 1200
[2025-10-21 22:54:52] [Iter 1051/2250] R3[0/1200]    | LR: 0.030000 | E:  -60.870348 | E_var:     0.7158 | E_err:   0.009348
[2025-10-21 22:55:19] [Iter 1052/2250] R3[1/1200]    | LR: 0.030000 | E:  -60.864592 | E_var:     0.7096 | E_err:   0.009307
[2025-10-21 22:55:46] [Iter 1053/2250] R3[2/1200]    | LR: 0.030000 | E:  -60.869238 | E_var:     0.8114 | E_err:   0.009952
[2025-10-21 22:56:13] [Iter 1054/2250] R3[3/1200]    | LR: 0.030000 | E:  -60.877630 | E_var:     0.6913 | E_err:   0.009186
[2025-10-21 22:56:40] [Iter 1055/2250] R3[4/1200]    | LR: 0.029999 | E:  -60.878756 | E_var:     0.7207 | E_err:   0.009380
[2025-10-21 22:57:06] [Iter 1056/2250] R3[5/1200]    | LR: 0.029999 | E:  -60.865999 | E_var:     0.7385 | E_err:   0.009495
[2025-10-21 22:57:33] [Iter 1057/2250] R3[6/1200]    | LR: 0.029998 | E:  -60.864149 | E_var:     0.6711 | E_err:   0.009051
[2025-10-21 22:58:00] [Iter 1058/2250] R3[7/1200]    | LR: 0.029998 | E:  -60.870783 | E_var:     0.7594 | E_err:   0.009628
[2025-10-21 22:58:27] [Iter 1059/2250] R3[8/1200]    | LR: 0.029997 | E:  -60.869329 | E_var:     0.8155 | E_err:   0.009977
[2025-10-21 22:58:54] [Iter 1060/2250] R3[9/1200]    | LR: 0.029997 | E:  -60.873593 | E_var:     0.6941 | E_err:   0.009205
[2025-10-21 22:59:21] [Iter 1061/2250] R3[10/1200]   | LR: 0.029996 | E:  -60.860562 | E_var:     0.6964 | E_err:   0.009220
[2025-10-21 22:59:48] [Iter 1062/2250] R3[11/1200]   | LR: 0.029995 | E:  -60.882943 | E_var:     0.6489 | E_err:   0.008900
[2025-10-21 23:00:15] [Iter 1063/2250] R3[12/1200]   | LR: 0.029994 | E:  -60.888338 | E_var:     0.7171 | E_err:   0.009356
[2025-10-21 23:00:42] [Iter 1064/2250] R3[13/1200]   | LR: 0.029993 | E:  -60.880976 | E_var:     0.7794 | E_err:   0.009754
[2025-10-21 23:01:08] [Iter 1065/2250] R3[14/1200]   | LR: 0.029992 | E:  -60.872816 | E_var:     0.6363 | E_err:   0.008813
[2025-10-21 23:01:35] [Iter 1066/2250] R3[15/1200]   | LR: 0.029990 | E:  -60.878984 | E_var:     0.7743 | E_err:   0.009722
[2025-10-21 23:02:02] [Iter 1067/2250] R3[16/1200]   | LR: 0.029989 | E:  -60.873938 | E_var:     0.6928 | E_err:   0.009196
[2025-10-21 23:02:29] [Iter 1068/2250] R3[17/1200]   | LR: 0.029988 | E:  -60.870331 | E_var:     0.8222 | E_err:   0.010018
[2025-10-21 23:02:56] [Iter 1069/2250] R3[18/1200]   | LR: 0.029986 | E:  -60.873885 | E_var:     0.6772 | E_err:   0.009092
[2025-10-21 23:03:23] [Iter 1070/2250] R3[19/1200]   | LR: 0.029985 | E:  -60.882658 | E_var:     0.7352 | E_err:   0.009473
[2025-10-21 23:03:50] [Iter 1071/2250] R3[20/1200]   | LR: 0.029983 | E:  -60.864941 | E_var:     0.6481 | E_err:   0.008895
[2025-10-21 23:04:17] [Iter 1072/2250] R3[21/1200]   | LR: 0.029981 | E:  -60.870055 | E_var:     0.8751 | E_err:   0.010336
[2025-10-21 23:04:44] [Iter 1073/2250] R3[22/1200]   | LR: 0.029979 | E:  -60.874414 | E_var:     0.6042 | E_err:   0.008588
[2025-10-21 23:05:11] [Iter 1074/2250] R3[23/1200]   | LR: 0.029977 | E:  -60.859859 | E_var:     0.6146 | E_err:   0.008661
[2025-10-21 23:05:37] [Iter 1075/2250] R3[24/1200]   | LR: 0.029975 | E:  -60.877415 | E_var:     0.6951 | E_err:   0.009211
[2025-10-21 23:06:04] [Iter 1076/2250] R3[25/1200]   | LR: 0.029973 | E:  -60.877267 | E_var:     0.6364 | E_err:   0.008814
[2025-10-21 23:06:31] [Iter 1077/2250] R3[26/1200]   | LR: 0.029971 | E:  -60.880960 | E_var:     0.8697 | E_err:   0.010304
[2025-10-21 23:06:58] [Iter 1078/2250] R3[27/1200]   | LR: 0.029969 | E:  -60.883147 | E_var:     0.7384 | E_err:   0.009494
[2025-10-21 23:07:25] [Iter 1079/2250] R3[28/1200]   | LR: 0.029966 | E:  -60.860809 | E_var:     0.6704 | E_err:   0.009046
[2025-10-21 23:07:52] [Iter 1080/2250] R3[29/1200]   | LR: 0.029964 | E:  -60.881748 | E_var:     0.6790 | E_err:   0.009104
[2025-10-21 23:08:19] [Iter 1081/2250] R3[30/1200]   | LR: 0.029961 | E:  -60.885319 | E_var:     0.8786 | E_err:   0.010356
[2025-10-21 23:08:46] [Iter 1082/2250] R3[31/1200]   | LR: 0.029959 | E:  -60.885475 | E_var:     0.6995 | E_err:   0.009241
[2025-10-21 23:09:13] [Iter 1083/2250] R3[32/1200]   | LR: 0.029956 | E:  -60.872647 | E_var:     0.7149 | E_err:   0.009342
[2025-10-21 23:09:40] [Iter 1084/2250] R3[33/1200]   | LR: 0.029953 | E:  -60.875216 | E_var:     0.7350 | E_err:   0.009472
[2025-10-21 23:10:06] [Iter 1085/2250] R3[34/1200]   | LR: 0.029951 | E:  -60.885918 | E_var:     0.7141 | E_err:   0.009336
[2025-10-21 23:10:33] [Iter 1086/2250] R3[35/1200]   | LR: 0.029948 | E:  -60.872308 | E_var:     0.7338 | E_err:   0.009464
[2025-10-21 23:11:00] [Iter 1087/2250] R3[36/1200]   | LR: 0.029945 | E:  -60.884948 | E_var:     0.8185 | E_err:   0.009996
[2025-10-21 23:11:27] [Iter 1088/2250] R3[37/1200]   | LR: 0.029941 | E:  -60.867333 | E_var:     0.7637 | E_err:   0.009655
[2025-10-21 23:11:54] [Iter 1089/2250] R3[38/1200]   | LR: 0.029938 | E:  -60.865824 | E_var:     0.7155 | E_err:   0.009346
[2025-10-21 23:12:21] [Iter 1090/2250] R3[39/1200]   | LR: 0.029935 | E:  -60.871648 | E_var:     0.6751 | E_err:   0.009078
[2025-10-21 23:12:48] [Iter 1091/2250] R3[40/1200]   | LR: 0.029932 | E:  -60.891881 | E_var:     0.7290 | E_err:   0.009433
[2025-10-21 23:13:14] [Iter 1092/2250] R3[41/1200]   | LR: 0.029928 | E:  -60.878129 | E_var:     0.6488 | E_err:   0.008899
[2025-10-21 23:13:41] [Iter 1093/2250] R3[42/1200]   | LR: 0.029925 | E:  -60.882965 | E_var:     0.7035 | E_err:   0.009267
[2025-10-21 23:14:08] [Iter 1094/2250] R3[43/1200]   | LR: 0.029921 | E:  -60.877611 | E_var:     0.6870 | E_err:   0.009157
[2025-10-21 23:14:35] [Iter 1095/2250] R3[44/1200]   | LR: 0.029917 | E:  -60.869372 | E_var:     0.7368 | E_err:   0.009484
[2025-10-21 23:15:02] [Iter 1096/2250] R3[45/1200]   | LR: 0.029913 | E:  -60.868760 | E_var:     0.6559 | E_err:   0.008948
[2025-10-21 23:15:29] [Iter 1097/2250] R3[46/1200]   | LR: 0.029909 | E:  -60.881160 | E_var:     0.6955 | E_err:   0.009214
[2025-10-21 23:15:56] [Iter 1098/2250] R3[47/1200]   | LR: 0.029905 | E:  -60.874905 | E_var:     0.6886 | E_err:   0.009168
[2025-10-21 23:16:23] [Iter 1099/2250] R3[48/1200]   | LR: 0.029901 | E:  -60.873272 | E_var:     0.6162 | E_err:   0.008673
[2025-10-21 23:16:50] [Iter 1100/2250] R3[49/1200]   | LR: 0.029897 | E:  -60.887576 | E_var:     0.6287 | E_err:   0.008760
[2025-10-21 23:17:17] [Iter 1101/2250] R3[50/1200]   | LR: 0.029893 | E:  -60.859548 | E_var:     0.6798 | E_err:   0.009109
[2025-10-21 23:17:43] [Iter 1102/2250] R3[51/1200]   | LR: 0.029889 | E:  -60.873614 | E_var:     0.7342 | E_err:   0.009467
[2025-10-21 23:18:10] [Iter 1103/2250] R3[52/1200]   | LR: 0.029884 | E:  -60.873364 | E_var:     0.6295 | E_err:   0.008766
[2025-10-21 23:18:37] [Iter 1104/2250] R3[53/1200]   | LR: 0.029880 | E:  -60.871271 | E_var:     0.6983 | E_err:   0.009233
[2025-10-21 23:19:04] [Iter 1105/2250] R3[54/1200]   | LR: 0.029875 | E:  -60.865035 | E_var:     0.7226 | E_err:   0.009392
[2025-10-21 23:19:31] [Iter 1106/2250] R3[55/1200]   | LR: 0.029871 | E:  -60.881515 | E_var:     0.7283 | E_err:   0.009429
[2025-10-21 23:19:58] [Iter 1107/2250] R3[56/1200]   | LR: 0.029866 | E:  -60.865880 | E_var:     0.7162 | E_err:   0.009350
[2025-10-21 23:20:25] [Iter 1108/2250] R3[57/1200]   | LR: 0.029861 | E:  -60.871935 | E_var:     0.7556 | E_err:   0.009604
[2025-10-21 23:20:52] [Iter 1109/2250] R3[58/1200]   | LR: 0.029856 | E:  -60.881372 | E_var:     0.7080 | E_err:   0.009296
[2025-10-21 23:21:19] [Iter 1110/2250] R3[59/1200]   | LR: 0.029851 | E:  -60.875371 | E_var:     0.7293 | E_err:   0.009436
[2025-10-21 23:21:45] [Iter 1111/2250] R3[60/1200]   | LR: 0.029846 | E:  -60.882018 | E_var:     0.7229 | E_err:   0.009394
[2025-10-21 23:22:12] [Iter 1112/2250] R3[61/1200]   | LR: 0.029841 | E:  -60.878430 | E_var:     0.7564 | E_err:   0.009609
[2025-10-21 23:22:39] [Iter 1113/2250] R3[62/1200]   | LR: 0.029836 | E:  -60.890404 | E_var:     0.9087 | E_err:   0.010532
[2025-10-21 23:23:06] [Iter 1114/2250] R3[63/1200]   | LR: 0.029830 | E:  -60.877361 | E_var:     1.1898 | E_err:   0.012052
[2025-10-21 23:23:33] [Iter 1115/2250] R3[64/1200]   | LR: 0.029825 | E:  -60.880103 | E_var:     0.6609 | E_err:   0.008982
[2025-10-21 23:24:00] [Iter 1116/2250] R3[65/1200]   | LR: 0.029819 | E:  -60.865075 | E_var:     0.6708 | E_err:   0.009049
[2025-10-21 23:24:27] [Iter 1117/2250] R3[66/1200]   | LR: 0.029814 | E:  -60.882246 | E_var:     0.7376 | E_err:   0.009489
[2025-10-21 23:24:54] [Iter 1118/2250] R3[67/1200]   | LR: 0.029808 | E:  -60.878909 | E_var:     0.7920 | E_err:   0.009833
[2025-10-21 23:25:21] [Iter 1119/2250] R3[68/1200]   | LR: 0.029802 | E:  -60.871612 | E_var:     0.7336 | E_err:   0.009463
[2025-10-21 23:25:48] [Iter 1120/2250] R3[69/1200]   | LR: 0.029797 | E:  -60.874094 | E_var:     0.7532 | E_err:   0.009589
[2025-10-21 23:26:14] [Iter 1121/2250] R3[70/1200]   | LR: 0.029791 | E:  -60.883697 | E_var:     0.6990 | E_err:   0.009237
[2025-10-21 23:26:41] [Iter 1122/2250] R3[71/1200]   | LR: 0.029785 | E:  -60.882454 | E_var:     0.8162 | E_err:   0.009982
[2025-10-21 23:27:08] [Iter 1123/2250] R3[72/1200]   | LR: 0.029779 | E:  -60.870079 | E_var:     0.7358 | E_err:   0.009477
[2025-10-21 23:27:35] [Iter 1124/2250] R3[73/1200]   | LR: 0.029772 | E:  -60.890244 | E_var:     0.7349 | E_err:   0.009472
[2025-10-21 23:28:02] [Iter 1125/2250] R3[74/1200]   | LR: 0.029766 | E:  -60.879114 | E_var:     0.6647 | E_err:   0.009008
[2025-10-21 23:28:02] ✓ Checkpoint saved: checkpoint_iter_001125.pkl
[2025-10-21 23:28:29] [Iter 1126/2250] R3[75/1200]   | LR: 0.029760 | E:  -60.886814 | E_var:     0.7065 | E_err:   0.009287
[2025-10-21 23:28:56] [Iter 1127/2250] R3[76/1200]   | LR: 0.029753 | E:  -60.875337 | E_var:     0.6923 | E_err:   0.009193
[2025-10-21 23:29:23] [Iter 1128/2250] R3[77/1200]   | LR: 0.029747 | E:  -60.879682 | E_var:     0.6798 | E_err:   0.009110
[2025-10-21 23:29:50] [Iter 1129/2250] R3[78/1200]   | LR: 0.029740 | E:  -60.859893 | E_var:     0.6756 | E_err:   0.009081
[2025-10-21 23:30:17] [Iter 1130/2250] R3[79/1200]   | LR: 0.029734 | E:  -60.864746 | E_var:     0.7295 | E_err:   0.009437
[2025-10-21 23:30:43] [Iter 1131/2250] R3[80/1200]   | LR: 0.029727 | E:  -60.872319 | E_var:     0.7292 | E_err:   0.009434
[2025-10-21 23:31:10] [Iter 1132/2250] R3[81/1200]   | LR: 0.029720 | E:  -60.878708 | E_var:     0.6652 | E_err:   0.009011
[2025-10-21 23:31:37] [Iter 1133/2250] R3[82/1200]   | LR: 0.029713 | E:  -60.888714 | E_var:     0.6627 | E_err:   0.008995
[2025-10-21 23:32:04] [Iter 1134/2250] R3[83/1200]   | LR: 0.029706 | E:  -60.886144 | E_var:     0.6267 | E_err:   0.008747
[2025-10-21 23:32:31] [Iter 1135/2250] R3[84/1200]   | LR: 0.029699 | E:  -60.882581 | E_var:     0.8155 | E_err:   0.009978
[2025-10-21 23:32:58] [Iter 1136/2250] R3[85/1200]   | LR: 0.029692 | E:  -60.869769 | E_var:     0.6819 | E_err:   0.009124
[2025-10-21 23:33:25] [Iter 1137/2250] R3[86/1200]   | LR: 0.029685 | E:  -60.867670 | E_var:     0.6667 | E_err:   0.009021
[2025-10-21 23:33:52] [Iter 1138/2250] R3[87/1200]   | LR: 0.029677 | E:  -60.858788 | E_var:     0.6524 | E_err:   0.008924
[2025-10-21 23:34:18] [Iter 1139/2250] R3[88/1200]   | LR: 0.029670 | E:  -60.884420 | E_var:     1.0012 | E_err:   0.011055
[2025-10-21 23:34:45] [Iter 1140/2250] R3[89/1200]   | LR: 0.029662 | E:  -60.872034 | E_var:     0.8520 | E_err:   0.010198
[2025-10-21 23:35:12] [Iter 1141/2250] R3[90/1200]   | LR: 0.029655 | E:  -60.873863 | E_var:     0.7043 | E_err:   0.009272
[2025-10-21 23:35:39] [Iter 1142/2250] R3[91/1200]   | LR: 0.029647 | E:  -60.883825 | E_var:     0.6546 | E_err:   0.008939
[2025-10-21 23:36:06] [Iter 1143/2250] R3[92/1200]   | LR: 0.029639 | E:  -60.887259 | E_var:     0.6666 | E_err:   0.009021
[2025-10-21 23:36:33] [Iter 1144/2250] R3[93/1200]   | LR: 0.029631 | E:  -60.876937 | E_var:     0.6049 | E_err:   0.008593
[2025-10-21 23:37:00] [Iter 1145/2250] R3[94/1200]   | LR: 0.029623 | E:  -60.879872 | E_var:     0.7046 | E_err:   0.009274
[2025-10-21 23:37:26] [Iter 1146/2250] R3[95/1200]   | LR: 0.029615 | E:  -60.876458 | E_var:     0.6922 | E_err:   0.009192
[2025-10-21 23:37:53] [Iter 1147/2250] R3[96/1200]   | LR: 0.029607 | E:  -60.882986 | E_var:     0.8066 | E_err:   0.009923
[2025-10-21 23:38:20] [Iter 1148/2250] R3[97/1200]   | LR: 0.029599 | E:  -60.870672 | E_var:     1.1401 | E_err:   0.011797
[2025-10-21 23:38:47] [Iter 1149/2250] R3[98/1200]   | LR: 0.029591 | E:  -60.857305 | E_var:     0.7769 | E_err:   0.009739
[2025-10-21 23:39:14] [Iter 1150/2250] R3[99/1200]   | LR: 0.029583 | E:  -60.875367 | E_var:     0.8218 | E_err:   0.010016
[2025-10-21 23:39:41] [Iter 1151/2250] R3[100/1200]  | LR: 0.029574 | E:  -60.869982 | E_var:     0.7405 | E_err:   0.009507
[2025-10-21 23:40:08] [Iter 1152/2250] R3[101/1200]  | LR: 0.029566 | E:  -60.866809 | E_var:     0.8007 | E_err:   0.009886
[2025-10-21 23:40:35] [Iter 1153/2250] R3[102/1200]  | LR: 0.029557 | E:  -60.866885 | E_var:     0.6078 | E_err:   0.008614
[2025-10-21 23:41:02] [Iter 1154/2250] R3[103/1200]  | LR: 0.029548 | E:  -60.886713 | E_var:     0.7005 | E_err:   0.009247
[2025-10-21 23:41:29] [Iter 1155/2250] R3[104/1200]  | LR: 0.029540 | E:  -60.870335 | E_var:     0.7085 | E_err:   0.009300
[2025-10-21 23:41:55] [Iter 1156/2250] R3[105/1200]  | LR: 0.029531 | E:  -60.862554 | E_var:     0.6250 | E_err:   0.008735
[2025-10-21 23:42:22] [Iter 1157/2250] R3[106/1200]  | LR: 0.029522 | E:  -60.871829 | E_var:     0.6489 | E_err:   0.008900
[2025-10-21 23:42:49] [Iter 1158/2250] R3[107/1200]  | LR: 0.029513 | E:  -60.891460 | E_var:     0.8682 | E_err:   0.010295
[2025-10-21 23:43:16] [Iter 1159/2250] R3[108/1200]  | LR: 0.029504 | E:  -60.883280 | E_var:     0.7227 | E_err:   0.009392
[2025-10-21 23:43:43] [Iter 1160/2250] R3[109/1200]  | LR: 0.029494 | E:  -60.891969 | E_var:     0.8517 | E_err:   0.010196
[2025-10-21 23:44:10] [Iter 1161/2250] R3[110/1200]  | LR: 0.029485 | E:  -60.866580 | E_var:     0.7520 | E_err:   0.009581
[2025-10-21 23:44:37] [Iter 1162/2250] R3[111/1200]  | LR: 0.029476 | E:  -60.882606 | E_var:     0.8690 | E_err:   0.010299
[2025-10-21 23:45:04] [Iter 1163/2250] R3[112/1200]  | LR: 0.029466 | E:  -60.887697 | E_var:     0.6787 | E_err:   0.009102
[2025-10-21 23:45:31] [Iter 1164/2250] R3[113/1200]  | LR: 0.029457 | E:  -60.901338 | E_var:     0.6844 | E_err:   0.009141
[2025-10-21 23:45:58] [Iter 1165/2250] R3[114/1200]  | LR: 0.029447 | E:  -60.872353 | E_var:     0.7085 | E_err:   0.009300
[2025-10-21 23:46:24] [Iter 1166/2250] R3[115/1200]  | LR: 0.029438 | E:  -60.874248 | E_var:     0.7660 | E_err:   0.009670
[2025-10-21 23:46:51] [Iter 1167/2250] R3[116/1200]  | LR: 0.029428 | E:  -60.870886 | E_var:     0.6741 | E_err:   0.009071
[2025-10-21 23:47:18] [Iter 1168/2250] R3[117/1200]  | LR: 0.029418 | E:  -60.873845 | E_var:     0.7213 | E_err:   0.009384
[2025-10-21 23:47:45] [Iter 1169/2250] R3[118/1200]  | LR: 0.029408 | E:  -60.865244 | E_var:     0.7659 | E_err:   0.009669
[2025-10-21 23:48:12] [Iter 1170/2250] R3[119/1200]  | LR: 0.029398 | E:  -60.858947 | E_var:     0.7099 | E_err:   0.009309
[2025-10-21 23:48:39] [Iter 1171/2250] R3[120/1200]  | LR: 0.029388 | E:  -60.891487 | E_var:     0.7410 | E_err:   0.009511
[2025-10-21 23:49:06] [Iter 1172/2250] R3[121/1200]  | LR: 0.029378 | E:  -60.873581 | E_var:     0.8270 | E_err:   0.010048
[2025-10-21 23:49:33] [Iter 1173/2250] R3[122/1200]  | LR: 0.029368 | E:  -60.874899 | E_var:     0.6375 | E_err:   0.008821
[2025-10-21 23:50:00] [Iter 1174/2250] R3[123/1200]  | LR: 0.029358 | E:  -60.879655 | E_var:     0.6924 | E_err:   0.009194
[2025-10-21 23:50:27] [Iter 1175/2250] R3[124/1200]  | LR: 0.029347 | E:  -60.873611 | E_var:     0.6394 | E_err:   0.008835
[2025-10-21 23:50:53] [Iter 1176/2250] R3[125/1200]  | LR: 0.029337 | E:  -60.890032 | E_var:     0.7339 | E_err:   0.009465
[2025-10-21 23:51:20] [Iter 1177/2250] R3[126/1200]  | LR: 0.029326 | E:  -60.869438 | E_var:     0.6915 | E_err:   0.009188
[2025-10-21 23:51:47] [Iter 1178/2250] R3[127/1200]  | LR: 0.029315 | E:  -60.886207 | E_var:     0.7095 | E_err:   0.009306
[2025-10-21 23:52:14] [Iter 1179/2250] R3[128/1200]  | LR: 0.029305 | E:  -60.887606 | E_var:     0.7725 | E_err:   0.009710
[2025-10-21 23:52:41] [Iter 1180/2250] R3[129/1200]  | LR: 0.029294 | E:  -60.885497 | E_var:     0.6895 | E_err:   0.009174
[2025-10-21 23:53:08] [Iter 1181/2250] R3[130/1200]  | LR: 0.029283 | E:  -60.880605 | E_var:     0.7117 | E_err:   0.009321
[2025-10-21 23:53:35] [Iter 1182/2250] R3[131/1200]  | LR: 0.029272 | E:  -60.858782 | E_var:     0.8592 | E_err:   0.010241
[2025-10-21 23:54:02] [Iter 1183/2250] R3[132/1200]  | LR: 0.029261 | E:  -60.871915 | E_var:     0.7274 | E_err:   0.009423
[2025-10-21 23:54:29] [Iter 1184/2250] R3[133/1200]  | LR: 0.029250 | E:  -60.883496 | E_var:     0.8135 | E_err:   0.009965
[2025-10-21 23:54:56] [Iter 1185/2250] R3[134/1200]  | LR: 0.029239 | E:  -60.874517 | E_var:     0.7613 | E_err:   0.009640
[2025-10-21 23:55:22] [Iter 1186/2250] R3[135/1200]  | LR: 0.029227 | E:  -60.895905 | E_var:     0.6549 | E_err:   0.008941
[2025-10-21 23:55:49] [Iter 1187/2250] R3[136/1200]  | LR: 0.029216 | E:  -60.878341 | E_var:     0.6206 | E_err:   0.008704
[2025-10-21 23:56:16] [Iter 1188/2250] R3[137/1200]  | LR: 0.029205 | E:  -60.861801 | E_var:     0.6824 | E_err:   0.009127
[2025-10-21 23:56:43] [Iter 1189/2250] R3[138/1200]  | LR: 0.029193 | E:  -60.869658 | E_var:     0.6787 | E_err:   0.009102
[2025-10-21 23:57:10] [Iter 1190/2250] R3[139/1200]  | LR: 0.029181 | E:  -60.867908 | E_var:     0.6828 | E_err:   0.009129
[2025-10-21 23:57:37] [Iter 1191/2250] R3[140/1200]  | LR: 0.029170 | E:  -60.880656 | E_var:     0.6815 | E_err:   0.009121
[2025-10-21 23:58:04] [Iter 1192/2250] R3[141/1200]  | LR: 0.029158 | E:  -60.883735 | E_var:     0.6776 | E_err:   0.009095
[2025-10-21 23:58:31] [Iter 1193/2250] R3[142/1200]  | LR: 0.029146 | E:  -60.881070 | E_var:     0.6475 | E_err:   0.008890
[2025-10-21 23:58:58] [Iter 1194/2250] R3[143/1200]  | LR: 0.029134 | E:  -60.897077 | E_var:     0.7145 | E_err:   0.009339
[2025-10-21 23:59:25] [Iter 1195/2250] R3[144/1200]  | LR: 0.029122 | E:  -60.879123 | E_var:     0.6567 | E_err:   0.008953
[2025-10-21 23:59:51] [Iter 1196/2250] R3[145/1200]  | LR: 0.029110 | E:  -60.879965 | E_var:     0.7912 | E_err:   0.009828
[2025-10-22 00:00:18] [Iter 1197/2250] R3[146/1200]  | LR: 0.029098 | E:  -60.877820 | E_var:     0.6884 | E_err:   0.009167
[2025-10-22 00:00:45] [Iter 1198/2250] R3[147/1200]  | LR: 0.029086 | E:  -60.869195 | E_var:     0.7669 | E_err:   0.009676
[2025-10-22 00:01:12] [Iter 1199/2250] R3[148/1200]  | LR: 0.029073 | E:  -60.879552 | E_var:     0.5758 | E_err:   0.008384
[2025-10-22 00:01:39] [Iter 1200/2250] R3[149/1200]  | LR: 0.029061 | E:  -60.871681 | E_var:     0.7356 | E_err:   0.009476
[2025-10-22 00:02:06] [Iter 1201/2250] R3[150/1200]  | LR: 0.029048 | E:  -60.894745 | E_var:     0.8369 | E_err:   0.010107
[2025-10-22 00:02:33] [Iter 1202/2250] R3[151/1200]  | LR: 0.029036 | E:  -60.875523 | E_var:     0.7125 | E_err:   0.009326
[2025-10-22 00:03:00] [Iter 1203/2250] R3[152/1200]  | LR: 0.029023 | E:  -60.893260 | E_var:     0.8487 | E_err:   0.010178
[2025-10-22 00:03:27] [Iter 1204/2250] R3[153/1200]  | LR: 0.029011 | E:  -60.887741 | E_var:     0.7087 | E_err:   0.009301
[2025-10-22 00:03:54] [Iter 1205/2250] R3[154/1200]  | LR: 0.028998 | E:  -60.894223 | E_var:     0.7151 | E_err:   0.009343
[2025-10-22 00:04:20] [Iter 1206/2250] R3[155/1200]  | LR: 0.028985 | E:  -60.877304 | E_var:     0.6386 | E_err:   0.008829
[2025-10-22 00:04:48] [Iter 1207/2250] R3[156/1200]  | LR: 0.028972 | E:  -60.890015 | E_var:     0.7183 | E_err:   0.009364
[2025-10-22 00:05:15] [Iter 1208/2250] R3[157/1200]  | LR: 0.028959 | E:  -60.870825 | E_var:     0.7680 | E_err:   0.009682
[2025-10-22 00:05:41] [Iter 1209/2250] R3[158/1200]  | LR: 0.028946 | E:  -60.868559 | E_var:     0.6613 | E_err:   0.008985
[2025-10-22 00:06:08] [Iter 1210/2250] R3[159/1200]  | LR: 0.028933 | E:  -60.910907 | E_var:     0.7434 | E_err:   0.009526
[2025-10-22 00:06:35] [Iter 1211/2250] R3[160/1200]  | LR: 0.028919 | E:  -60.888830 | E_var:     0.6282 | E_err:   0.008757
[2025-10-22 00:07:02] [Iter 1212/2250] R3[161/1200]  | LR: 0.028906 | E:  -60.874149 | E_var:     0.6651 | E_err:   0.009011
[2025-10-22 00:07:29] [Iter 1213/2250] R3[162/1200]  | LR: 0.028893 | E:  -60.894933 | E_var:     0.7213 | E_err:   0.009383
[2025-10-22 00:07:56] [Iter 1214/2250] R3[163/1200]  | LR: 0.028879 | E:  -60.882346 | E_var:     0.6586 | E_err:   0.008967
[2025-10-22 00:08:23] [Iter 1215/2250] R3[164/1200]  | LR: 0.028865 | E:  -60.885047 | E_var:     0.7181 | E_err:   0.009363
[2025-10-22 00:08:50] [Iter 1216/2250] R3[165/1200]  | LR: 0.028852 | E:  -60.880576 | E_var:     0.6475 | E_err:   0.008891
[2025-10-22 00:09:17] [Iter 1217/2250] R3[166/1200]  | LR: 0.028838 | E:  -60.872270 | E_var:     0.9373 | E_err:   0.010697
[2025-10-22 00:09:44] [Iter 1218/2250] R3[167/1200]  | LR: 0.028824 | E:  -60.891096 | E_var:     0.6627 | E_err:   0.008994
[2025-10-22 00:10:10] [Iter 1219/2250] R3[168/1200]  | LR: 0.028810 | E:  -60.869867 | E_var:     0.7317 | E_err:   0.009451
[2025-10-22 00:10:37] [Iter 1220/2250] R3[169/1200]  | LR: 0.028796 | E:  -60.876211 | E_var:     0.7119 | E_err:   0.009322
[2025-10-22 00:11:04] [Iter 1221/2250] R3[170/1200]  | LR: 0.028782 | E:  -60.876815 | E_var:     0.6730 | E_err:   0.009064
[2025-10-22 00:11:31] [Iter 1222/2250] R3[171/1200]  | LR: 0.028768 | E:  -60.898780 | E_var:     0.6697 | E_err:   0.009041
[2025-10-22 00:11:58] [Iter 1223/2250] R3[172/1200]  | LR: 0.028754 | E:  -60.898479 | E_var:     0.7418 | E_err:   0.009516
[2025-10-22 00:12:25] [Iter 1224/2250] R3[173/1200]  | LR: 0.028740 | E:  -60.878433 | E_var:     0.6546 | E_err:   0.008939
[2025-10-22 00:12:52] [Iter 1225/2250] R3[174/1200]  | LR: 0.028725 | E:  -60.894740 | E_var:     0.6573 | E_err:   0.008958
[2025-10-22 00:13:19] [Iter 1226/2250] R3[175/1200]  | LR: 0.028711 | E:  -60.861730 | E_var:     0.7708 | E_err:   0.009700
[2025-10-22 00:13:46] [Iter 1227/2250] R3[176/1200]  | LR: 0.028696 | E:  -60.881011 | E_var:     0.6615 | E_err:   0.008986
[2025-10-22 00:14:13] [Iter 1228/2250] R3[177/1200]  | LR: 0.028682 | E:  -60.874802 | E_var:     0.6610 | E_err:   0.008982
[2025-10-22 00:14:39] [Iter 1229/2250] R3[178/1200]  | LR: 0.028667 | E:  -60.884291 | E_var:     0.7012 | E_err:   0.009252
[2025-10-22 00:15:06] [Iter 1230/2250] R3[179/1200]  | LR: 0.028652 | E:  -60.881501 | E_var:     0.7162 | E_err:   0.009350
[2025-10-22 00:15:33] [Iter 1231/2250] R3[180/1200]  | LR: 0.028638 | E:  -60.886332 | E_var:     0.6969 | E_err:   0.009223
[2025-10-22 00:16:00] [Iter 1232/2250] R3[181/1200]  | LR: 0.028623 | E:  -60.901482 | E_var:     0.7194 | E_err:   0.009371
[2025-10-22 00:16:27] [Iter 1233/2250] R3[182/1200]  | LR: 0.028608 | E:  -60.873868 | E_var:     0.7993 | E_err:   0.009878
[2025-10-22 00:16:54] [Iter 1234/2250] R3[183/1200]  | LR: 0.028593 | E:  -60.895973 | E_var:     0.6350 | E_err:   0.008804
[2025-10-22 00:17:21] [Iter 1235/2250] R3[184/1200]  | LR: 0.028578 | E:  -60.904013 | E_var:     0.7504 | E_err:   0.009571
[2025-10-22 00:17:48] [Iter 1236/2250] R3[185/1200]  | LR: 0.028562 | E:  -60.887164 | E_var:     0.6663 | E_err:   0.009018
[2025-10-22 00:18:15] [Iter 1237/2250] R3[186/1200]  | LR: 0.028547 | E:  -60.867351 | E_var:     0.8320 | E_err:   0.010078
[2025-10-22 00:18:42] [Iter 1238/2250] R3[187/1200]  | LR: 0.028532 | E:  -60.888119 | E_var:     0.9425 | E_err:   0.010726
[2025-10-22 00:19:09] [Iter 1239/2250] R3[188/1200]  | LR: 0.028516 | E:  -60.874478 | E_var:     0.8951 | E_err:   0.010453
[2025-10-22 00:19:36] [Iter 1240/2250] R3[189/1200]  | LR: 0.028501 | E:  -60.872695 | E_var:     0.5952 | E_err:   0.008524
[2025-10-22 00:20:02] [Iter 1241/2250] R3[190/1200]  | LR: 0.028485 | E:  -60.882503 | E_var:     0.6811 | E_err:   0.009118
[2025-10-22 00:20:29] [Iter 1242/2250] R3[191/1200]  | LR: 0.028470 | E:  -60.865628 | E_var:     0.6601 | E_err:   0.008976
[2025-10-22 00:20:57] [Iter 1243/2250] R3[192/1200]  | LR: 0.028454 | E:  -60.866359 | E_var:     0.6676 | E_err:   0.009027
[2025-10-22 00:21:24] [Iter 1244/2250] R3[193/1200]  | LR: 0.028438 | E:  -60.886469 | E_var:     0.7657 | E_err:   0.009668
[2025-10-22 00:21:50] [Iter 1245/2250] R3[194/1200]  | LR: 0.028422 | E:  -60.894164 | E_var:     0.7387 | E_err:   0.009496
[2025-10-22 00:22:17] [Iter 1246/2250] R3[195/1200]  | LR: 0.028406 | E:  -60.886993 | E_var:     0.7158 | E_err:   0.009348
[2025-10-22 00:22:44] [Iter 1247/2250] R3[196/1200]  | LR: 0.028390 | E:  -60.877970 | E_var:     0.6619 | E_err:   0.008989
[2025-10-22 00:23:11] [Iter 1248/2250] R3[197/1200]  | LR: 0.028374 | E:  -60.898671 | E_var:     0.8685 | E_err:   0.010296
[2025-10-22 00:23:38] [Iter 1249/2250] R3[198/1200]  | LR: 0.028358 | E:  -60.874354 | E_var:     0.6491 | E_err:   0.008901
[2025-10-22 00:24:05] [Iter 1250/2250] R3[199/1200]  | LR: 0.028342 | E:  -60.889326 | E_var:     0.6771 | E_err:   0.009091
[2025-10-22 00:24:32] [Iter 1251/2250] R3[200/1200]  | LR: 0.028325 | E:  -60.884048 | E_var:     0.6738 | E_err:   0.009069
[2025-10-22 00:24:59] [Iter 1252/2250] R3[201/1200]  | LR: 0.028309 | E:  -60.889558 | E_var:     0.6686 | E_err:   0.009034
[2025-10-22 00:25:26] [Iter 1253/2250] R3[202/1200]  | LR: 0.028292 | E:  -60.875982 | E_var:     0.7292 | E_err:   0.009435
[2025-10-22 00:25:53] [Iter 1254/2250] R3[203/1200]  | LR: 0.028276 | E:  -60.891011 | E_var:     0.6194 | E_err:   0.008695
[2025-10-22 00:26:20] [Iter 1255/2250] R3[204/1200]  | LR: 0.028259 | E:  -60.890264 | E_var:     0.7436 | E_err:   0.009527
[2025-10-22 00:26:47] [Iter 1256/2250] R3[205/1200]  | LR: 0.028243 | E:  -60.895672 | E_var:     0.6650 | E_err:   0.009010
[2025-10-22 00:27:14] [Iter 1257/2250] R3[206/1200]  | LR: 0.028226 | E:  -60.881718 | E_var:     0.7144 | E_err:   0.009338
[2025-10-22 00:27:41] [Iter 1258/2250] R3[207/1200]  | LR: 0.028209 | E:  -60.900003 | E_var:     0.6902 | E_err:   0.009179
[2025-10-22 00:28:08] [Iter 1259/2250] R3[208/1200]  | LR: 0.028192 | E:  -60.869475 | E_var:     0.9747 | E_err:   0.010908
[2025-10-22 00:28:34] [Iter 1260/2250] R3[209/1200]  | LR: 0.028175 | E:  -60.883422 | E_var:     0.6527 | E_err:   0.008926
[2025-10-22 00:29:01] [Iter 1261/2250] R3[210/1200]  | LR: 0.028158 | E:  -60.880619 | E_var:     0.7532 | E_err:   0.009589
[2025-10-22 00:29:28] [Iter 1262/2250] R3[211/1200]  | LR: 0.028141 | E:  -60.880813 | E_var:     0.7580 | E_err:   0.009619
[2025-10-22 00:29:55] [Iter 1263/2250] R3[212/1200]  | LR: 0.028124 | E:  -60.884841 | E_var:     0.8204 | E_err:   0.010007
[2025-10-22 00:30:22] [Iter 1264/2250] R3[213/1200]  | LR: 0.028106 | E:  -60.880142 | E_var:     0.6892 | E_err:   0.009172
[2025-10-22 00:30:49] [Iter 1265/2250] R3[214/1200]  | LR: 0.028089 | E:  -60.898006 | E_var:     0.6952 | E_err:   0.009212
[2025-10-22 00:31:16] [Iter 1266/2250] R3[215/1200]  | LR: 0.028072 | E:  -60.889032 | E_var:     0.6844 | E_err:   0.009140
[2025-10-22 00:31:43] [Iter 1267/2250] R3[216/1200]  | LR: 0.028054 | E:  -60.864017 | E_var:     0.6522 | E_err:   0.008923
[2025-10-22 00:32:10] [Iter 1268/2250] R3[217/1200]  | LR: 0.028037 | E:  -60.896578 | E_var:     0.6798 | E_err:   0.009110
[2025-10-22 00:32:36] [Iter 1269/2250] R3[218/1200]  | LR: 0.028019 | E:  -60.882378 | E_var:     0.6595 | E_err:   0.008973
[2025-10-22 00:33:03] [Iter 1270/2250] R3[219/1200]  | LR: 0.028001 | E:  -60.897853 | E_var:     0.6548 | E_err:   0.008940
[2025-10-22 00:33:30] [Iter 1271/2250] R3[220/1200]  | LR: 0.027983 | E:  -60.886173 | E_var:     0.7216 | E_err:   0.009385
[2025-10-22 00:33:57] [Iter 1272/2250] R3[221/1200]  | LR: 0.027966 | E:  -60.878282 | E_var:     0.6610 | E_err:   0.008983
[2025-10-22 00:34:24] [Iter 1273/2250] R3[222/1200]  | LR: 0.027948 | E:  -60.870501 | E_var:     0.7318 | E_err:   0.009452
[2025-10-22 00:34:51] [Iter 1274/2250] R3[223/1200]  | LR: 0.027930 | E:  -60.877768 | E_var:     0.6780 | E_err:   0.009098
[2025-10-22 00:35:18] [Iter 1275/2250] R3[224/1200]  | LR: 0.027912 | E:  -60.888557 | E_var:     0.6185 | E_err:   0.008689
[2025-10-22 00:35:45] [Iter 1276/2250] R3[225/1200]  | LR: 0.027893 | E:  -60.864501 | E_var:     0.6996 | E_err:   0.009241
[2025-10-22 00:36:12] [Iter 1277/2250] R3[226/1200]  | LR: 0.027875 | E:  -60.903562 | E_var:     0.7450 | E_err:   0.009537
[2025-10-22 00:36:39] [Iter 1278/2250] R3[227/1200]  | LR: 0.027857 | E:  -60.874609 | E_var:     0.7356 | E_err:   0.009476
[2025-10-22 00:37:05] [Iter 1279/2250] R3[228/1200]  | LR: 0.027839 | E:  -60.878037 | E_var:     0.8694 | E_err:   0.010302
[2025-10-22 00:37:32] [Iter 1280/2250] R3[229/1200]  | LR: 0.027820 | E:  -60.898228 | E_var:     0.6570 | E_err:   0.008955
[2025-10-22 00:37:59] [Iter 1281/2250] R3[230/1200]  | LR: 0.027802 | E:  -60.880495 | E_var:     0.7393 | E_err:   0.009500
[2025-10-22 00:38:26] [Iter 1282/2250] R3[231/1200]  | LR: 0.027783 | E:  -60.874555 | E_var:     0.5754 | E_err:   0.008381
[2025-10-22 00:38:53] [Iter 1283/2250] R3[232/1200]  | LR: 0.027764 | E:  -60.892222 | E_var:     0.8756 | E_err:   0.010339
[2025-10-22 00:39:20] [Iter 1284/2250] R3[233/1200]  | LR: 0.027746 | E:  -60.883828 | E_var:     0.6734 | E_err:   0.009067
[2025-10-22 00:39:47] [Iter 1285/2250] R3[234/1200]  | LR: 0.027727 | E:  -60.883508 | E_var:     0.6246 | E_err:   0.008732
[2025-10-22 00:40:14] [Iter 1286/2250] R3[235/1200]  | LR: 0.027708 | E:  -60.868096 | E_var:     0.7450 | E_err:   0.009537
[2025-10-22 00:40:41] [Iter 1287/2250] R3[236/1200]  | LR: 0.027689 | E:  -60.881348 | E_var:     0.6295 | E_err:   0.008766
[2025-10-22 00:41:08] [Iter 1288/2250] R3[237/1200]  | LR: 0.027670 | E:  -60.882288 | E_var:     0.6429 | E_err:   0.008859
[2025-10-22 00:41:35] [Iter 1289/2250] R3[238/1200]  | LR: 0.027651 | E:  -60.881187 | E_var:     0.6370 | E_err:   0.008818
[2025-10-22 00:42:02] [Iter 1290/2250] R3[239/1200]  | LR: 0.027632 | E:  -60.885937 | E_var:     0.6290 | E_err:   0.008762
[2025-10-22 00:42:29] [Iter 1291/2250] R3[240/1200]  | LR: 0.027613 | E:  -60.884787 | E_var:     0.7327 | E_err:   0.009457
[2025-10-22 00:42:56] [Iter 1292/2250] R3[241/1200]  | LR: 0.027593 | E:  -60.897035 | E_var:     1.2111 | E_err:   0.012159
[2025-10-22 00:43:23] [Iter 1293/2250] R3[242/1200]  | LR: 0.027574 | E:  -60.882377 | E_var:     0.6658 | E_err:   0.009015
[2025-10-22 00:43:49] [Iter 1294/2250] R3[243/1200]  | LR: 0.027555 | E:  -60.895467 | E_var:     0.7055 | E_err:   0.009280
[2025-10-22 00:44:16] [Iter 1295/2250] R3[244/1200]  | LR: 0.027535 | E:  -60.882727 | E_var:     0.6402 | E_err:   0.008840
[2025-10-22 00:44:43] [Iter 1296/2250] R3[245/1200]  | LR: 0.027516 | E:  -60.873993 | E_var:     0.6770 | E_err:   0.009091
[2025-10-22 00:45:10] [Iter 1297/2250] R3[246/1200]  | LR: 0.027496 | E:  -60.886288 | E_var:     0.6327 | E_err:   0.008788
[2025-10-22 00:45:37] [Iter 1298/2250] R3[247/1200]  | LR: 0.027476 | E:  -60.871318 | E_var:     0.7666 | E_err:   0.009674
[2025-10-22 00:46:04] [Iter 1299/2250] R3[248/1200]  | LR: 0.027457 | E:  -60.882150 | E_var:     0.5869 | E_err:   0.008465
[2025-10-22 00:46:31] [Iter 1300/2250] R3[249/1200]  | LR: 0.027437 | E:  -60.894009 | E_var:     0.7075 | E_err:   0.009294
[2025-10-22 00:46:58] [Iter 1301/2250] R3[250/1200]  | LR: 0.027417 | E:  -60.882776 | E_var:     0.7365 | E_err:   0.009482
[2025-10-22 00:47:24] [Iter 1302/2250] R3[251/1200]  | LR: 0.027397 | E:  -60.902611 | E_var:     0.7388 | E_err:   0.009497
[2025-10-22 00:47:51] [Iter 1303/2250] R3[252/1200]  | LR: 0.027377 | E:  -60.884551 | E_var:     0.6888 | E_err:   0.009169
[2025-10-22 00:48:18] [Iter 1304/2250] R3[253/1200]  | LR: 0.027357 | E:  -60.876511 | E_var:     0.6023 | E_err:   0.008575
[2025-10-22 00:48:45] [Iter 1305/2250] R3[254/1200]  | LR: 0.027337 | E:  -60.890314 | E_var:     0.6436 | E_err:   0.008863
[2025-10-22 00:49:12] [Iter 1306/2250] R3[255/1200]  | LR: 0.027316 | E:  -60.886735 | E_var:     0.7261 | E_err:   0.009415
[2025-10-22 00:49:39] [Iter 1307/2250] R3[256/1200]  | LR: 0.027296 | E:  -60.897334 | E_var:     0.9233 | E_err:   0.010616
[2025-10-22 00:50:06] [Iter 1308/2250] R3[257/1200]  | LR: 0.027276 | E:  -60.883196 | E_var:     0.6967 | E_err:   0.009222
[2025-10-22 00:50:33] [Iter 1309/2250] R3[258/1200]  | LR: 0.027255 | E:  -60.887447 | E_var:     0.6655 | E_err:   0.009013
[2025-10-22 00:51:00] [Iter 1310/2250] R3[259/1200]  | LR: 0.027235 | E:  -60.885851 | E_var:     0.7129 | E_err:   0.009328
[2025-10-22 00:51:26] [Iter 1311/2250] R3[260/1200]  | LR: 0.027214 | E:  -60.902455 | E_var:     0.6369 | E_err:   0.008818
[2025-10-22 00:51:53] [Iter 1312/2250] R3[261/1200]  | LR: 0.027194 | E:  -60.890052 | E_var:     0.6896 | E_err:   0.009175
[2025-10-22 00:52:20] [Iter 1313/2250] R3[262/1200]  | LR: 0.027173 | E:  -60.899687 | E_var:     0.6855 | E_err:   0.009148
[2025-10-22 00:52:47] [Iter 1314/2250] R3[263/1200]  | LR: 0.027152 | E:  -60.886714 | E_var:     0.7191 | E_err:   0.009369
[2025-10-22 00:53:14] [Iter 1315/2250] R3[264/1200]  | LR: 0.027131 | E:  -60.891151 | E_var:     0.6884 | E_err:   0.009167
[2025-10-22 00:53:41] [Iter 1316/2250] R3[265/1200]  | LR: 0.027111 | E:  -60.875483 | E_var:     0.6330 | E_err:   0.008790
[2025-10-22 00:54:08] [Iter 1317/2250] R3[266/1200]  | LR: 0.027090 | E:  -60.895144 | E_var:     0.6663 | E_err:   0.009019
[2025-10-22 00:54:35] [Iter 1318/2250] R3[267/1200]  | LR: 0.027069 | E:  -60.888617 | E_var:     0.7281 | E_err:   0.009427
[2025-10-22 00:55:02] [Iter 1319/2250] R3[268/1200]  | LR: 0.027047 | E:  -60.879583 | E_var:     0.6398 | E_err:   0.008838
[2025-10-22 00:55:29] [Iter 1320/2250] R3[269/1200]  | LR: 0.027026 | E:  -60.889125 | E_var:     0.7618 | E_err:   0.009643
[2025-10-22 00:55:55] [Iter 1321/2250] R3[270/1200]  | LR: 0.027005 | E:  -60.882022 | E_var:     0.7845 | E_err:   0.009786
[2025-10-22 00:56:22] [Iter 1322/2250] R3[271/1200]  | LR: 0.026984 | E:  -60.889123 | E_var:     0.6185 | E_err:   0.008689
[2025-10-22 00:56:49] [Iter 1323/2250] R3[272/1200]  | LR: 0.026962 | E:  -60.880150 | E_var:     0.7138 | E_err:   0.009335
[2025-10-22 00:57:16] [Iter 1324/2250] R3[273/1200]  | LR: 0.026941 | E:  -60.874314 | E_var:     1.0829 | E_err:   0.011497
[2025-10-22 00:57:43] [Iter 1325/2250] R3[274/1200]  | LR: 0.026920 | E:  -60.890648 | E_var:     0.6718 | E_err:   0.009055
[2025-10-22 00:58:10] [Iter 1326/2250] R3[275/1200]  | LR: 0.026898 | E:  -60.879780 | E_var:     0.7016 | E_err:   0.009254
[2025-10-22 00:58:37] [Iter 1327/2250] R3[276/1200]  | LR: 0.026876 | E:  -60.887295 | E_var:     0.8008 | E_err:   0.009887
[2025-10-22 00:59:04] [Iter 1328/2250] R3[277/1200]  | LR: 0.026855 | E:  -60.881861 | E_var:     0.6062 | E_err:   0.008602
[2025-10-22 00:59:31] [Iter 1329/2250] R3[278/1200]  | LR: 0.026833 | E:  -60.882532 | E_var:     0.6376 | E_err:   0.008822
[2025-10-22 00:59:57] [Iter 1330/2250] R3[279/1200]  | LR: 0.026811 | E:  -60.902401 | E_var:     0.6362 | E_err:   0.008813
[2025-10-22 01:00:24] [Iter 1331/2250] R3[280/1200]  | LR: 0.026789 | E:  -60.885526 | E_var:     0.6671 | E_err:   0.009024
[2025-10-22 01:00:51] [Iter 1332/2250] R3[281/1200]  | LR: 0.026767 | E:  -60.878543 | E_var:     0.6501 | E_err:   0.008908
[2025-10-22 01:01:18] [Iter 1333/2250] R3[282/1200]  | LR: 0.026745 | E:  -60.895849 | E_var:     0.7426 | E_err:   0.009521
[2025-10-22 01:01:45] [Iter 1334/2250] R3[283/1200]  | LR: 0.026723 | E:  -60.879046 | E_var:     0.7179 | E_err:   0.009362
[2025-10-22 01:02:12] [Iter 1335/2250] R3[284/1200]  | LR: 0.026701 | E:  -60.888212 | E_var:     1.2021 | E_err:   0.012114
[2025-10-22 01:02:39] [Iter 1336/2250] R3[285/1200]  | LR: 0.026679 | E:  -60.877729 | E_var:     0.6621 | E_err:   0.008990
[2025-10-22 01:03:06] [Iter 1337/2250] R3[286/1200]  | LR: 0.026657 | E:  -60.895462 | E_var:     0.6994 | E_err:   0.009240
[2025-10-22 01:03:33] [Iter 1338/2250] R3[287/1200]  | LR: 0.026634 | E:  -60.885100 | E_var:     0.6515 | E_err:   0.008918
[2025-10-22 01:04:00] [Iter 1339/2250] R3[288/1200]  | LR: 0.026612 | E:  -60.875672 | E_var:     0.8414 | E_err:   0.010135
[2025-10-22 01:04:26] [Iter 1340/2250] R3[289/1200]  | LR: 0.026590 | E:  -60.893033 | E_var:     2.3221 | E_err:   0.016836
[2025-10-22 01:04:53] [Iter 1341/2250] R3[290/1200]  | LR: 0.026567 | E:  -60.891210 | E_var:     0.6603 | E_err:   0.008978
[2025-10-22 01:05:20] [Iter 1342/2250] R3[291/1200]  | LR: 0.026545 | E:  -60.899758 | E_var:     0.7134 | E_err:   0.009332
[2025-10-22 01:05:47] [Iter 1343/2250] R3[292/1200]  | LR: 0.026522 | E:  -60.882422 | E_var:     0.6059 | E_err:   0.008600
[2025-10-22 01:06:14] [Iter 1344/2250] R3[293/1200]  | LR: 0.026499 | E:  -60.889904 | E_var:     0.7081 | E_err:   0.009297
[2025-10-22 01:06:41] [Iter 1345/2250] R3[294/1200]  | LR: 0.026477 | E:  -60.880233 | E_var:     0.6884 | E_err:   0.009167
[2025-10-22 01:07:08] [Iter 1346/2250] R3[295/1200]  | LR: 0.026454 | E:  -60.868394 | E_var:     0.6427 | E_err:   0.008857
[2025-10-22 01:07:35] [Iter 1347/2250] R3[296/1200]  | LR: 0.026431 | E:  -60.879694 | E_var:     0.6889 | E_err:   0.009171
[2025-10-22 01:08:02] [Iter 1348/2250] R3[297/1200]  | LR: 0.026408 | E:  -60.887754 | E_var:     0.6590 | E_err:   0.008969
[2025-10-22 01:08:28] [Iter 1349/2250] R3[298/1200]  | LR: 0.026385 | E:  -60.870504 | E_var:     0.9514 | E_err:   0.010777
[2025-10-22 01:08:56] [Iter 1350/2250] R3[299/1200]  | LR: 0.026362 | E:  -60.893660 | E_var:     0.6805 | E_err:   0.009114
[2025-10-22 01:08:56] ✓ Checkpoint saved: checkpoint_iter_001350.pkl
[2025-10-22 01:09:23] [Iter 1351/2250] R3[300/1200]  | LR: 0.026339 | E:  -60.881125 | E_var:     0.6157 | E_err:   0.008670
[2025-10-22 01:09:49] [Iter 1352/2250] R3[301/1200]  | LR: 0.026316 | E:  -60.894058 | E_var:     0.7298 | E_err:   0.009438
[2025-10-22 01:10:16] [Iter 1353/2250] R3[302/1200]  | LR: 0.026292 | E:  -60.883860 | E_var:     0.6209 | E_err:   0.008706
[2025-10-22 01:10:43] [Iter 1354/2250] R3[303/1200]  | LR: 0.026269 | E:  -60.878714 | E_var:     0.6871 | E_err:   0.009158
[2025-10-22 01:11:10] [Iter 1355/2250] R3[304/1200]  | LR: 0.026246 | E:  -60.887254 | E_var:     0.6821 | E_err:   0.009125
[2025-10-22 01:11:37] [Iter 1356/2250] R3[305/1200]  | LR: 0.026222 | E:  -60.894025 | E_var:     0.7718 | E_err:   0.009706
[2025-10-22 01:12:04] [Iter 1357/2250] R3[306/1200]  | LR: 0.026199 | E:  -60.879467 | E_var:     0.6823 | E_err:   0.009126
[2025-10-22 01:12:31] [Iter 1358/2250] R3[307/1200]  | LR: 0.026175 | E:  -60.885835 | E_var:     0.7074 | E_err:   0.009292
[2025-10-22 01:12:58] [Iter 1359/2250] R3[308/1200]  | LR: 0.026152 | E:  -60.883165 | E_var:     0.6635 | E_err:   0.009000
[2025-10-22 01:13:25] [Iter 1360/2250] R3[309/1200]  | LR: 0.026128 | E:  -60.894356 | E_var:     0.6426 | E_err:   0.008857
[2025-10-22 01:13:52] [Iter 1361/2250] R3[310/1200]  | LR: 0.026104 | E:  -60.887626 | E_var:     0.6905 | E_err:   0.009181
[2025-10-22 01:14:19] [Iter 1362/2250] R3[311/1200]  | LR: 0.026081 | E:  -60.887939 | E_var:     0.6798 | E_err:   0.009109
[2025-10-22 01:14:46] [Iter 1363/2250] R3[312/1200]  | LR: 0.026057 | E:  -60.885636 | E_var:     0.6885 | E_err:   0.009168
[2025-10-22 01:15:13] [Iter 1364/2250] R3[313/1200]  | LR: 0.026033 | E:  -60.897721 | E_var:     0.7314 | E_err:   0.009449
[2025-10-22 01:15:39] [Iter 1365/2250] R3[314/1200]  | LR: 0.026009 | E:  -60.880579 | E_var:     0.9192 | E_err:   0.010593
[2025-10-22 01:16:06] [Iter 1366/2250] R3[315/1200]  | LR: 0.025985 | E:  -60.891483 | E_var:     0.7837 | E_err:   0.009781
[2025-10-22 01:16:33] [Iter 1367/2250] R3[316/1200]  | LR: 0.025961 | E:  -60.889755 | E_var:     0.7681 | E_err:   0.009683
[2025-10-22 01:17:00] [Iter 1368/2250] R3[317/1200]  | LR: 0.025937 | E:  -60.887132 | E_var:     0.9108 | E_err:   0.010544
[2025-10-22 01:17:27] [Iter 1369/2250] R3[318/1200]  | LR: 0.025913 | E:  -60.883279 | E_var:     0.7138 | E_err:   0.009335
[2025-10-22 01:17:54] [Iter 1370/2250] R3[319/1200]  | LR: 0.025888 | E:  -60.893189 | E_var:     0.6444 | E_err:   0.008869
[2025-10-22 01:18:21] [Iter 1371/2250] R3[320/1200]  | LR: 0.025864 | E:  -60.893728 | E_var:     0.5972 | E_err:   0.008538
[2025-10-22 01:18:47] [Iter 1372/2250] R3[321/1200]  | LR: 0.025840 | E:  -60.875242 | E_var:     0.6755 | E_err:   0.009080
[2025-10-22 01:19:14] [Iter 1373/2250] R3[322/1200]  | LR: 0.025815 | E:  -60.889350 | E_var:     0.7282 | E_err:   0.009428
[2025-10-22 01:19:41] [Iter 1374/2250] R3[323/1200]  | LR: 0.025791 | E:  -60.894449 | E_var:     0.6683 | E_err:   0.009032
[2025-10-22 01:20:08] [Iter 1375/2250] R3[324/1200]  | LR: 0.025766 | E:  -60.882436 | E_var:     0.6993 | E_err:   0.009239
[2025-10-22 01:20:35] [Iter 1376/2250] R3[325/1200]  | LR: 0.025742 | E:  -60.887412 | E_var:     0.7365 | E_err:   0.009482
[2025-10-22 01:21:02] [Iter 1377/2250] R3[326/1200]  | LR: 0.025717 | E:  -60.898146 | E_var:     0.5309 | E_err:   0.008050
[2025-10-22 01:21:28] [Iter 1378/2250] R3[327/1200]  | LR: 0.025693 | E:  -60.881189 | E_var:     0.8812 | E_err:   0.010371
[2025-10-22 01:21:55] [Iter 1379/2250] R3[328/1200]  | LR: 0.025668 | E:  -60.888983 | E_var:     0.6901 | E_err:   0.009178
[2025-10-22 01:22:22] [Iter 1380/2250] R3[329/1200]  | LR: 0.025643 | E:  -60.885787 | E_var:     0.6403 | E_err:   0.008841
[2025-10-22 01:22:49] [Iter 1381/2250] R3[330/1200]  | LR: 0.025618 | E:  -60.874247 | E_var:     0.6106 | E_err:   0.008633
[2025-10-22 01:23:16] [Iter 1382/2250] R3[331/1200]  | LR: 0.025593 | E:  -60.897558 | E_var:     0.6435 | E_err:   0.008863
[2025-10-22 01:23:43] [Iter 1383/2250] R3[332/1200]  | LR: 0.025568 | E:  -60.896289 | E_var:     0.6869 | E_err:   0.009157
[2025-10-22 01:24:10] [Iter 1384/2250] R3[333/1200]  | LR: 0.025543 | E:  -60.896932 | E_var:     0.6411 | E_err:   0.008847
[2025-10-22 01:24:36] [Iter 1385/2250] R3[334/1200]  | LR: 0.025518 | E:  -60.883649 | E_var:     0.6570 | E_err:   0.008956
[2025-10-22 01:25:03] [Iter 1386/2250] R3[335/1200]  | LR: 0.025493 | E:  -60.897106 | E_var:     0.6159 | E_err:   0.008671
[2025-10-22 01:25:30] [Iter 1387/2250] R3[336/1200]  | LR: 0.025468 | E:  -60.896016 | E_var:     0.6886 | E_err:   0.009168
[2025-10-22 01:25:57] [Iter 1388/2250] R3[337/1200]  | LR: 0.025443 | E:  -60.893643 | E_var:     0.7187 | E_err:   0.009367
[2025-10-22 01:26:24] [Iter 1389/2250] R3[338/1200]  | LR: 0.025417 | E:  -60.891247 | E_var:     0.9171 | E_err:   0.010581
[2025-10-22 01:26:51] [Iter 1390/2250] R3[339/1200]  | LR: 0.025392 | E:  -60.893070 | E_var:     0.7199 | E_err:   0.009374
[2025-10-22 01:27:18] [Iter 1391/2250] R3[340/1200]  | LR: 0.025367 | E:  -60.880615 | E_var:     0.7631 | E_err:   0.009651
[2025-10-22 01:27:45] [Iter 1392/2250] R3[341/1200]  | LR: 0.025341 | E:  -60.899389 | E_var:     0.6429 | E_err:   0.008859
[2025-10-22 01:28:12] [Iter 1393/2250] R3[342/1200]  | LR: 0.025316 | E:  -60.877719 | E_var:     0.6784 | E_err:   0.009100
[2025-10-22 01:28:38] [Iter 1394/2250] R3[343/1200]  | LR: 0.025290 | E:  -60.879122 | E_var:     0.6020 | E_err:   0.008572
[2025-10-22 01:29:05] [Iter 1395/2250] R3[344/1200]  | LR: 0.025264 | E:  -60.880815 | E_var:     0.6955 | E_err:   0.009214
[2025-10-22 01:29:32] [Iter 1396/2250] R3[345/1200]  | LR: 0.025239 | E:  -60.885959 | E_var:     0.7794 | E_err:   0.009754
[2025-10-22 01:29:59] [Iter 1397/2250] R3[346/1200]  | LR: 0.025213 | E:  -60.883330 | E_var:     0.6777 | E_err:   0.009095
[2025-10-22 01:30:26] [Iter 1398/2250] R3[347/1200]  | LR: 0.025187 | E:  -60.885326 | E_var:     0.6237 | E_err:   0.008726
[2025-10-22 01:30:53] [Iter 1399/2250] R3[348/1200]  | LR: 0.025161 | E:  -60.894937 | E_var:     0.6628 | E_err:   0.008995
[2025-10-22 01:31:20] [Iter 1400/2250] R3[349/1200]  | LR: 0.025135 | E:  -60.884180 | E_var:     0.8763 | E_err:   0.010342
[2025-10-22 01:31:46] [Iter 1401/2250] R3[350/1200]  | LR: 0.025110 | E:  -60.883269 | E_var:     0.7941 | E_err:   0.009846
[2025-10-22 01:32:13] [Iter 1402/2250] R3[351/1200]  | LR: 0.025084 | E:  -60.891175 | E_var:     0.7002 | E_err:   0.009246
[2025-10-22 01:32:40] [Iter 1403/2250] R3[352/1200]  | LR: 0.025057 | E:  -60.885123 | E_var:     0.6761 | E_err:   0.009085
[2025-10-22 01:33:07] [Iter 1404/2250] R3[353/1200]  | LR: 0.025031 | E:  -60.886551 | E_var:     0.8068 | E_err:   0.009924
[2025-10-22 01:33:34] [Iter 1405/2250] R3[354/1200]  | LR: 0.025005 | E:  -60.888494 | E_var:     0.7991 | E_err:   0.009876
[2025-10-22 01:34:01] [Iter 1406/2250] R3[355/1200]  | LR: 0.024979 | E:  -60.888169 | E_var:     0.6849 | E_err:   0.009144
[2025-10-22 01:34:28] [Iter 1407/2250] R3[356/1200]  | LR: 0.024953 | E:  -60.887668 | E_var:     0.6947 | E_err:   0.009209
[2025-10-22 01:34:54] [Iter 1408/2250] R3[357/1200]  | LR: 0.024927 | E:  -60.900367 | E_var:     0.6351 | E_err:   0.008805
[2025-10-22 01:35:21] [Iter 1409/2250] R3[358/1200]  | LR: 0.024900 | E:  -60.889053 | E_var:     0.6279 | E_err:   0.008755
[2025-10-22 01:35:48] [Iter 1410/2250] R3[359/1200]  | LR: 0.024874 | E:  -60.893853 | E_var:     0.7317 | E_err:   0.009451
[2025-10-22 01:36:15] [Iter 1411/2250] R3[360/1200]  | LR: 0.024847 | E:  -60.889174 | E_var:     0.6181 | E_err:   0.008686
[2025-10-22 01:36:42] [Iter 1412/2250] R3[361/1200]  | LR: 0.024821 | E:  -60.899615 | E_var:     0.5887 | E_err:   0.008477
[2025-10-22 01:37:09] [Iter 1413/2250] R3[362/1200]  | LR: 0.024794 | E:  -60.898314 | E_var:     0.6696 | E_err:   0.009041
[2025-10-22 01:37:36] [Iter 1414/2250] R3[363/1200]  | LR: 0.024768 | E:  -60.889562 | E_var:     0.6252 | E_err:   0.008736
[2025-10-22 01:38:03] [Iter 1415/2250] R3[364/1200]  | LR: 0.024741 | E:  -60.884168 | E_var:     0.8187 | E_err:   0.009997
[2025-10-22 01:38:30] [Iter 1416/2250] R3[365/1200]  | LR: 0.024714 | E:  -60.889730 | E_var:     0.6528 | E_err:   0.008927
[2025-10-22 01:38:56] [Iter 1417/2250] R3[366/1200]  | LR: 0.024688 | E:  -60.883465 | E_var:     0.5973 | E_err:   0.008539
[2025-10-22 01:39:23] [Iter 1418/2250] R3[367/1200]  | LR: 0.024661 | E:  -60.888453 | E_var:     0.7263 | E_err:   0.009416
[2025-10-22 01:39:50] [Iter 1419/2250] R3[368/1200]  | LR: 0.024634 | E:  -60.884191 | E_var:     0.7425 | E_err:   0.009520
[2025-10-22 01:40:17] [Iter 1420/2250] R3[369/1200]  | LR: 0.024607 | E:  -60.896156 | E_var:     0.6252 | E_err:   0.008736
[2025-10-22 01:40:44] [Iter 1421/2250] R3[370/1200]  | LR: 0.024580 | E:  -60.895733 | E_var:     0.5851 | E_err:   0.008452
[2025-10-22 01:41:11] [Iter 1422/2250] R3[371/1200]  | LR: 0.024553 | E:  -60.895017 | E_var:     0.8136 | E_err:   0.009966
[2025-10-22 01:41:38] [Iter 1423/2250] R3[372/1200]  | LR: 0.024526 | E:  -60.882601 | E_var:     0.6424 | E_err:   0.008855
[2025-10-22 01:42:05] [Iter 1424/2250] R3[373/1200]  | LR: 0.024499 | E:  -60.887063 | E_var:     0.6707 | E_err:   0.009049
[2025-10-22 01:42:32] [Iter 1425/2250] R3[374/1200]  | LR: 0.024472 | E:  -60.898229 | E_var:     0.6345 | E_err:   0.008801
[2025-10-22 01:42:58] [Iter 1426/2250] R3[375/1200]  | LR: 0.024445 | E:  -60.892945 | E_var:     0.8751 | E_err:   0.010335
[2025-10-22 01:43:25] [Iter 1427/2250] R3[376/1200]  | LR: 0.024417 | E:  -60.887402 | E_var:     0.7729 | E_err:   0.009713
[2025-10-22 01:43:52] [Iter 1428/2250] R3[377/1200]  | LR: 0.024390 | E:  -60.875997 | E_var:     0.6469 | E_err:   0.008886
[2025-10-22 01:44:19] [Iter 1429/2250] R3[378/1200]  | LR: 0.024363 | E:  -60.891768 | E_var:     0.6811 | E_err:   0.009118
[2025-10-22 01:44:46] [Iter 1430/2250] R3[379/1200]  | LR: 0.024335 | E:  -60.902564 | E_var:     0.5964 | E_err:   0.008533
[2025-10-22 01:45:13] [Iter 1431/2250] R3[380/1200]  | LR: 0.024308 | E:  -60.906914 | E_var:     0.6613 | E_err:   0.008985
[2025-10-22 01:45:40] [Iter 1432/2250] R3[381/1200]  | LR: 0.024281 | E:  -60.897284 | E_var:     0.6707 | E_err:   0.009049
[2025-10-22 01:46:07] [Iter 1433/2250] R3[382/1200]  | LR: 0.024253 | E:  -60.895166 | E_var:     0.6911 | E_err:   0.009185
[2025-10-22 01:46:34] [Iter 1434/2250] R3[383/1200]  | LR: 0.024225 | E:  -60.894684 | E_var:     0.7295 | E_err:   0.009437
[2025-10-22 01:47:01] [Iter 1435/2250] R3[384/1200]  | LR: 0.024198 | E:  -60.881211 | E_var:     0.6030 | E_err:   0.008580
[2025-10-22 01:47:27] [Iter 1436/2250] R3[385/1200]  | LR: 0.024170 | E:  -60.879892 | E_var:     0.7136 | E_err:   0.009333
[2025-10-22 01:47:54] [Iter 1437/2250] R3[386/1200]  | LR: 0.024142 | E:  -60.902345 | E_var:     0.6850 | E_err:   0.009144
[2025-10-22 01:48:21] [Iter 1438/2250] R3[387/1200]  | LR: 0.024115 | E:  -60.883323 | E_var:     0.6335 | E_err:   0.008794
[2025-10-22 01:48:48] [Iter 1439/2250] R3[388/1200]  | LR: 0.024087 | E:  -60.883965 | E_var:     0.6782 | E_err:   0.009099
[2025-10-22 01:49:15] [Iter 1440/2250] R3[389/1200]  | LR: 0.024059 | E:  -60.902622 | E_var:     0.6603 | E_err:   0.008978
[2025-10-22 01:49:42] [Iter 1441/2250] R3[390/1200]  | LR: 0.024031 | E:  -60.887376 | E_var:     0.7790 | E_err:   0.009752
[2025-10-22 01:50:09] [Iter 1442/2250] R3[391/1200]  | LR: 0.024003 | E:  -60.887385 | E_var:     0.6024 | E_err:   0.008575
[2025-10-22 01:50:36] [Iter 1443/2250] R3[392/1200]  | LR: 0.023975 | E:  -60.878745 | E_var:     0.6220 | E_err:   0.008714
[2025-10-22 01:51:03] [Iter 1444/2250] R3[393/1200]  | LR: 0.023947 | E:  -60.890886 | E_var:     0.5977 | E_err:   0.008542
[2025-10-22 01:51:29] [Iter 1445/2250] R3[394/1200]  | LR: 0.023919 | E:  -60.902139 | E_var:     0.6620 | E_err:   0.008990
[2025-10-22 01:51:56] [Iter 1446/2250] R3[395/1200]  | LR: 0.023891 | E:  -60.876166 | E_var:     0.6163 | E_err:   0.008674
[2025-10-22 01:52:23] [Iter 1447/2250] R3[396/1200]  | LR: 0.023863 | E:  -60.896802 | E_var:     0.6381 | E_err:   0.008826
[2025-10-22 01:52:50] [Iter 1448/2250] R3[397/1200]  | LR: 0.023835 | E:  -60.879328 | E_var:     0.6438 | E_err:   0.008865
[2025-10-22 01:53:17] [Iter 1449/2250] R3[398/1200]  | LR: 0.023807 | E:  -60.879593 | E_var:     0.7998 | E_err:   0.009881
[2025-10-22 01:53:44] [Iter 1450/2250] R3[399/1200]  | LR: 0.023778 | E:  -60.903407 | E_var:     0.6767 | E_err:   0.009089
[2025-10-22 01:54:11] [Iter 1451/2250] R3[400/1200]  | LR: 0.023750 | E:  -60.905642 | E_var:     0.5992 | E_err:   0.008553
[2025-10-22 01:54:38] [Iter 1452/2250] R3[401/1200]  | LR: 0.023722 | E:  -60.882675 | E_var:     0.6769 | E_err:   0.009090
[2025-10-22 01:55:05] [Iter 1453/2250] R3[402/1200]  | LR: 0.023693 | E:  -60.879454 | E_var:     0.7725 | E_err:   0.009711
[2025-10-22 01:55:31] [Iter 1454/2250] R3[403/1200]  | LR: 0.023665 | E:  -60.877895 | E_var:     0.6550 | E_err:   0.008942
[2025-10-22 01:55:58] [Iter 1455/2250] R3[404/1200]  | LR: 0.023636 | E:  -60.906632 | E_var:     0.6836 | E_err:   0.009135
[2025-10-22 01:56:25] [Iter 1456/2250] R3[405/1200]  | LR: 0.023608 | E:  -60.897955 | E_var:     0.5773 | E_err:   0.008395
[2025-10-22 01:56:52] [Iter 1457/2250] R3[406/1200]  | LR: 0.023579 | E:  -60.886113 | E_var:     0.7082 | E_err:   0.009298
[2025-10-22 01:57:19] [Iter 1458/2250] R3[407/1200]  | LR: 0.023551 | E:  -60.899833 | E_var:     0.6108 | E_err:   0.008635
[2025-10-22 01:57:46] [Iter 1459/2250] R3[408/1200]  | LR: 0.023522 | E:  -60.874622 | E_var:     0.6268 | E_err:   0.008747
[2025-10-22 01:58:13] [Iter 1460/2250] R3[409/1200]  | LR: 0.023493 | E:  -60.900725 | E_var:     0.6359 | E_err:   0.008810
[2025-10-22 01:58:40] [Iter 1461/2250] R3[410/1200]  | LR: 0.023464 | E:  -60.899634 | E_var:     0.6622 | E_err:   0.008991
[2025-10-22 01:59:07] [Iter 1462/2250] R3[411/1200]  | LR: 0.023436 | E:  -60.887889 | E_var:     0.6189 | E_err:   0.008692
[2025-10-22 01:59:33] [Iter 1463/2250] R3[412/1200]  | LR: 0.023407 | E:  -60.882142 | E_var:     0.6260 | E_err:   0.008742
[2025-10-22 02:00:00] [Iter 1464/2250] R3[413/1200]  | LR: 0.023378 | E:  -60.902052 | E_var:     0.7774 | E_err:   0.009742
[2025-10-22 02:00:27] [Iter 1465/2250] R3[414/1200]  | LR: 0.023349 | E:  -60.878040 | E_var:     0.6693 | E_err:   0.009039
[2025-10-22 02:00:54] [Iter 1466/2250] R3[415/1200]  | LR: 0.023320 | E:  -60.892435 | E_var:     0.6704 | E_err:   0.009046
[2025-10-22 02:01:21] [Iter 1467/2250] R3[416/1200]  | LR: 0.023291 | E:  -60.887552 | E_var:     0.5677 | E_err:   0.008325
[2025-10-22 02:01:48] [Iter 1468/2250] R3[417/1200]  | LR: 0.023262 | E:  -60.899210 | E_var:     0.7287 | E_err:   0.009432
[2025-10-22 02:02:15] [Iter 1469/2250] R3[418/1200]  | LR: 0.023233 | E:  -60.905680 | E_var:     0.7184 | E_err:   0.009365
[2025-10-22 02:02:42] [Iter 1470/2250] R3[419/1200]  | LR: 0.023204 | E:  -60.890233 | E_var:     0.6387 | E_err:   0.008830
[2025-10-22 02:03:09] [Iter 1471/2250] R3[420/1200]  | LR: 0.023175 | E:  -60.896458 | E_var:     0.7309 | E_err:   0.009445
[2025-10-22 02:03:35] [Iter 1472/2250] R3[421/1200]  | LR: 0.023146 | E:  -60.883654 | E_var:     0.7251 | E_err:   0.009408
[2025-10-22 02:04:02] [Iter 1473/2250] R3[422/1200]  | LR: 0.023116 | E:  -60.870754 | E_var:     0.6250 | E_err:   0.008734
[2025-10-22 02:04:29] [Iter 1474/2250] R3[423/1200]  | LR: 0.023087 | E:  -60.898841 | E_var:     0.6316 | E_err:   0.008781
[2025-10-22 02:04:56] [Iter 1475/2250] R3[424/1200]  | LR: 0.023058 | E:  -60.892217 | E_var:     0.6203 | E_err:   0.008702
[2025-10-22 02:05:23] [Iter 1476/2250] R3[425/1200]  | LR: 0.023029 | E:  -60.883471 | E_var:     0.6557 | E_err:   0.008947
[2025-10-22 02:05:50] [Iter 1477/2250] R3[426/1200]  | LR: 0.022999 | E:  -60.894419 | E_var:     0.6665 | E_err:   0.009020
[2025-10-22 02:06:17] [Iter 1478/2250] R3[427/1200]  | LR: 0.022970 | E:  -60.876338 | E_var:     0.6846 | E_err:   0.009141
[2025-10-22 02:06:44] [Iter 1479/2250] R3[428/1200]  | LR: 0.022940 | E:  -60.897471 | E_var:     0.7114 | E_err:   0.009319
[2025-10-22 02:07:11] [Iter 1480/2250] R3[429/1200]  | LR: 0.022911 | E:  -60.902740 | E_var:     0.6385 | E_err:   0.008828
[2025-10-22 02:07:38] [Iter 1481/2250] R3[430/1200]  | LR: 0.022881 | E:  -60.900853 | E_var:     0.6586 | E_err:   0.008966
[2025-10-22 02:08:04] [Iter 1482/2250] R3[431/1200]  | LR: 0.022852 | E:  -60.896106 | E_var:     0.7089 | E_err:   0.009303
[2025-10-22 02:08:31] [Iter 1483/2250] R3[432/1200]  | LR: 0.022822 | E:  -60.888651 | E_var:     0.6221 | E_err:   0.008714
[2025-10-22 02:08:58] [Iter 1484/2250] R3[433/1200]  | LR: 0.022793 | E:  -60.887539 | E_var:     0.6303 | E_err:   0.008771
[2025-10-22 02:09:25] [Iter 1485/2250] R3[434/1200]  | LR: 0.022763 | E:  -60.883311 | E_var:     0.6708 | E_err:   0.009049
[2025-10-22 02:09:52] [Iter 1486/2250] R3[435/1200]  | LR: 0.022733 | E:  -60.885720 | E_var:     0.7305 | E_err:   0.009443
[2025-10-22 02:10:19] [Iter 1487/2250] R3[436/1200]  | LR: 0.022704 | E:  -60.883980 | E_var:     0.6807 | E_err:   0.009116
[2025-10-22 02:10:46] [Iter 1488/2250] R3[437/1200]  | LR: 0.022674 | E:  -60.893087 | E_var:     0.7523 | E_err:   0.009583
[2025-10-22 02:11:13] [Iter 1489/2250] R3[438/1200]  | LR: 0.022644 | E:  -60.884206 | E_var:     0.6006 | E_err:   0.008562
[2025-10-22 02:11:40] [Iter 1490/2250] R3[439/1200]  | LR: 0.022614 | E:  -60.898573 | E_var:     0.7137 | E_err:   0.009334
[2025-10-22 02:12:06] [Iter 1491/2250] R3[440/1200]  | LR: 0.022584 | E:  -60.898161 | E_var:     0.6469 | E_err:   0.008886
[2025-10-22 02:12:33] [Iter 1492/2250] R3[441/1200]  | LR: 0.022554 | E:  -60.890140 | E_var:     0.7052 | E_err:   0.009278
[2025-10-22 02:13:00] [Iter 1493/2250] R3[442/1200]  | LR: 0.022524 | E:  -60.894776 | E_var:     0.6360 | E_err:   0.008811
[2025-10-22 02:13:27] [Iter 1494/2250] R3[443/1200]  | LR: 0.022494 | E:  -60.887029 | E_var:     0.5994 | E_err:   0.008554
[2025-10-22 02:13:54] [Iter 1495/2250] R3[444/1200]  | LR: 0.022464 | E:  -60.886321 | E_var:     0.6265 | E_err:   0.008745
[2025-10-22 02:14:21] [Iter 1496/2250] R3[445/1200]  | LR: 0.022434 | E:  -60.885412 | E_var:     0.8500 | E_err:   0.010186
[2025-10-22 02:14:48] [Iter 1497/2250] R3[446/1200]  | LR: 0.022404 | E:  -60.884637 | E_var:     0.6114 | E_err:   0.008639
[2025-10-22 02:15:15] [Iter 1498/2250] R3[447/1200]  | LR: 0.022374 | E:  -60.893576 | E_var:     0.6426 | E_err:   0.008857
[2025-10-22 02:15:41] [Iter 1499/2250] R3[448/1200]  | LR: 0.022344 | E:  -60.884741 | E_var:     0.5722 | E_err:   0.008358
[2025-10-22 02:16:08] [Iter 1500/2250] R3[449/1200]  | LR: 0.022314 | E:  -60.889416 | E_var:     0.6328 | E_err:   0.008789
[2025-10-22 02:16:35] [Iter 1501/2250] R3[450/1200]  | LR: 0.022284 | E:  -60.894117 | E_var:     0.7000 | E_err:   0.009244
[2025-10-22 02:17:02] [Iter 1502/2250] R3[451/1200]  | LR: 0.022253 | E:  -60.891728 | E_var:     0.6345 | E_err:   0.008801
[2025-10-22 02:17:29] [Iter 1503/2250] R3[452/1200]  | LR: 0.022223 | E:  -60.884074 | E_var:     0.6801 | E_err:   0.009111
[2025-10-22 02:17:56] [Iter 1504/2250] R3[453/1200]  | LR: 0.022193 | E:  -60.873955 | E_var:     0.7244 | E_err:   0.009404
[2025-10-22 02:18:23] [Iter 1505/2250] R3[454/1200]  | LR: 0.022162 | E:  -60.880565 | E_var:     0.6598 | E_err:   0.008975
[2025-10-22 02:18:50] [Iter 1506/2250] R3[455/1200]  | LR: 0.022132 | E:  -60.891905 | E_var:     0.5971 | E_err:   0.008537
[2025-10-22 02:19:17] [Iter 1507/2250] R3[456/1200]  | LR: 0.022102 | E:  -60.897603 | E_var:     0.6571 | E_err:   0.008956
[2025-10-22 02:19:44] [Iter 1508/2250] R3[457/1200]  | LR: 0.022071 | E:  -60.879395 | E_var:     0.7952 | E_err:   0.009852
[2025-10-22 02:20:10] [Iter 1509/2250] R3[458/1200]  | LR: 0.022041 | E:  -60.877564 | E_var:     0.7110 | E_err:   0.009316
[2025-10-22 02:20:37] [Iter 1510/2250] R3[459/1200]  | LR: 0.022010 | E:  -60.886504 | E_var:     0.9274 | E_err:   0.010640
[2025-10-22 02:21:04] [Iter 1511/2250] R3[460/1200]  | LR: 0.021980 | E:  -60.885544 | E_var:     0.6368 | E_err:   0.008817
[2025-10-22 02:21:31] [Iter 1512/2250] R3[461/1200]  | LR: 0.021949 | E:  -60.900141 | E_var:     0.5866 | E_err:   0.008462
[2025-10-22 02:21:58] [Iter 1513/2250] R3[462/1200]  | LR: 0.021918 | E:  -60.886276 | E_var:     0.6665 | E_err:   0.009020
[2025-10-22 02:22:25] [Iter 1514/2250] R3[463/1200]  | LR: 0.021888 | E:  -60.893447 | E_var:     0.5855 | E_err:   0.008454
[2025-10-22 02:22:52] [Iter 1515/2250] R3[464/1200]  | LR: 0.021857 | E:  -60.871535 | E_var:     0.6228 | E_err:   0.008719
[2025-10-22 02:23:19] [Iter 1516/2250] R3[465/1200]  | LR: 0.021826 | E:  -60.891543 | E_var:     0.5893 | E_err:   0.008481
[2025-10-22 02:23:46] [Iter 1517/2250] R3[466/1200]  | LR: 0.021796 | E:  -60.885940 | E_var:     0.6486 | E_err:   0.008898
[2025-10-22 02:24:12] [Iter 1518/2250] R3[467/1200]  | LR: 0.021765 | E:  -60.893103 | E_var:     0.6293 | E_err:   0.008765
[2025-10-22 02:24:39] [Iter 1519/2250] R3[468/1200]  | LR: 0.021734 | E:  -60.878667 | E_var:     0.6741 | E_err:   0.009071
[2025-10-22 02:25:06] [Iter 1520/2250] R3[469/1200]  | LR: 0.021703 | E:  -60.890294 | E_var:     0.6410 | E_err:   0.008846
[2025-10-22 02:25:33] [Iter 1521/2250] R3[470/1200]  | LR: 0.021673 | E:  -60.872416 | E_var:     0.7740 | E_err:   0.009720
[2025-10-22 02:26:00] [Iter 1522/2250] R3[471/1200]  | LR: 0.021642 | E:  -60.881523 | E_var:     0.7713 | E_err:   0.009703
[2025-10-22 02:26:27] [Iter 1523/2250] R3[472/1200]  | LR: 0.021611 | E:  -60.894423 | E_var:     0.6405 | E_err:   0.008843
[2025-10-22 02:26:54] [Iter 1524/2250] R3[473/1200]  | LR: 0.021580 | E:  -60.884068 | E_var:     0.6415 | E_err:   0.008849
[2025-10-22 02:27:21] [Iter 1525/2250] R3[474/1200]  | LR: 0.021549 | E:  -60.884123 | E_var:     0.6914 | E_err:   0.009187
[2025-10-22 02:27:48] [Iter 1526/2250] R3[475/1200]  | LR: 0.021518 | E:  -60.895930 | E_var:     0.6714 | E_err:   0.009053
[2025-10-22 02:28:15] [Iter 1527/2250] R3[476/1200]  | LR: 0.021487 | E:  -60.876202 | E_var:     0.6300 | E_err:   0.008769
[2025-10-22 02:28:41] [Iter 1528/2250] R3[477/1200]  | LR: 0.021456 | E:  -60.901580 | E_var:     0.5956 | E_err:   0.008527
[2025-10-22 02:29:08] [Iter 1529/2250] R3[478/1200]  | LR: 0.021425 | E:  -60.889851 | E_var:     0.7952 | E_err:   0.009853
[2025-10-22 02:29:35] [Iter 1530/2250] R3[479/1200]  | LR: 0.021394 | E:  -60.888969 | E_var:     0.7455 | E_err:   0.009539
[2025-10-22 02:30:02] [Iter 1531/2250] R3[480/1200]  | LR: 0.021363 | E:  -60.885579 | E_var:     0.6113 | E_err:   0.008638
[2025-10-22 02:30:29] [Iter 1532/2250] R3[481/1200]  | LR: 0.021332 | E:  -60.900765 | E_var:     0.6019 | E_err:   0.008572
[2025-10-22 02:30:56] [Iter 1533/2250] R3[482/1200]  | LR: 0.021300 | E:  -60.887837 | E_var:     0.6969 | E_err:   0.009223
[2025-10-22 02:31:23] [Iter 1534/2250] R3[483/1200]  | LR: 0.021269 | E:  -60.891061 | E_var:     0.6917 | E_err:   0.009189
[2025-10-22 02:31:50] [Iter 1535/2250] R3[484/1200]  | LR: 0.021238 | E:  -60.894188 | E_var:     0.6379 | E_err:   0.008825
[2025-10-22 02:32:17] [Iter 1536/2250] R3[485/1200]  | LR: 0.021207 | E:  -60.899791 | E_var:     0.6799 | E_err:   0.009110
[2025-10-22 02:32:43] [Iter 1537/2250] R3[486/1200]  | LR: 0.021176 | E:  -60.880362 | E_var:     0.7393 | E_err:   0.009500
[2025-10-22 02:33:10] [Iter 1538/2250] R3[487/1200]  | LR: 0.021144 | E:  -60.896011 | E_var:     0.6251 | E_err:   0.008736
[2025-10-22 02:33:37] [Iter 1539/2250] R3[488/1200]  | LR: 0.021113 | E:  -60.901222 | E_var:     0.6905 | E_err:   0.009181
[2025-10-22 02:34:04] [Iter 1540/2250] R3[489/1200]  | LR: 0.021082 | E:  -60.896970 | E_var:     0.6176 | E_err:   0.008683
[2025-10-22 02:34:31] [Iter 1541/2250] R3[490/1200]  | LR: 0.021050 | E:  -60.891501 | E_var:     1.0095 | E_err:   0.011101
[2025-10-22 02:34:58] [Iter 1542/2250] R3[491/1200]  | LR: 0.021019 | E:  -60.892559 | E_var:     1.2120 | E_err:   0.012163
[2025-10-22 02:35:25] [Iter 1543/2250] R3[492/1200]  | LR: 0.020987 | E:  -60.900688 | E_var:     0.6057 | E_err:   0.008599
[2025-10-22 02:35:52] [Iter 1544/2250] R3[493/1200]  | LR: 0.020956 | E:  -60.891020 | E_var:     0.6161 | E_err:   0.008672
[2025-10-22 02:36:19] [Iter 1545/2250] R3[494/1200]  | LR: 0.020924 | E:  -60.894125 | E_var:     0.6796 | E_err:   0.009108
[2025-10-22 02:36:45] [Iter 1546/2250] R3[495/1200]  | LR: 0.020893 | E:  -60.907702 | E_var:     0.6724 | E_err:   0.009060
[2025-10-22 02:37:12] [Iter 1547/2250] R3[496/1200]  | LR: 0.020861 | E:  -60.890562 | E_var:     0.6940 | E_err:   0.009204
[2025-10-22 02:37:39] [Iter 1548/2250] R3[497/1200]  | LR: 0.020830 | E:  -60.899412 | E_var:     0.6248 | E_err:   0.008733
[2025-10-22 02:38:06] [Iter 1549/2250] R3[498/1200]  | LR: 0.020798 | E:  -60.876147 | E_var:     0.6797 | E_err:   0.009109
[2025-10-22 02:38:33] [Iter 1550/2250] R3[499/1200]  | LR: 0.020767 | E:  -60.889967 | E_var:     0.6252 | E_err:   0.008736
[2025-10-22 02:39:00] [Iter 1551/2250] R3[500/1200]  | LR: 0.020735 | E:  -60.890432 | E_var:     0.6890 | E_err:   0.009171
[2025-10-22 02:39:27] [Iter 1552/2250] R3[501/1200]  | LR: 0.020704 | E:  -60.885672 | E_var:     0.6441 | E_err:   0.008867
[2025-10-22 02:39:54] [Iter 1553/2250] R3[502/1200]  | LR: 0.020672 | E:  -60.903941 | E_var:     0.6986 | E_err:   0.009234
[2025-10-22 02:40:21] [Iter 1554/2250] R3[503/1200]  | LR: 0.020640 | E:  -60.897630 | E_var:     0.6624 | E_err:   0.008992
[2025-10-22 02:40:47] [Iter 1555/2250] R3[504/1200]  | LR: 0.020609 | E:  -60.881421 | E_var:     0.6234 | E_err:   0.008724
[2025-10-22 02:41:14] [Iter 1556/2250] R3[505/1200]  | LR: 0.020577 | E:  -60.906114 | E_var:     0.6454 | E_err:   0.008876
[2025-10-22 02:41:41] [Iter 1557/2250] R3[506/1200]  | LR: 0.020545 | E:  -60.906179 | E_var:     1.1976 | E_err:   0.012091
[2025-10-22 02:42:08] [Iter 1558/2250] R3[507/1200]  | LR: 0.020513 | E:  -60.877186 | E_var:     0.8054 | E_err:   0.009916
[2025-10-22 02:42:35] [Iter 1559/2250] R3[508/1200]  | LR: 0.020482 | E:  -60.891473 | E_var:     0.7463 | E_err:   0.009545
[2025-10-22 02:43:02] [Iter 1560/2250] R3[509/1200]  | LR: 0.020450 | E:  -60.892980 | E_var:     0.6242 | E_err:   0.008729
[2025-10-22 02:43:29] [Iter 1561/2250] R3[510/1200]  | LR: 0.020418 | E:  -60.875696 | E_var:     0.6283 | E_err:   0.008757
[2025-10-22 02:43:56] [Iter 1562/2250] R3[511/1200]  | LR: 0.020386 | E:  -60.876982 | E_var:     0.5605 | E_err:   0.008271
[2025-10-22 02:44:23] [Iter 1563/2250] R3[512/1200]  | LR: 0.020354 | E:  -60.898981 | E_var:     0.8504 | E_err:   0.010188
[2025-10-22 02:44:49] [Iter 1564/2250] R3[513/1200]  | LR: 0.020323 | E:  -60.893092 | E_var:     0.6942 | E_err:   0.009206
[2025-10-22 02:45:16] [Iter 1565/2250] R3[514/1200]  | LR: 0.020291 | E:  -60.889247 | E_var:     0.6650 | E_err:   0.009009
[2025-10-22 02:45:43] [Iter 1566/2250] R3[515/1200]  | LR: 0.020259 | E:  -60.891129 | E_var:     0.5774 | E_err:   0.008395
[2025-10-22 02:46:10] [Iter 1567/2250] R3[516/1200]  | LR: 0.020227 | E:  -60.899575 | E_var:     0.6912 | E_err:   0.009186
[2025-10-22 02:46:37] [Iter 1568/2250] R3[517/1200]  | LR: 0.020195 | E:  -60.898659 | E_var:     0.6263 | E_err:   0.008744
[2025-10-22 02:47:04] [Iter 1569/2250] R3[518/1200]  | LR: 0.020163 | E:  -60.884562 | E_var:     0.6258 | E_err:   0.008740
[2025-10-22 02:47:31] [Iter 1570/2250] R3[519/1200]  | LR: 0.020131 | E:  -60.906864 | E_var:     0.7623 | E_err:   0.009647
[2025-10-22 02:47:58] [Iter 1571/2250] R3[520/1200]  | LR: 0.020099 | E:  -60.886494 | E_var:     0.6419 | E_err:   0.008852
[2025-10-22 02:48:25] [Iter 1572/2250] R3[521/1200]  | LR: 0.020067 | E:  -60.895208 | E_var:     0.6064 | E_err:   0.008604
[2025-10-22 02:48:51] [Iter 1573/2250] R3[522/1200]  | LR: 0.020035 | E:  -60.898468 | E_var:     0.6678 | E_err:   0.009029
[2025-10-22 02:49:18] [Iter 1574/2250] R3[523/1200]  | LR: 0.020003 | E:  -60.898390 | E_var:     0.6601 | E_err:   0.008976
[2025-10-22 02:49:45] [Iter 1575/2250] R3[524/1200]  | LR: 0.019971 | E:  -60.889218 | E_var:     0.5858 | E_err:   0.008456
[2025-10-22 02:49:45] ✓ Checkpoint saved: checkpoint_iter_001575.pkl
[2025-10-22 02:50:12] [Iter 1576/2250] R3[525/1200]  | LR: 0.019939 | E:  -60.903126 | E_var:     0.6607 | E_err:   0.008981
[2025-10-22 02:50:39] [Iter 1577/2250] R3[526/1200]  | LR: 0.019907 | E:  -60.888979 | E_var:     0.5970 | E_err:   0.008537
[2025-10-22 02:51:06] [Iter 1578/2250] R3[527/1200]  | LR: 0.019874 | E:  -60.896064 | E_var:     0.5962 | E_err:   0.008531
[2025-10-22 02:51:33] [Iter 1579/2250] R3[528/1200]  | LR: 0.019842 | E:  -60.900331 | E_var:     0.6011 | E_err:   0.008566
[2025-10-22 02:52:00] [Iter 1580/2250] R3[529/1200]  | LR: 0.019810 | E:  -60.880763 | E_var:     0.6228 | E_err:   0.008719
[2025-10-22 02:52:27] [Iter 1581/2250] R3[530/1200]  | LR: 0.019778 | E:  -60.893417 | E_var:     0.5904 | E_err:   0.008489
[2025-10-22 02:52:54] [Iter 1582/2250] R3[531/1200]  | LR: 0.019746 | E:  -60.893610 | E_var:     0.6403 | E_err:   0.008841
[2025-10-22 02:53:21] [Iter 1583/2250] R3[532/1200]  | LR: 0.019714 | E:  -60.895358 | E_var:     0.6367 | E_err:   0.008816
[2025-10-22 02:53:47] [Iter 1584/2250] R3[533/1200]  | LR: 0.019681 | E:  -60.897965 | E_var:     0.6074 | E_err:   0.008611
[2025-10-22 02:54:14] [Iter 1585/2250] R3[534/1200]  | LR: 0.019649 | E:  -60.897258 | E_var:     0.6084 | E_err:   0.008618
[2025-10-22 02:54:41] [Iter 1586/2250] R3[535/1200]  | LR: 0.019617 | E:  -60.881052 | E_var:     0.6206 | E_err:   0.008704
[2025-10-22 02:55:08] [Iter 1587/2250] R3[536/1200]  | LR: 0.019585 | E:  -60.897067 | E_var:     0.7534 | E_err:   0.009590
[2025-10-22 02:55:35] [Iter 1588/2250] R3[537/1200]  | LR: 0.019552 | E:  -60.902160 | E_var:     0.6676 | E_err:   0.009027
[2025-10-22 02:56:02] [Iter 1589/2250] R3[538/1200]  | LR: 0.019520 | E:  -60.878660 | E_var:     0.6897 | E_err:   0.009175
[2025-10-22 02:56:29] [Iter 1590/2250] R3[539/1200]  | LR: 0.019488 | E:  -60.889698 | E_var:     0.6423 | E_err:   0.008854
[2025-10-22 02:56:56] [Iter 1591/2250] R3[540/1200]  | LR: 0.019455 | E:  -60.900099 | E_var:     0.5989 | E_err:   0.008550
[2025-10-22 02:57:23] [Iter 1592/2250] R3[541/1200]  | LR: 0.019423 | E:  -60.890209 | E_var:     0.5855 | E_err:   0.008454
[2025-10-22 02:57:49] [Iter 1593/2250] R3[542/1200]  | LR: 0.019391 | E:  -60.889842 | E_var:     0.6528 | E_err:   0.008927
[2025-10-22 02:58:16] [Iter 1594/2250] R3[543/1200]  | LR: 0.019358 | E:  -60.902915 | E_var:     0.6422 | E_err:   0.008854
[2025-10-22 02:58:43] [Iter 1595/2250] R3[544/1200]  | LR: 0.019326 | E:  -60.893212 | E_var:     0.7041 | E_err:   0.009271
[2025-10-22 02:59:10] [Iter 1596/2250] R3[545/1200]  | LR: 0.019294 | E:  -60.902968 | E_var:     0.6713 | E_err:   0.009052
[2025-10-22 02:59:37] [Iter 1597/2250] R3[546/1200]  | LR: 0.019261 | E:  -60.904417 | E_var:     0.6065 | E_err:   0.008604
[2025-10-22 03:00:04] [Iter 1598/2250] R3[547/1200]  | LR: 0.019229 | E:  -60.901715 | E_var:     0.6544 | E_err:   0.008938
[2025-10-22 03:00:31] [Iter 1599/2250] R3[548/1200]  | LR: 0.019196 | E:  -60.906955 | E_var:     0.6616 | E_err:   0.008987
[2025-10-22 03:00:58] [Iter 1600/2250] R3[549/1200]  | LR: 0.019164 | E:  -60.904573 | E_var:     0.7365 | E_err:   0.009482
[2025-10-22 03:01:25] [Iter 1601/2250] R3[550/1200]  | LR: 0.019132 | E:  -60.890369 | E_var:     1.3060 | E_err:   0.012626
[2025-10-22 03:01:51] [Iter 1602/2250] R3[551/1200]  | LR: 0.019099 | E:  -60.900244 | E_var:     0.5732 | E_err:   0.008365
[2025-10-22 03:02:18] [Iter 1603/2250] R3[552/1200]  | LR: 0.019067 | E:  -60.892052 | E_var:     0.6294 | E_err:   0.008766
[2025-10-22 03:02:45] [Iter 1604/2250] R3[553/1200]  | LR: 0.019034 | E:  -60.898293 | E_var:     0.6326 | E_err:   0.008788
[2025-10-22 03:03:12] [Iter 1605/2250] R3[554/1200]  | LR: 0.019002 | E:  -60.893741 | E_var:     0.9253 | E_err:   0.010628
[2025-10-22 03:03:39] [Iter 1606/2250] R3[555/1200]  | LR: 0.018969 | E:  -60.894234 | E_var:     0.6924 | E_err:   0.009193
[2025-10-22 03:04:06] [Iter 1607/2250] R3[556/1200]  | LR: 0.018937 | E:  -60.897977 | E_var:     0.6288 | E_err:   0.008761
[2025-10-22 03:04:33] [Iter 1608/2250] R3[557/1200]  | LR: 0.018904 | E:  -60.902258 | E_var:     0.6592 | E_err:   0.008970
[2025-10-22 03:05:00] [Iter 1609/2250] R3[558/1200]  | LR: 0.018872 | E:  -60.879267 | E_var:     0.6278 | E_err:   0.008754
[2025-10-22 03:05:27] [Iter 1610/2250] R3[559/1200]  | LR: 0.018839 | E:  -60.904287 | E_var:     0.7149 | E_err:   0.009342
[2025-10-22 03:05:53] [Iter 1611/2250] R3[560/1200]  | LR: 0.018807 | E:  -60.893427 | E_var:     0.7385 | E_err:   0.009495
[2025-10-22 03:06:20] [Iter 1612/2250] R3[561/1200]  | LR: 0.018774 | E:  -60.896294 | E_var:     0.7242 | E_err:   0.009402
[2025-10-22 03:06:47] [Iter 1613/2250] R3[562/1200]  | LR: 0.018741 | E:  -60.893392 | E_var:     0.6107 | E_err:   0.008634
[2025-10-22 03:07:14] [Iter 1614/2250] R3[563/1200]  | LR: 0.018709 | E:  -60.880016 | E_var:     0.7137 | E_err:   0.009334
[2025-10-22 03:07:41] [Iter 1615/2250] R3[564/1200]  | LR: 0.018676 | E:  -60.890085 | E_var:     0.6301 | E_err:   0.008770
[2025-10-22 03:08:08] [Iter 1616/2250] R3[565/1200]  | LR: 0.018644 | E:  -60.885954 | E_var:     0.8525 | E_err:   0.010201
[2025-10-22 03:08:35] [Iter 1617/2250] R3[566/1200]  | LR: 0.018611 | E:  -60.886366 | E_var:     0.7884 | E_err:   0.009810
[2025-10-22 03:09:02] [Iter 1618/2250] R3[567/1200]  | LR: 0.018579 | E:  -60.891813 | E_var:     0.6066 | E_err:   0.008605
[2025-10-22 03:09:29] [Iter 1619/2250] R3[568/1200]  | LR: 0.018546 | E:  -60.892149 | E_var:     0.7854 | E_err:   0.009791
[2025-10-22 03:09:56] [Iter 1620/2250] R3[569/1200]  | LR: 0.018513 | E:  -60.878715 | E_var:     0.6859 | E_err:   0.009150
[2025-10-22 03:10:23] [Iter 1621/2250] R3[570/1200]  | LR: 0.018481 | E:  -60.888775 | E_var:     0.6799 | E_err:   0.009110
[2025-10-22 03:10:50] [Iter 1622/2250] R3[571/1200]  | LR: 0.018448 | E:  -60.895198 | E_var:     0.5812 | E_err:   0.008423
[2025-10-22 03:11:16] [Iter 1623/2250] R3[572/1200]  | LR: 0.018415 | E:  -60.894469 | E_var:     0.6051 | E_err:   0.008595
[2025-10-22 03:11:43] [Iter 1624/2250] R3[573/1200]  | LR: 0.018383 | E:  -60.898978 | E_var:     0.6474 | E_err:   0.008890
[2025-10-22 03:12:10] [Iter 1625/2250] R3[574/1200]  | LR: 0.018350 | E:  -60.884036 | E_var:     0.5768 | E_err:   0.008391
[2025-10-22 03:12:37] [Iter 1626/2250] R3[575/1200]  | LR: 0.018318 | E:  -60.885765 | E_var:     0.7481 | E_err:   0.009556
[2025-10-22 03:13:04] [Iter 1627/2250] R3[576/1200]  | LR: 0.018285 | E:  -60.901280 | E_var:     0.6418 | E_err:   0.008851
[2025-10-22 03:13:31] [Iter 1628/2250] R3[577/1200]  | LR: 0.018252 | E:  -60.899732 | E_var:     0.6995 | E_err:   0.009240
[2025-10-22 03:13:58] [Iter 1629/2250] R3[578/1200]  | LR: 0.018220 | E:  -60.910450 | E_var:     0.7200 | E_err:   0.009375
[2025-10-22 03:14:25] [Iter 1630/2250] R3[579/1200]  | LR: 0.018187 | E:  -60.894731 | E_var:     0.6190 | E_err:   0.008692
[2025-10-22 03:14:52] [Iter 1631/2250] R3[580/1200]  | LR: 0.018154 | E:  -60.890582 | E_var:     0.6319 | E_err:   0.008782
[2025-10-22 03:15:18] [Iter 1632/2250] R3[581/1200]  | LR: 0.018122 | E:  -60.895133 | E_var:     0.6281 | E_err:   0.008757
[2025-10-22 03:15:45] [Iter 1633/2250] R3[582/1200]  | LR: 0.018089 | E:  -60.878840 | E_var:     0.6417 | E_err:   0.008851
[2025-10-22 03:16:12] [Iter 1634/2250] R3[583/1200]  | LR: 0.018056 | E:  -60.889563 | E_var:     0.6342 | E_err:   0.008799
[2025-10-22 03:16:39] [Iter 1635/2250] R3[584/1200]  | LR: 0.018023 | E:  -60.909030 | E_var:     0.6334 | E_err:   0.008793
[2025-10-22 03:17:06] [Iter 1636/2250] R3[585/1200]  | LR: 0.017991 | E:  -60.883014 | E_var:     0.5734 | E_err:   0.008366
[2025-10-22 03:17:33] [Iter 1637/2250] R3[586/1200]  | LR: 0.017958 | E:  -60.911555 | E_var:     0.6269 | E_err:   0.008748
[2025-10-22 03:18:00] [Iter 1638/2250] R3[587/1200]  | LR: 0.017925 | E:  -60.896575 | E_var:     0.6658 | E_err:   0.009015
[2025-10-22 03:18:27] [Iter 1639/2250] R3[588/1200]  | LR: 0.017893 | E:  -60.905058 | E_var:     0.6915 | E_err:   0.009188
[2025-10-22 03:18:54] [Iter 1640/2250] R3[589/1200]  | LR: 0.017860 | E:  -60.900852 | E_var:     0.6302 | E_err:   0.008771
[2025-10-22 03:19:20] [Iter 1641/2250] R3[590/1200]  | LR: 0.017827 | E:  -60.902604 | E_var:     0.6261 | E_err:   0.008742
[2025-10-22 03:19:47] [Iter 1642/2250] R3[591/1200]  | LR: 0.017794 | E:  -60.898388 | E_var:     0.6637 | E_err:   0.009001
[2025-10-22 03:20:14] [Iter 1643/2250] R3[592/1200]  | LR: 0.017762 | E:  -60.898710 | E_var:     0.5968 | E_err:   0.008535
[2025-10-22 03:20:41] [Iter 1644/2250] R3[593/1200]  | LR: 0.017729 | E:  -60.890188 | E_var:     0.7564 | E_err:   0.009609
[2025-10-22 03:21:08] [Iter 1645/2250] R3[594/1200]  | LR: 0.017696 | E:  -60.906112 | E_var:     0.6891 | E_err:   0.009172
[2025-10-22 03:21:35] [Iter 1646/2250] R3[595/1200]  | LR: 0.017664 | E:  -60.912282 | E_var:     0.5729 | E_err:   0.008363
[2025-10-22 03:22:02] [Iter 1647/2250] R3[596/1200]  | LR: 0.017631 | E:  -60.872230 | E_var:     0.7335 | E_err:   0.009463
[2025-10-22 03:22:29] [Iter 1648/2250] R3[597/1200]  | LR: 0.017598 | E:  -60.887866 | E_var:     0.6774 | E_err:   0.009093
[2025-10-22 03:22:56] [Iter 1649/2250] R3[598/1200]  | LR: 0.017565 | E:  -60.894974 | E_var:     0.6032 | E_err:   0.008581
[2025-10-22 03:23:22] [Iter 1650/2250] R3[599/1200]  | LR: 0.017533 | E:  -60.889458 | E_var:     0.6006 | E_err:   0.008562
[2025-10-22 03:23:49] [Iter 1651/2250] R3[600/1200]  | LR: 0.017500 | E:  -60.896817 | E_var:     0.6017 | E_err:   0.008571
[2025-10-22 03:24:16] [Iter 1652/2250] R3[601/1200]  | LR: 0.017467 | E:  -60.885758 | E_var:     0.5842 | E_err:   0.008445
[2025-10-22 03:24:43] [Iter 1653/2250] R3[602/1200]  | LR: 0.017435 | E:  -60.887943 | E_var:     0.6470 | E_err:   0.008887
[2025-10-22 03:25:10] [Iter 1654/2250] R3[603/1200]  | LR: 0.017402 | E:  -60.892160 | E_var:     0.6160 | E_err:   0.008672
[2025-10-22 03:25:37] [Iter 1655/2250] R3[604/1200]  | LR: 0.017369 | E:  -60.898149 | E_var:     0.5968 | E_err:   0.008535
[2025-10-22 03:26:04] [Iter 1656/2250] R3[605/1200]  | LR: 0.017336 | E:  -60.882187 | E_var:     0.7897 | E_err:   0.009818
[2025-10-22 03:26:31] [Iter 1657/2250] R3[606/1200]  | LR: 0.017304 | E:  -60.881742 | E_var:     0.7369 | E_err:   0.009484
[2025-10-22 03:26:58] [Iter 1658/2250] R3[607/1200]  | LR: 0.017271 | E:  -60.894668 | E_var:     0.6559 | E_err:   0.008948
[2025-10-22 03:27:24] [Iter 1659/2250] R3[608/1200]  | LR: 0.017238 | E:  -60.888042 | E_var:     0.6400 | E_err:   0.008839
[2025-10-22 03:27:51] [Iter 1660/2250] R3[609/1200]  | LR: 0.017206 | E:  -60.897145 | E_var:     0.9654 | E_err:   0.010856
[2025-10-22 03:28:18] [Iter 1661/2250] R3[610/1200]  | LR: 0.017173 | E:  -60.889959 | E_var:     0.6053 | E_err:   0.008596
[2025-10-22 03:28:45] [Iter 1662/2250] R3[611/1200]  | LR: 0.017140 | E:  -60.901064 | E_var:     0.6918 | E_err:   0.009190
[2025-10-22 03:29:12] [Iter 1663/2250] R3[612/1200]  | LR: 0.017107 | E:  -60.889619 | E_var:     0.6406 | E_err:   0.008843
[2025-10-22 03:29:39] [Iter 1664/2250] R3[613/1200]  | LR: 0.017075 | E:  -60.887042 | E_var:     0.5829 | E_err:   0.008436
[2025-10-22 03:30:06] [Iter 1665/2250] R3[614/1200]  | LR: 0.017042 | E:  -60.889728 | E_var:     0.6539 | E_err:   0.008935
[2025-10-22 03:30:33] [Iter 1666/2250] R3[615/1200]  | LR: 0.017009 | E:  -60.892871 | E_var:     0.6340 | E_err:   0.008797
[2025-10-22 03:31:00] [Iter 1667/2250] R3[616/1200]  | LR: 0.016977 | E:  -60.907378 | E_var:     0.9744 | E_err:   0.010906
[2025-10-22 03:31:26] [Iter 1668/2250] R3[617/1200]  | LR: 0.016944 | E:  -60.892818 | E_var:     0.6614 | E_err:   0.008985
[2025-10-22 03:31:53] [Iter 1669/2250] R3[618/1200]  | LR: 0.016911 | E:  -60.890318 | E_var:     0.9372 | E_err:   0.010696
[2025-10-22 03:32:20] [Iter 1670/2250] R3[619/1200]  | LR: 0.016878 | E:  -60.915907 | E_var:     0.6026 | E_err:   0.008577
[2025-10-22 03:32:47] [Iter 1671/2250] R3[620/1200]  | LR: 0.016846 | E:  -60.897591 | E_var:     0.6617 | E_err:   0.008988
[2025-10-22 03:33:14] [Iter 1672/2250] R3[621/1200]  | LR: 0.016813 | E:  -60.895826 | E_var:     0.6018 | E_err:   0.008571
[2025-10-22 03:33:41] [Iter 1673/2250] R3[622/1200]  | LR: 0.016780 | E:  -60.895339 | E_var:     0.6162 | E_err:   0.008673
[2025-10-22 03:34:08] [Iter 1674/2250] R3[623/1200]  | LR: 0.016748 | E:  -60.897924 | E_var:     0.8327 | E_err:   0.010082
[2025-10-22 03:34:35] [Iter 1675/2250] R3[624/1200]  | LR: 0.016715 | E:  -60.899911 | E_var:     0.6013 | E_err:   0.008567
[2025-10-22 03:35:02] [Iter 1676/2250] R3[625/1200]  | LR: 0.016682 | E:  -60.909641 | E_var:     0.7242 | E_err:   0.009402
[2025-10-22 03:35:28] [Iter 1677/2250] R3[626/1200]  | LR: 0.016650 | E:  -60.879735 | E_var:     0.7607 | E_err:   0.009636
[2025-10-22 03:35:55] [Iter 1678/2250] R3[627/1200]  | LR: 0.016617 | E:  -60.900440 | E_var:     0.6094 | E_err:   0.008625
[2025-10-22 03:36:22] [Iter 1679/2250] R3[628/1200]  | LR: 0.016585 | E:  -60.902982 | E_var:     0.5961 | E_err:   0.008530
[2025-10-22 03:36:49] [Iter 1680/2250] R3[629/1200]  | LR: 0.016552 | E:  -60.899143 | E_var:     0.6457 | E_err:   0.008878
[2025-10-22 03:37:16] [Iter 1681/2250] R3[630/1200]  | LR: 0.016519 | E:  -60.893539 | E_var:     0.6305 | E_err:   0.008773
[2025-10-22 03:37:43] [Iter 1682/2250] R3[631/1200]  | LR: 0.016487 | E:  -60.893280 | E_var:     0.6385 | E_err:   0.008829
[2025-10-22 03:38:10] [Iter 1683/2250] R3[632/1200]  | LR: 0.016454 | E:  -60.892441 | E_var:     0.6575 | E_err:   0.008959
[2025-10-22 03:38:37] [Iter 1684/2250] R3[633/1200]  | LR: 0.016421 | E:  -60.882786 | E_var:     0.6310 | E_err:   0.008776
[2025-10-22 03:39:04] [Iter 1685/2250] R3[634/1200]  | LR: 0.016389 | E:  -60.887221 | E_var:     0.5683 | E_err:   0.008329
[2025-10-22 03:39:31] [Iter 1686/2250] R3[635/1200]  | LR: 0.016356 | E:  -60.901458 | E_var:     0.5916 | E_err:   0.008498
[2025-10-22 03:39:58] [Iter 1687/2250] R3[636/1200]  | LR: 0.016324 | E:  -60.887088 | E_var:     0.5674 | E_err:   0.008322
[2025-10-22 03:40:24] [Iter 1688/2250] R3[637/1200]  | LR: 0.016291 | E:  -60.899669 | E_var:     0.6138 | E_err:   0.008656
[2025-10-22 03:40:51] [Iter 1689/2250] R3[638/1200]  | LR: 0.016259 | E:  -60.911585 | E_var:     0.7089 | E_err:   0.009303
[2025-10-22 03:41:18] [Iter 1690/2250] R3[639/1200]  | LR: 0.016226 | E:  -60.891096 | E_var:     0.6123 | E_err:   0.008646
[2025-10-22 03:41:45] [Iter 1691/2250] R3[640/1200]  | LR: 0.016193 | E:  -60.902749 | E_var:     1.3763 | E_err:   0.012962
[2025-10-22 03:42:12] [Iter 1692/2250] R3[641/1200]  | LR: 0.016161 | E:  -60.896177 | E_var:     0.6253 | E_err:   0.008737
[2025-10-22 03:42:39] [Iter 1693/2250] R3[642/1200]  | LR: 0.016128 | E:  -60.885875 | E_var:     0.6968 | E_err:   0.009222
[2025-10-22 03:43:06] [Iter 1694/2250] R3[643/1200]  | LR: 0.016096 | E:  -60.884162 | E_var:     0.7083 | E_err:   0.009298
[2025-10-22 03:43:33] [Iter 1695/2250] R3[644/1200]  | LR: 0.016063 | E:  -60.898764 | E_var:     0.8047 | E_err:   0.009911
[2025-10-22 03:44:00] [Iter 1696/2250] R3[645/1200]  | LR: 0.016031 | E:  -60.906885 | E_var:     0.7050 | E_err:   0.009277
[2025-10-22 03:44:26] [Iter 1697/2250] R3[646/1200]  | LR: 0.015998 | E:  -60.880442 | E_var:     0.6745 | E_err:   0.009074
[2025-10-22 03:44:53] [Iter 1698/2250] R3[647/1200]  | LR: 0.015966 | E:  -60.895095 | E_var:     0.6095 | E_err:   0.008626
[2025-10-22 03:45:20] [Iter 1699/2250] R3[648/1200]  | LR: 0.015933 | E:  -60.881977 | E_var:     0.7791 | E_err:   0.009752
[2025-10-22 03:45:47] [Iter 1700/2250] R3[649/1200]  | LR: 0.015901 | E:  -60.905242 | E_var:     0.6981 | E_err:   0.009231
[2025-10-22 03:46:14] [Iter 1701/2250] R3[650/1200]  | LR: 0.015868 | E:  -60.908212 | E_var:     0.6712 | E_err:   0.009052
[2025-10-22 03:46:41] [Iter 1702/2250] R3[651/1200]  | LR: 0.015836 | E:  -60.894808 | E_var:     0.7152 | E_err:   0.009344
[2025-10-22 03:47:08] [Iter 1703/2250] R3[652/1200]  | LR: 0.015804 | E:  -60.896710 | E_var:     0.6315 | E_err:   0.008780
[2025-10-22 03:47:35] [Iter 1704/2250] R3[653/1200]  | LR: 0.015771 | E:  -60.903253 | E_var:     0.6718 | E_err:   0.009055
[2025-10-22 03:48:02] [Iter 1705/2250] R3[654/1200]  | LR: 0.015739 | E:  -60.904108 | E_var:     0.6102 | E_err:   0.008631
[2025-10-22 03:48:28] [Iter 1706/2250] R3[655/1200]  | LR: 0.015706 | E:  -60.881018 | E_var:     0.6359 | E_err:   0.008811
[2025-10-22 03:48:55] [Iter 1707/2250] R3[656/1200]  | LR: 0.015674 | E:  -60.890789 | E_var:     0.6291 | E_err:   0.008763
[2025-10-22 03:49:22] [Iter 1708/2250] R3[657/1200]  | LR: 0.015642 | E:  -60.907648 | E_var:     0.5938 | E_err:   0.008514
[2025-10-22 03:49:49] [Iter 1709/2250] R3[658/1200]  | LR: 0.015609 | E:  -60.883550 | E_var:     0.6361 | E_err:   0.008812
[2025-10-22 03:50:16] [Iter 1710/2250] R3[659/1200]  | LR: 0.015577 | E:  -60.901783 | E_var:     0.7171 | E_err:   0.009356
[2025-10-22 03:50:43] [Iter 1711/2250] R3[660/1200]  | LR: 0.015545 | E:  -60.883604 | E_var:     0.7233 | E_err:   0.009397
[2025-10-22 03:51:10] [Iter 1712/2250] R3[661/1200]  | LR: 0.015512 | E:  -60.902276 | E_var:     0.7306 | E_err:   0.009444
[2025-10-22 03:51:37] [Iter 1713/2250] R3[662/1200]  | LR: 0.015480 | E:  -60.882437 | E_var:     0.6523 | E_err:   0.008923
[2025-10-22 03:52:04] [Iter 1714/2250] R3[663/1200]  | LR: 0.015448 | E:  -60.895198 | E_var:     0.6749 | E_err:   0.009076
[2025-10-22 03:52:30] [Iter 1715/2250] R3[664/1200]  | LR: 0.015415 | E:  -60.899907 | E_var:     0.6427 | E_err:   0.008857
[2025-10-22 03:52:57] [Iter 1716/2250] R3[665/1200]  | LR: 0.015383 | E:  -60.893796 | E_var:     0.7376 | E_err:   0.009489
[2025-10-22 03:53:24] [Iter 1717/2250] R3[666/1200]  | LR: 0.015351 | E:  -60.896877 | E_var:     0.6596 | E_err:   0.008973
[2025-10-22 03:53:51] [Iter 1718/2250] R3[667/1200]  | LR: 0.015319 | E:  -60.890027 | E_var:     0.7136 | E_err:   0.009333
[2025-10-22 03:54:18] [Iter 1719/2250] R3[668/1200]  | LR: 0.015286 | E:  -60.898492 | E_var:     0.6402 | E_err:   0.008840
[2025-10-22 03:54:45] [Iter 1720/2250] R3[669/1200]  | LR: 0.015254 | E:  -60.892315 | E_var:     0.5755 | E_err:   0.008382
[2025-10-22 03:55:12] [Iter 1721/2250] R3[670/1200]  | LR: 0.015222 | E:  -60.908716 | E_var:     0.6200 | E_err:   0.008700
[2025-10-22 03:55:39] [Iter 1722/2250] R3[671/1200]  | LR: 0.015190 | E:  -60.875454 | E_var:     0.5691 | E_err:   0.008335
[2025-10-22 03:56:06] [Iter 1723/2250] R3[672/1200]  | LR: 0.015158 | E:  -60.903605 | E_var:     0.5997 | E_err:   0.008556
[2025-10-22 03:56:33] [Iter 1724/2250] R3[673/1200]  | LR: 0.015126 | E:  -60.889057 | E_var:     0.7081 | E_err:   0.009297
[2025-10-22 03:56:59] [Iter 1725/2250] R3[674/1200]  | LR: 0.015093 | E:  -60.887734 | E_var:     0.6409 | E_err:   0.008845
[2025-10-22 03:57:26] [Iter 1726/2250] R3[675/1200]  | LR: 0.015061 | E:  -60.886301 | E_var:     0.7505 | E_err:   0.009571
[2025-10-22 03:57:53] [Iter 1727/2250] R3[676/1200]  | LR: 0.015029 | E:  -60.888535 | E_var:     0.6324 | E_err:   0.008786
[2025-10-22 03:58:20] [Iter 1728/2250] R3[677/1200]  | LR: 0.014997 | E:  -60.881631 | E_var:     0.6273 | E_err:   0.008751
[2025-10-22 03:58:47] [Iter 1729/2250] R3[678/1200]  | LR: 0.014965 | E:  -60.893057 | E_var:     0.6059 | E_err:   0.008600
[2025-10-22 03:59:14] [Iter 1730/2250] R3[679/1200]  | LR: 0.014933 | E:  -60.908847 | E_var:     0.6514 | E_err:   0.008917
[2025-10-22 03:59:41] [Iter 1731/2250] R3[680/1200]  | LR: 0.014901 | E:  -60.905030 | E_var:     0.6351 | E_err:   0.008805
[2025-10-22 04:00:08] [Iter 1732/2250] R3[681/1200]  | LR: 0.014869 | E:  -60.901101 | E_var:     0.5786 | E_err:   0.008404
[2025-10-22 04:00:35] [Iter 1733/2250] R3[682/1200]  | LR: 0.014837 | E:  -60.892489 | E_var:     0.6547 | E_err:   0.008940
[2025-10-22 04:01:01] [Iter 1734/2250] R3[683/1200]  | LR: 0.014805 | E:  -60.908323 | E_var:     0.5768 | E_err:   0.008391
[2025-10-22 04:01:28] [Iter 1735/2250] R3[684/1200]  | LR: 0.014773 | E:  -60.893664 | E_var:     0.5939 | E_err:   0.008514
[2025-10-22 04:01:55] [Iter 1736/2250] R3[685/1200]  | LR: 0.014741 | E:  -60.889020 | E_var:     0.5152 | E_err:   0.007931
[2025-10-22 04:02:22] [Iter 1737/2250] R3[686/1200]  | LR: 0.014709 | E:  -60.883058 | E_var:     0.6803 | E_err:   0.009113
[2025-10-22 04:02:49] [Iter 1738/2250] R3[687/1200]  | LR: 0.014677 | E:  -60.877898 | E_var:     0.6615 | E_err:   0.008986
[2025-10-22 04:03:16] [Iter 1739/2250] R3[688/1200]  | LR: 0.014646 | E:  -60.896309 | E_var:     0.6820 | E_err:   0.009124
[2025-10-22 04:03:43] [Iter 1740/2250] R3[689/1200]  | LR: 0.014614 | E:  -60.894164 | E_var:     0.6774 | E_err:   0.009093
[2025-10-22 04:04:10] [Iter 1741/2250] R3[690/1200]  | LR: 0.014582 | E:  -60.886058 | E_var:     0.6421 | E_err:   0.008853
[2025-10-22 04:04:36] [Iter 1742/2250] R3[691/1200]  | LR: 0.014550 | E:  -60.915865 | E_var:     0.6448 | E_err:   0.008872
[2025-10-22 04:05:03] [Iter 1743/2250] R3[692/1200]  | LR: 0.014518 | E:  -60.896191 | E_var:     0.6683 | E_err:   0.009032
[2025-10-22 04:05:30] [Iter 1744/2250] R3[693/1200]  | LR: 0.014487 | E:  -60.905272 | E_var:     0.6339 | E_err:   0.008797
[2025-10-22 04:05:57] [Iter 1745/2250] R3[694/1200]  | LR: 0.014455 | E:  -60.909324 | E_var:     0.6168 | E_err:   0.008677
[2025-10-22 04:06:24] [Iter 1746/2250] R3[695/1200]  | LR: 0.014423 | E:  -60.887689 | E_var:     0.5886 | E_err:   0.008477
[2025-10-22 04:06:51] [Iter 1747/2250] R3[696/1200]  | LR: 0.014391 | E:  -60.901752 | E_var:     0.6457 | E_err:   0.008878
[2025-10-22 04:07:18] [Iter 1748/2250] R3[697/1200]  | LR: 0.014360 | E:  -60.875218 | E_var:     0.7494 | E_err:   0.009564
[2025-10-22 04:07:45] [Iter 1749/2250] R3[698/1200]  | LR: 0.014328 | E:  -60.883625 | E_var:     0.7377 | E_err:   0.009489
[2025-10-22 04:08:12] [Iter 1750/2250] R3[699/1200]  | LR: 0.014296 | E:  -60.891909 | E_var:     0.5778 | E_err:   0.008398
[2025-10-22 04:08:38] [Iter 1751/2250] R3[700/1200]  | LR: 0.014265 | E:  -60.894109 | E_var:     0.6701 | E_err:   0.009045
[2025-10-22 04:09:05] [Iter 1752/2250] R3[701/1200]  | LR: 0.014233 | E:  -60.907423 | E_var:     0.6616 | E_err:   0.008987
[2025-10-22 04:09:32] [Iter 1753/2250] R3[702/1200]  | LR: 0.014202 | E:  -60.906559 | E_var:     0.6934 | E_err:   0.009200
[2025-10-22 04:09:59] [Iter 1754/2250] R3[703/1200]  | LR: 0.014170 | E:  -60.900584 | E_var:     0.8856 | E_err:   0.010398
[2025-10-22 04:10:26] [Iter 1755/2250] R3[704/1200]  | LR: 0.014139 | E:  -60.894556 | E_var:     0.7314 | E_err:   0.009449
[2025-10-22 04:10:53] [Iter 1756/2250] R3[705/1200]  | LR: 0.014107 | E:  -60.896413 | E_var:     0.7271 | E_err:   0.009421
[2025-10-22 04:11:20] [Iter 1757/2250] R3[706/1200]  | LR: 0.014076 | E:  -60.896712 | E_var:     0.5926 | E_err:   0.008505
[2025-10-22 04:11:47] [Iter 1758/2250] R3[707/1200]  | LR: 0.014044 | E:  -60.895488 | E_var:     0.6450 | E_err:   0.008873
[2025-10-22 04:12:14] [Iter 1759/2250] R3[708/1200]  | LR: 0.014013 | E:  -60.900517 | E_var:     0.6398 | E_err:   0.008838
[2025-10-22 04:12:40] [Iter 1760/2250] R3[709/1200]  | LR: 0.013981 | E:  -60.880464 | E_var:     0.6115 | E_err:   0.008640
[2025-10-22 04:13:07] [Iter 1761/2250] R3[710/1200]  | LR: 0.013950 | E:  -60.886533 | E_var:     0.5771 | E_err:   0.008393
[2025-10-22 04:13:34] [Iter 1762/2250] R3[711/1200]  | LR: 0.013918 | E:  -60.895114 | E_var:     0.5964 | E_err:   0.008532
[2025-10-22 04:14:01] [Iter 1763/2250] R3[712/1200]  | LR: 0.013887 | E:  -60.897685 | E_var:     0.6404 | E_err:   0.008842
[2025-10-22 04:14:28] [Iter 1764/2250] R3[713/1200]  | LR: 0.013856 | E:  -60.911577 | E_var:     0.6346 | E_err:   0.008802
[2025-10-22 04:14:55] [Iter 1765/2250] R3[714/1200]  | LR: 0.013824 | E:  -60.903543 | E_var:     0.6899 | E_err:   0.009177
[2025-10-22 04:15:22] [Iter 1766/2250] R3[715/1200]  | LR: 0.013793 | E:  -60.899767 | E_var:     0.6491 | E_err:   0.008901
[2025-10-22 04:15:49] [Iter 1767/2250] R3[716/1200]  | LR: 0.013762 | E:  -60.892379 | E_var:     0.6576 | E_err:   0.008959
[2025-10-22 04:16:16] [Iter 1768/2250] R3[717/1200]  | LR: 0.013731 | E:  -60.904791 | E_var:     0.6532 | E_err:   0.008930
[2025-10-22 04:16:42] [Iter 1769/2250] R3[718/1200]  | LR: 0.013700 | E:  -60.912071 | E_var:     0.6385 | E_err:   0.008828
[2025-10-22 04:17:09] [Iter 1770/2250] R3[719/1200]  | LR: 0.013668 | E:  -60.910466 | E_var:     0.5669 | E_err:   0.008319
[2025-10-22 04:17:36] [Iter 1771/2250] R3[720/1200]  | LR: 0.013637 | E:  -60.916794 | E_var:     0.5788 | E_err:   0.008406
[2025-10-22 04:18:03] [Iter 1772/2250] R3[721/1200]  | LR: 0.013606 | E:  -60.912724 | E_var:     0.7042 | E_err:   0.009271
[2025-10-22 04:18:30] [Iter 1773/2250] R3[722/1200]  | LR: 0.013575 | E:  -60.886163 | E_var:     0.8042 | E_err:   0.009908
[2025-10-22 04:18:57] [Iter 1774/2250] R3[723/1200]  | LR: 0.013544 | E:  -60.910549 | E_var:     0.6118 | E_err:   0.008642
[2025-10-22 04:19:24] [Iter 1775/2250] R3[724/1200]  | LR: 0.013513 | E:  -60.886121 | E_var:     0.6570 | E_err:   0.008956
[2025-10-22 04:19:51] [Iter 1776/2250] R3[725/1200]  | LR: 0.013482 | E:  -60.917108 | E_var:     0.7710 | E_err:   0.009702
[2025-10-22 04:20:18] [Iter 1777/2250] R3[726/1200]  | LR: 0.013451 | E:  -60.905147 | E_var:     0.6304 | E_err:   0.008773
[2025-10-22 04:20:44] [Iter 1778/2250] R3[727/1200]  | LR: 0.013420 | E:  -60.877906 | E_var:     0.7677 | E_err:   0.009681
[2025-10-22 04:21:11] [Iter 1779/2250] R3[728/1200]  | LR: 0.013389 | E:  -60.898760 | E_var:     0.6227 | E_err:   0.008719
[2025-10-22 04:21:38] [Iter 1780/2250] R3[729/1200]  | LR: 0.013358 | E:  -60.899426 | E_var:     0.6501 | E_err:   0.008908
[2025-10-22 04:22:05] [Iter 1781/2250] R3[730/1200]  | LR: 0.013327 | E:  -60.902237 | E_var:     0.6603 | E_err:   0.008978
[2025-10-22 04:22:32] [Iter 1782/2250] R3[731/1200]  | LR: 0.013297 | E:  -60.892190 | E_var:     0.6956 | E_err:   0.009215
[2025-10-22 04:22:59] [Iter 1783/2250] R3[732/1200]  | LR: 0.013266 | E:  -60.897120 | E_var:     0.6387 | E_err:   0.008830
[2025-10-22 04:23:26] [Iter 1784/2250] R3[733/1200]  | LR: 0.013235 | E:  -60.871657 | E_var:     0.6580 | E_err:   0.008962
[2025-10-22 04:23:53] [Iter 1785/2250] R3[734/1200]  | LR: 0.013204 | E:  -60.889808 | E_var:     0.6295 | E_err:   0.008766
[2025-10-22 04:24:20] [Iter 1786/2250] R3[735/1200]  | LR: 0.013174 | E:  -60.896463 | E_var:     0.6396 | E_err:   0.008836
[2025-10-22 04:24:47] [Iter 1787/2250] R3[736/1200]  | LR: 0.013143 | E:  -60.907948 | E_var:     0.6993 | E_err:   0.009239
[2025-10-22 04:25:13] [Iter 1788/2250] R3[737/1200]  | LR: 0.013112 | E:  -60.879523 | E_var:     0.6812 | E_err:   0.009119
[2025-10-22 04:25:40] [Iter 1789/2250] R3[738/1200]  | LR: 0.013082 | E:  -60.911958 | E_var:     0.6958 | E_err:   0.009216
[2025-10-22 04:26:07] [Iter 1790/2250] R3[739/1200]  | LR: 0.013051 | E:  -60.910129 | E_var:     0.7831 | E_err:   0.009777
[2025-10-22 04:26:34] [Iter 1791/2250] R3[740/1200]  | LR: 0.013020 | E:  -60.916645 | E_var:     0.5778 | E_err:   0.008399
[2025-10-22 04:27:01] [Iter 1792/2250] R3[741/1200]  | LR: 0.012990 | E:  -60.891059 | E_var:     0.6763 | E_err:   0.009086
[2025-10-22 04:27:28] [Iter 1793/2250] R3[742/1200]  | LR: 0.012959 | E:  -60.893963 | E_var:     0.5946 | E_err:   0.008519
[2025-10-22 04:27:55] [Iter 1794/2250] R3[743/1200]  | LR: 0.012929 | E:  -60.890049 | E_var:     0.6103 | E_err:   0.008631
[2025-10-22 04:28:22] [Iter 1795/2250] R3[744/1200]  | LR: 0.012898 | E:  -60.883514 | E_var:     0.7258 | E_err:   0.009413
[2025-10-22 04:28:49] [Iter 1796/2250] R3[745/1200]  | LR: 0.012868 | E:  -60.911218 | E_var:     0.5822 | E_err:   0.008430
[2025-10-22 04:29:15] [Iter 1797/2250] R3[746/1200]  | LR: 0.012838 | E:  -60.912078 | E_var:     0.6237 | E_err:   0.008726
[2025-10-22 04:29:42] [Iter 1798/2250] R3[747/1200]  | LR: 0.012807 | E:  -60.892639 | E_var:     0.6877 | E_err:   0.009162
[2025-10-22 04:30:09] [Iter 1799/2250] R3[748/1200]  | LR: 0.012777 | E:  -60.916406 | E_var:     0.6648 | E_err:   0.009009
[2025-10-22 04:30:36] [Iter 1800/2250] R3[749/1200]  | LR: 0.012747 | E:  -60.890791 | E_var:     0.7190 | E_err:   0.009368
[2025-10-22 04:30:36] ✓ Checkpoint saved: checkpoint_iter_001800.pkl
[2025-10-22 04:31:03] [Iter 1801/2250] R3[750/1200]  | LR: 0.012716 | E:  -60.889341 | E_var:     0.7072 | E_err:   0.009291
[2025-10-22 04:31:30] [Iter 1802/2250] R3[751/1200]  | LR: 0.012686 | E:  -60.903917 | E_var:     0.7304 | E_err:   0.009442
[2025-10-22 04:31:57] [Iter 1803/2250] R3[752/1200]  | LR: 0.012656 | E:  -60.894302 | E_var:     0.6775 | E_err:   0.009094
[2025-10-22 04:32:24] [Iter 1804/2250] R3[753/1200]  | LR: 0.012626 | E:  -60.890420 | E_var:     0.7027 | E_err:   0.009262
[2025-10-22 04:32:51] [Iter 1805/2250] R3[754/1200]  | LR: 0.012596 | E:  -60.904119 | E_var:     0.5956 | E_err:   0.008527
[2025-10-22 04:33:17] [Iter 1806/2250] R3[755/1200]  | LR: 0.012566 | E:  -60.904679 | E_var:     0.6275 | E_err:   0.008752
[2025-10-22 04:33:44] [Iter 1807/2250] R3[756/1200]  | LR: 0.012536 | E:  -60.883169 | E_var:     0.6377 | E_err:   0.008823
[2025-10-22 04:34:11] [Iter 1808/2250] R3[757/1200]  | LR: 0.012506 | E:  -60.897840 | E_var:     0.5718 | E_err:   0.008354
[2025-10-22 04:34:38] [Iter 1809/2250] R3[758/1200]  | LR: 0.012476 | E:  -60.887336 | E_var:     0.8063 | E_err:   0.009921
[2025-10-22 04:35:05] [Iter 1810/2250] R3[759/1200]  | LR: 0.012446 | E:  -60.903142 | E_var:     0.6792 | E_err:   0.009106
[2025-10-22 04:35:32] [Iter 1811/2250] R3[760/1200]  | LR: 0.012416 | E:  -60.903229 | E_var:     0.6545 | E_err:   0.008939
[2025-10-22 04:35:59] [Iter 1812/2250] R3[761/1200]  | LR: 0.012386 | E:  -60.895508 | E_var:     0.7368 | E_err:   0.009484
[2025-10-22 04:36:26] [Iter 1813/2250] R3[762/1200]  | LR: 0.012356 | E:  -60.893745 | E_var:     0.7017 | E_err:   0.009255
[2025-10-22 04:36:53] [Iter 1814/2250] R3[763/1200]  | LR: 0.012326 | E:  -60.895174 | E_var:     0.5899 | E_err:   0.008486
[2025-10-22 04:37:19] [Iter 1815/2250] R3[764/1200]  | LR: 0.012296 | E:  -60.915040 | E_var:     0.7127 | E_err:   0.009327
[2025-10-22 04:37:46] [Iter 1816/2250] R3[765/1200]  | LR: 0.012267 | E:  -60.881088 | E_var:     0.6287 | E_err:   0.008761
[2025-10-22 04:38:13] [Iter 1817/2250] R3[766/1200]  | LR: 0.012237 | E:  -60.898411 | E_var:     0.6753 | E_err:   0.009079
[2025-10-22 04:38:40] [Iter 1818/2250] R3[767/1200]  | LR: 0.012207 | E:  -60.897573 | E_var:     0.6200 | E_err:   0.008700
[2025-10-22 04:39:07] [Iter 1819/2250] R3[768/1200]  | LR: 0.012178 | E:  -60.906484 | E_var:     0.8181 | E_err:   0.009993
[2025-10-22 04:39:34] [Iter 1820/2250] R3[769/1200]  | LR: 0.012148 | E:  -60.898634 | E_var:     0.6783 | E_err:   0.009100
[2025-10-22 04:40:01] [Iter 1821/2250] R3[770/1200]  | LR: 0.012119 | E:  -60.908454 | E_var:     0.5531 | E_err:   0.008217
[2025-10-22 04:40:28] [Iter 1822/2250] R3[771/1200]  | LR: 0.012089 | E:  -60.901640 | E_var:     0.7730 | E_err:   0.009714
[2025-10-22 04:40:54] [Iter 1823/2250] R3[772/1200]  | LR: 0.012060 | E:  -60.898062 | E_var:     0.6387 | E_err:   0.008830
[2025-10-22 04:41:21] [Iter 1824/2250] R3[773/1200]  | LR: 0.012030 | E:  -60.909057 | E_var:     0.6537 | E_err:   0.008933
[2025-10-22 04:41:48] [Iter 1825/2250] R3[774/1200]  | LR: 0.012001 | E:  -60.894083 | E_var:     0.6640 | E_err:   0.009003
[2025-10-22 04:42:15] [Iter 1826/2250] R3[775/1200]  | LR: 0.011971 | E:  -60.912336 | E_var:     0.6254 | E_err:   0.008738
[2025-10-22 04:42:42] [Iter 1827/2250] R3[776/1200]  | LR: 0.011942 | E:  -60.901868 | E_var:     0.5830 | E_err:   0.008436
[2025-10-22 04:43:09] [Iter 1828/2250] R3[777/1200]  | LR: 0.011913 | E:  -60.908794 | E_var:     0.5870 | E_err:   0.008465
[2025-10-22 04:43:36] [Iter 1829/2250] R3[778/1200]  | LR: 0.011884 | E:  -60.911449 | E_var:     0.6069 | E_err:   0.008607
[2025-10-22 04:44:03] [Iter 1830/2250] R3[779/1200]  | LR: 0.011854 | E:  -60.902140 | E_var:     0.6256 | E_err:   0.008739
[2025-10-22 04:44:30] [Iter 1831/2250] R3[780/1200]  | LR: 0.011825 | E:  -60.894389 | E_var:     0.6698 | E_err:   0.009042
[2025-10-22 04:44:56] [Iter 1832/2250] R3[781/1200]  | LR: 0.011796 | E:  -60.883546 | E_var:     0.8154 | E_err:   0.009977
[2025-10-22 04:45:23] [Iter 1833/2250] R3[782/1200]  | LR: 0.011767 | E:  -60.904409 | E_var:     0.6155 | E_err:   0.008668
[2025-10-22 04:45:50] [Iter 1834/2250] R3[783/1200]  | LR: 0.011738 | E:  -60.910444 | E_var:     0.6740 | E_err:   0.009071
[2025-10-22 04:46:17] [Iter 1835/2250] R3[784/1200]  | LR: 0.011709 | E:  -60.906116 | E_var:     0.6031 | E_err:   0.008580
[2025-10-22 04:46:44] [Iter 1836/2250] R3[785/1200]  | LR: 0.011680 | E:  -60.897068 | E_var:     0.6259 | E_err:   0.008741
[2025-10-22 04:47:11] [Iter 1837/2250] R3[786/1200]  | LR: 0.011651 | E:  -60.901097 | E_var:     1.7068 | E_err:   0.014434
[2025-10-22 04:47:38] [Iter 1838/2250] R3[787/1200]  | LR: 0.011622 | E:  -60.913901 | E_var:     0.6455 | E_err:   0.008877
[2025-10-22 04:48:05] [Iter 1839/2250] R3[788/1200]  | LR: 0.011593 | E:  -60.895994 | E_var:     0.6406 | E_err:   0.008843
[2025-10-22 04:48:31] [Iter 1840/2250] R3[789/1200]  | LR: 0.011564 | E:  -60.898100 | E_var:     0.7427 | E_err:   0.009522
[2025-10-22 04:48:58] [Iter 1841/2250] R3[790/1200]  | LR: 0.011536 | E:  -60.896847 | E_var:     0.6511 | E_err:   0.008915
[2025-10-22 04:49:25] [Iter 1842/2250] R3[791/1200]  | LR: 0.011507 | E:  -60.907092 | E_var:     0.6799 | E_err:   0.009110
[2025-10-22 04:49:52] [Iter 1843/2250] R3[792/1200]  | LR: 0.011478 | E:  -60.907710 | E_var:     0.5561 | E_err:   0.008239
[2025-10-22 04:50:19] [Iter 1844/2250] R3[793/1200]  | LR: 0.011449 | E:  -60.897536 | E_var:     0.6962 | E_err:   0.009219
[2025-10-22 04:50:46] [Iter 1845/2250] R3[794/1200]  | LR: 0.011421 | E:  -60.888999 | E_var:     0.7091 | E_err:   0.009303
[2025-10-22 04:51:13] [Iter 1846/2250] R3[795/1200]  | LR: 0.011392 | E:  -60.893286 | E_var:     0.6032 | E_err:   0.008581
[2025-10-22 04:51:40] [Iter 1847/2250] R3[796/1200]  | LR: 0.011364 | E:  -60.891603 | E_var:     0.6303 | E_err:   0.008772
[2025-10-22 04:52:06] [Iter 1848/2250] R3[797/1200]  | LR: 0.011335 | E:  -60.887652 | E_var:     0.7196 | E_err:   0.009372
[2025-10-22 04:52:33] [Iter 1849/2250] R3[798/1200]  | LR: 0.011307 | E:  -60.902667 | E_var:     0.6930 | E_err:   0.009197
[2025-10-22 04:53:00] [Iter 1850/2250] R3[799/1200]  | LR: 0.011278 | E:  -60.887795 | E_var:     0.6230 | E_err:   0.008721
[2025-10-22 04:53:27] [Iter 1851/2250] R3[800/1200]  | LR: 0.011250 | E:  -60.913907 | E_var:     0.5848 | E_err:   0.008449
[2025-10-22 04:53:54] [Iter 1852/2250] R3[801/1200]  | LR: 0.011222 | E:  -60.901742 | E_var:     0.6588 | E_err:   0.008968
[2025-10-22 04:54:21] [Iter 1853/2250] R3[802/1200]  | LR: 0.011193 | E:  -60.895902 | E_var:     0.7143 | E_err:   0.009338
[2025-10-22 04:54:48] [Iter 1854/2250] R3[803/1200]  | LR: 0.011165 | E:  -60.894436 | E_var:     0.6723 | E_err:   0.009059
[2025-10-22 04:55:15] [Iter 1855/2250] R3[804/1200]  | LR: 0.011137 | E:  -60.901737 | E_var:     0.5768 | E_err:   0.008391
[2025-10-22 04:55:42] [Iter 1856/2250] R3[805/1200]  | LR: 0.011109 | E:  -60.900206 | E_var:     0.5638 | E_err:   0.008296
[2025-10-22 04:56:08] [Iter 1857/2250] R3[806/1200]  | LR: 0.011081 | E:  -60.909044 | E_var:     0.6576 | E_err:   0.008959
[2025-10-22 04:56:35] [Iter 1858/2250] R3[807/1200]  | LR: 0.011053 | E:  -60.898339 | E_var:     0.6800 | E_err:   0.009111
[2025-10-22 04:57:02] [Iter 1859/2250] R3[808/1200]  | LR: 0.011025 | E:  -60.900787 | E_var:     0.5673 | E_err:   0.008322
[2025-10-22 04:57:29] [Iter 1860/2250] R3[809/1200]  | LR: 0.010997 | E:  -60.901506 | E_var:     0.6978 | E_err:   0.009229
[2025-10-22 04:57:56] [Iter 1861/2250] R3[810/1200]  | LR: 0.010969 | E:  -60.896570 | E_var:     0.7109 | E_err:   0.009316
[2025-10-22 04:58:23] [Iter 1862/2250] R3[811/1200]  | LR: 0.010941 | E:  -60.894380 | E_var:     0.6277 | E_err:   0.008753
[2025-10-22 04:58:50] [Iter 1863/2250] R3[812/1200]  | LR: 0.010913 | E:  -60.891286 | E_var:     0.5692 | E_err:   0.008336
[2025-10-22 04:59:17] [Iter 1864/2250] R3[813/1200]  | LR: 0.010885 | E:  -60.894371 | E_var:     0.6556 | E_err:   0.008946
[2025-10-22 04:59:44] [Iter 1865/2250] R3[814/1200]  | LR: 0.010858 | E:  -60.887015 | E_var:     0.5975 | E_err:   0.008540
[2025-10-22 05:00:10] [Iter 1866/2250] R3[815/1200]  | LR: 0.010830 | E:  -60.907654 | E_var:     0.6021 | E_err:   0.008573
[2025-10-22 05:00:37] [Iter 1867/2250] R3[816/1200]  | LR: 0.010802 | E:  -60.902317 | E_var:     0.6058 | E_err:   0.008599
[2025-10-22 05:01:04] [Iter 1868/2250] R3[817/1200]  | LR: 0.010775 | E:  -60.905393 | E_var:     0.6481 | E_err:   0.008895
[2025-10-22 05:01:31] [Iter 1869/2250] R3[818/1200]  | LR: 0.010747 | E:  -60.906651 | E_var:     0.6235 | E_err:   0.008724
[2025-10-22 05:01:58] [Iter 1870/2250] R3[819/1200]  | LR: 0.010719 | E:  -60.899333 | E_var:     0.7293 | E_err:   0.009435
[2025-10-22 05:02:25] [Iter 1871/2250] R3[820/1200]  | LR: 0.010692 | E:  -60.888131 | E_var:     0.6092 | E_err:   0.008624
[2025-10-22 05:02:52] [Iter 1872/2250] R3[821/1200]  | LR: 0.010665 | E:  -60.901447 | E_var:     0.5920 | E_err:   0.008501
[2025-10-22 05:03:19] [Iter 1873/2250] R3[822/1200]  | LR: 0.010637 | E:  -60.919329 | E_var:     0.6360 | E_err:   0.008811
[2025-10-22 05:03:45] [Iter 1874/2250] R3[823/1200]  | LR: 0.010610 | E:  -60.894749 | E_var:     0.6566 | E_err:   0.008953
[2025-10-22 05:04:12] [Iter 1875/2250] R3[824/1200]  | LR: 0.010583 | E:  -60.889982 | E_var:     0.6185 | E_err:   0.008689
[2025-10-22 05:04:39] [Iter 1876/2250] R3[825/1200]  | LR: 0.010555 | E:  -60.894632 | E_var:     0.5863 | E_err:   0.008460
[2025-10-22 05:05:06] [Iter 1877/2250] R3[826/1200]  | LR: 0.010528 | E:  -60.921662 | E_var:     0.6228 | E_err:   0.008719
[2025-10-22 05:05:33] [Iter 1878/2250] R3[827/1200]  | LR: 0.010501 | E:  -60.894136 | E_var:     0.5805 | E_err:   0.008418
[2025-10-22 05:06:00] [Iter 1879/2250] R3[828/1200]  | LR: 0.010474 | E:  -60.913057 | E_var:     0.6089 | E_err:   0.008621
[2025-10-22 05:06:27] [Iter 1880/2250] R3[829/1200]  | LR: 0.010447 | E:  -60.897044 | E_var:     0.5254 | E_err:   0.008009
[2025-10-22 05:06:54] [Iter 1881/2250] R3[830/1200]  | LR: 0.010420 | E:  -60.905335 | E_var:     0.6508 | E_err:   0.008913
[2025-10-22 05:07:21] [Iter 1882/2250] R3[831/1200]  | LR: 0.010393 | E:  -60.886254 | E_var:     0.5671 | E_err:   0.008321
[2025-10-22 05:07:47] [Iter 1883/2250] R3[832/1200]  | LR: 0.010366 | E:  -60.891547 | E_var:     0.6810 | E_err:   0.009117
[2025-10-22 05:08:14] [Iter 1884/2250] R3[833/1200]  | LR: 0.010339 | E:  -60.896243 | E_var:     0.5607 | E_err:   0.008273
[2025-10-22 05:08:41] [Iter 1885/2250] R3[834/1200]  | LR: 0.010312 | E:  -60.896561 | E_var:     0.5830 | E_err:   0.008436
[2025-10-22 05:09:08] [Iter 1886/2250] R3[835/1200]  | LR: 0.010286 | E:  -60.897756 | E_var:     0.6825 | E_err:   0.009128
[2025-10-22 05:09:35] [Iter 1887/2250] R3[836/1200]  | LR: 0.010259 | E:  -60.895677 | E_var:     0.6227 | E_err:   0.008719
[2025-10-22 05:10:02] [Iter 1888/2250] R3[837/1200]  | LR: 0.010232 | E:  -60.918421 | E_var:     0.5853 | E_err:   0.008453
[2025-10-22 05:10:29] [Iter 1889/2250] R3[838/1200]  | LR: 0.010206 | E:  -60.886773 | E_var:     0.6725 | E_err:   0.009060
[2025-10-22 05:10:56] [Iter 1890/2250] R3[839/1200]  | LR: 0.010179 | E:  -60.894758 | E_var:     0.6250 | E_err:   0.008734
[2025-10-22 05:11:22] [Iter 1891/2250] R3[840/1200]  | LR: 0.010153 | E:  -60.898294 | E_var:     0.6229 | E_err:   0.008720
[2025-10-22 05:11:49] [Iter 1892/2250] R3[841/1200]  | LR: 0.010126 | E:  -60.895353 | E_var:     0.5858 | E_err:   0.008456
[2025-10-22 05:12:16] [Iter 1893/2250] R3[842/1200]  | LR: 0.010100 | E:  -60.901257 | E_var:     0.6493 | E_err:   0.008903
[2025-10-22 05:12:43] [Iter 1894/2250] R3[843/1200]  | LR: 0.010073 | E:  -60.910093 | E_var:     0.6195 | E_err:   0.008696
[2025-10-22 05:13:10] [Iter 1895/2250] R3[844/1200]  | LR: 0.010047 | E:  -60.906722 | E_var:     0.6221 | E_err:   0.008714
[2025-10-22 05:13:37] [Iter 1896/2250] R3[845/1200]  | LR: 0.010021 | E:  -60.894181 | E_var:     0.5779 | E_err:   0.008399
[2025-10-22 05:14:04] [Iter 1897/2250] R3[846/1200]  | LR: 0.009995 | E:  -60.888918 | E_var:     0.6610 | E_err:   0.008982
[2025-10-22 05:14:31] [Iter 1898/2250] R3[847/1200]  | LR: 0.009969 | E:  -60.904270 | E_var:     0.7411 | E_err:   0.009512
[2025-10-22 05:14:58] [Iter 1899/2250] R3[848/1200]  | LR: 0.009943 | E:  -60.910246 | E_var:     0.5924 | E_err:   0.008504
[2025-10-22 05:15:24] [Iter 1900/2250] R3[849/1200]  | LR: 0.009916 | E:  -60.896927 | E_var:     0.6017 | E_err:   0.008571
[2025-10-22 05:15:51] [Iter 1901/2250] R3[850/1200]  | LR: 0.009890 | E:  -60.913279 | E_var:     0.6011 | E_err:   0.008566
[2025-10-22 05:16:18] [Iter 1902/2250] R3[851/1200]  | LR: 0.009865 | E:  -60.895096 | E_var:     0.7098 | E_err:   0.009308
[2025-10-22 05:16:45] [Iter 1903/2250] R3[852/1200]  | LR: 0.009839 | E:  -60.897102 | E_var:     0.6951 | E_err:   0.009212
[2025-10-22 05:17:12] [Iter 1904/2250] R3[853/1200]  | LR: 0.009813 | E:  -60.909096 | E_var:     0.6715 | E_err:   0.009053
[2025-10-22 05:17:39] [Iter 1905/2250] R3[854/1200]  | LR: 0.009787 | E:  -60.907673 | E_var:     0.6125 | E_err:   0.008647
[2025-10-22 05:18:06] [Iter 1906/2250] R3[855/1200]  | LR: 0.009761 | E:  -60.913308 | E_var:     0.6136 | E_err:   0.008654
[2025-10-22 05:18:33] [Iter 1907/2250] R3[856/1200]  | LR: 0.009736 | E:  -60.909486 | E_var:     0.9062 | E_err:   0.010517
[2025-10-22 05:18:59] [Iter 1908/2250] R3[857/1200]  | LR: 0.009710 | E:  -60.890246 | E_var:     0.6886 | E_err:   0.009168
[2025-10-22 05:19:26] [Iter 1909/2250] R3[858/1200]  | LR: 0.009684 | E:  -60.891007 | E_var:     0.6825 | E_err:   0.009127
[2025-10-22 05:19:53] [Iter 1910/2250] R3[859/1200]  | LR: 0.009659 | E:  -60.899529 | E_var:     0.5901 | E_err:   0.008487
[2025-10-22 05:20:20] [Iter 1911/2250] R3[860/1200]  | LR: 0.009633 | E:  -60.889347 | E_var:     0.6957 | E_err:   0.009215
[2025-10-22 05:20:47] [Iter 1912/2250] R3[861/1200]  | LR: 0.009608 | E:  -60.900342 | E_var:     0.5863 | E_err:   0.008460
[2025-10-22 05:21:14] [Iter 1913/2250] R3[862/1200]  | LR: 0.009583 | E:  -60.904072 | E_var:     0.6233 | E_err:   0.008723
[2025-10-22 05:21:41] [Iter 1914/2250] R3[863/1200]  | LR: 0.009557 | E:  -60.888537 | E_var:     0.6511 | E_err:   0.008915
[2025-10-22 05:22:08] [Iter 1915/2250] R3[864/1200]  | LR: 0.009532 | E:  -60.886368 | E_var:     0.5892 | E_err:   0.008481
[2025-10-22 05:22:35] [Iter 1916/2250] R3[865/1200]  | LR: 0.009507 | E:  -60.905905 | E_var:     0.5899 | E_err:   0.008486
[2025-10-22 05:23:01] [Iter 1917/2250] R3[866/1200]  | LR: 0.009482 | E:  -60.896150 | E_var:     0.5915 | E_err:   0.008497
[2025-10-22 05:23:28] [Iter 1918/2250] R3[867/1200]  | LR: 0.009457 | E:  -60.893118 | E_var:     0.7431 | E_err:   0.009524
[2025-10-22 05:23:55] [Iter 1919/2250] R3[868/1200]  | LR: 0.009432 | E:  -60.898205 | E_var:     0.6695 | E_err:   0.009040
[2025-10-22 05:24:22] [Iter 1920/2250] R3[869/1200]  | LR: 0.009407 | E:  -60.906633 | E_var:     0.7119 | E_err:   0.009322
[2025-10-22 05:24:49] [Iter 1921/2250] R3[870/1200]  | LR: 0.009382 | E:  -60.890192 | E_var:     0.5697 | E_err:   0.008339
[2025-10-22 05:25:16] [Iter 1922/2250] R3[871/1200]  | LR: 0.009357 | E:  -60.890146 | E_var:     0.7423 | E_err:   0.009519
[2025-10-22 05:25:43] [Iter 1923/2250] R3[872/1200]  | LR: 0.009332 | E:  -60.905738 | E_var:     0.6151 | E_err:   0.008665
[2025-10-22 05:26:10] [Iter 1924/2250] R3[873/1200]  | LR: 0.009307 | E:  -60.912997 | E_var:     0.6027 | E_err:   0.008577
[2025-10-22 05:26:37] [Iter 1925/2250] R3[874/1200]  | LR: 0.009283 | E:  -60.898010 | E_var:     0.7180 | E_err:   0.009362
[2025-10-22 05:27:03] [Iter 1926/2250] R3[875/1200]  | LR: 0.009258 | E:  -60.904869 | E_var:     0.6764 | E_err:   0.009087
[2025-10-22 05:27:30] [Iter 1927/2250] R3[876/1200]  | LR: 0.009234 | E:  -60.902903 | E_var:     0.5941 | E_err:   0.008516
[2025-10-22 05:27:57] [Iter 1928/2250] R3[877/1200]  | LR: 0.009209 | E:  -60.911748 | E_var:     0.6259 | E_err:   0.008741
[2025-10-22 05:28:24] [Iter 1929/2250] R3[878/1200]  | LR: 0.009185 | E:  -60.900159 | E_var:     0.6499 | E_err:   0.008907
[2025-10-22 05:28:51] [Iter 1930/2250] R3[879/1200]  | LR: 0.009160 | E:  -60.891431 | E_var:     0.6267 | E_err:   0.008746
[2025-10-22 05:29:18] [Iter 1931/2250] R3[880/1200]  | LR: 0.009136 | E:  -60.908761 | E_var:     0.6301 | E_err:   0.008770
[2025-10-22 05:29:45] [Iter 1932/2250] R3[881/1200]  | LR: 0.009112 | E:  -60.904246 | E_var:     0.5986 | E_err:   0.008548
[2025-10-22 05:30:12] [Iter 1933/2250] R3[882/1200]  | LR: 0.009087 | E:  -60.885728 | E_var:     0.6480 | E_err:   0.008894
[2025-10-22 05:30:39] [Iter 1934/2250] R3[883/1200]  | LR: 0.009063 | E:  -60.902401 | E_var:     0.6549 | E_err:   0.008941
[2025-10-22 05:31:05] [Iter 1935/2250] R3[884/1200]  | LR: 0.009039 | E:  -60.908880 | E_var:     0.7999 | E_err:   0.009881
[2025-10-22 05:31:32] [Iter 1936/2250] R3[885/1200]  | LR: 0.009015 | E:  -60.899151 | E_var:     0.5927 | E_err:   0.008506
[2025-10-22 05:31:59] [Iter 1937/2250] R3[886/1200]  | LR: 0.008991 | E:  -60.900559 | E_var:     0.6339 | E_err:   0.008797
[2025-10-22 05:32:26] [Iter 1938/2250] R3[887/1200]  | LR: 0.008967 | E:  -60.917880 | E_var:     0.6385 | E_err:   0.008829
[2025-10-22 05:32:53] [Iter 1939/2250] R3[888/1200]  | LR: 0.008943 | E:  -60.902818 | E_var:     0.6457 | E_err:   0.008878
[2025-10-22 05:33:20] [Iter 1940/2250] R3[889/1200]  | LR: 0.008919 | E:  -60.912451 | E_var:     0.7066 | E_err:   0.009287
[2025-10-22 05:33:47] [Iter 1941/2250] R3[890/1200]  | LR: 0.008896 | E:  -60.907819 | E_var:     0.5478 | E_err:   0.008178
[2025-10-22 05:34:14] [Iter 1942/2250] R3[891/1200]  | LR: 0.008872 | E:  -60.891995 | E_var:     0.6477 | E_err:   0.008892
[2025-10-22 05:34:40] [Iter 1943/2250] R3[892/1200]  | LR: 0.008848 | E:  -60.891283 | E_var:     0.6085 | E_err:   0.008618
[2025-10-22 05:35:07] [Iter 1944/2250] R3[893/1200]  | LR: 0.008825 | E:  -60.896316 | E_var:     0.6611 | E_err:   0.008983
[2025-10-22 05:35:34] [Iter 1945/2250] R3[894/1200]  | LR: 0.008801 | E:  -60.903804 | E_var:     0.7676 | E_err:   0.009680
[2025-10-22 05:36:01] [Iter 1946/2250] R3[895/1200]  | LR: 0.008778 | E:  -60.915979 | E_var:     0.5995 | E_err:   0.008555
[2025-10-22 05:36:28] [Iter 1947/2250] R3[896/1200]  | LR: 0.008754 | E:  -60.911187 | E_var:     0.5427 | E_err:   0.008139
[2025-10-22 05:36:55] [Iter 1948/2250] R3[897/1200]  | LR: 0.008731 | E:  -60.898097 | E_var:     0.7981 | E_err:   0.009870
[2025-10-22 05:37:22] [Iter 1949/2250] R3[898/1200]  | LR: 0.008708 | E:  -60.897717 | E_var:     0.6156 | E_err:   0.008669
[2025-10-22 05:37:49] [Iter 1950/2250] R3[899/1200]  | LR: 0.008684 | E:  -60.908090 | E_var:     0.7001 | E_err:   0.009244
[2025-10-22 05:38:15] [Iter 1951/2250] R3[900/1200]  | LR: 0.008661 | E:  -60.906149 | E_var:     0.6029 | E_err:   0.008579
[2025-10-22 05:38:42] [Iter 1952/2250] R3[901/1200]  | LR: 0.008638 | E:  -60.885163 | E_var:     0.7916 | E_err:   0.009830
[2025-10-22 05:39:09] [Iter 1953/2250] R3[902/1200]  | LR: 0.008615 | E:  -60.905117 | E_var:     0.8750 | E_err:   0.010335
[2025-10-22 05:39:36] [Iter 1954/2250] R3[903/1200]  | LR: 0.008592 | E:  -60.881996 | E_var:     0.6333 | E_err:   0.008793
[2025-10-22 05:40:03] [Iter 1955/2250] R3[904/1200]  | LR: 0.008569 | E:  -60.913063 | E_var:     0.6335 | E_err:   0.008794
[2025-10-22 05:40:30] [Iter 1956/2250] R3[905/1200]  | LR: 0.008546 | E:  -60.901321 | E_var:     0.6798 | E_err:   0.009109
[2025-10-22 05:40:57] [Iter 1957/2250] R3[906/1200]  | LR: 0.008523 | E:  -60.903253 | E_var:     0.6049 | E_err:   0.008593
[2025-10-22 05:41:24] [Iter 1958/2250] R3[907/1200]  | LR: 0.008501 | E:  -60.892236 | E_var:     0.6150 | E_err:   0.008665
[2025-10-22 05:41:51] [Iter 1959/2250] R3[908/1200]  | LR: 0.008478 | E:  -60.912673 | E_var:     0.6284 | E_err:   0.008758
[2025-10-22 05:42:17] [Iter 1960/2250] R3[909/1200]  | LR: 0.008455 | E:  -60.903133 | E_var:     0.6671 | E_err:   0.009024
[2025-10-22 05:42:44] [Iter 1961/2250] R3[910/1200]  | LR: 0.008433 | E:  -60.906822 | E_var:     0.6058 | E_err:   0.008600
[2025-10-22 05:43:11] [Iter 1962/2250] R3[911/1200]  | LR: 0.008410 | E:  -60.916294 | E_var:     0.6359 | E_err:   0.008811
[2025-10-22 05:43:38] [Iter 1963/2250] R3[912/1200]  | LR: 0.008388 | E:  -60.892954 | E_var:     0.5634 | E_err:   0.008293
[2025-10-22 05:44:05] [Iter 1964/2250] R3[913/1200]  | LR: 0.008366 | E:  -60.905973 | E_var:     0.5534 | E_err:   0.008219
[2025-10-22 05:44:32] [Iter 1965/2250] R3[914/1200]  | LR: 0.008343 | E:  -60.916891 | E_var:     0.6726 | E_err:   0.009061
[2025-10-22 05:44:59] [Iter 1966/2250] R3[915/1200]  | LR: 0.008321 | E:  -60.908380 | E_var:     0.5364 | E_err:   0.008092
[2025-10-22 05:45:26] [Iter 1967/2250] R3[916/1200]  | LR: 0.008299 | E:  -60.897707 | E_var:     0.6675 | E_err:   0.009027
[2025-10-22 05:45:52] [Iter 1968/2250] R3[917/1200]  | LR: 0.008277 | E:  -60.896869 | E_var:     0.7395 | E_err:   0.009501
[2025-10-22 05:46:19] [Iter 1969/2250] R3[918/1200]  | LR: 0.008255 | E:  -60.892216 | E_var:     0.6908 | E_err:   0.009183
[2025-10-22 05:46:46] [Iter 1970/2250] R3[919/1200]  | LR: 0.008233 | E:  -60.894561 | E_var:     0.8187 | E_err:   0.009997
[2025-10-22 05:47:13] [Iter 1971/2250] R3[920/1200]  | LR: 0.008211 | E:  -60.907004 | E_var:     0.7182 | E_err:   0.009363
[2025-10-22 05:47:40] [Iter 1972/2250] R3[921/1200]  | LR: 0.008189 | E:  -60.899045 | E_var:     0.6525 | E_err:   0.008925
[2025-10-22 05:48:07] [Iter 1973/2250] R3[922/1200]  | LR: 0.008167 | E:  -60.902778 | E_var:     0.6951 | E_err:   0.009211
[2025-10-22 05:48:34] [Iter 1974/2250] R3[923/1200]  | LR: 0.008145 | E:  -60.899088 | E_var:     0.6161 | E_err:   0.008672
[2025-10-22 05:49:01] [Iter 1975/2250] R3[924/1200]  | LR: 0.008124 | E:  -60.899803 | E_var:     0.5854 | E_err:   0.008454
[2025-10-22 05:49:28] [Iter 1976/2250] R3[925/1200]  | LR: 0.008102 | E:  -60.892113 | E_var:     0.6336 | E_err:   0.008795
[2025-10-22 05:49:54] [Iter 1977/2250] R3[926/1200]  | LR: 0.008080 | E:  -60.909837 | E_var:     0.6755 | E_err:   0.009081
[2025-10-22 05:50:21] [Iter 1978/2250] R3[927/1200]  | LR: 0.008059 | E:  -60.895446 | E_var:     0.6525 | E_err:   0.008925
[2025-10-22 05:50:48] [Iter 1979/2250] R3[928/1200]  | LR: 0.008038 | E:  -60.885907 | E_var:     0.6613 | E_err:   0.008985
[2025-10-22 05:51:15] [Iter 1980/2250] R3[929/1200]  | LR: 0.008016 | E:  -60.907081 | E_var:     0.5826 | E_err:   0.008433
[2025-10-22 05:51:42] [Iter 1981/2250] R3[930/1200]  | LR: 0.007995 | E:  -60.888552 | E_var:     0.5761 | E_err:   0.008386
[2025-10-22 05:52:09] [Iter 1982/2250] R3[931/1200]  | LR: 0.007974 | E:  -60.891962 | E_var:     0.6234 | E_err:   0.008724
[2025-10-22 05:52:36] [Iter 1983/2250] R3[932/1200]  | LR: 0.007953 | E:  -60.903667 | E_var:     0.6626 | E_err:   0.008994
[2025-10-22 05:53:03] [Iter 1984/2250] R3[933/1200]  | LR: 0.007931 | E:  -60.894564 | E_var:     0.5514 | E_err:   0.008204
[2025-10-22 05:53:30] [Iter 1985/2250] R3[934/1200]  | LR: 0.007910 | E:  -60.901069 | E_var:     0.9828 | E_err:   0.010953
[2025-10-22 05:53:56] [Iter 1986/2250] R3[935/1200]  | LR: 0.007889 | E:  -60.905230 | E_var:     0.6701 | E_err:   0.009044
[2025-10-22 05:54:23] [Iter 1987/2250] R3[936/1200]  | LR: 0.007869 | E:  -60.912890 | E_var:     0.6639 | E_err:   0.009003
[2025-10-22 05:54:50] [Iter 1988/2250] R3[937/1200]  | LR: 0.007848 | E:  -60.907757 | E_var:     0.6106 | E_err:   0.008633
[2025-10-22 05:55:17] [Iter 1989/2250] R3[938/1200]  | LR: 0.007827 | E:  -60.910203 | E_var:     0.6880 | E_err:   0.009164
[2025-10-22 05:55:44] [Iter 1990/2250] R3[939/1200]  | LR: 0.007806 | E:  -60.899499 | E_var:     0.7222 | E_err:   0.009389
[2025-10-22 05:56:11] [Iter 1991/2250] R3[940/1200]  | LR: 0.007786 | E:  -60.901968 | E_var:     0.6723 | E_err:   0.009059
[2025-10-22 05:56:38] [Iter 1992/2250] R3[941/1200]  | LR: 0.007765 | E:  -60.900806 | E_var:     0.5922 | E_err:   0.008502
[2025-10-22 05:57:05] [Iter 1993/2250] R3[942/1200]  | LR: 0.007745 | E:  -60.892393 | E_var:     0.6773 | E_err:   0.009092
[2025-10-22 05:57:31] [Iter 1994/2250] R3[943/1200]  | LR: 0.007724 | E:  -60.908537 | E_var:     0.5932 | E_err:   0.008510
[2025-10-22 05:57:58] [Iter 1995/2250] R3[944/1200]  | LR: 0.007704 | E:  -60.902402 | E_var:     0.6902 | E_err:   0.009179
[2025-10-22 05:58:25] [Iter 1996/2250] R3[945/1200]  | LR: 0.007684 | E:  -60.904379 | E_var:     0.6952 | E_err:   0.009212
[2025-10-22 05:58:52] [Iter 1997/2250] R3[946/1200]  | LR: 0.007663 | E:  -60.906901 | E_var:     0.7066 | E_err:   0.009288
[2025-10-22 05:59:19] [Iter 1998/2250] R3[947/1200]  | LR: 0.007643 | E:  -60.898717 | E_var:     0.5489 | E_err:   0.008186
[2025-10-22 05:59:46] [Iter 1999/2250] R3[948/1200]  | LR: 0.007623 | E:  -60.913489 | E_var:     0.6775 | E_err:   0.009094
[2025-10-22 06:00:13] [Iter 2000/2250] R3[949/1200]  | LR: 0.007603 | E:  -60.902210 | E_var:     0.5879 | E_err:   0.008471
[2025-10-22 06:00:40] [Iter 2001/2250] R3[950/1200]  | LR: 0.007583 | E:  -60.899249 | E_var:     0.8057 | E_err:   0.009917
[2025-10-22 06:01:07] [Iter 2002/2250] R3[951/1200]  | LR: 0.007563 | E:  -60.893728 | E_var:     0.7753 | E_err:   0.009728
[2025-10-22 06:01:33] [Iter 2003/2250] R3[952/1200]  | LR: 0.007543 | E:  -60.907908 | E_var:     0.6799 | E_err:   0.009110
[2025-10-22 06:02:00] [Iter 2004/2250] R3[953/1200]  | LR: 0.007524 | E:  -60.886681 | E_var:     0.5971 | E_err:   0.008537
[2025-10-22 06:02:27] [Iter 2005/2250] R3[954/1200]  | LR: 0.007504 | E:  -60.892780 | E_var:     0.6636 | E_err:   0.009000
[2025-10-22 06:02:54] [Iter 2006/2250] R3[955/1200]  | LR: 0.007484 | E:  -60.912706 | E_var:     0.5434 | E_err:   0.008145
[2025-10-22 06:03:21] [Iter 2007/2250] R3[956/1200]  | LR: 0.007465 | E:  -60.894099 | E_var:     0.6010 | E_err:   0.008565
[2025-10-22 06:03:48] [Iter 2008/2250] R3[957/1200]  | LR: 0.007445 | E:  -60.895312 | E_var:     0.6283 | E_err:   0.008757
[2025-10-22 06:04:15] [Iter 2009/2250] R3[958/1200]  | LR: 0.007426 | E:  -60.897647 | E_var:     0.5815 | E_err:   0.008425
[2025-10-22 06:04:42] [Iter 2010/2250] R3[959/1200]  | LR: 0.007407 | E:  -60.914846 | E_var:     0.5304 | E_err:   0.008047
[2025-10-22 06:05:08] [Iter 2011/2250] R3[960/1200]  | LR: 0.007387 | E:  -60.912242 | E_var:     0.5408 | E_err:   0.008125
[2025-10-22 06:05:35] [Iter 2012/2250] R3[961/1200]  | LR: 0.007368 | E:  -60.907115 | E_var:     0.7702 | E_err:   0.009696
[2025-10-22 06:06:02] [Iter 2013/2250] R3[962/1200]  | LR: 0.007349 | E:  -60.897960 | E_var:     0.5550 | E_err:   0.008231
[2025-10-22 06:06:29] [Iter 2014/2250] R3[963/1200]  | LR: 0.007330 | E:  -60.889064 | E_var:     0.5288 | E_err:   0.008034
[2025-10-22 06:06:56] [Iter 2015/2250] R3[964/1200]  | LR: 0.007311 | E:  -60.897292 | E_var:     0.5956 | E_err:   0.008526
[2025-10-22 06:07:23] [Iter 2016/2250] R3[965/1200]  | LR: 0.007292 | E:  -60.896489 | E_var:     0.9726 | E_err:   0.010896
[2025-10-22 06:07:50] [Iter 2017/2250] R3[966/1200]  | LR: 0.007273 | E:  -60.903798 | E_var:     0.6744 | E_err:   0.009073
[2025-10-22 06:08:17] [Iter 2018/2250] R3[967/1200]  | LR: 0.007254 | E:  -60.903599 | E_var:     0.7520 | E_err:   0.009581
[2025-10-22 06:08:44] [Iter 2019/2250] R3[968/1200]  | LR: 0.007236 | E:  -60.902296 | E_var:     0.5868 | E_err:   0.008464
[2025-10-22 06:09:10] [Iter 2020/2250] R3[969/1200]  | LR: 0.007217 | E:  -60.917547 | E_var:     0.7737 | E_err:   0.009718
[2025-10-22 06:09:37] [Iter 2021/2250] R3[970/1200]  | LR: 0.007198 | E:  -60.898106 | E_var:     0.5901 | E_err:   0.008487
[2025-10-22 06:10:04] [Iter 2022/2250] R3[971/1200]  | LR: 0.007180 | E:  -60.893129 | E_var:     0.6140 | E_err:   0.008658
[2025-10-22 06:10:31] [Iter 2023/2250] R3[972/1200]  | LR: 0.007161 | E:  -60.893222 | E_var:     0.6517 | E_err:   0.008920
[2025-10-22 06:10:58] [Iter 2024/2250] R3[973/1200]  | LR: 0.007143 | E:  -60.903380 | E_var:     0.5897 | E_err:   0.008484
[2025-10-22 06:11:25] [Iter 2025/2250] R3[974/1200]  | LR: 0.007125 | E:  -60.923636 | E_var:     0.7598 | E_err:   0.009631
[2025-10-22 06:11:25] ✓ Checkpoint saved: checkpoint_iter_002025.pkl
[2025-10-22 06:11:52] [Iter 2026/2250] R3[975/1200]  | LR: 0.007107 | E:  -60.908249 | E_var:     0.7110 | E_err:   0.009316
[2025-10-22 06:12:19] [Iter 2027/2250] R3[976/1200]  | LR: 0.007088 | E:  -60.918944 | E_var:     0.6110 | E_err:   0.008637
[2025-10-22 06:12:46] [Iter 2028/2250] R3[977/1200]  | LR: 0.007070 | E:  -60.916272 | E_var:     0.5871 | E_err:   0.008465
[2025-10-22 06:13:12] [Iter 2029/2250] R3[978/1200]  | LR: 0.007052 | E:  -60.901073 | E_var:     0.6492 | E_err:   0.008902
[2025-10-22 06:13:39] [Iter 2030/2250] R3[979/1200]  | LR: 0.007034 | E:  -60.916121 | E_var:     0.7080 | E_err:   0.009296
[2025-10-22 06:14:06] [Iter 2031/2250] R3[980/1200]  | LR: 0.007017 | E:  -60.909894 | E_var:     0.7578 | E_err:   0.009618
[2025-10-22 06:14:33] [Iter 2032/2250] R3[981/1200]  | LR: 0.006999 | E:  -60.899841 | E_var:     0.7037 | E_err:   0.009268
[2025-10-22 06:15:00] [Iter 2033/2250] R3[982/1200]  | LR: 0.006981 | E:  -60.901696 | E_var:     0.6047 | E_err:   0.008591
[2025-10-22 06:15:27] [Iter 2034/2250] R3[983/1200]  | LR: 0.006963 | E:  -60.900918 | E_var:     0.5791 | E_err:   0.008408
[2025-10-22 06:15:54] [Iter 2035/2250] R3[984/1200]  | LR: 0.006946 | E:  -60.895015 | E_var:     0.6755 | E_err:   0.009081
[2025-10-22 06:16:21] [Iter 2036/2250] R3[985/1200]  | LR: 0.006928 | E:  -60.912801 | E_var:     0.6108 | E_err:   0.008635
[2025-10-22 06:16:48] [Iter 2037/2250] R3[986/1200]  | LR: 0.006911 | E:  -60.923068 | E_var:     0.7183 | E_err:   0.009364
[2025-10-22 06:17:14] [Iter 2038/2250] R3[987/1200]  | LR: 0.006894 | E:  -60.905511 | E_var:     0.8615 | E_err:   0.010255
[2025-10-22 06:17:41] [Iter 2039/2250] R3[988/1200]  | LR: 0.006876 | E:  -60.897314 | E_var:     0.5920 | E_err:   0.008501
[2025-10-22 06:18:08] [Iter 2040/2250] R3[989/1200]  | LR: 0.006859 | E:  -60.899110 | E_var:     0.6582 | E_err:   0.008964
[2025-10-22 06:18:35] [Iter 2041/2250] R3[990/1200]  | LR: 0.006842 | E:  -60.894392 | E_var:     0.6450 | E_err:   0.008873
[2025-10-22 06:19:02] [Iter 2042/2250] R3[991/1200]  | LR: 0.006825 | E:  -60.908426 | E_var:     0.6476 | E_err:   0.008891
[2025-10-22 06:19:29] [Iter 2043/2250] R3[992/1200]  | LR: 0.006808 | E:  -60.906487 | E_var:     0.5699 | E_err:   0.008341
[2025-10-22 06:19:56] [Iter 2044/2250] R3[993/1200]  | LR: 0.006791 | E:  -60.909627 | E_var:     0.6502 | E_err:   0.008909
[2025-10-22 06:20:23] [Iter 2045/2250] R3[994/1200]  | LR: 0.006774 | E:  -60.892098 | E_var:     0.5791 | E_err:   0.008408
[2025-10-22 06:20:50] [Iter 2046/2250] R3[995/1200]  | LR: 0.006757 | E:  -60.907614 | E_var:     0.6056 | E_err:   0.008598
[2025-10-22 06:21:16] [Iter 2047/2250] R3[996/1200]  | LR: 0.006741 | E:  -60.908025 | E_var:     0.6368 | E_err:   0.008817
[2025-10-22 06:21:43] [Iter 2048/2250] R3[997/1200]  | LR: 0.006724 | E:  -60.897465 | E_var:     0.5592 | E_err:   0.008262
[2025-10-22 06:22:10] [Iter 2049/2250] R3[998/1200]  | LR: 0.006708 | E:  -60.902381 | E_var:     0.6359 | E_err:   0.008810
[2025-10-22 06:22:37] [Iter 2050/2250] R3[999/1200]  | LR: 0.006691 | E:  -60.916459 | E_var:     0.5876 | E_err:   0.008469
[2025-10-22 06:23:04] [Iter 2051/2250] R3[1000/1200] | LR: 0.006675 | E:  -60.911028 | E_var:     0.6025 | E_err:   0.008576
[2025-10-22 06:23:31] [Iter 2052/2250] R3[1001/1200] | LR: 0.006658 | E:  -60.908003 | E_var:     0.8289 | E_err:   0.010059
[2025-10-22 06:23:58] [Iter 2053/2250] R3[1002/1200] | LR: 0.006642 | E:  -60.904900 | E_var:     0.5722 | E_err:   0.008357
[2025-10-22 06:24:25] [Iter 2054/2250] R3[1003/1200] | LR: 0.006626 | E:  -60.908859 | E_var:     0.5788 | E_err:   0.008406
[2025-10-22 06:24:52] [Iter 2055/2250] R3[1004/1200] | LR: 0.006610 | E:  -60.902717 | E_var:     0.6422 | E_err:   0.008854
[2025-10-22 06:25:18] [Iter 2056/2250] R3[1005/1200] | LR: 0.006594 | E:  -60.909063 | E_var:     0.6410 | E_err:   0.008846
[2025-10-22 06:25:45] [Iter 2057/2250] R3[1006/1200] | LR: 0.006578 | E:  -60.895595 | E_var:     0.6833 | E_err:   0.009133
[2025-10-22 06:26:12] [Iter 2058/2250] R3[1007/1200] | LR: 0.006562 | E:  -60.910623 | E_var:     0.6676 | E_err:   0.009028
[2025-10-22 06:26:39] [Iter 2059/2250] R3[1008/1200] | LR: 0.006546 | E:  -60.914018 | E_var:     0.6146 | E_err:   0.008662
[2025-10-22 06:27:06] [Iter 2060/2250] R3[1009/1200] | LR: 0.006530 | E:  -60.914231 | E_var:     0.6545 | E_err:   0.008939
[2025-10-22 06:27:33] [Iter 2061/2250] R3[1010/1200] | LR: 0.006515 | E:  -60.901910 | E_var:     0.6422 | E_err:   0.008854
[2025-10-22 06:28:00] [Iter 2062/2250] R3[1011/1200] | LR: 0.006499 | E:  -60.902718 | E_var:     0.6319 | E_err:   0.008783
[2025-10-22 06:28:27] [Iter 2063/2250] R3[1012/1200] | LR: 0.006484 | E:  -60.913297 | E_var:     0.5767 | E_err:   0.008391
[2025-10-22 06:28:53] [Iter 2064/2250] R3[1013/1200] | LR: 0.006468 | E:  -60.919253 | E_var:     0.5821 | E_err:   0.008429
[2025-10-22 06:29:20] [Iter 2065/2250] R3[1014/1200] | LR: 0.006453 | E:  -60.899714 | E_var:     0.6339 | E_err:   0.008797
[2025-10-22 06:29:47] [Iter 2066/2250] R3[1015/1200] | LR: 0.006438 | E:  -60.893741 | E_var:     0.5989 | E_err:   0.008550
[2025-10-22 06:30:14] [Iter 2067/2250] R3[1016/1200] | LR: 0.006422 | E:  -60.897720 | E_var:     0.6706 | E_err:   0.009048
[2025-10-22 06:30:41] [Iter 2068/2250] R3[1017/1200] | LR: 0.006407 | E:  -60.917142 | E_var:     0.5995 | E_err:   0.008554
[2025-10-22 06:31:08] [Iter 2069/2250] R3[1018/1200] | LR: 0.006392 | E:  -60.902349 | E_var:     0.6346 | E_err:   0.008802
[2025-10-22 06:31:35] [Iter 2070/2250] R3[1019/1200] | LR: 0.006377 | E:  -60.908992 | E_var:     0.6453 | E_err:   0.008875
[2025-10-22 06:32:02] [Iter 2071/2250] R3[1020/1200] | LR: 0.006362 | E:  -60.909926 | E_var:     0.6365 | E_err:   0.008815
[2025-10-22 06:32:29] [Iter 2072/2250] R3[1021/1200] | LR: 0.006348 | E:  -60.900903 | E_var:     0.7220 | E_err:   0.009388
[2025-10-22 06:32:55] [Iter 2073/2250] R3[1022/1200] | LR: 0.006333 | E:  -60.904131 | E_var:     0.6376 | E_err:   0.008822
[2025-10-22 06:33:22] [Iter 2074/2250] R3[1023/1200] | LR: 0.006318 | E:  -60.913231 | E_var:     0.6253 | E_err:   0.008737
[2025-10-22 06:33:49] [Iter 2075/2250] R3[1024/1200] | LR: 0.006304 | E:  -60.901041 | E_var:     0.5894 | E_err:   0.008482
[2025-10-22 06:34:16] [Iter 2076/2250] R3[1025/1200] | LR: 0.006289 | E:  -60.895128 | E_var:     0.6434 | E_err:   0.008862
[2025-10-22 06:34:43] [Iter 2077/2250] R3[1026/1200] | LR: 0.006275 | E:  -60.906253 | E_var:     0.6827 | E_err:   0.009129
[2025-10-22 06:35:10] [Iter 2078/2250] R3[1027/1200] | LR: 0.006260 | E:  -60.913474 | E_var:     0.6094 | E_err:   0.008625
[2025-10-22 06:35:37] [Iter 2079/2250] R3[1028/1200] | LR: 0.006246 | E:  -60.888007 | E_var:     0.6300 | E_err:   0.008769
[2025-10-22 06:36:04] [Iter 2080/2250] R3[1029/1200] | LR: 0.006232 | E:  -60.913257 | E_var:     0.6220 | E_err:   0.008714
[2025-10-22 06:36:31] [Iter 2081/2250] R3[1030/1200] | LR: 0.006218 | E:  -60.899197 | E_var:     0.7223 | E_err:   0.009390
[2025-10-22 06:36:57] [Iter 2082/2250] R3[1031/1200] | LR: 0.006204 | E:  -60.901652 | E_var:     0.6278 | E_err:   0.008754
[2025-10-22 06:37:24] [Iter 2083/2250] R3[1032/1200] | LR: 0.006190 | E:  -60.897105 | E_var:     0.7064 | E_err:   0.009286
[2025-10-22 06:37:51] [Iter 2084/2250] R3[1033/1200] | LR: 0.006176 | E:  -60.896450 | E_var:     0.5481 | E_err:   0.008179
[2025-10-22 06:38:18] [Iter 2085/2250] R3[1034/1200] | LR: 0.006162 | E:  -60.901178 | E_var:     0.6035 | E_err:   0.008583
[2025-10-22 06:38:45] [Iter 2086/2250] R3[1035/1200] | LR: 0.006148 | E:  -60.905957 | E_var:     0.7191 | E_err:   0.009369
[2025-10-22 06:39:12] [Iter 2087/2250] R3[1036/1200] | LR: 0.006135 | E:  -60.910520 | E_var:     0.7130 | E_err:   0.009329
[2025-10-22 06:39:39] [Iter 2088/2250] R3[1037/1200] | LR: 0.006121 | E:  -60.903680 | E_var:     0.6590 | E_err:   0.008969
[2025-10-22 06:40:06] [Iter 2089/2250] R3[1038/1200] | LR: 0.006107 | E:  -60.905698 | E_var:     0.6818 | E_err:   0.009123
[2025-10-22 06:40:33] [Iter 2090/2250] R3[1039/1200] | LR: 0.006094 | E:  -60.923045 | E_var:     0.7508 | E_err:   0.009573
[2025-10-22 06:40:59] [Iter 2091/2250] R3[1040/1200] | LR: 0.006081 | E:  -60.904089 | E_var:     0.6883 | E_err:   0.009166
[2025-10-22 06:41:26] [Iter 2092/2250] R3[1041/1200] | LR: 0.006067 | E:  -60.909637 | E_var:     0.6757 | E_err:   0.009082
[2025-10-22 06:41:53] [Iter 2093/2250] R3[1042/1200] | LR: 0.006054 | E:  -60.909180 | E_var:     0.6833 | E_err:   0.009133
[2025-10-22 06:42:20] [Iter 2094/2250] R3[1043/1200] | LR: 0.006041 | E:  -60.892406 | E_var:     0.6752 | E_err:   0.009079
[2025-10-22 06:42:47] [Iter 2095/2250] R3[1044/1200] | LR: 0.006028 | E:  -60.922161 | E_var:     1.2153 | E_err:   0.012180
[2025-10-22 06:43:14] [Iter 2096/2250] R3[1045/1200] | LR: 0.006015 | E:  -60.914082 | E_var:     0.6186 | E_err:   0.008690
[2025-10-22 06:43:41] [Iter 2097/2250] R3[1046/1200] | LR: 0.006002 | E:  -60.904096 | E_var:     0.7883 | E_err:   0.009809
[2025-10-22 06:44:08] [Iter 2098/2250] R3[1047/1200] | LR: 0.005989 | E:  -60.898249 | E_var:     0.6244 | E_err:   0.008730
[2025-10-22 06:44:34] [Iter 2099/2250] R3[1048/1200] | LR: 0.005977 | E:  -60.901469 | E_var:     0.6689 | E_err:   0.009036
[2025-10-22 06:45:01] [Iter 2100/2250] R3[1049/1200] | LR: 0.005964 | E:  -60.915529 | E_var:     0.5554 | E_err:   0.008234
[2025-10-22 06:45:28] [Iter 2101/2250] R3[1050/1200] | LR: 0.005952 | E:  -60.905311 | E_var:     0.7205 | E_err:   0.009379
[2025-10-22 06:45:55] [Iter 2102/2250] R3[1051/1200] | LR: 0.005939 | E:  -60.908363 | E_var:     0.7233 | E_err:   0.009397
[2025-10-22 06:46:22] [Iter 2103/2250] R3[1052/1200] | LR: 0.005927 | E:  -60.906077 | E_var:     0.6508 | E_err:   0.008913
[2025-10-22 06:46:49] [Iter 2104/2250] R3[1053/1200] | LR: 0.005914 | E:  -60.908408 | E_var:     0.6641 | E_err:   0.009004
[2025-10-22 06:47:16] [Iter 2105/2250] R3[1054/1200] | LR: 0.005902 | E:  -60.900782 | E_var:     0.7048 | E_err:   0.009275
[2025-10-22 06:47:43] [Iter 2106/2250] R3[1055/1200] | LR: 0.005890 | E:  -60.890649 | E_var:     0.6628 | E_err:   0.008995
[2025-10-22 06:48:10] [Iter 2107/2250] R3[1056/1200] | LR: 0.005878 | E:  -60.911943 | E_var:     0.5769 | E_err:   0.008392
[2025-10-22 06:48:36] [Iter 2108/2250] R3[1057/1200] | LR: 0.005866 | E:  -60.903543 | E_var:     0.5928 | E_err:   0.008507
[2025-10-22 06:49:03] [Iter 2109/2250] R3[1058/1200] | LR: 0.005854 | E:  -60.908442 | E_var:     0.6277 | E_err:   0.008753
[2025-10-22 06:49:30] [Iter 2110/2250] R3[1059/1200] | LR: 0.005842 | E:  -60.889702 | E_var:     0.7167 | E_err:   0.009353
[2025-10-22 06:49:57] [Iter 2111/2250] R3[1060/1200] | LR: 0.005830 | E:  -60.912650 | E_var:     0.6083 | E_err:   0.008617
[2025-10-22 06:50:24] [Iter 2112/2250] R3[1061/1200] | LR: 0.005819 | E:  -60.908655 | E_var:     0.8267 | E_err:   0.010046
[2025-10-22 06:50:51] [Iter 2113/2250] R3[1062/1200] | LR: 0.005807 | E:  -60.901754 | E_var:     0.6354 | E_err:   0.008807
[2025-10-22 06:51:18] [Iter 2114/2250] R3[1063/1200] | LR: 0.005795 | E:  -60.897806 | E_var:     0.7124 | E_err:   0.009326
[2025-10-22 06:51:45] [Iter 2115/2250] R3[1064/1200] | LR: 0.005784 | E:  -60.897015 | E_var:     0.5949 | E_err:   0.008522
[2025-10-22 06:52:12] [Iter 2116/2250] R3[1065/1200] | LR: 0.005773 | E:  -60.904913 | E_var:     0.5998 | E_err:   0.008557
[2025-10-22 06:52:38] [Iter 2117/2250] R3[1066/1200] | LR: 0.005761 | E:  -60.907304 | E_var:     0.7246 | E_err:   0.009405
[2025-10-22 06:53:05] [Iter 2118/2250] R3[1067/1200] | LR: 0.005750 | E:  -60.893742 | E_var:     0.5600 | E_err:   0.008268
[2025-10-22 06:53:32] [Iter 2119/2250] R3[1068/1200] | LR: 0.005739 | E:  -60.903203 | E_var:     0.6322 | E_err:   0.008785
[2025-10-22 06:53:59] [Iter 2120/2250] R3[1069/1200] | LR: 0.005728 | E:  -60.923207 | E_var:     0.6241 | E_err:   0.008729
[2025-10-22 06:54:26] [Iter 2121/2250] R3[1070/1200] | LR: 0.005717 | E:  -60.901726 | E_var:     0.5712 | E_err:   0.008350
[2025-10-22 06:54:53] [Iter 2122/2250] R3[1071/1200] | LR: 0.005706 | E:  -60.906719 | E_var:     0.6636 | E_err:   0.009000
[2025-10-22 06:55:20] [Iter 2123/2250] R3[1072/1200] | LR: 0.005695 | E:  -60.902496 | E_var:     0.6932 | E_err:   0.009199
[2025-10-22 06:55:47] [Iter 2124/2250] R3[1073/1200] | LR: 0.005685 | E:  -60.896282 | E_var:     0.6442 | E_err:   0.008868
[2025-10-22 06:56:14] [Iter 2125/2250] R3[1074/1200] | LR: 0.005674 | E:  -60.899111 | E_var:     0.6069 | E_err:   0.008607
[2025-10-22 06:56:40] [Iter 2126/2250] R3[1075/1200] | LR: 0.005663 | E:  -60.911809 | E_var:     0.6394 | E_err:   0.008835
[2025-10-22 06:57:07] [Iter 2127/2250] R3[1076/1200] | LR: 0.005653 | E:  -60.898035 | E_var:     0.5990 | E_err:   0.008551
[2025-10-22 06:57:34] [Iter 2128/2250] R3[1077/1200] | LR: 0.005642 | E:  -60.903087 | E_var:     0.6667 | E_err:   0.009021
[2025-10-22 06:58:01] [Iter 2129/2250] R3[1078/1200] | LR: 0.005632 | E:  -60.910088 | E_var:     0.6677 | E_err:   0.009028
[2025-10-22 06:58:28] [Iter 2130/2250] R3[1079/1200] | LR: 0.005622 | E:  -60.899314 | E_var:     0.7248 | E_err:   0.009406
[2025-10-22 06:58:55] [Iter 2131/2250] R3[1080/1200] | LR: 0.005612 | E:  -60.918645 | E_var:     0.6160 | E_err:   0.008672
[2025-10-22 06:59:22] [Iter 2132/2250] R3[1081/1200] | LR: 0.005602 | E:  -60.906295 | E_var:     0.6486 | E_err:   0.008898
[2025-10-22 06:59:49] [Iter 2133/2250] R3[1082/1200] | LR: 0.005592 | E:  -60.916860 | E_var:     0.6375 | E_err:   0.008822
[2025-10-22 07:00:16] [Iter 2134/2250] R3[1083/1200] | LR: 0.005582 | E:  -60.901872 | E_var:     0.6186 | E_err:   0.008690
[2025-10-22 07:00:42] [Iter 2135/2250] R3[1084/1200] | LR: 0.005572 | E:  -60.899453 | E_var:     0.5675 | E_err:   0.008323
[2025-10-22 07:01:09] [Iter 2136/2250] R3[1085/1200] | LR: 0.005562 | E:  -60.901361 | E_var:     0.6694 | E_err:   0.009040
[2025-10-22 07:01:36] [Iter 2137/2250] R3[1086/1200] | LR: 0.005553 | E:  -60.906715 | E_var:     0.5984 | E_err:   0.008547
[2025-10-22 07:02:03] [Iter 2138/2250] R3[1087/1200] | LR: 0.005543 | E:  -60.914602 | E_var:     0.6836 | E_err:   0.009135
[2025-10-22 07:02:30] [Iter 2139/2250] R3[1088/1200] | LR: 0.005534 | E:  -60.897214 | E_var:     0.6983 | E_err:   0.009232
[2025-10-22 07:02:57] [Iter 2140/2250] R3[1089/1200] | LR: 0.005524 | E:  -60.882714 | E_var:     0.6690 | E_err:   0.009037
[2025-10-22 07:03:24] [Iter 2141/2250] R3[1090/1200] | LR: 0.005515 | E:  -60.906624 | E_var:     0.6079 | E_err:   0.008614
[2025-10-22 07:03:51] [Iter 2142/2250] R3[1091/1200] | LR: 0.005506 | E:  -60.911170 | E_var:     0.5381 | E_err:   0.008105
[2025-10-22 07:04:18] [Iter 2143/2250] R3[1092/1200] | LR: 0.005496 | E:  -60.901884 | E_var:     0.5767 | E_err:   0.008390
[2025-10-22 07:04:44] [Iter 2144/2250] R3[1093/1200] | LR: 0.005487 | E:  -60.902052 | E_var:     0.6374 | E_err:   0.008821
[2025-10-22 07:05:11] [Iter 2145/2250] R3[1094/1200] | LR: 0.005478 | E:  -60.908752 | E_var:     0.6575 | E_err:   0.008959
[2025-10-22 07:05:38] [Iter 2146/2250] R3[1095/1200] | LR: 0.005469 | E:  -60.908636 | E_var:     0.7030 | E_err:   0.009264
[2025-10-22 07:06:05] [Iter 2147/2250] R3[1096/1200] | LR: 0.005460 | E:  -60.906293 | E_var:     0.6126 | E_err:   0.008647
[2025-10-22 07:06:32] [Iter 2148/2250] R3[1097/1200] | LR: 0.005452 | E:  -60.903264 | E_var:     0.6080 | E_err:   0.008615
[2025-10-22 07:06:59] [Iter 2149/2250] R3[1098/1200] | LR: 0.005443 | E:  -60.917020 | E_var:     0.5659 | E_err:   0.008311
[2025-10-22 07:07:26] [Iter 2150/2250] R3[1099/1200] | LR: 0.005434 | E:  -60.892345 | E_var:     0.6112 | E_err:   0.008638
[2025-10-22 07:07:53] [Iter 2151/2250] R3[1100/1200] | LR: 0.005426 | E:  -60.891398 | E_var:     0.5799 | E_err:   0.008413
[2025-10-22 07:08:20] [Iter 2152/2250] R3[1101/1200] | LR: 0.005417 | E:  -60.893573 | E_var:     0.5984 | E_err:   0.008547
[2025-10-22 07:08:46] [Iter 2153/2250] R3[1102/1200] | LR: 0.005409 | E:  -60.908740 | E_var:     0.6084 | E_err:   0.008618
[2025-10-22 07:09:13] [Iter 2154/2250] R3[1103/1200] | LR: 0.005401 | E:  -60.909199 | E_var:     0.6419 | E_err:   0.008852
[2025-10-22 07:09:40] [Iter 2155/2250] R3[1104/1200] | LR: 0.005393 | E:  -60.891549 | E_var:     0.5695 | E_err:   0.008338
[2025-10-22 07:10:07] [Iter 2156/2250] R3[1105/1200] | LR: 0.005385 | E:  -60.895879 | E_var:     0.6176 | E_err:   0.008683
[2025-10-22 07:10:34] [Iter 2157/2250] R3[1106/1200] | LR: 0.005377 | E:  -60.903109 | E_var:     0.5471 | E_err:   0.008172
[2025-10-22 07:11:01] [Iter 2158/2250] R3[1107/1200] | LR: 0.005369 | E:  -60.905307 | E_var:     0.8194 | E_err:   0.010001
[2025-10-22 07:11:28] [Iter 2159/2250] R3[1108/1200] | LR: 0.005361 | E:  -60.885578 | E_var:     0.7196 | E_err:   0.009372
[2025-10-22 07:11:55] [Iter 2160/2250] R3[1109/1200] | LR: 0.005353 | E:  -60.896172 | E_var:     0.5889 | E_err:   0.008479
[2025-10-22 07:12:22] [Iter 2161/2250] R3[1110/1200] | LR: 0.005345 | E:  -60.897735 | E_var:     0.6344 | E_err:   0.008800
[2025-10-22 07:12:48] [Iter 2162/2250] R3[1111/1200] | LR: 0.005338 | E:  -60.891809 | E_var:     0.6213 | E_err:   0.008708
[2025-10-22 07:13:15] [Iter 2163/2250] R3[1112/1200] | LR: 0.005330 | E:  -60.905412 | E_var:     0.8884 | E_err:   0.010414
[2025-10-22 07:13:42] [Iter 2164/2250] R3[1113/1200] | LR: 0.005323 | E:  -60.903276 | E_var:     0.6430 | E_err:   0.008860
[2025-10-22 07:14:09] [Iter 2165/2250] R3[1114/1200] | LR: 0.005315 | E:  -60.907163 | E_var:     0.6357 | E_err:   0.008809
[2025-10-22 07:14:36] [Iter 2166/2250] R3[1115/1200] | LR: 0.005308 | E:  -60.900531 | E_var:     0.6725 | E_err:   0.009060
[2025-10-22 07:15:03] [Iter 2167/2250] R3[1116/1200] | LR: 0.005301 | E:  -60.897618 | E_var:     0.5702 | E_err:   0.008343
[2025-10-22 07:15:30] [Iter 2168/2250] R3[1117/1200] | LR: 0.005294 | E:  -60.910765 | E_var:     0.6347 | E_err:   0.008802
[2025-10-22 07:15:57] [Iter 2169/2250] R3[1118/1200] | LR: 0.005287 | E:  -60.912950 | E_var:     0.6775 | E_err:   0.009094
[2025-10-22 07:16:24] [Iter 2170/2250] R3[1119/1200] | LR: 0.005280 | E:  -60.910042 | E_var:     0.6799 | E_err:   0.009110
[2025-10-22 07:16:50] [Iter 2171/2250] R3[1120/1200] | LR: 0.005273 | E:  -60.906565 | E_var:     0.5903 | E_err:   0.008489
[2025-10-22 07:17:17] [Iter 2172/2250] R3[1121/1200] | LR: 0.005266 | E:  -60.891891 | E_var:     0.6686 | E_err:   0.009034
[2025-10-22 07:17:44] [Iter 2173/2250] R3[1122/1200] | LR: 0.005260 | E:  -60.902964 | E_var:     0.7058 | E_err:   0.009282
[2025-10-22 07:18:11] [Iter 2174/2250] R3[1123/1200] | LR: 0.005253 | E:  -60.895732 | E_var:     0.6991 | E_err:   0.009238
[2025-10-22 07:18:38] [Iter 2175/2250] R3[1124/1200] | LR: 0.005247 | E:  -60.902458 | E_var:     0.8040 | E_err:   0.009907
[2025-10-22 07:19:05] [Iter 2176/2250] R3[1125/1200] | LR: 0.005240 | E:  -60.902888 | E_var:     0.6332 | E_err:   0.008792
[2025-10-22 07:19:32] [Iter 2177/2250] R3[1126/1200] | LR: 0.005234 | E:  -60.900535 | E_var:     0.6710 | E_err:   0.009051
[2025-10-22 07:19:59] [Iter 2178/2250] R3[1127/1200] | LR: 0.005228 | E:  -60.903693 | E_var:     0.6209 | E_err:   0.008706
[2025-10-22 07:20:25] [Iter 2179/2250] R3[1128/1200] | LR: 0.005221 | E:  -60.912336 | E_var:     0.7116 | E_err:   0.009320
[2025-10-22 07:20:52] [Iter 2180/2250] R3[1129/1200] | LR: 0.005215 | E:  -60.911923 | E_var:     0.6489 | E_err:   0.008900
[2025-10-22 07:21:19] [Iter 2181/2250] R3[1130/1200] | LR: 0.005209 | E:  -60.903913 | E_var:     0.6678 | E_err:   0.009029
[2025-10-22 07:21:46] [Iter 2182/2250] R3[1131/1200] | LR: 0.005203 | E:  -60.909059 | E_var:     0.5707 | E_err:   0.008346
[2025-10-22 07:22:13] [Iter 2183/2250] R3[1132/1200] | LR: 0.005198 | E:  -60.906562 | E_var:     0.6462 | E_err:   0.008881
[2025-10-22 07:22:40] [Iter 2184/2250] R3[1133/1200] | LR: 0.005192 | E:  -60.897199 | E_var:     0.5785 | E_err:   0.008404
[2025-10-22 07:23:07] [Iter 2185/2250] R3[1134/1200] | LR: 0.005186 | E:  -60.915558 | E_var:     0.6565 | E_err:   0.008952
[2025-10-22 07:23:34] [Iter 2186/2250] R3[1135/1200] | LR: 0.005181 | E:  -60.903355 | E_var:     0.5957 | E_err:   0.008528
[2025-10-22 07:24:01] [Iter 2187/2250] R3[1136/1200] | LR: 0.005175 | E:  -60.914064 | E_var:     0.5975 | E_err:   0.008540
[2025-10-22 07:24:27] [Iter 2188/2250] R3[1137/1200] | LR: 0.005170 | E:  -60.914361 | E_var:     0.6338 | E_err:   0.008796
[2025-10-22 07:24:54] [Iter 2189/2250] R3[1138/1200] | LR: 0.005164 | E:  -60.907891 | E_var:     0.5164 | E_err:   0.007940
[2025-10-22 07:25:21] [Iter 2190/2250] R3[1139/1200] | LR: 0.005159 | E:  -60.907217 | E_var:     0.5772 | E_err:   0.008394
[2025-10-22 07:25:48] [Iter 2191/2250] R3[1140/1200] | LR: 0.005154 | E:  -60.894243 | E_var:     0.6166 | E_err:   0.008676
[2025-10-22 07:26:15] [Iter 2192/2250] R3[1141/1200] | LR: 0.005149 | E:  -60.900510 | E_var:     0.5417 | E_err:   0.008132
[2025-10-22 07:26:42] [Iter 2193/2250] R3[1142/1200] | LR: 0.005144 | E:  -60.902953 | E_var:     0.5763 | E_err:   0.008387
[2025-10-22 07:27:09] [Iter 2194/2250] R3[1143/1200] | LR: 0.005139 | E:  -60.890364 | E_var:     0.5909 | E_err:   0.008493
[2025-10-22 07:27:36] [Iter 2195/2250] R3[1144/1200] | LR: 0.005134 | E:  -60.910477 | E_var:     0.9180 | E_err:   0.010586
[2025-10-22 07:28:02] [Iter 2196/2250] R3[1145/1200] | LR: 0.005129 | E:  -60.908772 | E_var:     0.6997 | E_err:   0.009242
[2025-10-22 07:28:29] [Iter 2197/2250] R3[1146/1200] | LR: 0.005125 | E:  -60.912769 | E_var:     0.7645 | E_err:   0.009660
[2025-10-22 07:28:56] [Iter 2198/2250] R3[1147/1200] | LR: 0.005120 | E:  -60.915268 | E_var:     0.5836 | E_err:   0.008440
[2025-10-22 07:29:23] [Iter 2199/2250] R3[1148/1200] | LR: 0.005116 | E:  -60.907882 | E_var:     0.5914 | E_err:   0.008496
[2025-10-22 07:29:50] [Iter 2200/2250] R3[1149/1200] | LR: 0.005111 | E:  -60.915479 | E_var:     0.6401 | E_err:   0.008839
[2025-10-22 07:30:17] [Iter 2201/2250] R3[1150/1200] | LR: 0.005107 | E:  -60.903408 | E_var:     0.6754 | E_err:   0.009080
[2025-10-22 07:30:44] [Iter 2202/2250] R3[1151/1200] | LR: 0.005103 | E:  -60.908281 | E_var:     0.6194 | E_err:   0.008695
[2025-10-22 07:31:11] [Iter 2203/2250] R3[1152/1200] | LR: 0.005099 | E:  -60.920157 | E_var:     0.6908 | E_err:   0.009183
[2025-10-22 07:31:38] [Iter 2204/2250] R3[1153/1200] | LR: 0.005095 | E:  -60.898687 | E_var:     0.6408 | E_err:   0.008844
[2025-10-22 07:32:04] [Iter 2205/2250] R3[1154/1200] | LR: 0.005091 | E:  -60.902230 | E_var:     0.5564 | E_err:   0.008241
[2025-10-22 07:32:31] [Iter 2206/2250] R3[1155/1200] | LR: 0.005087 | E:  -60.897099 | E_var:     0.7141 | E_err:   0.009337
[2025-10-22 07:32:58] [Iter 2207/2250] R3[1156/1200] | LR: 0.005083 | E:  -60.912783 | E_var:     0.6532 | E_err:   0.008929
[2025-10-22 07:33:25] [Iter 2208/2250] R3[1157/1200] | LR: 0.005079 | E:  -60.909983 | E_var:     0.6108 | E_err:   0.008635
[2025-10-22 07:33:52] [Iter 2209/2250] R3[1158/1200] | LR: 0.005075 | E:  -60.901892 | E_var:     0.8140 | E_err:   0.009968
[2025-10-22 07:34:19] [Iter 2210/2250] R3[1159/1200] | LR: 0.005072 | E:  -60.908692 | E_var:     0.5814 | E_err:   0.008424
[2025-10-22 07:34:46] [Iter 2211/2250] R3[1160/1200] | LR: 0.005068 | E:  -60.898409 | E_var:     0.7017 | E_err:   0.009255
[2025-10-22 07:35:13] [Iter 2212/2250] R3[1161/1200] | LR: 0.005065 | E:  -60.904635 | E_var:     0.6102 | E_err:   0.008631
[2025-10-22 07:35:40] [Iter 2213/2250] R3[1162/1200] | LR: 0.005062 | E:  -60.912095 | E_var:     0.5783 | E_err:   0.008402
[2025-10-22 07:36:06] [Iter 2214/2250] R3[1163/1200] | LR: 0.005059 | E:  -60.913504 | E_var:     0.6327 | E_err:   0.008789
[2025-10-22 07:36:33] [Iter 2215/2250] R3[1164/1200] | LR: 0.005055 | E:  -60.901851 | E_var:     0.5698 | E_err:   0.008340
[2025-10-22 07:37:00] [Iter 2216/2250] R3[1165/1200] | LR: 0.005052 | E:  -60.907010 | E_var:     0.6642 | E_err:   0.009005
[2025-10-22 07:37:27] [Iter 2217/2250] R3[1166/1200] | LR: 0.005049 | E:  -60.909348 | E_var:     0.6350 | E_err:   0.008805
[2025-10-22 07:37:54] [Iter 2218/2250] R3[1167/1200] | LR: 0.005047 | E:  -60.878297 | E_var:     0.7918 | E_err:   0.009831
[2025-10-22 07:38:21] [Iter 2219/2250] R3[1168/1200] | LR: 0.005044 | E:  -60.903988 | E_var:     0.7027 | E_err:   0.009262
[2025-10-22 07:38:48] [Iter 2220/2250] R3[1169/1200] | LR: 0.005041 | E:  -60.907961 | E_var:     0.7886 | E_err:   0.009811
[2025-10-22 07:39:15] [Iter 2221/2250] R3[1170/1200] | LR: 0.005039 | E:  -60.907141 | E_var:     0.6316 | E_err:   0.008781
[2025-10-22 07:39:42] [Iter 2222/2250] R3[1171/1200] | LR: 0.005036 | E:  -60.918138 | E_var:     0.7894 | E_err:   0.009816
[2025-10-22 07:40:08] [Iter 2223/2250] R3[1172/1200] | LR: 0.005034 | E:  -60.917820 | E_var:     0.5369 | E_err:   0.008096
[2025-10-22 07:40:35] [Iter 2224/2250] R3[1173/1200] | LR: 0.005031 | E:  -60.918885 | E_var:     0.5905 | E_err:   0.008490
[2025-10-22 07:41:02] [Iter 2225/2250] R3[1174/1200] | LR: 0.005029 | E:  -60.894737 | E_var:     0.6196 | E_err:   0.008697
[2025-10-22 07:41:29] [Iter 2226/2250] R3[1175/1200] | LR: 0.005027 | E:  -60.902225 | E_var:     0.9092 | E_err:   0.010535
[2025-10-22 07:41:56] [Iter 2227/2250] R3[1176/1200] | LR: 0.005025 | E:  -60.903256 | E_var:     0.5809 | E_err:   0.008420
[2025-10-22 07:42:23] [Iter 2228/2250] R3[1177/1200] | LR: 0.005023 | E:  -60.927959 | E_var:     0.6325 | E_err:   0.008787
[2025-10-22 07:42:50] [Iter 2229/2250] R3[1178/1200] | LR: 0.005021 | E:  -60.909332 | E_var:     0.5866 | E_err:   0.008462
[2025-10-22 07:43:17] [Iter 2230/2250] R3[1179/1200] | LR: 0.005019 | E:  -60.915163 | E_var:     0.5791 | E_err:   0.008408
[2025-10-22 07:43:44] [Iter 2231/2250] R3[1180/1200] | LR: 0.005017 | E:  -60.908989 | E_var:     0.5484 | E_err:   0.008182
[2025-10-22 07:44:10] [Iter 2232/2250] R3[1181/1200] | LR: 0.005015 | E:  -60.906240 | E_var:     0.5751 | E_err:   0.008378
[2025-10-22 07:44:37] [Iter 2233/2250] R3[1182/1200] | LR: 0.005014 | E:  -60.893952 | E_var:     0.7391 | E_err:   0.009498
[2025-10-22 07:45:04] [Iter 2234/2250] R3[1183/1200] | LR: 0.005012 | E:  -60.903323 | E_var:     0.7362 | E_err:   0.009480
[2025-10-22 07:45:31] [Iter 2235/2250] R3[1184/1200] | LR: 0.005011 | E:  -60.899685 | E_var:     0.8459 | E_err:   0.010162
[2025-10-22 07:45:58] [Iter 2236/2250] R3[1185/1200] | LR: 0.005010 | E:  -60.900995 | E_var:     0.9786 | E_err:   0.010929
[2025-10-22 07:46:25] [Iter 2237/2250] R3[1186/1200] | LR: 0.005008 | E:  -60.920593 | E_var:     0.5744 | E_err:   0.008373
[2025-10-22 07:46:52] [Iter 2238/2250] R3[1187/1200] | LR: 0.005007 | E:  -60.898107 | E_var:     0.6920 | E_err:   0.009191
[2025-10-22 07:47:19] [Iter 2239/2250] R3[1188/1200] | LR: 0.005006 | E:  -60.906115 | E_var:     0.6088 | E_err:   0.008621
[2025-10-22 07:47:46] [Iter 2240/2250] R3[1189/1200] | LR: 0.005005 | E:  -60.909105 | E_var:     0.6261 | E_err:   0.008743
[2025-10-22 07:48:12] [Iter 2241/2250] R3[1190/1200] | LR: 0.005004 | E:  -60.919442 | E_var:     0.6866 | E_err:   0.009155
[2025-10-22 07:48:39] [Iter 2242/2250] R3[1191/1200] | LR: 0.005003 | E:  -60.896717 | E_var:     0.5870 | E_err:   0.008465
[2025-10-22 07:49:06] [Iter 2243/2250] R3[1192/1200] | LR: 0.005003 | E:  -60.915739 | E_var:     0.5834 | E_err:   0.008439
[2025-10-22 07:49:33] [Iter 2244/2250] R3[1193/1200] | LR: 0.005002 | E:  -60.914015 | E_var:     0.5704 | E_err:   0.008344
[2025-10-22 07:50:00] [Iter 2245/2250] R3[1194/1200] | LR: 0.005002 | E:  -60.909173 | E_var:     0.5778 | E_err:   0.008398
[2025-10-22 07:50:27] [Iter 2246/2250] R3[1195/1200] | LR: 0.005001 | E:  -60.897370 | E_var:     0.5351 | E_err:   0.008082
[2025-10-22 07:50:54] [Iter 2247/2250] R3[1196/1200] | LR: 0.005001 | E:  -60.897367 | E_var:     0.6637 | E_err:   0.009001
[2025-10-22 07:51:21] [Iter 2248/2250] R3[1197/1200] | LR: 0.005000 | E:  -60.907556 | E_var:     0.5760 | E_err:   0.008385
[2025-10-22 07:51:47] [Iter 2249/2250] R3[1198/1200] | LR: 0.005000 | E:  -60.897295 | E_var:     0.6035 | E_err:   0.008583
[2025-10-22 07:52:14] [Iter 2250/2250] R3[1199/1200] | LR: 0.005000 | E:  -60.903988 | E_var:     0.5878 | E_err:   0.008471
[2025-10-22 07:52:14] ✓ Checkpoint saved: checkpoint_iter_002250.pkl
[2025-10-22 07:52:14] ======================================================================================================
[2025-10-22 07:52:14] ✅ Training completed successfully
[2025-10-22 07:52:14] Total restarts: 3
[2025-10-22 07:52:23] Final Energy: -60.90398827 ± 0.00847075
[2025-10-22 07:52:23] Final Variance: 0.587805
[2025-10-22 07:52:23] ======================================================================================================
[2025-10-22 07:52:23] ======================================================================================================
[2025-10-22 07:52:23] Training completed | Runtime: 60536.9s

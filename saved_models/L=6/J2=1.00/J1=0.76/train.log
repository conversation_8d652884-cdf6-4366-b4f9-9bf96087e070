[2025-10-29 23:58:25] ✓ 创建部分等变ViT模型（嵌入层等变）
[2025-10-29 23:58:25]   对称性组大小: 8 (C4v点群)
[2025-10-29 23:58:25]   嵌入层使用等变权重共享，后续Transformer层使用普通结构
[2025-10-29 23:58:46] 🔥 预热编译: 执行模型前向传播以触发JIT编译...
[2025-10-29 23:58:53] ✓ 预热编译完成 | 耗时: 7.25s
[2025-10-29 23:58:53] ======================================================================================================
[2025-10-29 23:58:53] ViT for Shastry-Sutherland Model
[2025-10-29 23:58:53] ======================================================================================================
[2025-10-29 23:58:53] System Parameters:
[2025-10-29 23:58:53]   - Lattice size: L = 6
[2025-10-29 23:58:53]   - Total sites: N = 144
[2025-10-29 23:58:53]   - J1 coupling: 0.76
[2025-10-29 23:58:53]   - J2 coupling: 1.0
[2025-10-29 23:58:53]   - Q (4-spin): 0.0
[2025-10-29 23:58:53] ------------------------------------------------------------------------------------------------------
[2025-10-29 23:58:53] Model Parameters:
[2025-10-29 23:58:53]   ViT Architecture:
[2025-10-29 23:58:53]     • Layers: 4
[2025-10-29 23:58:53]     • Embedding dimension (d_model): 96
[2025-10-29 23:58:53]     • Attention heads: 4
[2025-10-29 23:58:53]     • Patch size: 2x2
[2025-10-29 23:58:53]     • Number of patches: 36
[2025-10-29 23:58:53]     • Config: [4, 96, 4, 2]
[2025-10-29 23:58:53]     • Total parameters: 334,096
[2025-10-29 23:58:53]   Regularization:
[2025-10-29 23:58:53]     • Relative Position Encoding (RPE): True
[2025-10-29 23:58:53]     • Dropout rate: 0.1
[2025-10-29 23:58:53]   Optimizer:
[2025-10-29 23:58:53]     • Diagonal shift (SR): 0.2
[2025-10-29 23:58:53]     • Gradient clipping: 1.0
[2025-10-29 23:58:53]   Mixed Precision Training: True
[2025-10-29 23:58:53]     • Parameters: float64 (high precision)
[2025-10-29 23:58:53]     • Computation: bfloat16 (accelerated)
[2025-10-29 23:58:53]     • Critical ops (LayerNorm/Softmax/Output): float64
[2025-10-29 23:58:53]     • Expected speedup: 1.5-2x, Memory reduction: ~50%
[2025-10-29 23:58:53] ------------------------------------------------------------------------------------------------------
[2025-10-29 23:58:53] Training Hyperparameters:
[2025-10-29 23:58:53]   Learning Rate Schedule:
[2025-10-29 23:58:53]     • Max LR: 0.05
[2025-10-29 23:58:53]     • Min LR: 0.001
[2025-10-29 23:58:53]     • Annealing cycles: 1
[2025-10-29 23:58:53]     • Initial period: 1500
[2025-10-29 23:58:53]     • Period multiplier: 1.0
[2025-10-29 23:58:53]     • Warm-up: 75 iterations (5.0%)
[2025-10-29 23:58:53]     • Total iterations: 1500 + 75 (warm-up) = 1575
[2025-10-29 23:58:53]   Sampling Parameters:
[2025-10-29 23:58:53]     • Samples (n_samples): 8192
[2025-10-29 23:58:53]     • Parallel chains (n_chains): 512
[2025-10-29 23:58:53]     • Max exchange distance (d_max): 9.00 (auto from lattice)
[2025-10-29 23:58:53]     • Chunk size: 8192
[2025-10-29 23:58:53]     • Discarded samples per chain: 0
[2025-10-29 23:58:53]     • Parameter-to-sample ratio: 40.78
[2025-10-29 23:58:53]       ⚠ Moderate ratio (10-50), consider regularization
[2025-10-29 23:58:53] ------------------------------------------------------------------------------------------------------
[2025-10-29 23:58:53] Checkpoint Configuration:
[2025-10-29 23:58:53]   • Enabled: Yes
[2025-10-29 23:58:53]   • Save interval: 150 iterations
[2025-10-29 23:58:53]   • Keep history: True
[2025-10-29 23:58:53]   • Directory: saved_models/L=6/J2=1.00/J1=0.76/checkpoints
[2025-10-29 23:58:53] ------------------------------------------------------------------------------------------------------
[2025-10-29 23:58:53] Device Status:
[2025-10-29 23:58:53]   • Device type: H200
[2025-10-29 23:58:53]   • Number of devices: 1
[2025-10-29 23:58:53]   • Sharding enabled: True
[2025-10-29 23:58:54] ======================================================================================================
[2025-10-29 23:58:54] 🔥 Linear Warm-up: 75 iterations (5.0% of 1500) | LR: 0 -> 0.050000
[2025-10-29 23:58:54]    Total iterations: 75 (warm-up) + 1500 (training) = 1575
[2025-10-30 00:01:20] [Iter    1/1575] WARMUP[1/75]  | LR: 0.000667 | E:    1.952428 | E_var:    68.3630 | E_err:   0.103917
[2025-10-30 00:01:37] [Iter    2/1575] WARMUP[2/75]  | LR: 0.001333 | E:   -0.297457 | E_var:    52.5369 | E_err:   0.087823
[2025-10-30 00:01:53] [Iter    3/1575] WARMUP[3/75]  | LR: 0.002000 | E:   -1.284782 | E_var:    46.5251 | E_err:   0.082682
[2025-10-30 00:02:10] [Iter    4/1575] WARMUP[4/75]  | LR: 0.002667 | E:   -2.204185 | E_var:    47.3729 | E_err:   0.082988
[2025-10-30 00:02:27] [Iter    5/1575] WARMUP[5/75]  | LR: 0.003333 | E:   -2.852171 | E_var:    46.3391 | E_err:   0.083804
[2025-10-30 00:02:43] [Iter    6/1575] WARMUP[6/75]  | LR: 0.004000 | E:   -3.322796 | E_var:    48.4976 | E_err:   0.088317
[2025-10-30 00:03:00] [Iter    7/1575] WARMUP[7/75]  | LR: 0.004667 | E:   -4.080920 | E_var:    47.6160 | E_err:   0.083999
[2025-10-30 00:03:16] [Iter    8/1575] WARMUP[8/75]  | LR: 0.005333 | E:   -4.965910 | E_var:    44.6758 | E_err:   0.078816
[2025-10-30 00:03:33] [Iter    9/1575] WARMUP[9/75]  | LR: 0.006000 | E:   -5.848912 | E_var:    48.3129 | E_err:   0.082071
[2025-10-30 00:03:49] [Iter   10/1575] WARMUP[10/75] | LR: 0.006667 | E:   -6.681727 | E_var:    47.8525 | E_err:   0.081943
[2025-10-30 00:04:06] [Iter   11/1575] WARMUP[11/75] | LR: 0.007333 | E:   -7.633765 | E_var:    44.9851 | E_err:   0.078578
[2025-10-30 00:04:22] [Iter   12/1575] WARMUP[12/75] | LR: 0.008000 | E:   -8.630453 | E_var:    44.1403 | E_err:   0.074591
[2025-10-30 00:04:39] [Iter   13/1575] WARMUP[13/75] | LR: 0.008667 | E:   -9.622406 | E_var:    43.9493 | E_err:   0.075441
[2025-10-30 00:04:56] [Iter   14/1575] WARMUP[14/75] | LR: 0.009333 | E:  -10.563628 | E_var:    41.8436 | E_err:   0.076400
[2025-10-30 00:05:12] [Iter   15/1575] WARMUP[15/75] | LR: 0.010000 | E:  -11.820895 | E_var:    43.7173 | E_err:   0.082137
[2025-10-30 00:05:29] [Iter   16/1575] WARMUP[16/75] | LR: 0.010667 | E:  -12.754867 | E_var:    41.5566 | E_err:   0.076634
[2025-10-30 00:05:45] [Iter   17/1575] WARMUP[17/75] | LR: 0.011333 | E:  -14.061211 | E_var:    43.0280 | E_err:   0.076489
[2025-10-30 00:06:02] [Iter   18/1575] WARMUP[18/75] | LR: 0.012000 | E:  -15.333845 | E_var:    46.0271 | E_err:   0.078932
[2025-10-30 00:06:18] [Iter   19/1575] WARMUP[19/75] | LR: 0.012667 | E:  -16.771305 | E_var:    40.9937 | E_err:   0.072920
[2025-10-30 00:06:35] [Iter   20/1575] WARMUP[20/75] | LR: 0.013333 | E:  -18.082269 | E_var:    40.1786 | E_err:   0.075401
[2025-10-30 00:06:52] [Iter   21/1575] WARMUP[21/75] | LR: 0.014000 | E:  -19.527395 | E_var:    40.3681 | E_err:   0.073752
[2025-10-30 00:07:08] [Iter   22/1575] WARMUP[22/75] | LR: 0.014667 | E:  -21.102706 | E_var:    39.0389 | E_err:   0.075198
[2025-10-30 00:07:25] [Iter   23/1575] WARMUP[23/75] | LR: 0.015333 | E:  -22.343759 | E_var:    39.1508 | E_err:   0.075653
[2025-10-30 00:07:41] [Iter   24/1575] WARMUP[24/75] | LR: 0.016000 | E:  -23.739475 | E_var:    40.3397 | E_err:   0.073824
[2025-10-30 00:07:58] [Iter   25/1575] WARMUP[25/75] | LR: 0.016667 | E:  -25.156820 | E_var:    36.6141 | E_err:   0.072756
[2025-10-30 00:08:14] [Iter   26/1575] WARMUP[26/75] | LR: 0.017333 | E:  -26.380533 | E_var:    35.8032 | E_err:   0.068915
[2025-10-30 00:08:31] [Iter   27/1575] WARMUP[27/75] | LR: 0.018000 | E:  -27.534530 | E_var:    35.7559 | E_err:   0.070764
[2025-10-30 00:08:47] [Iter   28/1575] WARMUP[28/75] | LR: 0.018667 | E:  -29.049218 | E_var:    35.3699 | E_err:   0.077190
[2025-10-30 00:09:04] [Iter   29/1575] WARMUP[29/75] | LR: 0.019333 | E:  -30.114065 | E_var:    34.1443 | E_err:   0.071826
[2025-10-30 00:09:21] [Iter   30/1575] WARMUP[30/75] | LR: 0.020000 | E:  -30.887631 | E_var:    34.0637 | E_err:   0.067886
[2025-10-30 00:09:37] [Iter   31/1575] WARMUP[31/75] | LR: 0.020667 | E:  -31.729937 | E_var:    40.2247 | E_err:   0.145043
[2025-10-30 00:09:54] [Iter   32/1575] WARMUP[32/75] | LR: 0.021333 | E:  -32.501300 | E_var:    39.7385 | E_err:   0.148654
[2025-10-30 00:10:10] [Iter   33/1575] WARMUP[33/75] | LR: 0.022000 | E:  -33.059545 | E_var:    36.8752 | E_err:   0.133838
[2025-10-30 00:10:27] [Iter   34/1575] WARMUP[34/75] | LR: 0.022667 | E:  -33.808873 | E_var:    30.5065 | E_err:   0.068673
[2025-10-30 00:10:43] [Iter   35/1575] WARMUP[35/75] | LR: 0.023333 | E:  -34.396520 | E_var:    30.4894 | E_err:   0.069361
[2025-10-30 00:11:00] [Iter   36/1575] WARMUP[36/75] | LR: 0.024000 | E:  -35.389511 | E_var:    29.8485 | E_err:   0.065746
[2025-10-30 00:11:16] [Iter   37/1575] WARMUP[37/75] | LR: 0.024667 | E:  -35.853121 | E_var:    29.3569 | E_err:   0.065443
[2025-10-30 00:11:33] [Iter   38/1575] WARMUP[38/75] | LR: 0.025333 | E:  -36.435721 | E_var:    28.7771 | E_err:   0.063554
[2025-10-30 00:11:50] [Iter   39/1575] WARMUP[39/75] | LR: 0.026000 | E:  -37.265784 | E_var:    28.0358 | E_err:   0.065098
[2025-10-30 00:12:06] [Iter   40/1575] WARMUP[40/75] | LR: 0.026667 | E:  -37.730780 | E_var:    29.0202 | E_err:   0.065141
[2025-10-30 00:12:23] [Iter   41/1575] WARMUP[41/75] | LR: 0.027333 | E:  -38.349646 | E_var:    27.6748 | E_err:   0.066871
[2025-10-30 00:12:39] [Iter   42/1575] WARMUP[42/75] | LR: 0.028000 | E:  -38.873059 | E_var:    28.1795 | E_err:   0.067165
[2025-10-30 00:12:56] [Iter   43/1575] WARMUP[43/75] | LR: 0.028667 | E:  -39.367485 | E_var:    27.3136 | E_err:   0.062833
[2025-10-30 00:13:12] [Iter   44/1575] WARMUP[44/75] | LR: 0.029333 | E:  -39.837877 | E_var:    26.7339 | E_err:   0.064907
[2025-10-30 00:13:29] [Iter   45/1575] WARMUP[45/75] | LR: 0.030000 | E:  -40.143505 | E_var:    26.7469 | E_err:   0.063753
[2025-10-30 00:13:46] [Iter   46/1575] WARMUP[46/75] | LR: 0.030667 | E:  -40.571129 | E_var:    26.3788 | E_err:   0.065514
[2025-10-30 00:14:02] [Iter   47/1575] WARMUP[47/75] | LR: 0.031333 | E:  -40.886889 | E_var:    26.4010 | E_err:   0.066111
[2025-10-30 00:14:19] [Iter   48/1575] WARMUP[48/75] | LR: 0.032000 | E:  -41.102788 | E_var:    27.3753 | E_err:   0.068795
[2025-10-30 00:14:35] [Iter   49/1575] WARMUP[49/75] | LR: 0.032667 | E:  -41.796691 | E_var:    27.3849 | E_err:   0.065699
[2025-10-30 00:14:52] [Iter   50/1575] WARMUP[50/75] | LR: 0.033333 | E:  -42.152254 | E_var:    25.6106 | E_err:   0.065691
[2025-10-30 00:15:08] [Iter   51/1575] WARMUP[51/75] | LR: 0.034000 | E:  -42.408652 | E_var:    27.8714 | E_err:   0.070124
[2025-10-30 00:15:25] [Iter   52/1575] WARMUP[52/75] | LR: 0.034667 | E:  -42.667382 | E_var:    25.4728 | E_err:   0.065796
[2025-10-30 00:15:41] [Iter   53/1575] WARMUP[53/75] | LR: 0.035333 | E:  -42.969164 | E_var:    25.0959 | E_err:   0.063797
[2025-10-30 00:15:58] [Iter   54/1575] WARMUP[54/75] | LR: 0.036000 | E:  -43.279945 | E_var:    29.1880 | E_err:   0.069710
[2025-10-30 00:16:15] [Iter   55/1575] WARMUP[55/75] | LR: 0.036667 | E:  -43.254722 | E_var:    26.8942 | E_err:   0.074713
[2025-10-30 00:16:31] [Iter   56/1575] WARMUP[56/75] | LR: 0.037333 | E:  -43.292589 | E_var:    28.0674 | E_err:   0.070692
[2025-10-30 00:16:48] [Iter   57/1575] WARMUP[57/75] | LR: 0.038000 | E:  -43.513681 | E_var:    27.7827 | E_err:   0.069080
[2025-10-30 00:17:04] [Iter   58/1575] WARMUP[58/75] | LR: 0.038667 | E:  -43.850502 | E_var:    24.3672 | E_err:   0.068380
[2025-10-30 00:17:21] [Iter   59/1575] WARMUP[59/75] | LR: 0.039333 | E:  -44.081769 | E_var:    26.4734 | E_err:   0.065432
[2025-10-30 00:17:37] [Iter   60/1575] WARMUP[60/75] | LR: 0.040000 | E:  -44.338455 | E_var:    24.1199 | E_err:   0.061388
[2025-10-30 00:17:54] [Iter   61/1575] WARMUP[61/75] | LR: 0.040667 | E:  -44.574283 | E_var:    23.8449 | E_err:   0.059353
[2025-10-30 00:18:10] [Iter   62/1575] WARMUP[62/75] | LR: 0.041333 | E:  -44.817389 | E_var:    24.7326 | E_err:   0.065070
[2025-10-30 00:18:27] [Iter   63/1575] WARMUP[63/75] | LR: 0.042000 | E:  -44.959898 | E_var:    22.8423 | E_err:   0.063817
[2025-10-30 00:18:44] [Iter   64/1575] WARMUP[64/75] | LR: 0.042667 | E:  -43.731921 | E_var:    57.0628 | E_err:   0.218515
[2025-10-30 00:19:00] [Iter   65/1575] WARMUP[65/75] | LR: 0.043333 | E:  -44.296800 | E_var:    29.0988 | E_err:   0.072356
[2025-10-30 00:19:17] [Iter   66/1575] WARMUP[66/75] | LR: 0.044000 | E:  -45.098283 | E_var:    23.3868 | E_err:   0.061007
[2025-10-30 00:19:33] [Iter   67/1575] WARMUP[67/75] | LR: 0.044667 | E:  -45.352157 | E_var:    23.0345 | E_err:   0.061004
[2025-10-30 00:19:50] [Iter   68/1575] WARMUP[68/75] | LR: 0.045333 | E:  -45.671832 | E_var:    24.0406 | E_err:   0.063617
[2025-10-30 00:20:06] [Iter   69/1575] WARMUP[69/75] | LR: 0.046000 | E:  -44.957467 | E_var:    35.4397 | E_err:   0.115723
[2025-10-30 00:20:23] [Iter   70/1575] WARMUP[70/75] | LR: 0.046667 | E:  -45.315041 | E_var:    27.7373 | E_err:   0.071195
[2025-10-30 00:20:39] [Iter   71/1575] WARMUP[71/75] | LR: 0.047333 | E:  -45.858024 | E_var:    24.0330 | E_err:   0.061967
[2025-10-30 00:20:56] [Iter   72/1575] WARMUP[72/75] | LR: 0.048000 | E:  -46.197374 | E_var:    23.4651 | E_err:   0.059882
[2025-10-30 00:21:13] [Iter   73/1575] WARMUP[73/75] | LR: 0.048667 | E:  -46.278093 | E_var:    23.1944 | E_err:   0.063925
[2025-10-30 00:21:29] [Iter   74/1575] WARMUP[74/75] | LR: 0.049333 | E:  -46.699073 | E_var:    23.0972 | E_err:   0.060662
[2025-10-30 00:21:29] ✅ Warm-up completed | Starting cosine annealing from LR=0.050000
[2025-10-30 00:21:46] [Iter   75/1575] WARMUP[75/75] | LR: 0.050000 | E:  -46.782270 | E_var:    24.6715 | E_err:   0.062243
[2025-10-30 00:22:02] [Iter   76/1575] R0[0/1500]    | LR: 0.050000 | E:  -47.053651 | E_var:    22.1250 | E_err:   0.058242
[2025-10-30 00:22:19] [Iter   77/1575] R0[1/1500]    | LR: 0.050000 | E:  -47.346176 | E_var:    22.0656 | E_err:   0.057862
[2025-10-30 00:22:35] [Iter   78/1575] R0[2/1500]    | LR: 0.050000 | E:  -47.471372 | E_var:    22.5241 | E_err:   0.059073
[2025-10-30 00:22:52] [Iter   79/1575] R0[3/1500]    | LR: 0.050000 | E:  -47.742376 | E_var:    21.7922 | E_err:   0.059863
[2025-10-30 00:23:08] [Iter   80/1575] R0[4/1500]    | LR: 0.049999 | E:  -48.035376 | E_var:    23.1315 | E_err:   0.058903
[2025-10-30 00:23:25] [Iter   81/1575] R0[5/1500]    | LR: 0.049999 | E:  -48.161645 | E_var:    22.5996 | E_err:   0.061660
[2025-10-30 00:23:42] [Iter   82/1575] R0[6/1500]    | LR: 0.049998 | E:  -48.336260 | E_var:    21.5805 | E_err:   0.059354
[2025-10-30 00:23:58] [Iter   83/1575] R0[7/1500]    | LR: 0.049997 | E:  -48.464828 | E_var:    20.6310 | E_err:   0.054793
[2025-10-30 00:24:15] [Iter   84/1575] R0[8/1500]    | LR: 0.049997 | E:  -48.604376 | E_var:    22.2903 | E_err:   0.057011
[2025-10-30 00:24:31] [Iter   85/1575] R0[9/1500]    | LR: 0.049996 | E:  -49.056952 | E_var:    21.3037 | E_err:   0.056350
[2025-10-30 00:24:48] [Iter   86/1575] R0[10/1500]   | LR: 0.049995 | E:  -49.186465 | E_var:    20.7247 | E_err:   0.056929
[2025-10-30 00:25:04] [Iter   87/1575] R0[11/1500]   | LR: 0.049993 | E:  -49.466412 | E_var:    19.8907 | E_err:   0.056220
[2025-10-30 00:25:21] [Iter   88/1575] R0[12/1500]   | LR: 0.049992 | E:  -49.678672 | E_var:    19.3730 | E_err:   0.055055
[2025-10-30 00:25:38] [Iter   89/1575] R0[13/1500]   | LR: 0.049991 | E:  -49.709200 | E_var:    19.9817 | E_err:   0.055893
[2025-10-30 00:25:54] [Iter   90/1575] R0[14/1500]   | LR: 0.049989 | E:  -50.017073 | E_var:    19.6835 | E_err:   0.056830
[2025-10-30 00:26:11] [Iter   91/1575] R0[15/1500]   | LR: 0.049988 | E:  -50.081282 | E_var:    18.7175 | E_err:   0.053920
[2025-10-30 00:26:27] [Iter   92/1575] R0[16/1500]   | LR: 0.049986 | E:  -50.115902 | E_var:    19.5914 | E_err:   0.053875
[2025-10-30 00:26:44] [Iter   93/1575] R0[17/1500]   | LR: 0.049984 | E:  -50.187011 | E_var:    29.9949 | E_err:   0.115176
[2025-10-30 00:27:00] [Iter   94/1575] R0[18/1500]   | LR: 0.049983 | E:  -50.587058 | E_var:    19.1708 | E_err:   0.053096
[2025-10-30 00:27:17] [Iter   95/1575] R0[19/1500]   | LR: 0.049981 | E:  -50.798095 | E_var:    18.5175 | E_err:   0.053418
[2025-10-30 00:27:33] [Iter   96/1575] R0[20/1500]   | LR: 0.049979 | E:  -50.685461 | E_var:    18.9740 | E_err:   0.054545
[2025-10-30 00:27:50] [Iter   97/1575] R0[21/1500]   | LR: 0.049976 | E:  -50.951521 | E_var:    17.9087 | E_err:   0.051936
[2025-10-30 00:28:07] [Iter   98/1575] R0[22/1500]   | LR: 0.049974 | E:  -51.134039 | E_var:    18.2126 | E_err:   0.054048
[2025-10-30 00:28:23] [Iter   99/1575] R0[23/1500]   | LR: 0.049972 | E:  -51.263418 | E_var:    18.8407 | E_err:   0.053297
[2025-10-30 00:28:40] [Iter  100/1575] R0[24/1500]   | LR: 0.049969 | E:  -51.234592 | E_var:    17.3827 | E_err:   0.052137
[2025-10-30 00:28:56] [Iter  101/1575] R0[25/1500]   | LR: 0.049966 | E:  -51.478342 | E_var:    17.5044 | E_err:   0.050534
[2025-10-30 00:29:13] [Iter  102/1575] R0[26/1500]   | LR: 0.049964 | E:  -51.524969 | E_var:    18.4360 | E_err:   0.052896
[2025-10-30 00:29:29] [Iter  103/1575] R0[27/1500]   | LR: 0.049961 | E:  -51.067943 | E_var:    19.7520 | E_err:   0.060070
[2025-10-30 00:29:46] [Iter  104/1575] R0[28/1500]   | LR: 0.049958 | E:  -51.435801 | E_var:    17.4432 | E_err:   0.050016
[2025-10-30 00:30:02] [Iter  105/1575] R0[29/1500]   | LR: 0.049955 | E:  -51.673532 | E_var:    16.8355 | E_err:   0.048507
[2025-10-30 00:30:19] [Iter  106/1575] R0[30/1500]   | LR: 0.049952 | E:  -51.776234 | E_var:    17.3787 | E_err:   0.052610
[2025-10-30 00:30:35] [Iter  107/1575] R0[31/1500]   | LR: 0.049948 | E:  -52.028311 | E_var:    16.4990 | E_err:   0.051309
[2025-10-30 00:30:52] [Iter  108/1575] R0[32/1500]   | LR: 0.049945 | E:  -52.178921 | E_var:    17.4700 | E_err:   0.051922
[2025-10-30 00:31:09] [Iter  109/1575] R0[33/1500]   | LR: 0.049942 | E:  -52.311910 | E_var:    15.8682 | E_err:   0.050360
[2025-10-30 00:31:25] [Iter  110/1575] R0[34/1500]   | LR: 0.049938 | E:  -52.509553 | E_var:    15.4836 | E_err:   0.048665
[2025-10-30 00:31:42] [Iter  111/1575] R0[35/1500]   | LR: 0.049934 | E:  -52.625758 | E_var:    15.0952 | E_err:   0.048142
[2025-10-30 00:31:58] [Iter  112/1575] R0[36/1500]   | LR: 0.049930 | E:  -52.710445 | E_var:    15.6141 | E_err:   0.049575
[2025-10-30 00:32:15] [Iter  113/1575] R0[37/1500]   | LR: 0.049926 | E:  -52.919643 | E_var:    15.5558 | E_err:   0.048771
[2025-10-30 00:32:31] [Iter  114/1575] R0[38/1500]   | LR: 0.049922 | E:  -52.814509 | E_var:    15.4597 | E_err:   0.048098
[2025-10-30 00:32:48] [Iter  115/1575] R0[39/1500]   | LR: 0.049918 | E:  -52.965498 | E_var:    16.5223 | E_err:   0.050156
[2025-10-30 00:33:04] [Iter  116/1575] R0[40/1500]   | LR: 0.049914 | E:  -53.126483 | E_var:    15.9549 | E_err:   0.048764
[2025-10-30 00:33:21] [Iter  117/1575] R0[41/1500]   | LR: 0.049910 | E:  -53.248724 | E_var:    15.2761 | E_err:   0.048656
[2025-10-30 00:33:37] [Iter  118/1575] R0[42/1500]   | LR: 0.049905 | E:  -53.188028 | E_var:    15.0868 | E_err:   0.048993
[2025-10-30 00:33:54] [Iter  119/1575] R0[43/1500]   | LR: 0.049901 | E:  -53.384436 | E_var:    14.8703 | E_err:   0.047973
[2025-10-30 00:34:11] [Iter  120/1575] R0[44/1500]   | LR: 0.049896 | E:  -53.488492 | E_var:    15.1446 | E_err:   0.047885
[2025-10-30 00:34:27] [Iter  121/1575] R0[45/1500]   | LR: 0.049891 | E:  -53.471857 | E_var:    14.6395 | E_err:   0.045420
[2025-10-30 00:34:44] [Iter  122/1575] R0[46/1500]   | LR: 0.049886 | E:  -53.383899 | E_var:    14.4364 | E_err:   0.047943
[2025-10-30 00:35:00] [Iter  123/1575] R0[47/1500]   | LR: 0.049881 | E:  -52.917163 | E_var:    14.6187 | E_err:   0.048158
[2025-10-30 00:35:17] [Iter  124/1575] R0[48/1500]   | LR: 0.049876 | E:  -53.390446 | E_var:    14.1738 | E_err:   0.046400
[2025-10-30 00:35:33] [Iter  125/1575] R0[49/1500]   | LR: 0.049871 | E:  -53.543627 | E_var:    14.5297 | E_err:   0.049156
[2025-10-30 00:35:50] [Iter  126/1575] R0[50/1500]   | LR: 0.049866 | E:  -53.773954 | E_var:    13.8995 | E_err:   0.046294
[2025-10-30 00:36:06] [Iter  127/1575] R0[51/1500]   | LR: 0.049860 | E:  -53.987768 | E_var:    13.9752 | E_err:   0.046671
[2025-10-30 00:36:23] [Iter  128/1575] R0[52/1500]   | LR: 0.049855 | E:  -54.111606 | E_var:    14.0229 | E_err:   0.045500
[2025-10-30 00:36:40] [Iter  129/1575] R0[53/1500]   | LR: 0.049849 | E:  -54.194857 | E_var:    14.2902 | E_err:   0.045586
[2025-10-30 00:36:56] [Iter  130/1575] R0[54/1500]   | LR: 0.049843 | E:  -54.251892 | E_var:    13.3396 | E_err:   0.043997
[2025-10-30 00:37:13] [Iter  131/1575] R0[55/1500]   | LR: 0.049838 | E:  -54.422578 | E_var:    13.6576 | E_err:   0.044598
[2025-10-30 00:37:29] [Iter  132/1575] R0[56/1500]   | LR: 0.049832 | E:  -54.557665 | E_var:    14.3590 | E_err:   0.046765
[2025-10-30 00:37:46] [Iter  133/1575] R0[57/1500]   | LR: 0.049826 | E:  -54.609787 | E_var:    13.5025 | E_err:   0.044942
[2025-10-30 00:38:02] [Iter  134/1575] R0[58/1500]   | LR: 0.049819 | E:  -54.772497 | E_var:    13.7006 | E_err:   0.045211
[2025-10-30 00:38:19] [Iter  135/1575] R0[59/1500]   | LR: 0.049813 | E:  -54.833184 | E_var:    14.4313 | E_err:   0.044637
[2025-10-30 00:38:36] [Iter  136/1575] R0[60/1500]   | LR: 0.049807 | E:  -54.800833 | E_var:    12.7671 | E_err:   0.044415
[2025-10-30 00:38:52] [Iter  137/1575] R0[61/1500]   | LR: 0.049800 | E:  -54.818089 | E_var:    13.5301 | E_err:   0.045701
[2025-10-30 00:39:09] [Iter  138/1575] R0[62/1500]   | LR: 0.049794 | E:  -54.888353 | E_var:    13.1343 | E_err:   0.042228
[2025-10-30 00:39:25] [Iter  139/1575] R0[63/1500]   | LR: 0.049787 | E:  -55.055791 | E_var:    14.5258 | E_err:   0.045073
[2025-10-30 00:39:42] [Iter  140/1575] R0[64/1500]   | LR: 0.049780 | E:  -54.944087 | E_var:    12.5900 | E_err:   0.042390
[2025-10-30 00:39:58] [Iter  141/1575] R0[65/1500]   | LR: 0.049773 | E:  -55.271264 | E_var:    12.9669 | E_err:   0.042180
[2025-10-30 00:40:15] [Iter  142/1575] R0[66/1500]   | LR: 0.049766 | E:  -55.366581 | E_var:    13.1727 | E_err:   0.043094
[2025-10-30 00:40:31] [Iter  143/1575] R0[67/1500]   | LR: 0.049759 | E:  -55.426734 | E_var:    12.8864 | E_err:   0.044650
[2025-10-30 00:40:48] [Iter  144/1575] R0[68/1500]   | LR: 0.049752 | E:  -55.393671 | E_var:    12.9393 | E_err:   0.045248
[2025-10-30 00:41:05] [Iter  145/1575] R0[69/1500]   | LR: 0.049745 | E:  -55.461560 | E_var:    13.1308 | E_err:   0.044792
[2025-10-30 00:41:21] [Iter  146/1575] R0[70/1500]   | LR: 0.049737 | E:  -55.515691 | E_var:    12.5411 | E_err:   0.042683
[2025-10-30 00:41:38] [Iter  147/1575] R0[71/1500]   | LR: 0.049730 | E:  -55.671307 | E_var:    12.0637 | E_err:   0.041228
[2025-10-30 00:41:54] [Iter  148/1575] R0[72/1500]   | LR: 0.049722 | E:  -55.668083 | E_var:    12.4302 | E_err:   0.042022
[2025-10-30 00:42:11] [Iter  149/1575] R0[73/1500]   | LR: 0.049714 | E:  -55.579240 | E_var:    12.6120 | E_err:   0.045468
[2025-10-30 00:42:27] [Iter  150/1575] R0[74/1500]   | LR: 0.049706 | E:  -55.653660 | E_var:    11.6562 | E_err:   0.042641
[2025-10-30 00:42:44] [Iter  151/1575] R0[75/1500]   | LR: 0.049698 | E:  -55.805427 | E_var:    11.6786 | E_err:   0.042524
[2025-10-30 00:43:01] [Iter  152/1575] R0[76/1500]   | LR: 0.049690 | E:  -55.857875 | E_var:    11.4893 | E_err:   0.041957
[2025-10-30 00:43:17] [Iter  153/1575] R0[77/1500]   | LR: 0.049682 | E:  -55.831289 | E_var:    11.4914 | E_err:   0.040959
[2025-10-30 00:43:34] [Iter  154/1575] R0[78/1500]   | LR: 0.049674 | E:  -55.911615 | E_var:    12.0065 | E_err:   0.040627
[2025-10-30 00:43:50] [Iter  155/1575] R0[79/1500]   | LR: 0.049665 | E:  -55.993790 | E_var:    12.2986 | E_err:   0.042701
[2025-10-30 00:44:07] [Iter  156/1575] R0[80/1500]   | LR: 0.049657 | E:  -56.067429 | E_var:    11.5666 | E_err:   0.040151
[2025-10-30 00:44:23] [Iter  157/1575] R0[81/1500]   | LR: 0.049648 | E:  -56.059979 | E_var:    11.5858 | E_err:   0.042778
[2025-10-30 00:44:40] [Iter  158/1575] R0[82/1500]   | LR: 0.049640 | E:  -56.089546 | E_var:    11.9932 | E_err:   0.040737
[2025-10-30 00:44:56] [Iter  159/1575] R0[83/1500]   | LR: 0.049631 | E:  -56.084079 | E_var:    11.2128 | E_err:   0.038404
[2025-10-30 00:45:13] [Iter  160/1575] R0[84/1500]   | LR: 0.049622 | E:  -56.188338 | E_var:    11.3449 | E_err:   0.040950
[2025-10-30 00:45:29] [Iter  161/1575] R0[85/1500]   | LR: 0.049613 | E:  -56.049682 | E_var:    11.9489 | E_err:   0.042221
[2025-10-30 00:45:46] [Iter  162/1575] R0[86/1500]   | LR: 0.049604 | E:  -56.099816 | E_var:    11.6848 | E_err:   0.042464
[2025-10-30 00:46:03] [Iter  163/1575] R0[87/1500]   | LR: 0.049594 | E:  -56.084850 | E_var:    11.5953 | E_err:   0.041974
[2025-10-30 00:46:19] [Iter  164/1575] R0[88/1500]   | LR: 0.049585 | E:  -56.179104 | E_var:    11.5352 | E_err:   0.041084
[2025-10-30 00:46:36] [Iter  165/1575] R0[89/1500]   | LR: 0.049576 | E:  -56.151365 | E_var:    11.1826 | E_err:   0.039382
[2025-10-30 00:46:52] [Iter  166/1575] R0[90/1500]   | LR: 0.049566 | E:  -56.245986 | E_var:    11.1962 | E_err:   0.040276
[2025-10-30 00:47:09] [Iter  167/1575] R0[91/1500]   | LR: 0.049556 | E:  -56.218801 | E_var:    11.0967 | E_err:   0.040026
[2025-10-30 00:47:25] [Iter  168/1575] R0[92/1500]   | LR: 0.049547 | E:  -56.244858 | E_var:    11.3461 | E_err:   0.039925
[2025-10-30 00:47:42] [Iter  169/1575] R0[93/1500]   | LR: 0.049537 | E:  -56.104578 | E_var:    11.3579 | E_err:   0.041967
[2025-10-30 00:47:58] [Iter  170/1575] R0[94/1500]   | LR: 0.049527 | E:  -56.314677 | E_var:    10.8827 | E_err:   0.039161
[2025-10-30 00:48:15] [Iter  171/1575] R0[95/1500]   | LR: 0.049517 | E:  -56.416498 | E_var:    11.1771 | E_err:   0.038836
[2025-10-30 00:48:32] [Iter  172/1575] R0[96/1500]   | LR: 0.049506 | E:  -56.433368 | E_var:    11.6726 | E_err:   0.040735
[2025-10-30 00:48:48] [Iter  173/1575] R0[97/1500]   | LR: 0.049496 | E:  -56.371913 | E_var:    10.8667 | E_err:   0.040535
[2025-10-30 00:49:05] [Iter  174/1575] R0[98/1500]   | LR: 0.049486 | E:  -56.428755 | E_var:    11.0503 | E_err:   0.037739
[2025-10-30 00:49:21] [Iter  175/1575] R0[99/1500]   | LR: 0.049475 | E:  -56.512971 | E_var:    11.2258 | E_err:   0.038362
[2025-10-30 00:49:38] [Iter  176/1575] R0[100/1500]  | LR: 0.049465 | E:  -56.265013 | E_var:    11.5379 | E_err:   0.041323
[2025-10-30 00:49:54] [Iter  177/1575] R0[101/1500]  | LR: 0.049454 | E:  -56.398742 | E_var:    10.7950 | E_err:   0.040184
[2025-10-30 00:50:11] [Iter  178/1575] R0[102/1500]  | LR: 0.049443 | E:  -56.198024 | E_var:    10.3673 | E_err:   0.037639
[2025-10-30 00:50:28] [Iter  179/1575] R0[103/1500]  | LR: 0.049432 | E:  -56.273815 | E_var:    10.7208 | E_err:   0.039732
[2025-10-30 00:50:44] [Iter  180/1575] R0[104/1500]  | LR: 0.049421 | E:  -56.383496 | E_var:    10.2261 | E_err:   0.038232
[2025-10-30 00:51:01] [Iter  181/1575] R0[105/1500]  | LR: 0.049410 | E:  -56.525376 | E_var:    10.2145 | E_err:   0.038246
[2025-10-30 00:51:17] [Iter  182/1575] R0[106/1500]  | LR: 0.049399 | E:  -56.567202 | E_var:    10.9585 | E_err:   0.039295
[2025-10-30 00:51:34] [Iter  183/1575] R0[107/1500]  | LR: 0.049387 | E:  -56.567407 | E_var:    10.2816 | E_err:   0.039296
[2025-10-30 00:51:50] [Iter  184/1575] R0[108/1500]  | LR: 0.049376 | E:  -56.695134 | E_var:    10.7674 | E_err:   0.038758
[2025-10-30 00:52:07] [Iter  185/1575] R0[109/1500]  | LR: 0.049364 | E:  -56.652106 | E_var:    10.0129 | E_err:   0.037196
[2025-10-30 00:52:23] [Iter  186/1575] R0[110/1500]  | LR: 0.049353 | E:  -56.681387 | E_var:    10.0201 | E_err:   0.036942
[2025-10-30 00:52:40] [Iter  187/1575] R0[111/1500]  | LR: 0.049341 | E:  -56.412394 | E_var:     9.8531 | E_err:   0.038780
[2025-10-30 00:52:56] [Iter  188/1575] R0[112/1500]  | LR: 0.049329 | E:  -56.762231 | E_var:    10.1452 | E_err:   0.037185
[2025-10-30 00:53:13] [Iter  189/1575] R0[113/1500]  | LR: 0.049317 | E:  -56.726808 | E_var:    10.1130 | E_err:   0.035886
[2025-10-30 00:53:30] [Iter  190/1575] R0[114/1500]  | LR: 0.049305 | E:  -56.830158 | E_var:    10.2020 | E_err:   0.036291
[2025-10-30 00:53:46] [Iter  191/1575] R0[115/1500]  | LR: 0.049293 | E:  -56.543072 | E_var:    10.1921 | E_err:   0.037285
[2025-10-30 00:54:03] [Iter  192/1575] R0[116/1500]  | LR: 0.049280 | E:  -56.496708 | E_var:     9.9846 | E_err:   0.038159
[2025-10-30 00:54:19] [Iter  193/1575] R0[117/1500]  | LR: 0.049268 | E:  -56.523758 | E_var:    10.0381 | E_err:   0.037653
[2025-10-30 00:54:36] [Iter  194/1575] R0[118/1500]  | LR: 0.049256 | E:  -56.702405 | E_var:     9.9045 | E_err:   0.037327
[2025-10-30 00:54:52] [Iter  195/1575] R0[119/1500]  | LR: 0.049243 | E:  -56.941600 | E_var:    10.4037 | E_err:   0.037334
[2025-10-30 00:55:09] [Iter  196/1575] R0[120/1500]  | LR: 0.049230 | E:  -56.925031 | E_var:     9.8501 | E_err:   0.035944
[2025-10-30 00:55:25] [Iter  197/1575] R0[121/1500]  | LR: 0.049217 | E:  -56.882805 | E_var:     9.5453 | E_err:   0.036411
[2025-10-30 00:55:42] [Iter  198/1575] R0[122/1500]  | LR: 0.049205 | E:  -56.788304 | E_var:     9.7316 | E_err:   0.037909
[2025-10-30 00:55:59] [Iter  199/1575] R0[123/1500]  | LR: 0.049192 | E:  -56.810252 | E_var:     9.7267 | E_err:   0.037997
[2025-10-30 00:56:15] [Iter  200/1575] R0[124/1500]  | LR: 0.049178 | E:  -56.770800 | E_var:     9.9189 | E_err:   0.036468
[2025-10-30 00:56:32] [Iter  201/1575] R0[125/1500]  | LR: 0.049165 | E:  -56.371187 | E_var:     9.5913 | E_err:   0.037111
[2025-10-30 00:56:48] [Iter  202/1575] R0[126/1500]  | LR: 0.049152 | E:  -56.885235 | E_var:     9.5718 | E_err:   0.034414
[2025-10-30 00:57:05] [Iter  203/1575] R0[127/1500]  | LR: 0.049138 | E:  -57.018791 | E_var:     9.8696 | E_err:   0.034906
[2025-10-30 00:57:21] [Iter  204/1575] R0[128/1500]  | LR: 0.049125 | E:  -57.012761 | E_var:     9.6820 | E_err:   0.035572
[2025-10-30 00:57:38] [Iter  205/1575] R0[129/1500]  | LR: 0.049111 | E:  -57.044984 | E_var:     9.5888 | E_err:   0.035222
[2025-10-30 00:57:54] [Iter  206/1575] R0[130/1500]  | LR: 0.049097 | E:  -57.103530 | E_var:     9.8663 | E_err:   0.036400
[2025-10-30 00:58:11] [Iter  207/1575] R0[131/1500]  | LR: 0.049084 | E:  -57.077406 | E_var:     9.5801 | E_err:   0.035061
[2025-10-30 00:58:28] [Iter  208/1575] R0[132/1500]  | LR: 0.049070 | E:  -57.090547 | E_var:     9.7390 | E_err:   0.035426
[2025-10-30 00:58:44] [Iter  209/1575] R0[133/1500]  | LR: 0.049056 | E:  -57.170864 | E_var:     9.5064 | E_err:   0.034169
[2025-10-30 00:59:01] [Iter  210/1575] R0[134/1500]  | LR: 0.049041 | E:  -57.231933 | E_var:     9.8329 | E_err:   0.035236
[2025-10-30 00:59:17] [Iter  211/1575] R0[135/1500]  | LR: 0.049027 | E:  -56.960771 | E_var:     9.2657 | E_err:   0.035538
[2025-10-30 00:59:34] [Iter  212/1575] R0[136/1500]  | LR: 0.049013 | E:  -56.548456 | E_var:     9.0870 | E_err:   0.035259
[2025-10-30 00:59:50] [Iter  213/1575] R0[137/1500]  | LR: 0.048998 | E:  -56.466104 | E_var:     9.2181 | E_err:   0.035335
[2025-10-30 01:00:07] [Iter  214/1575] R0[138/1500]  | LR: 0.048984 | E:  -57.078227 | E_var:     9.3755 | E_err:   0.035642
[2025-10-30 01:00:23] [Iter  215/1575] R0[139/1500]  | LR: 0.048969 | E:  -57.074748 | E_var:     9.4259 | E_err:   0.035935
[2025-10-30 01:00:40] [Iter  216/1575] R0[140/1500]  | LR: 0.048954 | E:  -57.131267 | E_var:     9.2267 | E_err:   0.035026
[2025-10-30 01:00:57] [Iter  217/1575] R0[141/1500]  | LR: 0.048939 | E:  -57.234957 | E_var:     9.3435 | E_err:   0.035353
[2025-10-30 01:01:13] [Iter  218/1575] R0[142/1500]  | LR: 0.048924 | E:  -57.225431 | E_var:     9.4112 | E_err:   0.034748
[2025-10-30 01:01:30] [Iter  219/1575] R0[143/1500]  | LR: 0.048909 | E:  -57.199691 | E_var:     9.1544 | E_err:   0.035122
[2025-10-30 01:01:46] [Iter  220/1575] R0[144/1500]  | LR: 0.048894 | E:  -57.228497 | E_var:     9.4787 | E_err:   0.035546
[2025-10-30 01:02:03] [Iter  221/1575] R0[145/1500]  | LR: 0.048879 | E:  -57.316059 | E_var:     9.2419 | E_err:   0.034013
[2025-10-30 01:02:19] [Iter  222/1575] R0[146/1500]  | LR: 0.048863 | E:  -56.643432 | E_var:     9.1464 | E_err:   0.034683
[2025-10-30 01:02:36] [Iter  223/1575] R0[147/1500]  | LR: 0.048848 | E:  -57.257430 | E_var:     9.2981 | E_err:   0.034647
[2025-10-30 01:02:52] [Iter  224/1575] R0[148/1500]  | LR: 0.048832 | E:  -57.364077 | E_var:     9.3385 | E_err:   0.033787
[2025-10-30 01:03:09] [Iter  225/1575] R0[149/1500]  | LR: 0.048817 | E:  -57.400793 | E_var:     9.5163 | E_err:   0.036169
[2025-10-30 01:03:09] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-10-30 01:03:26] [Iter  226/1575] R0[150/1500]  | LR: 0.048801 | E:  -57.358514 | E_var:     9.1815 | E_err:   0.035029
[2025-10-30 01:03:43] [Iter  227/1575] R0[151/1500]  | LR: 0.048785 | E:  -57.209344 | E_var:     8.8963 | E_err:   0.031997
[2025-10-30 01:03:59] [Iter  228/1575] R0[152/1500]  | LR: 0.048769 | E:  -57.413459 | E_var:     9.0912 | E_err:   0.035349
[2025-10-30 01:04:16] [Iter  229/1575] R0[153/1500]  | LR: 0.048753 | E:  -57.473929 | E_var:     9.4354 | E_err:   0.035099
[2025-10-30 01:04:32] [Iter  230/1575] R0[154/1500]  | LR: 0.048737 | E:  -57.444917 | E_var:     9.2364 | E_err:   0.035739
[2025-10-30 01:04:49] [Iter  231/1575] R0[155/1500]  | LR: 0.048720 | E:  -57.434525 | E_var:     9.0145 | E_err:   0.033974
[2025-10-30 01:05:05] [Iter  232/1575] R0[156/1500]  | LR: 0.048704 | E:  -57.463946 | E_var:     8.8027 | E_err:   0.033053
[2025-10-30 01:05:22] [Iter  233/1575] R0[157/1500]  | LR: 0.048687 | E:  -57.460201 | E_var:     9.0096 | E_err:   0.034896
[2025-10-30 01:05:39] [Iter  234/1575] R0[158/1500]  | LR: 0.048671 | E:  -57.350915 | E_var:     8.6830 | E_err:   0.032976
[2025-10-30 01:05:55] [Iter  235/1575] R0[159/1500]  | LR: 0.048654 | E:  -57.309993 | E_var:     8.6799 | E_err:   0.034003
[2025-10-30 01:06:12] [Iter  236/1575] R0[160/1500]  | LR: 0.048637 | E:  -57.566176 | E_var:     9.3780 | E_err:   0.035246
[2025-10-30 01:06:28] [Iter  237/1575] R0[161/1500]  | LR: 0.048620 | E:  -57.597286 | E_var:     8.9462 | E_err:   0.032218
[2025-10-30 01:06:45] [Iter  238/1575] R0[162/1500]  | LR: 0.048603 | E:  -57.603032 | E_var:     8.6991 | E_err:   0.034286
[2025-10-30 01:07:01] [Iter  239/1575] R0[163/1500]  | LR: 0.048586 | E:  -57.578900 | E_var:     8.7013 | E_err:   0.034057
[2025-10-30 01:07:18] [Iter  240/1575] R0[164/1500]  | LR: 0.048569 | E:  -57.565553 | E_var:     9.0344 | E_err:   0.033728
[2025-10-30 01:07:34] [Iter  241/1575] R0[165/1500]  | LR: 0.048552 | E:  -57.582180 | E_var:     9.1026 | E_err:   0.033085
[2025-10-30 01:07:51] [Iter  242/1575] R0[166/1500]  | LR: 0.048534 | E:  -57.655487 | E_var:     8.9331 | E_err:   0.032939
[2025-10-30 01:08:08] [Iter  243/1575] R0[167/1500]  | LR: 0.048517 | E:  -57.346737 | E_var:     8.6523 | E_err:   0.035347
[2025-10-30 01:08:24] [Iter  244/1575] R0[168/1500]  | LR: 0.048499 | E:  -57.640762 | E_var:     9.1771 | E_err:   0.034489
[2025-10-30 01:08:41] [Iter  245/1575] R0[169/1500]  | LR: 0.048481 | E:  -57.577110 | E_var:     8.9690 | E_err:   0.033927
[2025-10-30 01:08:57] [Iter  246/1575] R0[170/1500]  | LR: 0.048463 | E:  -57.639025 | E_var:     8.8610 | E_err:   0.034973
[2025-10-30 01:09:14] [Iter  247/1575] R0[171/1500]  | LR: 0.048445 | E:  -57.667551 | E_var:     9.1324 | E_err:   0.034094
[2025-10-30 01:09:30] [Iter  248/1575] R0[172/1500]  | LR: 0.048427 | E:  -57.610826 | E_var:     8.6675 | E_err:   0.033253
[2025-10-30 01:09:47] [Iter  249/1575] R0[173/1500]  | LR: 0.048409 | E:  -57.592152 | E_var:     9.1731 | E_err:   0.035058
[2025-10-30 01:10:04] [Iter  250/1575] R0[174/1500]  | LR: 0.048391 | E:  -57.690408 | E_var:     9.0275 | E_err:   0.033418
[2025-10-30 01:10:20] [Iter  251/1575] R0[175/1500]  | LR: 0.048373 | E:  -57.717605 | E_var:     8.9375 | E_err:   0.034322
[2025-10-30 01:10:37] [Iter  252/1575] R0[176/1500]  | LR: 0.048354 | E:  -57.766587 | E_var:     9.0098 | E_err:   0.035583
[2025-10-30 01:10:53] [Iter  253/1575] R0[177/1500]  | LR: 0.048336 | E:  -57.713284 | E_var:     9.2675 | E_err:   0.034833
[2025-10-30 01:11:10] [Iter  254/1575] R0[178/1500]  | LR: 0.048317 | E:  -57.673446 | E_var:     9.1353 | E_err:   0.034493
[2025-10-30 01:11:26] [Iter  255/1575] R0[179/1500]  | LR: 0.048298 | E:  -57.644466 | E_var:     8.4564 | E_err:   0.033070
[2025-10-30 01:11:43] [Iter  256/1575] R0[180/1500]  | LR: 0.048280 | E:  -57.698091 | E_var:     9.3339 | E_err:   0.034113
[2025-10-30 01:11:59] [Iter  257/1575] R0[181/1500]  | LR: 0.048261 | E:  -57.739931 | E_var:     8.5755 | E_err:   0.033992
[2025-10-30 01:12:16] [Iter  258/1575] R0[182/1500]  | LR: 0.048242 | E:  -57.676432 | E_var:     8.1365 | E_err:   0.032759
[2025-10-30 01:12:33] [Iter  259/1575] R0[183/1500]  | LR: 0.048222 | E:  -57.707416 | E_var:     8.1718 | E_err:   0.032960
[2025-10-30 01:12:49] [Iter  260/1575] R0[184/1500]  | LR: 0.048203 | E:  -57.362067 | E_var:     8.0277 | E_err:   0.031910
[2025-10-30 01:13:06] [Iter  261/1575] R0[185/1500]  | LR: 0.048184 | E:  -57.177826 | E_var:     7.7979 | E_err:   0.031642
[2025-10-30 01:13:22] [Iter  262/1575] R0[186/1500]  | LR: 0.048164 | E:  -57.591441 | E_var:     8.1530 | E_err:   0.031917
[2025-10-30 01:13:39] [Iter  263/1575] R0[187/1500]  | LR: 0.048145 | E:  -57.704190 | E_var:     8.3508 | E_err:   0.035322
[2025-10-30 01:13:55] [Iter  264/1575] R0[188/1500]  | LR: 0.048125 | E:  -57.774408 | E_var:     8.5970 | E_err:   0.033800
[2025-10-30 01:14:12] [Iter  265/1575] R0[189/1500]  | LR: 0.048105 | E:  -57.774759 | E_var:     8.5201 | E_err:   0.033279
[2025-10-30 01:14:29] [Iter  266/1575] R0[190/1500]  | LR: 0.048086 | E:  -57.651614 | E_var:     8.2012 | E_err:   0.032469
[2025-10-30 01:14:45] [Iter  267/1575] R0[191/1500]  | LR: 0.048066 | E:  -57.604837 | E_var:     7.9837 | E_err:   0.031520
[2025-10-30 01:15:02] [Iter  268/1575] R0[192/1500]  | LR: 0.048046 | E:  -57.888765 | E_var:     8.7432 | E_err:   0.034701
[2025-10-30 01:15:18] [Iter  269/1575] R0[193/1500]  | LR: 0.048026 | E:  -57.837946 | E_var:     8.5885 | E_err:   0.031990
[2025-10-30 01:15:35] [Iter  270/1575] R0[194/1500]  | LR: 0.048005 | E:  -57.746067 | E_var:     9.3461 | E_err:   0.035864
[2025-10-30 01:15:51] [Iter  271/1575] R0[195/1500]  | LR: 0.047985 | E:  -57.806783 | E_var:     8.8337 | E_err:   0.033567
[2025-10-30 01:16:08] [Iter  272/1575] R0[196/1500]  | LR: 0.047965 | E:  -57.765142 | E_var:     9.3886 | E_err:   0.034904
[2025-10-30 01:16:24] [Iter  273/1575] R0[197/1500]  | LR: 0.047944 | E:  -57.788558 | E_var:     8.5634 | E_err:   0.034025
[2025-10-30 01:16:41] [Iter  274/1575] R0[198/1500]  | LR: 0.047923 | E:  -57.716513 | E_var:     7.8375 | E_err:   0.032018
[2025-10-30 01:16:58] [Iter  275/1575] R0[199/1500]  | LR: 0.047903 | E:  -57.749984 | E_var:     7.8656 | E_err:   0.033852
[2025-10-30 01:17:14] [Iter  276/1575] R0[200/1500]  | LR: 0.047882 | E:  -57.837863 | E_var:     8.1713 | E_err:   0.031315
[2025-10-30 01:17:31] [Iter  277/1575] R0[201/1500]  | LR: 0.047861 | E:  -57.783918 | E_var:     8.6532 | E_err:   0.032644
[2025-10-30 01:17:47] [Iter  278/1575] R0[202/1500]  | LR: 0.047840 | E:  -57.657284 | E_var:     9.2521 | E_err:   0.034886
[2025-10-30 01:18:04] [Iter  279/1575] R0[203/1500]  | LR: 0.047819 | E:  -57.804402 | E_var:     8.5617 | E_err:   0.033206
[2025-10-30 01:18:20] [Iter  280/1575] R0[204/1500]  | LR: 0.047798 | E:  -57.778765 | E_var:     8.7640 | E_err:   0.033752
[2025-10-30 01:18:37] [Iter  281/1575] R0[205/1500]  | LR: 0.047776 | E:  -57.781366 | E_var:     8.8311 | E_err:   0.033259
[2025-10-30 01:18:53] [Iter  282/1575] R0[206/1500]  | LR: 0.047755 | E:  -57.876780 | E_var:     8.3096 | E_err:   0.031949
[2025-10-30 01:19:10] [Iter  283/1575] R0[207/1500]  | LR: 0.047733 | E:  -57.887079 | E_var:     8.3387 | E_err:   0.031341
[2025-10-30 01:19:27] [Iter  284/1575] R0[208/1500]  | LR: 0.047712 | E:  -57.891052 | E_var:     8.2204 | E_err:   0.032909
[2025-10-30 01:19:43] [Iter  285/1575] R0[209/1500]  | LR: 0.047690 | E:  -57.946019 | E_var:     8.4242 | E_err:   0.035212
[2025-10-30 01:20:00] [Iter  286/1575] R0[210/1500]  | LR: 0.047668 | E:  -57.966361 | E_var:     8.1229 | E_err:   0.031307
[2025-10-30 01:20:16] [Iter  287/1575] R0[211/1500]  | LR: 0.047646 | E:  -57.924713 | E_var:     8.4011 | E_err:   0.032586
[2025-10-30 01:20:33] [Iter  288/1575] R0[212/1500]  | LR: 0.047624 | E:  -57.908486 | E_var:     8.2364 | E_err:   0.032869
[2025-10-30 01:20:49] [Iter  289/1575] R0[213/1500]  | LR: 0.047602 | E:  -57.819349 | E_var:     7.9125 | E_err:   0.033329
[2025-10-30 01:21:06] [Iter  290/1575] R0[214/1500]  | LR: 0.047580 | E:  -57.898001 | E_var:     8.0990 | E_err:   0.031597
[2025-10-30 01:21:23] [Iter  291/1575] R0[215/1500]  | LR: 0.047558 | E:  -58.001688 | E_var:     8.1466 | E_err:   0.034581
[2025-10-30 01:21:39] [Iter  292/1575] R0[216/1500]  | LR: 0.047535 | E:  -57.898803 | E_var:     8.1635 | E_err:   0.033044
[2025-10-30 01:21:56] [Iter  293/1575] R0[217/1500]  | LR: 0.047513 | E:  -57.927349 | E_var:     8.1968 | E_err:   0.032821
[2025-10-30 01:22:12] [Iter  294/1575] R0[218/1500]  | LR: 0.047490 | E:  -57.992016 | E_var:     7.9818 | E_err:   0.031127
[2025-10-30 01:22:29] [Iter  295/1575] R0[219/1500]  | LR: 0.047468 | E:  -57.960505 | E_var:     7.9606 | E_err:   0.033062
[2025-10-30 01:22:45] [Iter  296/1575] R0[220/1500]  | LR: 0.047445 | E:  -57.911009 | E_var:     7.7441 | E_err:   0.031513
[2025-10-30 01:23:02] [Iter  297/1575] R0[221/1500]  | LR: 0.047422 | E:  -57.959511 | E_var:     7.7570 | E_err:   0.031335
[2025-10-30 01:23:19] [Iter  298/1575] R0[222/1500]  | LR: 0.047399 | E:  -58.019030 | E_var:     8.0163 | E_err:   0.030783
[2025-10-30 01:23:35] [Iter  299/1575] R0[223/1500]  | LR: 0.047376 | E:  -58.022138 | E_var:     8.1128 | E_err:   0.032962
[2025-10-30 01:23:52] [Iter  300/1575] R0[224/1500]  | LR: 0.047353 | E:  -57.977892 | E_var:     8.1272 | E_err:   0.032624
[2025-10-30 01:24:08] [Iter  301/1575] R0[225/1500]  | LR: 0.047330 | E:  -58.052683 | E_var:     7.9547 | E_err:   0.031591
[2025-10-30 01:24:25] [Iter  302/1575] R0[226/1500]  | LR: 0.047306 | E:  -58.040786 | E_var:     8.1330 | E_err:   0.032833
[2025-10-30 01:24:41] [Iter  303/1575] R0[227/1500]  | LR: 0.047283 | E:  -58.010962 | E_var:     7.8350 | E_err:   0.031283
[2025-10-30 01:24:58] [Iter  304/1575] R0[228/1500]  | LR: 0.047259 | E:  -58.043013 | E_var:     9.0954 | E_err:   0.035229
[2025-10-30 01:25:14] [Iter  305/1575] R0[229/1500]  | LR: 0.047236 | E:  -57.905871 | E_var:     9.0454 | E_err:   0.034439
[2025-10-30 01:25:31] [Iter  306/1575] R0[230/1500]  | LR: 0.047212 | E:  -57.970144 | E_var:     7.7634 | E_err:   0.031210
[2025-10-30 01:25:48] [Iter  307/1575] R0[231/1500]  | LR: 0.047188 | E:  -58.030841 | E_var:     7.8610 | E_err:   0.031517
[2025-10-30 01:26:04] [Iter  308/1575] R0[232/1500]  | LR: 0.047164 | E:  -58.047063 | E_var:     7.6386 | E_err:   0.032377
[2025-10-30 01:26:21] [Iter  309/1575] R0[233/1500]  | LR: 0.047140 | E:  -58.076717 | E_var:     7.5832 | E_err:   0.030452
[2025-10-30 01:26:37] [Iter  310/1575] R0[234/1500]  | LR: 0.047116 | E:  -57.721872 | E_var:     7.2478 | E_err:   0.030839
[2025-10-30 01:26:54] [Iter  311/1575] R0[235/1500]  | LR: 0.047092 | E:  -58.087384 | E_var:     7.8835 | E_err:   0.031218
[2025-10-30 01:27:10] [Iter  312/1575] R0[236/1500]  | LR: 0.047068 | E:  -58.074478 | E_var:     8.2071 | E_err:   0.030865
[2025-10-30 01:27:27] [Iter  313/1575] R0[237/1500]  | LR: 0.047043 | E:  -58.050702 | E_var:     8.3228 | E_err:   0.033225
[2025-10-30 01:27:43] [Iter  314/1575] R0[238/1500]  | LR: 0.047019 | E:  -58.055692 | E_var:     8.8350 | E_err:   0.034092
[2025-10-30 01:28:00] [Iter  315/1575] R0[239/1500]  | LR: 0.046994 | E:  -58.023417 | E_var:     8.1136 | E_err:   0.031759
[2025-10-30 01:28:17] [Iter  316/1575] R0[240/1500]  | LR: 0.046970 | E:  -57.995083 | E_var:     8.2763 | E_err:   0.032944
[2025-10-30 01:28:33] [Iter  317/1575] R0[241/1500]  | LR: 0.046945 | E:  -58.072766 | E_var:     7.8146 | E_err:   0.031138
[2025-10-30 01:28:50] [Iter  318/1575] R0[242/1500]  | LR: 0.046920 | E:  -58.100772 | E_var:     7.9996 | E_err:   0.031554
[2025-10-30 01:29:06] [Iter  319/1575] R0[243/1500]  | LR: 0.046895 | E:  -58.101796 | E_var:     7.7803 | E_err:   0.031889
[2025-10-30 01:29:23] [Iter  320/1575] R0[244/1500]  | LR: 0.046870 | E:  -58.058715 | E_var:     7.7639 | E_err:   0.031378
[2025-10-30 01:29:39] [Iter  321/1575] R0[245/1500]  | LR: 0.046845 | E:  -58.083713 | E_var:     7.8551 | E_err:   0.032269
[2025-10-30 01:29:56] [Iter  322/1575] R0[246/1500]  | LR: 0.046820 | E:  -58.009906 | E_var:     8.1108 | E_err:   0.032779
[2025-10-30 01:30:12] [Iter  323/1575] R0[247/1500]  | LR: 0.046794 | E:  -58.060098 | E_var:     8.5186 | E_err:   0.034198
[2025-10-30 01:30:29] [Iter  324/1575] R0[248/1500]  | LR: 0.046769 | E:  -57.985078 | E_var:     7.3827 | E_err:   0.032081
[2025-10-30 01:30:46] [Iter  325/1575] R0[249/1500]  | LR: 0.046743 | E:  -58.033436 | E_var:     7.3909 | E_err:   0.031072
[2025-10-30 01:31:02] [Iter  326/1575] R0[250/1500]  | LR: 0.046718 | E:  -57.994620 | E_var:     7.3728 | E_err:   0.030857
[2025-10-30 01:31:19] [Iter  327/1575] R0[251/1500]  | LR: 0.046692 | E:  -58.018441 | E_var:     7.9429 | E_err:   0.033173
[2025-10-30 01:31:35] [Iter  328/1575] R0[252/1500]  | LR: 0.046666 | E:  -58.136654 | E_var:     7.7664 | E_err:   0.029720
[2025-10-30 01:31:52] [Iter  329/1575] R0[253/1500]  | LR: 0.046640 | E:  -58.037075 | E_var:     8.3412 | E_err:   0.032795
[2025-10-30 01:32:08] [Iter  330/1575] R0[254/1500]  | LR: 0.046614 | E:  -58.053414 | E_var:     7.7882 | E_err:   0.032245
[2025-10-30 01:32:25] [Iter  331/1575] R0[255/1500]  | LR: 0.046588 | E:  -58.022886 | E_var:     8.0164 | E_err:   0.033628
[2025-10-30 01:32:41] [Iter  332/1575] R0[256/1500]  | LR: 0.046562 | E:  -58.024968 | E_var:     8.6400 | E_err:   0.033648
[2025-10-30 01:32:58] [Iter  333/1575] R0[257/1500]  | LR: 0.046536 | E:  -58.022130 | E_var:     8.2120 | E_err:   0.033860
[2025-10-30 01:33:15] [Iter  334/1575] R0[258/1500]  | LR: 0.046509 | E:  -58.022314 | E_var:     7.8895 | E_err:   0.032007
[2025-10-30 01:33:31] [Iter  335/1575] R0[259/1500]  | LR: 0.046483 | E:  -58.046890 | E_var:     7.2585 | E_err:   0.031152
[2025-10-30 01:33:48] [Iter  336/1575] R0[260/1500]  | LR: 0.046456 | E:  -58.123634 | E_var:     7.2481 | E_err:   0.030624
[2025-10-30 01:34:04] [Iter  337/1575] R0[261/1500]  | LR: 0.046430 | E:  -58.053321 | E_var:     7.8241 | E_err:   0.033976
[2025-10-30 01:34:21] [Iter  338/1575] R0[262/1500]  | LR: 0.046403 | E:  -58.115089 | E_var:     7.4565 | E_err:   0.031221
[2025-10-30 01:34:37] [Iter  339/1575] R0[263/1500]  | LR: 0.046376 | E:  -58.130419 | E_var:     7.8077 | E_err:   0.033237
[2025-10-30 01:34:54] [Iter  340/1575] R0[264/1500]  | LR: 0.046349 | E:  -58.126123 | E_var:     7.5538 | E_err:   0.031415
[2025-10-30 01:35:10] [Iter  341/1575] R0[265/1500]  | LR: 0.046322 | E:  -58.005941 | E_var:     7.1713 | E_err:   0.031070
[2025-10-30 01:35:27] [Iter  342/1575] R0[266/1500]  | LR: 0.046295 | E:  -58.087796 | E_var:     7.5174 | E_err:   0.031124
[2025-10-30 01:35:44] [Iter  343/1575] R0[267/1500]  | LR: 0.046268 | E:  -58.073550 | E_var:     7.3510 | E_err:   0.029761
[2025-10-30 01:36:00] [Iter  344/1575] R0[268/1500]  | LR: 0.046241 | E:  -58.167579 | E_var:     7.2907 | E_err:   0.030637
[2025-10-30 01:36:17] [Iter  345/1575] R0[269/1500]  | LR: 0.046213 | E:  -58.137017 | E_var:     7.8312 | E_err:   0.031445
[2025-10-30 01:36:33] [Iter  346/1575] R0[270/1500]  | LR: 0.046186 | E:  -58.204137 | E_var:     7.5359 | E_err:   0.032722
[2025-10-30 01:36:50] [Iter  347/1575] R0[271/1500]  | LR: 0.046158 | E:  -58.186941 | E_var:     7.4164 | E_err:   0.031439
[2025-10-30 01:37:06] [Iter  348/1575] R0[272/1500]  | LR: 0.046131 | E:  -58.087158 | E_var:     8.0098 | E_err:   0.033449
[2025-10-30 01:37:23] [Iter  349/1575] R0[273/1500]  | LR: 0.046103 | E:  -58.189997 | E_var:     7.6760 | E_err:   0.030335
[2025-10-30 01:37:39] [Iter  350/1575] R0[274/1500]  | LR: 0.046075 | E:  -58.196410 | E_var:     7.3660 | E_err:   0.031586
[2025-10-30 01:37:56] [Iter  351/1575] R0[275/1500]  | LR: 0.046047 | E:  -58.170416 | E_var:     7.3163 | E_err:   0.029994
[2025-10-30 01:38:12] [Iter  352/1575] R0[276/1500]  | LR: 0.046019 | E:  -58.245928 | E_var:     7.7680 | E_err:   0.033093
[2025-10-30 01:38:29] [Iter  353/1575] R0[277/1500]  | LR: 0.045991 | E:  -58.150593 | E_var:     7.1977 | E_err:   0.031848
[2025-10-30 01:38:46] [Iter  354/1575] R0[278/1500]  | LR: 0.045963 | E:  -58.214189 | E_var:     7.4360 | E_err:   0.030807
[2025-10-30 01:39:02] [Iter  355/1575] R0[279/1500]  | LR: 0.045935 | E:  -58.252427 | E_var:     7.4310 | E_err:   0.030477
[2025-10-30 01:39:19] [Iter  356/1575] R0[280/1500]  | LR: 0.045907 | E:  -58.182314 | E_var:     7.3602 | E_err:   0.030567
[2025-10-30 01:39:35] [Iter  357/1575] R0[281/1500]  | LR: 0.045878 | E:  -58.146299 | E_var:     7.0544 | E_err:   0.029318
[2025-10-30 01:39:52] [Iter  358/1575] R0[282/1500]  | LR: 0.045850 | E:  -58.106896 | E_var:     7.0496 | E_err:   0.030648
[2025-10-30 01:40:08] [Iter  359/1575] R0[283/1500]  | LR: 0.045821 | E:  -58.226144 | E_var:     7.4945 | E_err:   0.029922
[2025-10-30 01:40:25] [Iter  360/1575] R0[284/1500]  | LR: 0.045792 | E:  -58.260020 | E_var:     7.2743 | E_err:   0.031045
[2025-10-30 01:40:41] [Iter  361/1575] R0[285/1500]  | LR: 0.045763 | E:  -58.232159 | E_var:     7.0671 | E_err:   0.030638
[2025-10-30 01:40:58] [Iter  362/1575] R0[286/1500]  | LR: 0.045735 | E:  -58.229175 | E_var:     7.0799 | E_err:   0.030746
[2025-10-30 01:41:15] [Iter  363/1575] R0[287/1500]  | LR: 0.045706 | E:  -57.969670 | E_var:     6.8956 | E_err:   0.029422
[2025-10-30 01:41:31] [Iter  364/1575] R0[288/1500]  | LR: 0.045677 | E:  -57.816534 | E_var:     6.7659 | E_err:   0.030670
[2025-10-30 01:41:48] [Iter  365/1575] R0[289/1500]  | LR: 0.045647 | E:  -58.029387 | E_var:     6.7152 | E_err:   0.029236
[2025-10-30 01:42:04] [Iter  366/1575] R0[290/1500]  | LR: 0.045618 | E:  -58.124122 | E_var:     6.7323 | E_err:   0.029952
[2025-10-30 01:42:21] [Iter  367/1575] R0[291/1500]  | LR: 0.045589 | E:  -58.277762 | E_var:     7.0933 | E_err:   0.030448
[2025-10-30 01:42:37] [Iter  368/1575] R0[292/1500]  | LR: 0.045559 | E:  -58.235770 | E_var:     7.1308 | E_err:   0.030210
[2025-10-30 01:42:54] [Iter  369/1575] R0[293/1500]  | LR: 0.045530 | E:  -58.178318 | E_var:     6.6076 | E_err:   0.030222
[2025-10-30 01:43:10] [Iter  370/1575] R0[294/1500]  | LR: 0.045500 | E:  -58.241454 | E_var:     6.9013 | E_err:   0.030033
[2025-10-30 01:43:27] [Iter  371/1575] R0[295/1500]  | LR: 0.045471 | E:  -58.319617 | E_var:     7.3645 | E_err:   0.029817
[2025-10-30 01:43:44] [Iter  372/1575] R0[296/1500]  | LR: 0.045441 | E:  -58.296578 | E_var:     7.2774 | E_err:   0.031470
[2025-10-30 01:44:00] [Iter  373/1575] R0[297/1500]  | LR: 0.045411 | E:  -58.314699 | E_var:     7.9597 | E_err:   0.030504
[2025-10-30 01:44:17] [Iter  374/1575] R0[298/1500]  | LR: 0.045381 | E:  -58.242531 | E_var:     7.4971 | E_err:   0.033496
[2025-10-30 01:44:33] [Iter  375/1575] R0[299/1500]  | LR: 0.045351 | E:  -58.278844 | E_var:     7.2890 | E_err:   0.031134
[2025-10-30 01:44:33] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-30 01:44:50] [Iter  376/1575] R0[300/1500]  | LR: 0.045321 | E:  -58.197794 | E_var:     7.4692 | E_err:   0.030907
[2025-10-30 01:45:06] [Iter  377/1575] R0[301/1500]  | LR: 0.045291 | E:  -58.174148 | E_var:     7.4913 | E_err:   0.032501
[2025-10-30 01:45:23] [Iter  378/1575] R0[302/1500]  | LR: 0.045260 | E:  -58.252140 | E_var:     6.9201 | E_err:   0.029406
[2025-10-30 01:45:40] [Iter  379/1575] R0[303/1500]  | LR: 0.045230 | E:  -58.214756 | E_var:     7.4045 | E_err:   0.030418
[2025-10-30 01:45:56] [Iter  380/1575] R0[304/1500]  | LR: 0.045200 | E:  -58.249752 | E_var:     7.2792 | E_err:   0.031053
[2025-10-30 01:46:13] [Iter  381/1575] R0[305/1500]  | LR: 0.045169 | E:  -58.278404 | E_var:     7.5002 | E_err:   0.030193
[2025-10-30 01:46:29] [Iter  382/1575] R0[306/1500]  | LR: 0.045138 | E:  -58.269574 | E_var:     7.1195 | E_err:   0.031300
[2025-10-30 01:46:46] [Iter  383/1575] R0[307/1500]  | LR: 0.045108 | E:  -58.253289 | E_var:     7.0004 | E_err:   0.030912
[2025-10-30 01:47:02] [Iter  384/1575] R0[308/1500]  | LR: 0.045077 | E:  -58.270081 | E_var:     6.9628 | E_err:   0.030593
[2025-10-30 01:47:19] [Iter  385/1575] R0[309/1500]  | LR: 0.045046 | E:  -58.278306 | E_var:     7.1273 | E_err:   0.029892
[2025-10-30 01:47:35] [Iter  386/1575] R0[310/1500]  | LR: 0.045015 | E:  -58.276243 | E_var:     7.3003 | E_err:   0.031340
[2025-10-30 01:47:52] [Iter  387/1575] R0[311/1500]  | LR: 0.044984 | E:  -58.194569 | E_var:     7.7758 | E_err:   0.031370
[2025-10-30 01:48:08] [Iter  388/1575] R0[312/1500]  | LR: 0.044953 | E:  -58.206154 | E_var:     7.1413 | E_err:   0.031895
[2025-10-30 01:48:25] [Iter  389/1575] R0[313/1500]  | LR: 0.044922 | E:  -58.291139 | E_var:     7.0948 | E_err:   0.030918
[2025-10-30 01:48:42] [Iter  390/1575] R0[314/1500]  | LR: 0.044890 | E:  -58.340591 | E_var:     7.0714 | E_err:   0.029724
[2025-10-30 01:48:58] [Iter  391/1575] R0[315/1500]  | LR: 0.044859 | E:  -58.308402 | E_var:     7.2761 | E_err:   0.031268
[2025-10-30 01:49:15] [Iter  392/1575] R0[316/1500]  | LR: 0.044827 | E:  -58.297635 | E_var:     7.4162 | E_err:   0.030371
[2025-10-30 01:49:31] [Iter  393/1575] R0[317/1500]  | LR: 0.044796 | E:  -58.320892 | E_var:     7.1421 | E_err:   0.029940
[2025-10-30 01:49:48] [Iter  394/1575] R0[318/1500]  | LR: 0.044764 | E:  -58.366476 | E_var:     7.0959 | E_err:   0.032022
[2025-10-30 01:50:04] [Iter  395/1575] R0[319/1500]  | LR: 0.044732 | E:  -58.317147 | E_var:     7.2008 | E_err:   0.029488
[2025-10-30 01:50:21] [Iter  396/1575] R0[320/1500]  | LR: 0.044700 | E:  -58.311457 | E_var:     6.9697 | E_err:   0.029344
[2025-10-30 01:50:37] [Iter  397/1575] R0[321/1500]  | LR: 0.044669 | E:  -58.354171 | E_var:     6.7392 | E_err:   0.028462
[2025-10-30 01:50:54] [Iter  398/1575] R0[322/1500]  | LR: 0.044637 | E:  -58.321126 | E_var:     6.8386 | E_err:   0.029474
[2025-10-30 01:51:10] [Iter  399/1575] R0[323/1500]  | LR: 0.044604 | E:  -58.320249 | E_var:     6.9994 | E_err:   0.030514
[2025-10-30 01:51:27] [Iter  400/1575] R0[324/1500]  | LR: 0.044572 | E:  -58.305904 | E_var:     6.9636 | E_err:   0.029041
[2025-10-30 01:51:44] [Iter  401/1575] R0[325/1500]  | LR: 0.044540 | E:  -58.347122 | E_var:     6.9073 | E_err:   0.028856
[2025-10-30 01:52:00] [Iter  402/1575] R0[326/1500]  | LR: 0.044508 | E:  -58.331008 | E_var:     7.2248 | E_err:   0.030237
[2025-10-30 01:52:17] [Iter  403/1575] R0[327/1500]  | LR: 0.044475 | E:  -58.390870 | E_var:     6.6920 | E_err:   0.029133
[2025-10-30 01:52:33] [Iter  404/1575] R0[328/1500]  | LR: 0.044443 | E:  -58.360865 | E_var:     7.1236 | E_err:   0.029329
[2025-10-30 01:52:50] [Iter  405/1575] R0[329/1500]  | LR: 0.044410 | E:  -58.388565 | E_var:     7.1625 | E_err:   0.031047
[2025-10-30 01:53:06] [Iter  406/1575] R0[330/1500]  | LR: 0.044378 | E:  -58.376909 | E_var:     6.8874 | E_err:   0.030296
[2025-10-30 01:53:23] [Iter  407/1575] R0[331/1500]  | LR: 0.044345 | E:  -58.220254 | E_var:     7.0608 | E_err:   0.030799
[2025-10-30 01:53:39] [Iter  408/1575] R0[332/1500]  | LR: 0.044312 | E:  -58.380889 | E_var:     6.9888 | E_err:   0.029336
[2025-10-30 01:53:56] [Iter  409/1575] R0[333/1500]  | LR: 0.044279 | E:  -58.352716 | E_var:     7.2475 | E_err:   0.030653
[2025-10-30 01:54:13] [Iter  410/1575] R0[334/1500]  | LR: 0.044246 | E:  -58.405008 | E_var:     6.9413 | E_err:   0.028891
[2025-10-30 01:54:29] [Iter  411/1575] R0[335/1500]  | LR: 0.044213 | E:  -58.390755 | E_var:     6.7411 | E_err:   0.028810
[2025-10-30 01:54:46] [Iter  412/1575] R0[336/1500]  | LR: 0.044180 | E:  -58.392311 | E_var:     7.0712 | E_err:   0.030288
[2025-10-30 01:55:02] [Iter  413/1575] R0[337/1500]  | LR: 0.044147 | E:  -58.359976 | E_var:     7.0899 | E_err:   0.030445
[2025-10-30 01:55:19] [Iter  414/1575] R0[338/1500]  | LR: 0.044113 | E:  -58.377577 | E_var:     6.6445 | E_err:   0.030212
[2025-10-30 01:55:35] [Iter  415/1575] R0[339/1500]  | LR: 0.044080 | E:  -58.355596 | E_var:     7.1886 | E_err:   0.031555
[2025-10-30 01:55:52] [Iter  416/1575] R0[340/1500]  | LR: 0.044046 | E:  -58.378092 | E_var:     6.8924 | E_err:   0.029261
[2025-10-30 01:56:08] [Iter  417/1575] R0[341/1500]  | LR: 0.044013 | E:  -58.405943 | E_var:     6.4867 | E_err:   0.028626
[2025-10-30 01:56:25] [Iter  418/1575] R0[342/1500]  | LR: 0.043979 | E:  -58.412387 | E_var:     6.8093 | E_err:   0.030615
[2025-10-30 01:56:41] [Iter  419/1575] R0[343/1500]  | LR: 0.043945 | E:  -58.454078 | E_var:     7.1027 | E_err:   0.029134
[2025-10-30 01:56:58] [Iter  420/1575] R0[344/1500]  | LR: 0.043912 | E:  -58.319843 | E_var:     7.2940 | E_err:   0.029671
[2025-10-30 01:57:15] [Iter  421/1575] R0[345/1500]  | LR: 0.043878 | E:  -58.421966 | E_var:     6.7585 | E_err:   0.029311
[2025-10-30 01:57:31] [Iter  422/1575] R0[346/1500]  | LR: 0.043844 | E:  -58.365739 | E_var:     7.1229 | E_err:   0.029908
[2025-10-30 01:57:48] [Iter  423/1575] R0[347/1500]  | LR: 0.043810 | E:  -58.404657 | E_var:     6.6892 | E_err:   0.029227
[2025-10-30 01:58:04] [Iter  424/1575] R0[348/1500]  | LR: 0.043776 | E:  -58.403881 | E_var:     6.6162 | E_err:   0.028798
[2025-10-30 01:58:21] [Iter  425/1575] R0[349/1500]  | LR: 0.043741 | E:  -58.432982 | E_var:     6.9672 | E_err:   0.029365
[2025-10-30 01:58:37] [Iter  426/1575] R0[350/1500]  | LR: 0.043707 | E:  -58.421923 | E_var:     6.8504 | E_err:   0.029793
[2025-10-30 01:58:54] [Iter  427/1575] R0[351/1500]  | LR: 0.043673 | E:  -58.398030 | E_var:     6.8650 | E_err:   0.029461
[2025-10-30 01:59:10] [Iter  428/1575] R0[352/1500]  | LR: 0.043638 | E:  -58.386889 | E_var:     6.8546 | E_err:   0.029528
[2025-10-30 01:59:27] [Iter  429/1575] R0[353/1500]  | LR: 0.043604 | E:  -58.382306 | E_var:     7.0937 | E_err:   0.028773
[2025-10-30 01:59:44] [Iter  430/1575] R0[354/1500]  | LR: 0.043569 | E:  -58.416000 | E_var:     6.9309 | E_err:   0.029850
[2025-10-30 02:00:00] [Iter  431/1575] R0[355/1500]  | LR: 0.043534 | E:  -58.415397 | E_var:     6.9652 | E_err:   0.031007
[2025-10-30 02:00:17] [Iter  432/1575] R0[356/1500]  | LR: 0.043500 | E:  -58.381471 | E_var:     6.7954 | E_err:   0.029076
[2025-10-30 02:00:33] [Iter  433/1575] R0[357/1500]  | LR: 0.043465 | E:  -58.429263 | E_var:     6.9500 | E_err:   0.029417
[2025-10-30 02:00:50] [Iter  434/1575] R0[358/1500]  | LR: 0.043430 | E:  -58.408913 | E_var:     6.9960 | E_err:   0.030059
[2025-10-30 02:01:06] [Iter  435/1575] R0[359/1500]  | LR: 0.043395 | E:  -58.417022 | E_var:     6.7933 | E_err:   0.029161
[2025-10-30 02:01:23] [Iter  436/1575] R0[360/1500]  | LR: 0.043360 | E:  -58.442736 | E_var:     6.9924 | E_err:   0.028702
[2025-10-30 02:01:39] [Iter  437/1575] R0[361/1500]  | LR: 0.043325 | E:  -58.385020 | E_var:     7.2202 | E_err:   0.031271
[2025-10-30 02:01:56] [Iter  438/1575] R0[362/1500]  | LR: 0.043289 | E:  -58.430952 | E_var:     6.6266 | E_err:   0.028973
[2025-10-30 02:02:12] [Iter  439/1575] R0[363/1500]  | LR: 0.043254 | E:  -58.443715 | E_var:     6.7111 | E_err:   0.027528
[2025-10-30 02:02:29] [Iter  440/1575] R0[364/1500]  | LR: 0.043219 | E:  -58.423090 | E_var:     7.1062 | E_err:   0.031286
[2025-10-30 02:02:46] [Iter  441/1575] R0[365/1500]  | LR: 0.043183 | E:  -58.479571 | E_var:     6.7486 | E_err:   0.030498
[2025-10-30 02:03:02] [Iter  442/1575] R0[366/1500]  | LR: 0.043148 | E:  -58.470237 | E_var:     6.7310 | E_err:   0.028812
[2025-10-30 02:03:19] [Iter  443/1575] R0[367/1500]  | LR: 0.043112 | E:  -58.449734 | E_var:     6.7059 | E_err:   0.027637
[2025-10-30 02:03:35] [Iter  444/1575] R0[368/1500]  | LR: 0.043076 | E:  -58.419076 | E_var:     6.8597 | E_err:   0.029592
[2025-10-30 02:03:52] [Iter  445/1575] R0[369/1500]  | LR: 0.043040 | E:  -58.500532 | E_var:     6.5973 | E_err:   0.028779
[2025-10-30 02:04:08] [Iter  446/1575] R0[370/1500]  | LR: 0.043005 | E:  -58.406451 | E_var:     6.5666 | E_err:   0.028304
[2025-10-30 02:04:25] [Iter  447/1575] R0[371/1500]  | LR: 0.042969 | E:  -58.441173 | E_var:     6.6790 | E_err:   0.029647
[2025-10-30 02:04:41] [Iter  448/1575] R0[372/1500]  | LR: 0.042933 | E:  -58.426269 | E_var:     7.0434 | E_err:   0.029670
[2025-10-30 02:04:58] [Iter  449/1575] R0[373/1500]  | LR: 0.042897 | E:  -58.374186 | E_var:     7.0212 | E_err:   0.030197
[2025-10-30 02:05:14] [Iter  450/1575] R0[374/1500]  | LR: 0.042860 | E:  -58.423743 | E_var:     6.4905 | E_err:   0.028345
[2025-10-30 02:05:31] [Iter  451/1575] R0[375/1500]  | LR: 0.042824 | E:  -58.243025 | E_var:     6.6209 | E_err:   0.031701
[2025-10-30 02:05:48] [Iter  452/1575] R0[376/1500]  | LR: 0.042788 | E:  -58.298965 | E_var:     6.5012 | E_err:   0.027374
[2025-10-30 02:06:04] [Iter  453/1575] R0[377/1500]  | LR: 0.042751 | E:  -58.361837 | E_var:     6.8359 | E_err:   0.029939
[2025-10-30 02:06:21] [Iter  454/1575] R0[378/1500]  | LR: 0.042715 | E:  -58.443150 | E_var:     6.9639 | E_err:   0.029452
[2025-10-30 02:06:37] [Iter  455/1575] R0[379/1500]  | LR: 0.042678 | E:  -58.391642 | E_var:     6.7377 | E_err:   0.030712
[2025-10-30 02:06:54] [Iter  456/1575] R0[380/1500]  | LR: 0.042642 | E:  -58.436833 | E_var:     6.5734 | E_err:   0.030325
[2025-10-30 02:07:10] [Iter  457/1575] R0[381/1500]  | LR: 0.042605 | E:  -58.463971 | E_var:     6.5655 | E_err:   0.029250
[2025-10-30 02:07:27] [Iter  458/1575] R0[382/1500]  | LR: 0.042568 | E:  -58.417493 | E_var:     6.9482 | E_err:   0.030769
[2025-10-30 02:07:43] [Iter  459/1575] R0[383/1500]  | LR: 0.042531 | E:  -58.460103 | E_var:     6.9045 | E_err:   0.028359
[2025-10-30 02:08:00] [Iter  460/1575] R0[384/1500]  | LR: 0.042495 | E:  -58.441120 | E_var:     7.0248 | E_err:   0.030407
[2025-10-30 02:08:16] [Iter  461/1575] R0[385/1500]  | LR: 0.042458 | E:  -58.450724 | E_var:     6.8293 | E_err:   0.028687
[2025-10-30 02:08:33] [Iter  462/1575] R0[386/1500]  | LR: 0.042420 | E:  -58.466403 | E_var:     6.5818 | E_err:   0.029394
[2025-10-30 02:08:50] [Iter  463/1575] R0[387/1500]  | LR: 0.042383 | E:  -58.531160 | E_var:     6.6827 | E_err:   0.028198
[2025-10-30 02:09:06] [Iter  464/1575] R0[388/1500]  | LR: 0.042346 | E:  -58.467351 | E_var:     6.5936 | E_err:   0.028908
[2025-10-30 02:09:23] [Iter  465/1575] R0[389/1500]  | LR: 0.042309 | E:  -58.492004 | E_var:     6.4852 | E_err:   0.029500
[2025-10-30 02:09:39] [Iter  466/1575] R0[390/1500]  | LR: 0.042271 | E:  -58.521263 | E_var:     6.4351 | E_err:   0.028293
[2025-10-30 02:09:56] [Iter  467/1575] R0[391/1500]  | LR: 0.042234 | E:  -58.537004 | E_var:     6.6171 | E_err:   0.029664
[2025-10-30 02:10:12] [Iter  468/1575] R0[392/1500]  | LR: 0.042196 | E:  -58.480154 | E_var:     6.6162 | E_err:   0.027843
[2025-10-30 02:10:29] [Iter  469/1575] R0[393/1500]  | LR: 0.042159 | E:  -58.492168 | E_var:     7.3549 | E_err:   0.030088
[2025-10-30 02:10:45] [Iter  470/1575] R0[394/1500]  | LR: 0.042121 | E:  -58.548755 | E_var:     6.4776 | E_err:   0.028700
[2025-10-30 02:11:02] [Iter  471/1575] R0[395/1500]  | LR: 0.042083 | E:  -58.512554 | E_var:     6.5693 | E_err:   0.028994
[2025-10-30 02:11:18] [Iter  472/1575] R0[396/1500]  | LR: 0.042046 | E:  -58.520419 | E_var:     6.6654 | E_err:   0.029251
[2025-10-30 02:11:35] [Iter  473/1575] R0[397/1500]  | LR: 0.042008 | E:  -58.530798 | E_var:     7.0532 | E_err:   0.030101
[2025-10-30 02:11:52] [Iter  474/1575] R0[398/1500]  | LR: 0.041970 | E:  -58.543006 | E_var:     6.5082 | E_err:   0.028622
[2025-10-30 02:12:08] [Iter  475/1575] R0[399/1500]  | LR: 0.041932 | E:  -58.496673 | E_var:     6.5237 | E_err:   0.029283
[2025-10-30 02:12:25] [Iter  476/1575] R0[400/1500]  | LR: 0.041894 | E:  -58.549716 | E_var:     6.5760 | E_err:   0.029474
[2025-10-30 02:12:41] [Iter  477/1575] R0[401/1500]  | LR: 0.041856 | E:  -58.535768 | E_var:     6.7182 | E_err:   0.028669
[2025-10-30 02:12:58] [Iter  478/1575] R0[402/1500]  | LR: 0.041817 | E:  -58.496052 | E_var:     6.6894 | E_err:   0.029745
[2025-10-30 02:13:14] [Iter  479/1575] R0[403/1500]  | LR: 0.041779 | E:  -58.504437 | E_var:     6.2206 | E_err:   0.028002
[2025-10-30 02:13:31] [Iter  480/1575] R0[404/1500]  | LR: 0.041741 | E:  -58.501930 | E_var:     6.0771 | E_err:   0.027704
[2025-10-30 02:13:47] [Iter  481/1575] R0[405/1500]  | LR: 0.041702 | E:  -58.528532 | E_var:     6.2148 | E_err:   0.026592
[2025-10-30 02:14:04] [Iter  482/1575] R0[406/1500]  | LR: 0.041664 | E:  -58.545606 | E_var:     6.2206 | E_err:   0.028443
[2025-10-30 02:14:21] [Iter  483/1575] R0[407/1500]  | LR: 0.041625 | E:  -58.502647 | E_var:     6.3896 | E_err:   0.027640
[2025-10-30 02:14:37] [Iter  484/1575] R0[408/1500]  | LR: 0.041586 | E:  -58.466993 | E_var:     6.3720 | E_err:   0.027203
[2025-10-30 02:14:54] [Iter  485/1575] R0[409/1500]  | LR: 0.041548 | E:  -58.438966 | E_var:     6.5874 | E_err:   0.028341
[2025-10-30 02:15:10] [Iter  486/1575] R0[410/1500]  | LR: 0.041509 | E:  -58.503940 | E_var:     6.5970 | E_err:   0.031229
[2025-10-30 02:15:27] [Iter  487/1575] R0[411/1500]  | LR: 0.041470 | E:  -58.509176 | E_var:     6.3627 | E_err:   0.029355
[2025-10-30 02:15:43] [Iter  488/1575] R0[412/1500]  | LR: 0.041431 | E:  -58.523280 | E_var:     6.8211 | E_err:   0.031155
[2025-10-30 02:16:00] [Iter  489/1575] R0[413/1500]  | LR: 0.041392 | E:  -58.565896 | E_var:     6.7259 | E_err:   0.028163
[2025-10-30 02:16:16] [Iter  490/1575] R0[414/1500]  | LR: 0.041353 | E:  -58.470904 | E_var:     6.2994 | E_err:   0.028251
[2025-10-30 02:16:33] [Iter  491/1575] R0[415/1500]  | LR: 0.041314 | E:  -58.534428 | E_var:     6.5235 | E_err:   0.030060
[2025-10-30 02:16:49] [Iter  492/1575] R0[416/1500]  | LR: 0.041274 | E:  -58.533870 | E_var:     6.0994 | E_err:   0.028078
[2025-10-30 02:17:06] [Iter  493/1575] R0[417/1500]  | LR: 0.041235 | E:  -58.541562 | E_var:     6.5157 | E_err:   0.028423
[2025-10-30 02:17:23] [Iter  494/1575] R0[418/1500]  | LR: 0.041196 | E:  -58.577198 | E_var:     6.9981 | E_err:   0.028385
[2025-10-30 02:17:39] [Iter  495/1575] R0[419/1500]  | LR: 0.041156 | E:  -58.511890 | E_var:     6.5017 | E_err:   0.027545
[2025-10-30 02:17:56] [Iter  496/1575] R0[420/1500]  | LR: 0.041117 | E:  -58.558262 | E_var:     6.4123 | E_err:   0.028431
[2025-10-30 02:18:12] [Iter  497/1575] R0[421/1500]  | LR: 0.041077 | E:  -58.560533 | E_var:     6.3129 | E_err:   0.029414
[2025-10-30 02:18:29] [Iter  498/1575] R0[422/1500]  | LR: 0.041038 | E:  -58.547386 | E_var:     6.4182 | E_err:   0.028491
[2025-10-30 02:18:45] [Iter  499/1575] R0[423/1500]  | LR: 0.040998 | E:  -58.560214 | E_var:     6.1760 | E_err:   0.029255
[2025-10-30 02:19:02] [Iter  500/1575] R0[424/1500]  | LR: 0.040958 | E:  -58.530143 | E_var:     6.4127 | E_err:   0.028616
[2025-10-30 02:19:18] [Iter  501/1575] R0[425/1500]  | LR: 0.040918 | E:  -58.552447 | E_var:     6.5906 | E_err:   0.028607
[2025-10-30 02:19:35] [Iter  502/1575] R0[426/1500]  | LR: 0.040878 | E:  -58.543807 | E_var:     6.2082 | E_err:   0.028183
[2025-10-30 02:19:52] [Iter  503/1575] R0[427/1500]  | LR: 0.040838 | E:  -58.469164 | E_var:     6.4683 | E_err:   0.029143
[2025-10-30 02:20:08] [Iter  504/1575] R0[428/1500]  | LR: 0.040798 | E:  -58.532802 | E_var:     6.5875 | E_err:   0.027477
[2025-10-30 02:20:25] [Iter  505/1575] R0[429/1500]  | LR: 0.040758 | E:  -58.545493 | E_var:     6.4393 | E_err:   0.029582
[2025-10-30 02:20:41] [Iter  506/1575] R0[430/1500]  | LR: 0.040718 | E:  -58.516204 | E_var:     6.6762 | E_err:   0.029672
[2025-10-30 02:20:58] [Iter  507/1575] R0[431/1500]  | LR: 0.040678 | E:  -58.549550 | E_var:     6.5442 | E_err:   0.029807
[2025-10-30 02:21:14] [Iter  508/1575] R0[432/1500]  | LR: 0.040638 | E:  -58.508577 | E_var:     7.0429 | E_err:   0.031270
[2025-10-30 02:21:31] [Iter  509/1575] R0[433/1500]  | LR: 0.040597 | E:  -58.495940 | E_var:     6.9163 | E_err:   0.029687
[2025-10-30 02:21:47] [Iter  510/1575] R0[434/1500]  | LR: 0.040557 | E:  -58.523771 | E_var:     6.3629 | E_err:   0.028632
[2025-10-30 02:22:04] [Iter  511/1575] R0[435/1500]  | LR: 0.040516 | E:  -58.472109 | E_var:     6.4227 | E_err:   0.027054
[2025-10-30 02:22:21] [Iter  512/1575] R0[436/1500]  | LR: 0.040476 | E:  -58.504045 | E_var:     6.4606 | E_err:   0.029431
[2025-10-30 02:22:37] [Iter  513/1575] R0[437/1500]  | LR: 0.040435 | E:  -58.533124 | E_var:     6.5126 | E_err:   0.030414
[2025-10-30 02:22:54] [Iter  514/1575] R0[438/1500]  | LR: 0.040394 | E:  -58.528542 | E_var:     6.6163 | E_err:   0.029770
[2025-10-30 02:23:10] [Iter  515/1575] R0[439/1500]  | LR: 0.040354 | E:  -58.531152 | E_var:     6.3409 | E_err:   0.027733
[2025-10-30 02:23:27] [Iter  516/1575] R0[440/1500]  | LR: 0.040313 | E:  -58.534124 | E_var:     6.3393 | E_err:   0.028939
[2025-10-30 02:23:43] [Iter  517/1575] R0[441/1500]  | LR: 0.040272 | E:  -58.557903 | E_var:     6.3581 | E_err:   0.028522
[2025-10-30 02:24:00] [Iter  518/1575] R0[442/1500]  | LR: 0.040231 | E:  -58.605691 | E_var:     6.3332 | E_err:   0.029212
[2025-10-30 02:24:16] [Iter  519/1575] R0[443/1500]  | LR: 0.040190 | E:  -58.553299 | E_var:     6.5371 | E_err:   0.029231
[2025-10-30 02:24:33] [Iter  520/1575] R0[444/1500]  | LR: 0.040149 | E:  -58.600738 | E_var:     5.9716 | E_err:   0.027216
[2025-10-30 02:24:50] [Iter  521/1575] R0[445/1500]  | LR: 0.040108 | E:  -58.577452 | E_var:     5.9441 | E_err:   0.026843
[2025-10-30 02:25:06] [Iter  522/1575] R0[446/1500]  | LR: 0.040066 | E:  -58.597619 | E_var:     6.2339 | E_err:   0.028820
[2025-10-30 02:25:23] [Iter  523/1575] R0[447/1500]  | LR: 0.040025 | E:  -58.582003 | E_var:     6.5551 | E_err:   0.028873
[2025-10-30 02:25:39] [Iter  524/1575] R0[448/1500]  | LR: 0.039984 | E:  -58.529901 | E_var:     6.5807 | E_err:   0.027634
[2025-10-30 02:25:56] [Iter  525/1575] R0[449/1500]  | LR: 0.039942 | E:  -58.578915 | E_var:     6.0523 | E_err:   0.028309
[2025-10-30 02:25:56] ✓ Checkpoint saved: checkpoint_iter_000450.pkl
[2025-10-30 02:26:12] [Iter  526/1575] R0[450/1500]  | LR: 0.039901 | E:  -58.559220 | E_var:     6.2171 | E_err:   0.028583
[2025-10-30 02:26:29] [Iter  527/1575] R0[451/1500]  | LR: 0.039859 | E:  -58.503140 | E_var:     5.9012 | E_err:   0.027469
[2025-10-30 02:26:46] [Iter  528/1575] R0[452/1500]  | LR: 0.039818 | E:  -58.584381 | E_var:     6.0459 | E_err:   0.028303
[2025-10-30 02:27:02] [Iter  529/1575] R0[453/1500]  | LR: 0.039776 | E:  -58.592986 | E_var:     5.9368 | E_err:   0.027728
[2025-10-30 02:27:19] [Iter  530/1575] R0[454/1500]  | LR: 0.039734 | E:  -58.541124 | E_var:     6.3402 | E_err:   0.028503
[2025-10-30 02:27:35] [Iter  531/1575] R0[455/1500]  | LR: 0.039692 | E:  -58.518659 | E_var:     6.2486 | E_err:   0.028098
[2025-10-30 02:27:52] [Iter  532/1575] R0[456/1500]  | LR: 0.039651 | E:  -58.518700 | E_var:     6.3925 | E_err:   0.028838
[2025-10-30 02:28:08] [Iter  533/1575] R0[457/1500]  | LR: 0.039609 | E:  -58.600539 | E_var:     6.4589 | E_err:   0.028663
[2025-10-30 02:28:25] [Iter  534/1575] R0[458/1500]  | LR: 0.039567 | E:  -58.561423 | E_var:     6.3920 | E_err:   0.028263
[2025-10-30 02:28:41] [Iter  535/1575] R0[459/1500]  | LR: 0.039525 | E:  -58.527531 | E_var:     6.3947 | E_err:   0.028234
[2025-10-30 02:28:58] [Iter  536/1575] R0[460/1500]  | LR: 0.039482 | E:  -58.556734 | E_var:     6.1006 | E_err:   0.028640
[2025-10-30 02:29:14] [Iter  537/1575] R0[461/1500]  | LR: 0.039440 | E:  -58.518287 | E_var:     6.5697 | E_err:   0.029658
[2025-10-30 02:29:31] [Iter  538/1575] R0[462/1500]  | LR: 0.039398 | E:  -58.573520 | E_var:     6.4727 | E_err:   0.029626
[2025-10-30 02:29:48] [Iter  539/1575] R0[463/1500]  | LR: 0.039356 | E:  -58.548269 | E_var:     6.1554 | E_err:   0.028681
[2025-10-30 02:30:04] [Iter  540/1575] R0[464/1500]  | LR: 0.039313 | E:  -58.624928 | E_var:     6.3747 | E_err:   0.027765
[2025-10-30 02:30:21] [Iter  541/1575] R0[465/1500]  | LR: 0.039271 | E:  -58.548746 | E_var:     6.2237 | E_err:   0.027437
[2025-10-30 02:30:37] [Iter  542/1575] R0[466/1500]  | LR: 0.039229 | E:  -58.543631 | E_var:     6.6108 | E_err:   0.029144
[2025-10-30 02:30:54] [Iter  543/1575] R0[467/1500]  | LR: 0.039186 | E:  -58.593347 | E_var:     6.2845 | E_err:   0.028392
[2025-10-30 02:31:10] [Iter  544/1575] R0[468/1500]  | LR: 0.039143 | E:  -58.566042 | E_var:     6.3473 | E_err:   0.028619
[2025-10-30 02:31:27] [Iter  545/1575] R0[469/1500]  | LR: 0.039101 | E:  -58.587612 | E_var:     6.4324 | E_err:   0.027225
[2025-10-30 02:31:43] [Iter  546/1575] R0[470/1500]  | LR: 0.039058 | E:  -58.589615 | E_var:     6.2689 | E_err:   0.027643
[2025-10-30 02:32:00] [Iter  547/1575] R0[471/1500]  | LR: 0.039015 | E:  -58.594250 | E_var:     6.5625 | E_err:   0.029174
[2025-10-30 02:32:17] [Iter  548/1575] R0[472/1500]  | LR: 0.038972 | E:  -58.541329 | E_var:     6.1254 | E_err:   0.026488
[2025-10-30 02:32:33] [Iter  549/1575] R0[473/1500]  | LR: 0.038930 | E:  -58.612255 | E_var:     6.4724 | E_err:   0.030421
[2025-10-30 02:32:50] [Iter  550/1575] R0[474/1500]  | LR: 0.038887 | E:  -58.652227 | E_var:     6.3376 | E_err:   0.028143
[2025-10-30 02:33:06] [Iter  551/1575] R0[475/1500]  | LR: 0.038844 | E:  -58.610624 | E_var:     6.2938 | E_err:   0.028631
[2025-10-30 02:33:23] [Iter  552/1575] R0[476/1500]  | LR: 0.038801 | E:  -58.628734 | E_var:     6.0551 | E_err:   0.027631
[2025-10-30 02:33:39] [Iter  553/1575] R0[477/1500]  | LR: 0.038757 | E:  -58.605978 | E_var:     6.5833 | E_err:   0.027517
[2025-10-30 02:33:56] [Iter  554/1575] R0[478/1500]  | LR: 0.038714 | E:  -58.652029 | E_var:     6.1674 | E_err:   0.027264
[2025-10-30 02:34:12] [Iter  555/1575] R0[479/1500]  | LR: 0.038671 | E:  -58.691698 | E_var:     5.9938 | E_err:   0.026943
[2025-10-30 02:34:29] [Iter  556/1575] R0[480/1500]  | LR: 0.038628 | E:  -58.588917 | E_var:     5.7661 | E_err:   0.026570
[2025-10-30 02:34:45] [Iter  557/1575] R0[481/1500]  | LR: 0.038584 | E:  -58.647274 | E_var:     6.1360 | E_err:   0.027497
[2025-10-30 02:35:02] [Iter  558/1575] R0[482/1500]  | LR: 0.038541 | E:  -58.571795 | E_var:     6.0464 | E_err:   0.027449
[2025-10-30 02:35:19] [Iter  559/1575] R0[483/1500]  | LR: 0.038498 | E:  -58.628600 | E_var:     6.2152 | E_err:   0.027567
[2025-10-30 02:35:35] [Iter  560/1575] R0[484/1500]  | LR: 0.038454 | E:  -58.618124 | E_var:     6.2482 | E_err:   0.027671
[2025-10-30 02:35:52] [Iter  561/1575] R0[485/1500]  | LR: 0.038410 | E:  -58.631909 | E_var:     6.2138 | E_err:   0.027266
[2025-10-30 02:36:08] [Iter  562/1575] R0[486/1500]  | LR: 0.038367 | E:  -58.586327 | E_var:     6.6103 | E_err:   0.028505
[2025-10-30 02:36:25] [Iter  563/1575] R0[487/1500]  | LR: 0.038323 | E:  -58.640922 | E_var:     5.9114 | E_err:   0.028069
[2025-10-30 02:36:41] [Iter  564/1575] R0[488/1500]  | LR: 0.038279 | E:  -58.627581 | E_var:     6.2816 | E_err:   0.028061
[2025-10-30 02:36:58] [Iter  565/1575] R0[489/1500]  | LR: 0.038236 | E:  -58.647826 | E_var:     6.1654 | E_err:   0.028620
[2025-10-30 02:37:14] [Iter  566/1575] R0[490/1500]  | LR: 0.038192 | E:  -58.580038 | E_var:     6.0493 | E_err:   0.025788
[2025-10-30 02:37:31] [Iter  567/1575] R0[491/1500]  | LR: 0.038148 | E:  -58.601851 | E_var:     5.8846 | E_err:   0.026235
[2025-10-30 02:37:48] [Iter  568/1575] R0[492/1500]  | LR: 0.038104 | E:  -58.606851 | E_var:     5.9861 | E_err:   0.027325
[2025-10-30 02:38:04] [Iter  569/1575] R0[493/1500]  | LR: 0.038060 | E:  -58.674168 | E_var:     5.9694 | E_err:   0.026865
[2025-10-30 02:38:21] [Iter  570/1575] R0[494/1500]  | LR: 0.038016 | E:  -58.614065 | E_var:     6.0364 | E_err:   0.027159
[2025-10-30 02:38:37] [Iter  571/1575] R0[495/1500]  | LR: 0.037972 | E:  -58.621541 | E_var:     5.8520 | E_err:   0.028080
[2025-10-30 02:38:54] [Iter  572/1575] R0[496/1500]  | LR: 0.037927 | E:  -58.609513 | E_var:     5.8642 | E_err:   0.027939
[2025-10-30 02:39:10] [Iter  573/1575] R0[497/1500]  | LR: 0.037883 | E:  -58.580574 | E_var:     6.0569 | E_err:   0.027694
[2025-10-30 02:39:27] [Iter  574/1575] R0[498/1500]  | LR: 0.037839 | E:  -58.635805 | E_var:     6.0726 | E_err:   0.029278
[2025-10-30 02:39:43] [Iter  575/1575] R0[499/1500]  | LR: 0.037794 | E:  -58.620748 | E_var:     5.8652 | E_err:   0.027904
[2025-10-30 02:40:00] [Iter  576/1575] R0[500/1500]  | LR: 0.037750 | E:  -58.631268 | E_var:     6.5296 | E_err:   0.028783
[2025-10-30 02:40:17] [Iter  577/1575] R0[501/1500]  | LR: 0.037706 | E:  -58.624308 | E_var:     6.0582 | E_err:   0.027459
[2025-10-30 02:40:33] [Iter  578/1575] R0[502/1500]  | LR: 0.037661 | E:  -58.632822 | E_var:     6.0151 | E_err:   0.026992
[2025-10-30 02:40:50] [Iter  579/1575] R0[503/1500]  | LR: 0.037616 | E:  -58.631381 | E_var:     6.2798 | E_err:   0.028768
[2025-10-30 02:41:06] [Iter  580/1575] R0[504/1500]  | LR: 0.037572 | E:  -58.609405 | E_var:     5.7534 | E_err:   0.026663
[2025-10-30 02:41:23] [Iter  581/1575] R0[505/1500]  | LR: 0.037527 | E:  -58.601222 | E_var:     6.0553 | E_err:   0.027498
[2025-10-30 02:41:39] [Iter  582/1575] R0[506/1500]  | LR: 0.037482 | E:  -58.595616 | E_var:     5.9132 | E_err:   0.027337
[2025-10-30 02:41:56] [Iter  583/1575] R0[507/1500]  | LR: 0.037438 | E:  -58.629387 | E_var:     6.0262 | E_err:   0.027660
[2025-10-30 02:42:13] [Iter  584/1575] R0[508/1500]  | LR: 0.037393 | E:  -58.670071 | E_var:     6.0945 | E_err:   0.028076
[2025-10-30 02:42:29] [Iter  585/1575] R0[509/1500]  | LR: 0.037348 | E:  -58.618090 | E_var:     5.7598 | E_err:   0.026168
[2025-10-30 02:42:46] [Iter  586/1575] R0[510/1500]  | LR: 0.037303 | E:  -58.717449 | E_var:     6.1122 | E_err:   0.027595
[2025-10-30 02:43:02] [Iter  587/1575] R0[511/1500]  | LR: 0.037258 | E:  -58.715315 | E_var:     6.1647 | E_err:   0.028344
[2025-10-30 02:43:19] [Iter  588/1575] R0[512/1500]  | LR: 0.037213 | E:  -58.678738 | E_var:     5.9805 | E_err:   0.028778
[2025-10-30 02:43:35] [Iter  589/1575] R0[513/1500]  | LR: 0.037168 | E:  -58.698834 | E_var:     5.9534 | E_err:   0.026662
[2025-10-30 02:43:52] [Iter  590/1575] R0[514/1500]  | LR: 0.037123 | E:  -58.630840 | E_var:     5.6168 | E_err:   0.028209
[2025-10-30 02:44:08] [Iter  591/1575] R0[515/1500]  | LR: 0.037077 | E:  -58.605655 | E_var:     5.9000 | E_err:   0.026740
[2025-10-30 02:44:25] [Iter  592/1575] R0[516/1500]  | LR: 0.037032 | E:  -58.655748 | E_var:     5.9413 | E_err:   0.027894
[2025-10-30 02:44:41] [Iter  593/1575] R0[517/1500]  | LR: 0.036987 | E:  -58.668287 | E_var:     6.2556 | E_err:   0.027949
[2025-10-30 02:44:58] [Iter  594/1575] R0[518/1500]  | LR: 0.036942 | E:  -58.703383 | E_var:     6.1338 | E_err:   0.028635
[2025-10-30 02:45:15] [Iter  595/1575] R0[519/1500]  | LR: 0.036896 | E:  -58.624168 | E_var:     6.0209 | E_err:   0.027458
[2025-10-30 02:45:31] [Iter  596/1575] R0[520/1500]  | LR: 0.036851 | E:  -58.669228 | E_var:     5.8230 | E_err:   0.027448
[2025-10-30 02:45:48] [Iter  597/1575] R0[521/1500]  | LR: 0.036805 | E:  -58.608684 | E_var:     6.0869 | E_err:   0.027510
[2025-10-30 02:46:04] [Iter  598/1575] R0[522/1500]  | LR: 0.036760 | E:  -58.685687 | E_var:     6.0838 | E_err:   0.029266
[2025-10-30 02:46:21] [Iter  599/1575] R0[523/1500]  | LR: 0.036714 | E:  -58.605798 | E_var:     6.3587 | E_err:   0.028393
[2025-10-30 02:46:37] [Iter  600/1575] R0[524/1500]  | LR: 0.036668 | E:  -58.657203 | E_var:     5.8949 | E_err:   0.027382
[2025-10-30 02:46:54] [Iter  601/1575] R0[525/1500]  | LR: 0.036623 | E:  -58.663468 | E_var:     5.8771 | E_err:   0.027861
[2025-10-30 02:47:10] [Iter  602/1575] R0[526/1500]  | LR: 0.036577 | E:  -58.647230 | E_var:     6.0854 | E_err:   0.026899
[2025-10-30 02:47:27] [Iter  603/1575] R0[527/1500]  | LR: 0.036531 | E:  -58.688396 | E_var:     6.3725 | E_err:   0.028174
[2025-10-30 02:47:44] [Iter  604/1575] R0[528/1500]  | LR: 0.036485 | E:  -58.674650 | E_var:     5.9507 | E_err:   0.026699
[2025-10-30 02:48:00] [Iter  605/1575] R0[529/1500]  | LR: 0.036439 | E:  -58.677308 | E_var:     5.8984 | E_err:   0.028441
[2025-10-30 02:48:17] [Iter  606/1575] R0[530/1500]  | LR: 0.036394 | E:  -58.651450 | E_var:     6.0671 | E_err:   0.028082
[2025-10-30 02:48:33] [Iter  607/1575] R0[531/1500]  | LR: 0.036348 | E:  -58.632488 | E_var:     5.7626 | E_err:   0.026384
[2025-10-30 02:48:50] [Iter  608/1575] R0[532/1500]  | LR: 0.036302 | E:  -58.603168 | E_var:     5.7886 | E_err:   0.026707
[2025-10-30 02:49:06] [Iter  609/1575] R0[533/1500]  | LR: 0.036255 | E:  -58.693336 | E_var:     5.5985 | E_err:   0.026382
[2025-10-30 02:49:23] [Iter  610/1575] R0[534/1500]  | LR: 0.036209 | E:  -58.699897 | E_var:     5.6487 | E_err:   0.026133
[2025-10-30 02:49:39] [Iter  611/1575] R0[535/1500]  | LR: 0.036163 | E:  -58.729671 | E_var:     5.9018 | E_err:   0.027687
[2025-10-30 02:49:56] [Iter  612/1575] R0[536/1500]  | LR: 0.036117 | E:  -58.698772 | E_var:     5.7765 | E_err:   0.028231
[2025-10-30 02:50:13] [Iter  613/1575] R0[537/1500]  | LR: 0.036071 | E:  -58.629807 | E_var:     5.8214 | E_err:   0.027415
[2025-10-30 02:50:29] [Iter  614/1575] R0[538/1500]  | LR: 0.036024 | E:  -58.675643 | E_var:     6.1590 | E_err:   0.028352
[2025-10-30 02:50:46] [Iter  615/1575] R0[539/1500]  | LR: 0.035978 | E:  -58.681788 | E_var:     6.2038 | E_err:   0.028161
[2025-10-30 02:51:02] [Iter  616/1575] R0[540/1500]  | LR: 0.035932 | E:  -58.689799 | E_var:     6.3850 | E_err:   0.028475
[2025-10-30 02:51:19] [Iter  617/1575] R0[541/1500]  | LR: 0.035885 | E:  -58.654150 | E_var:     6.0263 | E_err:   0.028888
[2025-10-30 02:51:35] [Iter  618/1575] R0[542/1500]  | LR: 0.035839 | E:  -58.691645 | E_var:     6.0796 | E_err:   0.028345
[2025-10-30 02:51:52] [Iter  619/1575] R0[543/1500]  | LR: 0.035792 | E:  -58.693920 | E_var:     6.3198 | E_err:   0.028021
[2025-10-30 02:52:08] [Iter  620/1575] R0[544/1500]  | LR: 0.035746 | E:  -58.649569 | E_var:     5.7181 | E_err:   0.025820
[2025-10-30 02:52:25] [Iter  621/1575] R0[545/1500]  | LR: 0.035699 | E:  -58.724272 | E_var:     5.8039 | E_err:   0.028208
[2025-10-30 02:52:41] [Iter  622/1575] R0[546/1500]  | LR: 0.035652 | E:  -58.653994 | E_var:     5.7762 | E_err:   0.026508
[2025-10-30 02:52:58] [Iter  623/1575] R0[547/1500]  | LR: 0.035605 | E:  -58.707375 | E_var:     5.9194 | E_err:   0.027086
[2025-10-30 02:53:15] [Iter  624/1575] R0[548/1500]  | LR: 0.035559 | E:  -58.732422 | E_var:     6.0028 | E_err:   0.027578
[2025-10-30 02:53:31] [Iter  625/1575] R0[549/1500]  | LR: 0.035512 | E:  -58.673038 | E_var:     6.1899 | E_err:   0.027559
[2025-10-30 02:53:48] [Iter  626/1575] R0[550/1500]  | LR: 0.035465 | E:  -58.715862 | E_var:     6.0052 | E_err:   0.027524
[2025-10-30 02:54:04] [Iter  627/1575] R0[551/1500]  | LR: 0.035418 | E:  -58.710825 | E_var:     6.0757 | E_err:   0.028591
[2025-10-30 02:54:21] [Iter  628/1575] R0[552/1500]  | LR: 0.035371 | E:  -58.680048 | E_var:     5.9149 | E_err:   0.026302
[2025-10-30 02:54:37] [Iter  629/1575] R0[553/1500]  | LR: 0.035324 | E:  -58.712706 | E_var:     5.8811 | E_err:   0.027580
[2025-10-30 02:54:54] [Iter  630/1575] R0[554/1500]  | LR: 0.035277 | E:  -58.738917 | E_var:     5.7613 | E_err:   0.027076
[2025-10-30 02:55:10] [Iter  631/1575] R0[555/1500]  | LR: 0.035230 | E:  -58.667352 | E_var:     5.6103 | E_err:   0.026552
[2025-10-30 02:55:27] [Iter  632/1575] R0[556/1500]  | LR: 0.035183 | E:  -58.670593 | E_var:     5.9375 | E_err:   0.027683
[2025-10-30 02:55:44] [Iter  633/1575] R0[557/1500]  | LR: 0.035136 | E:  -58.713124 | E_var:     6.1229 | E_err:   0.027931
[2025-10-30 02:56:00] [Iter  634/1575] R0[558/1500]  | LR: 0.035089 | E:  -58.678641 | E_var:     6.0538 | E_err:   0.025492
[2025-10-30 02:56:17] [Iter  635/1575] R0[559/1500]  | LR: 0.035041 | E:  -58.721346 | E_var:     6.1097 | E_err:   0.028438
[2025-10-30 02:56:34] [Iter  636/1575] R0[560/1500]  | LR: 0.034994 | E:  -58.708035 | E_var:     5.8828 | E_err:   0.025797
[2025-10-30 02:56:50] [Iter  637/1575] R0[561/1500]  | LR: 0.034947 | E:  -58.685538 | E_var:     5.9141 | E_err:   0.026543
[2025-10-30 02:57:07] [Iter  638/1575] R0[562/1500]  | LR: 0.034899 | E:  -58.733252 | E_var:     5.7599 | E_err:   0.025715
[2025-10-30 02:57:23] [Iter  639/1575] R0[563/1500]  | LR: 0.034852 | E:  -58.696523 | E_var:     5.7595 | E_err:   0.026563
[2025-10-30 02:57:40] [Iter  640/1575] R0[564/1500]  | LR: 0.034805 | E:  -58.759081 | E_var:     5.8214 | E_err:   0.027321
[2025-10-30 02:57:56] [Iter  641/1575] R0[565/1500]  | LR: 0.034757 | E:  -58.724107 | E_var:     5.6370 | E_err:   0.026948
[2025-10-30 02:58:13] [Iter  642/1575] R0[566/1500]  | LR: 0.034710 | E:  -58.709078 | E_var:     5.8013 | E_err:   0.027210
[2025-10-30 02:58:29] [Iter  643/1575] R0[567/1500]  | LR: 0.034662 | E:  -58.665844 | E_var:     5.6619 | E_err:   0.027462
[2025-10-30 02:58:46] [Iter  644/1575] R0[568/1500]  | LR: 0.034614 | E:  -58.664131 | E_var:     5.6987 | E_err:   0.025885
[2025-10-30 02:59:02] [Iter  645/1575] R0[569/1500]  | LR: 0.034567 | E:  -58.705793 | E_var:     5.7232 | E_err:   0.026910
[2025-10-30 02:59:19] [Iter  646/1575] R0[570/1500]  | LR: 0.034519 | E:  -58.686159 | E_var:     5.5831 | E_err:   0.026103
[2025-10-30 02:59:35] [Iter  647/1575] R0[571/1500]  | LR: 0.034471 | E:  -58.694949 | E_var:     5.8873 | E_err:   0.028123
[2025-10-30 02:59:52] [Iter  648/1575] R0[572/1500]  | LR: 0.034424 | E:  -58.686230 | E_var:     5.5947 | E_err:   0.025663
[2025-10-30 03:00:09] [Iter  649/1575] R0[573/1500]  | LR: 0.034376 | E:  -58.713122 | E_var:     5.5935 | E_err:   0.027519
[2025-10-30 03:00:25] [Iter  650/1575] R0[574/1500]  | LR: 0.034328 | E:  -58.709116 | E_var:     5.6264 | E_err:   0.027554
[2025-10-30 03:00:42] [Iter  651/1575] R0[575/1500]  | LR: 0.034280 | E:  -58.699486 | E_var:     5.8049 | E_err:   0.027583
[2025-10-30 03:00:58] [Iter  652/1575] R0[576/1500]  | LR: 0.034232 | E:  -58.731808 | E_var:     6.1878 | E_err:   0.028734
[2025-10-30 03:01:15] [Iter  653/1575] R0[577/1500]  | LR: 0.034184 | E:  -58.712603 | E_var:     5.8200 | E_err:   0.027521
[2025-10-30 03:01:31] [Iter  654/1575] R0[578/1500]  | LR: 0.034136 | E:  -58.698401 | E_var:     5.9842 | E_err:   0.027816
[2025-10-30 03:01:48] [Iter  655/1575] R0[579/1500]  | LR: 0.034088 | E:  -58.757892 | E_var:     5.8449 | E_err:   0.027396
[2025-10-30 03:02:04] [Iter  656/1575] R0[580/1500]  | LR: 0.034040 | E:  -58.691343 | E_var:     5.8973 | E_err:   0.027466
[2025-10-30 03:02:21] [Iter  657/1575] R0[581/1500]  | LR: 0.033992 | E:  -58.711019 | E_var:     5.8555 | E_err:   0.027657
[2025-10-30 03:02:38] [Iter  658/1575] R0[582/1500]  | LR: 0.033944 | E:  -58.748220 | E_var:     5.6912 | E_err:   0.025177
[2025-10-30 03:02:54] [Iter  659/1575] R0[583/1500]  | LR: 0.033896 | E:  -58.717728 | E_var:     5.6604 | E_err:   0.028100
[2025-10-30 03:03:11] [Iter  660/1575] R0[584/1500]  | LR: 0.033847 | E:  -58.706414 | E_var:     5.5133 | E_err:   0.026354
[2025-10-30 03:03:27] [Iter  661/1575] R0[585/1500]  | LR: 0.033799 | E:  -58.633161 | E_var:     5.2795 | E_err:   0.025827
[2025-10-30 03:03:44] [Iter  662/1575] R0[586/1500]  | LR: 0.033751 | E:  -58.635043 | E_var:     5.7967 | E_err:   0.027620
[2025-10-30 03:04:00] [Iter  663/1575] R0[587/1500]  | LR: 0.033702 | E:  -58.641630 | E_var:     5.6759 | E_err:   0.028604
[2025-10-30 03:04:17] [Iter  664/1575] R0[588/1500]  | LR: 0.033654 | E:  -58.755701 | E_var:     5.4266 | E_err:   0.025424
[2025-10-30 03:04:33] [Iter  665/1575] R0[589/1500]  | LR: 0.033606 | E:  -58.750090 | E_var:     5.4759 | E_err:   0.025449
[2025-10-30 03:04:50] [Iter  666/1575] R0[590/1500]  | LR: 0.033557 | E:  -58.753716 | E_var:     5.4819 | E_err:   0.026466
[2025-10-30 03:05:06] [Iter  667/1575] R0[591/1500]  | LR: 0.033509 | E:  -58.746926 | E_var:     5.5425 | E_err:   0.026012
[2025-10-30 03:05:23] [Iter  668/1575] R0[592/1500]  | LR: 0.033460 | E:  -58.725758 | E_var:     5.6310 | E_err:   0.025772
[2025-10-30 03:05:40] [Iter  669/1575] R0[593/1500]  | LR: 0.033412 | E:  -58.777416 | E_var:     5.9088 | E_err:   0.027537
[2025-10-30 03:05:56] [Iter  670/1575] R0[594/1500]  | LR: 0.033363 | E:  -58.721570 | E_var:     5.8211 | E_err:   0.027509
[2025-10-30 03:06:13] [Iter  671/1575] R0[595/1500]  | LR: 0.033315 | E:  -58.760516 | E_var:     5.9273 | E_err:   0.027205
[2025-10-30 03:06:29] [Iter  672/1575] R0[596/1500]  | LR: 0.033266 | E:  -58.805720 | E_var:     5.8042 | E_err:   0.027105
[2025-10-30 03:06:46] [Iter  673/1575] R0[597/1500]  | LR: 0.033217 | E:  -58.728225 | E_var:     5.7042 | E_err:   0.027038
[2025-10-30 03:07:02] [Iter  674/1575] R0[598/1500]  | LR: 0.033168 | E:  -58.710591 | E_var:     5.8941 | E_err:   0.027932
[2025-10-30 03:07:19] [Iter  675/1575] R0[599/1500]  | LR: 0.033120 | E:  -58.714694 | E_var:     5.6658 | E_err:   0.026840
[2025-10-30 03:07:19] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-30 03:07:36] [Iter  676/1575] R0[600/1500]  | LR: 0.033071 | E:  -58.752029 | E_var:     5.9616 | E_err:   0.028018
[2025-10-30 03:07:52] [Iter  677/1575] R0[601/1500]  | LR: 0.033022 | E:  -58.809078 | E_var:     5.7343 | E_err:   0.027559
[2025-10-30 03:08:09] [Iter  678/1575] R0[602/1500]  | LR: 0.032973 | E:  -58.723619 | E_var:     5.9743 | E_err:   0.026145
[2025-10-30 03:08:25] [Iter  679/1575] R0[603/1500]  | LR: 0.032924 | E:  -58.720505 | E_var:     5.8354 | E_err:   0.027305
[2025-10-30 03:08:42] [Iter  680/1575] R0[604/1500]  | LR: 0.032875 | E:  -58.702181 | E_var:     6.0432 | E_err:   0.028437
[2025-10-30 03:08:58] [Iter  681/1575] R0[605/1500]  | LR: 0.032826 | E:  -58.719281 | E_var:     5.8531 | E_err:   0.027787
[2025-10-30 03:09:15] [Iter  682/1575] R0[606/1500]  | LR: 0.032778 | E:  -58.771075 | E_var:     5.6789 | E_err:   0.027557
[2025-10-30 03:09:31] [Iter  683/1575] R0[607/1500]  | LR: 0.032729 | E:  -58.746177 | E_var:     5.4738 | E_err:   0.027364
[2025-10-30 03:09:48] [Iter  684/1575] R0[608/1500]  | LR: 0.032679 | E:  -58.711146 | E_var:     5.9326 | E_err:   0.027603
[2025-10-30 03:10:05] [Iter  685/1575] R0[609/1500]  | LR: 0.032630 | E:  -58.747600 | E_var:     5.8611 | E_err:   0.028255
[2025-10-30 03:10:21] [Iter  686/1575] R0[610/1500]  | LR: 0.032581 | E:  -58.733097 | E_var:     5.6717 | E_err:   0.027086
[2025-10-30 03:10:38] [Iter  687/1575] R0[611/1500]  | LR: 0.032532 | E:  -58.746975 | E_var:     5.7792 | E_err:   0.027344
[2025-10-30 03:10:54] [Iter  688/1575] R0[612/1500]  | LR: 0.032483 | E:  -58.727641 | E_var:     5.7510 | E_err:   0.026446
[2025-10-30 03:11:11] [Iter  689/1575] R0[613/1500]  | LR: 0.032434 | E:  -58.746461 | E_var:     5.5914 | E_err:   0.026590
[2025-10-30 03:11:27] [Iter  690/1575] R0[614/1500]  | LR: 0.032385 | E:  -58.740531 | E_var:     5.8991 | E_err:   0.026661
[2025-10-30 03:11:44] [Iter  691/1575] R0[615/1500]  | LR: 0.032335 | E:  -58.746665 | E_var:     5.6835 | E_err:   0.027212
[2025-10-30 03:12:00] [Iter  692/1575] R0[616/1500]  | LR: 0.032286 | E:  -58.761607 | E_var:     5.5322 | E_err:   0.026216
[2025-10-30 03:12:17] [Iter  693/1575] R0[617/1500]  | LR: 0.032237 | E:  -58.741190 | E_var:     5.4039 | E_err:   0.025514
[2025-10-30 03:12:33] [Iter  694/1575] R0[618/1500]  | LR: 0.032187 | E:  -58.729138 | E_var:     5.7907 | E_err:   0.026062
[2025-10-30 03:12:50] [Iter  695/1575] R0[619/1500]  | LR: 0.032138 | E:  -58.764791 | E_var:     5.8013 | E_err:   0.027280
[2025-10-30 03:13:07] [Iter  696/1575] R0[620/1500]  | LR: 0.032089 | E:  -58.743323 | E_var:     5.5320 | E_err:   0.026558
[2025-10-30 03:13:23] [Iter  697/1575] R0[621/1500]  | LR: 0.032039 | E:  -58.800871 | E_var:     5.9409 | E_err:   0.027994
[2025-10-30 03:13:40] [Iter  698/1575] R0[622/1500]  | LR: 0.031990 | E:  -58.728780 | E_var:     5.7747 | E_err:   0.025300
[2025-10-30 03:13:56] [Iter  699/1575] R0[623/1500]  | LR: 0.031940 | E:  -58.724820 | E_var:     5.9200 | E_err:   0.026630
[2025-10-30 03:14:13] [Iter  700/1575] R0[624/1500]  | LR: 0.031891 | E:  -58.780995 | E_var:     5.6527 | E_err:   0.027794
[2025-10-30 03:14:29] [Iter  701/1575] R0[625/1500]  | LR: 0.031841 | E:  -58.767826 | E_var:     5.6149 | E_err:   0.026173
[2025-10-30 03:14:46] [Iter  702/1575] R0[626/1500]  | LR: 0.031791 | E:  -58.772657 | E_var:     5.4649 | E_err:   0.026858
[2025-10-30 03:15:02] [Iter  703/1575] R0[627/1500]  | LR: 0.031742 | E:  -58.736105 | E_var:     5.6650 | E_err:   0.026193
[2025-10-30 03:15:19] [Iter  704/1575] R0[628/1500]  | LR: 0.031692 | E:  -58.765836 | E_var:     6.0077 | E_err:   0.028394
[2025-10-30 03:15:35] [Iter  705/1575] R0[629/1500]  | LR: 0.031643 | E:  -58.746461 | E_var:     5.7400 | E_err:   0.027326
[2025-10-30 03:15:52] [Iter  706/1575] R0[630/1500]  | LR: 0.031593 | E:  -58.760676 | E_var:     5.9361 | E_err:   0.027135
[2025-10-30 03:16:09] [Iter  707/1575] R0[631/1500]  | LR: 0.031543 | E:  -58.738252 | E_var:     5.7019 | E_err:   0.027955
[2025-10-30 03:16:25] [Iter  708/1575] R0[632/1500]  | LR: 0.031493 | E:  -58.773058 | E_var:     5.6807 | E_err:   0.027351
[2025-10-30 03:16:42] [Iter  709/1575] R0[633/1500]  | LR: 0.031444 | E:  -58.731161 | E_var:     6.2508 | E_err:   0.028325
[2025-10-30 03:16:58] [Iter  710/1575] R0[634/1500]  | LR: 0.031394 | E:  -58.744066 | E_var:     5.8227 | E_err:   0.027830
[2025-10-30 03:17:15] [Iter  711/1575] R0[635/1500]  | LR: 0.031344 | E:  -58.770459 | E_var:     5.5556 | E_err:   0.026023
[2025-10-30 03:17:31] [Iter  712/1575] R0[636/1500]  | LR: 0.031294 | E:  -58.751184 | E_var:     5.9403 | E_err:   0.027627
[2025-10-30 03:17:48] [Iter  713/1575] R0[637/1500]  | LR: 0.031244 | E:  -58.756249 | E_var:     5.8193 | E_err:   0.026675
[2025-10-30 03:18:04] [Iter  714/1575] R0[638/1500]  | LR: 0.031194 | E:  -58.769432 | E_var:     5.7631 | E_err:   0.027811
[2025-10-30 03:18:21] [Iter  715/1575] R0[639/1500]  | LR: 0.031145 | E:  -58.727740 | E_var:     5.6371 | E_err:   0.026625
[2025-10-30 03:18:38] [Iter  716/1575] R0[640/1500]  | LR: 0.031095 | E:  -58.776236 | E_var:     5.7664 | E_err:   0.027288
[2025-10-30 03:18:54] [Iter  717/1575] R0[641/1500]  | LR: 0.031045 | E:  -58.748339 | E_var:     5.5819 | E_err:   0.025835
[2025-10-30 03:19:11] [Iter  718/1575] R0[642/1500]  | LR: 0.030995 | E:  -58.794406 | E_var:     5.5322 | E_err:   0.026845
[2025-10-30 03:19:27] [Iter  719/1575] R0[643/1500]  | LR: 0.030945 | E:  -58.780853 | E_var:     5.6414 | E_err:   0.026777
[2025-10-30 03:19:44] [Iter  720/1575] R0[644/1500]  | LR: 0.030895 | E:  -58.757205 | E_var:     5.6001 | E_err:   0.026149
[2025-10-30 03:20:00] [Iter  721/1575] R0[645/1500]  | LR: 0.030845 | E:  -58.791330 | E_var:     5.8468 | E_err:   0.027911
[2025-10-30 03:20:17] [Iter  722/1575] R0[646/1500]  | LR: 0.030794 | E:  -58.814059 | E_var:     5.6944 | E_err:   0.025992
[2025-10-30 03:20:33] [Iter  723/1575] R0[647/1500]  | LR: 0.030744 | E:  -58.802220 | E_var:     5.6437 | E_err:   0.026827
[2025-10-30 03:20:50] [Iter  724/1575] R0[648/1500]  | LR: 0.030694 | E:  -58.808340 | E_var:     5.6219 | E_err:   0.027611
[2025-10-30 03:21:07] [Iter  725/1575] R0[649/1500]  | LR: 0.030644 | E:  -58.770120 | E_var:     5.7905 | E_err:   0.026298
[2025-10-30 03:21:23] [Iter  726/1575] R0[650/1500]  | LR: 0.030594 | E:  -58.809743 | E_var:     5.6092 | E_err:   0.026202
[2025-10-30 03:21:40] [Iter  727/1575] R0[651/1500]  | LR: 0.030544 | E:  -58.771548 | E_var:     5.9454 | E_err:   0.026481
[2025-10-30 03:21:56] [Iter  728/1575] R0[652/1500]  | LR: 0.030493 | E:  -58.745531 | E_var:     5.6144 | E_err:   0.026943
[2025-10-30 03:22:13] [Iter  729/1575] R0[653/1500]  | LR: 0.030443 | E:  -58.766313 | E_var:     5.7478 | E_err:   0.026990
[2025-10-30 03:22:29] [Iter  730/1575] R0[654/1500]  | LR: 0.030393 | E:  -58.729707 | E_var:     5.9969 | E_err:   0.028152
[2025-10-30 03:22:46] [Iter  731/1575] R0[655/1500]  | LR: 0.030343 | E:  -58.749444 | E_var:     6.0994 | E_err:   0.027952
[2025-10-30 03:23:02] [Iter  732/1575] R0[656/1500]  | LR: 0.030292 | E:  -58.732211 | E_var:     5.9707 | E_err:   0.027225
[2025-10-30 03:23:19] [Iter  733/1575] R0[657/1500]  | LR: 0.030242 | E:  -58.772488 | E_var:     5.6263 | E_err:   0.026969
[2025-10-30 03:23:35] [Iter  734/1575] R0[658/1500]  | LR: 0.030192 | E:  -58.748211 | E_var:     6.0416 | E_err:   0.029698
[2025-10-30 03:23:52] [Iter  735/1575] R0[659/1500]  | LR: 0.030141 | E:  -58.734802 | E_var:     5.4493 | E_err:   0.026364
[2025-10-30 03:24:09] [Iter  736/1575] R0[660/1500]  | LR: 0.030091 | E:  -58.814878 | E_var:     5.4454 | E_err:   0.027364
[2025-10-30 03:24:25] [Iter  737/1575] R0[661/1500]  | LR: 0.030040 | E:  -58.741526 | E_var:     5.2905 | E_err:   0.025283
[2025-10-30 03:24:42] [Iter  738/1575] R0[662/1500]  | LR: 0.029990 | E:  -58.719867 | E_var:     5.4734 | E_err:   0.025796
[2025-10-30 03:24:58] [Iter  739/1575] R0[663/1500]  | LR: 0.029940 | E:  -58.766141 | E_var:     5.5791 | E_err:   0.025705
[2025-10-30 03:25:15] [Iter  740/1575] R0[664/1500]  | LR: 0.029889 | E:  -58.784323 | E_var:     5.5667 | E_err:   0.026433
[2025-10-30 03:25:31] [Iter  741/1575] R0[665/1500]  | LR: 0.029839 | E:  -58.793472 | E_var:     5.4459 | E_err:   0.026695
[2025-10-30 03:25:48] [Iter  742/1575] R0[666/1500]  | LR: 0.029788 | E:  -58.707127 | E_var:     5.2903 | E_err:   0.027295
[2025-10-30 03:26:04] [Iter  743/1575] R0[667/1500]  | LR: 0.029738 | E:  -58.788087 | E_var:     5.1629 | E_err:   0.025292
[2025-10-30 03:26:21] [Iter  744/1575] R0[668/1500]  | LR: 0.029687 | E:  -58.729481 | E_var:     5.2600 | E_err:   0.026395
[2025-10-30 03:26:37] [Iter  745/1575] R0[669/1500]  | LR: 0.029636 | E:  -58.740098 | E_var:     5.4683 | E_err:   0.026473
[2025-10-30 03:26:54] [Iter  746/1575] R0[670/1500]  | LR: 0.029586 | E:  -58.761471 | E_var:     5.2803 | E_err:   0.026546
[2025-10-30 03:27:11] [Iter  747/1575] R0[671/1500]  | LR: 0.029535 | E:  -58.736014 | E_var:     5.2862 | E_err:   0.026548
[2025-10-30 03:27:27] [Iter  748/1575] R0[672/1500]  | LR: 0.029485 | E:  -58.742681 | E_var:     5.3487 | E_err:   0.026993
[2025-10-30 03:27:44] [Iter  749/1575] R0[673/1500]  | LR: 0.029434 | E:  -58.697846 | E_var:     5.1861 | E_err:   0.026313
[2025-10-30 03:28:00] [Iter  750/1575] R0[674/1500]  | LR: 0.029383 | E:  -58.724844 | E_var:     5.3209 | E_err:   0.027010
[2025-10-30 03:28:17] [Iter  751/1575] R0[675/1500]  | LR: 0.029333 | E:  -58.792673 | E_var:     5.2238 | E_err:   0.025098
[2025-10-30 03:28:33] [Iter  752/1575] R0[676/1500]  | LR: 0.029282 | E:  -58.789932 | E_var:     5.2927 | E_err:   0.026323
[2025-10-30 03:28:50] [Iter  753/1575] R0[677/1500]  | LR: 0.029231 | E:  -58.794440 | E_var:     5.4430 | E_err:   0.027137
[2025-10-30 03:29:06] [Iter  754/1575] R0[678/1500]  | LR: 0.029181 | E:  -58.822147 | E_var:     5.5735 | E_err:   0.025913
[2025-10-30 03:29:23] [Iter  755/1575] R0[679/1500]  | LR: 0.029130 | E:  -58.800593 | E_var:     5.4034 | E_err:   0.026501
[2025-10-30 03:29:39] [Iter  756/1575] R0[680/1500]  | LR: 0.029079 | E:  -58.758391 | E_var:     5.3958 | E_err:   0.025853
[2025-10-30 03:29:56] [Iter  757/1575] R0[681/1500]  | LR: 0.029028 | E:  -58.773659 | E_var:     5.4478 | E_err:   0.026525
[2025-10-30 03:30:13] [Iter  758/1575] R0[682/1500]  | LR: 0.028977 | E:  -58.726610 | E_var:     5.2927 | E_err:   0.024740
[2025-10-30 03:30:29] [Iter  759/1575] R0[683/1500]  | LR: 0.028927 | E:  -58.822473 | E_var:     5.2876 | E_err:   0.025052
[2025-10-30 03:30:46] [Iter  760/1575] R0[684/1500]  | LR: 0.028876 | E:  -58.798525 | E_var:     5.6253 | E_err:   0.027122
[2025-10-30 03:31:02] [Iter  761/1575] R0[685/1500]  | LR: 0.028825 | E:  -58.783260 | E_var:     5.3299 | E_err:   0.026201
[2025-10-30 03:31:19] [Iter  762/1575] R0[686/1500]  | LR: 0.028774 | E:  -58.786774 | E_var:     5.5400 | E_err:   0.026880
[2025-10-30 03:31:35] [Iter  763/1575] R0[687/1500]  | LR: 0.028723 | E:  -58.792073 | E_var:     5.5464 | E_err:   0.026861
[2025-10-30 03:31:52] [Iter  764/1575] R0[688/1500]  | LR: 0.028672 | E:  -58.784856 | E_var:     5.6398 | E_err:   0.027250
[2025-10-30 03:32:08] [Iter  765/1575] R0[689/1500]  | LR: 0.028622 | E:  -58.786348 | E_var:     5.4371 | E_err:   0.026299
[2025-10-30 03:32:25] [Iter  766/1575] R0[690/1500]  | LR: 0.028571 | E:  -58.712178 | E_var:     5.2555 | E_err:   0.025837
[2025-10-30 03:32:42] [Iter  767/1575] R0[691/1500]  | LR: 0.028520 | E:  -58.768666 | E_var:     5.3916 | E_err:   0.025086
[2025-10-30 03:32:58] [Iter  768/1575] R0[692/1500]  | LR: 0.028469 | E:  -58.788916 | E_var:     5.6906 | E_err:   0.026336
[2025-10-30 03:33:15] [Iter  769/1575] R0[693/1500]  | LR: 0.028418 | E:  -58.800330 | E_var:     5.6389 | E_err:   0.028149
[2025-10-30 03:33:31] [Iter  770/1575] R0[694/1500]  | LR: 0.028367 | E:  -58.796949 | E_var:     5.4332 | E_err:   0.025174
[2025-10-30 03:33:48] [Iter  771/1575] R0[695/1500]  | LR: 0.028316 | E:  -58.812307 | E_var:     5.4713 | E_err:   0.026318
[2025-10-30 03:34:04] [Iter  772/1575] R0[696/1500]  | LR: 0.028265 | E:  -58.784575 | E_var:     5.3013 | E_err:   0.024699
[2025-10-30 03:34:21] [Iter  773/1575] R0[697/1500]  | LR: 0.028214 | E:  -58.797215 | E_var:     5.3080 | E_err:   0.025976
[2025-10-30 03:34:37] [Iter  774/1575] R0[698/1500]  | LR: 0.028163 | E:  -58.816779 | E_var:     5.5591 | E_err:   0.026681
[2025-10-30 03:34:54] [Iter  775/1575] R0[699/1500]  | LR: 0.028112 | E:  -58.780681 | E_var:     5.2345 | E_err:   0.025664
[2025-10-30 03:35:10] [Iter  776/1575] R0[700/1500]  | LR: 0.028061 | E:  -58.779104 | E_var:     5.3650 | E_err:   0.025352
[2025-10-30 03:35:27] [Iter  777/1575] R0[701/1500]  | LR: 0.028010 | E:  -58.788466 | E_var:     5.5114 | E_err:   0.026043
[2025-10-30 03:35:44] [Iter  778/1575] R0[702/1500]  | LR: 0.027959 | E:  -58.815337 | E_var:     5.7133 | E_err:   0.026584
[2025-10-30 03:36:00] [Iter  779/1575] R0[703/1500]  | LR: 0.027908 | E:  -58.823818 | E_var:     5.2339 | E_err:   0.025099
[2025-10-30 03:36:17] [Iter  780/1575] R0[704/1500]  | LR: 0.027857 | E:  -58.839213 | E_var:     5.5505 | E_err:   0.026385
[2025-10-30 03:36:33] [Iter  781/1575] R0[705/1500]  | LR: 0.027806 | E:  -58.810920 | E_var:     5.4523 | E_err:   0.025844
[2025-10-30 03:36:50] [Iter  782/1575] R0[706/1500]  | LR: 0.027755 | E:  -58.829235 | E_var:     5.6910 | E_err:   0.025378
[2025-10-30 03:37:06] [Iter  783/1575] R0[707/1500]  | LR: 0.027703 | E:  -58.803141 | E_var:     5.6698 | E_err:   0.027515
[2025-10-30 03:37:23] [Iter  784/1575] R0[708/1500]  | LR: 0.027652 | E:  -58.821508 | E_var:     5.6543 | E_err:   0.027530
[2025-10-30 03:37:39] [Iter  785/1575] R0[709/1500]  | LR: 0.027601 | E:  -58.832552 | E_var:     5.6853 | E_err:   0.025292
[2025-10-30 03:37:56] [Iter  786/1575] R0[710/1500]  | LR: 0.027550 | E:  -58.819538 | E_var:     5.6280 | E_err:   0.027774
[2025-10-30 03:38:13] [Iter  787/1575] R0[711/1500]  | LR: 0.027499 | E:  -58.782503 | E_var:     5.7855 | E_err:   0.026864
[2025-10-30 03:38:29] [Iter  788/1575] R0[712/1500]  | LR: 0.027448 | E:  -58.798365 | E_var:     5.4639 | E_err:   0.026175
[2025-10-30 03:38:46] [Iter  789/1575] R0[713/1500]  | LR: 0.027397 | E:  -58.846066 | E_var:     5.5852 | E_err:   0.027870
[2025-10-30 03:39:02] [Iter  790/1575] R0[714/1500]  | LR: 0.027346 | E:  -58.850467 | E_var:     5.4602 | E_err:   0.027371
[2025-10-30 03:39:19] [Iter  791/1575] R0[715/1500]  | LR: 0.027294 | E:  -58.868061 | E_var:     5.7229 | E_err:   0.027981
[2025-10-30 03:39:35] [Iter  792/1575] R0[716/1500]  | LR: 0.027243 | E:  -58.763121 | E_var:     5.5803 | E_err:   0.027724
[2025-10-30 03:39:52] [Iter  793/1575] R0[717/1500]  | LR: 0.027192 | E:  -58.792999 | E_var:     5.3736 | E_err:   0.026971
[2025-10-30 03:40:08] [Iter  794/1575] R0[718/1500]  | LR: 0.027141 | E:  -58.837943 | E_var:     5.7059 | E_err:   0.026239
[2025-10-30 03:40:25] [Iter  795/1575] R0[719/1500]  | LR: 0.027090 | E:  -58.799935 | E_var:     5.3563 | E_err:   0.025619
[2025-10-30 03:40:42] [Iter  796/1575] R0[720/1500]  | LR: 0.027038 | E:  -58.864446 | E_var:     5.6985 | E_err:   0.027082
[2025-10-30 03:40:58] [Iter  797/1575] R0[721/1500]  | LR: 0.026987 | E:  -58.794704 | E_var:     5.5349 | E_err:   0.026681
[2025-10-30 03:41:15] [Iter  798/1575] R0[722/1500]  | LR: 0.026936 | E:  -58.824299 | E_var:     5.4532 | E_err:   0.024885
[2025-10-30 03:41:31] [Iter  799/1575] R0[723/1500]  | LR: 0.026885 | E:  -58.808181 | E_var:     5.3981 | E_err:   0.026413
[2025-10-30 03:41:48] [Iter  800/1575] R0[724/1500]  | LR: 0.026833 | E:  -58.853263 | E_var:     5.4733 | E_err:   0.024723
[2025-10-30 03:42:04] [Iter  801/1575] R0[725/1500]  | LR: 0.026782 | E:  -58.806898 | E_var:     5.4865 | E_err:   0.025869
[2025-10-30 03:42:21] [Iter  802/1575] R0[726/1500]  | LR: 0.026731 | E:  -58.868710 | E_var:     5.5013 | E_err:   0.027029
[2025-10-30 03:42:37] [Iter  803/1575] R0[727/1500]  | LR: 0.026680 | E:  -58.851671 | E_var:     5.3308 | E_err:   0.025606
[2025-10-30 03:42:54] [Iter  804/1575] R0[728/1500]  | LR: 0.026628 | E:  -58.851612 | E_var:     5.3457 | E_err:   0.025337
[2025-10-30 03:43:11] [Iter  805/1575] R0[729/1500]  | LR: 0.026577 | E:  -58.805192 | E_var:     5.4850 | E_err:   0.027685
[2025-10-30 03:43:27] [Iter  806/1575] R0[730/1500]  | LR: 0.026526 | E:  -58.838452 | E_var:     5.3920 | E_err:   0.025983
[2025-10-30 03:43:44] [Iter  807/1575] R0[731/1500]  | LR: 0.026475 | E:  -58.805704 | E_var:     5.4036 | E_err:   0.027430
[2025-10-30 03:44:00] [Iter  808/1575] R0[732/1500]  | LR: 0.026423 | E:  -58.841963 | E_var:     5.3654 | E_err:   0.027511
[2025-10-30 03:44:17] [Iter  809/1575] R0[733/1500]  | LR: 0.026372 | E:  -58.823122 | E_var:     5.8059 | E_err:   0.027483
[2025-10-30 03:44:33] [Iter  810/1575] R0[734/1500]  | LR: 0.026321 | E:  -58.842741 | E_var:     5.2630 | E_err:   0.026180
[2025-10-30 03:44:50] [Iter  811/1575] R0[735/1500]  | LR: 0.026270 | E:  -58.853558 | E_var:     5.6930 | E_err:   0.026101
[2025-10-30 03:45:06] [Iter  812/1575] R0[736/1500]  | LR: 0.026218 | E:  -58.757175 | E_var:     5.7143 | E_err:   0.026068
[2025-10-30 03:45:23] [Iter  813/1575] R0[737/1500]  | LR: 0.026167 | E:  -58.802737 | E_var:     5.7402 | E_err:   0.026029
[2025-10-30 03:45:40] [Iter  814/1575] R0[738/1500]  | LR: 0.026116 | E:  -58.838266 | E_var:     5.6573 | E_err:   0.027269
[2025-10-30 03:45:56] [Iter  815/1575] R0[739/1500]  | LR: 0.026064 | E:  -58.825598 | E_var:     5.4875 | E_err:   0.026558
[2025-10-30 03:46:13] [Iter  816/1575] R0[740/1500]  | LR: 0.026013 | E:  -58.859443 | E_var:     5.5594 | E_err:   0.026367
[2025-10-30 03:46:29] [Iter  817/1575] R0[741/1500]  | LR: 0.025962 | E:  -58.853349 | E_var:     5.5102 | E_err:   0.026606
[2025-10-30 03:46:46] [Iter  818/1575] R0[742/1500]  | LR: 0.025910 | E:  -58.879337 | E_var:     5.3541 | E_err:   0.024715
[2025-10-30 03:47:02] [Iter  819/1575] R0[743/1500]  | LR: 0.025859 | E:  -58.797011 | E_var:     5.4142 | E_err:   0.027701
[2025-10-30 03:47:19] [Iter  820/1575] R0[744/1500]  | LR: 0.025808 | E:  -58.822389 | E_var:     5.3855 | E_err:   0.026824
[2025-10-30 03:47:35] [Iter  821/1575] R0[745/1500]  | LR: 0.025757 | E:  -58.810880 | E_var:     5.4606 | E_err:   0.026462
[2025-10-30 03:47:52] [Iter  822/1575] R0[746/1500]  | LR: 0.025705 | E:  -58.859308 | E_var:     5.4396 | E_err:   0.027031
[2025-10-30 03:48:09] [Iter  823/1575] R0[747/1500]  | LR: 0.025654 | E:  -58.791281 | E_var:     5.6232 | E_err:   0.027232
[2025-10-30 03:48:25] [Iter  824/1575] R0[748/1500]  | LR: 0.025603 | E:  -58.781192 | E_var:     5.2872 | E_err:   0.025289
[2025-10-30 03:48:42] [Iter  825/1575] R0[749/1500]  | LR: 0.025551 | E:  -58.820977 | E_var:     5.6313 | E_err:   0.027495
[2025-10-30 03:48:42] ✓ Checkpoint saved: checkpoint_iter_000750.pkl
[2025-10-30 03:48:58] [Iter  826/1575] R0[750/1500]  | LR: 0.025500 | E:  -58.840528 | E_var:     5.4598 | E_err:   0.026585
[2025-10-30 03:49:15] [Iter  827/1575] R0[751/1500]  | LR: 0.025449 | E:  -58.853869 | E_var:     5.7001 | E_err:   0.027092
[2025-10-30 03:49:31] [Iter  828/1575] R0[752/1500]  | LR: 0.025397 | E:  -58.842569 | E_var:     5.2504 | E_err:   0.025325
[2025-10-30 03:49:48] [Iter  829/1575] R0[753/1500]  | LR: 0.025346 | E:  -58.843979 | E_var:     5.1793 | E_err:   0.026728
[2025-10-30 03:50:04] [Iter  830/1575] R0[754/1500]  | LR: 0.025295 | E:  -58.843801 | E_var:     5.4134 | E_err:   0.027933
[2025-10-30 03:50:21] [Iter  831/1575] R0[755/1500]  | LR: 0.025243 | E:  -58.845492 | E_var:     5.4098 | E_err:   0.025593
[2025-10-30 03:50:38] [Iter  832/1575] R0[756/1500]  | LR: 0.025192 | E:  -58.863053 | E_var:     5.4266 | E_err:   0.025871
[2025-10-30 03:50:54] [Iter  833/1575] R0[757/1500]  | LR: 0.025141 | E:  -58.857441 | E_var:     5.7130 | E_err:   0.025814
[2025-10-30 03:51:11] [Iter  834/1575] R0[758/1500]  | LR: 0.025090 | E:  -58.842732 | E_var:     5.3355 | E_err:   0.025899
[2025-10-30 03:51:27] [Iter  835/1575] R0[759/1500]  | LR: 0.025038 | E:  -58.908102 | E_var:     5.4306 | E_err:   0.026690
[2025-10-30 03:51:44] [Iter  836/1575] R0[760/1500]  | LR: 0.024987 | E:  -58.825658 | E_var:     5.3764 | E_err:   0.026052
[2025-10-30 03:52:00] [Iter  837/1575] R0[761/1500]  | LR: 0.024936 | E:  -58.872260 | E_var:     5.3694 | E_err:   0.025626
[2025-10-30 03:52:17] [Iter  838/1575] R0[762/1500]  | LR: 0.024884 | E:  -58.844025 | E_var:     5.4655 | E_err:   0.026704
[2025-10-30 03:52:33] [Iter  839/1575] R0[763/1500]  | LR: 0.024833 | E:  -58.894348 | E_var:     5.5788 | E_err:   0.027342
[2025-10-30 03:52:50] [Iter  840/1575] R0[764/1500]  | LR: 0.024782 | E:  -58.862911 | E_var:     5.5082 | E_err:   0.025471
[2025-10-30 03:53:06] [Iter  841/1575] R0[765/1500]  | LR: 0.024730 | E:  -58.858465 | E_var:     5.4333 | E_err:   0.026686
[2025-10-30 03:53:23] [Iter  842/1575] R0[766/1500]  | LR: 0.024679 | E:  -58.843937 | E_var:     5.5103 | E_err:   0.027117
[2025-10-30 03:53:40] [Iter  843/1575] R0[767/1500]  | LR: 0.024628 | E:  -58.853258 | E_var:     5.2847 | E_err:   0.025824
[2025-10-30 03:53:56] [Iter  844/1575] R0[768/1500]  | LR: 0.024577 | E:  -58.815797 | E_var:     5.3669 | E_err:   0.025385
[2025-10-30 03:54:13] [Iter  845/1575] R0[769/1500]  | LR: 0.024525 | E:  -58.801874 | E_var:     5.2609 | E_err:   0.025144
[2025-10-30 03:54:29] [Iter  846/1575] R0[770/1500]  | LR: 0.024474 | E:  -58.853506 | E_var:     5.5022 | E_err:   0.025831
[2025-10-30 03:54:46] [Iter  847/1575] R0[771/1500]  | LR: 0.024423 | E:  -58.867184 | E_var:     5.5780 | E_err:   0.025695
[2025-10-30 03:55:02] [Iter  848/1575] R0[772/1500]  | LR: 0.024372 | E:  -58.851475 | E_var:     5.3059 | E_err:   0.026843
[2025-10-30 03:55:19] [Iter  849/1575] R0[773/1500]  | LR: 0.024320 | E:  -58.809931 | E_var:     5.1230 | E_err:   0.025918
[2025-10-30 03:55:35] [Iter  850/1575] R0[774/1500]  | LR: 0.024269 | E:  -58.825496 | E_var:     5.2014 | E_err:   0.025362
[2025-10-30 03:55:52] [Iter  851/1575] R0[775/1500]  | LR: 0.024218 | E:  -58.847683 | E_var:     5.4622 | E_err:   0.024742
[2025-10-30 03:56:08] [Iter  852/1575] R0[776/1500]  | LR: 0.024167 | E:  -58.880421 | E_var:     5.6508 | E_err:   0.025829
[2025-10-30 03:56:25] [Iter  853/1575] R0[777/1500]  | LR: 0.024115 | E:  -58.890603 | E_var:     5.4970 | E_err:   0.026381
[2025-10-30 03:56:42] [Iter  854/1575] R0[778/1500]  | LR: 0.024064 | E:  -58.844211 | E_var:     5.5045 | E_err:   0.025156
[2025-10-30 03:56:58] [Iter  855/1575] R0[779/1500]  | LR: 0.024013 | E:  -58.808147 | E_var:     5.2537 | E_err:   0.025234
[2025-10-30 03:57:15] [Iter  856/1575] R0[780/1500]  | LR: 0.023962 | E:  -58.790058 | E_var:     5.3865 | E_err:   0.024667
[2025-10-30 03:57:31] [Iter  857/1575] R0[781/1500]  | LR: 0.023910 | E:  -58.855575 | E_var:     5.4272 | E_err:   0.026654
[2025-10-30 03:57:48] [Iter  858/1575] R0[782/1500]  | LR: 0.023859 | E:  -58.845571 | E_var:     5.5889 | E_err:   0.027479
[2025-10-30 03:58:04] [Iter  859/1575] R0[783/1500]  | LR: 0.023808 | E:  -58.905555 | E_var:     5.5304 | E_err:   0.025782
[2025-10-30 03:58:21] [Iter  860/1575] R0[784/1500]  | LR: 0.023757 | E:  -58.838997 | E_var:     5.4272 | E_err:   0.025583
[2025-10-30 03:58:37] [Iter  861/1575] R0[785/1500]  | LR: 0.023706 | E:  -58.899346 | E_var:     5.6231 | E_err:   0.026898
[2025-10-30 03:58:54] [Iter  862/1575] R0[786/1500]  | LR: 0.023654 | E:  -58.848607 | E_var:     5.4618 | E_err:   0.027017
[2025-10-30 03:59:10] [Iter  863/1575] R0[787/1500]  | LR: 0.023603 | E:  -58.806520 | E_var:     5.7171 | E_err:   0.027572
[2025-10-30 03:59:27] [Iter  864/1575] R0[788/1500]  | LR: 0.023552 | E:  -58.800023 | E_var:     5.6246 | E_err:   0.027572
[2025-10-30 03:59:44] [Iter  865/1575] R0[789/1500]  | LR: 0.023501 | E:  -58.796455 | E_var:     5.3833 | E_err:   0.027377
[2025-10-30 04:00:00] [Iter  866/1575] R0[790/1500]  | LR: 0.023450 | E:  -58.821563 | E_var:     5.7872 | E_err:   0.027155
[2025-10-30 04:00:17] [Iter  867/1575] R0[791/1500]  | LR: 0.023399 | E:  -58.835172 | E_var:     5.6989 | E_err:   0.027823
[2025-10-30 04:00:33] [Iter  868/1575] R0[792/1500]  | LR: 0.023348 | E:  -58.846033 | E_var:     5.6419 | E_err:   0.025726
[2025-10-30 04:00:50] [Iter  869/1575] R0[793/1500]  | LR: 0.023297 | E:  -58.887570 | E_var:     5.5981 | E_err:   0.027713
[2025-10-30 04:01:06] [Iter  870/1575] R0[794/1500]  | LR: 0.023245 | E:  -58.879853 | E_var:     5.2759 | E_err:   0.025450
[2025-10-30 04:01:23] [Iter  871/1575] R0[795/1500]  | LR: 0.023194 | E:  -58.872901 | E_var:     5.2339 | E_err:   0.025661
[2025-10-30 04:01:39] [Iter  872/1575] R0[796/1500]  | LR: 0.023143 | E:  -58.885505 | E_var:     5.2852 | E_err:   0.025456
[2025-10-30 04:01:56] [Iter  873/1575] R0[797/1500]  | LR: 0.023092 | E:  -58.836147 | E_var:     5.5610 | E_err:   0.027898
[2025-10-30 04:02:13] [Iter  874/1575] R0[798/1500]  | LR: 0.023041 | E:  -58.895646 | E_var:     5.3709 | E_err:   0.025187
[2025-10-30 04:02:29] [Iter  875/1575] R0[799/1500]  | LR: 0.022990 | E:  -58.848987 | E_var:     5.2335 | E_err:   0.025725
[2025-10-30 04:02:46] [Iter  876/1575] R0[800/1500]  | LR: 0.022939 | E:  -58.872976 | E_var:     5.3540 | E_err:   0.025824
[2025-10-30 04:03:02] [Iter  877/1575] R0[801/1500]  | LR: 0.022888 | E:  -58.851068 | E_var:     5.2364 | E_err:   0.025443
[2025-10-30 04:03:19] [Iter  878/1575] R0[802/1500]  | LR: 0.022837 | E:  -58.863777 | E_var:     5.3642 | E_err:   0.026103
[2025-10-30 04:03:35] [Iter  879/1575] R0[803/1500]  | LR: 0.022786 | E:  -58.878023 | E_var:     5.1429 | E_err:   0.024803
[2025-10-30 04:03:52] [Iter  880/1575] R0[804/1500]  | LR: 0.022735 | E:  -58.858093 | E_var:     5.3960 | E_err:   0.027501
[2025-10-30 04:04:08] [Iter  881/1575] R0[805/1500]  | LR: 0.022684 | E:  -58.823188 | E_var:     5.4439 | E_err:   0.025996
[2025-10-30 04:04:25] [Iter  882/1575] R0[806/1500]  | LR: 0.022633 | E:  -58.867749 | E_var:     5.2368 | E_err:   0.027425
[2025-10-30 04:04:42] [Iter  883/1575] R0[807/1500]  | LR: 0.022582 | E:  -58.901539 | E_var:     5.3075 | E_err:   0.027344
[2025-10-30 04:04:58] [Iter  884/1575] R0[808/1500]  | LR: 0.022531 | E:  -58.847038 | E_var:     5.2242 | E_err:   0.025135
[2025-10-30 04:05:15] [Iter  885/1575] R0[809/1500]  | LR: 0.022480 | E:  -58.829359 | E_var:     5.4997 | E_err:   0.026213
[2025-10-30 04:05:31] [Iter  886/1575] R0[810/1500]  | LR: 0.022429 | E:  -58.827746 | E_var:     5.3733 | E_err:   0.026555
[2025-10-30 04:05:48] [Iter  887/1575] R0[811/1500]  | LR: 0.022378 | E:  -58.823015 | E_var:     5.5029 | E_err:   0.026171
[2025-10-30 04:06:04] [Iter  888/1575] R0[812/1500]  | LR: 0.022328 | E:  -58.826006 | E_var:     5.3688 | E_err:   0.026415
[2025-10-30 04:06:21] [Iter  889/1575] R0[813/1500]  | LR: 0.022277 | E:  -58.827476 | E_var:     5.3200 | E_err:   0.025678
[2025-10-30 04:06:37] [Iter  890/1575] R0[814/1500]  | LR: 0.022226 | E:  -58.806266 | E_var:     5.4493 | E_err:   0.026189
[2025-10-30 04:06:54] [Iter  891/1575] R0[815/1500]  | LR: 0.022175 | E:  -58.868659 | E_var:     5.3217 | E_err:   0.027046
[2025-10-30 04:07:11] [Iter  892/1575] R0[816/1500]  | LR: 0.022124 | E:  -58.855786 | E_var:     5.5176 | E_err:   0.025629
[2025-10-30 04:07:27] [Iter  893/1575] R0[817/1500]  | LR: 0.022073 | E:  -58.879952 | E_var:     5.6159 | E_err:   0.026309
[2025-10-30 04:07:44] [Iter  894/1575] R0[818/1500]  | LR: 0.022023 | E:  -58.842800 | E_var:     5.7073 | E_err:   0.027408
[2025-10-30 04:08:00] [Iter  895/1575] R0[819/1500]  | LR: 0.021972 | E:  -58.936963 | E_var:     5.5283 | E_err:   0.026608
[2025-10-30 04:08:17] [Iter  896/1575] R0[820/1500]  | LR: 0.021921 | E:  -58.875927 | E_var:     5.5541 | E_err:   0.026012
[2025-10-30 04:08:33] [Iter  897/1575] R0[821/1500]  | LR: 0.021870 | E:  -58.830743 | E_var:     5.6954 | E_err:   0.027408
[2025-10-30 04:08:50] [Iter  898/1575] R0[822/1500]  | LR: 0.021819 | E:  -58.834726 | E_var:     5.5674 | E_err:   0.028349
[2025-10-30 04:09:06] [Iter  899/1575] R0[823/1500]  | LR: 0.021769 | E:  -58.910510 | E_var:     5.3848 | E_err:   0.025834
[2025-10-30 04:09:23] [Iter  900/1575] R0[824/1500]  | LR: 0.021718 | E:  -58.870371 | E_var:     5.4901 | E_err:   0.027938
[2025-10-30 04:09:40] [Iter  901/1575] R0[825/1500]  | LR: 0.021667 | E:  -58.857698 | E_var:     5.2791 | E_err:   0.027403
[2025-10-30 04:09:56] [Iter  902/1575] R0[826/1500]  | LR: 0.021617 | E:  -58.853275 | E_var:     5.3278 | E_err:   0.025080
[2025-10-30 04:10:13] [Iter  903/1575] R0[827/1500]  | LR: 0.021566 | E:  -58.866205 | E_var:     5.4967 | E_err:   0.025740
[2025-10-30 04:10:29] [Iter  904/1575] R0[828/1500]  | LR: 0.021515 | E:  -58.856921 | E_var:     5.3425 | E_err:   0.025634
[2025-10-30 04:10:46] [Iter  905/1575] R0[829/1500]  | LR: 0.021465 | E:  -58.875548 | E_var:     5.4680 | E_err:   0.026756
[2025-10-30 04:11:02] [Iter  906/1575] R0[830/1500]  | LR: 0.021414 | E:  -58.836083 | E_var:     5.5131 | E_err:   0.025207
[2025-10-30 04:11:19] [Iter  907/1575] R0[831/1500]  | LR: 0.021364 | E:  -58.843732 | E_var:     5.6994 | E_err:   0.027365
[2025-10-30 04:11:35] [Iter  908/1575] R0[832/1500]  | LR: 0.021313 | E:  -58.863316 | E_var:     5.7585 | E_err:   0.026405
[2025-10-30 04:11:52] [Iter  909/1575] R0[833/1500]  | LR: 0.021262 | E:  -58.831016 | E_var:     5.4501 | E_err:   0.024719
[2025-10-30 04:12:09] [Iter  910/1575] R0[834/1500]  | LR: 0.021212 | E:  -58.844749 | E_var:     5.4631 | E_err:   0.026468
[2025-10-30 04:12:25] [Iter  911/1575] R0[835/1500]  | LR: 0.021161 | E:  -58.836224 | E_var:     5.4986 | E_err:   0.027567
[2025-10-30 04:12:42] [Iter  912/1575] R0[836/1500]  | LR: 0.021111 | E:  -58.833032 | E_var:     5.3856 | E_err:   0.024989
[2025-10-30 04:12:58] [Iter  913/1575] R0[837/1500]  | LR: 0.021060 | E:  -58.876728 | E_var:     5.4114 | E_err:   0.026002
[2025-10-30 04:13:15] [Iter  914/1575] R0[838/1500]  | LR: 0.021010 | E:  -58.820217 | E_var:     5.3330 | E_err:   0.026795
[2025-10-30 04:13:31] [Iter  915/1575] R0[839/1500]  | LR: 0.020960 | E:  -58.860829 | E_var:     5.3616 | E_err:   0.026097
[2025-10-30 04:13:48] [Iter  916/1575] R0[840/1500]  | LR: 0.020909 | E:  -58.891613 | E_var:     5.2880 | E_err:   0.026148
[2025-10-30 04:14:04] [Iter  917/1575] R0[841/1500]  | LR: 0.020859 | E:  -58.848636 | E_var:     5.1523 | E_err:   0.024625
[2025-10-30 04:14:21] [Iter  918/1575] R0[842/1500]  | LR: 0.020808 | E:  -58.875806 | E_var:     5.6229 | E_err:   0.027485
[2025-10-30 04:14:38] [Iter  919/1575] R0[843/1500]  | LR: 0.020758 | E:  -58.852527 | E_var:     5.5170 | E_err:   0.026637
[2025-10-30 04:14:54] [Iter  920/1575] R0[844/1500]  | LR: 0.020708 | E:  -58.908658 | E_var:     5.4403 | E_err:   0.025688
[2025-10-30 04:15:11] [Iter  921/1575] R0[845/1500]  | LR: 0.020657 | E:  -58.890414 | E_var:     5.2821 | E_err:   0.025341
[2025-10-30 04:15:27] [Iter  922/1575] R0[846/1500]  | LR: 0.020607 | E:  -58.894310 | E_var:     5.4244 | E_err:   0.026568
[2025-10-30 04:15:44] [Iter  923/1575] R0[847/1500]  | LR: 0.020557 | E:  -58.899849 | E_var:     5.3301 | E_err:   0.027193
[2025-10-30 04:16:00] [Iter  924/1575] R0[848/1500]  | LR: 0.020507 | E:  -58.826615 | E_var:     5.1740 | E_err:   0.025927
[2025-10-30 04:16:17] [Iter  925/1575] R0[849/1500]  | LR: 0.020456 | E:  -58.747705 | E_var:     5.2041 | E_err:   0.024368
[2025-10-30 04:16:33] [Iter  926/1575] R0[850/1500]  | LR: 0.020406 | E:  -58.799041 | E_var:     5.2770 | E_err:   0.026213
[2025-10-30 04:16:50] [Iter  927/1575] R0[851/1500]  | LR: 0.020356 | E:  -58.873160 | E_var:     5.4122 | E_err:   0.025844
[2025-10-30 04:17:06] [Iter  928/1575] R0[852/1500]  | LR: 0.020306 | E:  -58.848985 | E_var:     5.0432 | E_err:   0.026404
[2025-10-30 04:17:23] [Iter  929/1575] R0[853/1500]  | LR: 0.020256 | E:  -58.903234 | E_var:     5.1808 | E_err:   0.025773
[2025-10-30 04:17:39] [Iter  930/1575] R0[854/1500]  | LR: 0.020206 | E:  -58.807277 | E_var:     5.2513 | E_err:   0.025543
[2025-10-30 04:17:56] [Iter  931/1575] R0[855/1500]  | LR: 0.020155 | E:  -58.821924 | E_var:     5.2848 | E_err:   0.026095
[2025-10-30 04:18:13] [Iter  932/1575] R0[856/1500]  | LR: 0.020105 | E:  -58.733937 | E_var:     5.3529 | E_err:   0.025472
[2025-10-30 04:18:29] [Iter  933/1575] R0[857/1500]  | LR: 0.020055 | E:  -58.828973 | E_var:     5.3083 | E_err:   0.025681
[2025-10-30 04:18:46] [Iter  934/1575] R0[858/1500]  | LR: 0.020005 | E:  -58.812434 | E_var:     5.1025 | E_err:   0.025973
[2025-10-30 04:19:02] [Iter  935/1575] R0[859/1500]  | LR: 0.019955 | E:  -58.851992 | E_var:     5.2543 | E_err:   0.025652
[2025-10-30 04:19:19] [Iter  936/1575] R0[860/1500]  | LR: 0.019905 | E:  -58.855968 | E_var:     5.2399 | E_err:   0.026270
[2025-10-30 04:19:35] [Iter  937/1575] R0[861/1500]  | LR: 0.019855 | E:  -58.896736 | E_var:     5.2845 | E_err:   0.025759
[2025-10-30 04:19:52] [Iter  938/1575] R0[862/1500]  | LR: 0.019806 | E:  -58.931300 | E_var:     5.2586 | E_err:   0.024848
[2025-10-30 04:20:08] [Iter  939/1575] R0[863/1500]  | LR: 0.019756 | E:  -58.893371 | E_var:     4.9884 | E_err:   0.025827
[2025-10-30 04:20:25] [Iter  940/1575] R0[864/1500]  | LR: 0.019706 | E:  -58.896651 | E_var:     5.2421 | E_err:   0.025540
[2025-10-30 04:20:42] [Iter  941/1575] R0[865/1500]  | LR: 0.019656 | E:  -58.908552 | E_var:     5.2680 | E_err:   0.025656
[2025-10-30 04:20:58] [Iter  942/1575] R0[866/1500]  | LR: 0.019606 | E:  -58.897518 | E_var:     5.1096 | E_err:   0.025895
[2025-10-30 04:21:15] [Iter  943/1575] R0[867/1500]  | LR: 0.019556 | E:  -58.894847 | E_var:     5.1783 | E_err:   0.026834
[2025-10-30 04:21:31] [Iter  944/1575] R0[868/1500]  | LR: 0.019507 | E:  -58.879831 | E_var:     5.2061 | E_err:   0.025902
[2025-10-30 04:21:48] [Iter  945/1575] R0[869/1500]  | LR: 0.019457 | E:  -58.918239 | E_var:     5.0999 | E_err:   0.025846
[2025-10-30 04:22:04] [Iter  946/1575] R0[870/1500]  | LR: 0.019407 | E:  -58.921891 | E_var:     4.9834 | E_err:   0.024726
[2025-10-30 04:22:21] [Iter  947/1575] R0[871/1500]  | LR: 0.019357 | E:  -58.898827 | E_var:     5.1603 | E_err:   0.026243
[2025-10-30 04:22:37] [Iter  948/1575] R0[872/1500]  | LR: 0.019308 | E:  -58.888594 | E_var:     5.1506 | E_err:   0.026011
[2025-10-30 04:22:54] [Iter  949/1575] R0[873/1500]  | LR: 0.019258 | E:  -58.893604 | E_var:     5.1056 | E_err:   0.025191
[2025-10-30 04:23:10] [Iter  950/1575] R0[874/1500]  | LR: 0.019209 | E:  -58.935927 | E_var:     5.0313 | E_err:   0.025590
[2025-10-30 04:23:27] [Iter  951/1575] R0[875/1500]  | LR: 0.019159 | E:  -58.907320 | E_var:     5.1150 | E_err:   0.025975
[2025-10-30 04:23:44] [Iter  952/1575] R0[876/1500]  | LR: 0.019109 | E:  -58.943534 | E_var:     5.3228 | E_err:   0.025249
[2025-10-30 04:24:00] [Iter  953/1575] R0[877/1500]  | LR: 0.019060 | E:  -58.910748 | E_var:     5.0974 | E_err:   0.025759
[2025-10-30 04:24:17] [Iter  954/1575] R0[878/1500]  | LR: 0.019010 | E:  -58.905275 | E_var:     5.0637 | E_err:   0.025278
[2025-10-30 04:24:33] [Iter  955/1575] R0[879/1500]  | LR: 0.018961 | E:  -58.876269 | E_var:     5.3310 | E_err:   0.024550
[2025-10-30 04:24:50] [Iter  956/1575] R0[880/1500]  | LR: 0.018911 | E:  -58.954201 | E_var:     5.1600 | E_err:   0.025140
[2025-10-30 04:25:06] [Iter  957/1575] R0[881/1500]  | LR: 0.018862 | E:  -58.849426 | E_var:     5.1154 | E_err:   0.025795
[2025-10-30 04:25:23] [Iter  958/1575] R0[882/1500]  | LR: 0.018813 | E:  -58.876733 | E_var:     5.0150 | E_err:   0.025255
[2025-10-30 04:25:39] [Iter  959/1575] R0[883/1500]  | LR: 0.018763 | E:  -58.936318 | E_var:     5.0286 | E_err:   0.025533
[2025-10-30 04:25:56] [Iter  960/1575] R0[884/1500]  | LR: 0.018714 | E:  -58.947649 | E_var:     4.8603 | E_err:   0.024654
[2025-10-30 04:26:13] [Iter  961/1575] R0[885/1500]  | LR: 0.018665 | E:  -58.872416 | E_var:     4.9608 | E_err:   0.025883
[2025-10-30 04:26:29] [Iter  962/1575] R0[886/1500]  | LR: 0.018615 | E:  -58.907284 | E_var:     5.3706 | E_err:   0.027352
[2025-10-30 04:26:46] [Iter  963/1575] R0[887/1500]  | LR: 0.018566 | E:  -58.880534 | E_var:     5.5879 | E_err:   0.025730
[2025-10-30 04:27:02] [Iter  964/1575] R0[888/1500]  | LR: 0.018517 | E:  -58.941227 | E_var:     5.2511 | E_err:   0.027212
[2025-10-30 04:27:19] [Iter  965/1575] R0[889/1500]  | LR: 0.018468 | E:  -58.879811 | E_var:     5.1056 | E_err:   0.025547
[2025-10-30 04:27:35] [Iter  966/1575] R0[890/1500]  | LR: 0.018419 | E:  -58.870880 | E_var:     5.2484 | E_err:   0.027671
[2025-10-30 04:27:52] [Iter  967/1575] R0[891/1500]  | LR: 0.018370 | E:  -58.924387 | E_var:     5.3884 | E_err:   0.026737
[2025-10-30 04:28:08] [Iter  968/1575] R0[892/1500]  | LR: 0.018321 | E:  -58.870192 | E_var:     5.3622 | E_err:   0.026646
[2025-10-30 04:28:25] [Iter  969/1575] R0[893/1500]  | LR: 0.018271 | E:  -58.947689 | E_var:     5.2851 | E_err:   0.026316
[2025-10-30 04:28:42] [Iter  970/1575] R0[894/1500]  | LR: 0.018222 | E:  -58.952808 | E_var:     5.2929 | E_err:   0.026231
[2025-10-30 04:28:58] [Iter  971/1575] R0[895/1500]  | LR: 0.018174 | E:  -58.890757 | E_var:     5.4328 | E_err:   0.026172
[2025-10-30 04:29:15] [Iter  972/1575] R0[896/1500]  | LR: 0.018125 | E:  -58.931469 | E_var:     5.6067 | E_err:   0.026242
[2025-10-30 04:29:31] [Iter  973/1575] R0[897/1500]  | LR: 0.018076 | E:  -58.892847 | E_var:     5.2779 | E_err:   0.026031
[2025-10-30 04:29:48] [Iter  974/1575] R0[898/1500]  | LR: 0.018027 | E:  -58.923466 | E_var:     5.8290 | E_err:   0.028251
[2025-10-30 04:30:04] [Iter  975/1575] R0[899/1500]  | LR: 0.017978 | E:  -58.880010 | E_var:     5.3645 | E_err:   0.026604
[2025-10-30 04:30:05] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-30 04:30:21] [Iter  976/1575] R0[900/1500]  | LR: 0.017929 | E:  -58.888017 | E_var:     5.5539 | E_err:   0.026972
[2025-10-30 04:30:38] [Iter  977/1575] R0[901/1500]  | LR: 0.017880 | E:  -58.873842 | E_var:     5.7341 | E_err:   0.029442
[2025-10-30 04:30:54] [Iter  978/1575] R0[902/1500]  | LR: 0.017832 | E:  -58.911189 | E_var:     5.4849 | E_err:   0.025875
[2025-10-30 04:31:11] [Iter  979/1575] R0[903/1500]  | LR: 0.017783 | E:  -58.951574 | E_var:     5.4020 | E_err:   0.025145
[2025-10-30 04:31:27] [Iter  980/1575] R0[904/1500]  | LR: 0.017734 | E:  -58.961706 | E_var:     5.2725 | E_err:   0.024798
[2025-10-30 04:31:44] [Iter  981/1575] R0[905/1500]  | LR: 0.017685 | E:  -58.896021 | E_var:     5.3732 | E_err:   0.026336
[2025-10-30 04:32:00] [Iter  982/1575] R0[906/1500]  | LR: 0.017637 | E:  -58.896251 | E_var:     5.2626 | E_err:   0.025407
[2025-10-30 04:32:17] [Iter  983/1575] R0[907/1500]  | LR: 0.017588 | E:  -58.868049 | E_var:     5.4064 | E_err:   0.027244
[2025-10-30 04:32:34] [Iter  984/1575] R0[908/1500]  | LR: 0.017540 | E:  -58.922740 | E_var:     5.4039 | E_err:   0.025764
[2025-10-30 04:32:50] [Iter  985/1575] R0[909/1500]  | LR: 0.017491 | E:  -58.926867 | E_var:     5.1237 | E_err:   0.025804
[2025-10-30 04:33:07] [Iter  986/1575] R0[910/1500]  | LR: 0.017443 | E:  -58.920615 | E_var:     5.4648 | E_err:   0.026967
[2025-10-30 04:33:23] [Iter  987/1575] R0[911/1500]  | LR: 0.017394 | E:  -58.970936 | E_var:     5.2574 | E_err:   0.025647
[2025-10-30 04:33:40] [Iter  988/1575] R0[912/1500]  | LR: 0.017346 | E:  -58.921450 | E_var:     5.3747 | E_err:   0.027008
[2025-10-30 04:33:56] [Iter  989/1575] R0[913/1500]  | LR: 0.017298 | E:  -58.936184 | E_var:     5.2861 | E_err:   0.024966
[2025-10-30 04:34:13] [Iter  990/1575] R0[914/1500]  | LR: 0.017249 | E:  -58.885496 | E_var:     5.4753 | E_err:   0.025383
[2025-10-30 04:34:29] [Iter  991/1575] R0[915/1500]  | LR: 0.017201 | E:  -58.931637 | E_var:     5.4804 | E_err:   0.026742
[2025-10-30 04:34:46] [Iter  992/1575] R0[916/1500]  | LR: 0.017153 | E:  -58.915314 | E_var:     5.4376 | E_err:   0.026590
[2025-10-30 04:35:03] [Iter  993/1575] R0[917/1500]  | LR: 0.017104 | E:  -58.922450 | E_var:     5.3940 | E_err:   0.026586
[2025-10-30 04:35:19] [Iter  994/1575] R0[918/1500]  | LR: 0.017056 | E:  -58.901883 | E_var:     5.4472 | E_err:   0.026447
[2025-10-30 04:35:36] [Iter  995/1575] R0[919/1500]  | LR: 0.017008 | E:  -58.893293 | E_var:     5.4027 | E_err:   0.026233
[2025-10-30 04:35:52] [Iter  996/1575] R0[920/1500]  | LR: 0.016960 | E:  -58.905849 | E_var:     5.5070 | E_err:   0.027293
[2025-10-30 04:36:09] [Iter  997/1575] R0[921/1500]  | LR: 0.016912 | E:  -58.872128 | E_var:     5.5822 | E_err:   0.027179
[2025-10-30 04:36:25] [Iter  998/1575] R0[922/1500]  | LR: 0.016864 | E:  -58.935427 | E_var:     5.5740 | E_err:   0.026359
[2025-10-30 04:36:42] [Iter  999/1575] R0[923/1500]  | LR: 0.016816 | E:  -58.857830 | E_var:     5.4299 | E_err:   0.025694
[2025-10-30 04:36:58] [Iter 1000/1575] R0[924/1500]  | LR: 0.016768 | E:  -58.890890 | E_var:     5.4427 | E_err:   0.025156
[2025-10-30 04:37:15] [Iter 1001/1575] R0[925/1500]  | LR: 0.016720 | E:  -58.947821 | E_var:     5.3423 | E_err:   0.025852
[2025-10-30 04:37:31] [Iter 1002/1575] R0[926/1500]  | LR: 0.016672 | E:  -58.882099 | E_var:     5.4638 | E_err:   0.025745
[2025-10-30 04:37:48] [Iter 1003/1575] R0[927/1500]  | LR: 0.016624 | E:  -58.942056 | E_var:     4.9204 | E_err:   0.025055
[2025-10-30 04:38:05] [Iter 1004/1575] R0[928/1500]  | LR: 0.016576 | E:  -58.932803 | E_var:     5.0096 | E_err:   0.025262
[2025-10-30 04:38:21] [Iter 1005/1575] R0[929/1500]  | LR: 0.016529 | E:  -58.900585 | E_var:     5.0913 | E_err:   0.024588
[2025-10-30 04:38:38] [Iter 1006/1575] R0[930/1500]  | LR: 0.016481 | E:  -58.901425 | E_var:     5.0691 | E_err:   0.025815
[2025-10-30 04:38:54] [Iter 1007/1575] R0[931/1500]  | LR: 0.016433 | E:  -58.948837 | E_var:     5.1900 | E_err:   0.024833
[2025-10-30 04:39:11] [Iter 1008/1575] R0[932/1500]  | LR: 0.016386 | E:  -58.951508 | E_var:     5.1599 | E_err:   0.026317
[2025-10-30 04:39:27] [Iter 1009/1575] R0[933/1500]  | LR: 0.016338 | E:  -58.937405 | E_var:     5.5016 | E_err:   0.025291
[2025-10-30 04:39:44] [Iter 1010/1575] R0[934/1500]  | LR: 0.016290 | E:  -58.883370 | E_var:     5.1234 | E_err:   0.024802
[2025-10-30 04:40:01] [Iter 1011/1575] R0[935/1500]  | LR: 0.016243 | E:  -58.934049 | E_var:     5.4476 | E_err:   0.025469
[2025-10-30 04:40:17] [Iter 1012/1575] R0[936/1500]  | LR: 0.016195 | E:  -58.937489 | E_var:     5.3235 | E_err:   0.027267
[2025-10-30 04:40:34] [Iter 1013/1575] R0[937/1500]  | LR: 0.016148 | E:  -58.903943 | E_var:     5.2932 | E_err:   0.025073
[2025-10-30 04:40:50] [Iter 1014/1575] R0[938/1500]  | LR: 0.016101 | E:  -58.928393 | E_var:     5.2465 | E_err:   0.025276
[2025-10-30 04:41:07] [Iter 1015/1575] R0[939/1500]  | LR: 0.016053 | E:  -58.864652 | E_var:     5.2975 | E_err:   0.026292
[2025-10-30 04:41:23] [Iter 1016/1575] R0[940/1500]  | LR: 0.016006 | E:  -58.913205 | E_var:     5.3324 | E_err:   0.026749
[2025-10-30 04:41:40] [Iter 1017/1575] R0[941/1500]  | LR: 0.015959 | E:  -58.927457 | E_var:     5.1774 | E_err:   0.024736
[2025-10-30 04:41:56] [Iter 1018/1575] R0[942/1500]  | LR: 0.015911 | E:  -58.891323 | E_var:     5.4315 | E_err:   0.025353
[2025-10-30 04:42:13] [Iter 1019/1575] R0[943/1500]  | LR: 0.015864 | E:  -58.921990 | E_var:     5.3906 | E_err:   0.026638
[2025-10-30 04:42:29] [Iter 1020/1575] R0[944/1500]  | LR: 0.015817 | E:  -58.907438 | E_var:     5.1939 | E_err:   0.025094
[2025-10-30 04:42:46] [Iter 1021/1575] R0[945/1500]  | LR: 0.015770 | E:  -58.889916 | E_var:     5.2684 | E_err:   0.026814
[2025-10-30 04:43:03] [Iter 1022/1575] R0[946/1500]  | LR: 0.015723 | E:  -58.916385 | E_var:     5.1942 | E_err:   0.026121
[2025-10-30 04:43:19] [Iter 1023/1575] R0[947/1500]  | LR: 0.015676 | E:  -58.885119 | E_var:     5.3845 | E_err:   0.027063
[2025-10-30 04:43:36] [Iter 1024/1575] R0[948/1500]  | LR: 0.015629 | E:  -58.898160 | E_var:     5.0255 | E_err:   0.025706
[2025-10-30 04:43:52] [Iter 1025/1575] R0[949/1500]  | LR: 0.015582 | E:  -58.920824 | E_var:     5.2782 | E_err:   0.026717
[2025-10-30 04:44:09] [Iter 1026/1575] R0[950/1500]  | LR: 0.015535 | E:  -58.926813 | E_var:     5.3534 | E_err:   0.024735
[2025-10-30 04:44:25] [Iter 1027/1575] R0[951/1500]  | LR: 0.015488 | E:  -58.903792 | E_var:     5.3175 | E_err:   0.024791
[2025-10-30 04:44:42] [Iter 1028/1575] R0[952/1500]  | LR: 0.015441 | E:  -58.955397 | E_var:     5.3201 | E_err:   0.026576
[2025-10-30 04:44:58] [Iter 1029/1575] R0[953/1500]  | LR: 0.015395 | E:  -58.912895 | E_var:     5.2120 | E_err:   0.025125
[2025-10-30 04:45:15] [Iter 1030/1575] R0[954/1500]  | LR: 0.015348 | E:  -58.980028 | E_var:     5.1587 | E_err:   0.024453
[2025-10-30 04:45:31] [Iter 1031/1575] R0[955/1500]  | LR: 0.015301 | E:  -58.921332 | E_var:     5.3942 | E_err:   0.025863
[2025-10-30 04:45:48] [Iter 1032/1575] R0[956/1500]  | LR: 0.015254 | E:  -58.943892 | E_var:     5.0875 | E_err:   0.026466
[2025-10-30 04:46:05] [Iter 1033/1575] R0[957/1500]  | LR: 0.015208 | E:  -58.936207 | E_var:     5.0987 | E_err:   0.025908
[2025-10-30 04:46:21] [Iter 1034/1575] R0[958/1500]  | LR: 0.015161 | E:  -58.934199 | E_var:     4.9991 | E_err:   0.024659
[2025-10-30 04:46:38] [Iter 1035/1575] R0[959/1500]  | LR: 0.015115 | E:  -58.923762 | E_var:     5.1924 | E_err:   0.026115
[2025-10-30 04:46:54] [Iter 1036/1575] R0[960/1500]  | LR: 0.015068 | E:  -58.892269 | E_var:     5.1628 | E_err:   0.025553
[2025-10-30 04:47:11] [Iter 1037/1575] R0[961/1500]  | LR: 0.015022 | E:  -58.936604 | E_var:     5.0510 | E_err:   0.026220
[2025-10-30 04:47:27] [Iter 1038/1575] R0[962/1500]  | LR: 0.014976 | E:  -58.925696 | E_var:     5.0555 | E_err:   0.024711
[2025-10-30 04:47:44] [Iter 1039/1575] R0[963/1500]  | LR: 0.014929 | E:  -58.955271 | E_var:     5.2929 | E_err:   0.024391
[2025-10-30 04:48:00] [Iter 1040/1575] R0[964/1500]  | LR: 0.014883 | E:  -58.888129 | E_var:     5.1548 | E_err:   0.025690
[2025-10-30 04:48:17] [Iter 1041/1575] R0[965/1500]  | LR: 0.014837 | E:  -58.890949 | E_var:     5.1123 | E_err:   0.026242
[2025-10-30 04:48:33] [Iter 1042/1575] R0[966/1500]  | LR: 0.014791 | E:  -58.883158 | E_var:     4.9806 | E_err:   0.025466
[2025-10-30 04:48:50] [Iter 1043/1575] R0[967/1500]  | LR: 0.014745 | E:  -58.908785 | E_var:     5.0146 | E_err:   0.024708
[2025-10-30 04:49:07] [Iter 1044/1575] R0[968/1500]  | LR: 0.014698 | E:  -58.948191 | E_var:     5.0937 | E_err:   0.023616
[2025-10-30 04:49:23] [Iter 1045/1575] R0[969/1500]  | LR: 0.014652 | E:  -58.872922 | E_var:     5.1572 | E_err:   0.025898
[2025-10-30 04:49:40] [Iter 1046/1575] R0[970/1500]  | LR: 0.014606 | E:  -58.947647 | E_var:     5.1527 | E_err:   0.026637
[2025-10-30 04:49:56] [Iter 1047/1575] R0[971/1500]  | LR: 0.014561 | E:  -58.965891 | E_var:     5.2561 | E_err:   0.025413
[2025-10-30 04:50:13] [Iter 1048/1575] R0[972/1500]  | LR: 0.014515 | E:  -58.955767 | E_var:     5.0629 | E_err:   0.025656
[2025-10-30 04:50:29] [Iter 1049/1575] R0[973/1500]  | LR: 0.014469 | E:  -58.985115 | E_var:     4.9958 | E_err:   0.025228
[2025-10-30 04:50:46] [Iter 1050/1575] R0[974/1500]  | LR: 0.014423 | E:  -58.891793 | E_var:     4.9675 | E_err:   0.025749
[2025-10-30 04:51:02] [Iter 1051/1575] R0[975/1500]  | LR: 0.014377 | E:  -58.948198 | E_var:     5.0684 | E_err:   0.024954
[2025-10-30 04:51:19] [Iter 1052/1575] R0[976/1500]  | LR: 0.014332 | E:  -58.928857 | E_var:     5.2487 | E_err:   0.024935
[2025-10-30 04:51:36] [Iter 1053/1575] R0[977/1500]  | LR: 0.014286 | E:  -58.916641 | E_var:     5.0392 | E_err:   0.025486
[2025-10-30 04:51:52] [Iter 1054/1575] R0[978/1500]  | LR: 0.014240 | E:  -58.934749 | E_var:     5.2358 | E_err:   0.025947
[2025-10-30 04:52:09] [Iter 1055/1575] R0[979/1500]  | LR: 0.014195 | E:  -58.954411 | E_var:     5.1633 | E_err:   0.025015
[2025-10-30 04:52:25] [Iter 1056/1575] R0[980/1500]  | LR: 0.014149 | E:  -58.979667 | E_var:     4.9223 | E_err:   0.024562
[2025-10-30 04:52:42] [Iter 1057/1575] R0[981/1500]  | LR: 0.014104 | E:  -58.935100 | E_var:     5.3098 | E_err:   0.026480
[2025-10-30 04:52:58] [Iter 1058/1575] R0[982/1500]  | LR: 0.014058 | E:  -58.969420 | E_var:     5.0404 | E_err:   0.025185
[2025-10-30 04:53:15] [Iter 1059/1575] R0[983/1500]  | LR: 0.014013 | E:  -58.946664 | E_var:     5.1002 | E_err:   0.024328
[2025-10-30 04:53:31] [Iter 1060/1575] R0[984/1500]  | LR: 0.013968 | E:  -58.940917 | E_var:     5.0500 | E_err:   0.024999
[2025-10-30 04:53:48] [Iter 1061/1575] R0[985/1500]  | LR: 0.013923 | E:  -58.965463 | E_var:     5.1494 | E_err:   0.026327
[2025-10-30 04:54:05] [Iter 1062/1575] R0[986/1500]  | LR: 0.013877 | E:  -58.904145 | E_var:     5.2045 | E_err:   0.026108
[2025-10-30 04:54:21] [Iter 1063/1575] R0[987/1500]  | LR: 0.013832 | E:  -58.996709 | E_var:     4.9838 | E_err:   0.025320
[2025-10-30 04:54:38] [Iter 1064/1575] R0[988/1500]  | LR: 0.013787 | E:  -58.925631 | E_var:     5.0760 | E_err:   0.025262
[2025-10-30 04:54:54] [Iter 1065/1575] R0[989/1500]  | LR: 0.013742 | E:  -59.003330 | E_var:     5.1767 | E_err:   0.026234
[2025-10-30 04:55:11] [Iter 1066/1575] R0[990/1500]  | LR: 0.013697 | E:  -58.884560 | E_var:     5.1069 | E_err:   0.024846
[2025-10-30 04:55:27] [Iter 1067/1575] R0[991/1500]  | LR: 0.013652 | E:  -58.948481 | E_var:     5.1028 | E_err:   0.025799
[2025-10-30 04:55:44] [Iter 1068/1575] R0[992/1500]  | LR: 0.013607 | E:  -58.961224 | E_var:     5.1607 | E_err:   0.025822
[2025-10-30 04:56:00] [Iter 1069/1575] R0[993/1500]  | LR: 0.013562 | E:  -58.917702 | E_var:     5.0348 | E_err:   0.024806
[2025-10-30 04:56:17] [Iter 1070/1575] R0[994/1500]  | LR: 0.013518 | E:  -58.931344 | E_var:     5.2339 | E_err:   0.025253
[2025-10-30 04:56:33] [Iter 1071/1575] R0[995/1500]  | LR: 0.013473 | E:  -58.962009 | E_var:     5.2041 | E_err:   0.025039
[2025-10-30 04:56:50] [Iter 1072/1575] R0[996/1500]  | LR: 0.013428 | E:  -58.950640 | E_var:     5.1602 | E_err:   0.026578
[2025-10-30 04:57:07] [Iter 1073/1575] R0[997/1500]  | LR: 0.013384 | E:  -58.900283 | E_var:     5.4059 | E_err:   0.027200
[2025-10-30 04:57:23] [Iter 1074/1575] R0[998/1500]  | LR: 0.013339 | E:  -58.951270 | E_var:     5.1208 | E_err:   0.025415
[2025-10-30 04:57:40] [Iter 1075/1575] R0[999/1500]  | LR: 0.013294 | E:  -58.902001 | E_var:     5.4452 | E_err:   0.025922
[2025-10-30 04:57:56] [Iter 1076/1575] R0[1000/1500] | LR: 0.013250 | E:  -58.926334 | E_var:     5.2148 | E_err:   0.027044
[2025-10-30 04:58:13] [Iter 1077/1575] R0[1001/1500] | LR: 0.013206 | E:  -58.941673 | E_var:     5.1683 | E_err:   0.024883
[2025-10-30 04:58:29] [Iter 1078/1575] R0[1002/1500] | LR: 0.013161 | E:  -58.953578 | E_var:     5.0534 | E_err:   0.027549
[2025-10-30 04:58:46] [Iter 1079/1575] R0[1003/1500] | LR: 0.013117 | E:  -58.969339 | E_var:     5.0506 | E_err:   0.023767
[2025-10-30 04:59:02] [Iter 1080/1575] R0[1004/1500] | LR: 0.013073 | E:  -58.957515 | E_var:     4.8290 | E_err:   0.024697
[2025-10-30 04:59:19] [Iter 1081/1575] R0[1005/1500] | LR: 0.013028 | E:  -58.940950 | E_var:     5.0708 | E_err:   0.026168
[2025-10-30 04:59:36] [Iter 1082/1575] R0[1006/1500] | LR: 0.012984 | E:  -58.977972 | E_var:     4.9635 | E_err:   0.024526
[2025-10-30 04:59:52] [Iter 1083/1575] R0[1007/1500] | LR: 0.012940 | E:  -58.913334 | E_var:     5.0104 | E_err:   0.024910
[2025-10-30 05:00:09] [Iter 1084/1575] R0[1008/1500] | LR: 0.012896 | E:  -58.942522 | E_var:     5.2382 | E_err:   0.024872
[2025-10-30 05:00:25] [Iter 1085/1575] R0[1009/1500] | LR: 0.012852 | E:  -58.944713 | E_var:     5.2371 | E_err:   0.025250
[2025-10-30 05:00:42] [Iter 1086/1575] R0[1010/1500] | LR: 0.012808 | E:  -58.970101 | E_var:     5.1209 | E_err:   0.025712
[2025-10-30 05:00:58] [Iter 1087/1575] R0[1011/1500] | LR: 0.012764 | E:  -58.964168 | E_var:     5.0898 | E_err:   0.025354
[2025-10-30 05:01:15] [Iter 1088/1575] R0[1012/1500] | LR: 0.012721 | E:  -58.951067 | E_var:     5.0652 | E_err:   0.026184
[2025-10-30 05:01:31] [Iter 1089/1575] R0[1013/1500] | LR: 0.012677 | E:  -58.988519 | E_var:     5.2539 | E_err:   0.026099
[2025-10-30 05:01:48] [Iter 1090/1575] R0[1014/1500] | LR: 0.012633 | E:  -58.925052 | E_var:     5.0631 | E_err:   0.026212
[2025-10-30 05:02:05] [Iter 1091/1575] R0[1015/1500] | LR: 0.012590 | E:  -59.002955 | E_var:     5.0944 | E_err:   0.025141
[2025-10-30 05:02:21] [Iter 1092/1575] R0[1016/1500] | LR: 0.012546 | E:  -58.969406 | E_var:     5.0346 | E_err:   0.025131
[2025-10-30 05:02:38] [Iter 1093/1575] R0[1017/1500] | LR: 0.012502 | E:  -58.974365 | E_var:     5.0795 | E_err:   0.026161
[2025-10-30 05:02:54] [Iter 1094/1575] R0[1018/1500] | LR: 0.012459 | E:  -58.957192 | E_var:     4.9283 | E_err:   0.026299
[2025-10-30 05:03:11] [Iter 1095/1575] R0[1019/1500] | LR: 0.012416 | E:  -58.959994 | E_var:     4.9561 | E_err:   0.025090
[2025-10-30 05:03:27] [Iter 1096/1575] R0[1020/1500] | LR: 0.012372 | E:  -58.937894 | E_var:     5.0647 | E_err:   0.025270
[2025-10-30 05:03:44] [Iter 1097/1575] R0[1021/1500] | LR: 0.012329 | E:  -58.969932 | E_var:     5.1288 | E_err:   0.025544
[2025-10-30 05:04:00] [Iter 1098/1575] R0[1022/1500] | LR: 0.012286 | E:  -58.961466 | E_var:     4.9965 | E_err:   0.026110
[2025-10-30 05:04:17] [Iter 1099/1575] R0[1023/1500] | LR: 0.012243 | E:  -58.989043 | E_var:     5.1426 | E_err:   0.026513
[2025-10-30 05:04:34] [Iter 1100/1575] R0[1024/1500] | LR: 0.012199 | E:  -58.968124 | E_var:     5.0649 | E_err:   0.025916
[2025-10-30 05:04:50] [Iter 1101/1575] R0[1025/1500] | LR: 0.012156 | E:  -58.966788 | E_var:     5.1331 | E_err:   0.025192
[2025-10-30 05:05:07] [Iter 1102/1575] R0[1026/1500] | LR: 0.012113 | E:  -58.989717 | E_var:     5.0228 | E_err:   0.025487
[2025-10-30 05:05:23] [Iter 1103/1575] R0[1027/1500] | LR: 0.012070 | E:  -58.981691 | E_var:     5.1255 | E_err:   0.025228
[2025-10-30 05:05:40] [Iter 1104/1575] R0[1028/1500] | LR: 0.012028 | E:  -59.009664 | E_var:     5.0624 | E_err:   0.025808
[2025-10-30 05:05:56] [Iter 1105/1575] R0[1029/1500] | LR: 0.011985 | E:  -58.960272 | E_var:     5.0376 | E_err:   0.025795
[2025-10-30 05:06:13] [Iter 1106/1575] R0[1030/1500] | LR: 0.011942 | E:  -58.962952 | E_var:     4.9113 | E_err:   0.024995
[2025-10-30 05:06:29] [Iter 1107/1575] R0[1031/1500] | LR: 0.011899 | E:  -58.934731 | E_var:     5.1136 | E_err:   0.026160
[2025-10-30 05:06:46] [Iter 1108/1575] R0[1032/1500] | LR: 0.011857 | E:  -58.974906 | E_var:     5.0408 | E_err:   0.025876
[2025-10-30 05:07:02] [Iter 1109/1575] R0[1033/1500] | LR: 0.011814 | E:  -58.932855 | E_var:     4.9959 | E_err:   0.024771
[2025-10-30 05:07:19] [Iter 1110/1575] R0[1034/1500] | LR: 0.011771 | E:  -58.946106 | E_var:     5.1224 | E_err:   0.025652
[2025-10-30 05:07:36] [Iter 1111/1575] R0[1035/1500] | LR: 0.011729 | E:  -58.969550 | E_var:     4.9761 | E_err:   0.025695
[2025-10-30 05:07:52] [Iter 1112/1575] R0[1036/1500] | LR: 0.011687 | E:  -58.970192 | E_var:     5.1218 | E_err:   0.024920
[2025-10-30 05:08:09] [Iter 1113/1575] R0[1037/1500] | LR: 0.011644 | E:  -58.971649 | E_var:     5.2509 | E_err:   0.024711
[2025-10-30 05:08:25] [Iter 1114/1575] R0[1038/1500] | LR: 0.011602 | E:  -58.953776 | E_var:     5.1074 | E_err:   0.026140
[2025-10-30 05:08:42] [Iter 1115/1575] R0[1039/1500] | LR: 0.011560 | E:  -58.998012 | E_var:     5.0071 | E_err:   0.025151
[2025-10-30 05:08:58] [Iter 1116/1575] R0[1040/1500] | LR: 0.011518 | E:  -58.962954 | E_var:     5.0310 | E_err:   0.026079
[2025-10-30 05:09:15] [Iter 1117/1575] R0[1041/1500] | LR: 0.011475 | E:  -58.991169 | E_var:     5.0356 | E_err:   0.027257
[2025-10-30 05:09:31] [Iter 1118/1575] R0[1042/1500] | LR: 0.011433 | E:  -58.967726 | E_var:     4.9009 | E_err:   0.025099
[2025-10-30 05:09:48] [Iter 1119/1575] R0[1043/1500] | LR: 0.011391 | E:  -58.931795 | E_var:     5.0462 | E_err:   0.024724
[2025-10-30 05:10:05] [Iter 1120/1575] R0[1044/1500] | LR: 0.011349 | E:  -58.966340 | E_var:     5.1071 | E_err:   0.024800
[2025-10-30 05:10:21] [Iter 1121/1575] R0[1045/1500] | LR: 0.011308 | E:  -58.940851 | E_var:     5.0810 | E_err:   0.025636
[2025-10-30 05:10:38] [Iter 1122/1575] R0[1046/1500] | LR: 0.011266 | E:  -58.972927 | E_var:     4.9787 | E_err:   0.024783
[2025-10-30 05:10:54] [Iter 1123/1575] R0[1047/1500] | LR: 0.011224 | E:  -58.971420 | E_var:     5.1478 | E_err:   0.024888
[2025-10-30 05:11:11] [Iter 1124/1575] R0[1048/1500] | LR: 0.011182 | E:  -59.002802 | E_var:     5.1648 | E_err:   0.025417
[2025-10-30 05:11:27] [Iter 1125/1575] R0[1049/1500] | LR: 0.011141 | E:  -58.988839 | E_var:     4.9544 | E_err:   0.024950
[2025-10-30 05:11:28] ✓ Checkpoint saved: checkpoint_iter_001050.pkl
[2025-10-30 05:11:44] [Iter 1126/1575] R0[1050/1500] | LR: 0.011099 | E:  -58.956334 | E_var:     5.0019 | E_err:   0.024720
[2025-10-30 05:12:01] [Iter 1127/1575] R0[1051/1500] | LR: 0.011058 | E:  -58.933333 | E_var:     4.8743 | E_err:   0.025868
[2025-10-30 05:12:17] [Iter 1128/1575] R0[1052/1500] | LR: 0.011016 | E:  -58.928874 | E_var:     5.0078 | E_err:   0.026102
[2025-10-30 05:12:34] [Iter 1129/1575] R0[1053/1500] | LR: 0.010975 | E:  -58.956764 | E_var:     5.0449 | E_err:   0.024936
[2025-10-30 05:12:50] [Iter 1130/1575] R0[1054/1500] | LR: 0.010934 | E:  -58.993934 | E_var:     4.8919 | E_err:   0.023000
[2025-10-30 05:13:07] [Iter 1131/1575] R0[1055/1500] | LR: 0.010892 | E:  -58.941689 | E_var:     4.9621 | E_err:   0.024647
[2025-10-30 05:13:23] [Iter 1132/1575] R0[1056/1500] | LR: 0.010851 | E:  -58.969286 | E_var:     5.0899 | E_err:   0.024905
[2025-10-30 05:13:40] [Iter 1133/1575] R0[1057/1500] | LR: 0.010810 | E:  -58.965592 | E_var:     4.9409 | E_err:   0.025974
[2025-10-30 05:13:57] [Iter 1134/1575] R0[1058/1500] | LR: 0.010769 | E:  -59.039441 | E_var:     4.8175 | E_err:   0.024531
[2025-10-30 05:14:13] [Iter 1135/1575] R0[1059/1500] | LR: 0.010728 | E:  -58.967920 | E_var:     4.9618 | E_err:   0.024833
[2025-10-30 05:14:30] [Iter 1136/1575] R0[1060/1500] | LR: 0.010687 | E:  -58.936943 | E_var:     4.9741 | E_err:   0.025602
[2025-10-30 05:14:46] [Iter 1137/1575] R0[1061/1500] | LR: 0.010646 | E:  -58.942684 | E_var:     5.0034 | E_err:   0.024462
[2025-10-30 05:15:03] [Iter 1138/1575] R0[1062/1500] | LR: 0.010606 | E:  -58.973341 | E_var:     5.1657 | E_err:   0.026033
[2025-10-30 05:15:19] [Iter 1139/1575] R0[1063/1500] | LR: 0.010565 | E:  -58.981608 | E_var:     5.0564 | E_err:   0.024786
[2025-10-30 05:15:36] [Iter 1140/1575] R0[1064/1500] | LR: 0.010524 | E:  -58.993613 | E_var:     4.9249 | E_err:   0.024700
[2025-10-30 05:15:52] [Iter 1141/1575] R0[1065/1500] | LR: 0.010484 | E:  -59.008912 | E_var:     4.9437 | E_err:   0.025120
[2025-10-30 05:16:09] [Iter 1142/1575] R0[1066/1500] | LR: 0.010443 | E:  -58.999296 | E_var:     5.2232 | E_err:   0.025948
[2025-10-30 05:16:26] [Iter 1143/1575] R0[1067/1500] | LR: 0.010403 | E:  -58.953105 | E_var:     4.9672 | E_err:   0.024859
[2025-10-30 05:16:42] [Iter 1144/1575] R0[1068/1500] | LR: 0.010362 | E:  -58.990737 | E_var:     5.0470 | E_err:   0.025162
[2025-10-30 05:16:59] [Iter 1145/1575] R0[1069/1500] | LR: 0.010322 | E:  -58.942669 | E_var:     4.8725 | E_err:   0.025358
[2025-10-30 05:17:15] [Iter 1146/1575] R0[1070/1500] | LR: 0.010282 | E:  -58.950528 | E_var:     4.8895 | E_err:   0.024152
[2025-10-30 05:17:32] [Iter 1147/1575] R0[1071/1500] | LR: 0.010242 | E:  -58.978135 | E_var:     4.9913 | E_err:   0.026284
[2025-10-30 05:17:48] [Iter 1148/1575] R0[1072/1500] | LR: 0.010202 | E:  -58.973318 | E_var:     4.8672 | E_err:   0.026081
[2025-10-30 05:18:05] [Iter 1149/1575] R0[1073/1500] | LR: 0.010162 | E:  -58.970382 | E_var:     4.8673 | E_err:   0.024517
[2025-10-30 05:18:21] [Iter 1150/1575] R0[1074/1500] | LR: 0.010122 | E:  -58.973357 | E_var:     4.9537 | E_err:   0.024574
[2025-10-30 05:18:38] [Iter 1151/1575] R0[1075/1500] | LR: 0.010082 | E:  -58.994374 | E_var:     4.9735 | E_err:   0.024929
[2025-10-30 05:18:55] [Iter 1152/1575] R0[1076/1500] | LR: 0.010042 | E:  -58.990699 | E_var:     4.9105 | E_err:   0.025111
[2025-10-30 05:19:11] [Iter 1153/1575] R0[1077/1500] | LR: 0.010002 | E:  -58.979973 | E_var:     4.9907 | E_err:   0.024601
[2025-10-30 05:19:28] [Iter 1154/1575] R0[1078/1500] | LR: 0.009962 | E:  -58.984423 | E_var:     5.4340 | E_err:   0.026292
[2025-10-30 05:19:44] [Iter 1155/1575] R0[1079/1500] | LR: 0.009923 | E:  -58.924389 | E_var:     5.1793 | E_err:   0.024869
[2025-10-30 05:20:01] [Iter 1156/1575] R0[1080/1500] | LR: 0.009883 | E:  -58.977376 | E_var:     5.1501 | E_err:   0.025778
[2025-10-30 05:20:17] [Iter 1157/1575] R0[1081/1500] | LR: 0.009844 | E:  -58.988173 | E_var:     5.0769 | E_err:   0.025837
[2025-10-30 05:20:34] [Iter 1158/1575] R0[1082/1500] | LR: 0.009804 | E:  -58.986869 | E_var:     5.0694 | E_err:   0.026300
[2025-10-30 05:20:50] [Iter 1159/1575] R0[1083/1500] | LR: 0.009765 | E:  -58.946605 | E_var:     4.9384 | E_err:   0.025781
[2025-10-30 05:21:07] [Iter 1160/1575] R0[1084/1500] | LR: 0.009726 | E:  -58.961955 | E_var:     5.0220 | E_err:   0.026198
[2025-10-30 05:21:24] [Iter 1161/1575] R0[1085/1500] | LR: 0.009686 | E:  -58.982131 | E_var:     5.0928 | E_err:   0.025893
[2025-10-30 05:21:40] [Iter 1162/1575] R0[1086/1500] | LR: 0.009647 | E:  -58.996337 | E_var:     4.9208 | E_err:   0.024558
[2025-10-30 05:21:57] [Iter 1163/1575] R0[1087/1500] | LR: 0.009608 | E:  -58.962531 | E_var:     4.9400 | E_err:   0.024258
[2025-10-30 05:22:13] [Iter 1164/1575] R0[1088/1500] | LR: 0.009569 | E:  -58.999757 | E_var:     4.9858 | E_err:   0.024909
[2025-10-30 05:22:30] [Iter 1165/1575] R0[1089/1500] | LR: 0.009530 | E:  -59.038437 | E_var:     5.0425 | E_err:   0.026031
[2025-10-30 05:22:46] [Iter 1166/1575] R0[1090/1500] | LR: 0.009491 | E:  -58.978524 | E_var:     4.9146 | E_err:   0.024840
[2025-10-30 05:23:03] [Iter 1167/1575] R0[1091/1500] | LR: 0.009452 | E:  -58.973177 | E_var:     5.1476 | E_err:   0.024924
[2025-10-30 05:23:20] [Iter 1168/1575] R0[1092/1500] | LR: 0.009414 | E:  -58.962410 | E_var:     5.1500 | E_err:   0.025463
[2025-10-30 05:23:36] [Iter 1169/1575] R0[1093/1500] | LR: 0.009375 | E:  -58.996345 | E_var:     5.1836 | E_err:   0.025602
[2025-10-30 05:23:53] [Iter 1170/1575] R0[1094/1500] | LR: 0.009336 | E:  -58.951999 | E_var:     5.2701 | E_err:   0.026346
[2025-10-30 05:24:09] [Iter 1171/1575] R0[1095/1500] | LR: 0.009298 | E:  -58.953963 | E_var:     5.3149 | E_err:   0.027213
[2025-10-30 05:24:26] [Iter 1172/1575] R0[1096/1500] | LR: 0.009259 | E:  -58.977169 | E_var:     5.0842 | E_err:   0.023746
[2025-10-30 05:24:42] [Iter 1173/1575] R0[1097/1500] | LR: 0.009221 | E:  -58.981152 | E_var:     5.2048 | E_err:   0.025320
[2025-10-30 05:24:59] [Iter 1174/1575] R0[1098/1500] | LR: 0.009183 | E:  -58.958995 | E_var:     5.0250 | E_err:   0.024944
[2025-10-30 05:25:15] [Iter 1175/1575] R0[1099/1500] | LR: 0.009144 | E:  -59.003002 | E_var:     4.9378 | E_err:   0.024992
[2025-10-30 05:25:32] [Iter 1176/1575] R0[1100/1500] | LR: 0.009106 | E:  -58.940816 | E_var:     4.9871 | E_err:   0.025201
[2025-10-30 05:25:49] [Iter 1177/1575] R0[1101/1500] | LR: 0.009068 | E:  -58.991270 | E_var:     4.7812 | E_err:   0.024219
[2025-10-30 05:26:05] [Iter 1178/1575] R0[1102/1500] | LR: 0.009030 | E:  -59.002085 | E_var:     4.9840 | E_err:   0.023772
[2025-10-30 05:26:22] [Iter 1179/1575] R0[1103/1500] | LR: 0.008992 | E:  -58.980389 | E_var:     4.8883 | E_err:   0.026327
[2025-10-30 05:26:38] [Iter 1180/1575] R0[1104/1500] | LR: 0.008954 | E:  -58.977345 | E_var:     4.9337 | E_err:   0.024289
[2025-10-30 05:26:55] [Iter 1181/1575] R0[1105/1500] | LR: 0.008917 | E:  -58.988092 | E_var:     5.0769 | E_err:   0.025729
[2025-10-30 05:27:11] [Iter 1182/1575] R0[1106/1500] | LR: 0.008879 | E:  -58.960776 | E_var:     5.0450 | E_err:   0.026281
[2025-10-30 05:27:28] [Iter 1183/1575] R0[1107/1500] | LR: 0.008841 | E:  -58.988363 | E_var:     5.0055 | E_err:   0.024970
[2025-10-30 05:27:44] [Iter 1184/1575] R0[1108/1500] | LR: 0.008804 | E:  -58.983067 | E_var:     5.1231 | E_err:   0.026085
[2025-10-30 05:28:01] [Iter 1185/1575] R0[1109/1500] | LR: 0.008766 | E:  -58.982865 | E_var:     4.9894 | E_err:   0.025121
[2025-10-30 05:28:18] [Iter 1186/1575] R0[1110/1500] | LR: 0.008729 | E:  -58.974756 | E_var:     4.9680 | E_err:   0.024811
[2025-10-30 05:28:34] [Iter 1187/1575] R0[1111/1500] | LR: 0.008691 | E:  -58.977001 | E_var:     4.8447 | E_err:   0.025675
[2025-10-30 05:28:51] [Iter 1188/1575] R0[1112/1500] | LR: 0.008654 | E:  -58.985776 | E_var:     5.0343 | E_err:   0.024727
[2025-10-30 05:29:07] [Iter 1189/1575] R0[1113/1500] | LR: 0.008617 | E:  -59.017022 | E_var:     4.9905 | E_err:   0.025326
[2025-10-30 05:29:24] [Iter 1190/1575] R0[1114/1500] | LR: 0.008580 | E:  -59.020584 | E_var:     4.8928 | E_err:   0.025925
[2025-10-30 05:29:40] [Iter 1191/1575] R0[1115/1500] | LR: 0.008542 | E:  -58.990827 | E_var:     4.9290 | E_err:   0.026377
[2025-10-30 05:29:57] [Iter 1192/1575] R0[1116/1500] | LR: 0.008505 | E:  -58.997167 | E_var:     5.0243 | E_err:   0.024259
[2025-10-30 05:30:13] [Iter 1193/1575] R0[1117/1500] | LR: 0.008469 | E:  -58.996861 | E_var:     4.9276 | E_err:   0.024911
[2025-10-30 05:30:30] [Iter 1194/1575] R0[1118/1500] | LR: 0.008432 | E:  -58.986301 | E_var:     5.0873 | E_err:   0.025879
[2025-10-30 05:30:47] [Iter 1195/1575] R0[1119/1500] | LR: 0.008395 | E:  -58.974969 | E_var:     4.9252 | E_err:   0.024865
[2025-10-30 05:31:03] [Iter 1196/1575] R0[1120/1500] | LR: 0.008358 | E:  -58.985230 | E_var:     5.0547 | E_err:   0.024591
[2025-10-30 05:31:20] [Iter 1197/1575] R0[1121/1500] | LR: 0.008322 | E:  -59.008710 | E_var:     5.0243 | E_err:   0.024222
[2025-10-30 05:31:36] [Iter 1198/1575] R0[1122/1500] | LR: 0.008285 | E:  -58.991519 | E_var:     5.1077 | E_err:   0.025797
[2025-10-30 05:31:53] [Iter 1199/1575] R0[1123/1500] | LR: 0.008249 | E:  -58.931154 | E_var:     5.0670 | E_err:   0.024584
[2025-10-30 05:32:09] [Iter 1200/1575] R0[1124/1500] | LR: 0.008212 | E:  -59.004570 | E_var:     5.1936 | E_err:   0.025191
[2025-10-30 05:32:26] [Iter 1201/1575] R0[1125/1500] | LR: 0.008176 | E:  -58.998257 | E_var:     5.3131 | E_err:   0.026129
[2025-10-30 05:32:43] [Iter 1202/1575] R0[1126/1500] | LR: 0.008140 | E:  -58.979097 | E_var:     5.1227 | E_err:   0.025844
[2025-10-30 05:32:59] [Iter 1203/1575] R0[1127/1500] | LR: 0.008103 | E:  -59.018060 | E_var:     5.2200 | E_err:   0.027220
[2025-10-30 05:33:16] [Iter 1204/1575] R0[1128/1500] | LR: 0.008067 | E:  -59.025012 | E_var:     5.2067 | E_err:   0.026795
[2025-10-30 05:33:32] [Iter 1205/1575] R0[1129/1500] | LR: 0.008031 | E:  -58.983099 | E_var:     5.1709 | E_err:   0.025497
[2025-10-30 05:33:49] [Iter 1206/1575] R0[1130/1500] | LR: 0.007995 | E:  -59.024147 | E_var:     4.9616 | E_err:   0.024887
[2025-10-30 05:34:05] [Iter 1207/1575] R0[1131/1500] | LR: 0.007960 | E:  -58.994258 | E_var:     5.2737 | E_err:   0.025480
[2025-10-30 05:34:22] [Iter 1208/1575] R0[1132/1500] | LR: 0.007924 | E:  -58.969477 | E_var:     5.0798 | E_err:   0.025275
[2025-10-30 05:34:38] [Iter 1209/1575] R0[1133/1500] | LR: 0.007888 | E:  -58.985700 | E_var:     5.2320 | E_err:   0.025229
[2025-10-30 05:34:55] [Iter 1210/1575] R0[1134/1500] | LR: 0.007852 | E:  -58.953089 | E_var:     5.0816 | E_err:   0.024486
[2025-10-30 05:35:12] [Iter 1211/1575] R0[1135/1500] | LR: 0.007817 | E:  -58.971418 | E_var:     5.0400 | E_err:   0.024529
[2025-10-30 05:35:28] [Iter 1212/1575] R0[1136/1500] | LR: 0.007781 | E:  -58.983755 | E_var:     5.1771 | E_err:   0.025346
[2025-10-30 05:35:45] [Iter 1213/1575] R0[1137/1500] | LR: 0.007746 | E:  -58.962790 | E_var:     5.1370 | E_err:   0.025477
[2025-10-30 05:36:01] [Iter 1214/1575] R0[1138/1500] | LR: 0.007711 | E:  -59.010162 | E_var:     5.2212 | E_err:   0.025344
[2025-10-30 05:36:18] [Iter 1215/1575] R0[1139/1500] | LR: 0.007675 | E:  -59.011020 | E_var:     5.0758 | E_err:   0.025612
[2025-10-30 05:36:34] [Iter 1216/1575] R0[1140/1500] | LR: 0.007640 | E:  -58.953933 | E_var:     4.9675 | E_err:   0.025417
[2025-10-30 05:36:51] [Iter 1217/1575] R0[1141/1500] | LR: 0.007605 | E:  -59.013176 | E_var:     5.1705 | E_err:   0.025589
[2025-10-30 05:37:07] [Iter 1218/1575] R0[1142/1500] | LR: 0.007570 | E:  -59.024376 | E_var:     5.1522 | E_err:   0.025038
[2025-10-30 05:37:24] [Iter 1219/1575] R0[1143/1500] | LR: 0.007535 | E:  -58.993860 | E_var:     5.1131 | E_err:   0.026774
[2025-10-30 05:37:41] [Iter 1220/1575] R0[1144/1500] | LR: 0.007500 | E:  -59.019214 | E_var:     5.1786 | E_err:   0.026200
[2025-10-30 05:37:57] [Iter 1221/1575] R0[1145/1500] | LR: 0.007466 | E:  -58.938676 | E_var:     5.1476 | E_err:   0.024875
[2025-10-30 05:38:14] [Iter 1222/1575] R0[1146/1500] | LR: 0.007431 | E:  -59.033972 | E_var:     5.0084 | E_err:   0.025672
[2025-10-30 05:38:30] [Iter 1223/1575] R0[1147/1500] | LR: 0.007396 | E:  -59.022064 | E_var:     4.9939 | E_err:   0.025023
[2025-10-30 05:38:47] [Iter 1224/1575] R0[1148/1500] | LR: 0.007362 | E:  -58.980111 | E_var:     5.1555 | E_err:   0.025350
[2025-10-30 05:39:03] [Iter 1225/1575] R0[1149/1500] | LR: 0.007327 | E:  -59.000300 | E_var:     4.9516 | E_err:   0.024533
[2025-10-30 05:39:20] [Iter 1226/1575] R0[1150/1500] | LR: 0.007293 | E:  -59.017791 | E_var:     5.0817 | E_err:   0.026761
[2025-10-30 05:39:36] [Iter 1227/1575] R0[1151/1500] | LR: 0.007259 | E:  -58.997038 | E_var:     4.9936 | E_err:   0.025162
[2025-10-30 05:39:53] [Iter 1228/1575] R0[1152/1500] | LR: 0.007224 | E:  -58.940328 | E_var:     4.9565 | E_err:   0.024470
[2025-10-30 05:40:10] [Iter 1229/1575] R0[1153/1500] | LR: 0.007190 | E:  -58.995324 | E_var:     5.1468 | E_err:   0.025614
[2025-10-30 05:40:26] [Iter 1230/1575] R0[1154/1500] | LR: 0.007156 | E:  -58.996643 | E_var:     4.9585 | E_err:   0.024840
[2025-10-30 05:40:43] [Iter 1231/1575] R0[1155/1500] | LR: 0.007122 | E:  -59.029793 | E_var:     5.1020 | E_err:   0.024937
[2025-10-30 05:40:59] [Iter 1232/1575] R0[1156/1500] | LR: 0.007088 | E:  -58.960515 | E_var:     5.1444 | E_err:   0.025162
[2025-10-30 05:41:16] [Iter 1233/1575] R0[1157/1500] | LR: 0.007055 | E:  -59.004220 | E_var:     5.0583 | E_err:   0.025495
[2025-10-30 05:41:32] [Iter 1234/1575] R0[1158/1500] | LR: 0.007021 | E:  -58.970729 | E_var:     5.1178 | E_err:   0.026411
[2025-10-30 05:41:49] [Iter 1235/1575] R0[1159/1500] | LR: 0.006987 | E:  -58.976195 | E_var:     5.2462 | E_err:   0.026673
[2025-10-30 05:42:05] [Iter 1236/1575] R0[1160/1500] | LR: 0.006954 | E:  -59.012209 | E_var:     5.4014 | E_err:   0.025080
[2025-10-30 05:42:22] [Iter 1237/1575] R0[1161/1500] | LR: 0.006920 | E:  -58.960864 | E_var:     5.4086 | E_err:   0.025716
[2025-10-30 05:42:39] [Iter 1238/1575] R0[1162/1500] | LR: 0.006887 | E:  -59.007840 | E_var:     5.1694 | E_err:   0.027701
[2025-10-30 05:42:55] [Iter 1239/1575] R0[1163/1500] | LR: 0.006853 | E:  -58.959194 | E_var:     5.3665 | E_err:   0.025867
[2025-10-30 05:43:12] [Iter 1240/1575] R0[1164/1500] | LR: 0.006820 | E:  -59.043494 | E_var:     5.2542 | E_err:   0.025609
[2025-10-30 05:43:28] [Iter 1241/1575] R0[1165/1500] | LR: 0.006787 | E:  -58.971657 | E_var:     5.0885 | E_err:   0.024868
[2025-10-30 05:43:45] [Iter 1242/1575] R0[1166/1500] | LR: 0.006754 | E:  -59.014868 | E_var:     5.1823 | E_err:   0.025527
[2025-10-30 05:44:01] [Iter 1243/1575] R0[1167/1500] | LR: 0.006721 | E:  -58.995242 | E_var:     5.0193 | E_err:   0.025520
[2025-10-30 05:44:18] [Iter 1244/1575] R0[1168/1500] | LR: 0.006688 | E:  -59.002548 | E_var:     4.8662 | E_err:   0.024917
[2025-10-30 05:44:35] [Iter 1245/1575] R0[1169/1500] | LR: 0.006655 | E:  -59.001276 | E_var:     5.0966 | E_err:   0.025753
[2025-10-30 05:44:51] [Iter 1246/1575] R0[1170/1500] | LR: 0.006622 | E:  -59.020402 | E_var:     5.2487 | E_err:   0.026474
[2025-10-30 05:45:08] [Iter 1247/1575] R0[1171/1500] | LR: 0.006590 | E:  -59.045304 | E_var:     5.3441 | E_err:   0.025778
[2025-10-30 05:45:24] [Iter 1248/1575] R0[1172/1500] | LR: 0.006557 | E:  -58.959928 | E_var:     5.0776 | E_err:   0.024795
[2025-10-30 05:45:41] [Iter 1249/1575] R0[1173/1500] | LR: 0.006525 | E:  -58.972223 | E_var:     5.0897 | E_err:   0.025876
[2025-10-30 05:45:57] [Iter 1250/1575] R0[1174/1500] | LR: 0.006492 | E:  -58.941678 | E_var:     5.1207 | E_err:   0.026280
[2025-10-30 05:46:14] [Iter 1251/1575] R0[1175/1500] | LR: 0.006460 | E:  -59.011951 | E_var:     5.1767 | E_err:   0.025784
[2025-10-30 05:46:30] [Iter 1252/1575] R0[1176/1500] | LR: 0.006428 | E:  -59.040294 | E_var:     5.0029 | E_err:   0.024112
[2025-10-30 05:46:47] [Iter 1253/1575] R0[1177/1500] | LR: 0.006396 | E:  -59.036152 | E_var:     5.1901 | E_err:   0.025229
[2025-10-30 05:47:04] [Iter 1254/1575] R0[1178/1500] | LR: 0.006363 | E:  -59.025197 | E_var:     5.1463 | E_err:   0.025607
[2025-10-30 05:47:20] [Iter 1255/1575] R0[1179/1500] | LR: 0.006331 | E:  -58.981274 | E_var:     4.9205 | E_err:   0.025689
[2025-10-30 05:47:37] [Iter 1256/1575] R0[1180/1500] | LR: 0.006300 | E:  -59.000043 | E_var:     5.0269 | E_err:   0.024678
[2025-10-30 05:47:53] [Iter 1257/1575] R0[1181/1500] | LR: 0.006268 | E:  -58.970044 | E_var:     5.2171 | E_err:   0.025148
[2025-10-30 05:48:10] [Iter 1258/1575] R0[1182/1500] | LR: 0.006236 | E:  -59.055762 | E_var:     5.0759 | E_err:   0.024753
[2025-10-30 05:48:26] [Iter 1259/1575] R0[1183/1500] | LR: 0.006204 | E:  -58.959457 | E_var:     5.1814 | E_err:   0.024837
[2025-10-30 05:48:43] [Iter 1260/1575] R0[1184/1500] | LR: 0.006173 | E:  -58.978960 | E_var:     5.0607 | E_err:   0.025544
[2025-10-30 05:48:59] [Iter 1261/1575] R0[1185/1500] | LR: 0.006141 | E:  -59.005222 | E_var:     5.1200 | E_err:   0.025088
[2025-10-30 05:49:16] [Iter 1262/1575] R0[1186/1500] | LR: 0.006110 | E:  -59.054821 | E_var:     5.2160 | E_err:   0.025683
[2025-10-30 05:49:33] [Iter 1263/1575] R0[1187/1500] | LR: 0.006078 | E:  -58.987369 | E_var:     5.0133 | E_err:   0.025680
[2025-10-30 05:49:49] [Iter 1264/1575] R0[1188/1500] | LR: 0.006047 | E:  -59.005163 | E_var:     5.0403 | E_err:   0.026100
[2025-10-30 05:50:06] [Iter 1265/1575] R0[1189/1500] | LR: 0.006016 | E:  -58.985983 | E_var:     5.1911 | E_err:   0.026326
[2025-10-30 05:50:22] [Iter 1266/1575] R0[1190/1500] | LR: 0.005985 | E:  -59.032534 | E_var:     5.0914 | E_err:   0.025244
[2025-10-30 05:50:39] [Iter 1267/1575] R0[1191/1500] | LR: 0.005954 | E:  -58.997450 | E_var:     4.9410 | E_err:   0.025513
[2025-10-30 05:50:55] [Iter 1268/1575] R0[1192/1500] | LR: 0.005923 | E:  -59.003087 | E_var:     4.9751 | E_err:   0.024355
[2025-10-30 05:51:12] [Iter 1269/1575] R0[1193/1500] | LR: 0.005892 | E:  -58.997395 | E_var:     5.0668 | E_err:   0.026251
[2025-10-30 05:51:28] [Iter 1270/1575] R0[1194/1500] | LR: 0.005862 | E:  -58.951210 | E_var:     5.0939 | E_err:   0.026254
[2025-10-30 05:51:45] [Iter 1271/1575] R0[1195/1500] | LR: 0.005831 | E:  -58.984011 | E_var:     5.4776 | E_err:   0.026684
[2025-10-30 05:52:02] [Iter 1272/1575] R0[1196/1500] | LR: 0.005800 | E:  -59.013497 | E_var:     5.3589 | E_err:   0.024865
[2025-10-30 05:52:18] [Iter 1273/1575] R0[1197/1500] | LR: 0.005770 | E:  -58.996033 | E_var:     5.1439 | E_err:   0.025265
[2025-10-30 05:52:35] [Iter 1274/1575] R0[1198/1500] | LR: 0.005740 | E:  -59.003326 | E_var:     5.4919 | E_err:   0.025337
[2025-10-30 05:52:51] [Iter 1275/1575] R0[1199/1500] | LR: 0.005709 | E:  -58.949007 | E_var:     5.1826 | E_err:   0.024818
[2025-10-30 05:52:51] ✓ Checkpoint saved: checkpoint_iter_001200.pkl
[2025-10-30 05:53:08] [Iter 1276/1575] R0[1200/1500] | LR: 0.005679 | E:  -59.004421 | E_var:     5.3591 | E_err:   0.027033
[2025-10-30 05:53:25] [Iter 1277/1575] R0[1201/1500] | LR: 0.005649 | E:  -59.016930 | E_var:     5.2363 | E_err:   0.025853
[2025-10-30 05:53:41] [Iter 1278/1575] R0[1202/1500] | LR: 0.005619 | E:  -59.003669 | E_var:     5.3991 | E_err:   0.026969
[2025-10-30 05:53:58] [Iter 1279/1575] R0[1203/1500] | LR: 0.005589 | E:  -59.002361 | E_var:     4.9569 | E_err:   0.025230
[2025-10-30 05:54:14] [Iter 1280/1575] R0[1204/1500] | LR: 0.005559 | E:  -59.010645 | E_var:     5.1590 | E_err:   0.024886
[2025-10-30 05:54:31] [Iter 1281/1575] R0[1205/1500] | LR: 0.005529 | E:  -59.021895 | E_var:     5.0446 | E_err:   0.025173
[2025-10-30 05:54:47] [Iter 1282/1575] R0[1206/1500] | LR: 0.005500 | E:  -59.049414 | E_var:     5.1249 | E_err:   0.026440
[2025-10-30 05:55:04] [Iter 1283/1575] R0[1207/1500] | LR: 0.005470 | E:  -59.003894 | E_var:     5.1295 | E_err:   0.025060
[2025-10-30 05:55:20] [Iter 1284/1575] R0[1208/1500] | LR: 0.005441 | E:  -58.991426 | E_var:     4.9697 | E_err:   0.025792
[2025-10-30 05:55:37] [Iter 1285/1575] R0[1209/1500] | LR: 0.005411 | E:  -58.991139 | E_var:     4.8645 | E_err:   0.025130
[2025-10-30 05:55:54] [Iter 1286/1575] R0[1210/1500] | LR: 0.005382 | E:  -58.990754 | E_var:     5.0860 | E_err:   0.025967
[2025-10-30 05:56:10] [Iter 1287/1575] R0[1211/1500] | LR: 0.005353 | E:  -58.995998 | E_var:     5.2871 | E_err:   0.026950
[2025-10-30 05:56:27] [Iter 1288/1575] R0[1212/1500] | LR: 0.005323 | E:  -59.031150 | E_var:     4.8674 | E_err:   0.024527
[2025-10-30 05:56:43] [Iter 1289/1575] R0[1213/1500] | LR: 0.005294 | E:  -59.017505 | E_var:     5.0636 | E_err:   0.026486
[2025-10-30 05:57:00] [Iter 1290/1575] R0[1214/1500] | LR: 0.005265 | E:  -59.033793 | E_var:     4.9149 | E_err:   0.025625
[2025-10-30 05:57:16] [Iter 1291/1575] R0[1215/1500] | LR: 0.005237 | E:  -59.029728 | E_var:     4.9547 | E_err:   0.025725
[2025-10-30 05:57:33] [Iter 1292/1575] R0[1216/1500] | LR: 0.005208 | E:  -59.034309 | E_var:     5.0041 | E_err:   0.025647
[2025-10-30 05:57:49] [Iter 1293/1575] R0[1217/1500] | LR: 0.005179 | E:  -58.985043 | E_var:     4.9129 | E_err:   0.024274
[2025-10-30 05:58:06] [Iter 1294/1575] R0[1218/1500] | LR: 0.005150 | E:  -59.011217 | E_var:     5.0333 | E_err:   0.025548
[2025-10-30 05:58:23] [Iter 1295/1575] R0[1219/1500] | LR: 0.005122 | E:  -58.981753 | E_var:     5.0492 | E_err:   0.025180
[2025-10-30 05:58:39] [Iter 1296/1575] R0[1220/1500] | LR: 0.005093 | E:  -59.028460 | E_var:     4.7816 | E_err:   0.024476
[2025-10-30 05:58:56] [Iter 1297/1575] R0[1221/1500] | LR: 0.005065 | E:  -59.053807 | E_var:     4.8847 | E_err:   0.024597
[2025-10-30 05:59:12] [Iter 1298/1575] R0[1222/1500] | LR: 0.005037 | E:  -59.022581 | E_var:     4.7774 | E_err:   0.025448
[2025-10-30 05:59:29] [Iter 1299/1575] R0[1223/1500] | LR: 0.005009 | E:  -58.961855 | E_var:     4.9599 | E_err:   0.025863
[2025-10-30 05:59:45] [Iter 1300/1575] R0[1224/1500] | LR: 0.004981 | E:  -59.075266 | E_var:     4.9124 | E_err:   0.024404
[2025-10-30 06:00:02] [Iter 1301/1575] R0[1225/1500] | LR: 0.004953 | E:  -59.015839 | E_var:     5.0253 | E_err:   0.025769
[2025-10-30 06:00:18] [Iter 1302/1575] R0[1226/1500] | LR: 0.004925 | E:  -58.986850 | E_var:     4.9904 | E_err:   0.023886
[2025-10-30 06:00:35] [Iter 1303/1575] R0[1227/1500] | LR: 0.004897 | E:  -59.046749 | E_var:     4.9006 | E_err:   0.024736
[2025-10-30 06:00:52] [Iter 1304/1575] R0[1228/1500] | LR: 0.004869 | E:  -59.003702 | E_var:     4.9976 | E_err:   0.024101
[2025-10-30 06:01:08] [Iter 1305/1575] R0[1229/1500] | LR: 0.004842 | E:  -59.012990 | E_var:     4.9985 | E_err:   0.024862
[2025-10-30 06:01:25] [Iter 1306/1575] R0[1230/1500] | LR: 0.004814 | E:  -59.069245 | E_var:     5.1320 | E_err:   0.025252
[2025-10-30 06:01:41] [Iter 1307/1575] R0[1231/1500] | LR: 0.004787 | E:  -59.025059 | E_var:     4.9733 | E_err:   0.023740
[2025-10-30 06:01:58] [Iter 1308/1575] R0[1232/1500] | LR: 0.004759 | E:  -59.007566 | E_var:     4.9522 | E_err:   0.024880
[2025-10-30 06:02:14] [Iter 1309/1575] R0[1233/1500] | LR: 0.004732 | E:  -59.014161 | E_var:     4.8081 | E_err:   0.024962
[2025-10-30 06:02:31] [Iter 1310/1575] R0[1234/1500] | LR: 0.004705 | E:  -59.014676 | E_var:     5.0067 | E_err:   0.024165
[2025-10-30 06:02:47] [Iter 1311/1575] R0[1235/1500] | LR: 0.004678 | E:  -59.014556 | E_var:     5.0031 | E_err:   0.024878
[2025-10-30 06:03:04] [Iter 1312/1575] R0[1236/1500] | LR: 0.004651 | E:  -59.019402 | E_var:     4.8685 | E_err:   0.025502
[2025-10-30 06:03:21] [Iter 1313/1575] R0[1237/1500] | LR: 0.004624 | E:  -59.054153 | E_var:     4.8920 | E_err:   0.025124
[2025-10-30 06:03:37] [Iter 1314/1575] R0[1238/1500] | LR: 0.004597 | E:  -59.043087 | E_var:     4.8723 | E_err:   0.024195
[2025-10-30 06:03:54] [Iter 1315/1575] R0[1239/1500] | LR: 0.004570 | E:  -58.997493 | E_var:     5.0364 | E_err:   0.024648
[2025-10-30 06:04:10] [Iter 1316/1575] R0[1240/1500] | LR: 0.004544 | E:  -59.033016 | E_var:     5.0614 | E_err:   0.025064
[2025-10-30 06:04:27] [Iter 1317/1575] R0[1241/1500] | LR: 0.004517 | E:  -59.011314 | E_var:     5.1476 | E_err:   0.026775
[2025-10-30 06:04:43] [Iter 1318/1575] R0[1242/1500] | LR: 0.004491 | E:  -59.012759 | E_var:     5.0108 | E_err:   0.025642
[2025-10-30 06:05:00] [Iter 1319/1575] R0[1243/1500] | LR: 0.004464 | E:  -59.003774 | E_var:     4.9665 | E_err:   0.024618
[2025-10-30 06:05:16] [Iter 1320/1575] R0[1244/1500] | LR: 0.004438 | E:  -59.017677 | E_var:     5.3361 | E_err:   0.027326
[2025-10-30 06:05:33] [Iter 1321/1575] R0[1245/1500] | LR: 0.004412 | E:  -58.986797 | E_var:     5.1455 | E_err:   0.026984
[2025-10-30 06:05:50] [Iter 1322/1575] R0[1246/1500] | LR: 0.004386 | E:  -59.009134 | E_var:     5.1049 | E_err:   0.024668
[2025-10-30 06:06:06] [Iter 1323/1575] R0[1247/1500] | LR: 0.004360 | E:  -59.053050 | E_var:     5.0192 | E_err:   0.024754
[2025-10-30 06:06:23] [Iter 1324/1575] R0[1248/1500] | LR: 0.004334 | E:  -59.052343 | E_var:     4.8500 | E_err:   0.023910
[2025-10-30 06:06:39] [Iter 1325/1575] R0[1249/1500] | LR: 0.004308 | E:  -59.053040 | E_var:     4.9562 | E_err:   0.023959
[2025-10-30 06:06:56] [Iter 1326/1575] R0[1250/1500] | LR: 0.004282 | E:  -59.010075 | E_var:     5.1018 | E_err:   0.025497
[2025-10-30 06:07:12] [Iter 1327/1575] R0[1251/1500] | LR: 0.004257 | E:  -59.024091 | E_var:     4.8855 | E_err:   0.025256
[2025-10-30 06:07:29] [Iter 1328/1575] R0[1252/1500] | LR: 0.004231 | E:  -59.033626 | E_var:     4.9370 | E_err:   0.025303
[2025-10-30 06:07:45] [Iter 1329/1575] R0[1253/1500] | LR: 0.004206 | E:  -59.000945 | E_var:     5.0960 | E_err:   0.025785
[2025-10-30 06:08:02] [Iter 1330/1575] R0[1254/1500] | LR: 0.004180 | E:  -59.028340 | E_var:     5.0342 | E_err:   0.025091
[2025-10-30 06:08:18] [Iter 1331/1575] R0[1255/1500] | LR: 0.004155 | E:  -58.969024 | E_var:     5.0958 | E_err:   0.025922
[2025-10-30 06:08:35] [Iter 1332/1575] R0[1256/1500] | LR: 0.004130 | E:  -59.036615 | E_var:     5.0280 | E_err:   0.026201
[2025-10-30 06:08:52] [Iter 1333/1575] R0[1257/1500] | LR: 0.004105 | E:  -58.994723 | E_var:     5.3561 | E_err:   0.025129
[2025-10-30 06:09:08] [Iter 1334/1575] R0[1258/1500] | LR: 0.004080 | E:  -59.035073 | E_var:     5.1044 | E_err:   0.025130
[2025-10-30 06:09:25] [Iter 1335/1575] R0[1259/1500] | LR: 0.004055 | E:  -59.002097 | E_var:     5.1848 | E_err:   0.024862
[2025-10-30 06:09:41] [Iter 1336/1575] R0[1260/1500] | LR: 0.004030 | E:  -59.020035 | E_var:     5.0513 | E_err:   0.025877
[2025-10-30 06:09:58] [Iter 1337/1575] R0[1261/1500] | LR: 0.004006 | E:  -59.027927 | E_var:     5.0985 | E_err:   0.025581
[2025-10-30 06:10:14] [Iter 1338/1575] R0[1262/1500] | LR: 0.003981 | E:  -59.036962 | E_var:     4.9347 | E_err:   0.024449
[2025-10-30 06:10:31] [Iter 1339/1575] R0[1263/1500] | LR: 0.003957 | E:  -59.009196 | E_var:     4.9524 | E_err:   0.024471
[2025-10-30 06:10:47] [Iter 1340/1575] R0[1264/1500] | LR: 0.003932 | E:  -59.040226 | E_var:     4.9576 | E_err:   0.024360
[2025-10-30 06:11:04] [Iter 1341/1575] R0[1265/1500] | LR: 0.003908 | E:  -59.000602 | E_var:     4.9408 | E_err:   0.024926
[2025-10-30 06:11:20] [Iter 1342/1575] R0[1266/1500] | LR: 0.003884 | E:  -59.001451 | E_var:     4.7794 | E_err:   0.024256
[2025-10-30 06:11:37] [Iter 1343/1575] R0[1267/1500] | LR: 0.003860 | E:  -59.048336 | E_var:     4.8695 | E_err:   0.025795
[2025-10-30 06:11:54] [Iter 1344/1575] R0[1268/1500] | LR: 0.003836 | E:  -59.038264 | E_var:     4.9252 | E_err:   0.024475
[2025-10-30 06:12:10] [Iter 1345/1575] R0[1269/1500] | LR: 0.003812 | E:  -58.997706 | E_var:     4.8638 | E_err:   0.024477
[2025-10-30 06:12:27] [Iter 1346/1575] R0[1270/1500] | LR: 0.003788 | E:  -59.052151 | E_var:     4.8370 | E_err:   0.025165
[2025-10-30 06:12:43] [Iter 1347/1575] R0[1271/1500] | LR: 0.003764 | E:  -59.037559 | E_var:     4.8901 | E_err:   0.024120
[2025-10-30 06:13:00] [Iter 1348/1575] R0[1272/1500] | LR: 0.003741 | E:  -59.020509 | E_var:     5.0529 | E_err:   0.024609
[2025-10-30 06:13:16] [Iter 1349/1575] R0[1273/1500] | LR: 0.003717 | E:  -59.010738 | E_var:     4.8620 | E_err:   0.024156
[2025-10-30 06:13:33] [Iter 1350/1575] R0[1274/1500] | LR: 0.003694 | E:  -58.976990 | E_var:     4.7170 | E_err:   0.024522
[2025-10-30 06:13:49] [Iter 1351/1575] R0[1275/1500] | LR: 0.003670 | E:  -59.040848 | E_var:     4.9376 | E_err:   0.025327
[2025-10-30 06:14:06] [Iter 1352/1575] R0[1276/1500] | LR: 0.003647 | E:  -59.046621 | E_var:     4.8812 | E_err:   0.024025
[2025-10-30 06:14:23] [Iter 1353/1575] R0[1277/1500] | LR: 0.003624 | E:  -59.015179 | E_var:     4.8201 | E_err:   0.024063
[2025-10-30 06:14:39] [Iter 1354/1575] R0[1278/1500] | LR: 0.003601 | E:  -58.969586 | E_var:     4.9976 | E_err:   0.026216
[2025-10-30 06:14:56] [Iter 1355/1575] R0[1279/1500] | LR: 0.003578 | E:  -58.998442 | E_var:     4.9793 | E_err:   0.025180
[2025-10-30 06:15:12] [Iter 1356/1575] R0[1280/1500] | LR: 0.003555 | E:  -59.019897 | E_var:     5.1165 | E_err:   0.024157
[2025-10-30 06:15:29] [Iter 1357/1575] R0[1281/1500] | LR: 0.003532 | E:  -58.978080 | E_var:     5.0790 | E_err:   0.024895
[2025-10-30 06:15:45] [Iter 1358/1575] R0[1282/1500] | LR: 0.003510 | E:  -59.049147 | E_var:     5.1555 | E_err:   0.025361
[2025-10-30 06:16:02] [Iter 1359/1575] R0[1283/1500] | LR: 0.003487 | E:  -59.001233 | E_var:     4.8429 | E_err:   0.024640
[2025-10-30 06:16:18] [Iter 1360/1575] R0[1284/1500] | LR: 0.003465 | E:  -59.013088 | E_var:     5.2709 | E_err:   0.025397
[2025-10-30 06:16:35] [Iter 1361/1575] R0[1285/1500] | LR: 0.003442 | E:  -59.046502 | E_var:     4.8545 | E_err:   0.024118
[2025-10-30 06:16:52] [Iter 1362/1575] R0[1286/1500] | LR: 0.003420 | E:  -59.019234 | E_var:     5.0660 | E_err:   0.024064
[2025-10-30 06:17:08] [Iter 1363/1575] R0[1287/1500] | LR: 0.003398 | E:  -59.052367 | E_var:     5.2098 | E_err:   0.026532
[2025-10-30 06:17:25] [Iter 1364/1575] R0[1288/1500] | LR: 0.003376 | E:  -59.022171 | E_var:     5.0178 | E_err:   0.024684
[2025-10-30 06:17:41] [Iter 1365/1575] R0[1289/1500] | LR: 0.003354 | E:  -59.043718 | E_var:     5.0780 | E_err:   0.023977
[2025-10-30 06:17:58] [Iter 1366/1575] R0[1290/1500] | LR: 0.003332 | E:  -59.038260 | E_var:     4.9246 | E_err:   0.023886
[2025-10-30 06:18:14] [Iter 1367/1575] R0[1291/1500] | LR: 0.003310 | E:  -59.045745 | E_var:     4.9911 | E_err:   0.025056
[2025-10-30 06:18:31] [Iter 1368/1575] R0[1292/1500] | LR: 0.003288 | E:  -59.021881 | E_var:     4.8360 | E_err:   0.024478
[2025-10-30 06:18:47] [Iter 1369/1575] R0[1293/1500] | LR: 0.003267 | E:  -59.017550 | E_var:     4.9366 | E_err:   0.025441
[2025-10-30 06:19:04] [Iter 1370/1575] R0[1294/1500] | LR: 0.003245 | E:  -59.034092 | E_var:     5.0938 | E_err:   0.025577
[2025-10-30 06:19:20] [Iter 1371/1575] R0[1295/1500] | LR: 0.003224 | E:  -59.044272 | E_var:     4.7767 | E_err:   0.024780
[2025-10-30 06:19:37] [Iter 1372/1575] R0[1296/1500] | LR: 0.003202 | E:  -59.027291 | E_var:     4.8079 | E_err:   0.024135
[2025-10-30 06:19:54] [Iter 1373/1575] R0[1297/1500] | LR: 0.003181 | E:  -59.025210 | E_var:     4.7891 | E_err:   0.023524
[2025-10-30 06:20:10] [Iter 1374/1575] R0[1298/1500] | LR: 0.003160 | E:  -58.988641 | E_var:     4.7883 | E_err:   0.024273
[2025-10-30 06:20:27] [Iter 1375/1575] R0[1299/1500] | LR: 0.003139 | E:  -58.983279 | E_var:     4.8552 | E_err:   0.024394
[2025-10-30 06:20:43] [Iter 1376/1575] R0[1300/1500] | LR: 0.003118 | E:  -59.000639 | E_var:     4.8763 | E_err:   0.024419
[2025-10-30 06:21:00] [Iter 1377/1575] R0[1301/1500] | LR: 0.003097 | E:  -59.076870 | E_var:     4.9484 | E_err:   0.024768
[2025-10-30 06:21:16] [Iter 1378/1575] R0[1302/1500] | LR: 0.003077 | E:  -58.985745 | E_var:     4.9581 | E_err:   0.024775
[2025-10-30 06:21:33] [Iter 1379/1575] R0[1303/1500] | LR: 0.003056 | E:  -59.016350 | E_var:     4.9231 | E_err:   0.025715
[2025-10-30 06:21:49] [Iter 1380/1575] R0[1304/1500] | LR: 0.003035 | E:  -59.063044 | E_var:     4.8430 | E_err:   0.025181
[2025-10-30 06:22:06] [Iter 1381/1575] R0[1305/1500] | LR: 0.003015 | E:  -59.076598 | E_var:     5.1113 | E_err:   0.025228
[2025-10-30 06:22:23] [Iter 1382/1575] R0[1306/1500] | LR: 0.002995 | E:  -59.027243 | E_var:     4.9200 | E_err:   0.023921
[2025-10-30 06:22:39] [Iter 1383/1575] R0[1307/1500] | LR: 0.002974 | E:  -59.014662 | E_var:     5.0916 | E_err:   0.025353
[2025-10-30 06:22:56] [Iter 1384/1575] R0[1308/1500] | LR: 0.002954 | E:  -58.953763 | E_var:     4.9494 | E_err:   0.025287
[2025-10-30 06:23:12] [Iter 1385/1575] R0[1309/1500] | LR: 0.002934 | E:  -58.978470 | E_var:     4.9201 | E_err:   0.025445
[2025-10-30 06:23:29] [Iter 1386/1575] R0[1310/1500] | LR: 0.002914 | E:  -58.984675 | E_var:     5.0117 | E_err:   0.026195
[2025-10-30 06:23:45] [Iter 1387/1575] R0[1311/1500] | LR: 0.002895 | E:  -58.995299 | E_var:     5.0672 | E_err:   0.026064
[2025-10-30 06:24:02] [Iter 1388/1575] R0[1312/1500] | LR: 0.002875 | E:  -59.069864 | E_var:     5.0086 | E_err:   0.026159
[2025-10-30 06:24:18] [Iter 1389/1575] R0[1313/1500] | LR: 0.002855 | E:  -59.019158 | E_var:     5.0625 | E_err:   0.024694
[2025-10-30 06:24:35] [Iter 1390/1575] R0[1314/1500] | LR: 0.002836 | E:  -58.990702 | E_var:     4.8638 | E_err:   0.025036
[2025-10-30 06:24:52] [Iter 1391/1575] R0[1315/1500] | LR: 0.002816 | E:  -59.050256 | E_var:     4.8914 | E_err:   0.025290
[2025-10-30 06:25:08] [Iter 1392/1575] R0[1316/1500] | LR: 0.002797 | E:  -58.971148 | E_var:     5.1711 | E_err:   0.025470
[2025-10-30 06:25:25] [Iter 1393/1575] R0[1317/1500] | LR: 0.002778 | E:  -58.959336 | E_var:     5.1364 | E_err:   0.024625
[2025-10-30 06:25:41] [Iter 1394/1575] R0[1318/1500] | LR: 0.002758 | E:  -59.025435 | E_var:     4.9446 | E_err:   0.025252
[2025-10-30 06:25:58] [Iter 1395/1575] R0[1319/1500] | LR: 0.002739 | E:  -58.925210 | E_var:     4.9605 | E_err:   0.025097
[2025-10-30 06:26:14] [Iter 1396/1575] R0[1320/1500] | LR: 0.002720 | E:  -58.954593 | E_var:     5.1217 | E_err:   0.026529
[2025-10-30 06:26:31] [Iter 1397/1575] R0[1321/1500] | LR: 0.002702 | E:  -58.945219 | E_var:     5.1437 | E_err:   0.026992
[2025-10-30 06:26:47] [Iter 1398/1575] R0[1322/1500] | LR: 0.002683 | E:  -59.000733 | E_var:     5.1657 | E_err:   0.026209
[2025-10-30 06:27:04] [Iter 1399/1575] R0[1323/1500] | LR: 0.002664 | E:  -58.987113 | E_var:     5.2979 | E_err:   0.027041
[2025-10-30 06:27:21] [Iter 1400/1575] R0[1324/1500] | LR: 0.002646 | E:  -58.965209 | E_var:     4.9267 | E_err:   0.024765
[2025-10-30 06:27:37] [Iter 1401/1575] R0[1325/1500] | LR: 0.002627 | E:  -58.998097 | E_var:     4.9943 | E_err:   0.025729
[2025-10-30 06:27:54] [Iter 1402/1575] R0[1326/1500] | LR: 0.002609 | E:  -59.020464 | E_var:     5.0668 | E_err:   0.026034
[2025-10-30 06:28:10] [Iter 1403/1575] R0[1327/1500] | LR: 0.002591 | E:  -59.013629 | E_var:     4.8403 | E_err:   0.026119
[2025-10-30 06:28:27] [Iter 1404/1575] R0[1328/1500] | LR: 0.002573 | E:  -59.022576 | E_var:     4.9081 | E_err:   0.025512
[2025-10-30 06:28:43] [Iter 1405/1575] R0[1329/1500] | LR: 0.002555 | E:  -59.025561 | E_var:     4.9191 | E_err:   0.025667
[2025-10-30 06:29:00] [Iter 1406/1575] R0[1330/1500] | LR: 0.002537 | E:  -59.086266 | E_var:     4.7397 | E_err:   0.025709
[2025-10-30 06:29:16] [Iter 1407/1575] R0[1331/1500] | LR: 0.002519 | E:  -59.036562 | E_var:     4.9253 | E_err:   0.024124
[2025-10-30 06:29:33] [Iter 1408/1575] R0[1332/1500] | LR: 0.002501 | E:  -58.983322 | E_var:     5.1804 | E_err:   0.025844
[2025-10-30 06:29:50] [Iter 1409/1575] R0[1333/1500] | LR: 0.002483 | E:  -59.038588 | E_var:     4.8976 | E_err:   0.024821
[2025-10-30 06:30:06] [Iter 1410/1575] R0[1334/1500] | LR: 0.002466 | E:  -59.037759 | E_var:     5.1301 | E_err:   0.026376
[2025-10-30 06:30:23] [Iter 1411/1575] R0[1335/1500] | LR: 0.002448 | E:  -59.057717 | E_var:     4.9432 | E_err:   0.026511
[2025-10-30 06:30:39] [Iter 1412/1575] R0[1336/1500] | LR: 0.002431 | E:  -59.083927 | E_var:     4.8332 | E_err:   0.024069
[2025-10-30 06:30:56] [Iter 1413/1575] R0[1337/1500] | LR: 0.002414 | E:  -59.032115 | E_var:     4.7394 | E_err:   0.024892
[2025-10-30 06:31:12] [Iter 1414/1575] R0[1338/1500] | LR: 0.002397 | E:  -59.035273 | E_var:     4.7899 | E_err:   0.023955
[2025-10-30 06:31:29] [Iter 1415/1575] R0[1339/1500] | LR: 0.002380 | E:  -59.028101 | E_var:     4.9464 | E_err:   0.025021
[2025-10-30 06:31:45] [Iter 1416/1575] R0[1340/1500] | LR: 0.002363 | E:  -59.080928 | E_var:     4.9767 | E_err:   0.024997
[2025-10-30 06:32:02] [Iter 1417/1575] R0[1341/1500] | LR: 0.002346 | E:  -59.120336 | E_var:     4.9693 | E_err:   0.025760
[2025-10-30 06:32:19] [Iter 1418/1575] R0[1342/1500] | LR: 0.002329 | E:  -59.060896 | E_var:     4.9779 | E_err:   0.023651
[2025-10-30 06:32:35] [Iter 1419/1575] R0[1343/1500] | LR: 0.002313 | E:  -59.098826 | E_var:     5.1775 | E_err:   0.025838
[2025-10-30 06:32:52] [Iter 1420/1575] R0[1344/1500] | LR: 0.002296 | E:  -59.056304 | E_var:     5.0038 | E_err:   0.024258
[2025-10-30 06:33:08] [Iter 1421/1575] R0[1345/1500] | LR: 0.002280 | E:  -59.009789 | E_var:     5.1551 | E_err:   0.025010
[2025-10-30 06:33:25] [Iter 1422/1575] R0[1346/1500] | LR: 0.002263 | E:  -59.039563 | E_var:     5.1558 | E_err:   0.025161
[2025-10-30 06:33:41] [Iter 1423/1575] R0[1347/1500] | LR: 0.002247 | E:  -59.024217 | E_var:     5.1964 | E_err:   0.025860
[2025-10-30 06:33:58] [Iter 1424/1575] R0[1348/1500] | LR: 0.002231 | E:  -59.014465 | E_var:     5.1973 | E_err:   0.023517
[2025-10-30 06:34:14] [Iter 1425/1575] R0[1349/1500] | LR: 0.002215 | E:  -59.045122 | E_var:     5.0214 | E_err:   0.026333
[2025-10-30 06:34:15] ✓ Checkpoint saved: checkpoint_iter_001350.pkl
[2025-10-30 06:34:31] [Iter 1426/1575] R0[1350/1500] | LR: 0.002199 | E:  -59.008891 | E_var:     5.0412 | E_err:   0.025339
[2025-10-30 06:34:48] [Iter 1427/1575] R0[1351/1500] | LR: 0.002183 | E:  -59.010666 | E_var:     5.2117 | E_err:   0.025502
[2025-10-30 06:35:04] [Iter 1428/1575] R0[1352/1500] | LR: 0.002168 | E:  -59.074944 | E_var:     5.0618 | E_err:   0.025095
[2025-10-30 06:35:21] [Iter 1429/1575] R0[1353/1500] | LR: 0.002152 | E:  -59.025541 | E_var:     4.8988 | E_err:   0.024727
[2025-10-30 06:35:37] [Iter 1430/1575] R0[1354/1500] | LR: 0.002137 | E:  -59.038653 | E_var:     4.8444 | E_err:   0.024283
[2025-10-30 06:35:54] [Iter 1431/1575] R0[1355/1500] | LR: 0.002121 | E:  -58.990941 | E_var:     5.0016 | E_err:   0.025078
[2025-10-30 06:36:10] [Iter 1432/1575] R0[1356/1500] | LR: 0.002106 | E:  -59.027657 | E_var:     4.8974 | E_err:   0.025307
[2025-10-30 06:36:27] [Iter 1433/1575] R0[1357/1500] | LR: 0.002091 | E:  -59.009980 | E_var:     4.9293 | E_err:   0.024515
[2025-10-30 06:36:44] [Iter 1434/1575] R0[1358/1500] | LR: 0.002076 | E:  -59.068881 | E_var:     5.0720 | E_err:   0.025743
[2025-10-30 06:37:00] [Iter 1435/1575] R0[1359/1500] | LR: 0.002061 | E:  -59.025067 | E_var:     5.0959 | E_err:   0.024837
[2025-10-30 06:37:17] [Iter 1436/1575] R0[1360/1500] | LR: 0.002046 | E:  -59.019553 | E_var:     4.7711 | E_err:   0.025365
[2025-10-30 06:37:33] [Iter 1437/1575] R0[1361/1500] | LR: 0.002031 | E:  -59.025689 | E_var:     4.9524 | E_err:   0.025459
[2025-10-30 06:37:50] [Iter 1438/1575] R0[1362/1500] | LR: 0.002016 | E:  -59.083627 | E_var:     4.7826 | E_err:   0.024201
[2025-10-30 06:38:06] [Iter 1439/1575] R0[1363/1500] | LR: 0.002002 | E:  -59.043350 | E_var:     4.8341 | E_err:   0.025285
[2025-10-30 06:38:23] [Iter 1440/1575] R0[1364/1500] | LR: 0.001987 | E:  -59.047554 | E_var:     4.9897 | E_err:   0.025803
[2025-10-30 06:38:39] [Iter 1441/1575] R0[1365/1500] | LR: 0.001973 | E:  -59.067573 | E_var:     4.8362 | E_err:   0.023395
[2025-10-30 06:38:56] [Iter 1442/1575] R0[1366/1500] | LR: 0.001959 | E:  -59.032136 | E_var:     5.0265 | E_err:   0.024839
[2025-10-30 06:39:13] [Iter 1443/1575] R0[1367/1500] | LR: 0.001944 | E:  -59.102922 | E_var:     4.9459 | E_err:   0.026475
[2025-10-30 06:39:29] [Iter 1444/1575] R0[1368/1500] | LR: 0.001930 | E:  -59.071784 | E_var:     4.9405 | E_err:   0.025674
[2025-10-30 06:39:46] [Iter 1445/1575] R0[1369/1500] | LR: 0.001916 | E:  -59.035697 | E_var:     4.7892 | E_err:   0.025269
[2025-10-30 06:40:02] [Iter 1446/1575] R0[1370/1500] | LR: 0.001903 | E:  -59.047060 | E_var:     4.8441 | E_err:   0.024937
[2025-10-30 06:40:19] [Iter 1447/1575] R0[1371/1500] | LR: 0.001889 | E:  -59.031580 | E_var:     4.7743 | E_err:   0.025038
[2025-10-30 06:40:35] [Iter 1448/1575] R0[1372/1500] | LR: 0.001875 | E:  -59.019780 | E_var:     4.7226 | E_err:   0.024102
[2025-10-30 06:40:52] [Iter 1449/1575] R0[1373/1500] | LR: 0.001862 | E:  -59.052708 | E_var:     4.7863 | E_err:   0.023771
[2025-10-30 06:41:08] [Iter 1450/1575] R0[1374/1500] | LR: 0.001848 | E:  -59.055603 | E_var:     4.8790 | E_err:   0.024397
[2025-10-30 06:41:25] [Iter 1451/1575] R0[1375/1500] | LR: 0.001835 | E:  -59.064210 | E_var:     4.7310 | E_err:   0.024413
[2025-10-30 06:41:42] [Iter 1452/1575] R0[1376/1500] | LR: 0.001822 | E:  -59.062880 | E_var:     4.8177 | E_err:   0.025137
[2025-10-30 06:41:58] [Iter 1453/1575] R0[1377/1500] | LR: 0.001808 | E:  -59.011137 | E_var:     4.9349 | E_err:   0.025945
[2025-10-30 06:42:15] [Iter 1454/1575] R0[1378/1500] | LR: 0.001795 | E:  -59.012767 | E_var:     4.7362 | E_err:   0.023727
[2025-10-30 06:42:31] [Iter 1455/1575] R0[1379/1500] | LR: 0.001783 | E:  -59.038328 | E_var:     4.7931 | E_err:   0.024177
[2025-10-30 06:42:48] [Iter 1456/1575] R0[1380/1500] | LR: 0.001770 | E:  -59.056035 | E_var:     4.6699 | E_err:   0.024652
[2025-10-30 06:43:04] [Iter 1457/1575] R0[1381/1500] | LR: 0.001757 | E:  -59.040995 | E_var:     4.9023 | E_err:   0.024550
[2025-10-30 06:43:21] [Iter 1458/1575] R0[1382/1500] | LR: 0.001744 | E:  -59.044082 | E_var:     4.6993 | E_err:   0.024801
[2025-10-30 06:43:37] [Iter 1459/1575] R0[1383/1500] | LR: 0.001732 | E:  -59.016421 | E_var:     4.9085 | E_err:   0.024751
[2025-10-30 06:43:54] [Iter 1460/1575] R0[1384/1500] | LR: 0.001720 | E:  -59.027877 | E_var:     4.8851 | E_err:   0.026397
[2025-10-30 06:44:11] [Iter 1461/1575] R0[1385/1500] | LR: 0.001707 | E:  -59.062813 | E_var:     5.2487 | E_err:   0.025503
[2025-10-30 06:44:27] [Iter 1462/1575] R0[1386/1500] | LR: 0.001695 | E:  -59.054777 | E_var:     4.9324 | E_err:   0.025046
[2025-10-30 06:44:44] [Iter 1463/1575] R0[1387/1500] | LR: 0.001683 | E:  -59.067128 | E_var:     5.1736 | E_err:   0.026178
[2025-10-30 06:45:00] [Iter 1464/1575] R0[1388/1500] | LR: 0.001671 | E:  -59.100671 | E_var:     4.9453 | E_err:   0.024218
[2025-10-30 06:45:17] [Iter 1465/1575] R0[1389/1500] | LR: 0.001659 | E:  -59.033800 | E_var:     4.9280 | E_err:   0.025342
[2025-10-30 06:45:33] [Iter 1466/1575] R0[1390/1500] | LR: 0.001647 | E:  -59.035725 | E_var:     4.9730 | E_err:   0.026059
[2025-10-30 06:45:50] [Iter 1467/1575] R0[1391/1500] | LR: 0.001636 | E:  -59.041159 | E_var:     4.9012 | E_err:   0.025994
[2025-10-30 06:46:06] [Iter 1468/1575] R0[1392/1500] | LR: 0.001624 | E:  -59.059251 | E_var:     5.0312 | E_err:   0.024288
[2025-10-30 06:46:23] [Iter 1469/1575] R0[1393/1500] | LR: 0.001613 | E:  -59.092405 | E_var:     4.8612 | E_err:   0.026016
[2025-10-30 06:46:40] [Iter 1470/1575] R0[1394/1500] | LR: 0.001601 | E:  -59.057200 | E_var:     4.8466 | E_err:   0.025751
[2025-10-30 06:46:56] [Iter 1471/1575] R0[1395/1500] | LR: 0.001590 | E:  -59.024016 | E_var:     4.8491 | E_err:   0.025474
[2025-10-30 06:47:13] [Iter 1472/1575] R0[1396/1500] | LR: 0.001579 | E:  -59.046211 | E_var:     4.9818 | E_err:   0.025110
[2025-10-30 06:47:29] [Iter 1473/1575] R0[1397/1500] | LR: 0.001568 | E:  -59.045281 | E_var:     4.8081 | E_err:   0.024833
[2025-10-30 06:47:46] [Iter 1474/1575] R0[1398/1500] | LR: 0.001557 | E:  -59.031696 | E_var:     4.7703 | E_err:   0.024979
[2025-10-30 06:48:02] [Iter 1475/1575] R0[1399/1500] | LR: 0.001546 | E:  -59.058563 | E_var:     4.8494 | E_err:   0.024441
[2025-10-30 06:48:19] [Iter 1476/1575] R0[1400/1500] | LR: 0.001535 | E:  -59.091258 | E_var:     4.9687 | E_err:   0.025675
[2025-10-30 06:48:35] [Iter 1477/1575] R0[1401/1500] | LR: 0.001525 | E:  -59.080413 | E_var:     4.8704 | E_err:   0.024873
[2025-10-30 06:48:52] [Iter 1478/1575] R0[1402/1500] | LR: 0.001514 | E:  -59.114985 | E_var:     4.7428 | E_err:   0.025437
[2025-10-30 06:49:09] [Iter 1479/1575] R0[1403/1500] | LR: 0.001504 | E:  -59.069453 | E_var:     5.1401 | E_err:   0.026208
[2025-10-30 06:49:25] [Iter 1480/1575] R0[1404/1500] | LR: 0.001494 | E:  -59.107296 | E_var:     5.0343 | E_err:   0.023922
[2025-10-30 06:49:42] [Iter 1481/1575] R0[1405/1500] | LR: 0.001483 | E:  -59.048857 | E_var:     5.0939 | E_err:   0.025257
[2025-10-30 06:49:58] [Iter 1482/1575] R0[1406/1500] | LR: 0.001473 | E:  -59.043030 | E_var:     4.9758 | E_err:   0.024927
[2025-10-30 06:50:15] [Iter 1483/1575] R0[1407/1500] | LR: 0.001463 | E:  -59.068758 | E_var:     5.0188 | E_err:   0.025580
[2025-10-30 06:50:31] [Iter 1484/1575] R0[1408/1500] | LR: 0.001453 | E:  -59.047531 | E_var:     5.2362 | E_err:   0.025131
[2025-10-30 06:50:48] [Iter 1485/1575] R0[1409/1500] | LR: 0.001444 | E:  -59.006185 | E_var:     5.1063 | E_err:   0.026676
[2025-10-30 06:51:04] [Iter 1486/1575] R0[1410/1500] | LR: 0.001434 | E:  -58.988408 | E_var:     5.2568 | E_err:   0.025398
[2025-10-30 06:51:21] [Iter 1487/1575] R0[1411/1500] | LR: 0.001424 | E:  -59.070096 | E_var:     5.2126 | E_err:   0.026582
[2025-10-30 06:51:37] [Iter 1488/1575] R0[1412/1500] | LR: 0.001415 | E:  -59.074460 | E_var:     5.0497 | E_err:   0.023875
[2025-10-30 06:51:54] [Iter 1489/1575] R0[1413/1500] | LR: 0.001406 | E:  -59.083568 | E_var:     4.8903 | E_err:   0.025647
[2025-10-30 06:52:11] [Iter 1490/1575] R0[1414/1500] | LR: 0.001396 | E:  -59.074512 | E_var:     4.9086 | E_err:   0.024238
[2025-10-30 06:52:27] [Iter 1491/1575] R0[1415/1500] | LR: 0.001387 | E:  -59.069361 | E_var:     4.7383 | E_err:   0.025320
[2025-10-30 06:52:44] [Iter 1492/1575] R0[1416/1500] | LR: 0.001378 | E:  -59.050303 | E_var:     4.7960 | E_err:   0.025110
[2025-10-30 06:53:00] [Iter 1493/1575] R0[1417/1500] | LR: 0.001369 | E:  -59.057193 | E_var:     4.8310 | E_err:   0.024957
[2025-10-30 06:53:17] [Iter 1494/1575] R0[1418/1500] | LR: 0.001360 | E:  -59.033618 | E_var:     4.6047 | E_err:   0.025091
[2025-10-30 06:53:33] [Iter 1495/1575] R0[1419/1500] | LR: 0.001352 | E:  -59.077345 | E_var:     4.9030 | E_err:   0.024382
[2025-10-30 06:53:50] [Iter 1496/1575] R0[1420/1500] | LR: 0.001343 | E:  -59.048071 | E_var:     4.9316 | E_err:   0.025594
[2025-10-30 06:54:06] [Iter 1497/1575] R0[1421/1500] | LR: 0.001335 | E:  -59.040961 | E_var:     4.8096 | E_err:   0.024369
[2025-10-30 06:54:23] [Iter 1498/1575] R0[1422/1500] | LR: 0.001326 | E:  -59.057052 | E_var:     4.9342 | E_err:   0.025001
[2025-10-30 06:54:40] [Iter 1499/1575] R0[1423/1500] | LR: 0.001318 | E:  -59.072898 | E_var:     4.8186 | E_err:   0.025871
[2025-10-30 06:54:56] [Iter 1500/1575] R0[1424/1500] | LR: 0.001310 | E:  -59.065831 | E_var:     4.8504 | E_err:   0.024067
[2025-10-30 06:55:13] [Iter 1501/1575] R0[1425/1500] | LR: 0.001302 | E:  -59.099189 | E_var:     4.9161 | E_err:   0.024721
[2025-10-30 06:55:29] [Iter 1502/1575] R0[1426/1500] | LR: 0.001294 | E:  -59.065416 | E_var:     4.8948 | E_err:   0.024555
[2025-10-30 06:55:46] [Iter 1503/1575] R0[1427/1500] | LR: 0.001286 | E:  -59.071204 | E_var:     4.8545 | E_err:   0.025952
[2025-10-30 06:56:02] [Iter 1504/1575] R0[1428/1500] | LR: 0.001278 | E:  -59.085657 | E_var:     4.7967 | E_err:   0.024632
[2025-10-30 06:56:19] [Iter 1505/1575] R0[1429/1500] | LR: 0.001270 | E:  -59.108131 | E_var:     4.7394 | E_err:   0.024301
[2025-10-30 06:56:35] [Iter 1506/1575] R0[1430/1500] | LR: 0.001263 | E:  -59.096972 | E_var:     4.7870 | E_err:   0.023474
[2025-10-30 06:56:52] [Iter 1507/1575] R0[1431/1500] | LR: 0.001255 | E:  -59.092106 | E_var:     4.6906 | E_err:   0.025320
[2025-10-30 06:57:08] [Iter 1508/1575] R0[1432/1500] | LR: 0.001248 | E:  -59.056722 | E_var:     4.8408 | E_err:   0.024947
[2025-10-30 06:57:25] [Iter 1509/1575] R0[1433/1500] | LR: 0.001241 | E:  -59.074790 | E_var:     4.6035 | E_err:   0.023258
[2025-10-30 06:57:42] [Iter 1510/1575] R0[1434/1500] | LR: 0.001234 | E:  -59.052776 | E_var:     4.7782 | E_err:   0.024875
[2025-10-30 06:57:58] [Iter 1511/1575] R0[1435/1500] | LR: 0.001227 | E:  -59.087711 | E_var:     4.7775 | E_err:   0.024448
[2025-10-30 06:58:15] [Iter 1512/1575] R0[1436/1500] | LR: 0.001220 | E:  -59.048745 | E_var:     4.7664 | E_err:   0.024219
[2025-10-30 06:58:31] [Iter 1513/1575] R0[1437/1500] | LR: 0.001213 | E:  -59.087614 | E_var:     4.8451 | E_err:   0.025535
[2025-10-30 06:58:48] [Iter 1514/1575] R0[1438/1500] | LR: 0.001206 | E:  -59.064766 | E_var:     5.0221 | E_err:   0.023597
[2025-10-30 06:59:04] [Iter 1515/1575] R0[1439/1500] | LR: 0.001200 | E:  -59.075912 | E_var:     4.9600 | E_err:   0.024836
[2025-10-30 06:59:21] [Iter 1516/1575] R0[1440/1500] | LR: 0.001193 | E:  -59.072638 | E_var:     4.7966 | E_err:   0.024430
[2025-10-30 06:59:37] [Iter 1517/1575] R0[1441/1500] | LR: 0.001187 | E:  -59.065528 | E_var:     4.9076 | E_err:   0.025069
[2025-10-30 06:59:54] [Iter 1518/1575] R0[1442/1500] | LR: 0.001181 | E:  -59.087523 | E_var:     4.9183 | E_err:   0.024143
[2025-10-30 07:00:11] [Iter 1519/1575] R0[1443/1500] | LR: 0.001174 | E:  -59.088284 | E_var:     5.0569 | E_err:   0.024474
[2025-10-30 07:00:27] [Iter 1520/1575] R0[1444/1500] | LR: 0.001168 | E:  -59.028932 | E_var:     4.9565 | E_err:   0.025130
[2025-10-30 07:00:44] [Iter 1521/1575] R0[1445/1500] | LR: 0.001162 | E:  -59.047180 | E_var:     4.9283 | E_err:   0.025381
[2025-10-30 07:01:00] [Iter 1522/1575] R0[1446/1500] | LR: 0.001157 | E:  -59.083330 | E_var:     4.8209 | E_err:   0.024311
[2025-10-30 07:01:17] [Iter 1523/1575] R0[1447/1500] | LR: 0.001151 | E:  -59.055763 | E_var:     5.0499 | E_err:   0.024925
[2025-10-30 07:01:33] [Iter 1524/1575] R0[1448/1500] | LR: 0.001145 | E:  -59.065164 | E_var:     4.8616 | E_err:   0.023882
[2025-10-30 07:01:50] [Iter 1525/1575] R0[1449/1500] | LR: 0.001140 | E:  -59.078806 | E_var:     4.8072 | E_err:   0.024022
[2025-10-30 07:02:06] [Iter 1526/1575] R0[1450/1500] | LR: 0.001134 | E:  -59.118011 | E_var:     5.0222 | E_err:   0.025323
[2025-10-30 07:02:23] [Iter 1527/1575] R0[1451/1500] | LR: 0.001129 | E:  -59.078944 | E_var:     5.0581 | E_err:   0.025500
[2025-10-30 07:02:40] [Iter 1528/1575] R0[1452/1500] | LR: 0.001124 | E:  -59.084701 | E_var:     4.8642 | E_err:   0.024256
[2025-10-30 07:02:56] [Iter 1529/1575] R0[1453/1500] | LR: 0.001119 | E:  -59.067399 | E_var:     4.7642 | E_err:   0.025206
[2025-10-30 07:03:13] [Iter 1530/1575] R0[1454/1500] | LR: 0.001114 | E:  -59.063099 | E_var:     4.7394 | E_err:   0.024242
[2025-10-30 07:03:29] [Iter 1531/1575] R0[1455/1500] | LR: 0.001109 | E:  -59.074292 | E_var:     4.8781 | E_err:   0.024727
[2025-10-30 07:03:46] [Iter 1532/1575] R0[1456/1500] | LR: 0.001104 | E:  -59.057561 | E_var:     4.8721 | E_err:   0.023619
[2025-10-30 07:04:02] [Iter 1533/1575] R0[1457/1500] | LR: 0.001099 | E:  -59.070618 | E_var:     4.8854 | E_err:   0.024650
[2025-10-30 07:04:19] [Iter 1534/1575] R0[1458/1500] | LR: 0.001095 | E:  -59.069330 | E_var:     4.9305 | E_err:   0.025179
[2025-10-30 07:04:35] [Iter 1535/1575] R0[1459/1500] | LR: 0.001090 | E:  -59.098315 | E_var:     4.8022 | E_err:   0.024466
[2025-10-30 07:04:52] [Iter 1536/1575] R0[1460/1500] | LR: 0.001086 | E:  -59.089274 | E_var:     4.9434 | E_err:   0.025367
[2025-10-30 07:05:09] [Iter 1537/1575] R0[1461/1500] | LR: 0.001082 | E:  -59.065089 | E_var:     5.0844 | E_err:   0.026832
[2025-10-30 07:05:25] [Iter 1538/1575] R0[1462/1500] | LR: 0.001078 | E:  -59.096894 | E_var:     5.1128 | E_err:   0.025969
[2025-10-30 07:05:42] [Iter 1539/1575] R0[1463/1500] | LR: 0.001074 | E:  -59.084825 | E_var:     5.0132 | E_err:   0.024695
[2025-10-30 07:05:58] [Iter 1540/1575] R0[1464/1500] | LR: 0.001070 | E:  -59.131359 | E_var:     4.9641 | E_err:   0.025725
[2025-10-30 07:06:15] [Iter 1541/1575] R0[1465/1500] | LR: 0.001066 | E:  -59.035677 | E_var:     5.0689 | E_err:   0.024825
[2025-10-30 07:06:31] [Iter 1542/1575] R0[1466/1500] | LR: 0.001062 | E:  -59.102145 | E_var:     5.0403 | E_err:   0.026092
[2025-10-30 07:06:48] [Iter 1543/1575] R0[1467/1500] | LR: 0.001058 | E:  -59.066662 | E_var:     4.9959 | E_err:   0.025741
[2025-10-30 07:07:04] [Iter 1544/1575] R0[1468/1500] | LR: 0.001055 | E:  -59.118601 | E_var:     4.8185 | E_err:   0.024937
[2025-10-30 07:07:21] [Iter 1545/1575] R0[1469/1500] | LR: 0.001052 | E:  -59.129950 | E_var:     4.6603 | E_err:   0.022520
[2025-10-30 07:07:38] [Iter 1546/1575] R0[1470/1500] | LR: 0.001048 | E:  -59.060115 | E_var:     4.8819 | E_err:   0.024203
[2025-10-30 07:07:54] [Iter 1547/1575] R0[1471/1500] | LR: 0.001045 | E:  -59.080408 | E_var:     4.7671 | E_err:   0.024110
[2025-10-30 07:08:11] [Iter 1548/1575] R0[1472/1500] | LR: 0.001042 | E:  -59.012136 | E_var:     4.8958 | E_err:   0.024707
[2025-10-30 07:08:27] [Iter 1549/1575] R0[1473/1500] | LR: 0.001039 | E:  -59.080297 | E_var:     4.8134 | E_err:   0.024159
[2025-10-30 07:08:44] [Iter 1550/1575] R0[1474/1500] | LR: 0.001036 | E:  -59.089619 | E_var:     4.9992 | E_err:   0.025810
[2025-10-30 07:09:00] [Iter 1551/1575] R0[1475/1500] | LR: 0.001034 | E:  -59.070373 | E_var:     4.9413 | E_err:   0.025651
[2025-10-30 07:09:17] [Iter 1552/1575] R0[1476/1500] | LR: 0.001031 | E:  -59.074490 | E_var:     4.7651 | E_err:   0.025208
[2025-10-30 07:09:33] [Iter 1553/1575] R0[1477/1500] | LR: 0.001028 | E:  -58.998433 | E_var:     4.9296 | E_err:   0.025295
[2025-10-30 07:09:50] [Iter 1554/1575] R0[1478/1500] | LR: 0.001026 | E:  -59.057866 | E_var:     4.8018 | E_err:   0.025131
[2025-10-30 07:10:07] [Iter 1555/1575] R0[1479/1500] | LR: 0.001024 | E:  -59.082931 | E_var:     4.7705 | E_err:   0.024527
[2025-10-30 07:10:23] [Iter 1556/1575] R0[1480/1500] | LR: 0.001021 | E:  -59.074044 | E_var:     4.7056 | E_err:   0.025027
[2025-10-30 07:10:40] [Iter 1557/1575] R0[1481/1500] | LR: 0.001019 | E:  -59.086986 | E_var:     4.7441 | E_err:   0.026026
[2025-10-30 07:10:56] [Iter 1558/1575] R0[1482/1500] | LR: 0.001017 | E:  -59.086420 | E_var:     4.7586 | E_err:   0.024956
[2025-10-30 07:11:13] [Iter 1559/1575] R0[1483/1500] | LR: 0.001016 | E:  -59.071767 | E_var:     4.7422 | E_err:   0.024942
[2025-10-30 07:11:29] [Iter 1560/1575] R0[1484/1500] | LR: 0.001014 | E:  -59.097984 | E_var:     4.7913 | E_err:   0.024128
[2025-10-30 07:11:46] [Iter 1561/1575] R0[1485/1500] | LR: 0.001012 | E:  -59.112869 | E_var:     4.7695 | E_err:   0.024379
[2025-10-30 07:12:03] [Iter 1562/1575] R0[1486/1500] | LR: 0.001011 | E:  -59.101186 | E_var:     4.6608 | E_err:   0.024162
[2025-10-30 07:12:19] [Iter 1563/1575] R0[1487/1500] | LR: 0.001009 | E:  -59.050807 | E_var:     4.8198 | E_err:   0.024172
[2025-10-30 07:12:36] [Iter 1564/1575] R0[1488/1500] | LR: 0.001008 | E:  -59.088357 | E_var:     4.8491 | E_err:   0.024745
[2025-10-30 07:12:52] [Iter 1565/1575] R0[1489/1500] | LR: 0.001007 | E:  -59.090929 | E_var:     4.5826 | E_err:   0.024693
[2025-10-30 07:13:09] [Iter 1566/1575] R0[1490/1500] | LR: 0.001005 | E:  -59.074313 | E_var:     4.7647 | E_err:   0.025163
[2025-10-30 07:13:25] [Iter 1567/1575] R0[1491/1500] | LR: 0.001004 | E:  -59.121122 | E_var:     4.8079 | E_err:   0.024297
[2025-10-30 07:13:42] [Iter 1568/1575] R0[1492/1500] | LR: 0.001003 | E:  -59.082674 | E_var:     4.7236 | E_err:   0.024452
[2025-10-30 07:13:58] [Iter 1569/1575] R0[1493/1500] | LR: 0.001003 | E:  -59.048675 | E_var:     4.6996 | E_err:   0.024846
[2025-10-30 07:14:15] [Iter 1570/1575] R0[1494/1500] | LR: 0.001002 | E:  -59.063713 | E_var:     4.7217 | E_err:   0.024816
[2025-10-30 07:14:32] [Iter 1571/1575] R0[1495/1500] | LR: 0.001001 | E:  -59.088701 | E_var:     4.8333 | E_err:   0.025496
[2025-10-30 07:14:48] [Iter 1572/1575] R0[1496/1500] | LR: 0.001001 | E:  -59.141335 | E_var:     4.7748 | E_err:   0.024542
[2025-10-30 07:15:05] [Iter 1573/1575] R0[1497/1500] | LR: 0.001000 | E:  -59.053247 | E_var:     4.9738 | E_err:   0.024889
[2025-10-30 07:15:21] [Iter 1574/1575] R0[1498/1500] | LR: 0.001000 | E:  -59.093006 | E_var:     4.9732 | E_err:   0.025268
[2025-10-30 07:15:38] [Iter 1575/1575] R0[1499/1500] | LR: 0.001000 | E:  -59.064410 | E_var:     4.9640 | E_err:   0.024612
[2025-10-30 07:15:38] ✓ Checkpoint saved: checkpoint_iter_001500.pkl
[2025-10-30 07:15:38] ======================================================================================================
[2025-10-30 07:15:38] ✅ Training completed successfully
[2025-10-30 07:15:38] Total restarts: 0
[2025-10-30 07:15:41] Final Energy: -59.06440975 ± 0.02461196
[2025-10-30 07:15:41] Final Variance: 4.963977
[2025-10-30 07:15:41] ======================================================================================================
[2025-10-30 07:15:41] ======================================================================================================
[2025-10-30 07:15:41] Training completed | Runtime: 26208.1s

[2025-11-11 16:24:49] ✓ 创建部分等变ViT模型（嵌入层等变）
[2025-11-11 16:24:49]   对称性组大小: 8 (C4v点群)
[2025-11-11 16:24:49]   嵌入层使用等变权重共享，后续Transformer层使用普通结构
[2025-11-11 16:25:10] 🔥 预热编译: 执行模型前向传播以触发JIT编译...
[2025-11-11 16:25:17] ✓ 预热编译完成 | 耗时: 7.31s
[2025-11-11 16:25:17] ======================================================================================================
[2025-11-11 16:25:17] ViT for Shastry-Sutherland Model
[2025-11-11 16:25:17] ======================================================================================================
[2025-11-11 16:25:17] System Parameters:
[2025-11-11 16:25:17]   - Lattice size: L = 6
[2025-11-11 16:25:17]   - Total sites: N = 144
[2025-11-11 16:25:17]   - J1 coupling: 0.8
[2025-11-11 16:25:17]   - J2 coupling: 1.0
[2025-11-11 16:25:17]   - Q (4-spin): 0.0
[2025-11-11 16:25:17] ------------------------------------------------------------------------------------------------------
[2025-11-11 16:25:17] Model Parameters:
[2025-11-11 16:25:17]   ViT Architecture:
[2025-11-11 16:25:17]     • Layers: 4
[2025-11-11 16:25:17]     • Embedding dimension (d_model): 96
[2025-11-11 16:25:17]     • Attention heads: 4
[2025-11-11 16:25:17]     • Patch size: 2x2
[2025-11-11 16:25:17]     • Number of patches: 36
[2025-11-11 16:25:17]     • Config: [4, 96, 4, 2]
[2025-11-11 16:25:17]     • Total parameters: 482,320
[2025-11-11 16:25:17]   Regularization:
[2025-11-11 16:25:17]     • Relative Position Encoding (RPE): True
[2025-11-11 16:25:17]     • Dropout rate: 0.0
[2025-11-11 16:25:17]   Optimizer:
[2025-11-11 16:25:17]     • Diagonal shift (SR): 0.01
[2025-11-11 16:25:17]   Mixed Precision Training: True
[2025-11-11 16:25:17]     • Parameters: float64 (high precision)
[2025-11-11 16:25:17]     • Computation: bfloat16 (accelerated)
[2025-11-11 16:25:17]     • Critical ops (LayerNorm/Softmax/Output): float64
[2025-11-11 16:25:17]     • Expected speedup: 1.5-2x, Memory reduction: ~50%
[2025-11-11 16:25:17] ------------------------------------------------------------------------------------------------------
[2025-11-11 16:25:17] Training Hyperparameters:
[2025-11-11 16:25:17]   Learning Rate Schedule:
[2025-11-11 16:25:17]     • Max LR: 0.05
[2025-11-11 16:25:17]     • Min LR: 1e-07
[2025-11-11 16:25:17]     • Annealing cycles: 1
[2025-11-11 16:25:17]     • Initial period: 1500
[2025-11-11 16:25:17]     • Period multiplier: 1.0
[2025-11-11 16:25:17]     • Warm-up: 75 iterations (5.0%)
[2025-11-11 16:25:17]     • Total iterations: 1500 + 75 (warm-up) = 1575
[2025-11-11 16:25:17]   Sampling Parameters:
[2025-11-11 16:25:17]     • Samples (n_samples): 8192
[2025-11-11 16:25:17]     • Parallel chains (n_chains): 8192
[2025-11-11 16:25:17]     • Max exchange distance (d_max): 2
[2025-11-11 16:25:17]     • Chunk size: 8192
[2025-11-11 16:25:17]     • Discarded samples per chain: 0
[2025-11-11 16:25:17]     • Parameter-to-sample ratio: 58.88
[2025-11-11 16:25:17]       ⚠ High ratio (> 50), high overfitting risk!
[2025-11-11 16:25:17] ------------------------------------------------------------------------------------------------------
[2025-11-11 16:25:17] Checkpoint Configuration:
[2025-11-11 16:25:17]   • Enabled: Yes
[2025-11-11 16:25:17]   • Save interval: 150 iterations
[2025-11-11 16:25:17]   • Keep history: True
[2025-11-11 16:25:17]   • Directory: saved_models/L=6/J2=1.00/J1=0.80/checkpoints
[2025-11-11 16:25:17] ------------------------------------------------------------------------------------------------------
[2025-11-11 16:25:17] Device Status:
[2025-11-11 16:25:17]   • Device type: H200
[2025-11-11 16:25:17]   • Number of devices: 1
[2025-11-11 16:25:17]   • Sharding enabled: True
[2025-11-11 16:25:18] ======================================================================================================
[2025-11-11 16:25:18] 🔥 Linear Warm-up: 75 iterations (5.0% of 1500) | LR: 0 -> 0.050000
[2025-11-11 16:25:18]    Total iterations: 75 (warm-up) + 1500 (training) = 1575
[2025-11-11 16:27:42] [Iter    1/1575] WARMUP[1/75]  | LR: 0.000667 | E:    1.501812 | E_var:    76.7730 | E_err:   0.096808 | Acc: 0.2430
[2025-11-11 16:27:58] [Iter    2/1575] WARMUP[2/75]  | LR: 0.001333 | E:    0.165291 | E_var:    53.8450 | E_err:   0.081073 | Acc: 0.2254
[2025-11-11 16:28:14] [Iter    3/1575] WARMUP[3/75]  | LR: 0.002000 | E:   -0.431291 | E_var:    54.7189 | E_err:   0.081729 | Acc: 0.2200
[2025-11-11 16:28:29] [Iter    4/1575] WARMUP[4/75]  | LR: 0.002667 | E:   -1.012076 | E_var:    53.5443 | E_err:   0.080847 | Acc: 0.2189
[2025-11-11 16:28:45] [Iter    5/1575] WARMUP[5/75]  | LR: 0.003333 | E:   -1.582045 | E_var:    50.4938 | E_err:   0.078510 | Acc: 0.2165
[2025-11-11 16:29:01] [Iter    6/1575] WARMUP[6/75]  | LR: 0.004000 | E:   -2.228370 | E_var:    54.4185 | E_err:   0.081504 | Acc: 0.2199
[2025-11-11 16:29:17] [Iter    7/1575] WARMUP[7/75]  | LR: 0.004667 | E:   -3.023792 | E_var:    52.3424 | E_err:   0.079934 | Acc: 0.2211
[2025-11-11 16:29:33] [Iter    8/1575] WARMUP[8/75]  | LR: 0.005333 | E:   -3.792427 | E_var:    52.2042 | E_err:   0.079828 | Acc: 0.2251
[2025-11-11 16:29:49] [Iter    9/1575] WARMUP[9/75]  | LR: 0.006000 | E:   -4.738796 | E_var:    52.0041 | E_err:   0.079675 | Acc: 0.2331
[2025-11-11 16:30:05] [Iter   10/1575] WARMUP[10/75] | LR: 0.006667 | E:   -5.715837 | E_var:    56.9242 | E_err:   0.083359 | Acc: 0.2376
[2025-11-11 16:30:21] [Iter   11/1575] WARMUP[11/75] | LR: 0.007333 | E:   -7.158390 | E_var:    53.2447 | E_err:   0.080620 | Acc: 0.2444
[2025-11-11 16:30:37] [Iter   12/1575] WARMUP[12/75] | LR: 0.008000 | E:   -8.522050 | E_var:    58.0094 | E_err:   0.084150 | Acc: 0.2473
[2025-11-11 16:30:53] [Iter   13/1575] WARMUP[13/75] | LR: 0.008667 | E:  -10.105180 | E_var:    55.1788 | E_err:   0.082071 | Acc: 0.2505
[2025-11-11 16:31:09] [Iter   14/1575] WARMUP[14/75] | LR: 0.009333 | E:  -11.638497 | E_var:    52.2470 | E_err:   0.079861 | Acc: 0.2507
[2025-11-11 16:31:25] [Iter   15/1575] WARMUP[15/75] | LR: 0.010000 | E:  -13.295438 | E_var:    56.7503 | E_err:   0.083232 | Acc: 0.2373
[2025-11-11 16:31:41] [Iter   16/1575] WARMUP[16/75] | LR: 0.010667 | E:  -14.705459 | E_var:    51.5430 | E_err:   0.079321 | Acc: 0.2438
[2025-11-11 16:31:56] [Iter   17/1575] WARMUP[17/75] | LR: 0.011333 | E:  -16.148278 | E_var:    50.1590 | E_err:   0.078249 | Acc: 0.2487
[2025-11-11 16:32:12] [Iter   18/1575] WARMUP[18/75] | LR: 0.012000 | E:  -17.586016 | E_var:    47.4002 | E_err:   0.076067 | Acc: 0.2578
[2025-11-11 16:32:28] [Iter   19/1575] WARMUP[19/75] | LR: 0.012667 | E:  -19.509464 | E_var:    49.8180 | E_err:   0.077983 | Acc: 0.2496
[2025-11-11 16:32:44] [Iter   20/1575] WARMUP[20/75] | LR: 0.013333 | E:  -21.420527 | E_var:    47.8485 | E_err:   0.076426 | Acc: 0.2483
[2025-11-11 16:33:00] [Iter   21/1575] WARMUP[21/75] | LR: 0.014000 | E:  -23.918730 | E_var:    50.2355 | E_err:   0.078309 | Acc: 0.2327
[2025-11-11 16:33:16] [Iter   22/1575] WARMUP[22/75] | LR: 0.014667 | E:  -26.382525 | E_var:    46.4184 | E_err:   0.075275 | Acc: 0.2266
[2025-11-11 16:33:32] [Iter   23/1575] WARMUP[23/75] | LR: 0.015333 | E:  -28.188494 | E_var:    45.0597 | E_err:   0.074165 | Acc: 0.2171
[2025-11-11 16:33:48] [Iter   24/1575] WARMUP[24/75] | LR: 0.016000 | E:  -30.478770 | E_var:    43.7409 | E_err:   0.073072 | Acc: 0.2180
[2025-11-11 16:34:04] [Iter   25/1575] WARMUP[25/75] | LR: 0.016667 | E:  -31.860908 | E_var:    49.3049 | E_err:   0.077580 | Acc: 0.2025
[2025-11-11 16:34:20] [Iter   26/1575] WARMUP[26/75] | LR: 0.017333 | E:  -30.943646 | E_var:    62.0190 | E_err:   0.087010 | Acc: 0.2020
[2025-11-11 16:34:36] [Iter   27/1575] WARMUP[27/75] | LR: 0.018000 | E:  -34.552972 | E_var:    54.0929 | E_err:   0.081260 | Acc: 0.2103
[2025-11-11 16:34:52] [Iter   28/1575] WARMUP[28/75] | LR: 0.018667 | E:  -36.422666 | E_var:    53.8641 | E_err:   0.081088 | Acc: 0.1976
[2025-11-11 16:35:08] [Iter   29/1575] WARMUP[29/75] | LR: 0.019333 | E:  -38.023802 | E_var:    41.5410 | E_err:   0.071210 | Acc: 0.1895
[2025-11-11 16:35:23] [Iter   30/1575] WARMUP[30/75] | LR: 0.020000 | E:  -38.167651 | E_var:    33.5047 | E_err:   0.063953 | Acc: 0.2214
[2025-11-11 16:35:39] [Iter   31/1575] WARMUP[31/75] | LR: 0.020667 | E:  -38.516104 | E_var:    32.4661 | E_err:   0.062954 | Acc: 0.2511
[2025-11-11 16:35:55] [Iter   32/1575] WARMUP[32/75] | LR: 0.021333 | E:  -39.788335 | E_var:    31.0473 | E_err:   0.061563 | Acc: 0.2809
[2025-11-11 16:36:11] [Iter   33/1575] WARMUP[33/75] | LR: 0.022000 | E:  -41.350720 | E_var:    37.2562 | E_err:   0.067438 | Acc: 0.2525
[2025-11-11 16:36:27] [Iter   34/1575] WARMUP[34/75] | LR: 0.022667 | E:  -41.472117 | E_var:    29.7274 | E_err:   0.060240 | Acc: 0.2516
[2025-11-11 16:36:43] [Iter   35/1575] WARMUP[35/75] | LR: 0.023333 | E:  -41.813424 | E_var:    30.1334 | E_err:   0.060650 | Acc: 0.2572
[2025-11-11 16:36:59] [Iter   36/1575] WARMUP[36/75] | LR: 0.024000 | E:  -38.482837 | E_var:    36.0524 | E_err:   0.066339 | Acc: 0.3078
[2025-11-11 16:37:15] [Iter   37/1575] WARMUP[37/75] | LR: 0.024667 | E:  -40.803345 | E_var:    32.7865 | E_err:   0.063263 | Acc: 0.3115
[2025-11-11 16:37:31] [Iter   38/1575] WARMUP[38/75] | LR: 0.025333 | E:  -38.469550 | E_var:    36.3474 | E_err:   0.066610 | Acc: 0.3258
[2025-11-11 16:37:47] [Iter   39/1575] WARMUP[39/75] | LR: 0.026000 | E:  -39.744519 | E_var:    32.7439 | E_err:   0.063222 | Acc: 0.3330
[2025-11-11 16:38:03] [Iter   40/1575] WARMUP[40/75] | LR: 0.026667 | E:  -40.395563 | E_var:    36.3257 | E_err:   0.066590 | Acc: 0.3206
[2025-11-11 16:38:19] [Iter   41/1575] WARMUP[41/75] | LR: 0.027333 | E:  -42.588343 | E_var:    27.6566 | E_err:   0.058104 | Acc: 0.3281
[2025-11-11 16:38:35] [Iter   42/1575] WARMUP[42/75] | LR: 0.028000 | E:  -38.762161 | E_var:    34.3573 | E_err:   0.064761 | Acc: 0.3728
[2025-11-11 16:38:50] [Iter   43/1575] WARMUP[43/75] | LR: 0.028667 | E:  -41.515760 | E_var:    28.0689 | E_err:   0.058535 | Acc: 0.4054
[2025-11-11 16:39:06] [Iter   44/1575] WARMUP[44/75] | LR: 0.029333 | E:  -40.092975 | E_var:    28.6198 | E_err:   0.059107 | Acc: 0.4605
[2025-11-11 16:39:22] [Iter   45/1575] WARMUP[45/75] | LR: 0.030000 | E:  -42.703231 | E_var:    27.2481 | E_err:   0.057673 | Acc: 0.3938
[2025-11-11 16:39:38] [Iter   46/1575] WARMUP[46/75] | LR: 0.030667 | E:  -45.022576 | E_var:    23.7050 | E_err:   0.053793 | Acc: 0.3825
[2025-11-11 16:39:54] [Iter   47/1575] WARMUP[47/75] | LR: 0.031333 | E:  -46.255082 | E_var:    29.6823 | E_err:   0.060194 | Acc: 0.3346
[2025-11-11 16:40:10] [Iter   48/1575] WARMUP[48/75] | LR: 0.032000 | E:  -44.705838 | E_var:    28.2212 | E_err:   0.058694 | Acc: 0.3872
[2025-11-11 16:40:26] [Iter   49/1575] WARMUP[49/75] | LR: 0.032667 | E:  -43.016318 | E_var:    36.1908 | E_err:   0.066467 | Acc: 0.3832
[2025-11-11 16:40:42] [Iter   50/1575] WARMUP[50/75] | LR: 0.033333 | E:  -44.398614 | E_var:    34.7901 | E_err:   0.065168 | Acc: 0.3700
[2025-11-11 16:40:58] [Iter   51/1575] WARMUP[51/75] | LR: 0.034000 | E:  -48.720897 | E_var:    24.3483 | E_err:   0.054518 | Acc: 0.3278
[2025-11-11 16:41:14] [Iter   52/1575] WARMUP[52/75] | LR: 0.034667 | E:  -49.181410 | E_var:    18.8039 | E_err:   0.047910 | Acc: 0.3574
[2025-11-11 16:41:30] [Iter   53/1575] WARMUP[53/75] | LR: 0.035333 | E:  -48.661843 | E_var:    19.0210 | E_err:   0.048186 | Acc: 0.3970
[2025-11-11 16:41:46] [Iter   54/1575] WARMUP[54/75] | LR: 0.036000 | E:  -47.603737 | E_var:    20.2828 | E_err:   0.049759 | Acc: 0.4163
[2025-11-11 16:42:02] [Iter   55/1575] WARMUP[55/75] | LR: 0.036667 | E:  -49.410427 | E_var:    18.2058 | E_err:   0.047142 | Acc: 0.4019
[2025-11-11 16:42:17] [Iter   56/1575] WARMUP[56/75] | LR: 0.037333 | E:  -38.239740 | E_var:   165.9087 | E_err:   0.142311 | Acc: 0.4645
[2025-11-11 16:42:33] [Iter   57/1575] WARMUP[57/75] | LR: 0.038000 | E:  -48.445233 | E_var:    20.2140 | E_err:   0.049674 | Acc: 0.4714
[2025-11-11 16:42:49] [Iter   58/1575] WARMUP[58/75] | LR: 0.038667 | E:  -49.071813 | E_var:    17.7928 | E_err:   0.046604 | Acc: 0.4474
[2025-11-11 16:43:05] [Iter   59/1575] WARMUP[59/75] | LR: 0.039333 | E:  -37.083517 | E_var:   148.6727 | E_err:   0.134716 | Acc: 0.4913
[2025-11-11 16:43:21] [Iter   60/1575] WARMUP[60/75] | LR: 0.040000 | E:  -47.292182 | E_var:    29.2113 | E_err:   0.059715 | Acc: 0.4678
[2025-11-11 16:43:37] [Iter   61/1575] WARMUP[61/75] | LR: 0.040667 | E:  -43.260770 | E_var:    27.8016 | E_err:   0.058256 | Acc: 0.5933
[2025-11-11 16:43:53] [Iter   62/1575] WARMUP[62/75] | LR: 0.041333 | E:  -47.910669 | E_var:    19.7148 | E_err:   0.049057 | Acc: 0.4870
[2025-11-11 16:44:09] [Iter   63/1575] WARMUP[63/75] | LR: 0.042000 | E:  -47.756519 | E_var:    18.9673 | E_err:   0.048118 | Acc: 0.4800
[2025-11-11 16:44:25] [Iter   64/1575] WARMUP[64/75] | LR: 0.042667 | E:  -49.884433 | E_var:    18.9103 | E_err:   0.048046 | Acc: 0.4274
[2025-11-11 16:44:41] [Iter   65/1575] WARMUP[65/75] | LR: 0.043333 | E:  -49.511822 | E_var:    31.6792 | E_err:   0.062186 | Acc: 0.3834
[2025-11-11 16:44:57] [Iter   66/1575] WARMUP[66/75] | LR: 0.044000 | E:  -47.197374 | E_var:    23.3315 | E_err:   0.053367 | Acc: 0.3709
[2025-11-11 16:45:13] [Iter   67/1575] WARMUP[67/75] | LR: 0.044667 | E:  -39.895496 | E_var:    29.1517 | E_err:   0.059654 | Acc: 0.5640
[2025-11-11 16:45:28] [Iter   68/1575] WARMUP[68/75] | LR: 0.045333 | E:  -44.937065 | E_var:    22.4858 | E_err:   0.052391 | Acc: 0.4744
[2025-11-11 16:45:44] [Iter   69/1575] WARMUP[69/75] | LR: 0.046000 | E:  -46.036283 | E_var:    23.4172 | E_err:   0.053465 | Acc: 0.4086
[2025-11-11 16:46:00] [Iter   70/1575] WARMUP[70/75] | LR: 0.046667 | E:  -36.271619 | E_var:    33.1742 | E_err:   0.063636 | Acc: 0.6593
[2025-11-11 16:46:16] [Iter   71/1575] WARMUP[71/75] | LR: 0.047333 | E:  -44.984318 | E_var:    21.8881 | E_err:   0.051690 | Acc: 0.4852
[2025-11-11 16:46:32] [Iter   72/1575] WARMUP[72/75] | LR: 0.048000 | E:  -45.434020 | E_var:    24.8454 | E_err:   0.055072 | Acc: 0.4433
[2025-11-11 16:46:48] [Iter   73/1575] WARMUP[73/75] | LR: 0.048667 | E:  -46.683443 | E_var:    23.1682 | E_err:   0.053180 | Acc: 0.4527
[2025-11-11 16:47:04] [Iter   74/1575] WARMUP[74/75] | LR: 0.049333 | E:  -48.126122 | E_var:    28.3058 | E_err:   0.058782 | Acc: 0.4185
[2025-11-11 16:47:04] ✅ Warm-up completed | Starting cosine annealing from LR=0.050000
[2025-11-11 16:47:20] [Iter   75/1575] WARMUP[75/75] | LR: 0.050000 | E:  -40.688107 | E_var:    32.3215 | E_err:   0.062813 | Acc: 0.6390
[2025-11-11 16:47:36] [Iter   76/1575] R0[0/1500]    | LR: 0.050000 | E:  -44.270472 | E_var:    22.8543 | E_err:   0.052819 | Acc: 0.6181
[2025-11-11 16:47:52] [Iter   77/1575] R0[1/1500]    | LR: 0.050000 | E:  -48.408323 | E_var:    24.9336 | E_err:   0.055169 | Acc: 0.4675
[2025-11-11 16:48:08] [Iter   78/1575] R0[2/1500]    | LR: 0.050000 | E:  -35.638093 | E_var:    35.3630 | E_err:   0.065702 | Acc: 0.7146
[2025-11-11 16:48:24] [Iter   79/1575] R0[3/1500]    | LR: 0.050000 | E:  -41.502497 | E_var:    27.0287 | E_err:   0.057440 | Acc: 0.6394
[2025-11-11 16:48:39] [Iter   80/1575] R0[4/1500]    | LR: 0.049999 | E:  -45.165042 | E_var:    21.4748 | E_err:   0.051200 | Acc: 0.5749
[2025-11-11 16:48:55] [Iter   81/1575] R0[5/1500]    | LR: 0.049999 | E:  -50.704120 | E_var:    15.5353 | E_err:   0.043548 | Acc: 0.4841
[2025-11-11 16:49:11] [Iter   82/1575] R0[6/1500]    | LR: 0.049998 | E:  -44.738727 | E_var:    20.8805 | E_err:   0.050487 | Acc: 0.6788
[2025-11-11 16:49:27] [Iter   83/1575] R0[7/1500]    | LR: 0.049997 | E:  -49.915398 | E_var:    16.3651 | E_err:   0.044696 | Acc: 0.5397
[2025-11-11 16:49:43] [Iter   84/1575] R0[8/1500]    | LR: 0.049996 | E:  -50.066701 | E_var:    16.0449 | E_err:   0.044256 | Acc: 0.5006
[2025-11-11 16:49:59] [Iter   85/1575] R0[9/1500]    | LR: 0.049996 | E:  -48.786068 | E_var:    18.5782 | E_err:   0.047622 | Acc: 0.5217
[2025-11-11 16:50:15] [Iter   86/1575] R0[10/1500]   | LR: 0.049995 | E:  -51.032286 | E_var:    14.8237 | E_err:   0.042539 | Acc: 0.4950
[2025-11-11 16:50:31] [Iter   87/1575] R0[11/1500]   | LR: 0.049993 | E:  -52.810110 | E_var:    12.8712 | E_err:   0.039638 | Acc: 0.4466
[2025-11-11 16:50:47] [Iter   88/1575] R0[12/1500]   | LR: 0.049992 | E:  -52.865203 | E_var:    12.1329 | E_err:   0.038485 | Acc: 0.4805
[2025-11-11 16:51:03] [Iter   89/1575] R0[13/1500]   | LR: 0.049991 | E:  -54.160106 | E_var:    11.1357 | E_err:   0.036869 | Acc: 0.4494
[2025-11-11 16:51:19] [Iter   90/1575] R0[14/1500]   | LR: 0.049989 | E:  -50.788910 | E_var:    15.2467 | E_err:   0.043141 | Acc: 0.5647
[2025-11-11 16:51:35] [Iter   91/1575] R0[15/1500]   | LR: 0.049988 | E:  -54.760808 | E_var:    11.2574 | E_err:   0.037070 | Acc: 0.4656
[2025-11-11 16:51:51] [Iter   92/1575] R0[16/1500]   | LR: 0.049986 | E:  -53.045646 | E_var:    12.1853 | E_err:   0.038568 | Acc: 0.5218
[2025-11-11 16:52:06] [Iter   93/1575] R0[17/1500]   | LR: 0.049984 | E:  -53.967400 | E_var:    11.2650 | E_err:   0.037083 | Acc: 0.4977
[2025-11-11 16:52:22] [Iter   94/1575] R0[18/1500]   | LR: 0.049982 | E:  -55.562252 | E_var:    10.9841 | E_err:   0.036617 | Acc: 0.4431
[2025-11-11 16:52:38] [Iter   95/1575] R0[19/1500]   | LR: 0.049980 | E:  -51.832935 | E_var:    13.9163 | E_err:   0.041216 | Acc: 0.5720
[2025-11-11 16:52:54] [Iter   96/1575] R0[20/1500]   | LR: 0.049978 | E:  -51.569605 | E_var:    13.7860 | E_err:   0.041023 | Acc: 0.5901
[2025-11-11 16:53:10] [Iter   97/1575] R0[21/1500]   | LR: 0.049976 | E:  -55.099050 | E_var:    10.8050 | E_err:   0.036318 | Acc: 0.4840
[2025-11-11 16:53:26] [Iter   98/1575] R0[22/1500]   | LR: 0.049973 | E:  -54.993576 | E_var:    10.6309 | E_err:   0.036024 | Acc: 0.4527
[2025-11-11 16:53:42] [Iter   99/1575] R0[23/1500]   | LR: 0.049971 | E:  -53.918979 | E_var:    12.3143 | E_err:   0.038771 | Acc: 0.4898
[2025-11-11 16:53:58] [Iter  100/1575] R0[24/1500]   | LR: 0.049968 | E:  -49.086914 | E_var:    16.9499 | E_err:   0.045487 | Acc: 0.6284
[2025-11-11 16:54:14] [Iter  101/1575] R0[25/1500]   | LR: 0.049966 | E:  -45.683570 | E_var:    21.9365 | E_err:   0.051747 | Acc: 0.6569
[2025-11-11 16:54:30] [Iter  102/1575] R0[26/1500]   | LR: 0.049963 | E:  -49.267570 | E_var:    16.0687 | E_err:   0.044289 | Acc: 0.6187
[2025-11-11 16:54:46] [Iter  103/1575] R0[27/1500]   | LR: 0.049960 | E:  -51.704460 | E_var:    14.0502 | E_err:   0.041414 | Acc: 0.5757
[2025-11-11 16:55:02] [Iter  104/1575] R0[28/1500]   | LR: 0.049957 | E:  -54.378914 | E_var:    11.0999 | E_err:   0.036810 | Acc: 0.5048
[2025-11-11 16:55:18] [Iter  105/1575] R0[29/1500]   | LR: 0.049954 | E:  -53.858409 | E_var:    11.7723 | E_err:   0.037908 | Acc: 0.4899
[2025-11-11 16:55:34] [Iter  106/1575] R0[30/1500]   | LR: 0.049951 | E:  -55.541235 | E_var:    10.2612 | E_err:   0.035392 | Acc: 0.4613
[2025-11-11 16:55:50] [Iter  107/1575] R0[31/1500]   | LR: 0.049947 | E:  -56.607183 | E_var:     9.4141 | E_err:   0.033900 | Acc: 0.4228
[2025-11-11 16:56:05] [Iter  108/1575] R0[32/1500]   | LR: 0.049944 | E:  -56.236189 | E_var:     9.9930 | E_err:   0.034926 | Acc: 0.4175
[2025-11-11 16:56:21] [Iter  109/1575] R0[33/1500]   | LR: 0.049940 | E:  -56.995297 | E_var:     9.0242 | E_err:   0.033190 | Acc: 0.4044
[2025-11-11 16:56:37] [Iter  110/1575] R0[34/1500]   | LR: 0.049937 | E:  -57.168486 | E_var:     9.0537 | E_err:   0.033244 | Acc: 0.3893
[2025-11-11 16:56:53] [Iter  111/1575] R0[35/1500]   | LR: 0.049933 | E:  -57.509480 | E_var:     8.8272 | E_err:   0.032826 | Acc: 0.3776
[2025-11-11 16:57:09] [Iter  112/1575] R0[36/1500]   | LR: 0.049929 | E:  -57.730470 | E_var:     8.6764 | E_err:   0.032544 | Acc: 0.3725
[2025-11-11 16:57:25] [Iter  113/1575] R0[37/1500]   | LR: 0.049925 | E:  -57.197166 | E_var:    10.1629 | E_err:   0.035222 | Acc: 0.3616
[2025-11-11 16:57:41] [Iter  114/1575] R0[38/1500]   | LR: 0.049921 | E:  -57.988287 | E_var:     9.0614 | E_err:   0.033259 | Acc: 0.3475
[2025-11-11 16:57:57] [Iter  115/1575] R0[39/1500]   | LR: 0.049917 | E:  -57.397092 | E_var:    10.0906 | E_err:   0.035097 | Acc: 0.3524
[2025-11-11 16:58:13] [Iter  116/1575] R0[40/1500]   | LR: 0.049912 | E:  -57.762709 | E_var:     8.9177 | E_err:   0.032994 | Acc: 0.3547
[2025-11-11 16:58:29] [Iter  117/1575] R0[41/1500]   | LR: 0.049908 | E:  -57.607115 | E_var:     9.4451 | E_err:   0.033955 | Acc: 0.3489
[2025-11-11 16:58:45] [Iter  118/1575] R0[42/1500]   | LR: 0.049903 | E:  -56.428679 | E_var:    10.7462 | E_err:   0.036219 | Acc: 0.3973
[2025-11-11 16:59:01] [Iter  119/1575] R0[43/1500]   | LR: 0.049899 | E:  -55.166653 | E_var:    11.1087 | E_err:   0.036824 | Acc: 0.4653
[2025-11-11 16:59:17] [Iter  120/1575] R0[44/1500]   | LR: 0.049894 | E:  -56.696447 | E_var:     8.9913 | E_err:   0.033130 | Acc: 0.4384
[2025-11-11 16:59:32] [Iter  121/1575] R0[45/1500]   | LR: 0.049889 | E:  -58.115718 | E_var:    12.4890 | E_err:   0.039045 | Acc: 0.3424
[2025-11-11 16:59:48] [Iter  122/1575] R0[46/1500]   | LR: 0.049884 | E:  -53.927838 | E_var:    13.2055 | E_err:   0.040150 | Acc: 0.5108
[2025-11-11 17:00:04] [Iter  123/1575] R0[47/1500]   | LR: 0.049879 | E:  -54.668990 | E_var:    12.6449 | E_err:   0.039288 | Acc: 0.4640
[2025-11-11 17:00:20] [Iter  124/1575] R0[48/1500]   | LR: 0.049874 | E:  -56.286902 | E_var:    10.5878 | E_err:   0.035951 | Acc: 0.4123
[2025-11-11 17:00:36] [Iter  125/1575] R0[49/1500]   | LR: 0.049868 | E:  -56.233792 | E_var:    10.5499 | E_err:   0.035886 | Acc: 0.3920
[2025-11-11 17:00:52] [Iter  126/1575] R0[50/1500]   | LR: 0.049863 | E:  -55.958324 | E_var:    12.6225 | E_err:   0.039253 | Acc: 0.3599
[2025-11-11 17:01:08] [Iter  127/1575] R0[51/1500]   | LR: 0.049858 | E:  -51.040007 | E_var:    17.6111 | E_err:   0.046366 | Acc: 0.5033
[2025-11-11 17:01:24] [Iter  128/1575] R0[52/1500]   | LR: 0.049852 | E:  -52.901600 | E_var:    14.3295 | E_err:   0.041823 | Acc: 0.4768
[2025-11-11 17:01:40] [Iter  129/1575] R0[53/1500]   | LR: 0.049846 | E:  -55.949836 | E_var:    10.3356 | E_err:   0.035520 | Acc: 0.4060
[2025-11-11 17:01:56] [Iter  130/1575] R0[54/1500]   | LR: 0.049840 | E:  -49.753404 | E_var:    18.6438 | E_err:   0.047706 | Acc: 0.6060
[2025-11-11 17:02:12] [Iter  131/1575] R0[55/1500]   | LR: 0.049834 | E:  -53.588386 | E_var:    13.2521 | E_err:   0.040220 | Acc: 0.5306
[2025-11-11 17:02:28] [Iter  132/1575] R0[56/1500]   | LR: 0.049828 | E:  -55.247122 | E_var:    10.7925 | E_err:   0.036297 | Acc: 0.4551
[2025-11-11 17:02:44] [Iter  133/1575] R0[57/1500]   | LR: 0.049822 | E:  -55.981600 | E_var:    11.5208 | E_err:   0.037501 | Acc: 0.3950
[2025-11-11 17:03:00] [Iter  134/1575] R0[58/1500]   | LR: 0.049816 | E:  -51.864045 | E_var:    17.7628 | E_err:   0.046565 | Acc: 0.4569
[2025-11-11 17:03:15] [Iter  135/1575] R0[59/1500]   | LR: 0.049809 | E:  -54.534392 | E_var:    13.2270 | E_err:   0.040182 | Acc: 0.4283
[2025-11-11 17:03:31] [Iter  136/1575] R0[60/1500]   | LR: 0.049803 | E:  -55.478280 | E_var:    11.3389 | E_err:   0.037204 | Acc: 0.4200
[2025-11-11 17:03:47] [Iter  137/1575] R0[61/1500]   | LR: 0.049796 | E:  -56.494228 | E_var:     9.3738 | E_err:   0.033827 | Acc: 0.4192
[2025-11-11 17:04:03] [Iter  138/1575] R0[62/1500]   | LR: 0.049790 | E:  -56.797771 | E_var:     9.5738 | E_err:   0.034186 | Acc: 0.3834
[2025-11-11 17:04:19] [Iter  139/1575] R0[63/1500]   | LR: 0.049783 | E:  -56.515551 | E_var:     9.5625 | E_err:   0.034166 | Acc: 0.3778
[2025-11-11 17:04:35] [Iter  140/1575] R0[64/1500]   | LR: 0.049776 | E:  -56.219575 | E_var:     9.9842 | E_err:   0.034911 | Acc: 0.3782
[2025-11-11 17:04:51] [Iter  141/1575] R0[65/1500]   | LR: 0.049769 | E:  -57.136704 | E_var:     9.2747 | E_err:   0.033648 | Acc: 0.3574
[2025-11-11 17:05:07] [Iter  142/1575] R0[66/1500]   | LR: 0.049762 | E:  -58.115469 | E_var:     8.6183 | E_err:   0.032435 | Acc: 0.3412
[2025-11-11 17:05:23] [Iter  143/1575] R0[67/1500]   | LR: 0.049754 | E:  -57.645602 | E_var:     8.6279 | E_err:   0.032453 | Acc: 0.3655
[2025-11-11 17:05:39] [Iter  144/1575] R0[68/1500]   | LR: 0.049747 | E:  -57.569111 | E_var:     8.8863 | E_err:   0.032936 | Acc: 0.4013
[2025-11-11 17:05:55] [Iter  145/1575] R0[69/1500]   | LR: 0.049739 | E:  -56.948250 | E_var:     9.8679 | E_err:   0.034707 | Acc: 0.4032
[2025-11-11 17:06:11] [Iter  146/1575] R0[70/1500]   | LR: 0.049732 | E:  -57.221509 | E_var:     8.9397 | E_err:   0.033034 | Acc: 0.3888
[2025-11-11 17:06:27] [Iter  147/1575] R0[71/1500]   | LR: 0.049724 | E:  -57.672774 | E_var:     8.9700 | E_err:   0.033090 | Acc: 0.3586
[2025-11-11 17:06:43] [Iter  148/1575] R0[72/1500]   | LR: 0.049716 | E:  -58.453425 | E_var:     8.0212 | E_err:   0.031291 | Acc: 0.3418
[2025-11-11 17:06:58] [Iter  149/1575] R0[73/1500]   | LR: 0.049708 | E:  -58.412448 | E_var:     8.5795 | E_err:   0.032362 | Acc: 0.3285
[2025-11-11 17:07:14] [Iter  150/1575] R0[74/1500]   | LR: 0.049700 | E:  -58.242131 | E_var:     8.7708 | E_err:   0.032721 | Acc: 0.3249
[2025-11-11 17:07:30] [Iter  151/1575] R0[75/1500]   | LR: 0.049692 | E:  -58.112832 | E_var:     8.8088 | E_err:   0.032792 | Acc: 0.3279
[2025-11-11 17:07:46] [Iter  152/1575] R0[76/1500]   | LR: 0.049684 | E:  -58.770430 | E_var:     9.0755 | E_err:   0.033284 | Acc: 0.3084
[2025-11-11 17:08:02] [Iter  153/1575] R0[77/1500]   | LR: 0.049676 | E:  -57.897866 | E_var:     8.7206 | E_err:   0.032627 | Acc: 0.3556
[2025-11-11 17:08:18] [Iter  154/1575] R0[78/1500]   | LR: 0.049667 | E:  -57.599716 | E_var:     9.3186 | E_err:   0.033727 | Acc: 0.3976
[2025-11-11 17:08:34] [Iter  155/1575] R0[79/1500]   | LR: 0.049659 | E:  -58.327390 | E_var:     8.0804 | E_err:   0.031407 | Acc: 0.3778
[2025-11-11 17:08:50] [Iter  156/1575] R0[80/1500]   | LR: 0.049650 | E:  -58.828396 | E_var:     8.8427 | E_err:   0.032855 | Acc: 0.3366
[2025-11-11 17:09:06] [Iter  157/1575] R0[81/1500]   | LR: 0.049641 | E:  -57.603041 | E_var:     9.1881 | E_err:   0.033490 | Acc: 0.3979
[2025-11-11 17:09:22] [Iter  158/1575] R0[82/1500]   | LR: 0.049632 | E:  -56.731938 | E_var:    10.0517 | E_err:   0.035029 | Acc: 0.4399
[2025-11-11 17:09:38] [Iter  159/1575] R0[83/1500]   | LR: 0.049623 | E:  -57.280846 | E_var:     9.5369 | E_err:   0.034120 | Acc: 0.3939
[2025-11-11 17:09:54] [Iter  160/1575] R0[84/1500]   | LR: 0.049614 | E:  -57.717693 | E_var:     9.3540 | E_err:   0.033791 | Acc: 0.3651
[2025-11-11 17:10:10] [Iter  161/1575] R0[85/1500]   | LR: 0.049605 | E:  -58.299523 | E_var:     7.9164 | E_err:   0.031086 | Acc: 0.3637
[2025-11-11 17:10:25] [Iter  162/1575] R0[86/1500]   | LR: 0.049596 | E:  -58.733891 | E_var:     7.6198 | E_err:   0.030498 | Acc: 0.3574
[2025-11-11 17:10:41] [Iter  163/1575] R0[87/1500]   | LR: 0.049586 | E:  -58.092376 | E_var:     8.1955 | E_err:   0.031630 | Acc: 0.3579
[2025-11-11 17:10:57] [Iter  164/1575] R0[88/1500]   | LR: 0.049577 | E:  -59.045861 | E_var:     7.4476 | E_err:   0.030152 | Acc: 0.3513
[2025-11-11 17:11:13] [Iter  165/1575] R0[89/1500]   | LR: 0.049567 | E:  -58.607518 | E_var:     8.3713 | E_err:   0.031967 | Acc: 0.3356
[2025-11-11 17:11:29] [Iter  166/1575] R0[90/1500]   | LR: 0.049557 | E:  -57.298047 | E_var:    11.6027 | E_err:   0.037634 | Acc: 0.3313
[2025-11-11 17:11:45] [Iter  167/1575] R0[91/1500]   | LR: 0.049547 | E:  -56.036328 | E_var:    12.0862 | E_err:   0.038410 | Acc: 0.3340
[2025-11-11 17:12:01] [Iter  168/1575] R0[92/1500]   | LR: 0.049537 | E:  -56.353421 | E_var:    11.5528 | E_err:   0.037553 | Acc: 0.3326
[2025-11-11 17:12:17] [Iter  169/1575] R0[93/1500]   | LR: 0.049527 | E:  -56.883270 | E_var:    10.8316 | E_err:   0.036362 | Acc: 0.3270
[2025-11-11 17:12:33] [Iter  170/1575] R0[94/1500]   | LR: 0.049517 | E:  -57.819105 | E_var:     9.3965 | E_err:   0.033868 | Acc: 0.3092
[2025-11-11 17:12:49] [Iter  171/1575] R0[95/1500]   | LR: 0.049507 | E:  -58.399009 | E_var:     8.8753 | E_err:   0.032915 | Acc: 0.2995
[2025-11-11 17:13:05] [Iter  172/1575] R0[96/1500]   | LR: 0.049496 | E:  -57.643864 | E_var:     9.3260 | E_err:   0.033741 | Acc: 0.3215
[2025-11-11 17:13:21] [Iter  173/1575] R0[97/1500]   | LR: 0.049486 | E:  -58.261180 | E_var:     8.9360 | E_err:   0.033028 | Acc: 0.3115
[2025-11-11 17:13:37] [Iter  174/1575] R0[98/1500]   | LR: 0.049475 | E:  -58.192026 | E_var:     8.3677 | E_err:   0.031960 | Acc: 0.3267
[2025-11-11 17:13:52] [Iter  175/1575] R0[99/1500]   | LR: 0.049465 | E:  -59.480059 | E_var:     7.5501 | E_err:   0.030359 | Acc: 0.3260
[2025-11-11 17:14:08] [Iter  176/1575] R0[100/1500]  | LR: 0.049454 | E:  -59.675569 | E_var:     7.7428 | E_err:   0.030744 | Acc: 0.3080
[2025-11-11 17:14:24] [Iter  177/1575] R0[101/1500]  | LR: 0.049443 | E:  -59.980763 | E_var:     8.2952 | E_err:   0.031821 | Acc: 0.2897
[2025-11-11 17:14:40] [Iter  178/1575] R0[102/1500]  | LR: 0.049432 | E:  -60.000687 | E_var:     7.4824 | E_err:   0.030222 | Acc: 0.2889
[2025-11-11 17:14:56] [Iter  179/1575] R0[103/1500]  | LR: 0.049421 | E:  -59.927486 | E_var:     7.4633 | E_err:   0.030183 | Acc: 0.2885
[2025-11-11 17:15:12] [Iter  180/1575] R0[104/1500]  | LR: 0.049409 | E:  -60.002868 | E_var:     8.2269 | E_err:   0.031690 | Acc: 0.2831
[2025-11-11 17:15:28] [Iter  181/1575] R0[105/1500]  | LR: 0.049398 | E:  -59.465203 | E_var:     7.8501 | E_err:   0.030956 | Acc: 0.3153
[2025-11-11 17:15:44] [Iter  182/1575] R0[106/1500]  | LR: 0.049386 | E:  -60.162043 | E_var:     8.2398 | E_err:   0.031715 | Acc: 0.2930
[2025-11-11 17:16:00] [Iter  183/1575] R0[107/1500]  | LR: 0.049375 | E:  -60.058798 | E_var:     8.2801 | E_err:   0.031792 | Acc: 0.2686
[2025-11-11 17:16:16] [Iter  184/1575] R0[108/1500]  | LR: 0.049363 | E:  -59.485204 | E_var:     7.7149 | E_err:   0.030688 | Acc: 0.3021
[2025-11-11 17:16:32] [Iter  185/1575] R0[109/1500]  | LR: 0.049351 | E:  -59.988223 | E_var:     8.1098 | E_err:   0.031464 | Acc: 0.2857
[2025-11-11 17:16:48] [Iter  186/1575] R0[110/1500]  | LR: 0.049339 | E:  -59.938087 | E_var:     7.1082 | E_err:   0.029457 | Acc: 0.3082
[2025-11-11 17:17:04] [Iter  187/1575] R0[111/1500]  | LR: 0.049327 | E:  -59.608704 | E_var:     7.7583 | E_err:   0.030774 | Acc: 0.3188
[2025-11-11 17:17:19] [Iter  188/1575] R0[112/1500]  | LR: 0.049315 | E:  -60.048534 | E_var:     7.5199 | E_err:   0.030298 | Acc: 0.3033
[2025-11-11 17:17:35] [Iter  189/1575] R0[113/1500]  | LR: 0.049303 | E:  -60.230128 | E_var:     6.9684 | E_err:   0.029166 | Acc: 0.3049
[2025-11-11 17:17:51] [Iter  190/1575] R0[114/1500]  | LR: 0.049291 | E:  -60.332926 | E_var:     7.0969 | E_err:   0.029433 | Acc: 0.2999
[2025-11-11 17:18:07] [Iter  191/1575] R0[115/1500]  | LR: 0.049278 | E:  -60.245392 | E_var:     7.6376 | E_err:   0.030534 | Acc: 0.3007
[2025-11-11 17:18:23] [Iter  192/1575] R0[116/1500]  | LR: 0.049266 | E:  -60.380573 | E_var:     7.3036 | E_err:   0.029859 | Acc: 0.2951
[2025-11-11 17:18:39] [Iter  193/1575] R0[117/1500]  | LR: 0.049253 | E:  -59.163830 | E_var:     9.6939 | E_err:   0.034400 | Acc: 0.2932
[2025-11-11 17:18:55] [Iter  194/1575] R0[118/1500]  | LR: 0.049240 | E:  -58.322069 | E_var:     9.9879 | E_err:   0.034917 | Acc: 0.3222
[2025-11-11 17:19:11] [Iter  195/1575] R0[119/1500]  | LR: 0.049228 | E:  -58.555648 | E_var:     9.6023 | E_err:   0.034237 | Acc: 0.3210
[2025-11-11 17:19:27] [Iter  196/1575] R0[120/1500]  | LR: 0.049215 | E:  -58.908306 | E_var:     8.5419 | E_err:   0.032291 | Acc: 0.3231
[2025-11-11 17:19:43] [Iter  197/1575] R0[121/1500]  | LR: 0.049202 | E:  -58.692305 | E_var:     8.9389 | E_err:   0.033033 | Acc: 0.3155
[2025-11-11 17:19:59] [Iter  198/1575] R0[122/1500]  | LR: 0.049188 | E:  -59.145251 | E_var:     8.2452 | E_err:   0.031725 | Acc: 0.3083
[2025-11-11 17:20:15] [Iter  199/1575] R0[123/1500]  | LR: 0.049175 | E:  -59.352125 | E_var:     7.8828 | E_err:   0.031020 | Acc: 0.3054
[2025-11-11 17:20:31] [Iter  200/1575] R0[124/1500]  | LR: 0.049162 | E:  -59.435178 | E_var:     8.1029 | E_err:   0.031450 | Acc: 0.3033
[2025-11-11 17:20:47] [Iter  201/1575] R0[125/1500]  | LR: 0.049148 | E:  -59.559479 | E_var:     7.8000 | E_err:   0.030857 | Acc: 0.2986
[2025-11-11 17:21:02] [Iter  202/1575] R0[126/1500]  | LR: 0.049135 | E:  -59.455890 | E_var:     8.0354 | E_err:   0.031319 | Acc: 0.2956
[2025-11-11 17:21:18] [Iter  203/1575] R0[127/1500]  | LR: 0.049121 | E:  -59.693367 | E_var:     7.6898 | E_err:   0.030638 | Acc: 0.2925
[2025-11-11 17:21:34] [Iter  204/1575] R0[128/1500]  | LR: 0.049107 | E:  -60.310079 | E_var:     7.8480 | E_err:   0.030952 | Acc: 0.2698
[2025-11-11 17:21:50] [Iter  205/1575] R0[129/1500]  | LR: 0.049093 | E:  -59.839041 | E_var:     7.7135 | E_err:   0.030685 | Acc: 0.2819
[2025-11-11 17:22:06] [Iter  206/1575] R0[130/1500]  | LR: 0.049079 | E:  -59.646005 | E_var:     7.9361 | E_err:   0.031125 | Acc: 0.2813
[2025-11-11 17:22:22] [Iter  207/1575] R0[131/1500]  | LR: 0.049065 | E:  -59.590390 | E_var:     7.9526 | E_err:   0.031157 | Acc: 0.2864
[2025-11-11 17:22:38] [Iter  208/1575] R0[132/1500]  | LR: 0.049051 | E:  -59.921067 | E_var:     8.4418 | E_err:   0.032101 | Acc: 0.2645
[2025-11-11 17:22:54] [Iter  209/1575] R0[133/1500]  | LR: 0.049036 | E:  -59.779924 | E_var:     7.3910 | E_err:   0.030037 | Acc: 0.2845
[2025-11-11 17:23:10] [Iter  210/1575] R0[134/1500]  | LR: 0.049022 | E:  -59.372162 | E_var:     8.3809 | E_err:   0.031985 | Acc: 0.2883
[2025-11-11 17:23:26] [Iter  211/1575] R0[135/1500]  | LR: 0.049007 | E:  -59.732351 | E_var:     7.9052 | E_err:   0.031064 | Acc: 0.2828
[2025-11-11 17:23:42] [Iter  212/1575] R0[136/1500]  | LR: 0.048993 | E:  -59.932624 | E_var:     8.2662 | E_err:   0.031766 | Acc: 0.2684
[2025-11-11 17:23:58] [Iter  213/1575] R0[137/1500]  | LR: 0.048978 | E:  -59.858619 | E_var:     7.7280 | E_err:   0.030714 | Acc: 0.2640
[2025-11-11 17:24:14] [Iter  214/1575] R0[138/1500]  | LR: 0.048963 | E:  -60.469802 | E_var:     6.9657 | E_err:   0.029160 | Acc: 0.2653
[2025-11-11 17:24:30] [Iter  215/1575] R0[139/1500]  | LR: 0.048948 | E:  -60.514476 | E_var:     6.9291 | E_err:   0.029083 | Acc: 0.2807
[2025-11-11 17:24:45] [Iter  216/1575] R0[140/1500]  | LR: 0.048933 | E:  -60.329042 | E_var:     6.8468 | E_err:   0.028910 | Acc: 0.3019
[2025-11-11 17:25:01] [Iter  217/1575] R0[141/1500]  | LR: 0.048918 | E:  -60.551314 | E_var:     6.7226 | E_err:   0.028647 | Acc: 0.3060
[2025-11-11 17:25:17] [Iter  218/1575] R0[142/1500]  | LR: 0.048903 | E:  -60.678817 | E_var:     6.9124 | E_err:   0.029048 | Acc: 0.3021
[2025-11-11 17:25:33] [Iter  219/1575] R0[143/1500]  | LR: 0.048887 | E:  -60.728244 | E_var:     6.6789 | E_err:   0.028553 | Acc: 0.2992
[2025-11-11 17:25:49] [Iter  220/1575] R0[144/1500]  | LR: 0.048872 | E:  -60.502264 | E_var:     6.6459 | E_err:   0.028483 | Acc: 0.3056
[2025-11-11 17:26:05] [Iter  221/1575] R0[145/1500]  | LR: 0.048856 | E:  -60.235819 | E_var:     7.0825 | E_err:   0.029404 | Acc: 0.3092
[2025-11-11 17:26:21] [Iter  222/1575] R0[146/1500]  | LR: 0.048840 | E:  -60.348697 | E_var:     7.0678 | E_err:   0.029373 | Acc: 0.3000
[2025-11-11 17:26:37] [Iter  223/1575] R0[147/1500]  | LR: 0.048824 | E:  -60.342386 | E_var:     7.1654 | E_err:   0.029575 | Acc: 0.2869
[2025-11-11 17:26:53] [Iter  224/1575] R0[148/1500]  | LR: 0.048809 | E:  -60.538103 | E_var:     6.5611 | E_err:   0.028300 | Acc: 0.2987
[2025-11-11 17:27:09] [Iter  225/1575] R0[149/1500]  | LR: 0.048793 | E:  -60.787715 | E_var:     6.6362 | E_err:   0.028462 | Acc: 0.2854
[2025-11-11 17:27:09] ✓ Checkpoint saved: checkpoint_iter_000150.pkl
[2025-11-11 17:27:25] [Iter  226/1575] R0[150/1500]  | LR: 0.048776 | E:  -60.981033 | E_var:     6.6529 | E_err:   0.028498 | Acc: 0.2770
[2025-11-11 17:27:41] [Iter  227/1575] R0[151/1500]  | LR: 0.048760 | E:  -60.921023 | E_var:     6.4034 | E_err:   0.027958 | Acc: 0.2874
[2025-11-11 17:27:57] [Iter  228/1575] R0[152/1500]  | LR: 0.048744 | E:  -61.071011 | E_var:     7.4812 | E_err:   0.030220 | Acc: 0.2774
[2025-11-11 17:28:13] [Iter  229/1575] R0[153/1500]  | LR: 0.048727 | E:  -61.075761 | E_var:     6.5720 | E_err:   0.028324 | Acc: 0.2744
[2025-11-11 17:28:29] [Iter  230/1575] R0[154/1500]  | LR: 0.048711 | E:  -60.877223 | E_var:     6.9577 | E_err:   0.029143 | Acc: 0.2857
[2025-11-11 17:28:45] [Iter  231/1575] R0[155/1500]  | LR: 0.048694 | E:  -60.759273 | E_var:     7.2030 | E_err:   0.029653 | Acc: 0.2811
[2025-11-11 17:29:01] [Iter  232/1575] R0[156/1500]  | LR: 0.048677 | E:  -60.894264 | E_var:     6.5463 | E_err:   0.028269 | Acc: 0.2924
[2025-11-11 17:29:16] [Iter  233/1575] R0[157/1500]  | LR: 0.048661 | E:  -61.017783 | E_var:     7.6240 | E_err:   0.030507 | Acc: 0.2746
[2025-11-11 17:29:32] [Iter  234/1575] R0[158/1500]  | LR: 0.048644 | E:  -60.930081 | E_var:     6.4263 | E_err:   0.028008 | Acc: 0.2786
[2025-11-11 17:29:48] [Iter  235/1575] R0[159/1500]  | LR: 0.048627 | E:  -61.017504 | E_var:     6.9002 | E_err:   0.029023 | Acc: 0.2773
[2025-11-11 17:30:04] [Iter  236/1575] R0[160/1500]  | LR: 0.048609 | E:  -60.830654 | E_var:     6.5460 | E_err:   0.028268 | Acc: 0.2947
[2025-11-11 17:30:20] [Iter  237/1575] R0[161/1500]  | LR: 0.048592 | E:  -60.776042 | E_var:     6.6836 | E_err:   0.028563 | Acc: 0.2984
[2025-11-11 17:30:36] [Iter  238/1575] R0[162/1500]  | LR: 0.048575 | E:  -61.069642 | E_var:     7.2112 | E_err:   0.029669 | Acc: 0.2755
[2025-11-11 17:30:52] [Iter  239/1575] R0[163/1500]  | LR: 0.048557 | E:  -61.308963 | E_var:     6.5749 | E_err:   0.028330 | Acc: 0.2670
[2025-11-11 17:31:08] [Iter  240/1575] R0[164/1500]  | LR: 0.048540 | E:  -61.260379 | E_var:     6.3549 | E_err:   0.027852 | Acc: 0.2644
[2025-11-11 17:31:24] [Iter  241/1575] R0[165/1500]  | LR: 0.048522 | E:  -61.169168 | E_var:     7.1470 | E_err:   0.029537 | Acc: 0.2502
[2025-11-11 17:31:40] [Iter  242/1575] R0[166/1500]  | LR: 0.048504 | E:  -61.262162 | E_var:     6.1920 | E_err:   0.027493 | Acc: 0.2586
[2025-11-11 17:31:56] [Iter  243/1575] R0[167/1500]  | LR: 0.048486 | E:  -61.228269 | E_var:     6.9265 | E_err:   0.029078 | Acc: 0.2493
[2025-11-11 17:32:12] [Iter  244/1575] R0[168/1500]  | LR: 0.048468 | E:  -61.245993 | E_var:     6.1383 | E_err:   0.027373 | Acc: 0.2603
[2025-11-11 17:32:28] [Iter  245/1575] R0[169/1500]  | LR: 0.048450 | E:  -61.155775 | E_var:     6.6661 | E_err:   0.028526 | Acc: 0.2610
[2025-11-11 17:32:43] [Iter  246/1575] R0[170/1500]  | LR: 0.048432 | E:  -61.173652 | E_var:     6.4829 | E_err:   0.028131 | Acc: 0.2520
[2025-11-11 17:32:59] [Iter  247/1575] R0[171/1500]  | LR: 0.048414 | E:  -61.244190 | E_var:     6.5888 | E_err:   0.028360 | Acc: 0.2515
[2025-11-11 17:33:15] [Iter  248/1575] R0[172/1500]  | LR: 0.048395 | E:  -61.280404 | E_var:     6.4233 | E_err:   0.028002 | Acc: 0.2570
[2025-11-11 17:33:31] [Iter  249/1575] R0[173/1500]  | LR: 0.048377 | E:  -61.204003 | E_var:     6.4599 | E_err:   0.028081 | Acc: 0.2669
[2025-11-11 17:33:47] [Iter  250/1575] R0[174/1500]  | LR: 0.048358 | E:  -61.316236 | E_var:     6.6201 | E_err:   0.028427 | Acc: 0.2607
[2025-11-11 17:34:03] [Iter  251/1575] R0[175/1500]  | LR: 0.048340 | E:  -61.358942 | E_var:     6.2988 | E_err:   0.027729 | Acc: 0.2617
[2025-11-11 17:34:19] [Iter  252/1575] R0[176/1500]  | LR: 0.048321 | E:  -60.725202 | E_var:     7.8051 | E_err:   0.030867 | Acc: 0.2554
[2025-11-11 17:34:35] [Iter  253/1575] R0[177/1500]  | LR: 0.048302 | E:  -61.056477 | E_var:     6.9974 | E_err:   0.029226 | Acc: 0.2558
[2025-11-11 17:34:51] [Iter  254/1575] R0[178/1500]  | LR: 0.048283 | E:  -61.101792 | E_var:     6.7105 | E_err:   0.028621 | Acc: 0.2578
[2025-11-11 17:35:07] [Iter  255/1575] R0[179/1500]  | LR: 0.048264 | E:  -61.396098 | E_var:     6.1904 | E_err:   0.027489 | Acc: 0.2584
[2025-11-11 17:35:23] [Iter  256/1575] R0[180/1500]  | LR: 0.048244 | E:  -61.640156 | E_var:     6.3573 | E_err:   0.027857 | Acc: 0.2550
[2025-11-11 17:35:39] [Iter  257/1575] R0[181/1500]  | LR: 0.048225 | E:  -61.384976 | E_var:     6.4717 | E_err:   0.028107 | Acc: 0.2857
[2025-11-11 17:35:55] [Iter  258/1575] R0[182/1500]  | LR: 0.048206 | E:  -60.966162 | E_var:     6.6515 | E_err:   0.028495 | Acc: 0.3179
[2025-11-11 17:36:10] [Iter  259/1575] R0[183/1500]  | LR: 0.048186 | E:  -61.136927 | E_var:     6.5335 | E_err:   0.028241 | Acc: 0.3210
[2025-11-11 17:36:26] [Iter  260/1575] R0[184/1500]  | LR: 0.048167 | E:  -61.156407 | E_var:     6.2428 | E_err:   0.027606 | Acc: 0.3123
[2025-11-11 17:36:42] [Iter  261/1575] R0[185/1500]  | LR: 0.048147 | E:  -61.582241 | E_var:     6.5428 | E_err:   0.028261 | Acc: 0.2844
[2025-11-11 17:36:58] [Iter  262/1575] R0[186/1500]  | LR: 0.048127 | E:  -61.255410 | E_var:     6.0224 | E_err:   0.027114 | Acc: 0.2964
[2025-11-11 17:37:14] [Iter  263/1575] R0[187/1500]  | LR: 0.048107 | E:  -60.828123 | E_var:     6.6157 | E_err:   0.028418 | Acc: 0.3241
[2025-11-11 17:37:30] [Iter  264/1575] R0[188/1500]  | LR: 0.048087 | E:  -60.361757 | E_var:     7.1325 | E_err:   0.029507 | Acc: 0.3421
[2025-11-11 17:37:46] [Iter  265/1575] R0[189/1500]  | LR: 0.048067 | E:  -61.069725 | E_var:     6.5088 | E_err:   0.028187 | Acc: 0.3204
[2025-11-11 17:38:02] [Iter  266/1575] R0[190/1500]  | LR: 0.048047 | E:  -61.061904 | E_var:     6.5748 | E_err:   0.028330 | Acc: 0.2949
[2025-11-11 17:38:18] [Iter  267/1575] R0[191/1500]  | LR: 0.048026 | E:  -61.096148 | E_var:     6.2411 | E_err:   0.027602 | Acc: 0.2923
[2025-11-11 17:38:34] [Iter  268/1575] R0[192/1500]  | LR: 0.048006 | E:  -60.842949 | E_var:     6.3138 | E_err:   0.027762 | Acc: 0.3145
[2025-11-11 17:38:50] [Iter  269/1575] R0[193/1500]  | LR: 0.047985 | E:  -60.729515 | E_var:     6.5731 | E_err:   0.028326 | Acc: 0.3052
[2025-11-11 17:39:06] [Iter  270/1575] R0[194/1500]  | LR: 0.047965 | E:  -60.784093 | E_var:     7.8977 | E_err:   0.031050 | Acc: 0.2778
[2025-11-11 17:39:22] [Iter  271/1575] R0[195/1500]  | LR: 0.047944 | E:  -61.178609 | E_var:     6.0241 | E_err:   0.027118 | Acc: 0.2738
[2025-11-11 17:39:38] [Iter  272/1575] R0[196/1500]  | LR: 0.047923 | E:  -61.627682 | E_var:     6.7084 | E_err:   0.028616 | Acc: 0.2599
[2025-11-11 17:39:53] [Iter  273/1575] R0[197/1500]  | LR: 0.047902 | E:  -61.537158 | E_var:     5.8871 | E_err:   0.026808 | Acc: 0.2704
[2025-11-11 17:40:09] [Iter  274/1575] R0[198/1500]  | LR: 0.047881 | E:  -61.434278 | E_var:     6.8412 | E_err:   0.028898 | Acc: 0.2588
[2025-11-11 17:40:25] [Iter  275/1575] R0[199/1500]  | LR: 0.047860 | E:  -61.494075 | E_var:     5.9970 | E_err:   0.027056 | Acc: 0.2573
[2025-11-11 17:40:41] [Iter  276/1575] R0[200/1500]  | LR: 0.047839 | E:  -61.705437 | E_var:     6.2098 | E_err:   0.027532 | Acc: 0.2534
[2025-11-11 17:40:57] [Iter  277/1575] R0[201/1500]  | LR: 0.047817 | E:  -61.869929 | E_var:     5.9773 | E_err:   0.027012 | Acc: 0.2468
[2025-11-11 17:41:13] [Iter  278/1575] R0[202/1500]  | LR: 0.047796 | E:  -61.826668 | E_var:     6.8091 | E_err:   0.028830 | Acc: 0.2339
[2025-11-11 17:41:29] [Iter  279/1575] R0[203/1500]  | LR: 0.047774 | E:  -61.834399 | E_var:     6.2232 | E_err:   0.027562 | Acc: 0.2265
[2025-11-11 17:41:45] [Iter  280/1575] R0[204/1500]  | LR: 0.047753 | E:  -61.877956 | E_var:     6.5403 | E_err:   0.028256 | Acc: 0.2249
[2025-11-11 17:42:01] [Iter  281/1575] R0[205/1500]  | LR: 0.047731 | E:  -61.779546 | E_var:     7.4486 | E_err:   0.030154 | Acc: 0.2133
[2025-11-11 17:42:17] [Iter  282/1575] R0[206/1500]  | LR: 0.047709 | E:  -61.721867 | E_var:     5.8569 | E_err:   0.026739 | Acc: 0.2422
[2025-11-11 17:42:33] [Iter  283/1575] R0[207/1500]  | LR: 0.047687 | E:  -61.726675 | E_var:     6.2765 | E_err:   0.027680 | Acc: 0.2476
[2025-11-11 17:42:49] [Iter  284/1575] R0[208/1500]  | LR: 0.047665 | E:  -61.917899 | E_var:     5.8558 | E_err:   0.026736 | Acc: 0.2464
[2025-11-11 17:43:05] [Iter  285/1575] R0[209/1500]  | LR: 0.047643 | E:  -61.754352 | E_var:     6.0748 | E_err:   0.027232 | Acc: 0.2532
[2025-11-11 17:43:21] [Iter  286/1575] R0[210/1500]  | LR: 0.047621 | E:  -61.747935 | E_var:     5.8506 | E_err:   0.026724 | Acc: 0.2625
[2025-11-11 17:43:36] [Iter  287/1575] R0[211/1500]  | LR: 0.047598 | E:  -61.712929 | E_var:     6.0812 | E_err:   0.027246 | Acc: 0.2584
[2025-11-11 17:43:52] [Iter  288/1575] R0[212/1500]  | LR: 0.047576 | E:  -61.430168 | E_var:     6.2327 | E_err:   0.027583 | Acc: 0.2635
[2025-11-11 17:44:08] [Iter  289/1575] R0[213/1500]  | LR: 0.047553 | E:  -61.344198 | E_var:     6.3957 | E_err:   0.027942 | Acc: 0.2687
[2025-11-11 17:44:24] [Iter  290/1575] R0[214/1500]  | LR: 0.047531 | E:  -61.287568 | E_var:     6.2272 | E_err:   0.027571 | Acc: 0.2872
[2025-11-11 17:44:40] [Iter  291/1575] R0[215/1500]  | LR: 0.047508 | E:  -61.763841 | E_var:     5.8540 | E_err:   0.026732 | Acc: 0.2814
[2025-11-11 17:44:56] [Iter  292/1575] R0[216/1500]  | LR: 0.047485 | E:  -61.607046 | E_var:     5.7864 | E_err:   0.026577 | Acc: 0.2829
[2025-11-11 17:45:12] [Iter  293/1575] R0[217/1500]  | LR: 0.047462 | E:  -62.041081 | E_var:     5.5357 | E_err:   0.025995 | Acc: 0.2783
[2025-11-11 17:45:28] [Iter  294/1575] R0[218/1500]  | LR: 0.047439 | E:  -61.865618 | E_var:     5.5320 | E_err:   0.025986 | Acc: 0.2858
[2025-11-11 17:45:44] [Iter  295/1575] R0[219/1500]  | LR: 0.047416 | E:  -61.898227 | E_var:     5.4313 | E_err:   0.025749 | Acc: 0.2911
[2025-11-11 17:46:00] [Iter  296/1575] R0[220/1500]  | LR: 0.047393 | E:  -62.065326 | E_var:     6.1119 | E_err:   0.027315 | Acc: 0.2737
[2025-11-11 17:46:16] [Iter  297/1575] R0[221/1500]  | LR: 0.047369 | E:  -61.964180 | E_var:     5.5721 | E_err:   0.026080 | Acc: 0.2838
[2025-11-11 17:46:32] [Iter  298/1575] R0[222/1500]  | LR: 0.047346 | E:  -61.852358 | E_var:     5.5269 | E_err:   0.025974 | Acc: 0.2848
[2025-11-11 17:46:48] [Iter  299/1575] R0[223/1500]  | LR: 0.047323 | E:  -61.898762 | E_var:     5.4661 | E_err:   0.025831 | Acc: 0.2782
[2025-11-11 17:47:04] [Iter  300/1575] R0[224/1500]  | LR: 0.047299 | E:  -62.099555 | E_var:     5.7220 | E_err:   0.026429 | Acc: 0.2607
[2025-11-11 17:47:19] [Iter  301/1575] R0[225/1500]  | LR: 0.047275 | E:  -62.068043 | E_var:     5.5861 | E_err:   0.026113 | Acc: 0.2634
[2025-11-11 17:47:35] [Iter  302/1575] R0[226/1500]  | LR: 0.047251 | E:  -61.931430 | E_var:     5.7617 | E_err:   0.026521 | Acc: 0.2721
[2025-11-11 17:47:51] [Iter  303/1575] R0[227/1500]  | LR: 0.047227 | E:  -62.088783 | E_var:     5.4769 | E_err:   0.025857 | Acc: 0.2718
[2025-11-11 17:48:07] [Iter  304/1575] R0[228/1500]  | LR: 0.047203 | E:  -62.004991 | E_var:     5.4245 | E_err:   0.025733 | Acc: 0.2825
[2025-11-11 17:48:23] [Iter  305/1575] R0[229/1500]  | LR: 0.047179 | E:  -61.831781 | E_var:     5.6473 | E_err:   0.026256 | Acc: 0.2855
[2025-11-11 17:48:39] [Iter  306/1575] R0[230/1500]  | LR: 0.047155 | E:  -62.096841 | E_var:     6.4672 | E_err:   0.028097 | Acc: 0.2581
[2025-11-11 17:48:55] [Iter  307/1575] R0[231/1500]  | LR: 0.047131 | E:  -62.120870 | E_var:     5.3807 | E_err:   0.025629 | Acc: 0.2630
[2025-11-11 17:49:11] [Iter  308/1575] R0[232/1500]  | LR: 0.047106 | E:  -62.083902 | E_var:     6.0547 | E_err:   0.027186 | Acc: 0.2502
[2025-11-11 17:49:27] [Iter  309/1575] R0[233/1500]  | LR: 0.047082 | E:  -61.991410 | E_var:     5.2181 | E_err:   0.025238 | Acc: 0.2649
[2025-11-11 17:49:43] [Iter  310/1575] R0[234/1500]  | LR: 0.047057 | E:  -61.936293 | E_var:     5.4324 | E_err:   0.025751 | Acc: 0.2756
[2025-11-11 17:49:59] [Iter  311/1575] R0[235/1500]  | LR: 0.047033 | E:  -61.919562 | E_var:     5.6751 | E_err:   0.026320 | Acc: 0.2804
[2025-11-11 17:50:15] [Iter  312/1575] R0[236/1500]  | LR: 0.047008 | E:  -61.999380 | E_var:     5.2553 | E_err:   0.025328 | Acc: 0.2857
[2025-11-11 17:50:31] [Iter  313/1575] R0[237/1500]  | LR: 0.046983 | E:  -61.811266 | E_var:     5.7220 | E_err:   0.026429 | Acc: 0.2848
[2025-11-11 17:50:47] [Iter  314/1575] R0[238/1500]  | LR: 0.046958 | E:  -62.075234 | E_var:     5.2375 | E_err:   0.025285 | Acc: 0.2771
[2025-11-11 17:51:02] [Iter  315/1575] R0[239/1500]  | LR: 0.046933 | E:  -62.070205 | E_var:     5.3449 | E_err:   0.025543 | Acc: 0.2715
[2025-11-11 17:51:18] [Iter  316/1575] R0[240/1500]  | LR: 0.046908 | E:  -62.230251 | E_var:     5.2632 | E_err:   0.025347 | Acc: 0.2675
[2025-11-11 17:51:34] [Iter  317/1575] R0[241/1500]  | LR: 0.046882 | E:  -62.082556 | E_var:     5.3527 | E_err:   0.025562 | Acc: 0.2847
[2025-11-11 17:51:50] [Iter  318/1575] R0[242/1500]  | LR: 0.046857 | E:  -61.879305 | E_var:     5.6166 | E_err:   0.026184 | Acc: 0.3046
[2025-11-11 17:52:06] [Iter  319/1575] R0[243/1500]  | LR: 0.046832 | E:  -61.875166 | E_var:     5.6199 | E_err:   0.026192 | Acc: 0.2977
[2025-11-11 17:52:22] [Iter  320/1575] R0[244/1500]  | LR: 0.046806 | E:  -61.910028 | E_var:     5.2564 | E_err:   0.025331 | Acc: 0.3023
[2025-11-11 17:52:38] [Iter  321/1575] R0[245/1500]  | LR: 0.046780 | E:  -62.099864 | E_var:     5.3007 | E_err:   0.025437 | Acc: 0.2929
[2025-11-11 17:52:54] [Iter  322/1575] R0[246/1500]  | LR: 0.046755 | E:  -62.270415 | E_var:     5.4343 | E_err:   0.025756 | Acc: 0.2725
[2025-11-11 17:53:10] [Iter  323/1575] R0[247/1500]  | LR: 0.046729 | E:  -61.820228 | E_var:     6.1393 | E_err:   0.027376 | Acc: 0.2550
[2025-11-11 17:53:26] [Iter  324/1575] R0[248/1500]  | LR: 0.046703 | E:  -61.973984 | E_var:     5.5487 | E_err:   0.026026 | Acc: 0.2515
[2025-11-11 17:53:42] [Iter  325/1575] R0[249/1500]  | LR: 0.046677 | E:  -61.699285 | E_var:     5.7290 | E_err:   0.026445 | Acc: 0.2687
[2025-11-11 17:53:58] [Iter  326/1575] R0[250/1500]  | LR: 0.046651 | E:  -62.075594 | E_var:     5.4723 | E_err:   0.025846 | Acc: 0.2667
[2025-11-11 17:54:14] [Iter  327/1575] R0[251/1500]  | LR: 0.046624 | E:  -61.959245 | E_var:     5.4120 | E_err:   0.025703 | Acc: 0.2860
[2025-11-11 17:54:30] [Iter  328/1575] R0[252/1500]  | LR: 0.046598 | E:  -61.766025 | E_var:     5.6934 | E_err:   0.026363 | Acc: 0.3006
[2025-11-11 17:54:45] [Iter  329/1575] R0[253/1500]  | LR: 0.046572 | E:  -61.951913 | E_var:     5.8082 | E_err:   0.026627 | Acc: 0.2788
[2025-11-11 17:55:01] [Iter  330/1575] R0[254/1500]  | LR: 0.046545 | E:  -61.892508 | E_var:     5.4479 | E_err:   0.025788 | Acc: 0.2759
[2025-11-11 17:55:17] [Iter  331/1575] R0[255/1500]  | LR: 0.046519 | E:  -62.114496 | E_var:     5.7355 | E_err:   0.026460 | Acc: 0.2774
[2025-11-11 17:55:33] [Iter  332/1575] R0[256/1500]  | LR: 0.046492 | E:  -61.960550 | E_var:     6.3180 | E_err:   0.027771 | Acc: 0.2721
[2025-11-11 17:55:49] [Iter  333/1575] R0[257/1500]  | LR: 0.046465 | E:  -62.207958 | E_var:     5.4079 | E_err:   0.025693 | Acc: 0.2679
[2025-11-11 17:56:05] [Iter  334/1575] R0[258/1500]  | LR: 0.046438 | E:  -62.218375 | E_var:     5.1688 | E_err:   0.025119 | Acc: 0.2647
[2025-11-11 17:56:21] [Iter  335/1575] R0[259/1500]  | LR: 0.046411 | E:  -62.246031 | E_var:     5.0367 | E_err:   0.024796 | Acc: 0.2792
[2025-11-11 17:56:37] [Iter  336/1575] R0[260/1500]  | LR: 0.046384 | E:  -61.976830 | E_var:     5.5610 | E_err:   0.026054 | Acc: 0.2925
[2025-11-11 17:56:53] [Iter  337/1575] R0[261/1500]  | LR: 0.046357 | E:  -62.140630 | E_var:     5.6051 | E_err:   0.026158 | Acc: 0.2795
[2025-11-11 17:57:09] [Iter  338/1575] R0[262/1500]  | LR: 0.046330 | E:  -62.107682 | E_var:     5.0984 | E_err:   0.024947 | Acc: 0.2789
[2025-11-11 17:57:25] [Iter  339/1575] R0[263/1500]  | LR: 0.046302 | E:  -62.226923 | E_var:     5.3736 | E_err:   0.025612 | Acc: 0.2735
[2025-11-11 17:57:41] [Iter  340/1575] R0[264/1500]  | LR: 0.046275 | E:  -62.130826 | E_var:     5.0953 | E_err:   0.024940 | Acc: 0.2792
[2025-11-11 17:57:57] [Iter  341/1575] R0[265/1500]  | LR: 0.046247 | E:  -62.145802 | E_var:     5.0164 | E_err:   0.024746 | Acc: 0.2810
[2025-11-11 17:58:12] [Iter  342/1575] R0[266/1500]  | LR: 0.046220 | E:  -61.751525 | E_var:     5.7373 | E_err:   0.026464 | Acc: 0.2907
[2025-11-11 17:58:28] [Iter  343/1575] R0[267/1500]  | LR: 0.046192 | E:  -61.635063 | E_var:     5.8996 | E_err:   0.026836 | Acc: 0.3058
[2025-11-11 17:58:44] [Iter  344/1575] R0[268/1500]  | LR: 0.046164 | E:  -61.788802 | E_var:     5.4952 | E_err:   0.025900 | Acc: 0.3147
[2025-11-11 17:59:00] [Iter  345/1575] R0[269/1500]  | LR: 0.046136 | E:  -62.033259 | E_var:     5.1405 | E_err:   0.025050 | Acc: 0.3040
[2025-11-11 17:59:16] [Iter  346/1575] R0[270/1500]  | LR: 0.046108 | E:  -62.245412 | E_var:     4.9332 | E_err:   0.024540 | Acc: 0.2953
[2025-11-11 17:59:32] [Iter  347/1575] R0[271/1500]  | LR: 0.046080 | E:  -62.186963 | E_var:     5.1332 | E_err:   0.025032 | Acc: 0.3021
[2025-11-11 17:59:48] [Iter  348/1575] R0[272/1500]  | LR: 0.046052 | E:  -62.335710 | E_var:     4.9394 | E_err:   0.024555 | Acc: 0.2978
[2025-11-11 18:00:04] [Iter  349/1575] R0[273/1500]  | LR: 0.046024 | E:  -62.318037 | E_var:     5.0713 | E_err:   0.024881 | Acc: 0.2818
[2025-11-11 18:00:20] [Iter  350/1575] R0[274/1500]  | LR: 0.045995 | E:  -62.192835 | E_var:     5.2752 | E_err:   0.025376 | Acc: 0.2823
[2025-11-11 18:00:36] [Iter  351/1575] R0[275/1500]  | LR: 0.045967 | E:  -62.318026 | E_var:     5.1423 | E_err:   0.025054 | Acc: 0.2852
[2025-11-11 18:00:52] [Iter  352/1575] R0[276/1500]  | LR: 0.045938 | E:  -62.138820 | E_var:     6.2037 | E_err:   0.027519 | Acc: 0.2625
[2025-11-11 18:01:08] [Iter  353/1575] R0[277/1500]  | LR: 0.045910 | E:  -62.207709 | E_var:     4.9583 | E_err:   0.024602 | Acc: 0.2811
[2025-11-11 18:01:24] [Iter  354/1575] R0[278/1500]  | LR: 0.045881 | E:  -62.068783 | E_var:     5.3321 | E_err:   0.025512 | Acc: 0.3081
[2025-11-11 18:01:40] [Iter  355/1575] R0[279/1500]  | LR: 0.045852 | E:  -62.504166 | E_var:     6.0752 | E_err:   0.027232 | Acc: 0.2831
[2025-11-11 18:01:56] [Iter  356/1575] R0[280/1500]  | LR: 0.045823 | E:  -62.457149 | E_var:     7.3848 | E_err:   0.030024 | Acc: 0.2736
[2025-11-11 18:02:11] [Iter  357/1575] R0[281/1500]  | LR: 0.045794 | E:  -62.540227 | E_var:     5.4447 | E_err:   0.025781 | Acc: 0.2571
[2025-11-11 18:02:27] [Iter  358/1575] R0[282/1500]  | LR: 0.045765 | E:  -62.556000 | E_var:     4.8991 | E_err:   0.024455 | Acc: 0.2652
[2025-11-11 18:02:43] [Iter  359/1575] R0[283/1500]  | LR: 0.045736 | E:  -62.484724 | E_var:     4.8215 | E_err:   0.024260 | Acc: 0.2798
[2025-11-11 18:02:59] [Iter  360/1575] R0[284/1500]  | LR: 0.045706 | E:  -62.523780 | E_var:     4.9815 | E_err:   0.024659 | Acc: 0.2808
[2025-11-11 18:03:15] [Iter  361/1575] R0[285/1500]  | LR: 0.045677 | E:  -62.448025 | E_var:     5.2304 | E_err:   0.025268 | Acc: 0.2782
[2025-11-11 18:03:31] [Iter  362/1575] R0[286/1500]  | LR: 0.045648 | E:  -62.497539 | E_var:     5.2165 | E_err:   0.025235 | Acc: 0.2788
[2025-11-11 18:03:47] [Iter  363/1575] R0[287/1500]  | LR: 0.045618 | E:  -62.550266 | E_var:     6.2302 | E_err:   0.027578 | Acc: 0.2532
[2025-11-11 18:04:03] [Iter  364/1575] R0[288/1500]  | LR: 0.045588 | E:  -62.576510 | E_var:     4.7013 | E_err:   0.023956 | Acc: 0.2635
[2025-11-11 18:04:19] [Iter  365/1575] R0[289/1500]  | LR: 0.045559 | E:  -62.506656 | E_var:     4.8825 | E_err:   0.024413 | Acc: 0.2701
[2025-11-11 18:04:35] [Iter  366/1575] R0[290/1500]  | LR: 0.045529 | E:  -62.442923 | E_var:     5.1438 | E_err:   0.025058 | Acc: 0.2911
[2025-11-11 18:04:51] [Iter  367/1575] R0[291/1500]  | LR: 0.045499 | E:  -62.545889 | E_var:     5.0403 | E_err:   0.024805 | Acc: 0.2824
[2025-11-11 18:05:07] [Iter  368/1575] R0[292/1500]  | LR: 0.045469 | E:  -62.511459 | E_var:     5.3873 | E_err:   0.025644 | Acc: 0.2696
[2025-11-11 18:05:23] [Iter  369/1575] R0[293/1500]  | LR: 0.045439 | E:  -62.458307 | E_var:     4.8861 | E_err:   0.024422 | Acc: 0.2861
[2025-11-11 18:05:38] [Iter  370/1575] R0[294/1500]  | LR: 0.045408 | E:  -62.478353 | E_var:     5.1729 | E_err:   0.025129 | Acc: 0.2810
[2025-11-11 18:05:54] [Iter  371/1575] R0[295/1500]  | LR: 0.045378 | E:  -62.488796 | E_var:     5.1057 | E_err:   0.024965 | Acc: 0.2777
[2025-11-11 18:06:10] [Iter  372/1575] R0[296/1500]  | LR: 0.045348 | E:  -62.599592 | E_var:     5.5534 | E_err:   0.026037 | Acc: 0.2613
[2025-11-11 18:06:26] [Iter  373/1575] R0[297/1500]  | LR: 0.045317 | E:  -62.588024 | E_var:     4.8048 | E_err:   0.024218 | Acc: 0.2720
[2025-11-11 18:06:42] [Iter  374/1575] R0[298/1500]  | LR: 0.045287 | E:  -62.560810 | E_var:     4.8650 | E_err:   0.024370 | Acc: 0.2870
[2025-11-11 18:06:58] [Iter  375/1575] R0[299/1500]  | LR: 0.045256 | E:  -62.673219 | E_var:     5.0051 | E_err:   0.024718 | Acc: 0.2782
[2025-11-11 18:06:58] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-11-11 18:07:14] [Iter  376/1575] R0[300/1500]  | LR: 0.045225 | E:  -62.449407 | E_var:     5.0634 | E_err:   0.024861 | Acc: 0.3026
[2025-11-11 18:07:30] [Iter  377/1575] R0[301/1500]  | LR: 0.045195 | E:  -62.253988 | E_var:     5.2484 | E_err:   0.025312 | Acc: 0.3249
[2025-11-11 18:07:46] [Iter  378/1575] R0[302/1500]  | LR: 0.045164 | E:  -62.035670 | E_var:     5.2991 | E_err:   0.025433 | Acc: 0.3351
[2025-11-12 00:45:58] ✓ 创建部分等变ViT模型（嵌入层等变）
[2025-11-12 00:45:58]   对称性组大小: 8 (C4v点群)
[2025-11-12 00:45:58]   嵌入层使用等变权重共享，后续Transformer层使用普通结构
[2025-11-12 00:46:18] 🔥 预热编译: 执行模型前向传播以触发JIT编译...
[2025-11-12 00:46:26] ✓ 预热编译完成 | 耗时: 7.31s
[2025-11-12 00:46:26] ======================================================================================================
[2025-11-12 00:46:26] ViT for Shastry-Sutherland Model
[2025-11-12 00:46:26] ======================================================================================================
[2025-11-12 00:46:26] System Parameters:
[2025-11-12 00:46:26]   - Lattice size: L = 6
[2025-11-12 00:46:26]   - Total sites: N = 144
[2025-11-12 00:46:26]   - J1 coupling: 0.8
[2025-11-12 00:46:26]   - J2 coupling: 1.0
[2025-11-12 00:46:26]   - Q (4-spin): 0.0
[2025-11-12 00:46:26] ------------------------------------------------------------------------------------------------------
[2025-11-12 00:46:26] Model Parameters:
[2025-11-12 00:46:26]   ViT Architecture:
[2025-11-12 00:46:26]     • Layers: 4
[2025-11-12 00:46:26]     • Embedding dimension (d_model): 96
[2025-11-12 00:46:26]     • Attention heads: 4
[2025-11-12 00:46:26]     • Patch size: 2x2
[2025-11-12 00:46:26]     • Number of patches: 36
[2025-11-12 00:46:26]     • Config: [4, 96, 4, 2]
[2025-11-12 00:46:26]     • Total parameters: 482,320
[2025-11-12 00:46:26]   Regularization:
[2025-11-12 00:46:26]     • Relative Position Encoding (RPE): True
[2025-11-12 00:46:26]     • Dropout rate: 0.0
[2025-11-12 00:46:26]   Optimizer:
[2025-11-12 00:46:26]     • Diagonal shift (SR): 0.01
[2025-11-12 00:46:26]   Mixed Precision Training: True
[2025-11-12 00:46:26]     • Parameters: float64 (high precision)
[2025-11-12 00:46:26]     • Computation: bfloat16 (accelerated)
[2025-11-12 00:46:26]     • Critical ops (LayerNorm/Softmax/Output): float64
[2025-11-12 00:46:26]     • Expected speedup: 1.5-2x, Memory reduction: ~50%
[2025-11-12 00:46:26] ------------------------------------------------------------------------------------------------------
[2025-11-12 00:46:26] Training Hyperparameters:
[2025-11-12 00:46:26]   Learning Rate Schedule:
[2025-11-12 00:46:26]     • Max LR: 0.03
[2025-11-12 00:46:26]     • Min LR: 1e-07
[2025-11-12 00:46:26]     • Annealing cycles: 1
[2025-11-12 00:46:26]     • Initial period: 3000
[2025-11-12 00:46:26]     • Period multiplier: 1.0
[2025-11-12 00:46:26]     • Warm-up: 90 iterations (3.0%)
[2025-11-12 00:46:26]     • Total iterations: 3000 + 90 (warm-up) = 3090
[2025-11-12 00:46:26]   Sampling Parameters:
[2025-11-12 00:46:26]     • Samples (n_samples): 8192
[2025-11-12 00:46:26]     • Parallel chains (n_chains): 8192
[2025-11-12 00:46:26]     • Max exchange distance (d_max): 2
[2025-11-12 00:46:26]     • Chunk size: 8192
[2025-11-12 00:46:26]     • Discarded samples per chain: 0
[2025-11-12 00:46:26]     • Parameter-to-sample ratio: 58.88
[2025-11-12 00:46:26]       ⚠ High ratio (> 50), high overfitting risk!
[2025-11-12 00:46:26] ------------------------------------------------------------------------------------------------------
[2025-11-12 00:46:26] Checkpoint Configuration:
[2025-11-12 00:46:26]   • Enabled: Yes
[2025-11-12 00:46:26]   • Save interval: 300 iterations
[2025-11-12 00:46:26]   • Keep history: True
[2025-11-12 00:46:26]   • Directory: saved_models/L=6/J2=1.00/J1=0.80/checkpoints
[2025-11-12 00:46:26] ------------------------------------------------------------------------------------------------------
[2025-11-12 00:46:26] Device Status:
[2025-11-12 00:46:26]   • Device type: H200
[2025-11-12 00:46:26]   • Number of devices: 1
[2025-11-12 00:46:26]   • Sharding enabled: True
[2025-11-12 00:46:27] ======================================================================================================
[2025-11-12 00:46:27] 🔥 Linear Warm-up: 90 iterations (3.0% of 3000) | LR: 0 -> 0.030000
[2025-11-12 00:46:27]    Total iterations: 90 (warm-up) + 3000 (training) = 3090
[2025-11-12 00:46:27] 🚀 Training iterations started at 2025-11-12 00:46:27
[2025-11-12 00:46:57] 00:00:30<25:38:36, 29.89s/it | [Iter    1/3090] WARMUP[1/90]  | LR: 0.000333 | E:   4.328587 | E_img: -0.1295j E_var:    89.6814 E_err:   0.104630 | Acc: 0.2655
[2025-11-12 00:47:13] 00:00:46<19:39:47, 22.92s/it | [Iter    2/3090] WARMUP[2/90]  | LR: 0.000667 | E:   2.712610 | E_img: +0.0844j E_var:    71.8784 E_err:   0.093671 | Acc: 0.2392
[2025-11-12 00:47:29] 00:01:02<17:37:44, 20.56s/it | [Iter    3/3090] WARMUP[3/90]  | LR: 0.001000 | E:   1.454916 | E_img: -0.0607j E_var:    60.9357 E_err:   0.086246 | Acc: 0.2242
[2025-11-12 00:47:44] 00:01:18<16:36:47, 19.38s/it | [Iter    4/3090] WARMUP[4/90]  | LR: 0.001333 | E:   0.452699 | E_img: +0.0413j E_var:    65.6256 E_err:   0.089504 | Acc: 0.2107
[2025-11-12 00:48:01] 00:01:34<16:02:52, 18.73s/it | [Iter    5/3090] WARMUP[5/90]  | LR: 0.001667 | E:   0.155969 | E_img: -0.0144j E_var:    54.9311 E_err:   0.081887 | Acc: 0.2029
[2025-11-12 00:48:16] 00:01:49<15:37:36, 18.24s/it | [Iter    6/3090] WARMUP[6/90]  | LR: 0.002000 | E:  -0.246236 | E_img: -0.1343j E_var:    56.7375 E_err:   0.083222 | Acc: 0.2005
[2025-11-12 00:48:32] 00:02:05<15:19:37, 17.90s/it | [Iter    7/3090] WARMUP[7/90]  | LR: 0.002333 | E:  -0.702238 | E_img: -0.0055j E_var:    51.3229 E_err:   0.079152 | Acc: 0.1960
[2025-11-12 00:48:48] 00:02:21<15:06:01, 17.64s/it | [Iter    8/3090] WARMUP[8/90]  | LR: 0.002667 | E:  -1.136135 | E_img: -0.0881j E_var:    56.5072 E_err:   0.083053 | Acc: 0.1915
[2025-11-12 00:49:04] 00:02:37<14:55:23, 17.44s/it | [Iter    9/3090] WARMUP[9/90]  | LR: 0.003000 | E:  -1.490770 | E_img: +0.0282j E_var:    50.6315 E_err:   0.078617 | Acc: 0.1918
[2025-11-12 00:49:20] 00:02:53<14:46:49, 17.28s/it | [Iter   10/3090] WARMUP[10/90] | LR: 0.003333 | E:  -1.785952 | E_img: +0.0006j E_var:    49.0011 E_err:   0.077341 | Acc: 0.1907
[2025-11-12 00:49:36] 00:03:09<14:40:12, 17.15s/it | [Iter   11/3090] WARMUP[11/90] | LR: 0.003667 | E:  -2.105287 | E_img: +0.0230j E_var:    52.4586 E_err:   0.080023 | Acc: 0.1878
[2025-11-12 00:49:51] 00:03:25<14:34:19, 17.04s/it | [Iter   12/3090] WARMUP[12/90] | LR: 0.004000 | E:  -2.378601 | E_img: +0.0053j E_var:    53.7035 E_err:   0.080967 | Acc: 0.1881
[2025-11-12 00:50:07] 00:03:40<14:29:17, 16.95s/it | [Iter   13/3090] WARMUP[13/90] | LR: 0.004333 | E:  -2.778123 | E_img: +0.0279j E_var:    54.1848 E_err:   0.081329 | Acc: 0.1877
[2025-11-12 00:50:23] 00:03:56<14:25:09, 16.88s/it | [Iter   14/3090] WARMUP[14/90] | LR: 0.004667 | E:  -3.188348 | E_img: +0.0949j E_var:    50.8173 E_err:   0.078761 | Acc: 0.1897
[2025-11-12 00:50:39] 00:04:12<14:22:19, 16.83s/it | [Iter   15/3090] WARMUP[15/90] | LR: 0.005000 | E:  -3.667042 | E_img: +0.0046j E_var:    52.1564 E_err:   0.079792 | Acc: 0.1868
[2025-11-12 00:50:55] 00:04:28<14:18:53, 16.76s/it | [Iter   16/3090] WARMUP[16/90] | LR: 0.005333 | E:  -4.002528 | E_img: +0.0628j E_var:    50.3277 E_err:   0.078381 | Acc: 0.1856
[2025-11-12 00:51:11] 00:04:44<14:15:49, 16.71s/it | [Iter   17/3090] WARMUP[17/90] | LR: 0.005667 | E:  -4.534509 | E_img: -0.0663j E_var:    47.9989 E_err:   0.076546 | Acc: 0.1862
[2025-11-12 00:51:27] 00:05:00<14:13:00, 16.66s/it | [Iter   18/3090] WARMUP[18/90] | LR: 0.006000 | E:  -5.009372 | E_img: -0.0242j E_var:    49.0263 E_err:   0.077361 | Acc: 0.1869
[2025-11-12 00:51:43] 00:05:16<14:10:31, 16.62s/it | [Iter   19/3090] WARMUP[19/90] | LR: 0.006333 | E:  -5.530291 | E_img: -0.0386j E_var:    54.1451 E_err:   0.081299 | Acc: 0.1852
[2025-11-12 00:51:58] 00:05:32<14:08:13, 16.58s/it | [Iter   20/3090] WARMUP[20/90] | LR: 0.006667 | E:  -6.059385 | E_img: -0.0776j E_var:    49.4776 E_err:   0.077716 | Acc: 0.1886
[2025-11-12 00:52:14] 00:05:47<14:06:06, 16.54s/it | [Iter   21/3090] WARMUP[21/90] | LR: 0.007000 | E:  -6.681630 | E_img: +0.0022j E_var:    51.0162 E_err:   0.078915 | Acc: 0.1885
[2025-11-12 00:52:30] 00:06:03<14:04:10, 16.51s/it | [Iter   22/3090] WARMUP[22/90] | LR: 0.007333 | E:  -7.235138 | E_img: +0.0464j E_var:    47.3931 E_err:   0.076061 | Acc: 0.1881
[2025-11-12 00:52:46] 00:06:19<14:02:56, 16.49s/it | [Iter   23/3090] WARMUP[23/90] | LR: 0.007667 | E:  -7.808478 | E_img: +0.0412j E_var:    48.8188 E_err:   0.077197 | Acc: 0.1908
[2025-11-12 00:53:02] 00:06:35<14:01:17, 16.46s/it | [Iter   24/3090] WARMUP[24/90] | LR: 0.008000 | E:  -8.324317 | E_img: +0.0474j E_var:    49.8256 E_err:   0.077989 | Acc: 0.1903
[2025-11-12 00:53:18] 00:06:51<13:59:42, 16.44s/it | [Iter   25/3090] WARMUP[25/90] | LR: 0.008333 | E:  -9.053323 | E_img: +0.0231j E_var:    46.3919 E_err:   0.075253 | Acc: 0.1916
[2025-11-12 00:53:34] 00:07:07<13:58:17, 16.42s/it | [Iter   26/3090] WARMUP[26/90] | LR: 0.008667 | E:  -9.541791 | E_img: -0.0265j E_var:    49.4221 E_err:   0.077672 | Acc: 0.1925
[2025-11-12 00:53:50] 00:07:23<13:56:53, 16.39s/it | [Iter   27/3090] WARMUP[27/90] | LR: 0.009000 | E: -10.126051 | E_img: +0.0464j E_var:    54.7003 E_err:   0.081715 | Acc: 0.1947
[2025-11-12 00:54:05] 00:07:38<13:55:36, 16.37s/it | [Iter   28/3090] WARMUP[28/90] | LR: 0.009333 | E: -10.923414 | E_img: -0.0728j E_var:    53.8145 E_err:   0.081050 | Acc: 0.1927
[2025-11-12 00:54:21] 00:07:54<13:54:21, 16.35s/it | [Iter   29/3090] WARMUP[29/90] | LR: 0.009667 | E: -11.598679 | E_img: -0.0738j E_var:    46.1088 E_err:   0.075023 | Acc: 0.1964
[2025-11-12 00:54:37] 00:08:10<13:53:11, 16.34s/it | [Iter   30/3090] WARMUP[30/90] | LR: 0.010000 | E: -12.151367 | E_img: +0.0365j E_var:    54.0507 E_err:   0.081228 | Acc: 0.1973
[2025-11-12 00:54:53] 00:08:26<13:52:05, 16.32s/it | [Iter   31/3090] WARMUP[31/90] | LR: 0.010333 | E: -12.825897 | E_img: +0.0322j E_var:    48.8644 E_err:   0.077233 | Acc: 0.1975
[2025-11-12 00:55:09] 00:08:42<13:51:02, 16.31s/it | [Iter   32/3090] WARMUP[32/90] | LR: 0.010667 | E: -13.589252 | E_img: -0.0324j E_var:    49.2587 E_err:   0.077544 | Acc: 0.2006
[2025-11-12 00:55:24] 00:08:58<13:50:01, 16.29s/it | [Iter   33/3090] WARMUP[33/90] | LR: 0.011000 | E: -14.328316 | E_img: -0.0036j E_var:    47.2456 E_err:   0.075943 | Acc: 0.2022
[2025-11-12 00:55:40] 00:09:13<13:49:04, 16.28s/it | [Iter   34/3090] WARMUP[34/90] | LR: 0.011333 | E: -15.058892 | E_img: -0.0604j E_var:    50.3744 E_err:   0.078417 | Acc: 0.2005
[2025-11-12 00:55:56] 00:09:29<13:48:09, 16.26s/it | [Iter   35/3090] WARMUP[35/90] | LR: 0.011667 | E: -15.958254 | E_img: -0.0545j E_var:    48.5646 E_err:   0.076995 | Acc: 0.1991
[2025-11-12 00:56:12] 00:09:45<13:47:16, 16.25s/it | [Iter   36/3090] WARMUP[36/90] | LR: 0.012000 | E: -16.751933 | E_img: -0.0552j E_var:    55.2714 E_err:   0.082140 | Acc: 0.1995
[2025-11-12 00:56:28] 00:10:01<13:46:24, 16.24s/it | [Iter   37/3090] WARMUP[37/90] | LR: 0.012333 | E: -17.510766 | E_img: -0.0289j E_var:    49.1750 E_err:   0.077478 | Acc: 0.1996
[2025-11-12 00:56:44] 00:10:17<13:45:36, 16.23s/it | [Iter   38/3090] WARMUP[38/90] | LR: 0.012667 | E: -18.482845 | E_img: +0.0607j E_var:    78.4949 E_err:   0.097887 | Acc: 0.2013
[2025-11-12 00:56:59] 00:10:33<13:44:47, 16.22s/it | [Iter   39/3090] WARMUP[39/90] | LR: 0.013000 | E: -19.443729 | E_img: +0.0206j E_var:    48.9638 E_err:   0.077311 | Acc: 0.1994
[2025-11-12 00:57:15] 00:10:48<13:44:01, 16.21s/it | [Iter   40/3090] WARMUP[40/90] | LR: 0.013333 | E: -20.179529 | E_img: -0.0703j E_var:    51.0076 E_err:   0.078908 | Acc: 0.2007
[2025-11-12 00:57:31] 00:11:04<13:43:31, 16.21s/it | [Iter   41/3090] WARMUP[41/90] | LR: 0.013667 | E: -21.054980 | E_img: +0.0199j E_var:    46.9312 E_err:   0.075690 | Acc: 0.2013
[2025-11-12 00:57:47] 00:11:20<13:42:48, 16.20s/it | [Iter   42/3090] WARMUP[42/90] | LR: 0.014000 | E: -22.001074 | E_img: +0.0085j E_var:    47.8514 E_err:   0.076428 | Acc: 0.2032
[2025-11-12 00:58:03] 00:11:36<13:42:07, 16.19s/it | [Iter   43/3090] WARMUP[43/90] | LR: 0.014333 | E: -22.536608 | E_img: -0.0295j E_var:    53.6940 E_err:   0.080959 | Acc: 0.2092
[2025-11-12 00:58:19] 00:11:52<13:41:25, 16.18s/it | [Iter   44/3090] WARMUP[44/90] | LR: 0.014667 | E: -23.640288 | E_img: -0.0633j E_var:    44.7815 E_err:   0.073936 | Acc: 0.2149
[2025-11-12 00:58:35] 00:12:08<13:40:47, 16.17s/it | [Iter   45/3090] WARMUP[45/90] | LR: 0.015000 | E: -24.694341 | E_img: -0.0161j E_var:    45.9439 E_err:   0.074889 | Acc: 0.2129
[2025-11-12 00:58:50] 00:12:24<13:40:07, 16.17s/it | [Iter   46/3090] WARMUP[46/90] | LR: 0.015333 | E: -25.699333 | E_img: +0.0104j E_var:    41.4823 E_err:   0.071160 | Acc: 0.2127
[2025-11-12 00:59:06] 00:12:39<13:39:29, 16.16s/it | [Iter   47/3090] WARMUP[47/90] | LR: 0.015667 | E: -26.685619 | E_img: -0.0209j E_var:    43.3555 E_err:   0.072749 | Acc: 0.2126
[2025-11-12 00:59:22] 00:12:55<13:38:52, 16.15s/it | [Iter   48/3090] WARMUP[48/90] | LR: 0.016000 | E: -27.041559 | E_img: -0.0017j E_var:    40.6181 E_err:   0.070415 | Acc: 0.2274
[2025-11-12 00:59:38] 00:13:11<13:38:15, 16.14s/it | [Iter   49/3090] WARMUP[49/90] | LR: 0.016333 | E: -27.821201 | E_img: +0.0134j E_var:    40.5998 E_err:   0.070399 | Acc: 0.2275
[2025-11-12 00:59:54] 00:13:27<13:37:41, 16.14s/it | [Iter   50/3090] WARMUP[50/90] | LR: 0.016667 | E: -28.848495 | E_img: +0.0734j E_var:    40.1558 E_err:   0.070013 | Acc: 0.2327
[2025-11-12 01:00:10] 00:13:43<13:37:06, 16.13s/it | [Iter   51/3090] WARMUP[51/90] | LR: 0.017000 | E: -30.291595 | E_img: -0.0353j E_var:    41.1924 E_err:   0.070911 | Acc: 0.2192
[2025-11-12 01:00:25] 00:13:59<13:36:33, 16.13s/it | [Iter   52/3090] WARMUP[52/90] | LR: 0.017333 | E: -30.772185 | E_img: +0.0640j E_var:    38.8663 E_err:   0.068880 | Acc: 0.2219
[2025-11-12 01:00:41] 00:14:14<13:36:00, 16.12s/it | [Iter   53/3090] WARMUP[53/90] | LR: 0.017667 | E: -31.943911 | E_img: -0.1281j E_var:    40.1423 E_err:   0.070001 | Acc: 0.2118
[2025-11-12 01:00:57] 00:14:30<13:35:28, 16.12s/it | [Iter   54/3090] WARMUP[54/90] | LR: 0.018000 | E: -33.042939 | E_img: +0.0872j E_var:    44.0805 E_err:   0.073355 | Acc: 0.1969
[2025-11-12 01:01:13] 00:14:46<13:34:56, 16.11s/it | [Iter   55/3090] WARMUP[55/90] | LR: 0.018333 | E: -34.418608 | E_img: -0.0655j E_var:    46.1427 E_err:   0.075051 | Acc: 0.1883
[2025-11-12 01:01:29] 00:15:02<13:34:24, 16.11s/it | [Iter   56/3090] WARMUP[56/90] | LR: 0.018667 | E: -34.792663 | E_img: -0.0246j E_var:    41.2328 E_err:   0.070946 | Acc: 0.1882
[2025-11-12 01:01:45] 00:15:18<13:33:54, 16.10s/it | [Iter   57/3090] WARMUP[57/90] | LR: 0.019000 | E: -34.808535 | E_img: -0.0609j E_var:    44.5345 E_err:   0.073732 | Acc: 0.1931
[2025-11-12 01:02:00] 00:15:34<13:33:24, 16.10s/it | [Iter   58/3090] WARMUP[58/90] | LR: 0.019333 | E: -35.124767 | E_img: +0.1249j E_var:    56.7375 E_err:   0.083222 | Acc: 0.1905
[2025-11-12 01:02:16] 00:15:49<13:32:55, 16.09s/it | [Iter   59/3090] WARMUP[59/90] | LR: 0.019667 | E: -36.451938 | E_img: -0.0020j E_var:    50.9560 E_err:   0.078868 | Acc: 0.1934
[2025-11-12 01:02:32] 00:16:05<13:32:27, 16.09s/it | [Iter   60/3090] WARMUP[60/90] | LR: 0.020000 | E: -37.535882 | E_img: -0.0002j E_var:    40.1155 E_err:   0.069978 | Acc: 0.1942
[2025-11-12 01:02:48] 00:16:21<13:31:58, 16.08s/it | [Iter   61/3090] WARMUP[61/90] | LR: 0.020333 | E: -38.909318 | E_img: -0.0501j E_var:    34.1993 E_err:   0.064612 | Acc: 0.1960
[2025-11-12 01:03:04] 00:16:37<13:31:30, 16.08s/it | [Iter   62/3090] WARMUP[62/90] | LR: 0.020667 | E: -39.100616 | E_img: -0.0988j E_var:    33.3316 E_err:   0.063787 | Acc: 0.1915
[2025-11-12 01:03:20] 00:16:53<13:31:02, 16.08s/it | [Iter   63/3090] WARMUP[63/90] | LR: 0.021000 | E: -39.884371 | E_img: -0.3022j E_var:    32.3127 E_err:   0.062805 | Acc: 0.1931
[2025-11-12 01:03:36] 00:17:09<13:30:35, 16.07s/it | [Iter   64/3090] WARMUP[64/90] | LR: 0.021333 | E: -40.311201 | E_img: -0.0251j E_var:    31.7613 E_err:   0.062266 | Acc: 0.2024
[2025-11-12 01:03:51] 00:17:24<13:30:08, 16.07s/it | [Iter   65/3090] WARMUP[65/90] | LR: 0.021667 | E: -40.255763 | E_img: -0.0198j E_var:    30.3592 E_err:   0.060877 | Acc: 0.2195
[2025-11-12 01:04:07] 00:17:40<13:29:41, 16.07s/it | [Iter   66/3090] WARMUP[66/90] | LR: 0.022000 | E: -39.765325 | E_img: -0.0173j E_var:    41.2078 E_err:   0.070924 | Acc: 0.2161
[2025-11-12 01:04:23] 00:17:56<13:29:15, 16.06s/it | [Iter   67/3090] WARMUP[67/90] | LR: 0.022333 | E: -40.081873 | E_img: -0.1828j E_var:    37.3380 E_err:   0.067512 | Acc: 0.2181
[2025-11-12 01:04:39] 00:18:12<13:28:49, 16.06s/it | [Iter   68/3090] WARMUP[68/90] | LR: 0.022667 | E: -40.259207 | E_img: -0.0793j E_var:    35.8241 E_err:   0.066129 | Acc: 0.2215
[2025-11-12 01:04:55] 00:18:28<13:28:23, 16.06s/it | [Iter   69/3090] WARMUP[69/90] | LR: 0.023000 | E: -41.350879 | E_img: -0.0945j E_var:    31.5455 E_err:   0.062055 | Acc: 0.2333
[2025-11-12 01:05:11] 00:18:44<13:27:58, 16.05s/it | [Iter   70/3090] WARMUP[70/90] | LR: 0.023333 | E: -42.396485 | E_img: +0.0935j E_var:    29.7392 E_err:   0.060252 | Acc: 0.2252
[2025-11-12 01:05:26] 00:18:59<13:27:32, 16.05s/it | [Iter   71/3090] WARMUP[71/90] | LR: 0.023667 | E: -42.361925 | E_img: +0.0148j E_var:    28.3846 E_err:   0.058864 | Acc: 0.2430
[2025-11-12 01:05:42] 00:19:15<13:27:08, 16.05s/it | [Iter   72/3090] WARMUP[72/90] | LR: 0.024000 | E: -42.175460 | E_img: -0.0027j E_var:    29.5578 E_err:   0.060068 | Acc: 0.2622
[2025-11-12 01:05:58] 00:19:31<13:26:42, 16.04s/it | [Iter   73/3090] WARMUP[73/90] | LR: 0.024333 | E: -43.657779 | E_img: -0.2173j E_var:    31.7762 E_err:   0.062281 | Acc: 0.2477
[2025-11-12 01:06:14] 00:19:47<13:26:18, 16.04s/it | [Iter   74/3090] WARMUP[74/90] | LR: 0.024667 | E: -43.792670 | E_img: -0.0660j E_var:    27.0825 E_err:   0.057498 | Acc: 0.2496
[2025-11-12 01:06:30] 00:20:03<13:25:53, 16.04s/it | [Iter   75/3090] WARMUP[75/90] | LR: 0.025000 | E: -44.753744 | E_img: +0.0257j E_var:    26.3680 E_err:   0.056734 | Acc: 0.2491
[2025-11-12 01:06:46] 00:20:19<13:25:30, 16.04s/it | [Iter   76/3090] WARMUP[76/90] | LR: 0.025333 | E: -45.322648 | E_img: -0.1652j E_var:    27.0940 E_err:   0.057510 | Acc: 0.2453
[2025-11-12 01:07:01] 00:20:34<13:25:05, 16.03s/it | [Iter   77/3090] WARMUP[77/90] | LR: 0.025667 | E: -46.334216 | E_img: -0.0800j E_var:    28.0432 E_err:   0.058508 | Acc: 0.2400
[2025-11-12 01:07:17] 00:20:50<13:24:42, 16.03s/it | [Iter   78/3090] WARMUP[78/90] | LR: 0.026000 | E: -46.903556 | E_img: +0.0018j E_var:    25.7890 E_err:   0.056108 | Acc: 0.2407
[2025-11-12 01:07:33] 00:21:06<13:24:18, 16.03s/it | [Iter   79/3090] WARMUP[79/90] | LR: 0.026333 | E: -46.072174 | E_img: -0.0190j E_var:    33.6550 E_err:   0.064096 | Acc: 0.2484
[2025-11-12 01:07:49] 00:21:22<13:23:54, 16.02s/it | [Iter   80/3090] WARMUP[80/90] | LR: 0.026667 | E: -46.002245 | E_img: -0.0975j E_var:    40.2112 E_err:   0.070061 | Acc: 0.2401
[2025-11-12 01:08:05] 00:21:38<13:23:31, 16.02s/it | [Iter   81/3090] WARMUP[81/90] | LR: 0.027000 | E: -47.541787 | E_img: -0.0316j E_var:    30.0443 E_err:   0.060560 | Acc: 0.2333
[2025-11-12 01:08:21] 00:21:54<13:23:08, 16.02s/it | [Iter   82/3090] WARMUP[82/90] | LR: 0.027333 | E: -47.655896 | E_img: +0.0619j E_var:    31.2671 E_err:   0.061780 | Acc: 0.2299
[2025-11-12 01:08:36] 00:22:09<13:22:45, 16.02s/it | [Iter   83/3090] WARMUP[83/90] | LR: 0.027667 | E: -47.100690 | E_img: +0.1904j E_var:    35.0076 E_err:   0.065371 | Acc: 0.2203
[2025-11-12 01:08:52] 00:22:25<13:22:22, 16.02s/it | [Iter   84/3090] WARMUP[84/90] | LR: 0.028000 | E: -45.639475 | E_img: +0.2781j E_var:    43.2639 E_err:   0.072672 | Acc: 0.2225
[2025-11-12 01:09:08] 00:22:41<13:22:00, 16.01s/it | [Iter   85/3090] WARMUP[85/90] | LR: 0.028333 | E: -44.605821 | E_img: +0.0640j E_var:    41.1470 E_err:   0.070872 | Acc: 0.2339
[2025-11-12 01:09:24] 00:22:57<13:21:37, 16.01s/it | [Iter   86/3090] WARMUP[86/90] | LR: 0.028667 | E: -46.190055 | E_img: +0.2400j E_var:    37.5121 E_err:   0.067669 | Acc: 0.2172
[2025-11-12 01:09:40] 00:23:13<13:21:14, 16.01s/it | [Iter   87/3090] WARMUP[87/90] | LR: 0.029000 | E: -47.752427 | E_img: -0.1745j E_var:    26.6896 E_err:   0.057079 | Acc: 0.2263
[2025-11-12 01:09:55] 00:23:29<13:20:53, 16.01s/it | [Iter   88/3090] WARMUP[88/90] | LR: 0.029333 | E: -48.875766 | E_img: -0.0419j E_var:    23.8818 E_err:   0.053993 | Acc: 0.2416
[2025-11-12 01:10:11] 00:23:44<13:20:31, 16.00s/it | [Iter   89/3090] WARMUP[89/90] | LR: 0.029667 | E: -48.635741 | E_img: +0.0708j E_var:    26.8303 E_err:   0.057229 | Acc: 0.2437
[2025-11-12 01:10:11] ✅ Warm-up completed | Starting cosine annealing from LR=0.030000
[2025-11-12 01:10:27] 00:24:00<13:20:09, 16.00s/it | [Iter   90/3090] WARMUP[90/90] | LR: 0.030000 | E: -49.090674 | E_img: -0.2263j E_var:    22.9841 E_err:   0.052969 | Acc: 0.2326
[2025-11-12 01:10:43] 00:24:16<13:19:48, 16.00s/it | [Iter   91/3090] R0[0/3000]    | LR: 0.030000 | E: -49.859064 | E_img: -0.1230j E_var:    21.3144 E_err:   0.051008 | Acc: 0.2192
[2025-11-12 01:10:59] 00:24:32<13:19:26, 16.00s/it | [Iter   92/3090] R0[1/3000]    | LR: 0.030000 | E: -48.983081 | E_img: -0.3524j E_var:    23.3587 E_err:   0.053398 | Acc: 0.2391
[2025-11-12 01:11:15] 00:24:48<13:19:05, 16.00s/it | [Iter   93/3090] R0[2/3000]    | LR: 0.030000 | E: -49.532091 | E_img: +0.0471j E_var:    22.8043 E_err:   0.052761 | Acc: 0.2376
[2025-11-12 01:11:30] 00:25:04<13:18:44, 16.00s/it | [Iter   94/3090] R0[3/3000]    | LR: 0.030000 | E: -50.001147 | E_img: +0.2446j E_var:    21.1800 E_err:   0.050847 | Acc: 0.2286
[2025-11-12 01:11:46] 00:25:19<13:18:23, 15.99s/it | [Iter   95/3090] R0[4/3000]    | LR: 0.030000 | E: -48.901993 | E_img: -0.0309j E_var:    22.4873 E_err:   0.052393 | Acc: 0.2531
[2025-11-12 01:12:02] 00:25:35<13:18:02, 15.99s/it | [Iter   96/3090] R0[5/3000]    | LR: 0.030000 | E: -49.899512 | E_img: +0.1818j E_var:    22.6030 E_err:   0.052528 | Acc: 0.2389
[2025-11-12 01:12:18] 00:25:51<13:17:42, 15.99s/it | [Iter   97/3090] R0[6/3000]    | LR: 0.030000 | E: -49.860434 | E_img: +0.1117j E_var:    31.9485 E_err:   0.062450 | Acc: 0.2220
[2025-11-12 01:12:34] 00:26:07<13:17:22, 15.99s/it | [Iter   98/3090] R0[7/3000]    | LR: 0.030000 | E: -49.237988 | E_img: -0.1089j E_var:   153.1186 E_err:   0.136716 | Acc: 0.2207
[2025-11-12 01:12:50] 00:26:23<13:17:01, 15.99s/it | [Iter   99/3090] R0[8/3000]    | LR: 0.029999 | E: -49.239024 | E_img: -0.3667j E_var:   270.1036 E_err:   0.181581 | Acc: 0.2369
[2025-11-12 01:13:06] 00:26:39<13:16:41, 15.99s/it | [Iter  100/3090] R0[9/3000]    | LR: 0.029999 | E: -12.268488 | E_img: +0.6071j E_var:   119.0604 E_err:   0.120556 | Acc: 0.5965
[2025-11-12 01:13:21] 00:26:54<13:16:19, 15.99s/it | [Iter  101/3090] R0[10/3000]   | LR: 0.029999 | E: -23.306070 | E_img: -0.0454j E_var:    69.8997 E_err:   0.092372 | Acc: 0.5563
[2025-11-12 01:13:37] 00:27:10<13:15:59, 15.98s/it | [Iter  102/3090] R0[11/3000]   | LR: 0.029999 | E: -29.366678 | E_img: -0.4141j E_var:    67.0572 E_err:   0.090475 | Acc: 0.4680
[2025-11-12 01:13:53] 00:27:26<13:15:38, 15.98s/it | [Iter  103/3090] R0[12/3000]   | LR: 0.029999 | E: -33.205726 | E_img: +0.0379j E_var:    51.5760 E_err:   0.079347 | Acc: 0.4496
[2025-11-12 01:14:09] 00:27:42<13:15:18, 15.98s/it | [Iter  104/3090] R0[13/3000]   | LR: 0.029999 | E: -34.942600 | E_img: -0.1893j E_var:    44.2062 E_err:   0.073459 | Acc: 0.4769
[2025-11-12 01:14:25] 00:27:58<13:14:57, 15.98s/it | [Iter  105/3090] R0[14/3000]   | LR: 0.029998 | E: -37.782473 | E_img: -0.2587j E_var:    41.7306 E_err:   0.071373 | Acc: 0.4231
[2025-11-12 01:14:41] 00:28:14<13:14:37, 15.98s/it | [Iter  106/3090] R0[15/3000]   | LR: 0.029998 | E: -41.705099 | E_img: -0.1412j E_var:    33.5229 E_err:   0.063970 | Acc: 0.3833
[2025-11-12 01:14:56] 00:28:29<13:14:17, 15.98s/it | [Iter  107/3090] R0[16/3000]   | LR: 0.029998 | E: -40.779959 | E_img: +0.0441j E_var:    31.0820 E_err:   0.061597 | Acc: 0.4310
[2025-11-12 01:15:12] 00:28:45<13:13:57, 15.97s/it | [Iter  108/3090] R0[17/3000]   | LR: 0.029998 | E: -38.854519 | E_img: -0.0335j E_var:    31.4538 E_err:   0.061964 | Acc: 0.4756
[2025-11-12 01:15:28] 00:29:01<13:13:36, 15.97s/it | [Iter  109/3090] R0[18/3000]   | LR: 0.029997 | E: -40.545144 | E_img: +0.0261j E_var:    29.7152 E_err:   0.060227 | Acc: 0.4779
[2025-11-12 01:15:44] 00:29:17<13:13:17, 15.97s/it | [Iter  110/3090] R0[19/3000]   | LR: 0.029997 | E: -45.276390 | E_img: -0.0869j E_var:    22.3138 E_err:   0.052191 | Acc: 0.4285
[2025-11-12 01:16:00] 00:29:33<13:12:57, 15.97s/it | [Iter  111/3090] R0[20/3000]   | LR: 0.029997 | E: -48.083418 | E_img: -0.0287j E_var:    20.5295 E_err:   0.050060 | Acc: 0.3697
[2025-11-12 01:16:15] 00:29:49<13:12:38, 15.97s/it | [Iter  112/3090] R0[21/3000]   | LR: 0.029996 | E: -47.053811 | E_img: +0.0571j E_var:    21.3269 E_err:   0.051023 | Acc: 0.3930
[2025-11-12 01:16:31] 00:30:04<13:12:18, 15.97s/it | [Iter  113/3090] R0[22/3000]   | LR: 0.029996 | E: -47.614351 | E_img: -0.8487j E_var:    29.7782 E_err:   0.060291 | Acc: 0.3564
[2025-11-12 01:16:47] 00:30:20<13:11:59, 15.97s/it | [Iter  114/3090] R0[23/3000]   | LR: 0.029996 | E: -42.086324 | E_img: +0.5725j E_var:    76.0279 E_err:   0.096337 | Acc: 0.3764
[2025-11-12 01:17:03] 00:30:36<13:11:40, 15.97s/it | [Iter  115/3090] R0[24/3000]   | LR: 0.029995 | E: -46.074330 | E_img: +0.0406j E_var:    25.8700 E_err:   0.056196 | Acc: 0.3976
[2025-11-12 01:17:19] 00:30:52<13:11:20, 15.97s/it | [Iter  116/3090] R0[25/3000]   | LR: 0.029995 | E: -47.793471 | E_img: -0.0473j E_var:    24.4437 E_err:   0.054625 | Acc: 0.3837
[2025-11-12 01:17:35] 00:31:08<13:11:01, 15.96s/it | [Iter  117/3090] R0[26/3000]   | LR: 0.029994 | E: -48.596321 | E_img: +0.0865j E_var:    24.9349 E_err:   0.055171 | Acc: 0.3689
[2025-11-12 01:17:51] 00:31:24<13:10:42, 15.96s/it | [Iter  118/3090] R0[27/3000]   | LR: 0.029994 | E: -48.323281 | E_img: +0.1360j E_var:    20.9920 E_err:   0.050621 | Acc: 0.3895
[2025-11-12 01:18:06] 00:31:39<13:10:23, 15.96s/it | [Iter  119/3090] R0[28/3000]   | LR: 0.029994 | E: -48.488803 | E_img: +0.0792j E_var:    19.8392 E_err:   0.049212 | Acc: 0.3791
[2025-11-12 01:18:22] 00:31:55<13:10:04, 15.96s/it | [Iter  120/3090] R0[29/3000]   | LR: 0.029993 | E: -48.098736 | E_img: +0.0598j E_var:    20.3630 E_err:   0.049857 | Acc: 0.4312
[2025-11-12 01:18:38] 00:32:11<13:09:45, 15.96s/it | [Iter  121/3090] R0[30/3000]   | LR: 0.029993 | E: -46.374173 | E_img: +0.1534j E_var:    22.2956 E_err:   0.052169 | Acc: 0.4876
[2025-11-12 01:18:54] 00:32:27<13:09:26, 15.96s/it | [Iter  122/3090] R0[31/3000]   | LR: 0.029992 | E: -48.033542 | E_img: -0.1904j E_var:    20.1425 E_err:   0.049586 | Acc: 0.4518
[2025-11-12 01:19:10] 00:32:43<13:09:07, 15.96s/it | [Iter  123/3090] R0[32/3000]   | LR: 0.029992 | E: -49.398445 | E_img: -0.0638j E_var:    17.6185 E_err:   0.046376 | Acc: 0.4250
[2025-11-12 01:19:26] 00:32:59<13:08:48, 15.96s/it | [Iter  124/3090] R0[33/3000]   | LR: 0.029991 | E: -51.494802 | E_img: +0.0058j E_var:    15.3856 E_err:   0.043337 | Acc: 0.3850
[2025-11-12 01:19:41] 00:33:14<13:08:30, 15.96s/it | [Iter  125/3090] R0[34/3000]   | LR: 0.029990 | E: -49.505430 | E_img: +0.0268j E_var:    17.1578 E_err:   0.045765 | Acc: 0.4314
[2025-11-12 01:19:57] 00:33:30<13:08:11, 15.96s/it | [Iter  126/3090] R0[35/3000]   | LR: 0.029990 | E: -50.487412 | E_img: +0.0007j E_var:    16.6613 E_err:   0.045098 | Acc: 0.4102
[2025-11-12 01:20:13] 00:33:46<13:07:52, 15.95s/it | [Iter  127/3090] R0[36/3000]   | LR: 0.029989 | E: -51.328429 | E_img: -0.0655j E_var:    20.6229 E_err:   0.050174 | Acc: 0.3861
[2025-11-12 01:20:29] 00:34:02<13:07:33, 15.95s/it | [Iter  128/3090] R0[37/3000]   | LR: 0.029989 | E: -51.303036 | E_img: +0.0908j E_var:    16.2834 E_err:   0.044584 | Acc: 0.4198
[2025-11-12 01:20:45] 00:34:18<13:07:15, 15.95s/it | [Iter  129/3090] R0[38/3000]   | LR: 0.029988 | E: -52.189229 | E_img: +0.0858j E_var:    14.0440 E_err:   0.041405 | Acc: 0.4209
[2025-11-12 01:21:01] 00:34:34<13:06:56, 15.95s/it | [Iter  130/3090] R0[39/3000]   | LR: 0.029987 | E: -54.159555 | E_img: +0.0669j E_var:    13.1699 E_err:   0.040096 | Acc: 0.3839
[2025-11-12 01:21:16] 00:34:50<13:06:37, 15.95s/it | [Iter  131/3090] R0[40/3000]   | LR: 0.029987 | E: -54.616132 | E_img: +0.0262j E_var:    14.3897 E_err:   0.041911 | Acc: 0.3480
[2025-11-12 01:21:32] 00:35:05<13:06:19, 15.95s/it | [Iter  132/3090] R0[41/3000]   | LR: 0.029986 | E: -53.608191 | E_img: +0.0791j E_var:    13.1171 E_err:   0.040015 | Acc: 0.4095
[2025-11-12 01:21:48] 00:35:21<13:06:00, 15.95s/it | [Iter  133/3090] R0[42/3000]   | LR: 0.029985 | E: -54.628758 | E_img: +0.0184j E_var:    12.2135 E_err:   0.038612 | Acc: 0.4006
[2025-11-12 01:22:04] 00:35:37<13:05:42, 15.95s/it | [Iter  134/3090] R0[43/3000]   | LR: 0.029985 | E: -55.413743 | E_img: +0.0729j E_var:    13.2968 E_err:   0.040288 | Acc: 0.3637
[2025-11-12 01:22:20] 00:35:53<13:05:23, 15.95s/it | [Iter  135/3090] R0[44/3000]   | LR: 0.029984 | E: -55.510769 | E_img: +0.0809j E_var:    13.3897 E_err:   0.040429 | Acc: 0.3405
[2025-11-12 01:22:36] 00:36:09<13:05:05, 15.95s/it | [Iter  136/3090] R0[45/3000]   | LR: 0.029983 | E: -55.473249 | E_img: +0.1296j E_var:    13.2511 E_err:   0.040219 | Acc: 0.3203
[2025-11-12 01:22:51] 00:36:24<13:04:46, 15.95s/it | [Iter  137/3090] R0[46/3000]   | LR: 0.029983 | E: -55.385194 | E_img: +0.0352j E_var:    12.8805 E_err:   0.039653 | Acc: 0.3226
[2025-11-12 01:23:07] 00:36:40<13:04:28, 15.94s/it | [Iter  138/3090] R0[47/3000]   | LR: 0.029982 | E: -55.267362 | E_img: -0.0086j E_var:    13.6012 E_err:   0.040747 | Acc: 0.3144
[2025-11-12 01:23:23] 00:36:56<13:04:09, 15.94s/it | [Iter  139/3090] R0[48/3000]   | LR: 0.029981 | E: -55.596458 | E_img: -0.1162j E_var:    11.9569 E_err:   0.038204 | Acc: 0.3268
[2025-11-12 01:23:39] 00:37:12<13:03:51, 15.94s/it | [Iter  140/3090] R0[49/3000]   | LR: 0.029980 | E: -56.007314 | E_img: -0.0759j E_var:    11.8407 E_err:   0.038018 | Acc: 0.3256
[2025-11-12 01:23:55] 00:37:28<13:03:33, 15.94s/it | [Iter  141/3090] R0[50/3000]   | LR: 0.029979 | E: -56.003239 | E_img: -0.0767j E_var:    11.5655 E_err:   0.037574 | Acc: 0.3470
[2025-11-12 01:24:11] 00:37:44<13:03:15, 15.94s/it | [Iter  142/3090] R0[51/3000]   | LR: 0.029979 | E: -55.604956 | E_img: +0.1981j E_var:    16.4641 E_err:   0.044831 | Acc: 0.3218
[2025-11-12 01:24:26] 00:37:59<13:02:56, 15.94s/it | [Iter  143/3090] R0[52/3000]   | LR: 0.029978 | E: -54.800785 | E_img: -0.1120j E_var:    12.1379 E_err:   0.038493 | Acc: 0.4209
[2025-11-12 01:24:42] 00:38:15<13:02:38, 15.94s/it | [Iter  144/3090] R0[53/3000]   | LR: 0.029977 | E: -55.790226 | E_img: +0.0466j E_var:    11.0665 E_err:   0.036755 | Acc: 0.4015
[2025-11-12 01:24:58] 00:38:31<13:02:20, 15.94s/it | [Iter  145/3090] R0[54/3000]   | LR: 0.029976 | E: -54.063446 | E_img: -0.0217j E_var:    12.2625 E_err:   0.038690 | Acc: 0.4764
[2025-11-12 01:25:14] 00:38:47<13:02:02, 15.94s/it | [Iter  146/3090] R0[55/3000]   | LR: 0.029975 | E: -55.949376 | E_img: -0.0349j E_var:    11.3342 E_err:   0.037196 | Acc: 0.3967
[2025-11-12 01:25:30] 00:39:03<13:01:45, 15.94s/it | [Iter  147/3090] R0[56/3000]   | LR: 0.029974 | E: -56.085105 | E_img: +0.0446j E_var:    11.6279 E_err:   0.037675 | Acc: 0.3616
[2025-11-12 01:25:46] 00:39:19<13:01:27, 15.94s/it | [Iter  148/3090] R0[57/3000]   | LR: 0.029973 | E: -54.820322 | E_img: +0.0214j E_var:    12.2301 E_err:   0.038638 | Acc: 0.4242
[2025-11-12 01:26:01] 00:39:35<13:01:10, 15.94s/it | [Iter  149/3090] R0[58/3000]   | LR: 0.029972 | E: -55.406840 | E_img: -0.0786j E_var:    11.6456 E_err:   0.037704 | Acc: 0.4018
[2025-11-12 01:26:17] 00:39:50<13:00:52, 15.94s/it | [Iter  150/3090] R0[59/3000]   | LR: 0.029971 | E: -55.692250 | E_img: -0.0959j E_var:    11.3810 E_err:   0.037273 | Acc: 0.3911
[2025-11-12 01:26:33] 00:40:06<13:00:34, 15.94s/it | [Iter  151/3090] R0[60/3000]   | LR: 0.029970 | E: -56.775361 | E_img: +0.0272j E_var:    10.9869 E_err:   0.036622 | Acc: 0.3582
[2025-11-12 01:26:49] 00:40:22<13:00:17, 15.93s/it | [Iter  152/3090] R0[61/3000]   | LR: 0.029969 | E: -53.829632 | E_img: -0.0211j E_var:    13.2648 E_err:   0.040240 | Acc: 0.4660
[2025-11-12 01:27:05] 00:40:38<12:59:59, 15.93s/it | [Iter  153/3090] R0[62/3000]   | LR: 0.029968 | E: -56.143492 | E_img: +0.0816j E_var:    10.8979 E_err:   0.036473 | Acc: 0.4033
[2025-11-12 01:27:21] 00:40:54<12:59:41, 15.93s/it | [Iter  154/3090] R0[63/3000]   | LR: 0.029967 | E: -57.112879 | E_img: +0.1238j E_var:    11.7493 E_err:   0.037871 | Acc: 0.3523
[2025-11-12 01:27:36] 00:41:10<12:59:23, 15.93s/it | [Iter  155/3090] R0[64/3000]   | LR: 0.029966 | E: -56.754401 | E_img: +0.1039j E_var:    10.4024 E_err:   0.035635 | Acc: 0.3978
[2025-11-12 01:27:52] 00:41:25<12:59:05, 15.93s/it | [Iter  156/3090] R0[65/3000]   | LR: 0.029965 | E: -57.592333 | E_img: -0.0009j E_var:    10.6146 E_err:   0.035996 | Acc: 0.3634
[2025-11-12 01:28:08] 00:41:41<12:58:47, 15.93s/it | [Iter  157/3090] R0[66/3000]   | LR: 0.029964 | E: -57.366660 | E_img: +0.0023j E_var:     9.8366 E_err:   0.034652 | Acc: 0.3555
[2025-11-12 01:28:24] 00:41:57<12:58:29, 15.93s/it | [Iter  158/3090] R0[67/3000]   | LR: 0.029963 | E: -57.702968 | E_img: -0.0136j E_var:    11.7593 E_err:   0.037888 | Acc: 0.3326
[2025-11-12 01:28:40] 00:42:13<12:58:12, 15.93s/it | [Iter  159/3090] R0[68/3000]   | LR: 0.029962 | E: -54.903553 | E_img: +0.1572j E_var:    13.2664 E_err:   0.040242 | Acc: 0.4644
[2025-11-12 01:28:56] 00:42:29<12:57:54, 15.93s/it | [Iter  160/3090] R0[69/3000]   | LR: 0.029961 | E: -55.645620 | E_img: -0.0010j E_var:    12.5607 E_err:   0.039157 | Acc: 0.4428
[2025-11-12 01:29:11] 00:42:45<12:57:37, 15.93s/it | [Iter  161/3090] R0[70/3000]   | LR: 0.029960 | E: -55.203252 | E_img: +0.0285j E_var:    13.2731 E_err:   0.040252 | Acc: 0.4547
[2025-11-12 01:29:27] 00:43:00<12:57:19, 15.93s/it | [Iter  162/3090] R0[71/3000]   | LR: 0.029959 | E: -56.316470 | E_img: -0.1902j E_var:    11.7617 E_err:   0.037891 | Acc: 0.3973
[2025-11-12 01:29:43] 00:43:16<12:57:02, 15.93s/it | [Iter  163/3090] R0[72/3000]   | LR: 0.029957 | E: -56.699516 | E_img: +0.0420j E_var:    11.2477 E_err:   0.037054 | Acc: 0.3968
[2025-11-12 01:29:59] 00:43:32<12:56:44, 15.93s/it | [Iter  164/3090] R0[73/3000]   | LR: 0.029956 | E: -55.583803 | E_img: +0.0731j E_var:    11.8059 E_err:   0.037962 | Acc: 0.4602
[2025-11-12 01:30:15] 00:43:48<12:56:27, 15.93s/it | [Iter  165/3090] R0[74/3000]   | LR: 0.029955 | E: -56.963950 | E_img: +0.0116j E_var:    10.0331 E_err:   0.034996 | Acc: 0.4335
[2025-11-12 01:30:31] 00:44:04<12:56:10, 15.93s/it | [Iter  166/3090] R0[75/3000]   | LR: 0.029954 | E: -56.297970 | E_img: -0.0077j E_var:    10.8662 E_err:   0.036420 | Acc: 0.4420
[2025-11-12 01:30:47] 00:44:20<12:55:53, 15.93s/it | [Iter  167/3090] R0[76/3000]   | LR: 0.029953 | E: -57.021745 | E_img: +0.0203j E_var:    10.2773 E_err:   0.035420 | Acc: 0.4189
[2025-11-12 01:31:02] 00:44:36<12:55:36, 15.93s/it | [Iter  168/3090] R0[77/3000]   | LR: 0.029951 | E: -57.359386 | E_img: -0.0454j E_var:    10.3368 E_err:   0.035522 | Acc: 0.3818
[2025-11-12 01:31:19] 00:44:52<12:55:24, 15.93s/it | [Iter  169/3090] R0[78/3000]   | LR: 0.029950 | E: -55.313589 | E_img: +0.0471j E_var:    12.2555 E_err:   0.038679 | Acc: 0.4652
[2025-11-12 01:31:34] 00:45:08<12:55:07, 15.93s/it | [Iter  170/3090] R0[79/3000]   | LR: 0.029949 | E: -55.934944 | E_img: +0.0339j E_var:    10.7905 E_err:   0.036293 | Acc: 0.4676
[2025-11-12 01:31:50] 00:45:24<12:54:51, 15.93s/it | [Iter  171/3090] R0[80/3000]   | LR: 0.029947 | E: -57.268933 | E_img: -0.0857j E_var:     9.8761 E_err:   0.034722 | Acc: 0.4110
[2025-11-12 01:32:06] 00:45:39<12:54:34, 15.93s/it | [Iter  172/3090] R0[81/3000]   | LR: 0.029946 | E: -58.034557 | E_img: -0.0882j E_var:     9.5180 E_err:   0.034086 | Acc: 0.3711
[2025-11-12 01:32:22] 00:45:55<12:54:17, 15.93s/it | [Iter  173/3090] R0[82/3000]   | LR: 0.029945 | E: -57.113167 | E_img: +0.1262j E_var:     9.9847 E_err:   0.034912 | Acc: 0.4009
[2025-11-12 01:32:38] 00:46:11<12:54:00, 15.93s/it | [Iter  174/3090] R0[83/3000]   | LR: 0.029943 | E: -57.873319 | E_img: -0.0182j E_var:     8.9293 E_err:   0.033015 | Acc: 0.3856
[2025-11-12 01:32:54] 00:46:27<12:53:42, 15.93s/it | [Iter  175/3090] R0[84/3000]   | LR: 0.029942 | E: -57.998198 | E_img: +0.0018j E_var:     8.7512 E_err:   0.032684 | Acc: 0.3906
[2025-11-12 01:33:10] 00:46:43<12:53:25, 15.92s/it | [Iter  176/3090] R0[85/3000]   | LR: 0.029941 | E: -58.550605 | E_img: -0.0099j E_var:     8.2460 E_err:   0.031727 | Acc: 0.3830
[2025-11-12 01:33:26] 00:46:59<12:53:08, 15.92s/it | [Iter  177/3090] R0[86/3000]   | LR: 0.029939 | E: -58.488568 | E_img: -0.0281j E_var:     8.0617 E_err:   0.031370 | Acc: 0.4022
[2025-11-12 01:33:42] 00:47:15<12:52:57, 15.93s/it | [Iter  178/3090] R0[87/3000]   | LR: 0.029938 | E: -59.304579 | E_img: -0.0457j E_var:     8.4083 E_err:   0.032037 | Acc: 0.3591
[2025-11-12 01:33:58] 00:47:31<12:52:39, 15.93s/it | [Iter  179/3090] R0[88/3000]   | LR: 0.029936 | E: -59.320372 | E_img: +0.0534j E_var:     7.6733 E_err:   0.030605 | Acc: 0.3548
[2025-11-12 01:34:13] 00:47:46<12:52:22, 15.92s/it | [Iter  180/3090] R0[89/3000]   | LR: 0.029935 | E: -58.656615 | E_img: +0.1104j E_var:     8.2782 E_err:   0.031789 | Acc: 0.3928
[2025-11-12 01:34:29] 00:48:02<12:52:04, 15.92s/it | [Iter  181/3090] R0[90/3000]   | LR: 0.029933 | E: -57.298719 | E_img: +0.0192j E_var:     9.5187 E_err:   0.034087 | Acc: 0.4558
[2025-11-12 01:34:45] 00:48:18<12:51:47, 15.92s/it | [Iter  182/3090] R0[91/3000]   | LR: 0.029932 | E: -57.758959 | E_img: +0.0144j E_var:     8.8525 E_err:   0.032873 | Acc: 0.4484
[2025-11-12 01:35:01] 00:48:34<12:51:30, 15.92s/it | [Iter  183/3090] R0[92/3000]   | LR: 0.029930 | E: -59.036233 | E_img: +0.0166j E_var:     7.8716 E_err:   0.030998 | Acc: 0.3980
[2025-11-12 01:35:17] 00:48:50<12:51:12, 15.92s/it | [Iter  184/3090] R0[93/3000]   | LR: 0.029929 | E: -57.807932 | E_img: +0.0428j E_var:     8.9683 E_err:   0.033087 | Acc: 0.4297
[2025-11-12 01:35:33] 00:49:06<12:50:55, 15.92s/it | [Iter  185/3090] R0[94/3000]   | LR: 0.029927 | E: -58.910924 | E_img: -0.1017j E_var:     9.0541 E_err:   0.033245 | Acc: 0.3761
[2025-11-12 01:35:48] 00:49:22<12:50:38, 15.92s/it | [Iter  186/3090] R0[95/3000]   | LR: 0.029926 | E: -59.149587 | E_img: -0.0178j E_var:     9.3757 E_err:   0.033830 | Acc: 0.3388
[2025-11-12 01:36:04] 00:49:37<12:50:21, 15.92s/it | [Iter  187/3090] R0[96/3000]   | LR: 0.029924 | E: -58.137896 | E_img: +0.0777j E_var:     9.2374 E_err:   0.033580 | Acc: 0.3721
[2025-11-12 01:36:20] 00:49:53<12:50:04, 15.92s/it | [Iter  188/3090] R0[97/3000]   | LR: 0.029923 | E: -58.965948 | E_img: -0.0443j E_var:     8.5338 E_err:   0.032276 | Acc: 0.3547
[2025-11-12 01:36:36] 00:50:09<12:49:47, 15.92s/it | [Iter  189/3090] R0[98/3000]   | LR: 0.029921 | E: -59.066436 | E_img: -0.0146j E_var:     8.0674 E_err:   0.031381 | Acc: 0.3525
[2025-11-12 01:36:52] 00:50:25<12:49:30, 15.92s/it | [Iter  190/3090] R0[99/3000]   | LR: 0.029919 | E: -59.443393 | E_img: -0.0085j E_var:     7.8769 E_err:   0.031009 | Acc: 0.3485
[2025-11-12 01:37:08] 00:50:41<12:49:13, 15.92s/it | [Iter  191/3090] R0[100/3000]  | LR: 0.029918 | E: -59.773974 | E_img: +0.0452j E_var:     8.1450 E_err:   0.031532 | Acc: 0.3338
[2025-11-12 01:37:23] 00:50:57<12:48:55, 15.92s/it | [Iter  192/3090] R0[101/3000]  | LR: 0.029916 | E: -59.695477 | E_img: +0.0358j E_var:     7.8692 E_err:   0.030993 | Acc: 0.3264
[2025-11-12 01:37:39] 00:51:12<12:48:38, 15.92s/it | [Iter  193/3090] R0[102/3000]  | LR: 0.029915 | E: -59.774313 | E_img: -0.0497j E_var:     8.7522 E_err:   0.032686 | Acc: 0.3102
[2025-11-12 01:37:55] 00:51:28<12:48:21, 15.92s/it | [Iter  194/3090] R0[103/3000]  | LR: 0.029913 | E: -59.821202 | E_img: +0.0373j E_var:     7.3262 E_err:   0.029905 | Acc: 0.3366
[2025-11-12 01:38:11] 00:51:44<12:48:04, 15.92s/it | [Iter  195/3090] R0[104/3000]  | LR: 0.029911 | E: -59.963064 | E_img: -0.0437j E_var:     7.6017 E_err:   0.030462 | Acc: 0.3319
[2025-11-12 01:38:27] 00:52:00<12:47:47, 15.92s/it | [Iter  196/3090] R0[105/3000]  | LR: 0.029909 | E: -59.607659 | E_img: +0.0181j E_var:     7.5478 E_err:   0.030354 | Acc: 0.3544
[2025-11-12 01:38:43] 00:52:16<12:47:30, 15.92s/it | [Iter  197/3090] R0[106/3000]  | LR: 0.029908 | E: -59.640201 | E_img: +0.0316j E_var:     7.4942 E_err:   0.030246 | Acc: 0.3550
[2025-11-12 01:38:59] 00:52:32<12:47:13, 15.92s/it | [Iter  198/3090] R0[107/3000]  | LR: 0.029906 | E: -60.119148 | E_img: -0.0309j E_var:     7.2402 E_err:   0.029729 | Acc: 0.3385
[2025-11-12 01:39:15] 00:52:48<12:47:03, 15.92s/it | [Iter  199/3090] R0[108/3000]  | LR: 0.029904 | E: -59.689321 | E_img: +0.0191j E_var:     7.5591 E_err:   0.030377 | Acc: 0.3557
[2025-11-12 01:39:31] 00:53:04<12:46:46, 15.92s/it | [Iter  200/3090] R0[109/3000]  | LR: 0.029902 | E: -60.030144 | E_img: +0.0136j E_var:     7.3295 E_err:   0.029912 | Acc: 0.3501
[2025-11-12 01:39:46] 00:53:20<12:46:28, 15.92s/it | [Iter  201/3090] R0[110/3000]  | LR: 0.029901 | E: -59.880443 | E_img: +0.0018j E_var:     7.4025 E_err:   0.030060 | Acc: 0.3564
[2025-11-12 01:40:02] 00:53:35<12:46:11, 15.92s/it | [Iter  202/3090] R0[111/3000]  | LR: 0.029899 | E: -60.349893 | E_img: +0.0288j E_var:     7.5046 E_err:   0.030267 | Acc: 0.3278
[2025-11-12 01:40:18] 00:53:51<12:45:54, 15.92s/it | [Iter  203/3090] R0[112/3000]  | LR: 0.029897 | E: -60.464515 | E_img: +0.0406j E_var:     7.0900 E_err:   0.029419 | Acc: 0.3190
[2025-11-12 01:40:34] 00:54:07<12:45:37, 15.92s/it | [Iter  204/3090] R0[113/3000]  | LR: 0.029895 | E: -60.519800 | E_img: +0.0243j E_var:     8.1183 E_err:   0.031480 | Acc: 0.3092
[2025-11-12 01:40:50] 00:54:23<12:45:20, 15.92s/it | [Iter  205/3090] R0[114/3000]  | LR: 0.029893 | E: -60.469263 | E_img: +0.0233j E_var:     7.0834 E_err:   0.029405 | Acc: 0.3156
[2025-11-12 01:41:06] 00:54:39<12:45:04, 15.92s/it | [Iter  206/3090] R0[115/3000]  | LR: 0.029891 | E: -60.627554 | E_img: +0.0135j E_var:     7.7298 E_err:   0.030718 | Acc: 0.2999
[2025-11-12 01:41:22] 00:54:55<12:44:46, 15.92s/it | [Iter  207/3090] R0[116/3000]  | LR: 0.029889 | E: -60.631758 | E_img: -0.0174j E_var:     6.7717 E_err:   0.028751 | Acc: 0.3111
[2025-11-12 01:41:37] 00:55:10<12:44:29, 15.92s/it | [Iter  208/3090] R0[117/3000]  | LR: 0.029888 | E: -60.743125 | E_img: -0.0279j E_var:     7.8306 E_err:   0.030917 | Acc: 0.2973
[2025-11-12 01:41:53] 00:55:26<12:44:13, 15.92s/it | [Iter  209/3090] R0[118/3000]  | LR: 0.029886 | E: -60.734477 | E_img: +0.0379j E_var:     7.1304 E_err:   0.029503 | Acc: 0.2960
[2025-11-12 01:42:09] 00:55:42<12:43:56, 15.92s/it | [Iter  210/3090] R0[119/3000]  | LR: 0.029884 | E: -60.791637 | E_img: +0.0408j E_var:     8.3484 E_err:   0.031923 | Acc: 0.2817
[2025-11-12 01:42:25] 00:55:58<12:43:39, 15.91s/it | [Iter  211/3090] R0[120/3000]  | LR: 0.029882 | E: -60.785706 | E_img: -0.0114j E_var:     6.9991 E_err:   0.029230 | Acc: 0.2956
[2025-11-12 01:42:41] 00:56:14<12:43:22, 15.91s/it | [Iter  212/3090] R0[121/3000]  | LR: 0.029880 | E: -60.777983 | E_img: +0.0159j E_var:     7.8591 E_err:   0.030974 | Acc: 0.2925
[2025-11-12 01:42:57] 00:56:30<12:43:05, 15.91s/it | [Iter  213/3090] R0[122/3000]  | LR: 0.029878 | E: -60.847957 | E_img: +0.0097j E_var:     7.4620 E_err:   0.030181 | Acc: 0.2856
[2025-11-12 01:43:12] 00:56:46<12:42:48, 15.91s/it | [Iter  214/3090] R0[123/3000]  | LR: 0.029876 | E: -60.327054 | E_img: +0.0556j E_var:     7.1780 E_err:   0.029601 | Acc: 0.3393
[2025-11-12 01:43:28] 00:57:01<12:42:31, 15.91s/it | [Iter  215/3090] R0[124/3000]  | LR: 0.029874 | E: -60.587063 | E_img: +0.0223j E_var:     7.3765 E_err:   0.030007 | Acc: 0.3281
[2025-11-12 01:43:44] 00:57:17<12:42:14, 15.91s/it | [Iter  216/3090] R0[125/3000]  | LR: 0.029872 | E: -60.687397 | E_img: -0.0177j E_var:     7.0242 E_err:   0.029282 | Acc: 0.3153
[2025-11-12 01:44:00] 00:57:33<12:41:57, 15.91s/it | [Iter  217/3090] R0[126/3000]  | LR: 0.029870 | E: -60.864440 | E_img: +0.0046j E_var:     7.3686 E_err:   0.029992 | Acc: 0.2928
[2025-11-12 01:44:16] 00:57:49<12:41:40, 15.91s/it | [Iter  218/3090] R0[127/3000]  | LR: 0.029868 | E: -60.869147 | E_img: +0.0076j E_var:     7.8830 E_err:   0.031021 | Acc: 0.2716
[2025-11-12 01:44:32] 00:58:05<12:41:23, 15.91s/it | [Iter  219/3090] R0[128/3000]  | LR: 0.029865 | E: -60.791743 | E_img: -0.0390j E_var:     7.7850 E_err:   0.030827 | Acc: 0.2737
[2025-11-12 01:44:47] 00:58:21<12:41:06, 15.91s/it | [Iter  220/3090] R0[129/3000]  | LR: 0.029863 | E: -60.923574 | E_img: -0.0247j E_var:     7.1410 E_err:   0.029525 | Acc: 0.2947
[2025-11-12 01:45:03] 00:58:36<12:40:49, 15.91s/it | [Iter  221/3090] R0[130/3000]  | LR: 0.029861 | E: -60.838991 | E_img: -0.0231j E_var:     6.8626 E_err:   0.028943 | Acc: 0.3071
[2025-11-12 01:45:19] 00:58:52<12:40:32, 15.91s/it | [Iter  222/3090] R0[131/3000]  | LR: 0.029859 | E: -60.794135 | E_img: -0.0116j E_var:     7.0105 E_err:   0.029254 | Acc: 0.3152
[2025-11-12 01:45:35] 00:59:08<12:40:15, 15.91s/it | [Iter  223/3090] R0[132/3000]  | LR: 0.029857 | E: -60.919792 | E_img: +0.0163j E_var:     7.0874 E_err:   0.029414 | Acc: 0.3014
[2025-11-12 01:45:51] 00:59:24<12:39:59, 15.91s/it | [Iter  224/3090] R0[133/3000]  | LR: 0.029855 | E: -61.050712 | E_img: +0.0572j E_var:     7.9456 E_err:   0.031144 | Acc: 0.2757
[2025-11-12 01:46:07] 00:59:40<12:39:43, 15.91s/it | [Iter  225/3090] R0[134/3000]  | LR: 0.029853 | E: -61.158280 | E_img: +0.0049j E_var:     6.8791 E_err:   0.028978 | Acc: 0.2822
[2025-11-12 01:46:23] 00:59:56<12:39:26, 15.91s/it | [Iter  226/3090] R0[135/3000]  | LR: 0.029850 | E: -61.145887 | E_img: +0.0355j E_var:     7.1470 E_err:   0.029537 | Acc: 0.2790
[2025-11-12 01:46:38] 01:00:12<12:39:10, 15.91s/it | [Iter  227/3090] R0[136/3000]  | LR: 0.029848 | E: -61.165110 | E_img: -0.0104j E_var:     6.7678 E_err:   0.028743 | Acc: 0.2922
[2025-11-12 01:46:54] 01:00:27<12:38:53, 15.91s/it | [Iter  228/3090] R0[137/3000]  | LR: 0.029846 | E: -61.078390 | E_img: -0.0241j E_var:     6.6787 E_err:   0.028553 | Acc: 0.2980
[2025-11-12 01:47:10] 01:00:43<12:38:37, 15.91s/it | [Iter  229/3090] R0[138/3000]  | LR: 0.029844 | E: -61.200455 | E_img: +0.0046j E_var:     6.8121 E_err:   0.028837 | Acc: 0.2916
[2025-11-12 01:47:26] 01:00:59<12:38:20, 15.91s/it | [Iter  230/3090] R0[139/3000]  | LR: 0.029841 | E: -61.279076 | E_img: -0.0093j E_var:     7.1346 E_err:   0.029511 | Acc: 0.2833
[2025-11-12 01:47:42] 01:01:15<12:38:03, 15.91s/it | [Iter  231/3090] R0[140/3000]  | LR: 0.029839 | E: -60.932955 | E_img: -0.0404j E_var:     6.8597 E_err:   0.028937 | Acc: 0.3196
[2025-11-12 01:47:58] 01:01:31<12:37:47, 15.91s/it | [Iter  232/3090] R0[141/3000]  | LR: 0.029837 | E: -61.013164 | E_img: -0.0049j E_var:     6.7662 E_err:   0.028739 | Acc: 0.3178
[2025-11-12 01:48:14] 01:01:47<12:37:30, 15.91s/it | [Iter  233/3090] R0[142/3000]  | LR: 0.029834 | E: -60.920508 | E_img: -0.0172j E_var:     6.6268 E_err:   0.028442 | Acc: 0.3203
[2025-11-12 01:48:29] 01:02:02<12:37:13, 15.91s/it | [Iter  234/3090] R0[143/3000]  | LR: 0.029832 | E: -60.741360 | E_img: -0.0230j E_var:     6.9694 E_err:   0.029168 | Acc: 0.3223
[2025-11-12 01:48:45] 01:02:18<12:36:57, 15.91s/it | [Iter  235/3090] R0[144/3000]  | LR: 0.029830 | E: -60.851721 | E_img: +0.0105j E_var:     6.9814 E_err:   0.029193 | Acc: 0.3185
[2025-11-12 01:49:01] 01:02:34<12:36:40, 15.91s/it | [Iter  236/3090] R0[145/3000]  | LR: 0.029827 | E: -60.915286 | E_img: +0.0354j E_var:     6.9651 E_err:   0.029159 | Acc: 0.3101
[2025-11-12 01:49:17] 01:02:50<12:36:23, 15.91s/it | [Iter  237/3090] R0[146/3000]  | LR: 0.029825 | E: -60.828011 | E_img: +0.0239j E_var:     6.6910 E_err:   0.028579 | Acc: 0.3076
[2025-11-12 01:49:33] 01:03:06<12:36:06, 15.91s/it | [Iter  238/3090] R0[147/3000]  | LR: 0.029823 | E: -61.034614 | E_img: +0.0157j E_var:     6.7006 E_err:   0.028600 | Acc: 0.2987
[2025-11-12 01:49:49] 01:03:22<12:35:49, 15.91s/it | [Iter  239/3090] R0[148/3000]  | LR: 0.029820 | E: -60.961838 | E_img: +0.0648j E_var:     7.1107 E_err:   0.029462 | Acc: 0.2844
[2025-11-12 01:50:04] 01:03:37<12:35:33, 15.91s/it | [Iter  240/3090] R0[149/3000]  | LR: 0.029818 | E: -60.566123 | E_img: +0.0216j E_var:     7.0798 E_err:   0.029398 | Acc: 0.3036
[2025-11-12 01:50:20] 01:03:53<12:35:16, 15.91s/it | [Iter  241/3090] R0[150/3000]  | LR: 0.029815 | E: -60.684648 | E_img: -0.0339j E_var:     6.8848 E_err:   0.028990 | Acc: 0.3152
[2025-11-12 01:50:36] 01:04:09<12:34:59, 15.91s/it | [Iter  242/3090] R0[151/3000]  | LR: 0.029813 | E: -60.695309 | E_img: -0.0086j E_var:     6.8645 E_err:   0.028947 | Acc: 0.3114
[2025-11-12 01:50:52] 01:04:25<12:34:43, 15.91s/it | [Iter  243/3090] R0[152/3000]  | LR: 0.029810 | E: -60.552863 | E_img: -0.0242j E_var:     7.0014 E_err:   0.029235 | Acc: 0.3176
[2025-11-12 01:51:08] 01:04:41<12:34:26, 15.91s/it | [Iter  244/3090] R0[153/3000]  | LR: 0.029808 | E: -60.206088 | E_img: -0.0419j E_var:     7.6368 E_err:   0.030532 | Acc: 0.3132
[2025-11-12 01:51:24] 01:04:57<12:34:09, 15.90s/it | [Iter  245/3090] R0[154/3000]  | LR: 0.029805 | E: -60.180981 | E_img: -0.0370j E_var:     7.2554 E_err:   0.029760 | Acc: 0.3116
[2025-11-12 01:51:39] 01:05:13<12:33:52, 15.90s/it | [Iter  246/3090] R0[155/3000]  | LR: 0.029803 | E: -60.340357 | E_img: +0.0319j E_var:     7.2753 E_err:   0.029801 | Acc: 0.3143
[2025-11-12 01:51:55] 01:05:28<12:33:36, 15.90s/it | [Iter  247/3090] R0[156/3000]  | LR: 0.029800 | E: -60.371670 | E_img: +0.0762j E_var:     7.5665 E_err:   0.030391 | Acc: 0.3019
[2025-11-12 01:52:11] 01:05:44<12:33:19, 15.90s/it | [Iter  248/3090] R0[157/3000]  | LR: 0.029798 | E: -59.596800 | E_img: +0.0493j E_var:     8.3798 E_err:   0.031983 | Acc: 0.3497
[2025-11-12 01:52:27] 01:06:00<12:33:02, 15.90s/it | [Iter  249/3090] R0[158/3000]  | LR: 0.029795 | E: -60.036174 | E_img: +0.1238j E_var:     7.9051 E_err:   0.031064 | Acc: 0.3213
[2025-11-12 01:52:43] 01:06:16<12:32:46, 15.90s/it | [Iter  250/3090] R0[159/3000]  | LR: 0.029793 | E: -59.759338 | E_img: -0.0286j E_var:     7.7592 E_err:   0.030776 | Acc: 0.3179
[2025-11-12 01:52:59] 01:06:32<12:32:30, 15.90s/it | [Iter  251/3090] R0[160/3000]  | LR: 0.029790 | E: -59.851565 | E_img: +0.0610j E_var:     7.6301 E_err:   0.030519 | Acc: 0.3214
[2025-11-12 01:53:14] 01:06:48<12:32:13, 15.90s/it | [Iter  252/3090] R0[161/3000]  | LR: 0.029787 | E: -59.948188 | E_img: -0.0015j E_var:     7.3708 E_err:   0.029996 | Acc: 0.3181
[2025-11-12 01:53:30] 01:07:03<12:31:56, 15.90s/it | [Iter  253/3090] R0[162/3000]  | LR: 0.029785 | E: -60.057335 | E_img: -0.0111j E_var:     7.3290 E_err:   0.029911 | Acc: 0.3175
[2025-11-12 01:53:46] 01:07:19<12:31:40, 15.90s/it | [Iter  254/3090] R0[163/3000]  | LR: 0.029782 | E: -60.300492 | E_img: +0.0047j E_var:     7.4812 E_err:   0.030220 | Acc: 0.3004
[2025-11-12 01:54:02] 01:07:35<12:31:23, 15.90s/it | [Iter  255/3090] R0[164/3000]  | LR: 0.029779 | E: -60.286366 | E_img: +0.0906j E_var:     7.3765 E_err:   0.030007 | Acc: 0.2832
[2025-11-12 01:54:18] 01:07:51<12:31:06, 15.90s/it | [Iter  256/3090] R0[165/3000]  | LR: 0.029777 | E: -60.850953 | E_img: +0.0272j E_var:     7.0524 E_err:   0.029341 | Acc: 0.2731
[2025-11-12 01:54:34] 01:08:07<12:30:50, 15.90s/it | [Iter  257/3090] R0[166/3000]  | LR: 0.029774 | E: -61.013225 | E_img: -0.0636j E_var:     7.3437 E_err:   0.029941 | Acc: 0.2626
[2025-11-12 01:54:49] 01:08:23<12:30:33, 15.90s/it | [Iter  258/3090] R0[167/3000]  | LR: 0.029771 | E: -61.233398 | E_img: +0.0266j E_var:     7.2474 E_err:   0.029744 | Acc: 0.2504
[2025-11-12 01:55:05] 01:08:38<12:30:16, 15.90s/it | [Iter  259/3090] R0[168/3000]  | LR: 0.029768 | E: -61.183498 | E_img: +0.0392j E_var:     6.7879 E_err:   0.028785 | Acc: 0.2508
[2025-11-12 01:55:21] 01:08:54<12:30:00, 15.90s/it | [Iter  260/3090] R0[169/3000]  | LR: 0.029766 | E: -61.329008 | E_img: +0.0043j E_var:     7.3454 E_err:   0.029944 | Acc: 0.2463
[2025-11-12 01:55:37] 01:09:10<12:29:43, 15.90s/it | [Iter  261/3090] R0[170/3000]  | LR: 0.029763 | E: -61.402355 | E_img: +0.0151j E_var:     6.3124 E_err:   0.027759 | Acc: 0.2519
[2025-11-12 01:55:53] 01:09:26<12:29:27, 15.90s/it | [Iter  262/3090] R0[171/3000]  | LR: 0.029760 | E: -61.473587 | E_img: -0.0140j E_var:     7.7368 E_err:   0.030732 | Acc: 0.2391
[2025-11-12 01:56:09] 01:09:42<12:29:11, 15.90s/it | [Iter  263/3090] R0[172/3000]  | LR: 0.029757 | E: -61.384661 | E_img: +0.0205j E_var:     7.7025 E_err:   0.030663 | Acc: 0.2270
[2025-11-12 01:56:25] 01:09:58<12:28:54, 15.90s/it | [Iter  264/3090] R0[173/3000]  | LR: 0.029755 | E: -61.490875 | E_img: -0.0007j E_var:     6.8377 E_err:   0.028891 | Acc: 0.2296
[2025-11-12 01:56:40] 01:10:13<12:28:37, 15.90s/it | [Iter  265/3090] R0[174/3000]  | LR: 0.029752 | E: -61.434043 | E_img: +0.0405j E_var:     6.3736 E_err:   0.027893 | Acc: 0.2606
[2025-11-12 01:56:56] 01:10:29<12:28:21, 15.90s/it | [Iter  266/3090] R0[175/3000]  | LR: 0.029749 | E: -61.549627 | E_img: +0.0346j E_var:     6.8598 E_err:   0.028937 | Acc: 0.2555
[2025-11-12 01:57:12] 01:10:45<12:28:04, 15.90s/it | [Iter  267/3090] R0[176/3000]  | LR: 0.029746 | E: -61.598280 | E_img: +0.0125j E_var:     7.8821 E_err:   0.031019 | Acc: 0.2326
[2025-11-12 01:57:28] 01:11:01<12:27:48, 15.90s/it | [Iter  268/3090] R0[177/3000]  | LR: 0.029743 | E: -61.613035 | E_img: +0.0222j E_var:     6.2376 E_err:   0.027594 | Acc: 0.2455
[2025-11-12 01:57:44] 01:11:17<12:27:31, 15.90s/it | [Iter  269/3090] R0[178/3000]  | LR: 0.029740 | E: -61.580571 | E_img: +0.0288j E_var:     6.3156 E_err:   0.027766 | Acc: 0.2643
[2025-11-12 01:58:00] 01:11:33<12:27:15, 15.90s/it | [Iter  270/3090] R0[179/3000]  | LR: 0.029737 | E: -61.349961 | E_img: +0.0022j E_var:     6.6879 E_err:   0.028573 | Acc: 0.2963
[2025-11-12 01:58:15] 01:11:49<12:26:58, 15.90s/it | [Iter  271/3090] R0[180/3000]  | LR: 0.029734 | E: -61.373546 | E_img: -0.0263j E_var:     6.9177 E_err:   0.029059 | Acc: 0.2945
[2025-11-12 01:58:31] 01:12:04<12:26:42, 15.90s/it | [Iter  272/3090] R0[181/3000]  | LR: 0.029731 | E: -61.604415 | E_img: +0.0167j E_var:     6.5409 E_err:   0.028257 | Acc: 0.2775
[2025-11-12 01:58:47] 01:12:20<12:26:25, 15.90s/it | [Iter  273/3090] R0[182/3000]  | LR: 0.029728 | E: -61.636573 | E_img: -0.0052j E_var:     6.3583 E_err:   0.027860 | Acc: 0.2738
[2025-11-12 01:59:03] 01:12:36<12:26:08, 15.90s/it | [Iter  274/3090] R0[183/3000]  | LR: 0.029725 | E: -61.701349 | E_img: -0.0208j E_var:     6.4952 E_err:   0.028158 | Acc: 0.2663
[2025-11-12 01:59:19] 01:12:52<12:25:52, 15.90s/it | [Iter  275/3090] R0[184/3000]  | LR: 0.029722 | E: -61.739331 | E_img: +0.0042j E_var:     6.9382 E_err:   0.029102 | Acc: 0.2521
[2025-11-12 01:59:35] 01:13:08<12:25:35, 15.90s/it | [Iter  276/3090] R0[185/3000]  | LR: 0.029719 | E: -61.748616 | E_img: +0.0076j E_var:     6.4317 E_err:   0.028020 | Acc: 0.2488
[2025-11-12 01:59:50] 01:13:24<12:25:19, 15.90s/it | [Iter  277/3090] R0[186/3000]  | LR: 0.029716 | E: -61.729739 | E_img: -0.0111j E_var:     6.6419 E_err:   0.028474 | Acc: 0.2472
[2025-11-12 02:00:06] 01:13:39<12:25:02, 15.90s/it | [Iter  278/3090] R0[187/3000]  | LR: 0.029713 | E: -61.609874 | E_img: -0.0068j E_var:     6.6325 E_err:   0.028454 | Acc: 0.2600
[2025-11-12 02:00:22] 01:13:55<12:24:46, 15.90s/it | [Iter  279/3090] R0[188/3000]  | LR: 0.029710 | E: -61.688860 | E_img: -0.0101j E_var:     6.2843 E_err:   0.027697 | Acc: 0.2645
[2025-11-12 02:00:38] 01:14:11<12:24:29, 15.90s/it | [Iter  280/3090] R0[189/3000]  | LR: 0.029707 | E: -61.583603 | E_img: -0.0155j E_var:     6.1077 E_err:   0.027305 | Acc: 0.2816
[2025-11-12 02:00:54] 01:14:27<12:24:13, 15.90s/it | [Iter  281/3090] R0[190/3000]  | LR: 0.029704 | E: -61.619096 | E_img: -0.0230j E_var:     6.3906 E_err:   0.027930 | Acc: 0.2807
[2025-11-12 02:01:10] 01:14:43<12:23:56, 15.90s/it | [Iter  282/3090] R0[191/3000]  | LR: 0.029701 | E: -61.784221 | E_img: +0.0250j E_var:     6.5929 E_err:   0.028369 | Acc: 0.2636
[2025-11-12 02:01:25] 01:14:59<12:23:40, 15.90s/it | [Iter  283/3090] R0[192/3000]  | LR: 0.029698 | E: -61.821948 | E_img: +0.0029j E_var:     6.2490 E_err:   0.027619 | Acc: 0.2557
[2025-11-12 02:01:41] 01:15:14<12:23:23, 15.90s/it | [Iter  284/3090] R0[193/3000]  | LR: 0.029695 | E: -61.776526 | E_img: +0.0338j E_var:     6.2826 E_err:   0.027693 | Acc: 0.2572
[2025-11-12 02:01:57] 01:15:30<12:23:07, 15.90s/it | [Iter  285/3090] R0[194/3000]  | LR: 0.029692 | E: -61.853539 | E_img: +0.0129j E_var:     6.0372 E_err:   0.027147 | Acc: 0.2605
[2025-11-12 02:02:13] 01:15:46<12:22:51, 15.90s/it | [Iter  286/3090] R0[195/3000]  | LR: 0.029688 | E: -61.794016 | E_img: -0.0020j E_var:     6.3285 E_err:   0.027794 | Acc: 0.2614
[2025-11-12 02:02:29] 01:16:02<12:22:34, 15.90s/it | [Iter  287/3090] R0[196/3000]  | LR: 0.029685 | E: -61.722647 | E_img: +0.0125j E_var:     6.0124 E_err:   0.027091 | Acc: 0.2732
[2025-11-12 02:02:45] 01:16:18<12:22:18, 15.90s/it | [Iter  288/3090] R0[197/3000]  | LR: 0.029682 | E: -61.759894 | E_img: -0.0018j E_var:     6.0463 E_err:   0.027168 | Acc: 0.2732
[2025-11-12 02:03:00] 01:16:34<12:22:01, 15.89s/it | [Iter  289/3090] R0[198/3000]  | LR: 0.029679 | E: -61.776984 | E_img: -0.0198j E_var:     6.6661 E_err:   0.028526 | Acc: 0.2719
[2025-11-12 02:03:16] 01:16:49<12:21:45, 15.89s/it | [Iter  290/3090] R0[199/3000]  | LR: 0.029675 | E: -61.817823 | E_img: +0.0086j E_var:     6.7161 E_err:   0.028633 | Acc: 0.2704
[2025-11-12 02:03:32] 01:17:05<12:21:29, 15.89s/it | [Iter  291/3090] R0[200/3000]  | LR: 0.029672 | E: -61.826691 | E_img: -0.0023j E_var:     6.0374 E_err:   0.027148 | Acc: 0.2723
[2025-11-12 02:03:48] 01:17:21<12:21:12, 15.89s/it | [Iter  292/3090] R0[201/3000]  | LR: 0.029669 | E: -61.821303 | E_img: -0.0099j E_var:     6.6585 E_err:   0.028510 | Acc: 0.2648
[2025-11-12 02:04:04] 01:17:37<12:20:56, 15.89s/it | [Iter  293/3090] R0[202/3000]  | LR: 0.029666 | E: -61.836205 | E_img: -0.0615j E_var:     6.7757 E_err:   0.028760 | Acc: 0.2482
[2025-11-12 02:04:20] 01:17:53<12:20:39, 15.89s/it | [Iter  294/3090] R0[203/3000]  | LR: 0.029662 | E: -61.798774 | E_img: -0.0017j E_var:     6.6278 E_err:   0.028444 | Acc: 0.2405
[2025-11-12 02:04:35] 01:18:09<12:20:23, 15.89s/it | [Iter  295/3090] R0[204/3000]  | LR: 0.029659 | E: -61.753673 | E_img: +0.0396j E_var:     6.1868 E_err:   0.027481 | Acc: 0.2700
[2025-11-12 02:04:51] 01:18:24<12:20:06, 15.89s/it | [Iter  296/3090] R0[205/3000]  | LR: 0.029656 | E: -61.787852 | E_img: -0.0127j E_var:     6.7492 E_err:   0.028703 | Acc: 0.2661
[2025-11-12 02:05:07] 01:18:40<12:19:50, 15.89s/it | [Iter  297/3090] R0[206/3000]  | LR: 0.029652 | E: -61.816167 | E_img: -0.0130j E_var:     6.1824 E_err:   0.027471 | Acc: 0.2706
[2025-11-12 02:05:23] 01:18:56<12:19:33, 15.89s/it | [Iter  298/3090] R0[207/3000]  | LR: 0.029649 | E: -61.865141 | E_img: -0.0131j E_var:     6.8041 E_err:   0.028820 | Acc: 0.2477
[2025-11-12 02:05:39] 01:19:12<12:19:17, 15.89s/it | [Iter  299/3090] R0[208/3000]  | LR: 0.029646 | E: -61.871742 | E_img: -0.0105j E_var:     7.3767 E_err:   0.030008 | Acc: 0.2348
[2025-11-12 02:05:55] 01:19:28<12:19:00, 15.89s/it | [Iter  300/3090] R0[209/3000]  | LR: 0.029642 | E: -61.826860 | E_img: -0.0221j E_var:     7.9530 E_err:   0.031158 | Acc: 0.2277
[2025-11-12 02:06:11] 01:19:44<12:18:44, 15.89s/it | [Iter  301/3090] R0[210/3000]  | LR: 0.029639 | E: -61.821890 | E_img: +0.0157j E_var:     6.6727 E_err:   0.028540 | Acc: 0.2262
[2025-11-12 02:06:26] 01:19:59<12:18:28, 15.89s/it | [Iter  302/3090] R0[211/3000]  | LR: 0.029635 | E: -61.877014 | E_img: -0.0057j E_var:     6.4507 E_err:   0.028061 | Acc: 0.2337
[2025-11-12 02:06:42] 01:20:15<12:18:11, 15.89s/it | [Iter  303/3090] R0[212/3000]  | LR: 0.029632 | E: -61.940760 | E_img: -0.0127j E_var:     8.6683 E_err:   0.032529 | Acc: 0.2259
[2025-11-12 02:06:58] 01:20:31<12:17:55, 15.89s/it | [Iter  304/3090] R0[213/3000]  | LR: 0.029628 | E: -61.855773 | E_img: +0.0196j E_var:     7.1344 E_err:   0.029511 | Acc: 0.2159
[2025-11-12 02:07:14] 01:20:47<12:17:39, 15.89s/it | [Iter  305/3090] R0[214/3000]  | LR: 0.029625 | E: -61.992015 | E_img: +0.0119j E_var:     6.0478 E_err:   0.027171 | Acc: 0.2321
[2025-11-12 02:07:30] 01:21:03<12:17:22, 15.89s/it | [Iter  306/3090] R0[215/3000]  | LR: 0.029621 | E: -61.950478 | E_img: +0.0332j E_var:     6.2367 E_err:   0.027592 | Acc: 0.2394
[2025-11-12 02:07:46] 01:21:19<12:17:06, 15.89s/it | [Iter  307/3090] R0[216/3000]  | LR: 0.029618 | E: -62.010976 | E_img: +0.0182j E_var:     6.0809 E_err:   0.027245 | Acc: 0.2434
[2025-11-12 02:08:01] 01:21:35<12:16:50, 15.89s/it | [Iter  308/3090] R0[217/3000]  | LR: 0.029614 | E: -61.962419 | E_img: +0.0385j E_var:     6.2023 E_err:   0.027516 | Acc: 0.2592
[2025-11-12 02:08:17] 01:21:50<12:16:34, 15.89s/it | [Iter  309/3090] R0[218/3000]  | LR: 0.029611 | E: -62.031931 | E_img: -0.0220j E_var:     7.4941 E_err:   0.030246 | Acc: 0.2341
[2025-11-12 02:08:33] 01:22:06<12:16:17, 15.89s/it | [Iter  310/3090] R0[219/3000]  | LR: 0.029607 | E: -62.038051 | E_img: +0.0140j E_var:     6.4868 E_err:   0.028140 | Acc: 0.2267
[2025-11-12 02:08:49] 01:22:22<12:16:01, 15.89s/it | [Iter  311/3090] R0[220/3000]  | LR: 0.029604 | E: -62.035573 | E_img: +0.0196j E_var:     6.0515 E_err:   0.027179 | Acc: 0.2412
[2025-11-12 02:09:05] 01:22:38<12:15:44, 15.89s/it | [Iter  312/3090] R0[221/3000]  | LR: 0.029600 | E: -61.949929 | E_img: +0.0348j E_var:     5.9129 E_err:   0.026866 | Acc: 0.2517
[2025-11-12 02:09:21] 01:22:54<12:15:28, 15.89s/it | [Iter  313/3090] R0[222/3000]  | LR: 0.029596 | E: -61.967374 | E_img: -0.0045j E_var:     6.0807 E_err:   0.027245 | Acc: 0.2631
[2025-11-12 02:09:36] 01:23:10<12:15:12, 15.89s/it | [Iter  314/3090] R0[223/3000]  | LR: 0.029593 | E: -61.888909 | E_img: -0.0013j E_var:     6.3278 E_err:   0.027793 | Acc: 0.2768
[2025-11-12 02:09:52] 01:23:25<12:14:55, 15.89s/it | [Iter  315/3090] R0[224/3000]  | LR: 0.029589 | E: -61.779974 | E_img: +0.0003j E_var:     6.9183 E_err:   0.029061 | Acc: 0.2667
[2025-11-12 02:10:08] 01:23:41<12:14:39, 15.89s/it | [Iter  316/3090] R0[225/3000]  | LR: 0.029586 | E: -61.936250 | E_img: +0.0313j E_var:     6.0953 E_err:   0.027277 | Acc: 0.2698
[2025-11-12 02:10:24] 01:23:57<12:14:23, 15.89s/it | [Iter  317/3090] R0[226/3000]  | LR: 0.029582 | E: -61.985080 | E_img: -0.0222j E_var:     6.2557 E_err:   0.027634 | Acc: 0.2648
[2025-11-12 02:10:40] 01:24:13<12:14:06, 15.89s/it | [Iter  318/3090] R0[227/3000]  | LR: 0.029578 | E: -61.851373 | E_img: +0.0324j E_var:     6.6514 E_err:   0.028494 | Acc: 0.2479
[2025-11-12 02:10:56] 01:24:29<12:13:50, 15.89s/it | [Iter  319/3090] R0[228/3000]  | LR: 0.029574 | E: -61.868460 | E_img: -0.0628j E_var:     6.4184 E_err:   0.027991 | Acc: 0.2508
[2025-11-12 02:11:11] 01:24:45<12:13:33, 15.89s/it | [Iter  320/3090] R0[229/3000]  | LR: 0.029571 | E: -61.974378 | E_img: -0.0209j E_var:     5.9018 E_err:   0.026841 | Acc: 0.2604
[2025-11-12 02:11:27] 01:25:00<12:13:17, 15.89s/it | [Iter  321/3090] R0[230/3000]  | LR: 0.029567 | E: -61.833233 | E_img: -0.0740j E_var:     6.2221 E_err:   0.027560 | Acc: 0.2646
[2025-11-12 02:11:43] 01:25:16<12:13:01, 15.89s/it | [Iter  322/3090] R0[231/3000]  | LR: 0.029563 | E: -61.741320 | E_img: +0.0221j E_var:     6.1207 E_err:   0.027334 | Acc: 0.2658
[2025-11-12 02:11:59] 01:25:32<12:12:44, 15.89s/it | [Iter  323/3090] R0[232/3000]  | LR: 0.029559 | E: -61.982413 | E_img: +0.0627j E_var:     5.8381 E_err:   0.026696 | Acc: 0.2613
[2025-11-12 02:12:15] 01:25:48<12:12:28, 15.89s/it | [Iter  324/3090] R0[233/3000]  | LR: 0.029556 | E: -61.988931 | E_img: +0.0194j E_var:     5.8138 E_err:   0.026640 | Acc: 0.2594
[2025-11-12 02:12:31] 01:26:04<12:12:12, 15.89s/it | [Iter  325/3090] R0[234/3000]  | LR: 0.029552 | E: -61.999120 | E_img: -0.0077j E_var:     6.2502 E_err:   0.027622 | Acc: 0.2607
[2025-11-12 02:12:46] 01:26:20<12:11:55, 15.89s/it | [Iter  326/3090] R0[235/3000]  | LR: 0.029548 | E: -62.051090 | E_img: +0.0309j E_var:     5.7048 E_err:   0.026389 | Acc: 0.2596
[2025-11-12 02:13:02] 01:26:35<12:11:39, 15.89s/it | [Iter  327/3090] R0[236/3000]  | LR: 0.029544 | E: -61.955580 | E_img: -0.0290j E_var:     5.7629 E_err:   0.026523 | Acc: 0.2778
[2025-11-12 02:13:18] 01:26:51<12:11:23, 15.89s/it | [Iter  328/3090] R0[237/3000]  | LR: 0.029540 | E: -62.034482 | E_img: +0.0236j E_var:     6.0739 E_err:   0.027229 | Acc: 0.2779
[2025-11-12 02:13:34] 01:27:07<12:11:06, 15.89s/it | [Iter  329/3090] R0[238/3000]  | LR: 0.029537 | E: -62.191042 | E_img: +0.0467j E_var:     6.7663 E_err:   0.028740 | Acc: 0.2494
[2025-11-12 02:13:50] 01:27:23<12:10:50, 15.89s/it | [Iter  330/3090] R0[239/3000]  | LR: 0.029533 | E: -62.168306 | E_img: +0.0018j E_var:     5.7302 E_err:   0.026448 | Acc: 0.2543
[2025-11-12 02:14:06] 01:27:39<12:10:34, 15.89s/it | [Iter  331/3090] R0[240/3000]  | LR: 0.029529 | E: -62.130478 | E_img: +0.0025j E_var:     5.5742 E_err:   0.026085 | Acc: 0.2602
[2025-11-12 02:14:21] 01:27:55<12:10:17, 15.89s/it | [Iter  332/3090] R0[241/3000]  | LR: 0.029525 | E: -62.169972 | E_img: +0.0080j E_var:     6.1198 E_err:   0.027332 | Acc: 0.2498
[2025-11-12 02:14:37] 01:28:10<12:10:01, 15.89s/it | [Iter  333/3090] R0[242/3000]  | LR: 0.029521 | E: -62.179682 | E_img: +0.0068j E_var:     5.8007 E_err:   0.026610 | Acc: 0.2466
[2025-11-12 02:14:53] 01:28:26<12:09:45, 15.89s/it | [Iter  334/3090] R0[243/3000]  | LR: 0.029517 | E: -62.171453 | E_img: +0.0034j E_var:     5.8399 E_err:   0.026700 | Acc: 0.2451
[2025-11-12 02:15:09] 01:28:42<12:09:29, 15.89s/it | [Iter  335/3090] R0[244/3000]  | LR: 0.029513 | E: -62.197926 | E_img: -0.0268j E_var:     6.3224 E_err:   0.027781 | Acc: 0.2352
[2025-11-12 02:15:25] 01:28:58<12:09:12, 15.89s/it | [Iter  336/3090] R0[245/3000]  | LR: 0.029509 | E: -62.165712 | E_img: -0.0436j E_var:     6.3523 E_err:   0.027846 | Acc: 0.2279
[2025-11-12 02:15:41] 01:29:14<12:08:56, 15.89s/it | [Iter  337/3090] R0[246/3000]  | LR: 0.029505 | E: -62.127573 | E_img: +0.0229j E_var:     6.6620 E_err:   0.028517 | Acc: 0.2188
[2025-11-12 02:15:57] 01:29:30<12:08:40, 15.89s/it | [Iter  338/3090] R0[247/3000]  | LR: 0.029501 | E: -62.192968 | E_img: -0.0354j E_var:     5.8561 E_err:   0.026737 | Acc: 0.2250
[2025-11-12 02:16:12] 01:29:45<12:08:23, 15.89s/it | [Iter  339/3090] R0[248/3000]  | LR: 0.029497 | E: -62.147969 | E_img: +0.0352j E_var:     6.1264 E_err:   0.027347 | Acc: 0.2263
[2025-11-12 02:16:28] 01:30:01<12:08:07, 15.89s/it | [Iter  340/3090] R0[249/3000]  | LR: 0.029493 | E: -62.186107 | E_img: +0.0188j E_var:     6.5519 E_err:   0.028281 | Acc: 0.2180
[2025-11-12 02:16:44] 01:30:17<12:07:51, 15.89s/it | [Iter  341/3090] R0[250/3000]  | LR: 0.029489 | E: -62.173893 | E_img: -0.0032j E_var:     6.1798 E_err:   0.027466 | Acc: 0.2138
[2025-11-12 02:17:00] 01:30:33<12:07:34, 15.89s/it | [Iter  342/3090] R0[251/3000]  | LR: 0.029485 | E: -62.183828 | E_img: -0.0199j E_var:     6.1079 E_err:   0.027306 | Acc: 0.2202
[2025-11-12 02:17:16] 01:30:49<12:07:18, 15.89s/it | [Iter  343/3090] R0[252/3000]  | LR: 0.029481 | E: -62.182769 | E_img: +0.0309j E_var:     6.7323 E_err:   0.028667 | Acc: 0.2165
[2025-11-12 02:17:32] 01:31:05<12:07:02, 15.89s/it | [Iter  344/3090] R0[253/3000]  | LR: 0.029477 | E: -62.211684 | E_img: +0.0152j E_var:     6.2630 E_err:   0.027650 | Acc: 0.2146
[2025-11-12 02:17:47] 01:31:20<12:06:46, 15.89s/it | [Iter  345/3090] R0[254/3000]  | LR: 0.029473 | E: -62.250060 | E_img: +0.0014j E_var:     5.8965 E_err:   0.026829 | Acc: 0.2246
[2025-11-12 02:18:03] 01:31:36<12:06:29, 15.89s/it | [Iter  346/3090] R0[255/3000]  | LR: 0.029468 | E: -62.216667 | E_img: +0.0188j E_var:     6.1779 E_err:   0.027462 | Acc: 0.2255
[2025-11-12 02:18:19] 01:31:52<12:06:13, 15.89s/it | [Iter  347/3090] R0[256/3000]  | LR: 0.029464 | E: -62.245323 | E_img: +0.0227j E_var:     5.7965 E_err:   0.026600 | Acc: 0.2303
[2025-11-12 02:18:35] 01:32:08<12:05:57, 15.89s/it | [Iter  348/3090] R0[257/3000]  | LR: 0.029460 | E: -62.228560 | E_img: +0.0170j E_var:     6.3047 E_err:   0.027742 | Acc: 0.2319
[2025-11-12 02:18:51] 01:32:24<12:05:41, 15.88s/it | [Iter  349/3090] R0[258/3000]  | LR: 0.029456 | E: -62.266413 | E_img: -0.0033j E_var:     6.1200 E_err:   0.027333 | Acc: 0.2320
[2025-11-12 02:19:07] 01:32:40<12:05:24, 15.88s/it | [Iter  350/3090] R0[259/3000]  | LR: 0.029452 | E: -62.292276 | E_img: +0.0004j E_var:     5.9736 E_err:   0.027004 | Acc: 0.2268
[2025-11-12 02:19:22] 01:32:56<12:05:08, 15.88s/it | [Iter  351/3090] R0[260/3000]  | LR: 0.029447 | E: -62.212684 | E_img: +0.0369j E_var:     5.8156 E_err:   0.026644 | Acc: 0.2255
[2025-11-12 02:19:38] 01:33:11<12:04:52, 15.88s/it | [Iter  352/3090] R0[261/3000]  | LR: 0.029443 | E: -62.187312 | E_img: -0.0039j E_var:     6.5759 E_err:   0.028332 | Acc: 0.2154
[2025-11-12 02:19:54] 01:33:27<12:04:36, 15.88s/it | [Iter  353/3090] R0[262/3000]  | LR: 0.029439 | E: -62.172061 | E_img: -0.0366j E_var:     6.5480 E_err:   0.028272 | Acc: 0.2087
[2025-11-12 02:20:10] 01:33:43<12:04:19, 15.88s/it | [Iter  354/3090] R0[263/3000]  | LR: 0.029435 | E: -62.166324 | E_img: -0.0015j E_var:     6.1446 E_err:   0.027387 | Acc: 0.2084
[2025-11-12 02:20:26] 01:33:59<12:04:03, 15.88s/it | [Iter  355/3090] R0[264/3000]  | LR: 0.029430 | E: -62.279007 | E_img: -0.0254j E_var:     5.8960 E_err:   0.026828 | Acc: 0.2106
[2025-11-12 02:20:42] 01:34:15<12:03:47, 15.88s/it | [Iter  356/3090] R0[265/3000]  | LR: 0.029426 | E: -62.333942 | E_img: -0.0098j E_var:     5.7175 E_err:   0.026418 | Acc: 0.2155
[2025-11-12 02:20:57] 01:34:31<12:03:31, 15.88s/it | [Iter  357/3090] R0[266/3000]  | LR: 0.029422 | E: -62.299364 | E_img: -0.0037j E_var:     6.6543 E_err:   0.028501 | Acc: 0.2149
[2025-11-12 02:21:13] 01:34:46<12:03:14, 15.88s/it | [Iter  358/3090] R0[267/3000]  | LR: 0.029417 | E: -62.353588 | E_img: -0.0174j E_var:     5.8908 E_err:   0.026816 | Acc: 0.2242
[2025-11-12 02:21:29] 01:35:02<12:02:58, 15.88s/it | [Iter  359/3090] R0[268/3000]  | LR: 0.029413 | E: -62.296355 | E_img: +0.0118j E_var:     7.0783 E_err:   0.029395 | Acc: 0.2299
[2025-11-12 02:21:45] 01:35:18<12:02:42, 15.88s/it | [Iter  360/3090] R0[269/3000]  | LR: 0.029409 | E: -62.336650 | E_img: -0.0205j E_var:     6.5359 E_err:   0.028246 | Acc: 0.2179
[2025-11-12 02:22:01] 01:35:34<12:02:26, 15.88s/it | [Iter  361/3090] R0[270/3000]  | LR: 0.029404 | E: -62.362739 | E_img: +0.0226j E_var:     5.6506 E_err:   0.026263 | Acc: 0.2409
[2025-11-12 02:22:17] 01:35:50<12:02:09, 15.88s/it | [Iter  362/3090] R0[271/3000]  | LR: 0.029400 | E: -62.300091 | E_img: -0.0076j E_var:     6.1565 E_err:   0.027414 | Acc: 0.2394
[2025-11-12 02:22:32] 01:36:06<12:01:53, 15.88s/it | [Iter  363/3090] R0[272/3000]  | LR: 0.029396 | E: -62.268314 | E_img: -0.0013j E_var:     5.9037 E_err:   0.026845 | Acc: 0.2584
[2025-11-12 02:22:48] 01:36:21<12:01:37, 15.88s/it | [Iter  364/3090] R0[273/3000]  | LR: 0.029391 | E: -62.257801 | E_img: +0.0027j E_var:     5.9525 E_err:   0.026956 | Acc: 0.2673
[2025-11-12 02:23:04] 01:36:37<12:01:21, 15.88s/it | [Iter  365/3090] R0[274/3000]  | LR: 0.029387 | E: -62.206967 | E_img: -0.0113j E_var:     5.9772 E_err:   0.027012 | Acc: 0.2746
[2025-11-12 02:23:20] 01:36:53<12:01:05, 15.88s/it | [Iter  366/3090] R0[275/3000]  | LR: 0.029382 | E: -62.294983 | E_img: -0.0023j E_var:     6.0544 E_err:   0.027186 | Acc: 0.2656
[2025-11-12 02:23:36] 01:37:09<12:00:48, 15.88s/it | [Iter  367/3090] R0[276/3000]  | LR: 0.029378 | E: -62.337263 | E_img: +0.0065j E_var:     5.3947 E_err:   0.025662 | Acc: 0.2624
[2025-11-12 02:23:52] 01:37:25<12:00:32, 15.88s/it | [Iter  368/3090] R0[277/3000]  | LR: 0.029373 | E: -62.387446 | E_img: -0.0158j E_var:     5.6538 E_err:   0.026271 | Acc: 0.2528
[2025-11-12 02:24:07] 01:37:41<12:00:16, 15.88s/it | [Iter  369/3090] R0[278/3000]  | LR: 0.029369 | E: -62.407383 | E_img: -0.0232j E_var:     5.8215 E_err:   0.026658 | Acc: 0.2411
[2025-11-12 02:24:23] 01:37:56<11:59:59, 15.88s/it | [Iter  370/3090] R0[279/3000]  | LR: 0.029364 | E: -62.455847 | E_img: +0.0418j E_var:     5.9580 E_err:   0.026968 | Acc: 0.2247
[2025-11-12 02:24:39] 01:38:12<11:59:43, 15.88s/it | [Iter  371/3090] R0[280/3000]  | LR: 0.029360 | E: -62.386065 | E_img: -0.0178j E_var:     5.6771 E_err:   0.026325 | Acc: 0.2237
[2025-11-12 02:24:55] 01:38:28<11:59:27, 15.88s/it | [Iter  372/3090] R0[281/3000]  | LR: 0.029355 | E: -62.438293 | E_img: +0.0065j E_var:     6.3962 E_err:   0.027943 | Acc: 0.2407
[2025-11-12 02:25:11] 01:38:44<11:59:11, 15.88s/it | [Iter  373/3090] R0[282/3000]  | LR: 0.029351 | E: -62.346995 | E_img: -0.0119j E_var:     5.5044 E_err:   0.025922 | Acc: 0.2512
[2025-11-12 02:25:27] 01:39:00<11:58:54, 15.88s/it | [Iter  374/3090] R0[283/3000]  | LR: 0.029346 | E: -62.435266 | E_img: +0.0068j E_var:     5.9013 E_err:   0.026840 | Acc: 0.2436
[2025-11-12 02:25:42] 01:39:16<11:58:38, 15.88s/it | [Iter  375/3090] R0[284/3000]  | LR: 0.029342 | E: -62.388231 | E_img: -0.0031j E_var:     5.3865 E_err:   0.025642 | Acc: 0.2413
[2025-11-12 02:25:58] 01:39:31<11:58:22, 15.88s/it | [Iter  376/3090] R0[285/3000]  | LR: 0.029337 | E: -62.402455 | E_img: +0.0066j E_var:     5.3122 E_err:   0.025465 | Acc: 0.2525
[2025-11-12 02:26:14] 01:39:47<11:58:06, 15.88s/it | [Iter  377/3090] R0[286/3000]  | LR: 0.029332 | E: -62.275371 | E_img: -0.0183j E_var:     5.9365 E_err:   0.026920 | Acc: 0.2487
[2025-11-12 02:26:30] 01:40:03<11:57:50, 15.88s/it | [Iter  378/3090] R0[287/3000]  | LR: 0.029328 | E: -62.116626 | E_img: -0.0292j E_var:     5.7208 E_err:   0.026426 | Acc: 0.2617
[2025-11-12 02:26:46] 01:40:19<11:57:34, 15.88s/it | [Iter  379/3090] R0[288/3000]  | LR: 0.029323 | E: -62.268391 | E_img: -0.0011j E_var:     5.6325 E_err:   0.026221 | Acc: 0.2620
[2025-11-12 02:27:02] 01:40:35<11:57:17, 15.88s/it | [Iter  380/3090] R0[289/3000]  | LR: 0.029318 | E: -62.286763 | E_img: +0.0120j E_var:     5.9466 E_err:   0.026943 | Acc: 0.2480
[2025-11-12 02:27:17] 01:40:51<11:57:01, 15.88s/it | [Iter  381/3090] R0[290/3000]  | LR: 0.029314 | E: -61.996819 | E_img: -0.0314j E_var:     5.5179 E_err:   0.025953 | Acc: 0.2541
[2025-11-12 02:27:33] 01:41:06<11:56:45, 15.88s/it | [Iter  382/3090] R0[291/3000]  | LR: 0.029309 | E: -62.170166 | E_img: -0.0289j E_var:     5.5625 E_err:   0.026058 | Acc: 0.2564
[2025-11-12 02:27:49] 01:41:22<11:56:29, 15.88s/it | [Iter  383/3090] R0[292/3000]  | LR: 0.029304 | E: -62.154088 | E_img: +0.0630j E_var:     6.2127 E_err:   0.027539 | Acc: 0.2425
[2025-11-12 02:28:05] 01:41:38<11:56:13, 15.88s/it | [Iter  384/3090] R0[293/3000]  | LR: 0.029299 | E: -62.072097 | E_img: +0.0652j E_var:     5.7724 E_err:   0.026545 | Acc: 0.2459
[2025-11-12 02:28:21] 01:41:54<11:55:56, 15.88s/it | [Iter  385/3090] R0[294/3000]  | LR: 0.029295 | E: -61.845877 | E_img: -0.0398j E_var:     5.9362 E_err:   0.026919 | Acc: 0.2535
[2025-11-12 02:28:37] 01:42:10<11:55:40, 15.88s/it | [Iter  386/3090] R0[295/3000]  | LR: 0.029290 | E: -61.984106 | E_img: +0.0475j E_var:     5.5928 E_err:   0.026129 | Acc: 0.2542
[2025-11-12 02:28:52] 01:42:26<11:55:24, 15.88s/it | [Iter  387/3090] R0[296/3000]  | LR: 0.029285 | E: -62.177334 | E_img: +0.0059j E_var:     5.7372 E_err:   0.026464 | Acc: 0.2425
[2025-11-12 02:29:08] 01:42:41<11:55:08, 15.88s/it | [Iter  388/3090] R0[297/3000]  | LR: 0.029280 | E: -62.115039 | E_img: -0.0515j E_var:     5.7804 E_err:   0.026563 | Acc: 0.2520
[2025-11-12 02:29:24] 01:42:57<11:54:51, 15.88s/it | [Iter  389/3090] R0[298/3000]  | LR: 0.029276 | E: -62.143101 | E_img: -0.0073j E_var:     5.7817 E_err:   0.026566 | Acc: 0.2455
[2025-11-12 02:29:40] 01:43:13<11:54:35, 15.88s/it | [Iter  390/3090] R0[299/3000]  | LR: 0.029271 | E: -62.230275 | E_img: -0.0119j E_var:     5.6462 E_err:   0.026253 | Acc: 0.2456
[2025-11-12 02:29:40] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-11-12 02:29:56] 01:43:29<11:54:20, 15.88s/it | [Iter  391/3090] R0[300/3000]  | LR: 0.029266 | E: -62.316933 | E_img: -0.0157j E_var:     5.6661 E_err:   0.026299 | Acc: 0.2475
[2025-11-12 02:30:12] 01:43:45<11:54:04, 15.88s/it | [Iter  392/3090] R0[301/3000]  | LR: 0.029261 | E: -62.373040 | E_img: +0.0127j E_var:     5.5103 E_err:   0.025935 | Acc: 0.2361
[2025-11-12 02:30:28] 01:44:01<11:53:48, 15.88s/it | [Iter  393/3090] R0[302/3000]  | LR: 0.029256 | E: -62.287186 | E_img: -0.0312j E_var:     5.1701 E_err:   0.025122 | Acc: 0.2482
[2025-11-12 02:30:43] 01:44:17<11:53:32, 15.88s/it | [Iter  394/3090] R0[303/3000]  | LR: 0.029251 | E: -62.372980 | E_img: -0.0083j E_var:     5.8072 E_err:   0.026625 | Acc: 0.2481
[2025-11-12 02:30:59] 01:44:32<11:53:15, 15.88s/it | [Iter  395/3090] R0[304/3000]  | LR: 0.029246 | E: -62.374702 | E_img: +0.0034j E_var:     5.4990 E_err:   0.025909 | Acc: 0.2556
[2025-11-12 02:31:15] 01:44:48<11:52:59, 15.88s/it | [Iter  396/3090] R0[305/3000]  | LR: 0.029241 | E: -62.422140 | E_img: +0.0479j E_var:     5.3161 E_err:   0.025474 | Acc: 0.2618
[2025-11-12 02:31:31] 01:45:04<11:52:43, 15.88s/it | [Iter  397/3090] R0[306/3000]  | LR: 0.029236 | E: -62.369333 | E_img: +0.0325j E_var:     5.2805 E_err:   0.025389 | Acc: 0.2662
[2025-11-12 02:31:47] 01:45:20<11:52:27, 15.88s/it | [Iter  398/3090] R0[307/3000]  | LR: 0.029231 | E: -62.451759 | E_img: +0.0046j E_var:     5.0232 E_err:   0.024763 | Acc: 0.2653
[2025-11-12 02:32:03] 01:45:36<11:52:11, 15.88s/it | [Iter  399/3090] R0[308/3000]  | LR: 0.029227 | E: -62.336171 | E_img: -0.0212j E_var:     5.1893 E_err:   0.025169 | Acc: 0.2714
[2025-11-12 02:32:18] 01:45:52<11:51:55, 15.88s/it | [Iter  400/3090] R0[309/3000]  | LR: 0.029222 | E: -62.295283 | E_img: +0.0044j E_var:     5.5000 E_err:   0.025911 | Acc: 0.2708
[2025-11-12 02:32:34] 01:46:07<11:51:38, 15.88s/it | [Iter  401/3090] R0[310/3000]  | LR: 0.029217 | E: -62.244756 | E_img: -0.0028j E_var:     5.3728 E_err:   0.025610 | Acc: 0.2703
[2025-11-12 02:32:50] 01:46:23<11:51:22, 15.88s/it | [Iter  402/3090] R0[311/3000]  | LR: 0.029212 | E: -62.179450 | E_img: +0.0520j E_var:     5.6821 E_err:   0.026337 | Acc: 0.2892
[2025-11-12 02:33:06] 01:46:39<11:51:06, 15.88s/it | [Iter  403/3090] R0[312/3000]  | LR: 0.029206 | E: -62.243034 | E_img: +0.0007j E_var:     5.4314 E_err:   0.025749 | Acc: 0.2865
[2025-11-12 02:33:22] 01:46:55<11:50:50, 15.88s/it | [Iter  404/3090] R0[313/3000]  | LR: 0.029201 | E: -62.118015 | E_img: -0.0314j E_var:     5.3475 E_err:   0.025549 | Acc: 0.2878
[2025-11-12 02:33:38] 01:47:11<11:50:34, 15.88s/it | [Iter  405/3090] R0[314/3000]  | LR: 0.029196 | E: -62.106799 | E_img: -0.0062j E_var:     5.7873 E_err:   0.026579 | Acc: 0.2869
[2025-11-12 02:33:54] 01:47:27<11:50:18, 15.88s/it | [Iter  406/3090] R0[315/3000]  | LR: 0.029191 | E: -62.029376 | E_img: +0.0201j E_var:     5.3652 E_err:   0.025592 | Acc: 0.2903
[2025-11-12 02:34:09] 01:47:42<11:50:01, 15.88s/it | [Iter  407/3090] R0[316/3000]  | LR: 0.029186 | E: -62.278599 | E_img: -0.0069j E_var:     5.3116 E_err:   0.025464 | Acc: 0.2858
[2025-11-12 02:34:25] 01:47:58<11:49:45, 15.88s/it | [Iter  408/3090] R0[317/3000]  | LR: 0.029181 | E: -62.311635 | E_img: +0.0056j E_var:     5.6071 E_err:   0.026162 | Acc: 0.2771
[2025-11-12 02:34:41] 01:48:14<11:49:29, 15.88s/it | [Iter  409/3090] R0[318/3000]  | LR: 0.029176 | E: -62.293830 | E_img: -0.0228j E_var:     5.2484 E_err:   0.025312 | Acc: 0.2693
[2025-11-12 02:34:57] 01:48:30<11:49:13, 15.88s/it | [Iter  410/3090] R0[319/3000]  | LR: 0.029171 | E: -62.397997 | E_img: -0.0057j E_var:     5.2692 E_err:   0.025362 | Acc: 0.2599
[2025-11-12 02:35:13] 01:48:46<11:48:57, 15.88s/it | [Iter  411/3090] R0[320/3000]  | LR: 0.029166 | E: -62.510233 | E_img: -0.0150j E_var:     5.8590 E_err:   0.026743 | Acc: 0.2404
[2025-11-12 02:35:29] 01:49:02<11:48:41, 15.88s/it | [Iter  412/3090] R0[321/3000]  | LR: 0.029160 | E: -62.531911 | E_img: -0.0177j E_var:     5.2014 E_err:   0.025198 | Acc: 0.2368
[2025-11-12 02:35:44] 01:49:17<11:48:24, 15.88s/it | [Iter  413/3090] R0[322/3000]  | LR: 0.029155 | E: -62.582759 | E_img: -0.0038j E_var:     5.2626 E_err:   0.025346 | Acc: 0.2405
[2025-11-12 02:36:00] 01:49:33<11:48:08, 15.88s/it | [Iter  414/3090] R0[323/3000]  | LR: 0.029150 | E: -62.577550 | E_img: -0.0181j E_var:     5.3101 E_err:   0.025460 | Acc: 0.2384
[2025-11-12 02:36:16] 01:49:49<11:47:52, 15.88s/it | [Iter  415/3090] R0[324/3000]  | LR: 0.029145 | E: -62.622788 | E_img: -0.0109j E_var:     5.0656 E_err:   0.024867 | Acc: 0.2512
[2025-11-12 02:36:32] 01:50:05<11:47:36, 15.88s/it | [Iter  416/3090] R0[325/3000]  | LR: 0.029140 | E: -62.616481 | E_img: -0.0450j E_var:     5.3300 E_err:   0.025508 | Acc: 0.2616
[2025-11-12 02:36:48] 01:50:21<11:47:20, 15.88s/it | [Iter  417/3090] R0[326/3000]  | LR: 0.029134 | E: -62.594759 | E_img: +0.0148j E_var:     5.2189 E_err:   0.025240 | Acc: 0.2542
[2025-11-12 02:37:04] 01:50:37<11:47:04, 15.88s/it | [Iter  418/3090] R0[327/3000]  | LR: 0.029129 | E: -62.625094 | E_img: +0.0070j E_var:     5.5779 E_err:   0.026094 | Acc: 0.2462
[2025-11-12 02:37:19] 01:50:53<11:46:48, 15.88s/it | [Iter  419/3090] R0[328/3000]  | LR: 0.029124 | E: -62.565870 | E_img: +0.0071j E_var:     5.4493 E_err:   0.025791 | Acc: 0.2395
[2025-11-12 02:37:35] 01:51:08<11:46:32, 15.88s/it | [Iter  420/3090] R0[329/3000]  | LR: 0.029119 | E: -62.595626 | E_img: -0.0285j E_var:     5.1339 E_err:   0.025034 | Acc: 0.2440
[2025-11-12 02:37:51] 01:51:24<11:46:16, 15.88s/it | [Iter  421/3090] R0[330/3000]  | LR: 0.029113 | E: -62.587041 | E_img: -0.0017j E_var:     5.1120 E_err:   0.024981 | Acc: 0.2495
[2025-11-12 02:38:07] 01:51:40<11:45:59, 15.88s/it | [Iter  422/3090] R0[331/3000]  | LR: 0.029108 | E: -62.488589 | E_img: -0.0181j E_var:     5.5290 E_err:   0.025979 | Acc: 0.2493
[2025-11-12 02:38:23] 01:51:56<11:45:43, 15.88s/it | [Iter  423/3090] R0[332/3000]  | LR: 0.029103 | E: -62.545318 | E_img: +0.0228j E_var:     5.5809 E_err:   0.026101 | Acc: 0.2403
[2025-11-12 02:38:39] 01:52:12<11:45:27, 15.88s/it | [Iter  424/3090] R0[333/3000]  | LR: 0.029097 | E: -62.554403 | E_img: +0.0267j E_var:     5.5240 E_err:   0.025968 | Acc: 0.2346
[2025-11-12 02:38:54] 01:52:28<11:45:11, 15.88s/it | [Iter  425/3090] R0[334/3000]  | LR: 0.029092 | E: -62.562654 | E_img: +0.0212j E_var:     5.5313 E_err:   0.025985 | Acc: 0.2348
[2025-11-12 02:39:10] 01:52:43<11:44:55, 15.88s/it | [Iter  426/3090] R0[335/3000]  | LR: 0.029086 | E: -62.536813 | E_img: +0.0089j E_var:     5.5723 E_err:   0.026081 | Acc: 0.2298
[2025-11-12 02:39:26] 01:52:59<11:44:39, 15.88s/it | [Iter  427/3090] R0[336/3000]  | LR: 0.029081 | E: -62.672702 | E_img: -0.0094j E_var:     4.8833 E_err:   0.024415 | Acc: 0.2419
[2025-11-12 02:39:42] 01:53:15<11:44:23, 15.88s/it | [Iter  428/3090] R0[337/3000]  | LR: 0.029076 | E: -62.609873 | E_img: +0.0295j E_var:     5.0131 E_err:   0.024738 | Acc: 0.2494
[2025-11-12 02:39:58] 01:53:31<11:44:07, 15.88s/it | [Iter  429/3090] R0[338/3000]  | LR: 0.029070 | E: -62.525599 | E_img: +0.0113j E_var:     4.9277 E_err:   0.024526 | Acc: 0.2677
[2025-11-12 02:40:14] 01:53:47<11:43:51, 15.88s/it | [Iter  430/3090] R0[339/3000]  | LR: 0.029065 | E: -62.528167 | E_img: +0.0009j E_var:     5.2323 E_err:   0.025273 | Acc: 0.2734
[2025-11-12 02:40:29] 01:54:03<11:43:34, 15.88s/it | [Iter  431/3090] R0[340/3000]  | LR: 0.029059 | E: -62.649244 | E_img: +0.0152j E_var:     5.2380 E_err:   0.025287 | Acc: 0.2632
[2025-11-12 02:40:45] 01:54:18<11:43:18, 15.88s/it | [Iter  432/3090] R0[341/3000]  | LR: 0.029054 | E: -62.620704 | E_img: +0.0170j E_var:     4.8931 E_err:   0.024440 | Acc: 0.2688
[2025-11-12 02:41:01] 01:54:34<11:43:02, 15.88s/it | [Iter  433/3090] R0[342/3000]  | LR: 0.029048 | E: -62.604173 | E_img: +0.0313j E_var:     5.4068 E_err:   0.025691 | Acc: 0.2828
[2025-11-12 02:41:17] 01:54:50<11:42:46, 15.88s/it | [Iter  434/3090] R0[343/3000]  | LR: 0.029043 | E: -62.607236 | E_img: -0.0202j E_var:     5.1626 E_err:   0.025104 | Acc: 0.2787
[2025-11-12 02:41:33] 01:55:06<11:42:30, 15.88s/it | [Iter  435/3090] R0[344/3000]  | LR: 0.029037 | E: -62.679531 | E_img: +0.0093j E_var:     5.1718 E_err:   0.025126 | Acc: 0.2670
[2025-11-12 02:41:49] 01:55:22<11:42:14, 15.88s/it | [Iter  436/3090] R0[345/3000]  | LR: 0.029032 | E: -62.641787 | E_img: +0.0180j E_var:     4.9131 E_err:   0.024490 | Acc: 0.2672
[2025-11-12 02:42:04] 01:55:38<11:41:58, 15.88s/it | [Iter  437/3090] R0[346/3000]  | LR: 0.029026 | E: -62.550786 | E_img: -0.0189j E_var:     5.0740 E_err:   0.024888 | Acc: 0.2826
[2025-11-12 02:42:20] 01:55:53<11:41:42, 15.88s/it | [Iter  438/3090] R0[347/3000]  | LR: 0.029021 | E: -62.590766 | E_img: -0.0207j E_var:     5.3210 E_err:   0.025486 | Acc: 0.2773
[2025-11-12 02:42:36] 01:56:09<11:41:26, 15.88s/it | [Iter  439/3090] R0[348/3000]  | LR: 0.029015 | E: -62.661368 | E_img: +0.0174j E_var:     4.8727 E_err:   0.024389 | Acc: 0.2771
[2025-11-12 02:42:52] 01:56:25<11:41:09, 15.88s/it | [Iter  440/3090] R0[349/3000]  | LR: 0.029009 | E: -62.646888 | E_img: -0.0020j E_var:     5.1124 E_err:   0.024981 | Acc: 0.2657
[2025-11-12 02:43:08] 01:56:41<11:40:53, 15.88s/it | [Iter  441/3090] R0[350/3000]  | LR: 0.029004 | E: -62.612268 | E_img: -0.0147j E_var:     5.0534 E_err:   0.024837 | Acc: 0.2630
[2025-11-12 02:43:24] 01:56:57<11:40:37, 15.88s/it | [Iter  442/3090] R0[351/3000]  | LR: 0.028998 | E: -62.681924 | E_img: -0.0181j E_var:     4.7021 E_err:   0.023958 | Acc: 0.2693
[2025-11-12 02:43:40] 01:57:13<11:40:21, 15.88s/it | [Iter  443/3090] R0[352/3000]  | LR: 0.028992 | E: -62.685786 | E_img: -0.0162j E_var:     4.7515 E_err:   0.024084 | Acc: 0.2704
[2025-11-12 02:43:55] 01:57:28<11:40:05, 15.87s/it | [Iter  444/3090] R0[353/3000]  | LR: 0.028987 | E: -62.663095 | E_img: -0.0301j E_var:     4.8471 E_err:   0.024325 | Acc: 0.2739
[2025-11-12 02:44:11] 01:57:44<11:39:49, 15.87s/it | [Iter  445/3090] R0[354/3000]  | LR: 0.028981 | E: -62.693055 | E_img: -0.0276j E_var:     4.9791 E_err:   0.024654 | Acc: 0.2772
[2025-11-12 02:44:27] 01:58:00<11:39:33, 15.87s/it | [Iter  446/3090] R0[355/3000]  | LR: 0.028975 | E: -62.659007 | E_img: -0.0306j E_var:     4.9365 E_err:   0.024548 | Acc: 0.2769
[2025-11-12 02:44:43] 01:58:16<11:39:17, 15.87s/it | [Iter  447/3090] R0[356/3000]  | LR: 0.028970 | E: -62.566774 | E_img: +0.0210j E_var:     4.8655 E_err:   0.024371 | Acc: 0.2830
[2025-11-12 02:44:59] 01:58:32<11:39:01, 15.87s/it | [Iter  448/3090] R0[357/3000]  | LR: 0.028964 | E: -62.671407 | E_img: -0.0003j E_var:     5.0339 E_err:   0.024789 | Acc: 0.2721
[2025-11-12 02:45:15] 01:58:48<11:38:45, 15.87s/it | [Iter  449/3090] R0[358/3000]  | LR: 0.028958 | E: -62.701176 | E_img: +0.0356j E_var:     5.0321 E_err:   0.024785 | Acc: 0.2668
[2025-11-12 02:45:30] 01:59:04<11:38:29, 15.87s/it | [Iter  450/3090] R0[359/3000]  | LR: 0.028952 | E: -62.716592 | E_img: -0.0109j E_var:     4.9572 E_err:   0.024599 | Acc: 0.2617
[2025-11-12 02:45:46] 01:59:19<11:38:13, 15.87s/it | [Iter  451/3090] R0[360/3000]  | LR: 0.028947 | E: -62.745755 | E_img: -0.0263j E_var:     4.7611 E_err:   0.024108 | Acc: 0.2623
[2025-11-12 02:46:02] 01:59:35<11:37:57, 15.87s/it | [Iter  452/3090] R0[361/3000]  | LR: 0.028941 | E: -62.780289 | E_img: -0.0215j E_var:     5.2406 E_err:   0.025293 | Acc: 0.2579
[2025-11-12 02:46:18] 01:59:51<11:37:41, 15.87s/it | [Iter  453/3090] R0[362/3000]  | LR: 0.028935 | E: -62.745062 | E_img: +0.0132j E_var:     5.4344 E_err:   0.025756 | Acc: 0.2431
[2025-11-12 02:46:34] 02:00:07<11:37:24, 15.87s/it | [Iter  454/3090] R0[363/3000]  | LR: 0.028929 | E: -62.700273 | E_img: +0.0167j E_var:     5.2530 E_err:   0.025323 | Acc: 0.2434
[2025-11-12 02:46:50] 02:00:23<11:37:08, 15.87s/it | [Iter  455/3090] R0[364/3000]  | LR: 0.028923 | E: -62.611214 | E_img: -0.0076j E_var:     4.8927 E_err:   0.024439 | Acc: 0.2545
[2025-11-12 02:47:05] 02:00:39<11:36:52, 15.87s/it | [Iter  456/3090] R0[365/3000]  | LR: 0.028918 | E: -62.552807 | E_img: -0.0247j E_var:     4.9325 E_err:   0.024538 | Acc: 0.2671
[2025-11-12 02:47:21] 02:00:54<11:36:36, 15.87s/it | [Iter  457/3090] R0[366/3000]  | LR: 0.028912 | E: -62.401427 | E_img: -0.0671j E_var:     5.2000 E_err:   0.025194 | Acc: 0.2796
[2025-11-12 02:47:37] 02:01:10<11:36:20, 15.87s/it | [Iter  458/3090] R0[367/3000]  | LR: 0.028906 | E: -62.437529 | E_img: -0.0373j E_var:     4.9752 E_err:   0.024644 | Acc: 0.2806
[2025-11-12 02:47:53] 02:01:26<11:36:04, 15.87s/it | [Iter  459/3090] R0[368/3000]  | LR: 0.028900 | E: -62.595626 | E_img: -0.0225j E_var:     5.0116 E_err:   0.024734 | Acc: 0.2754
[2025-11-12 02:48:09] 02:01:42<11:35:48, 15.87s/it | [Iter  460/3090] R0[369/3000]  | LR: 0.028894 | E: -62.528240 | E_img: -0.0006j E_var:     4.9834 E_err:   0.024664 | Acc: 0.2693
[2025-11-12 02:48:25] 02:01:58<11:35:32, 15.87s/it | [Iter  461/3090] R0[370/3000]  | LR: 0.028888 | E: -62.465810 | E_img: +0.0043j E_var:     4.8997 E_err:   0.024456 | Acc: 0.2775
[2025-11-12 02:48:41] 02:02:14<11:35:16, 15.87s/it | [Iter  462/3090] R0[371/3000]  | LR: 0.028882 | E: -62.466852 | E_img: -0.0027j E_var:     5.2424 E_err:   0.025297 | Acc: 0.2919
[2025-11-12 02:48:56] 02:02:29<11:35:00, 15.87s/it | [Iter  463/3090] R0[372/3000]  | LR: 0.028876 | E: -62.564069 | E_img: -0.0209j E_var:     5.1051 E_err:   0.024964 | Acc: 0.2820
[2025-11-12 02:49:12] 02:02:45<11:34:44, 15.87s/it | [Iter  464/3090] R0[373/3000]  | LR: 0.028870 | E: -62.528519 | E_img: -0.0397j E_var:     5.2102 E_err:   0.025219 | Acc: 0.2885
[2025-11-12 02:49:28] 02:03:01<11:34:28, 15.87s/it | [Iter  465/3090] R0[374/3000]  | LR: 0.028864 | E: -62.712625 | E_img: +0.0371j E_var:     4.9211 E_err:   0.024510 | Acc: 0.2780
[2025-11-12 02:49:44] 02:03:17<11:34:12, 15.87s/it | [Iter  466/3090] R0[375/3000]  | LR: 0.028858 | E: -62.674439 | E_img: +0.0121j E_var:     5.4903 E_err:   0.025888 | Acc: 0.2548
[2025-11-12 02:50:00] 02:03:33<11:33:55, 15.87s/it | [Iter  467/3090] R0[376/3000]  | LR: 0.028852 | E: -62.708782 | E_img: +0.0078j E_var:     4.5554 E_err:   0.023581 | Acc: 0.2558
[2025-11-12 02:50:16] 02:03:49<11:33:39, 15.87s/it | [Iter  468/3090] R0[377/3000]  | LR: 0.028846 | E: -62.668401 | E_img: -0.0215j E_var:     4.6618 E_err:   0.023855 | Acc: 0.2642
[2025-11-12 02:50:31] 02:04:04<11:33:23, 15.87s/it | [Iter  469/3090] R0[378/3000]  | LR: 0.028840 | E: -62.726216 | E_img: +0.0102j E_var:     4.7211 E_err:   0.024006 | Acc: 0.2827
[2025-11-12 02:50:47] 02:04:20<11:33:07, 15.87s/it | [Iter  470/3090] R0[379/3000]  | LR: 0.028834 | E: -62.819913 | E_img: -0.0173j E_var:     4.9206 E_err:   0.024508 | Acc: 0.2795
[2025-11-12 02:51:03] 02:04:36<11:32:51, 15.87s/it | [Iter  471/3090] R0[380/3000]  | LR: 0.028828 | E: -62.842592 | E_img: -0.0229j E_var:     4.8418 E_err:   0.024311 | Acc: 0.2687
[2025-11-12 02:51:19] 02:04:52<11:32:35, 15.87s/it | [Iter  472/3090] R0[381/3000]  | LR: 0.028822 | E: -62.818382 | E_img: -0.0266j E_var:     4.5550 E_err:   0.023580 | Acc: 0.2730
[2025-11-12 02:51:35] 02:05:08<11:32:19, 15.87s/it | [Iter  473/3090] R0[382/3000]  | LR: 0.028816 | E: -62.795908 | E_img: -0.0095j E_var:     4.7035 E_err:   0.023962 | Acc: 0.2755
[2025-11-12 02:51:51] 02:05:24<11:32:03, 15.87s/it | [Iter  474/3090] R0[383/3000]  | LR: 0.028810 | E: -62.831390 | E_img: -0.0050j E_var:     4.7257 E_err:   0.024018 | Acc: 0.2762
[2025-11-12 02:52:06] 02:05:40<11:31:47, 15.87s/it | [Iter  475/3090] R0[384/3000]  | LR: 0.028803 | E: -62.873175 | E_img: -0.0024j E_var:     4.8344 E_err:   0.024293 | Acc: 0.2754
[2025-11-12 02:52:22] 02:05:55<11:31:31, 15.87s/it | [Iter  476/3090] R0[385/3000]  | LR: 0.028797 | E: -62.872742 | E_img: +0.0010j E_var:     4.6657 E_err:   0.023865 | Acc: 0.2770
[2025-11-12 02:52:38] 02:06:11<11:31:15, 15.87s/it | [Iter  477/3090] R0[386/3000]  | LR: 0.028791 | E: -62.911255 | E_img: +0.0266j E_var:     4.8550 E_err:   0.024345 | Acc: 0.2670
[2025-11-12 02:52:54] 02:06:27<11:30:59, 15.87s/it | [Iter  478/3090] R0[387/3000]  | LR: 0.028785 | E: -62.860885 | E_img: +0.0169j E_var:     5.2122 E_err:   0.025224 | Acc: 0.2488
[2025-11-12 02:53:10] 02:06:43<11:30:43, 15.87s/it | [Iter  479/3090] R0[388/3000]  | LR: 0.028779 | E: -62.910509 | E_img: +0.0211j E_var:     4.4505 E_err:   0.023308 | Acc: 0.2561
[2025-11-12 02:53:26] 02:06:59<11:30:27, 15.87s/it | [Iter  480/3090] R0[389/3000]  | LR: 0.028773 | E: -62.985482 | E_img: +0.0092j E_var:     4.5967 E_err:   0.023688 | Acc: 0.2564
[2025-11-12 02:53:41] 02:07:15<11:30:11, 15.87s/it | [Iter  481/3090] R0[390/3000]  | LR: 0.028766 | E: -62.957061 | E_img: -0.0053j E_var:     4.5247 E_err:   0.023502 | Acc: 0.2634
[2025-11-12 02:53:57] 02:07:30<11:29:55, 15.87s/it | [Iter  482/3090] R0[391/3000]  | LR: 0.028760 | E: -62.927333 | E_img: +0.0251j E_var:     4.7000 E_err:   0.023953 | Acc: 0.2593
[2025-11-12 02:54:13] 02:07:46<11:29:39, 15.87s/it | [Iter  483/3090] R0[392/3000]  | LR: 0.028754 | E: -62.937177 | E_img: -0.0223j E_var:     5.1718 E_err:   0.025126 | Acc: 0.2475
[2025-11-12 02:54:29] 02:08:02<11:29:23, 15.87s/it | [Iter  484/3090] R0[393/3000]  | LR: 0.028748 | E: -62.946911 | E_img: -0.0181j E_var:     4.4588 E_err:   0.023330 | Acc: 0.2575
[2025-11-12 02:54:45] 02:08:18<11:29:07, 15.87s/it | [Iter  485/3090] R0[394/3000]  | LR: 0.028741 | E: -62.920187 | E_img: -0.0142j E_var:     4.5864 E_err:   0.023661 | Acc: 0.2616
[2025-11-12 02:55:01] 02:08:34<11:28:50, 15.87s/it | [Iter  486/3090] R0[395/3000]  | LR: 0.028735 | E: -62.925112 | E_img: -0.0092j E_var:     4.7653 E_err:   0.024119 | Acc: 0.2563
[2025-11-12 02:55:16] 02:08:50<11:28:34, 15.87s/it | [Iter  487/3090] R0[396/3000]  | LR: 0.028729 | E: -62.917231 | E_img: -0.0019j E_var:     4.8889 E_err:   0.024429 | Acc: 0.2595
[2025-11-12 02:55:32] 02:09:05<11:28:18, 15.87s/it | [Iter  488/3090] R0[397/3000]  | LR: 0.028722 | E: -62.897588 | E_img: -0.0086j E_var:     5.0888 E_err:   0.024924 | Acc: 0.2512
[2025-11-12 02:55:48] 02:09:21<11:28:02, 15.87s/it | [Iter  489/3090] R0[398/3000]  | LR: 0.028716 | E: -63.006959 | E_img: +0.0162j E_var:     4.3979 E_err:   0.023170 | Acc: 0.2614
[2025-11-12 02:56:04] 02:09:37<11:27:46, 15.87s/it | [Iter  490/3090] R0[399/3000]  | LR: 0.028710 | E: -62.995304 | E_img: +0.0032j E_var:     4.7338 E_err:   0.024039 | Acc: 0.2643
[2025-11-12 02:56:20] 02:09:53<11:27:30, 15.87s/it | [Iter  491/3090] R0[400/3000]  | LR: 0.028703 | E: -62.997062 | E_img: +0.0036j E_var:     4.3020 E_err:   0.022916 | Acc: 0.2730
[2025-11-12 02:56:36] 02:10:09<11:27:14, 15.87s/it | [Iter  492/3090] R0[401/3000]  | LR: 0.028697 | E: -63.017910 | E_img: -0.0031j E_var:     4.5483 E_err:   0.023563 | Acc: 0.2693
[2025-11-12 02:56:51] 02:10:25<11:26:58, 15.87s/it | [Iter  493/3090] R0[402/3000]  | LR: 0.028690 | E: -63.034384 | E_img: -0.0091j E_var:     4.7709 E_err:   0.024133 | Acc: 0.2589
[2025-11-12 02:57:07] 02:10:40<11:26:42, 15.87s/it | [Iter  494/3090] R0[403/3000]  | LR: 0.028684 | E: -62.996002 | E_img: -0.0074j E_var:     4.3203 E_err:   0.022965 | Acc: 0.2750
[2025-11-12 02:57:23] 02:10:56<11:26:26, 15.87s/it | [Iter  495/3090] R0[404/3000]  | LR: 0.028678 | E: -63.007471 | E_img: -0.0165j E_var:     4.5439 E_err:   0.023552 | Acc: 0.2800
[2025-11-12 02:57:39] 02:11:12<11:26:10, 15.87s/it | [Iter  496/3090] R0[405/3000]  | LR: 0.028671 | E: -63.034290 | E_img: -0.0150j E_var:     4.4005 E_err:   0.023177 | Acc: 0.2861
[2025-11-12 02:57:55] 02:11:28<11:25:54, 15.87s/it | [Iter  497/3090] R0[406/3000]  | LR: 0.028665 | E: -62.976886 | E_img: +0.0146j E_var:     4.4253 E_err:   0.023242 | Acc: 0.2928
[2025-11-12 02:58:11] 02:11:44<11:25:38, 15.87s/it | [Iter  498/3090] R0[407/3000]  | LR: 0.028658 | E: -63.009174 | E_img: +0.0040j E_var:     4.7054 E_err:   0.023966 | Acc: 0.2869
[2025-11-12 02:58:26] 02:12:00<11:25:21, 15.87s/it | [Iter  499/3090] R0[408/3000]  | LR: 0.028652 | E: -63.020451 | E_img: -0.0142j E_var:     4.5379 E_err:   0.023536 | Acc: 0.2847
[2025-11-12 02:58:42] 02:12:15<11:25:05, 15.87s/it | [Iter  500/3090] R0[409/3000]  | LR: 0.028645 | E: -62.998898 | E_img: -0.0113j E_var:     4.7248 E_err:   0.024016 | Acc: 0.2850
[2025-11-12 02:58:58] 02:12:31<11:24:49, 15.87s/it | [Iter  501/3090] R0[410/3000]  | LR: 0.028639 | E: -63.006480 | E_img: +0.0052j E_var:     4.5231 E_err:   0.023498 | Acc: 0.3014
[2025-11-12 02:59:14] 02:12:47<11:24:33, 15.87s/it | [Iter  502/3090] R0[411/3000]  | LR: 0.028632 | E: -62.892502 | E_img: +0.0011j E_var:     4.8606 E_err:   0.024358 | Acc: 0.3002
[2025-11-12 02:59:30] 02:13:03<11:24:17, 15.87s/it | [Iter  503/3090] R0[412/3000]  | LR: 0.028625 | E: -62.992324 | E_img: +0.0045j E_var:     4.6369 E_err:   0.023791 | Acc: 0.2880
[2025-11-12 02:59:46] 02:13:19<11:24:01, 15.87s/it | [Iter  504/3090] R0[413/3000]  | LR: 0.028619 | E: -63.064882 | E_img: +0.0017j E_var:     4.5819 E_err:   0.023650 | Acc: 0.2810
[2025-11-12 03:00:01] 02:13:35<11:23:45, 15.87s/it | [Iter  505/3090] R0[414/3000]  | LR: 0.028612 | E: -62.965569 | E_img: -0.0064j E_var:     4.5923 E_err:   0.023677 | Acc: 0.2783
[2025-11-12 03:00:17] 02:13:50<11:23:29, 15.87s/it | [Iter  506/3090] R0[415/3000]  | LR: 0.028606 | E: -63.052709 | E_img: +0.0140j E_var:     4.5608 E_err:   0.023595 | Acc: 0.2728
[2025-11-12 03:00:33] 02:14:06<11:23:13, 15.87s/it | [Iter  507/3090] R0[416/3000]  | LR: 0.028599 | E: -63.043654 | E_img: -0.0024j E_var:     4.4732 E_err:   0.023368 | Acc: 0.2780
[2025-11-12 03:00:49] 02:14:22<11:22:57, 15.87s/it | [Iter  508/3090] R0[417/3000]  | LR: 0.028592 | E: -63.073470 | E_img: -0.0021j E_var:     4.4762 E_err:   0.023375 | Acc: 0.2730
[2025-11-12 03:01:05] 02:14:38<11:22:41, 15.87s/it | [Iter  509/3090] R0[418/3000]  | LR: 0.028586 | E: -63.047952 | E_img: +0.0063j E_var:     4.3430 E_err:   0.023025 | Acc: 0.2851
[2025-11-12 03:01:21] 02:14:54<11:22:25, 15.87s/it | [Iter  510/3090] R0[419/3000]  | LR: 0.028579 | E: -62.950333 | E_img: +0.0016j E_var:     4.5190 E_err:   0.023487 | Acc: 0.2975
[2025-11-12 03:01:36] 02:15:10<11:22:09, 15.87s/it | [Iter  511/3090] R0[420/3000]  | LR: 0.028572 | E: -63.082603 | E_img: -0.0009j E_var:     4.4561 E_err:   0.023323 | Acc: 0.2841
[2025-11-12 03:01:52] 02:15:25<11:21:53, 15.87s/it | [Iter  512/3090] R0[421/3000]  | LR: 0.028566 | E: -63.112767 | E_img: +0.0149j E_var:     4.4777 E_err:   0.023379 | Acc: 0.2798
[2025-11-12 03:02:08] 02:15:41<11:21:37, 15.87s/it | [Iter  513/3090] R0[422/3000]  | LR: 0.028559 | E: -63.052049 | E_img: +0.0062j E_var:     4.3393 E_err:   0.023015 | Acc: 0.2900
[2025-11-12 03:02:24] 02:15:57<11:21:21, 15.87s/it | [Iter  514/3090] R0[423/3000]  | LR: 0.028552 | E: -63.062706 | E_img: +0.0141j E_var:     4.4174 E_err:   0.023221 | Acc: 0.2931
[2025-11-12 03:02:40] 02:16:13<11:21:05, 15.87s/it | [Iter  515/3090] R0[424/3000]  | LR: 0.028546 | E: -63.085665 | E_img: +0.0029j E_var:     4.5285 E_err:   0.023511 | Acc: 0.2873
[2025-11-12 03:02:56] 02:16:29<11:20:49, 15.87s/it | [Iter  516/3090] R0[425/3000]  | LR: 0.028539 | E: -63.089724 | E_img: +0.0095j E_var:     4.3405 E_err:   0.023018 | Acc: 0.2893
[2025-11-12 03:03:12] 02:16:45<11:20:33, 15.87s/it | [Iter  517/3090] R0[426/3000]  | LR: 0.028532 | E: -63.079333 | E_img: -0.0134j E_var:     4.9181 E_err:   0.024502 | Acc: 0.2695
[2025-11-12 03:03:27] 02:17:00<11:20:17, 15.87s/it | [Iter  518/3090] R0[427/3000]  | LR: 0.028525 | E: -63.034992 | E_img: +0.0125j E_var:     4.2427 E_err:   0.022758 | Acc: 0.2856
[2025-11-12 03:03:43] 02:17:16<11:20:01, 15.87s/it | [Iter  519/3090] R0[428/3000]  | LR: 0.028518 | E: -63.018707 | E_img: -0.0156j E_var:     4.4632 E_err:   0.023342 | Acc: 0.2880
[2025-11-12 03:03:59] 02:17:32<11:19:45, 15.87s/it | [Iter  520/3090] R0[429/3000]  | LR: 0.028512 | E: -63.050761 | E_img: -0.0106j E_var:     4.5789 E_err:   0.023642 | Acc: 0.2793
[2025-11-12 03:04:15] 02:17:48<11:19:29, 15.87s/it | [Iter  521/3090] R0[430/3000]  | LR: 0.028505 | E: -63.035411 | E_img: -0.0170j E_var:     4.4582 E_err:   0.023328 | Acc: 0.2782
[2025-11-12 03:04:31] 02:18:04<11:19:13, 15.87s/it | [Iter  522/3090] R0[431/3000]  | LR: 0.028498 | E: -62.995161 | E_img: +0.0086j E_var:     4.4045 E_err:   0.023188 | Acc: 0.2981
[2025-11-12 03:04:47] 02:18:20<11:18:57, 15.87s/it | [Iter  523/3090] R0[432/3000]  | LR: 0.028491 | E: -63.046596 | E_img: +0.0130j E_var:     4.2583 E_err:   0.022799 | Acc: 0.3004
[2025-11-12 03:05:02] 02:18:36<11:18:41, 15.87s/it | [Iter  524/3090] R0[433/3000]  | LR: 0.028484 | E: -63.080941 | E_img: -0.0043j E_var:     4.3347 E_err:   0.023003 | Acc: 0.2940
[2025-11-12 03:05:18] 02:18:51<11:18:25, 15.87s/it | [Iter  525/3090] R0[434/3000]  | LR: 0.028477 | E: -63.098452 | E_img: +0.0132j E_var:     4.3620 E_err:   0.023075 | Acc: 0.2865
[2025-11-12 03:05:34] 02:19:07<11:18:09, 15.87s/it | [Iter  526/3090] R0[435/3000]  | LR: 0.028470 | E: -63.108256 | E_img: +0.0077j E_var:     4.2643 E_err:   0.022815 | Acc: 0.2864
[2025-11-12 03:05:50] 02:19:23<11:17:53, 15.87s/it | [Iter  527/3090] R0[436/3000]  | LR: 0.028464 | E: -63.087805 | E_img: +0.0172j E_var:     4.1394 E_err:   0.022479 | Acc: 0.2904
[2025-11-12 03:06:06] 02:19:39<11:17:37, 15.87s/it | [Iter  528/3090] R0[437/3000]  | LR: 0.028457 | E: -63.173842 | E_img: +0.0103j E_var:     4.5792 E_err:   0.023643 | Acc: 0.2829
[2025-11-12 03:06:22] 02:19:55<11:17:21, 15.87s/it | [Iter  529/3090] R0[438/3000]  | LR: 0.028450 | E: -63.137858 | E_img: -0.0140j E_var:     4.6483 E_err:   0.023820 | Acc: 0.2691
[2025-11-12 03:06:38] 02:20:11<11:17:05, 15.87s/it | [Iter  530/3090] R0[439/3000]  | LR: 0.028443 | E: -63.167632 | E_img: +0.0270j E_var:     4.2276 E_err:   0.022717 | Acc: 0.2722
[2025-11-12 03:06:53] 02:20:26<11:16:49, 15.87s/it | [Iter  531/3090] R0[440/3000]  | LR: 0.028436 | E: -63.150210 | E_img: -0.0205j E_var:     4.3019 E_err:   0.022916 | Acc: 0.2735
[2025-11-12 03:07:09] 02:20:42<11:16:33, 15.87s/it | [Iter  532/3090] R0[441/3000]  | LR: 0.028429 | E: -63.118148 | E_img: -0.0120j E_var:     4.5515 E_err:   0.023571 | Acc: 0.2683
[2025-11-12 03:07:25] 02:20:58<11:16:17, 15.87s/it | [Iter  533/3090] R0[442/3000]  | LR: 0.028422 | E: -63.053738 | E_img: +0.0017j E_var:     4.1676 E_err:   0.022555 | Acc: 0.2862
[2025-11-12 03:07:41] 02:21:14<11:16:01, 15.87s/it | [Iter  534/3090] R0[443/3000]  | LR: 0.028415 | E: -63.115225 | E_img: -0.0022j E_var:     4.3987 E_err:   0.023172 | Acc: 0.2861
[2025-11-12 03:07:57] 02:21:30<11:15:45, 15.87s/it | [Iter  535/3090] R0[444/3000]  | LR: 0.028408 | E: -63.143645 | E_img: +0.0046j E_var:     4.6661 E_err:   0.023866 | Acc: 0.2718
[2025-11-12 03:08:13] 02:21:46<11:15:29, 15.87s/it | [Iter  536/3090] R0[445/3000]  | LR: 0.028401 | E: -63.141904 | E_img: +0.0098j E_var:     4.3310 E_err:   0.022993 | Acc: 0.2746
[2025-11-12 03:08:28] 02:22:02<11:15:13, 15.87s/it | [Iter  537/3090] R0[446/3000]  | LR: 0.028394 | E: -63.185526 | E_img: +0.0196j E_var:     4.5838 E_err:   0.023655 | Acc: 0.2688
[2025-11-12 03:08:44] 02:22:17<11:14:57, 15.87s/it | [Iter  538/3090] R0[447/3000]  | LR: 0.028386 | E: -63.166644 | E_img: +0.0059j E_var:     4.4302 E_err:   0.023255 | Acc: 0.2623
[2025-11-12 03:09:00] 02:22:33<11:14:41, 15.87s/it | [Iter  539/3090] R0[448/3000]  | LR: 0.028379 | E: -63.133145 | E_img: -0.0029j E_var:     4.2157 E_err:   0.022685 | Acc: 0.2693
[2025-11-12 03:09:16] 02:22:49<11:14:25, 15.87s/it | [Iter  540/3090] R0[449/3000]  | LR: 0.028372 | E: -63.130219 | E_img: +0.0084j E_var:     4.6884 E_err:   0.023923 | Acc: 0.2592
[2025-11-12 03:09:32] 02:23:05<11:14:09, 15.87s/it | [Iter  541/3090] R0[450/3000]  | LR: 0.028365 | E: -63.086925 | E_img: +0.0147j E_var:     4.3880 E_err:   0.023144 | Acc: 0.2603
[2025-11-12 03:09:48] 02:23:21<11:13:53, 15.87s/it | [Iter  542/3090] R0[451/3000]  | LR: 0.028358 | E: -63.095820 | E_img: -0.0182j E_var:     4.2991 E_err:   0.022908 | Acc: 0.2633
[2025-11-12 03:10:04] 02:23:37<11:13:37, 15.87s/it | [Iter  543/3090] R0[452/3000]  | LR: 0.028351 | E: -63.135763 | E_img: -0.0046j E_var:     4.3501 E_err:   0.023044 | Acc: 0.2734
[2025-11-12 03:10:19] 02:23:52<11:13:21, 15.87s/it | [Iter  544/3090] R0[453/3000]  | LR: 0.028344 | E: -63.100158 | E_img: +0.0190j E_var:     4.4591 E_err:   0.023331 | Acc: 0.2744
[2025-11-12 03:10:35] 02:24:08<11:13:05, 15.87s/it | [Iter  545/3090] R0[454/3000]  | LR: 0.028336 | E: -63.042985 | E_img: +0.0168j E_var:     4.1415 E_err:   0.022484 | Acc: 0.2892
[2025-11-12 03:10:51] 02:24:24<11:12:49, 15.87s/it | [Iter  546/3090] R0[455/3000]  | LR: 0.028329 | E: -63.017865 | E_img: -0.0007j E_var:     4.3818 E_err:   0.023128 | Acc: 0.3068
[2025-11-12 03:11:07] 02:24:40<11:12:33, 15.87s/it | [Iter  547/3090] R0[456/3000]  | LR: 0.028322 | E: -63.112923 | E_img: +0.0242j E_var:     4.4871 E_err:   0.023404 | Acc: 0.2937
[2025-11-12 03:11:23] 02:24:56<11:12:17, 15.87s/it | [Iter  548/3090] R0[457/3000]  | LR: 0.028315 | E: -63.061560 | E_img: +0.0375j E_var:     4.2383 E_err:   0.022746 | Acc: 0.2960
[2025-11-12 03:11:39] 02:25:12<11:12:01, 15.87s/it | [Iter  549/3090] R0[458/3000]  | LR: 0.028308 | E: -63.115180 | E_img: +0.0190j E_var:     4.1529 E_err:   0.022516 | Acc: 0.2948
[2025-11-12 03:11:54] 02:25:28<11:11:45, 15.87s/it | [Iter  550/3090] R0[459/3000]  | LR: 0.028300 | E: -63.137607 | E_img: +0.0035j E_var:     4.5424 E_err:   0.023548 | Acc: 0.2817
[2025-11-12 03:12:10] 02:25:43<11:11:29, 15.87s/it | [Iter  551/3090] R0[460/3000]  | LR: 0.028293 | E: -63.009130 | E_img: +0.0316j E_var:     4.5240 E_err:   0.023500 | Acc: 0.2816
[2025-11-12 03:12:26] 02:25:59<11:11:13, 15.87s/it | [Iter  552/3090] R0[461/3000]  | LR: 0.028286 | E: -63.054206 | E_img: -0.0384j E_var:     4.1082 E_err:   0.022394 | Acc: 0.2936
[2025-11-12 03:12:42] 02:26:15<11:10:57, 15.87s/it | [Iter  553/3090] R0[462/3000]  | LR: 0.028278 | E: -63.083725 | E_img: +0.0010j E_var:     4.6159 E_err:   0.023737 | Acc: 0.2818
[2025-11-12 03:12:58] 02:26:31<11:10:41, 15.87s/it | [Iter  554/3090] R0[463/3000]  | LR: 0.028271 | E: -63.169678 | E_img: +0.0135j E_var:     4.1454 E_err:   0.022495 | Acc: 0.2751
[2025-11-12 03:13:14] 02:26:47<11:10:25, 15.87s/it | [Iter  555/3090] R0[464/3000]  | LR: 0.028264 | E: -63.149004 | E_img: -0.0322j E_var:     3.9697 E_err:   0.022013 | Acc: 0.2847
[2025-11-12 03:13:29] 02:27:03<11:10:09, 15.87s/it | [Iter  556/3090] R0[465/3000]  | LR: 0.028256 | E: -63.128500 | E_img: -0.0193j E_var:     4.1706 E_err:   0.022563 | Acc: 0.2877
[2025-11-12 03:13:45] 02:27:18<11:09:53, 15.87s/it | [Iter  557/3090] R0[466/3000]  | LR: 0.028249 | E: -63.048337 | E_img: +0.0048j E_var:     4.1402 E_err:   0.022481 | Acc: 0.2917
[2025-11-12 03:14:01] 02:27:34<11:09:37, 15.87s/it | [Iter  558/3090] R0[467/3000]  | LR: 0.028242 | E: -63.166174 | E_img: +0.0000j E_var:     4.1136 E_err:   0.022409 | Acc: 0.2914
[2025-11-12 03:14:17] 02:27:50<11:09:21, 15.87s/it | [Iter  559/3090] R0[468/3000]  | LR: 0.028234 | E: -63.137974 | E_img: -0.0207j E_var:     4.0612 E_err:   0.022265 | Acc: 0.2893
[2025-11-12 03:14:33] 02:28:06<11:09:06, 15.87s/it | [Iter  560/3090] R0[469/3000]  | LR: 0.028227 | E: -63.224236 | E_img: -0.0023j E_var:     4.2620 E_err:   0.022809 | Acc: 0.2840
[2025-11-12 03:14:49] 02:28:22<11:08:50, 15.87s/it | [Iter  561/3090] R0[470/3000]  | LR: 0.028220 | E: -63.143691 | E_img: -0.0144j E_var:     3.9615 E_err:   0.021991 | Acc: 0.2976
[2025-11-12 03:15:10] 02:28:43<11:08:58, 15.88s/it | [Iter  562/3090] R0[471/3000]  | LR: 0.028212 | E: -63.064104 | E_img: +0.0003j E_var:     4.2482 E_err:   0.022772 | Acc: 0.3113
[2025-11-12 03:15:32] 02:29:05<11:09:09, 15.89s/it | [Iter  563/3090] R0[472/3000]  | LR: 0.028205 | E: -63.136040 | E_img: +0.0070j E_var:     4.1829 E_err:   0.022597 | Acc: 0.3024
[2025-11-12 03:15:48] 02:29:21<11:08:53, 15.89s/it | [Iter  564/3090] R0[473/3000]  | LR: 0.028197 | E: -63.219884 | E_img: -0.0130j E_var:     4.6279 E_err:   0.023768 | Acc: 0.2790
[2025-11-12 03:16:04] 02:29:37<11:08:37, 15.89s/it | [Iter  565/3090] R0[474/3000]  | LR: 0.028190 | E: -63.177541 | E_img: +0.0139j E_var:     4.0729 E_err:   0.022298 | Acc: 0.2796
[2025-11-12 03:16:19] 02:29:53<11:08:21, 15.89s/it | [Iter  566/3090] R0[475/3000]  | LR: 0.028182 | E: -63.144615 | E_img: +0.0007j E_var:     4.8459 E_err:   0.024322 | Acc: 0.2637
[2025-11-12 03:16:35] 02:30:08<11:08:05, 15.89s/it | [Iter  567/3090] R0[476/3000]  | LR: 0.028175 | E: -63.161424 | E_img: +0.0013j E_var:     4.2885 E_err:   0.022880 | Acc: 0.2631
[2025-11-12 03:16:51] 02:30:24<11:07:49, 15.89s/it | [Iter  568/3090] R0[477/3000]  | LR: 0.028167 | E: -63.189022 | E_img: -0.0202j E_var:     4.0097 E_err:   0.022124 | Acc: 0.2730
[2025-11-12 03:17:08] 02:30:41<11:07:37, 15.89s/it | [Iter  569/3090] R0[478/3000]  | LR: 0.028160 | E: -63.116761 | E_img: +0.0017j E_var:     4.0471 E_err:   0.022227 | Acc: 0.2910
[2025-11-12 03:17:35] 02:31:08<11:08:09, 15.91s/it | [Iter  570/3090] R0[479/3000]  | LR: 0.028152 | E: -63.146471 | E_img: -0.0007j E_var:     4.1616 E_err:   0.022539 | Acc: 0.2950
[2025-11-12 03:17:51] 02:31:24<11:07:53, 15.91s/it | [Iter  571/3090] R0[480/3000]  | LR: 0.028145 | E: -63.139318 | E_img: -0.0175j E_var:     4.2960 E_err:   0.022900 | Acc: 0.2887
[2025-11-12 03:18:19] 02:31:52<11:08:32, 15.93s/it | [Iter  572/3090] R0[481/3000]  | LR: 0.028137 | E: -63.074196 | E_img: -0.0061j E_var:     4.1500 E_err:   0.022507 | Acc: 0.2908
[2025-11-12 03:18:35] 02:32:08<11:08:16, 15.93s/it | [Iter  573/3090] R0[482/3000]  | LR: 0.028129 | E: -63.162933 | E_img: +0.0039j E_var:     4.0825 E_err:   0.022324 | Acc: 0.2907
[2025-11-12 03:18:51] 02:32:24<11:07:59, 15.93s/it | [Iter  574/3090] R0[483/3000]  | LR: 0.028122 | E: -63.140087 | E_img: +0.0128j E_var:     4.0560 E_err:   0.022251 | Acc: 0.2892
[2025-11-12 03:19:06] 02:32:40<11:07:43, 15.93s/it | [Iter  575/3090] R0[484/3000]  | LR: 0.028114 | E: -63.155819 | E_img: +0.0001j E_var:     4.1945 E_err:   0.022628 | Acc: 0.2848
[2025-11-12 03:19:22] 02:32:55<11:07:27, 15.93s/it | [Iter  576/3090] R0[485/3000]  | LR: 0.028107 | E: -63.143845 | E_img: -0.0201j E_var:     3.9239 E_err:   0.021886 | Acc: 0.2961
[2025-11-12 03:19:38] 02:33:11<11:07:11, 15.93s/it | [Iter  577/3090] R0[486/3000]  | LR: 0.028099 | E: -63.198957 | E_img: +0.0049j E_var:     4.1299 E_err:   0.022453 | Acc: 0.2864
[2025-11-12 03:19:54] 02:33:27<11:06:54, 15.93s/it | [Iter  578/3090] R0[487/3000]  | LR: 0.028091 | E: -63.181016 | E_img: -0.0156j E_var:     4.0317 E_err:   0.022185 | Acc: 0.2894
[2025-11-12 03:20:10] 02:33:43<11:06:38, 15.93s/it | [Iter  579/3090] R0[488/3000]  | LR: 0.028084 | E: -63.153754 | E_img: +0.0119j E_var:     4.1010 E_err:   0.022374 | Acc: 0.2909
[2025-11-12 03:20:26] 02:33:59<11:06:22, 15.93s/it | [Iter  580/3090] R0[489/3000]  | LR: 0.028076 | E: -63.167892 | E_img: +0.0013j E_var:     4.1377 E_err:   0.022474 | Acc: 0.2875
[2025-11-12 03:20:42] 02:34:15<11:06:06, 15.93s/it | [Iter  581/3090] R0[490/3000]  | LR: 0.028068 | E: -63.157751 | E_img: -0.0069j E_var:     3.9807 E_err:   0.022044 | Acc: 0.2954
[2025-11-12 03:20:57] 02:34:31<11:05:49, 15.93s/it | [Iter  582/3090] R0[491/3000]  | LR: 0.028060 | E: -63.169613 | E_img: +0.0119j E_var:     4.0443 E_err:   0.022219 | Acc: 0.2962
[2025-11-12 03:21:13] 02:34:46<11:05:33, 15.93s/it | [Iter  583/3090] R0[492/3000]  | LR: 0.028053 | E: -63.204518 | E_img: +0.0017j E_var:     4.1546 E_err:   0.022520 | Acc: 0.2857
[2025-11-12 03:21:29] 02:35:02<11:05:17, 15.93s/it | [Iter  584/3090] R0[493/3000]  | LR: 0.028045 | E: -63.243841 | E_img: -0.0322j E_var:     4.0827 E_err:   0.022324 | Acc: 0.2811
[2025-11-12 03:21:45] 02:35:18<11:05:00, 15.93s/it | [Iter  585/3090] R0[494/3000]  | LR: 0.028037 | E: -63.143918 | E_img: +0.0300j E_var:     4.4402 E_err:   0.023281 | Acc: 0.2736
[2025-11-12 03:22:01] 02:35:34<11:04:44, 15.93s/it | [Iter  586/3090] R0[495/3000]  | LR: 0.028029 | E: -63.198507 | E_img: -0.0052j E_var:     4.0782 E_err:   0.022312 | Acc: 0.2773
[2025-11-12 03:22:17] 02:35:50<11:04:28, 15.93s/it | [Iter  587/3090] R0[496/3000]  | LR: 0.028022 | E: -63.127308 | E_img: -0.0046j E_var:     4.0721 E_err:   0.022295 | Acc: 0.2770
[2025-11-12 03:22:32] 02:36:06<11:04:12, 15.93s/it | [Iter  588/3090] R0[497/3000]  | LR: 0.028014 | E: -63.132500 | E_img: -0.0107j E_var:     4.2048 E_err:   0.022656 | Acc: 0.2738
[2025-11-12 03:22:48] 02:36:21<11:03:55, 15.93s/it | [Iter  589/3090] R0[498/3000]  | LR: 0.028006 | E: -63.127738 | E_img: -0.0103j E_var:     4.1103 E_err:   0.022400 | Acc: 0.2722
[2025-11-12 03:23:04] 02:36:37<11:03:39, 15.93s/it | [Iter  590/3090] R0[499/3000]  | LR: 0.027998 | E: -63.186376 | E_img: -0.0195j E_var:     4.5052 E_err:   0.023451 | Acc: 0.2616
[2025-11-12 03:23:20] 02:36:53<11:03:23, 15.93s/it | [Iter  591/3090] R0[500/3000]  | LR: 0.027990 | E: -63.099577 | E_img: +0.0054j E_var:     4.5192 E_err:   0.023487 | Acc: 0.2584
[2025-11-12 03:23:36] 02:37:09<11:03:06, 15.93s/it | [Iter  592/3090] R0[501/3000]  | LR: 0.027983 | E: -63.191717 | E_img: +0.0020j E_var:     4.0377 E_err:   0.022201 | Acc: 0.2703
[2025-11-12 03:23:52] 02:37:25<11:02:50, 15.93s/it | [Iter  593/3090] R0[502/3000]  | LR: 0.027975 | E: -63.068568 | E_img: -0.0260j E_var:     4.0960 E_err:   0.022361 | Acc: 0.2902
[2025-11-12 03:24:08] 02:37:41<11:02:34, 15.93s/it | [Iter  594/3090] R0[503/3000]  | LR: 0.027967 | E: -62.939771 | E_img: -0.0485j E_var:     4.1154 E_err:   0.022414 | Acc: 0.3078
[2025-11-12 03:24:34] 02:38:07<11:03:03, 15.95s/it | [Iter  595/3090] R0[504/3000]  | LR: 0.027959 | E: -63.147482 | E_img: +0.0019j E_var:     3.9501 E_err:   0.021959 | Acc: 0.3056
[2025-11-12 03:24:50] 02:38:23<11:02:47, 15.95s/it | [Iter  596/3090] R0[505/3000]  | LR: 0.027951 | E: -63.140296 | E_img: -0.0021j E_var:     4.0273 E_err:   0.022172 | Acc: 0.2943
[2025-11-12 03:25:20] 02:38:53<11:03:27, 15.97s/it | [Iter  597/3090] R0[506/3000]  | LR: 0.027943 | E: -63.163360 | E_img: +0.0112j E_var:     3.9060 E_err:   0.021836 | Acc: 0.2921
[2025-11-12 03:25:36] 02:39:09<11:03:11, 15.97s/it | [Iter  598/3090] R0[507/3000]  | LR: 0.027935 | E: -63.168302 | E_img: -0.0184j E_var:     3.9748 E_err:   0.022027 | Acc: 0.2942
[2025-11-12 03:25:51] 02:39:24<11:02:55, 15.97s/it | [Iter  599/3090] R0[508/3000]  | LR: 0.027927 | E: -63.033440 | E_img: +0.0248j E_var:     3.9461 E_err:   0.021948 | Acc: 0.2938
[2025-11-12 03:26:07] 02:39:40<11:02:38, 15.97s/it | [Iter  600/3090] R0[509/3000]  | LR: 0.027919 | E: -63.011114 | E_img: -0.0042j E_var:     4.0578 E_err:   0.022256 | Acc: 0.3004
[2025-11-12 03:26:23] 02:39:56<11:02:22, 15.97s/it | [Iter  601/3090] R0[510/3000]  | LR: 0.027911 | E: -63.063170 | E_img: +0.0326j E_var:     4.2512 E_err:   0.022780 | Acc: 0.2908
[2025-11-12 03:26:39] 02:40:12<11:02:06, 15.97s/it | [Iter  602/3090] R0[511/3000]  | LR: 0.027903 | E: -63.057979 | E_img: +0.0226j E_var:     4.0026 E_err:   0.022104 | Acc: 0.2974
[2025-11-12 03:26:55] 02:40:28<11:01:49, 15.97s/it | [Iter  603/3090] R0[512/3000]  | LR: 0.027895 | E: -63.122056 | E_img: +0.0064j E_var:     4.0105 E_err:   0.022126 | Acc: 0.3007
[2025-11-12 03:27:11] 02:40:44<11:01:33, 15.97s/it | [Iter  604/3090] R0[513/3000]  | LR: 0.027887 | E: -63.020936 | E_img: +0.0143j E_var:     4.0996 E_err:   0.022370 | Acc: 0.3052
[2025-11-12 03:27:27] 02:41:00<11:01:16, 15.97s/it | [Iter  605/3090] R0[514/3000]  | LR: 0.027879 | E: -63.070354 | E_img: -0.0015j E_var:     4.0886 E_err:   0.022341 | Acc: 0.2957
[2025-11-12 03:27:42] 02:41:15<11:01:00, 15.97s/it | [Iter  606/3090] R0[515/3000]  | LR: 0.027871 | E: -63.084539 | E_img: +0.0101j E_var:     3.8055 E_err:   0.021553 | Acc: 0.2926
[2025-11-12 03:27:58] 02:41:31<11:00:43, 15.97s/it | [Iter  607/3090] R0[516/3000]  | LR: 0.027863 | E: -63.105313 | E_img: +0.0223j E_var:     4.1230 E_err:   0.022434 | Acc: 0.2845
[2025-11-12 03:28:14] 02:41:47<11:00:27, 15.97s/it | [Iter  608/3090] R0[517/3000]  | LR: 0.027855 | E: -63.090058 | E_img: +0.0037j E_var:     4.1177 E_err:   0.022420 | Acc: 0.2791
[2025-11-12 03:28:30] 02:42:03<11:00:11, 15.97s/it | [Iter  609/3090] R0[518/3000]  | LR: 0.027847 | E: -63.112149 | E_img: +0.0119j E_var:     4.5514 E_err:   0.023571 | Acc: 0.2692
[2025-11-12 03:28:46] 02:42:19<10:59:54, 15.97s/it | [Iter  610/3090] R0[519/3000]  | LR: 0.027839 | E: -63.049029 | E_img: -0.0278j E_var:     3.9691 E_err:   0.022011 | Acc: 0.2743
[2025-11-12 03:29:02] 02:42:35<10:59:38, 15.97s/it | [Iter  611/3090] R0[520/3000]  | LR: 0.027830 | E: -63.078343 | E_img: -0.0439j E_var:     3.9946 E_err:   0.022082 | Acc: 0.2850
[2025-11-12 03:29:17] 02:42:51<10:59:21, 15.96s/it | [Iter  612/3090] R0[521/3000]  | LR: 0.027822 | E: -63.110924 | E_img: -0.0217j E_var:     4.2442 E_err:   0.022762 | Acc: 0.2796
[2025-11-12 03:29:33] 02:43:06<10:59:05, 15.96s/it | [Iter  613/3090] R0[522/3000]  | LR: 0.027814 | E: -63.154167 | E_img: +0.0207j E_var:     3.8083 E_err:   0.021561 | Acc: 0.2877
[2025-11-12 03:29:49] 02:43:22<10:58:48, 15.96s/it | [Iter  614/3090] R0[523/3000]  | LR: 0.027806 | E: -63.127273 | E_img: -0.0059j E_var:     3.8469 E_err:   0.021670 | Acc: 0.3008
[2025-11-12 03:30:05] 02:43:38<10:58:32, 15.96s/it | [Iter  615/3090] R0[524/3000]  | LR: 0.027798 | E: -63.172251 | E_img: -0.0010j E_var:     4.0755 E_err:   0.022305 | Acc: 0.2913
[2025-11-12 03:30:21] 02:43:54<10:58:15, 15.96s/it | [Iter  616/3090] R0[525/3000]  | LR: 0.027790 | E: -63.162609 | E_img: +0.0159j E_var:     4.0034 E_err:   0.022106 | Acc: 0.2819
[2025-11-12 03:30:37] 02:44:10<10:57:59, 15.96s/it | [Iter  617/3090] R0[526/3000]  | LR: 0.027781 | E: -63.153306 | E_img: +0.0371j E_var:     3.9103 E_err:   0.021848 | Acc: 0.2871
[2025-11-12 03:30:52] 02:44:26<10:57:42, 15.96s/it | [Iter  618/3090] R0[527/3000]  | LR: 0.027773 | E: -63.051401 | E_img: +0.0140j E_var:     4.0037 E_err:   0.022107 | Acc: 0.3079
[2025-11-12 03:31:08] 02:44:41<10:57:26, 15.96s/it | [Iter  619/3090] R0[528/3000]  | LR: 0.027765 | E: -63.081822 | E_img: -0.0314j E_var:     4.0316 E_err:   0.022184 | Acc: 0.3072
[2025-11-12 03:31:24] 02:44:57<10:57:10, 15.96s/it | [Iter  620/3090] R0[529/3000]  | LR: 0.027757 | E: -63.136013 | E_img: -0.0057j E_var:     3.9698 E_err:   0.022014 | Acc: 0.3036
[2025-11-12 03:31:40] 02:45:13<10:56:53, 15.96s/it | [Iter  621/3090] R0[530/3000]  | LR: 0.027748 | E: -63.005456 | E_img: -0.0135j E_var:     3.8969 E_err:   0.021810 | Acc: 0.3163
[2025-11-12 03:31:56] 02:45:29<10:56:37, 15.96s/it | [Iter  622/3090] R0[531/3000]  | LR: 0.027740 | E: -63.234070 | E_img: -0.0077j E_var:     3.9805 E_err:   0.022043 | Acc: 0.3058
[2025-11-12 03:32:12] 02:45:45<10:56:20, 15.96s/it | [Iter  623/3090] R0[532/3000]  | LR: 0.027732 | E: -63.187944 | E_img: +0.0204j E_var:     3.6951 E_err:   0.021238 | Acc: 0.3003
[2025-11-12 03:32:28] 02:46:01<10:56:04, 15.96s/it | [Iter  624/3090] R0[533/3000]  | LR: 0.027724 | E: -63.127239 | E_img: +0.0153j E_var:     3.9508 E_err:   0.021961 | Acc: 0.2994
[2025-11-12 03:32:44] 02:46:17<10:55:49, 15.96s/it | [Iter  625/3090] R0[534/3000]  | LR: 0.027715 | E: -63.217480 | E_img: +0.0030j E_var:     4.0506 E_err:   0.022236 | Acc: 0.2901
[2025-11-12 03:33:00] 02:46:33<10:55:33, 15.96s/it | [Iter  626/3090] R0[535/3000]  | LR: 0.027707 | E: -63.218861 | E_img: -0.0231j E_var:     3.8306 E_err:   0.021624 | Acc: 0.2946
[2025-11-12 03:33:15] 02:46:49<10:55:16, 15.96s/it | [Iter  627/3090] R0[536/3000]  | LR: 0.027698 | E: -63.143788 | E_img: +0.0088j E_var:     3.8521 E_err:   0.021685 | Acc: 0.2930
[2025-11-12 03:33:31] 02:47:04<10:55:00, 15.96s/it | [Iter  628/3090] R0[537/3000]  | LR: 0.027690 | E: -63.115857 | E_img: -0.0152j E_var:     4.1370 E_err:   0.022472 | Acc: 0.2882
[2025-11-12 03:33:47] 02:47:20<10:54:43, 15.96s/it | [Iter  629/3090] R0[538/3000]  | LR: 0.027682 | E: -63.174931 | E_img: -0.0236j E_var:     4.0723 E_err:   0.022296 | Acc: 0.2851
[2025-11-12 03:34:03] 02:47:36<10:54:27, 15.96s/it | [Iter  630/3090] R0[539/3000]  | LR: 0.027673 | E: -63.122594 | E_img: -0.0076j E_var:     3.7160 E_err:   0.021298 | Acc: 0.2947
[2025-11-12 03:34:19] 02:47:52<10:54:11, 15.96s/it | [Iter  631/3090] R0[540/3000]  | LR: 0.027665 | E: -63.062249 | E_img: +0.0339j E_var:     3.8702 E_err:   0.021736 | Acc: 0.3041
[2025-11-12 03:34:35] 02:48:08<10:53:54, 15.96s/it | [Iter  632/3090] R0[541/3000]  | LR: 0.027657 | E: -63.118858 | E_img: -0.0014j E_var:     3.7986 E_err:   0.021534 | Acc: 0.3028
[2025-11-12 03:34:51] 02:48:24<10:53:38, 15.96s/it | [Iter  633/3090] R0[542/3000]  | LR: 0.027648 | E: -63.215345 | E_img: +0.0231j E_var:     3.8192 E_err:   0.021592 | Acc: 0.2964
[2025-11-12 03:35:06] 02:48:40<10:53:21, 15.96s/it | [Iter  634/3090] R0[543/3000]  | LR: 0.027640 | E: -63.219142 | E_img: -0.0199j E_var:     3.9810 E_err:   0.022045 | Acc: 0.2875
[2025-11-12 03:35:22] 02:48:55<10:53:05, 15.96s/it | [Iter  635/3090] R0[544/3000]  | LR: 0.027631 | E: -63.243690 | E_img: +0.0056j E_var:     3.8116 E_err:   0.021570 | Acc: 0.2876
[2025-11-12 03:35:38] 02:49:11<10:52:48, 15.96s/it | [Iter  636/3090] R0[545/3000]  | LR: 0.027623 | E: -63.221940 | E_img: +0.0120j E_var:     3.8692 E_err:   0.021733 | Acc: 0.2891
[2025-11-12 03:35:54] 02:49:27<10:52:32, 15.96s/it | [Iter  637/3090] R0[546/3000]  | LR: 0.027614 | E: -63.288807 | E_img: +0.0068j E_var:     4.0732 E_err:   0.022298 | Acc: 0.2777
[2025-11-12 03:36:10] 02:49:43<10:52:15, 15.96s/it | [Iter  638/3090] R0[547/3000]  | LR: 0.027606 | E: -63.280946 | E_img: +0.0230j E_var:     3.7589 E_err:   0.021421 | Acc: 0.2829
[2025-11-12 03:36:26] 02:49:59<10:51:59, 15.96s/it | [Iter  639/3090] R0[548/3000]  | LR: 0.027597 | E: -63.282431 | E_img: +0.0057j E_var:     3.6447 E_err:   0.021093 | Acc: 0.2987
[2025-11-12 03:36:41] 02:50:15<10:51:43, 15.96s/it | [Iter  640/3090] R0[549/3000]  | LR: 0.027589 | E: -63.148230 | E_img: -0.0332j E_var:     3.9317 E_err:   0.021908 | Acc: 0.3011
[2025-11-12 03:36:57] 02:50:30<10:51:26, 15.96s/it | [Iter  641/3090] R0[550/3000]  | LR: 0.027580 | E: -63.233881 | E_img: +0.0256j E_var:     3.8987 E_err:   0.021816 | Acc: 0.2939
[2025-11-12 03:37:13] 02:50:46<10:51:10, 15.96s/it | [Iter  642/3090] R0[551/3000]  | LR: 0.027572 | E: -63.144223 | E_img: +0.0057j E_var:     3.7511 E_err:   0.021399 | Acc: 0.3016
[2025-11-12 03:37:29] 02:51:02<10:50:53, 15.96s/it | [Iter  643/3090] R0[552/3000]  | LR: 0.027563 | E: -63.027461 | E_img: -0.0023j E_var:     4.0044 E_err:   0.022109 | Acc: 0.3165
[2025-11-12 03:37:45] 02:51:18<10:50:37, 15.96s/it | [Iter  644/3090] R0[553/3000]  | LR: 0.027554 | E: -62.997529 | E_img: +0.0230j E_var:     4.0467 E_err:   0.022226 | Acc: 0.3157
[2025-11-12 03:38:01] 02:51:34<10:50:21, 15.96s/it | [Iter  645/3090] R0[554/3000]  | LR: 0.027546 | E: -62.936185 | E_img: +0.0200j E_var:     3.9028 E_err:   0.021827 | Acc: 0.3156
[2025-11-12 03:38:17] 02:51:50<10:50:04, 15.96s/it | [Iter  646/3090] R0[555/3000]  | LR: 0.027537 | E: -62.983724 | E_img: -0.0124j E_var:     3.9353 E_err:   0.021918 | Acc: 0.3171
[2025-11-12 03:38:32] 02:52:05<10:49:48, 15.96s/it | [Iter  647/3090] R0[556/3000]  | LR: 0.027528 | E: -63.137404 | E_img: +0.0210j E_var:     3.7133 E_err:   0.021290 | Acc: 0.3113
[2025-11-12 03:38:48] 02:52:21<10:49:31, 15.96s/it | [Iter  648/3090] R0[557/3000]  | LR: 0.027520 | E: -63.108222 | E_img: +0.0012j E_var:     3.7796 E_err:   0.021480 | Acc: 0.3102
[2025-11-12 03:39:04] 02:52:37<10:49:15, 15.96s/it | [Iter  649/3090] R0[558/3000]  | LR: 0.027511 | E: -63.185267 | E_img: -0.0257j E_var:     3.8476 E_err:   0.021672 | Acc: 0.3030
[2025-11-12 03:39:20] 02:52:53<10:48:59, 15.96s/it | [Iter  650/3090] R0[559/3000]  | LR: 0.027503 | E: -63.146555 | E_img: +0.0153j E_var:     3.7722 E_err:   0.021459 | Acc: 0.2988
[2025-11-12 03:39:36] 02:53:09<10:48:42, 15.96s/it | [Iter  651/3090] R0[560/3000]  | LR: 0.027494 | E: -63.312212 | E_img: +0.0007j E_var:     3.7979 E_err:   0.021532 | Acc: 0.2904
[2025-11-12 03:39:52] 02:53:25<10:48:26, 15.96s/it | [Iter  652/3090] R0[561/3000]  | LR: 0.027485 | E: -63.284788 | E_img: +0.0127j E_var:     3.9531 E_err:   0.021967 | Acc: 0.2804
[2025-11-12 03:40:07] 02:53:41<10:48:09, 15.96s/it | [Iter  653/3090] R0[562/3000]  | LR: 0.027476 | E: -63.315829 | E_img: -0.0110j E_var:     4.0841 E_err:   0.022328 | Acc: 0.2740
[2025-11-12 03:40:23] 02:53:56<10:47:53, 15.96s/it | [Iter  654/3090] R0[563/3000]  | LR: 0.027468 | E: -63.256720 | E_img: +0.0068j E_var:     4.0471 E_err:   0.022227 | Acc: 0.2720
[2025-11-12 03:40:39] 02:54:12<10:47:37, 15.96s/it | [Iter  655/3090] R0[564/3000]  | LR: 0.027459 | E: -63.241276 | E_img: +0.0106j E_var:     3.8528 E_err:   0.021687 | Acc: 0.2825
[2025-11-12 03:40:55] 02:54:28<10:47:20, 15.96s/it | [Iter  656/3090] R0[565/3000]  | LR: 0.027450 | E: -63.227918 | E_img: -0.0122j E_var:     3.8768 E_err:   0.021754 | Acc: 0.2820
[2025-11-12 03:41:11] 02:54:44<10:47:04, 15.96s/it | [Iter  657/3090] R0[566/3000]  | LR: 0.027441 | E: -63.018336 | E_img: -0.0311j E_var:     3.8978 E_err:   0.021813 | Acc: 0.2925
[2025-11-12 03:41:27] 02:55:00<10:46:47, 15.96s/it | [Iter  658/3090] R0[567/3000]  | LR: 0.027433 | E: -63.240028 | E_img: +0.0125j E_var:     4.0222 E_err:   0.022158 | Acc: 0.2807
[2025-11-12 03:41:42] 02:55:16<10:46:31, 15.96s/it | [Iter  659/3090] R0[568/3000]  | LR: 0.027424 | E: -63.217726 | E_img: +0.0175j E_var:     3.7838 E_err:   0.021492 | Acc: 0.2859
[2025-11-12 03:41:58] 02:55:31<10:46:15, 15.96s/it | [Iter  660/3090] R0[569/3000]  | LR: 0.027415 | E: -63.147418 | E_img: -0.0238j E_var:     4.0794 E_err:   0.022315 | Acc: 0.2814
[2025-11-12 03:42:14] 02:55:47<10:45:58, 15.96s/it | [Iter  661/3090] R0[570/3000]  | LR: 0.027406 | E: -63.172820 | E_img: +0.0168j E_var:     4.2381 E_err:   0.022745 | Acc: 0.2719
[2025-11-12 03:42:30] 02:56:03<10:45:42, 15.96s/it | [Iter  662/3090] R0[571/3000]  | LR: 0.027397 | E: -63.209642 | E_img: +0.0008j E_var:     3.9104 E_err:   0.021848 | Acc: 0.2722
[2025-11-12 03:42:46] 02:56:19<10:45:26, 15.96s/it | [Iter  663/3090] R0[572/3000]  | LR: 0.027389 | E: -63.218138 | E_img: -0.0024j E_var:     3.8469 E_err:   0.021670 | Acc: 0.2857
[2025-11-12 03:43:02] 02:56:35<10:45:10, 15.96s/it | [Iter  664/3090] R0[573/3000]  | LR: 0.027380 | E: -63.098896 | E_img: -0.0154j E_var:     3.9418 E_err:   0.021936 | Acc: 0.2949
[2025-11-12 03:43:18] 02:56:51<10:44:54, 15.96s/it | [Iter  665/3090] R0[574/3000]  | LR: 0.027371 | E: -63.104268 | E_img: +0.0212j E_var:     4.0069 E_err:   0.022116 | Acc: 0.2941
[2025-11-12 03:43:34] 02:57:07<10:44:38, 15.96s/it | [Iter  666/3090] R0[575/3000]  | LR: 0.027362 | E: -63.126213 | E_img: -0.0193j E_var:     3.7841 E_err:   0.021492 | Acc: 0.2915
[2025-11-12 03:43:50] 02:57:23<10:44:23, 15.96s/it | [Iter  667/3090] R0[576/3000]  | LR: 0.027353 | E: -63.084584 | E_img: -0.0146j E_var:     3.9690 E_err:   0.022011 | Acc: 0.2965
[2025-11-12 03:44:06] 02:57:39<10:44:06, 15.96s/it | [Iter  668/3090] R0[577/3000]  | LR: 0.027344 | E: -62.968704 | E_img: -0.0115j E_var:     3.9445 E_err:   0.021943 | Acc: 0.3131
[2025-11-12 03:44:22] 02:57:55<10:43:50, 15.96s/it | [Iter  669/3090] R0[578/3000]  | LR: 0.027335 | E: -62.901409 | E_img: -0.0607j E_var:     4.1976 E_err:   0.022636 | Acc: 0.3215
[2025-11-12 03:44:37] 02:58:10<10:43:33, 15.96s/it | [Iter  670/3090] R0[579/3000]  | LR: 0.027326 | E: -62.909461 | E_img: -0.0224j E_var:     4.2831 E_err:   0.022866 | Acc: 0.3195
[2025-11-12 03:44:53] 02:58:26<10:43:17, 15.96s/it | [Iter  671/3090] R0[580/3000]  | LR: 0.027317 | E: -63.044236 | E_img: +0.0101j E_var:     4.1496 E_err:   0.022506 | Acc: 0.3012
[2025-11-12 03:45:09] 02:58:42<10:43:01, 15.96s/it | [Iter  672/3090] R0[581/3000]  | LR: 0.027308 | E: -63.127915 | E_img: +0.0058j E_var:     3.8556 E_err:   0.021694 | Acc: 0.2914
[2025-11-12 03:45:25] 02:58:58<10:42:44, 15.96s/it | [Iter  673/3090] R0[582/3000]  | LR: 0.027299 | E: -63.153368 | E_img: +0.0342j E_var:     3.7087 E_err:   0.021277 | Acc: 0.2943
[2025-11-12 03:45:41] 02:59:14<10:42:28, 15.96s/it | [Iter  674/3090] R0[583/3000]  | LR: 0.027290 | E: -63.186439 | E_img: +0.0085j E_var:     3.8870 E_err:   0.021783 | Acc: 0.2865
[2025-11-12 03:45:57] 02:59:30<10:42:12, 15.96s/it | [Iter  675/3090] R0[584/3000]  | LR: 0.027281 | E: -63.213363 | E_img: -0.0009j E_var:     3.8337 E_err:   0.021633 | Acc: 0.2873
[2025-11-12 03:46:12] 02:59:46<10:41:55, 15.95s/it | [Iter  676/3090] R0[585/3000]  | LR: 0.027272 | E: -63.185807 | E_img: +0.0119j E_var:     3.9147 E_err:   0.021860 | Acc: 0.2818
[2025-11-12 03:46:28] 03:00:01<10:41:39, 15.95s/it | [Iter  677/3090] R0[586/3000]  | LR: 0.027263 | E: -63.220409 | E_img: +0.0157j E_var:     4.2707 E_err:   0.022833 | Acc: 0.2643
[2025-11-12 03:46:44] 03:00:17<10:41:23, 15.95s/it | [Iter  678/3090] R0[587/3000]  | LR: 0.027254 | E: -63.180063 | E_img: +0.0205j E_var:     4.2535 E_err:   0.022787 | Acc: 0.2554
[2025-11-12 03:47:00] 03:00:33<10:41:06, 15.95s/it | [Iter  679/3090] R0[588/3000]  | LR: 0.027245 | E: -63.169203 | E_img: +0.0055j E_var:     4.1226 E_err:   0.022433 | Acc: 0.2585
[2025-11-12 03:47:16] 03:00:49<10:40:50, 15.95s/it | [Iter  680/3090] R0[589/3000]  | LR: 0.027236 | E: -63.199223 | E_img: +0.0008j E_var:     4.5134 E_err:   0.023472 | Acc: 0.2498
[2025-11-12 03:47:32] 03:01:05<10:40:34, 15.95s/it | [Iter  681/3090] R0[590/3000]  | LR: 0.027227 | E: -63.231356 | E_img: +0.0088j E_var:     4.2689 E_err:   0.022828 | Acc: 0.2454
[2025-11-12 03:47:48] 03:01:21<10:40:17, 15.95s/it | [Iter  682/3090] R0[591/3000]  | LR: 0.027218 | E: -63.272260 | E_img: +0.0143j E_var:     3.9371 E_err:   0.021923 | Acc: 0.2537
[2025-11-12 03:48:03] 03:01:36<10:40:01, 15.95s/it | [Iter  683/3090] R0[592/3000]  | LR: 0.027209 | E: -63.324808 | E_img: +0.0012j E_var:     4.0190 E_err:   0.022150 | Acc: 0.2548
[2025-11-12 03:48:19] 03:01:52<10:39:45, 15.95s/it | [Iter  684/3090] R0[593/3000]  | LR: 0.027200 | E: -63.329199 | E_img: +0.0023j E_var:     3.9493 E_err:   0.021957 | Acc: 0.2609
[2025-11-12 03:48:35] 03:02:08<10:39:28, 15.95s/it | [Iter  685/3090] R0[594/3000]  | LR: 0.027190 | E: -63.296567 | E_img: +0.0027j E_var:     4.2934 E_err:   0.022893 | Acc: 0.2567
[2025-11-12 03:48:51] 03:02:24<10:39:12, 15.95s/it | [Iter  686/3090] R0[595/3000]  | LR: 0.027181 | E: -63.303817 | E_img: -0.0157j E_var:     3.8858 E_err:   0.021779 | Acc: 0.2572
[2025-11-12 03:49:07] 03:02:40<10:38:56, 15.95s/it | [Iter  687/3090] R0[596/3000]  | LR: 0.027172 | E: -63.307666 | E_img: -0.0171j E_var:     4.1013 E_err:   0.022375 | Acc: 0.2560
[2025-11-12 03:49:23] 03:02:56<10:38:40, 15.95s/it | [Iter  688/3090] R0[597/3000]  | LR: 0.027163 | E: -63.333869 | E_img: +0.0071j E_var:     3.9078 E_err:   0.021841 | Acc: 0.2596
[2025-11-12 03:49:38] 03:03:12<10:38:23, 15.95s/it | [Iter  689/3090] R0[598/3000]  | LR: 0.027154 | E: -63.306806 | E_img: +0.0006j E_var:     4.1577 E_err:   0.022528 | Acc: 0.2587
[2025-11-12 03:49:54] 03:03:27<10:38:07, 15.95s/it | [Iter  690/3090] R0[599/3000]  | LR: 0.027144 | E: -63.294930 | E_img: -0.0218j E_var:     4.0470 E_err:   0.022226 | Acc: 0.2582
[2025-11-12 03:49:54] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-11-12 03:50:10] 03:03:43<10:37:51, 15.95s/it | [Iter  691/3090] R0[600/3000]  | LR: 0.027135 | E: -63.295599 | E_img: -0.0074j E_var:     4.4883 E_err:   0.023407 | Acc: 0.2507
[2025-11-12 03:50:26] 03:03:59<10:37:34, 15.95s/it | [Iter  692/3090] R0[601/3000]  | LR: 0.027126 | E: -63.266655 | E_img: +0.0010j E_var:     4.0661 E_err:   0.022279 | Acc: 0.2474
[2025-11-12 03:50:42] 03:04:15<10:37:18, 15.95s/it | [Iter  693/3090] R0[602/3000]  | LR: 0.027117 | E: -63.278278 | E_img: +0.0192j E_var:     4.2659 E_err:   0.022820 | Acc: 0.2429
[2025-11-12 03:50:58] 03:04:31<10:37:02, 15.95s/it | [Iter  694/3090] R0[603/3000]  | LR: 0.027108 | E: -63.299606 | E_img: +0.0241j E_var:     3.7781 E_err:   0.021475 | Acc: 0.2560
[2025-11-12 03:51:14] 03:04:47<10:36:45, 15.95s/it | [Iter  695/3090] R0[604/3000]  | LR: 0.027098 | E: -63.338926 | E_img: +0.0125j E_var:     3.8158 E_err:   0.021582 | Acc: 0.2689
[2025-11-12 03:51:29] 03:05:03<10:36:29, 15.95s/it | [Iter  696/3090] R0[605/3000]  | LR: 0.027089 | E: -63.368652 | E_img: +0.0108j E_var:     4.1457 E_err:   0.022496 | Acc: 0.2625
[2025-11-12 03:51:45] 03:05:18<10:36:13, 15.95s/it | [Iter  697/3090] R0[606/3000]  | LR: 0.027080 | E: -63.300172 | E_img: -0.0038j E_var:     4.4067 E_err:   0.023193 | Acc: 0.2566
[2025-11-12 03:52:01] 03:05:34<10:35:57, 15.95s/it | [Iter  698/3090] R0[607/3000]  | LR: 0.027070 | E: -63.286661 | E_img: +0.0066j E_var:     4.2854 E_err:   0.022872 | Acc: 0.2509
[2025-11-12 03:52:17] 03:05:50<10:35:41, 15.95s/it | [Iter  699/3090] R0[608/3000]  | LR: 0.027061 | E: -63.329904 | E_img: -0.0173j E_var:     3.9191 E_err:   0.021873 | Acc: 0.2590
[2025-11-12 03:52:33] 03:06:06<10:35:24, 15.95s/it | [Iter  700/3090] R0[609/3000]  | LR: 0.027052 | E: -63.356003 | E_img: -0.0117j E_var:     4.3362 E_err:   0.023007 | Acc: 0.2547
[2025-11-12 03:52:49] 03:06:22<10:35:08, 15.95s/it | [Iter  701/3090] R0[610/3000]  | LR: 0.027042 | E: -63.243100 | E_img: -0.0024j E_var:     4.6887 E_err:   0.023924 | Acc: 0.2393
[2025-11-12 03:53:05] 03:06:38<10:34:52, 15.95s/it | [Iter  702/3090] R0[611/3000]  | LR: 0.027033 | E: -63.195901 | E_img: -0.0171j E_var:     4.7054 E_err:   0.023966 | Acc: 0.2299
[2025-11-12 03:53:21] 03:06:54<10:34:37, 15.95s/it | [Iter  703/3090] R0[612/3000]  | LR: 0.027024 | E: -63.175818 | E_img: -0.0068j E_var:     4.5112 E_err:   0.023467 | Acc: 0.2287
[2025-11-12 03:53:37] 03:07:10<10:34:20, 15.95s/it | [Iter  704/3090] R0[613/3000]  | LR: 0.027014 | E: -63.128872 | E_img: +0.0042j E_var:     4.8831 E_err:   0.024415 | Acc: 0.2241
[2025-11-12 03:53:53] 03:07:26<10:34:04, 15.95s/it | [Iter  705/3090] R0[614/3000]  | LR: 0.027005 | E: -63.127035 | E_img: -0.0008j E_var:     4.5197 E_err:   0.023489 | Acc: 0.2226
[2025-11-12 03:54:08] 03:07:42<10:33:48, 15.95s/it | [Iter  706/3090] R0[615/3000]  | LR: 0.026995 | E: -63.196560 | E_img: +0.0150j E_var:     4.7598 E_err:   0.024105 | Acc: 0.2232
[2025-11-12 03:54:24] 03:07:57<10:33:31, 15.95s/it | [Iter  707/3090] R0[616/3000]  | LR: 0.026986 | E: -63.233380 | E_img: +0.0121j E_var:     4.2309 E_err:   0.022726 | Acc: 0.2352
[2025-11-12 03:54:40] 03:08:13<10:33:15, 15.95s/it | [Iter  708/3090] R0[617/3000]  | LR: 0.026976 | E: -63.202948 | E_img: -0.0058j E_var:     4.1733 E_err:   0.022571 | Acc: 0.2521
[2025-11-12 03:54:56] 03:08:29<10:32:59, 15.95s/it | [Iter  709/3090] R0[618/3000]  | LR: 0.026967 | E: -63.206204 | E_img: +0.0004j E_var:     4.1882 E_err:   0.022611 | Acc: 0.2555
[2025-11-12 03:55:12] 03:08:45<10:32:42, 15.95s/it | [Iter  710/3090] R0[619/3000]  | LR: 0.026957 | E: -63.271130 | E_img: -0.0040j E_var:     4.4737 E_err:   0.023369 | Acc: 0.2431
[2025-11-12 03:55:28] 03:09:01<10:32:26, 15.95s/it | [Iter  711/3090] R0[620/3000]  | LR: 0.026948 | E: -63.246431 | E_img: +0.0076j E_var:     4.1881 E_err:   0.022611 | Acc: 0.2412
[2025-11-12 03:55:43] 03:09:17<10:32:10, 15.95s/it | [Iter  712/3090] R0[621/3000]  | LR: 0.026938 | E: -63.245969 | E_img: +0.0289j E_var:     4.4165 E_err:   0.023219 | Acc: 0.2397
[2025-11-12 03:56:00] 03:09:33<10:31:55, 15.95s/it | [Iter  713/3090] R0[622/3000]  | LR: 0.026929 | E: -63.258462 | E_img: +0.0081j E_var:     4.2096 E_err:   0.022669 | Acc: 0.2394
[2025-11-12 03:56:26] 03:09:59<10:32:12, 15.96s/it | [Iter  714/3090] R0[623/3000]  | LR: 0.026919 | E: -63.269026 | E_img: +0.0003j E_var:     4.0300 E_err:   0.022180 | Acc: 0.2480
[2025-11-12 03:56:43] 03:10:16<10:32:00, 15.97s/it | [Iter  715/3090] R0[624/3000]  | LR: 0.026910 | E: -63.226392 | E_img: -0.0005j E_var:     4.4754 E_err:   0.023373 | Acc: 0.2398
[2025-11-12 03:57:04] 03:10:37<10:32:03, 15.97s/it | [Iter  716/3090] R0[625/3000]  | LR: 0.026900 | E: -63.276188 | E_img: +0.0128j E_var:     3.9960 E_err:   0.022086 | Acc: 0.2471
[2025-11-12 03:57:26] 03:10:59<10:32:06, 15.98s/it | [Iter  717/3090] R0[626/3000]  | LR: 0.026891 | E: -63.261639 | E_img: +0.0064j E_var:     4.1207 E_err:   0.022428 | Acc: 0.2490
[2025-11-12 03:57:42] 03:11:15<10:31:49, 15.98s/it | [Iter  718/3090] R0[627/3000]  | LR: 0.026881 | E: -63.305855 | E_img: -0.0125j E_var:     4.3599 E_err:   0.023070 | Acc: 0.2420
[2025-11-12 03:57:58] 03:11:31<10:31:33, 15.98s/it | [Iter  719/3090] R0[628/3000]  | LR: 0.026872 | E: -63.273364 | E_img: +0.0099j E_var:     4.0093 E_err:   0.022123 | Acc: 0.2472
[2025-11-12 03:58:14] 03:11:47<10:31:17, 15.98s/it | [Iter  720/3090] R0[629/3000]  | LR: 0.026862 | E: -63.293122 | E_img: -0.0088j E_var:     4.0628 E_err:   0.022270 | Acc: 0.2457
[2025-11-12 03:58:30] 03:12:03<10:31:01, 15.98s/it | [Iter  721/3090] R0[630/3000]  | LR: 0.026852 | E: -63.338869 | E_img: -0.0141j E_var:     3.8206 E_err:   0.021596 | Acc: 0.2589
[2025-11-12 03:58:46] 03:12:19<10:30:44, 15.98s/it | [Iter  722/3090] R0[631/3000]  | LR: 0.026843 | E: -63.341509 | E_img: +0.0146j E_var:     3.8196 E_err:   0.021593 | Acc: 0.2649
[2025-11-12 03:59:02] 03:12:35<10:30:30, 15.98s/it | [Iter  723/3090] R0[632/3000]  | LR: 0.026833 | E: -63.333661 | E_img: -0.0304j E_var:     3.7379 E_err:   0.021361 | Acc: 0.2733
[2025-11-12 03:59:18] 03:12:51<10:30:14, 15.98s/it | [Iter  724/3090] R0[633/3000]  | LR: 0.026823 | E: -63.321941 | E_img: +0.0033j E_var:     3.8376 E_err:   0.021644 | Acc: 0.2772
[2025-11-12 03:59:34] 03:13:07<10:29:58, 15.98s/it | [Iter  725/3090] R0[634/3000]  | LR: 0.026814 | E: -63.259251 | E_img: +0.0117j E_var:     3.7653 E_err:   0.021439 | Acc: 0.2948
[2025-11-12 03:59:50] 03:13:23<10:29:41, 15.98s/it | [Iter  726/3090] R0[635/3000]  | LR: 0.026804 | E: -63.202207 | E_img: +0.0025j E_var:     3.7971 E_err:   0.021529 | Acc: 0.2993
[2025-11-12 04:00:06] 03:13:39<10:29:25, 15.98s/it | [Iter  727/3090] R0[636/3000]  | LR: 0.026794 | E: -63.288260 | E_img: +0.0077j E_var:     4.0395 E_err:   0.022206 | Acc: 0.2810
[2025-11-12 04:00:22] 03:13:55<10:29:10, 15.98s/it | [Iter  728/3090] R0[637/3000]  | LR: 0.026785 | E: -63.276279 | E_img: +0.0131j E_var:     3.7579 E_err:   0.021418 | Acc: 0.2769
[2025-11-12 04:00:38] 03:14:11<10:28:53, 15.98s/it | [Iter  729/3090] R0[638/3000]  | LR: 0.026775 | E: -63.273858 | E_img: -0.0136j E_var:     3.9046 E_err:   0.021832 | Acc: 0.2807
[2025-11-12 04:00:54] 03:14:27<10:28:37, 15.98s/it | [Iter  730/3090] R0[639/3000]  | LR: 0.026765 | E: -63.282790 | E_img: -0.0043j E_var:     3.7572 E_err:   0.021416 | Acc: 0.2851
[2025-11-12 04:01:09] 03:14:43<10:28:21, 15.98s/it | [Iter  731/3090] R0[640/3000]  | LR: 0.026755 | E: -63.271279 | E_img: -0.0090j E_var:     3.9057 E_err:   0.021835 | Acc: 0.2753
[2025-11-12 04:01:25] 03:14:58<10:28:04, 15.98s/it | [Iter  732/3090] R0[641/3000]  | LR: 0.026746 | E: -63.285355 | E_img: -0.0037j E_var:     4.3629 E_err:   0.023078 | Acc: 0.2604
[2025-11-12 04:01:41] 03:15:14<10:27:48, 15.98s/it | [Iter  733/3090] R0[642/3000]  | LR: 0.026736 | E: -63.312633 | E_img: +0.0091j E_var:     3.9466 E_err:   0.021949 | Acc: 0.2518
[2025-11-12 04:01:57] 03:15:30<10:27:31, 15.98s/it | [Iter  734/3090] R0[643/3000]  | LR: 0.026726 | E: -63.264139 | E_img: -0.0140j E_var:     4.2336 E_err:   0.022733 | Acc: 0.2499
[2025-11-12 04:02:13] 03:15:46<10:27:15, 15.98s/it | [Iter  735/3090] R0[644/3000]  | LR: 0.026716 | E: -63.308482 | E_img: -0.0017j E_var:     3.9903 E_err:   0.022070 | Acc: 0.2532
[2025-11-12 04:02:29] 03:16:02<10:26:59, 15.98s/it | [Iter  736/3090] R0[645/3000]  | LR: 0.026706 | E: -63.309051 | E_img: -0.0082j E_var:     3.9493 E_err:   0.021957 | Acc: 0.2543
[2025-11-12 04:02:45] 03:16:18<10:26:42, 15.98s/it | [Iter  737/3090] R0[646/3000]  | LR: 0.026697 | E: -63.263316 | E_img: -0.0302j E_var:     4.1824 E_err:   0.022595 | Acc: 0.2494
[2025-11-12 04:03:00] 03:16:34<10:26:26, 15.98s/it | [Iter  738/3090] R0[647/3000]  | LR: 0.026687 | E: -63.339814 | E_img: +0.0002j E_var:     3.7600 E_err:   0.021424 | Acc: 0.2567
[2025-11-12 04:03:28] 03:17:01<10:26:46, 16.00s/it | [Iter  739/3090] R0[648/3000]  | LR: 0.026677 | E: -63.314831 | E_img: -0.0099j E_var:     4.2016 E_err:   0.022647 | Acc: 0.2521
[2025-11-12 04:03:44] 03:17:17<10:26:30, 16.00s/it | [Iter  740/3090] R0[649/3000]  | LR: 0.026667 | E: -63.310549 | E_img: -0.0027j E_var:     3.8878 E_err:   0.021785 | Acc: 0.2558
[2025-11-12 04:04:00] 03:17:33<10:26:13, 16.00s/it | [Iter  741/3090] R0[650/3000]  | LR: 0.026657 | E: -63.320440 | E_img: -0.0021j E_var:     4.0651 E_err:   0.022276 | Acc: 0.2520
[2025-11-12 04:04:15] 03:17:49<10:25:57, 16.00s/it | [Iter  742/3090] R0[651/3000]  | LR: 0.026647 | E: -63.361395 | E_img: +0.0151j E_var:     3.7603 E_err:   0.021425 | Acc: 0.2575
[2025-11-12 04:04:31] 03:18:04<10:25:40, 16.00s/it | [Iter  743/3090] R0[652/3000]  | LR: 0.026637 | E: -63.325040 | E_img: +0.0146j E_var:     3.8697 E_err:   0.021734 | Acc: 0.2612
[2025-11-12 04:04:47] 03:18:20<10:25:24, 15.99s/it | [Iter  744/3090] R0[653/3000]  | LR: 0.026627 | E: -63.303567 | E_img: +0.0004j E_var:     3.8496 E_err:   0.021678 | Acc: 0.2591
[2025-11-12 04:05:03] 03:18:36<10:25:08, 15.99s/it | [Iter  745/3090] R0[654/3000]  | LR: 0.026618 | E: -63.325266 | E_img: -0.0024j E_var:     4.0501 E_err:   0.022235 | Acc: 0.2475
[2025-11-12 04:05:19] 03:18:52<10:24:51, 15.99s/it | [Iter  746/3090] R0[655/3000]  | LR: 0.026608 | E: -63.330264 | E_img: -0.0158j E_var:     3.8541 E_err:   0.021690 | Acc: 0.2454
[2025-11-12 04:05:35] 03:19:08<10:24:36, 15.99s/it | [Iter  747/3090] R0[656/3000]  | LR: 0.026598 | E: -63.409486 | E_img: -0.0024j E_var:     3.7490 E_err:   0.021393 | Acc: 0.2549
[2025-11-12 04:06:03] 03:19:36<10:24:57, 16.01s/it | [Iter  748/3090] R0[657/3000]  | LR: 0.026588 | E: -63.342596 | E_img: +0.0020j E_var:     4.1070 E_err:   0.022391 | Acc: 0.2527
[2025-11-12 04:06:19] 03:19:52<10:24:41, 16.01s/it | [Iter  749/3090] R0[658/3000]  | LR: 0.026578 | E: -63.271174 | E_img: -0.0055j E_var:     4.4870 E_err:   0.023404 | Acc: 0.2399
[2025-11-12 04:06:35] 03:20:08<10:24:24, 16.01s/it | [Iter  750/3090] R0[659/3000]  | LR: 0.026568 | E: -63.357875 | E_img: +0.0183j E_var:     4.0516 E_err:   0.022239 | Acc: 0.2437
[2025-11-12 04:06:50] 03:20:24<10:24:08, 16.01s/it | [Iter  751/3090] R0[660/3000]  | LR: 0.026558 | E: -63.397747 | E_img: +0.0008j E_var:     3.6138 E_err:   0.021003 | Acc: 0.2603
[2025-11-12 04:07:06] 03:20:39<10:23:51, 16.01s/it | [Iter  752/3090] R0[661/3000]  | LR: 0.026548 | E: -63.382794 | E_img: -0.0049j E_var:     3.7895 E_err:   0.021508 | Acc: 0.2693
[2025-11-12 04:07:22] 03:20:55<10:23:35, 16.01s/it | [Iter  753/3090] R0[662/3000]  | LR: 0.026538 | E: -63.421011 | E_img: +0.0221j E_var:     3.8425 E_err:   0.021658 | Acc: 0.2799
[2025-11-12 04:07:38] 03:21:11<10:23:18, 16.01s/it | [Iter  754/3090] R0[663/3000]  | LR: 0.026528 | E: -63.367916 | E_img: -0.0124j E_var:     3.8142 E_err:   0.021578 | Acc: 0.2760
[2025-11-12 04:07:54] 03:21:27<10:23:02, 16.01s/it | [Iter  755/3090] R0[664/3000]  | LR: 0.026518 | E: -63.343518 | E_img: +0.0031j E_var:     3.8878 E_err:   0.021785 | Acc: 0.2678
[2025-11-12 04:08:10] 03:21:43<10:22:45, 16.01s/it | [Iter  756/3090] R0[665/3000]  | LR: 0.026507 | E: -63.391016 | E_img: +0.0059j E_var:     3.6786 E_err:   0.021191 | Acc: 0.2818
[2025-11-12 04:08:26] 03:21:59<10:22:29, 16.01s/it | [Iter  757/3090] R0[666/3000]  | LR: 0.026497 | E: -63.312601 | E_img: +0.0065j E_var:     3.7068 E_err:   0.021272 | Acc: 0.2902
[2025-11-12 04:08:41] 03:22:15<10:22:12, 16.01s/it | [Iter  758/3090] R0[667/3000]  | LR: 0.026487 | E: -63.349296 | E_img: -0.0005j E_var:     3.6947 E_err:   0.021237 | Acc: 0.2918
[2025-11-12 04:08:57] 03:22:30<10:21:56, 16.01s/it | [Iter  759/3090] R0[668/3000]  | LR: 0.026477 | E: -63.351558 | E_img: +0.0111j E_var:     3.6881 E_err:   0.021218 | Acc: 0.2904
[2025-11-12 04:09:13] 03:22:46<10:21:39, 16.01s/it | [Iter  760/3090] R0[669/3000]  | LR: 0.026467 | E: -63.352866 | E_img: +0.0206j E_var:     3.5820 E_err:   0.020911 | Acc: 0.2901
[2025-11-12 04:09:29] 03:23:02<10:21:23, 16.01s/it | [Iter  761/3090] R0[670/3000]  | LR: 0.026457 | E: -63.328558 | E_img: +0.0293j E_var:     3.7148 E_err:   0.021295 | Acc: 0.2919
[2025-11-12 04:09:45] 03:23:18<10:21:06, 16.01s/it | [Iter  762/3090] R0[671/3000]  | LR: 0.026447 | E: -63.382621 | E_img: +0.0216j E_var:     3.6340 E_err:   0.021062 | Acc: 0.2834
[2025-11-12 04:10:02] 03:23:35<10:20:54, 16.01s/it | [Iter  763/3090] R0[672/3000]  | LR: 0.026437 | E: -63.422606 | E_img: -0.0127j E_var:     3.5816 E_err:   0.020909 | Acc: 0.2779
[2025-11-12 04:10:30] 03:24:03<10:21:15, 16.03s/it | [Iter  764/3090] R0[673/3000]  | LR: 0.026426 | E: -63.425457 | E_img: +0.0106j E_var:     3.7726 E_err:   0.021460 | Acc: 0.2693
[2025-11-12 04:10:46] 03:24:19<10:20:58, 16.02s/it | [Iter  765/3090] R0[674/3000]  | LR: 0.026416 | E: -63.343182 | E_img: -0.0056j E_var:     3.7749 E_err:   0.021466 | Acc: 0.2694
[2025-11-12 04:11:02] 03:24:35<10:20:41, 16.02s/it | [Iter  766/3090] R0[675/3000]  | LR: 0.026406 | E: -63.387518 | E_img: -0.0024j E_var:     3.4954 E_err:   0.020656 | Acc: 0.2808
[2025-11-12 04:11:18] 03:24:51<10:20:25, 16.02s/it | [Iter  767/3090] R0[676/3000]  | LR: 0.026396 | E: -63.410765 | E_img: +0.0013j E_var:     3.8976 E_err:   0.021812 | Acc: 0.2710
[2025-11-12 04:11:34] 03:25:07<10:20:08, 16.02s/it | [Iter  768/3090] R0[677/3000]  | LR: 0.026386 | E: -63.426036 | E_img: -0.0216j E_var:     3.6177 E_err:   0.021015 | Acc: 0.2714
[2025-11-12 04:11:49] 03:25:22<10:19:52, 16.02s/it | [Iter  769/3090] R0[678/3000]  | LR: 0.026375 | E: -63.388477 | E_img: -0.0134j E_var:     3.4529 E_err:   0.020530 | Acc: 0.2818
[2025-11-12 04:12:05] 03:25:38<10:19:35, 16.02s/it | [Iter  770/3090] R0[679/3000]  | LR: 0.026365 | E: -63.407200 | E_img: +0.0021j E_var:     3.5066 E_err:   0.020689 | Acc: 0.2851
[2025-11-12 04:12:21] 03:25:54<10:19:19, 16.02s/it | [Iter  771/3090] R0[680/3000]  | LR: 0.026355 | E: -63.389220 | E_img: -0.0041j E_var:     3.5838 E_err:   0.020916 | Acc: 0.2887
[2025-11-12 04:12:37] 03:26:10<10:19:02, 16.02s/it | [Iter  772/3090] R0[681/3000]  | LR: 0.026345 | E: -63.378161 | E_img: -0.0044j E_var:     3.5653 E_err:   0.020862 | Acc: 0.2943
[2025-11-12 04:12:53] 03:26:26<10:18:46, 16.02s/it | [Iter  773/3090] R0[682/3000]  | LR: 0.026334 | E: -63.308292 | E_img: -0.0034j E_var:     3.6046 E_err:   0.020977 | Acc: 0.3028
[2025-11-12 04:13:09] 03:26:42<10:18:29, 16.02s/it | [Iter  774/3090] R0[683/3000]  | LR: 0.026324 | E: -63.417243 | E_img: +0.0047j E_var:     3.7673 E_err:   0.021445 | Acc: 0.2871
[2025-11-12 04:13:24] 03:26:58<10:18:12, 16.02s/it | [Iter  775/3090] R0[684/3000]  | LR: 0.026314 | E: -63.387102 | E_img: -0.0074j E_var:     3.4816 E_err:   0.020615 | Acc: 0.2855
[2025-11-12 04:13:40] 03:27:13<10:17:56, 16.02s/it | [Iter  776/3090] R0[685/3000]  | LR: 0.026303 | E: -63.331146 | E_img: +0.0107j E_var:     3.4553 E_err:   0.020538 | Acc: 0.3015
[2025-11-12 04:13:56] 03:27:29<10:17:39, 16.02s/it | [Iter  777/3090] R0[686/3000]  | LR: 0.026293 | E: -63.265856 | E_img: -0.0115j E_var:     3.6247 E_err:   0.021035 | Acc: 0.3130
[2025-11-12 04:14:12] 03:27:45<10:17:23, 16.02s/it | [Iter  778/3090] R0[687/3000]  | LR: 0.026283 | E: -63.304297 | E_img: +0.0151j E_var:     3.6848 E_err:   0.021209 | Acc: 0.3035
[2025-11-12 04:14:28] 03:28:01<10:17:06, 16.02s/it | [Iter  779/3090] R0[688/3000]  | LR: 0.026272 | E: -63.303763 | E_img: +0.0178j E_var:     3.8254 E_err:   0.021609 | Acc: 0.2906
[2025-11-12 04:14:44] 03:28:17<10:16:50, 16.02s/it | [Iter  780/3090] R0[689/3000]  | LR: 0.026262 | E: -63.384303 | E_img: -0.0091j E_var:     3.7368 E_err:   0.021358 | Acc: 0.2826
[2025-11-12 04:14:59] 03:28:33<10:16:33, 16.02s/it | [Iter  781/3090] R0[690/3000]  | LR: 0.026252 | E: -63.397141 | E_img: +0.0003j E_var:     3.8636 E_err:   0.021717 | Acc: 0.2713
[2025-11-12 04:15:15] 03:28:48<10:16:16, 16.02s/it | [Iter  782/3090] R0[691/3000]  | LR: 0.026241 | E: -63.412304 | E_img: +0.0106j E_var:     3.5665 E_err:   0.020865 | Acc: 0.2798
[2025-11-12 04:15:31] 03:29:04<10:16:00, 16.02s/it | [Iter  783/3090] R0[692/3000]  | LR: 0.026231 | E: -63.298111 | E_img: +0.0060j E_var:     3.6592 E_err:   0.021135 | Acc: 0.2973
[2025-11-12 04:15:47] 03:29:20<10:15:43, 16.02s/it | [Iter  784/3090] R0[693/3000]  | LR: 0.026220 | E: -63.275361 | E_img: +0.0164j E_var:     3.8389 E_err:   0.021648 | Acc: 0.2882
[2025-11-12 04:16:03] 03:29:36<10:15:27, 16.02s/it | [Iter  785/3090] R0[694/3000]  | LR: 0.026210 | E: -63.227839 | E_img: +0.0013j E_var:     3.6246 E_err:   0.021035 | Acc: 0.2969
[2025-11-12 04:16:19] 03:29:52<10:15:10, 16.02s/it | [Iter  786/3090] R0[695/3000]  | LR: 0.026200 | E: -63.280399 | E_img: -0.0110j E_var:     3.6354 E_err:   0.021066 | Acc: 0.3002
[2025-11-12 04:16:34] 03:30:08<10:14:54, 16.02s/it | [Iter  787/3090] R0[696/3000]  | LR: 0.026189 | E: -63.284378 | E_img: -0.0443j E_var:     3.5147 E_err:   0.020713 | Acc: 0.2991
[2025-11-12 04:16:50] 03:30:23<10:14:37, 16.02s/it | [Iter  788/3090] R0[697/3000]  | LR: 0.026179 | E: -63.306896 | E_img: +0.0173j E_var:     4.0044 E_err:   0.022109 | Acc: 0.2837
[2025-11-12 04:17:06] 03:30:39<10:14:21, 16.02s/it | [Iter  789/3090] R0[698/3000]  | LR: 0.026168 | E: -63.338457 | E_img: +0.0197j E_var:     3.4875 E_err:   0.020633 | Acc: 0.2824
[2025-11-12 04:17:22] 03:30:55<10:14:04, 16.02s/it | [Iter  790/3090] R0[699/3000]  | LR: 0.026158 | E: -63.268682 | E_img: -0.0032j E_var:     3.5996 E_err:   0.020962 | Acc: 0.2903
[2025-11-12 04:17:38] 03:31:11<10:13:48, 16.02s/it | [Iter  791/3090] R0[700/3000]  | LR: 0.026147 | E: -63.363544 | E_img: -0.0062j E_var:     3.5648 E_err:   0.020860 | Acc: 0.2854
[2025-11-12 04:17:54] 03:31:27<10:13:31, 16.02s/it | [Iter  792/3090] R0[701/3000]  | LR: 0.026137 | E: -63.381948 | E_img: +0.0038j E_var:     3.4922 E_err:   0.020647 | Acc: 0.2866
[2025-11-12 04:18:10] 03:31:43<10:13:14, 16.02s/it | [Iter  793/3090] R0[702/3000]  | LR: 0.026126 | E: -63.406605 | E_img: -0.0140j E_var:     3.6642 E_err:   0.021149 | Acc: 0.2834
[2025-11-12 04:18:25] 03:31:58<10:12:58, 16.02s/it | [Iter  794/3090] R0[703/3000]  | LR: 0.026116 | E: -63.401447 | E_img: -0.0079j E_var:     3.6073 E_err:   0.020984 | Acc: 0.2763
[2025-11-12 04:18:41] 03:32:14<10:12:41, 16.02s/it | [Iter  795/3090] R0[704/3000]  | LR: 0.026105 | E: -63.404709 | E_img: +0.0065j E_var:     3.4684 E_err:   0.020576 | Acc: 0.2759
[2025-11-12 04:18:57] 03:32:30<10:12:25, 16.02s/it | [Iter  796/3090] R0[705/3000]  | LR: 0.026094 | E: -63.423238 | E_img: -0.0062j E_var:     3.8677 E_err:   0.021729 | Acc: 0.2732
[2025-11-12 04:19:13] 03:32:46<10:12:08, 16.02s/it | [Iter  797/3090] R0[706/3000]  | LR: 0.026084 | E: -63.377713 | E_img: +0.0156j E_var:     3.6563 E_err:   0.021126 | Acc: 0.2697
[2025-11-12 04:19:29] 03:33:02<10:11:52, 16.02s/it | [Iter  798/3090] R0[707/3000]  | LR: 0.026073 | E: -63.438226 | E_img: -0.0007j E_var:     3.4096 E_err:   0.020401 | Acc: 0.2793
[2025-11-12 04:19:45] 03:33:18<10:11:35, 16.02s/it | [Iter  799/3090] R0[708/3000]  | LR: 0.026063 | E: -63.396983 | E_img: -0.0008j E_var:     3.3984 E_err:   0.020368 | Acc: 0.2927
[2025-11-12 04:20:00] 03:33:34<10:11:19, 16.02s/it | [Iter  800/3090] R0[709/3000]  | LR: 0.026052 | E: -63.388777 | E_img: -0.0282j E_var:     3.7758 E_err:   0.021469 | Acc: 0.2802
[2025-11-12 04:20:16] 03:33:49<10:11:02, 16.02s/it | [Iter  801/3090] R0[710/3000]  | LR: 0.026041 | E: -63.392111 | E_img: -0.0131j E_var:     4.0247 E_err:   0.022165 | Acc: 0.2634
[2025-11-12 04:20:32] 03:34:05<10:10:46, 16.02s/it | [Iter  802/3090] R0[711/3000]  | LR: 0.026031 | E: -63.383646 | E_img: +0.0032j E_var:     4.1186 E_err:   0.022422 | Acc: 0.2540
[2025-11-12 04:20:48] 03:34:21<10:10:29, 16.02s/it | [Iter  803/3090] R0[712/3000]  | LR: 0.026020 | E: -63.324162 | E_img: -0.0023j E_var:     4.5177 E_err:   0.023484 | Acc: 0.2428
[2025-11-12 04:21:04] 03:34:37<10:10:13, 16.02s/it | [Iter  804/3090] R0[713/3000]  | LR: 0.026010 | E: -63.387406 | E_img: +0.0130j E_var:     3.5675 E_err:   0.020868 | Acc: 0.2533
[2025-11-12 04:21:20] 03:34:53<10:09:56, 16.02s/it | [Iter  805/3090] R0[714/3000]  | LR: 0.025999 | E: -63.353977 | E_img: +0.0215j E_var:     3.9005 E_err:   0.021821 | Acc: 0.2600
[2025-11-12 04:21:36] 03:35:09<10:09:40, 16.02s/it | [Iter  806/3090] R0[715/3000]  | LR: 0.025988 | E: -63.336182 | E_img: -0.0151j E_var:     4.2064 E_err:   0.022660 | Acc: 0.2541
[2025-11-12 04:21:51] 03:35:24<10:09:23, 16.02s/it | [Iter  807/3090] R0[716/3000]  | LR: 0.025977 | E: -63.386496 | E_img: +0.0191j E_var:     3.6733 E_err:   0.021175 | Acc: 0.2679
[2025-11-12 04:22:07] 03:35:40<10:09:07, 16.02s/it | [Iter  808/3090] R0[717/3000]  | LR: 0.025967 | E: -63.386622 | E_img: +0.0173j E_var:     3.6243 E_err:   0.021034 | Acc: 0.2891
[2025-11-12 04:22:23] 03:35:56<10:08:50, 16.02s/it | [Iter  809/3090] R0[718/3000]  | LR: 0.025956 | E: -63.418498 | E_img: +0.0102j E_var:     3.8803 E_err:   0.021764 | Acc: 0.2844
[2025-11-12 04:22:39] 03:36:12<10:08:34, 16.01s/it | [Iter  810/3090] R0[719/3000]  | LR: 0.025945 | E: -63.406100 | E_img: +0.0092j E_var:     3.8706 E_err:   0.021737 | Acc: 0.2756
[2025-11-12 04:22:55] 03:36:28<10:08:17, 16.01s/it | [Iter  811/3090] R0[720/3000]  | LR: 0.025935 | E: -63.400653 | E_img: -0.0107j E_var:     3.6457 E_err:   0.021096 | Acc: 0.2743
[2025-11-12 04:23:11] 03:36:44<10:08:01, 16.01s/it | [Iter  812/3090] R0[721/3000]  | LR: 0.025924 | E: -63.465421 | E_img: -0.0116j E_var:     3.7154 E_err:   0.021296 | Acc: 0.2742
[2025-11-12 04:23:26] 03:37:00<10:07:44, 16.01s/it | [Iter  813/3090] R0[722/3000]  | LR: 0.025913 | E: -63.464225 | E_img: +0.0059j E_var:     3.6230 E_err:   0.021030 | Acc: 0.2784
[2025-11-12 04:23:42] 03:37:15<10:07:28, 16.01s/it | [Iter  814/3090] R0[723/3000]  | LR: 0.025902 | E: -63.440401 | E_img: +0.0144j E_var:     3.5012 E_err:   0.020673 | Acc: 0.2793
[2025-11-12 04:23:58] 03:37:31<10:07:11, 16.01s/it | [Iter  815/3090] R0[724/3000]  | LR: 0.025891 | E: -63.434420 | E_img: -0.0155j E_var:     3.6882 E_err:   0.021218 | Acc: 0.2747
[2025-11-12 04:24:14] 03:37:47<10:06:55, 16.01s/it | [Iter  816/3090] R0[725/3000]  | LR: 0.025881 | E: -63.429266 | E_img: -0.0065j E_var:     3.7164 E_err:   0.021299 | Acc: 0.2746
[2025-11-12 04:24:30] 03:38:03<10:06:38, 16.01s/it | [Iter  817/3090] R0[726/3000]  | LR: 0.025870 | E: -63.390132 | E_img: -0.0121j E_var:     3.9178 E_err:   0.021869 | Acc: 0.2661
[2025-11-12 04:24:46] 03:38:19<10:06:22, 16.01s/it | [Iter  818/3090] R0[727/3000]  | LR: 0.025859 | E: -63.387850 | E_img: -0.0150j E_var:     3.9334 E_err:   0.021912 | Acc: 0.2604
[2025-11-12 04:25:01] 03:38:35<10:06:05, 16.01s/it | [Iter  819/3090] R0[728/3000]  | LR: 0.025848 | E: -63.333951 | E_img: -0.0146j E_var:     4.4576 E_err:   0.023327 | Acc: 0.2448
[2025-11-12 04:25:17] 03:38:50<10:05:49, 16.01s/it | [Iter  820/3090] R0[729/3000]  | LR: 0.025837 | E: -63.364936 | E_img: -0.0160j E_var:     4.2700 E_err:   0.022831 | Acc: 0.2408
[2025-11-12 04:25:33] 03:39:06<10:05:32, 16.01s/it | [Iter  821/3090] R0[730/3000]  | LR: 0.025826 | E: -63.327782 | E_img: -0.0179j E_var:     4.3797 E_err:   0.023122 | Acc: 0.2397
[2025-11-12 04:25:49] 03:39:22<10:05:16, 16.01s/it | [Iter  822/3090] R0[731/3000]  | LR: 0.025816 | E: -63.262784 | E_img: -0.0171j E_var:     4.5953 E_err:   0.023684 | Acc: 0.2347
[2025-11-12 04:26:05] 03:39:38<10:04:59, 16.01s/it | [Iter  823/3090] R0[732/3000]  | LR: 0.025805 | E: -63.174990 | E_img: +0.0046j E_var:     4.6169 E_err:   0.023740 | Acc: 0.2338
[2025-11-12 04:26:21] 03:39:54<10:04:43, 16.01s/it | [Iter  824/3090] R0[733/3000]  | LR: 0.025794 | E: -63.228366 | E_img: +0.0313j E_var:     4.2384 E_err:   0.022746 | Acc: 0.2384
[2025-11-12 04:26:36] 03:40:10<10:04:26, 16.01s/it | [Iter  825/3090] R0[734/3000]  | LR: 0.025783 | E: -63.259162 | E_img: +0.0362j E_var:     4.0111 E_err:   0.022128 | Acc: 0.2462
[2025-11-12 04:26:52] 03:40:25<10:04:10, 16.01s/it | [Iter  826/3090] R0[735/3000]  | LR: 0.025772 | E: -63.320842 | E_img: -0.0089j E_var:     4.5741 E_err:   0.023630 | Acc: 0.2369
[2025-11-12 04:27:08] 03:40:41<10:03:53, 16.01s/it | [Iter  827/3090] R0[736/3000]  | LR: 0.025761 | E: -63.301009 | E_img: -0.0018j E_var:     4.3782 E_err:   0.023118 | Acc: 0.2318
[2025-11-12 04:27:24] 03:40:57<10:03:37, 16.01s/it | [Iter  828/3090] R0[737/3000]  | LR: 0.025750 | E: -63.345544 | E_img: +0.0078j E_var:     3.8827 E_err:   0.021771 | Acc: 0.2413
[2025-11-12 04:27:40] 03:41:13<10:03:20, 16.01s/it | [Iter  829/3090] R0[738/3000]  | LR: 0.025739 | E: -63.290965 | E_img: -0.0119j E_var:     4.0463 E_err:   0.022225 | Acc: 0.2478
[2025-11-12 04:27:56] 03:41:29<10:03:04, 16.01s/it | [Iter  830/3090] R0[739/3000]  | LR: 0.025728 | E: -63.305457 | E_img: +0.0081j E_var:     3.9167 E_err:   0.021866 | Acc: 0.2521
[2025-11-12 04:28:12] 03:41:45<10:02:47, 16.01s/it | [Iter  831/3090] R0[740/3000]  | LR: 0.025717 | E: -63.364059 | E_img: +0.0046j E_var:     3.7955 E_err:   0.021525 | Acc: 0.2587
[2025-11-12 04:28:27] 03:42:00<10:02:31, 16.01s/it | [Iter  832/3090] R0[741/3000]  | LR: 0.025706 | E: -63.360103 | E_img: +0.0026j E_var:     3.7921 E_err:   0.021515 | Acc: 0.2629
[2025-11-12 04:28:43] 03:42:16<10:02:15, 16.01s/it | [Iter  833/3090] R0[742/3000]  | LR: 0.025695 | E: -63.283149 | E_img: +0.0083j E_var:     3.8672 E_err:   0.021727 | Acc: 0.2668
[2025-11-12 04:28:59] 03:42:32<10:01:58, 16.01s/it | [Iter  834/3090] R0[743/3000]  | LR: 0.025684 | E: -63.393840 | E_img: -0.0072j E_var:     4.0212 E_err:   0.022156 | Acc: 0.2630
[2025-11-12 04:29:15] 03:42:48<10:01:42, 16.01s/it | [Iter  835/3090] R0[744/3000]  | LR: 0.025673 | E: -63.346693 | E_img: +0.0189j E_var:     3.6835 E_err:   0.021205 | Acc: 0.2697
[2025-11-12 04:29:31] 03:43:04<10:01:25, 16.01s/it | [Iter  836/3090] R0[745/3000]  | LR: 0.025662 | E: -63.271646 | E_img: +0.0071j E_var:     3.8642 E_err:   0.021719 | Acc: 0.2717
[2025-11-12 04:29:47] 03:43:20<10:01:09, 16.01s/it | [Iter  837/3090] R0[746/3000]  | LR: 0.025651 | E: -63.248968 | E_img: +0.0086j E_var:     3.6989 E_err:   0.021249 | Acc: 0.2796
[2025-11-12 04:30:02] 03:43:36<10:00:52, 16.01s/it | [Iter  838/3090] R0[747/3000]  | LR: 0.025640 | E: -63.266373 | E_img: +0.0072j E_var:     3.6481 E_err:   0.021103 | Acc: 0.2859
[2025-11-12 04:30:18] 03:43:51<10:00:36, 16.01s/it | [Iter  839/3090] R0[748/3000]  | LR: 0.025629 | E: -63.316031 | E_img: +0.0077j E_var:     3.6777 E_err:   0.021188 | Acc: 0.2842
[2025-11-12 04:30:34] 03:44:07<10:00:19, 16.01s/it | [Iter  840/3090] R0[749/3000]  | LR: 0.025618 | E: -63.380082 | E_img: -0.0119j E_var:     3.5076 E_err:   0.020692 | Acc: 0.2849
[2025-11-12 04:30:50] 03:44:23<10:00:03, 16.01s/it | [Iter  841/3090] R0[750/3000]  | LR: 0.025607 | E: -63.401887 | E_img: -0.0052j E_var:     3.6867 E_err:   0.021214 | Acc: 0.2794
[2025-11-12 04:31:06] 03:44:39<09:59:46, 16.01s/it | [Iter  842/3090] R0[751/3000]  | LR: 0.025596 | E: -63.424610 | E_img: +0.0056j E_var:     4.0902 E_err:   0.022345 | Acc: 0.2623
[2025-11-12 04:31:22] 03:44:55<09:59:30, 16.01s/it | [Iter  843/3090] R0[752/3000]  | LR: 0.025584 | E: -63.396435 | E_img: +0.0157j E_var:     3.5723 E_err:   0.020882 | Acc: 0.2604
[2025-11-12 04:31:37] 03:45:11<09:59:13, 16.01s/it | [Iter  844/3090] R0[753/3000]  | LR: 0.025573 | E: -63.427198 | E_img: +0.0003j E_var:     3.5080 E_err:   0.020693 | Acc: 0.2684
[2025-11-12 04:31:53] 03:45:26<09:58:57, 16.01s/it | [Iter  845/3090] R0[754/3000]  | LR: 0.025562 | E: -63.435124 | E_img: -0.0049j E_var:     3.8327 E_err:   0.021630 | Acc: 0.2709
[2025-11-12 04:32:09] 03:45:42<09:58:41, 16.01s/it | [Iter  846/3090] R0[755/3000]  | LR: 0.025551 | E: -63.412814 | E_img: -0.0021j E_var:     3.5977 E_err:   0.020956 | Acc: 0.2713
[2025-11-12 04:32:25] 03:45:58<09:58:24, 16.01s/it | [Iter  847/3090] R0[756/3000]  | LR: 0.025540 | E: -63.396570 | E_img: -0.0031j E_var:     3.9928 E_err:   0.022077 | Acc: 0.2631
[2025-11-12 04:32:41] 03:46:14<09:58:08, 16.01s/it | [Iter  848/3090] R0[757/3000]  | LR: 0.025529 | E: -63.408149 | E_img: -0.0018j E_var:     3.6319 E_err:   0.021056 | Acc: 0.2685
[2025-11-12 04:32:57] 03:46:30<09:57:51, 16.01s/it | [Iter  849/3090] R0[758/3000]  | LR: 0.025517 | E: -63.425377 | E_img: -0.0049j E_var:     3.6100 E_err:   0.020992 | Acc: 0.2744
[2025-11-12 04:33:13] 03:46:46<09:57:35, 16.01s/it | [Iter  850/3090] R0[759/3000]  | LR: 0.025506 | E: -63.369984 | E_img: +0.0107j E_var:     3.8126 E_err:   0.021573 | Acc: 0.2698
[2025-11-12 04:33:28] 03:47:01<09:57:18, 16.01s/it | [Iter  851/3090] R0[760/3000]  | LR: 0.025495 | E: -63.451804 | E_img: +0.0046j E_var:     3.5708 E_err:   0.020878 | Acc: 0.2842
[2025-11-12 04:33:44] 03:47:17<09:57:02, 16.01s/it | [Iter  852/3090] R0[761/3000]  | LR: 0.025484 | E: -63.410420 | E_img: -0.0053j E_var:     4.0263 E_err:   0.022170 | Acc: 0.2688
[2025-11-12 04:34:00] 03:47:33<09:56:46, 16.01s/it | [Iter  853/3090] R0[762/3000]  | LR: 0.025472 | E: -63.429482 | E_img: -0.0008j E_var:     3.6352 E_err:   0.021065 | Acc: 0.2586
[2025-11-12 04:34:16] 03:47:49<09:56:29, 16.01s/it | [Iter  854/3090] R0[763/3000]  | LR: 0.025461 | E: -63.473123 | E_img: -0.0035j E_var:     3.7281 E_err:   0.021333 | Acc: 0.2618
[2025-11-12 04:34:32] 03:48:05<09:56:13, 16.01s/it | [Iter  855/3090] R0[764/3000]  | LR: 0.025450 | E: -63.465770 | E_img: -0.0077j E_var:     3.6379 E_err:   0.021073 | Acc: 0.2685
[2025-11-12 04:34:48] 03:48:21<09:55:56, 16.01s/it | [Iter  856/3090] R0[765/3000]  | LR: 0.025439 | E: -63.454982 | E_img: +0.0121j E_var:     3.5153 E_err:   0.020715 | Acc: 0.2830
[2025-11-12 04:35:03] 03:48:37<09:55:40, 16.01s/it | [Iter  857/3090] R0[766/3000]  | LR: 0.025427 | E: -63.446712 | E_img: +0.0079j E_var:     3.5161 E_err:   0.020718 | Acc: 0.2915
[2025-11-12 04:35:19] 03:48:52<09:55:23, 16.01s/it | [Iter  858/3090] R0[767/3000]  | LR: 0.025416 | E: -63.468680 | E_img: -0.0218j E_var:     3.4991 E_err:   0.020667 | Acc: 0.2869
[2025-11-12 04:35:35] 03:49:08<09:55:07, 16.00s/it | [Iter  859/3090] R0[768/3000]  | LR: 0.025405 | E: -63.491599 | E_img: -0.0016j E_var:     3.5188 E_err:   0.020725 | Acc: 0.2882
[2025-11-12 04:35:51] 03:49:24<09:54:50, 16.00s/it | [Iter  860/3090] R0[769/3000]  | LR: 0.025393 | E: -63.432099 | E_img: -0.0090j E_var:     3.4172 E_err:   0.020424 | Acc: 0.2944
[2025-11-12 04:36:07] 03:49:40<09:54:34, 16.00s/it | [Iter  861/3090] R0[770/3000]  | LR: 0.025382 | E: -63.462281 | E_img: -0.0011j E_var:     3.5587 E_err:   0.020842 | Acc: 0.2889
[2025-11-12 04:36:23] 03:49:56<09:54:18, 16.00s/it | [Iter  862/3090] R0[771/3000]  | LR: 0.025371 | E: -63.409032 | E_img: +0.0074j E_var:     3.4155 E_err:   0.020419 | Acc: 0.2953
[2025-11-12 04:36:38] 03:50:12<09:54:01, 16.00s/it | [Iter  863/3090] R0[772/3000]  | LR: 0.025359 | E: -63.473431 | E_img: +0.0006j E_var:     3.3739 E_err:   0.020294 | Acc: 0.2942
[2025-11-12 04:36:54] 03:50:27<09:53:45, 16.00s/it | [Iter  864/3090] R0[773/3000]  | LR: 0.025348 | E: -63.441458 | E_img: +0.0069j E_var:     3.5454 E_err:   0.020803 | Acc: 0.2886
[2025-11-12 04:37:10] 03:50:43<09:53:28, 16.00s/it | [Iter  865/3090] R0[774/3000]  | LR: 0.025337 | E: -63.473837 | E_img: -0.0036j E_var:     3.4318 E_err:   0.020468 | Acc: 0.2880
[2025-11-12 04:37:26] 03:50:59<09:53:12, 16.00s/it | [Iter  866/3090] R0[775/3000]  | LR: 0.025325 | E: -63.461809 | E_img: +0.0146j E_var:     3.5209 E_err:   0.020732 | Acc: 0.2797
[2025-11-12 04:37:42] 03:51:15<09:52:55, 16.00s/it | [Iter  867/3090] R0[776/3000]  | LR: 0.025314 | E: -63.463268 | E_img: -0.0105j E_var:     3.7282 E_err:   0.021333 | Acc: 0.2666
[2025-11-12 04:37:58] 03:51:31<09:52:39, 16.00s/it | [Iter  868/3090] R0[777/3000]  | LR: 0.025303 | E: -63.430430 | E_img: +0.0120j E_var:     3.5691 E_err:   0.020873 | Acc: 0.2681
[2025-11-12 04:38:13] 03:51:47<09:52:23, 16.00s/it | [Iter  869/3090] R0[778/3000]  | LR: 0.025291 | E: -63.433818 | E_img: -0.0254j E_var:     3.7020 E_err:   0.021258 | Acc: 0.2687
[2025-11-12 04:38:29] 03:52:02<09:52:06, 16.00s/it | [Iter  870/3090] R0[779/3000]  | LR: 0.025280 | E: -63.398791 | E_img: +0.0100j E_var:     3.5271 E_err:   0.020750 | Acc: 0.2701
[2025-11-12 04:38:45] 03:52:18<09:51:50, 16.00s/it | [Iter  871/3090] R0[780/3000]  | LR: 0.025268 | E: -63.449622 | E_img: -0.0099j E_var:     3.4909 E_err:   0.020643 | Acc: 0.2818
[2025-11-12 04:39:01] 03:52:34<09:51:33, 16.00s/it | [Iter  872/3090] R0[781/3000]  | LR: 0.025257 | E: -63.389360 | E_img: +0.0017j E_var:     3.4600 E_err:   0.020551 | Acc: 0.2909
[2025-11-12 04:39:17] 03:52:50<09:51:17, 16.00s/it | [Iter  873/3090] R0[782/3000]  | LR: 0.025245 | E: -63.411112 | E_img: +0.0040j E_var:     3.7297 E_err:   0.021337 | Acc: 0.2780
[2025-11-12 04:39:33] 03:53:06<09:51:01, 16.00s/it | [Iter  874/3090] R0[783/3000]  | LR: 0.025234 | E: -63.413804 | E_img: -0.0056j E_var:     3.6085 E_err:   0.020988 | Acc: 0.2696
[2025-11-12 04:39:49] 03:53:22<09:50:44, 16.00s/it | [Iter  875/3090] R0[784/3000]  | LR: 0.025222 | E: -63.464562 | E_img: -0.0289j E_var:     3.4827 E_err:   0.020619 | Acc: 0.2718
[2025-11-12 04:40:04] 03:53:37<09:50:28, 16.00s/it | [Iter  876/3090] R0[785/3000]  | LR: 0.025211 | E: -63.447636 | E_img: +0.0121j E_var:     3.3900 E_err:   0.020343 | Acc: 0.2780
[2025-11-12 04:40:20] 03:53:53<09:50:11, 16.00s/it | [Iter  877/3090] R0[786/3000]  | LR: 0.025199 | E: -63.457340 | E_img: +0.0063j E_var:     3.5221 E_err:   0.020735 | Acc: 0.2814
[2025-11-12 04:40:36] 03:54:09<09:49:55, 16.00s/it | [Iter  878/3090] R0[787/3000]  | LR: 0.025188 | E: -63.433401 | E_img: -0.0084j E_var:     3.3589 E_err:   0.020249 | Acc: 0.2819
[2025-11-12 04:40:52] 03:54:25<09:49:38, 16.00s/it | [Iter  879/3090] R0[788/3000]  | LR: 0.025176 | E: -63.455688 | E_img: -0.0006j E_var:     3.4553 E_err:   0.020538 | Acc: 0.2807
[2025-11-12 04:41:08] 03:54:41<09:49:22, 16.00s/it | [Iter  880/3090] R0[789/3000]  | LR: 0.025165 | E: -63.480648 | E_img: -0.0182j E_var:     3.6398 E_err:   0.021079 | Acc: 0.2696
[2025-11-12 04:41:24] 03:54:57<09:49:06, 16.00s/it | [Iter  881/3090] R0[790/3000]  | LR: 0.025153 | E: -63.499363 | E_img: +0.0101j E_var:     3.4736 E_err:   0.020592 | Acc: 0.2714
[2025-11-12 04:41:39] 03:55:13<09:48:49, 16.00s/it | [Iter  882/3090] R0[791/3000]  | LR: 0.025142 | E: -63.451001 | E_img: -0.0066j E_var:     3.3837 E_err:   0.020323 | Acc: 0.2854
[2025-11-12 04:41:55] 03:55:28<09:48:33, 16.00s/it | [Iter  883/3090] R0[792/3000]  | LR: 0.025130 | E: -63.433524 | E_img: +0.0061j E_var:     3.3626 E_err:   0.020260 | Acc: 0.2886
[2025-11-12 04:42:11] 03:55:44<09:48:16, 16.00s/it | [Iter  884/3090] R0[793/3000]  | LR: 0.025118 | E: -63.438321 | E_img: +0.0078j E_var:     3.8601 E_err:   0.021707 | Acc: 0.2741
[2025-11-12 04:42:27] 03:56:00<09:48:00, 16.00s/it | [Iter  885/3090] R0[794/3000]  | LR: 0.025107 | E: -63.485297 | E_img: +0.0061j E_var:     3.6280 E_err:   0.021044 | Acc: 0.2701
[2025-11-12 04:42:43] 03:56:16<09:47:44, 16.00s/it | [Iter  886/3090] R0[795/3000]  | LR: 0.025095 | E: -63.402565 | E_img: -0.0010j E_var:     3.6365 E_err:   0.021069 | Acc: 0.2671
[2025-11-12 04:42:59] 03:56:32<09:47:27, 16.00s/it | [Iter  887/3090] R0[796/3000]  | LR: 0.025084 | E: -63.458251 | E_img: -0.0083j E_var:     3.7119 E_err:   0.021286 | Acc: 0.2623
[2025-11-12 04:43:14] 03:56:48<09:47:11, 16.00s/it | [Iter  888/3090] R0[797/3000]  | LR: 0.025072 | E: -63.364139 | E_img: -0.0050j E_var:     3.7594 E_err:   0.021422 | Acc: 0.2600
[2025-11-12 04:43:30] 03:57:03<09:46:54, 16.00s/it | [Iter  889/3090] R0[798/3000]  | LR: 0.025060 | E: -63.397928 | E_img: -0.0054j E_var:     3.5806 E_err:   0.020907 | Acc: 0.2617
[2025-11-12 04:43:46] 03:57:19<09:46:38, 16.00s/it | [Iter  890/3090] R0[799/3000]  | LR: 0.025049 | E: -63.418431 | E_img: +0.0000j E_var:     3.9257 E_err:   0.021891 | Acc: 0.2579
[2025-11-12 04:44:02] 03:57:35<09:46:22, 16.00s/it | [Iter  891/3090] R0[800/3000]  | LR: 0.025037 | E: -63.450879 | E_img: -0.0074j E_var:     3.5349 E_err:   0.020773 | Acc: 0.2667
[2025-11-12 04:44:18] 03:57:51<09:46:05, 16.00s/it | [Iter  892/3090] R0[801/3000]  | LR: 0.025025 | E: -63.431066 | E_img: +0.0066j E_var:     3.7582 E_err:   0.021419 | Acc: 0.2629
[2025-11-12 04:44:34] 03:58:07<09:45:49, 16.00s/it | [Iter  893/3090] R0[802/3000]  | LR: 0.025014 | E: -63.399755 | E_img: -0.0149j E_var:     3.5409 E_err:   0.020790 | Acc: 0.2771
[2025-11-12 04:44:49] 03:58:23<09:45:32, 16.00s/it | [Iter  894/3090] R0[803/3000]  | LR: 0.025002 | E: -63.339237 | E_img: -0.0074j E_var:     3.7742 E_err:   0.021464 | Acc: 0.2871
[2025-11-12 04:45:05] 03:58:38<09:45:16, 16.00s/it | [Iter  895/3090] R0[804/3000]  | LR: 0.024990 | E: -63.277951 | E_img: -0.0255j E_var:     3.7963 E_err:   0.021527 | Acc: 0.2860
[2025-11-12 04:45:21] 03:58:54<09:45:00, 16.00s/it | [Iter  896/3090] R0[805/3000]  | LR: 0.024978 | E: -63.315326 | E_img: -0.0195j E_var:     3.7312 E_err:   0.021342 | Acc: 0.2804
[2025-11-12 04:45:37] 03:59:10<09:44:43, 16.00s/it | [Iter  897/3090] R0[806/3000]  | LR: 0.024967 | E: -63.371467 | E_img: -0.0122j E_var:     3.7804 E_err:   0.021482 | Acc: 0.2719
[2025-11-12 04:45:53] 03:59:26<09:44:27, 16.00s/it | [Iter  898/3090] R0[807/3000]  | LR: 0.024955 | E: -63.435492 | E_img: -0.0162j E_var:     3.4497 E_err:   0.020521 | Acc: 0.2768
[2025-11-12 04:46:09] 03:59:42<09:44:11, 16.00s/it | [Iter  899/3090] R0[808/3000]  | LR: 0.024943 | E: -63.360347 | E_img: -0.0099j E_var:     3.5767 E_err:   0.020895 | Acc: 0.2798
[2025-11-12 04:46:24] 03:59:58<09:43:54, 16.00s/it | [Iter  900/3090] R0[809/3000]  | LR: 0.024931 | E: -63.375461 | E_img: -0.0103j E_var:     3.5959 E_err:   0.020951 | Acc: 0.3005
[2025-11-12 04:46:40] 04:00:13<09:43:38, 16.00s/it | [Iter  901/3090] R0[810/3000]  | LR: 0.024920 | E: -63.339551 | E_img: -0.0062j E_var:     3.5797 E_err:   0.020904 | Acc: 0.3014
[2025-11-12 04:46:56] 04:00:29<09:43:21, 16.00s/it | [Iter  902/3090] R0[811/3000]  | LR: 0.024908 | E: -63.347945 | E_img: +0.0049j E_var:     3.3943 E_err:   0.020355 | Acc: 0.3006
[2025-11-12 04:47:12] 04:00:45<09:43:05, 16.00s/it | [Iter  903/3090] R0[812/3000]  | LR: 0.024896 | E: -63.360438 | E_img: +0.0018j E_var:     3.4485 E_err:   0.020517 | Acc: 0.2989
[2025-11-12 04:47:28] 04:01:01<09:42:49, 16.00s/it | [Iter  904/3090] R0[813/3000]  | LR: 0.024884 | E: -63.404998 | E_img: -0.0065j E_var:     3.4484 E_err:   0.020517 | Acc: 0.2906
[2025-11-12 04:47:44] 04:01:17<09:42:32, 16.00s/it | [Iter  905/3090] R0[814/3000]  | LR: 0.024872 | E: -63.396169 | E_img: +0.0052j E_var:     3.3581 E_err:   0.020247 | Acc: 0.2966
[2025-11-12 04:47:59] 04:01:33<09:42:16, 16.00s/it | [Iter  906/3090] R0[815/3000]  | LR: 0.024861 | E: -63.266333 | E_img: -0.0460j E_var:     3.4868 E_err:   0.020631 | Acc: 0.3108
[2025-11-12 04:48:15] 04:01:48<09:41:59, 16.00s/it | [Iter  907/3090] R0[816/3000]  | LR: 0.024849 | E: -63.245893 | E_img: -0.0015j E_var:     3.5205 E_err:   0.020730 | Acc: 0.3159
[2025-11-12 04:48:31] 04:02:04<09:41:43, 16.00s/it | [Iter  908/3090] R0[817/3000]  | LR: 0.024837 | E: -63.294949 | E_img: -0.0104j E_var:     3.3989 E_err:   0.020369 | Acc: 0.3067
[2025-11-12 04:48:47] 04:02:20<09:41:27, 16.00s/it | [Iter  909/3090] R0[818/3000]  | LR: 0.024825 | E: -63.405763 | E_img: +0.0049j E_var:     3.7178 E_err:   0.021303 | Acc: 0.2845
[2025-11-12 04:49:03] 04:02:36<09:41:10, 16.00s/it | [Iter  910/3090] R0[819/3000]  | LR: 0.024813 | E: -63.441744 | E_img: -0.0086j E_var:     3.1206 E_err:   0.019517 | Acc: 0.2889
[2025-11-12 04:49:19] 04:02:52<09:40:54, 16.00s/it | [Iter  911/3090] R0[820/3000]  | LR: 0.024801 | E: -63.406671 | E_img: +0.0124j E_var:     3.3403 E_err:   0.020193 | Acc: 0.2960
[2025-11-12 04:49:34] 04:03:08<09:40:38, 16.00s/it | [Iter  912/3090] R0[821/3000]  | LR: 0.024789 | E: -63.375588 | E_img: +0.0196j E_var:     3.3266 E_err:   0.020151 | Acc: 0.3042
[2025-11-12 04:49:50] 04:03:23<09:40:21, 16.00s/it | [Iter  913/3090] R0[822/3000]  | LR: 0.024778 | E: -63.397143 | E_img: +0.0079j E_var:     3.3464 E_err:   0.020211 | Acc: 0.3024
[2025-11-12 04:50:06] 04:03:39<09:40:05, 15.99s/it | [Iter  914/3090] R0[823/3000]  | LR: 0.024766 | E: -63.466959 | E_img: +0.0032j E_var:     3.3997 E_err:   0.020372 | Acc: 0.2930
[2025-11-12 04:50:22] 04:03:55<09:39:48, 15.99s/it | [Iter  915/3090] R0[824/3000]  | LR: 0.024754 | E: -63.479665 | E_img: -0.0096j E_var:     3.3370 E_err:   0.020183 | Acc: 0.2880
[2025-11-12 04:50:38] 04:04:11<09:39:32, 15.99s/it | [Iter  916/3090] R0[825/3000]  | LR: 0.024742 | E: -63.428756 | E_img: +0.0156j E_var:     3.2282 E_err:   0.019851 | Acc: 0.3007
[2025-11-12 04:50:54] 04:04:27<09:39:16, 15.99s/it | [Iter  917/3090] R0[826/3000]  | LR: 0.024730 | E: -63.487715 | E_img: -0.0019j E_var:     3.3547 E_err:   0.020236 | Acc: 0.2938
[2025-11-12 04:51:10] 04:04:43<09:38:59, 15.99s/it | [Iter  918/3090] R0[827/3000]  | LR: 0.024718 | E: -63.483553 | E_img: +0.0040j E_var:     3.5915 E_err:   0.020938 | Acc: 0.2792
[2025-11-12 04:51:25] 04:04:58<09:38:43, 15.99s/it | [Iter  919/3090] R0[828/3000]  | LR: 0.024706 | E: -63.471484 | E_img: -0.0013j E_var:     4.2054 E_err:   0.022657 | Acc: 0.2609
[2025-11-12 04:51:41] 04:05:14<09:38:27, 15.99s/it | [Iter  920/3090] R0[829/3000]  | LR: 0.024694 | E: -63.435280 | E_img: +0.0008j E_var:     3.7980 E_err:   0.021532 | Acc: 0.2556
[2025-11-12 04:51:57] 04:05:30<09:38:10, 15.99s/it | [Iter  921/3090] R0[830/3000]  | LR: 0.024682 | E: -63.482405 | E_img: +0.0104j E_var:     3.4524 E_err:   0.020529 | Acc: 0.2602
[2025-11-12 04:52:13] 04:05:46<09:37:54, 15.99s/it | [Iter  922/3090] R0[831/3000]  | LR: 0.024670 | E: -63.474680 | E_img: -0.0292j E_var:     3.6875 E_err:   0.021216 | Acc: 0.2591
[2025-11-12 04:52:29] 04:06:02<09:37:38, 15.99s/it | [Iter  923/3090] R0[832/3000]  | LR: 0.024658 | E: -63.495008 | E_img: +0.0019j E_var:     3.4988 E_err:   0.020666 | Acc: 0.2678
[2025-11-12 04:52:45] 04:06:18<09:37:21, 15.99s/it | [Iter  924/3090] R0[833/3000]  | LR: 0.024646 | E: -63.483865 | E_img: +0.0141j E_var:     3.4109 E_err:   0.020405 | Acc: 0.2748
[2025-11-12 04:53:00] 04:06:34<09:37:05, 15.99s/it | [Iter  925/3090] R0[834/3000]  | LR: 0.024634 | E: -63.420306 | E_img: +0.0084j E_var:     3.2779 E_err:   0.020003 | Acc: 0.2850
[2025-11-12 04:53:16] 04:06:49<09:36:49, 15.99s/it | [Iter  926/3090] R0[835/3000]  | LR: 0.024622 | E: -63.441319 | E_img: +0.0070j E_var:     3.3845 E_err:   0.020326 | Acc: 0.2953
[2025-11-12 04:53:32] 04:07:05<09:36:32, 15.99s/it | [Iter  927/3090] R0[836/3000]  | LR: 0.024610 | E: -63.342825 | E_img: +0.0072j E_var:     3.6364 E_err:   0.021069 | Acc: 0.2869
[2025-11-12 04:53:48] 04:07:21<09:36:16, 15.99s/it | [Iter  928/3090] R0[837/3000]  | LR: 0.024598 | E: -63.454867 | E_img: -0.0119j E_var:     3.4976 E_err:   0.020663 | Acc: 0.2831
[2025-11-12 04:54:04] 04:07:37<09:35:59, 15.99s/it | [Iter  929/3090] R0[838/3000]  | LR: 0.024586 | E: -63.459683 | E_img: +0.0041j E_var:     3.5021 E_err:   0.020676 | Acc: 0.2793
[2025-11-12 04:54:20] 04:07:53<09:35:43, 15.99s/it | [Iter  930/3090] R0[839/3000]  | LR: 0.024573 | E: -63.488415 | E_img: -0.0006j E_var:     3.3078 E_err:   0.020094 | Acc: 0.2885
[2025-11-12 04:54:35] 04:08:09<09:35:27, 15.99s/it | [Iter  931/3090] R0[840/3000]  | LR: 0.024561 | E: -63.513332 | E_img: -0.0222j E_var:     3.5032 E_err:   0.020679 | Acc: 0.2892
[2025-11-12 04:54:51] 04:08:24<09:35:10, 15.99s/it | [Iter  932/3090] R0[841/3000]  | LR: 0.024549 | E: -63.469959 | E_img: +0.0043j E_var:     3.2911 E_err:   0.020044 | Acc: 0.2993
[2025-11-12 04:55:07] 04:08:40<09:34:54, 15.99s/it | [Iter  933/3090] R0[842/3000]  | LR: 0.024537 | E: -63.456413 | E_img: +0.0149j E_var:     3.4203 E_err:   0.020433 | Acc: 0.2905
[2025-11-12 04:55:23] 04:08:56<09:34:38, 15.99s/it | [Iter  934/3090] R0[843/3000]  | LR: 0.024525 | E: -63.506714 | E_img: +0.0117j E_var:     3.3312 E_err:   0.020165 | Acc: 0.2893
[2025-11-12 04:55:39] 04:09:12<09:34:21, 15.99s/it | [Iter  935/3090] R0[844/3000]  | LR: 0.024513 | E: -63.510784 | E_img: -0.0055j E_var:     3.2093 E_err:   0.019793 | Acc: 0.2895
[2025-11-12 04:55:55] 04:09:28<09:34:05, 15.99s/it | [Iter  936/3090] R0[845/3000]  | LR: 0.024501 | E: -63.483652 | E_img: -0.0002j E_var:     3.1879 E_err:   0.019727 | Acc: 0.2920
[2025-11-12 04:56:11] 04:09:44<09:33:49, 15.99s/it | [Iter  937/3090] R0[846/3000]  | LR: 0.024489 | E: -63.473584 | E_img: +0.0155j E_var:     3.3421 E_err:   0.020198 | Acc: 0.2928
[2025-11-12 04:56:26] 04:09:59<09:33:32, 15.99s/it | [Iter  938/3090] R0[847/3000]  | LR: 0.024476 | E: -63.459208 | E_img: +0.0071j E_var:     3.5215 E_err:   0.020733 | Acc: 0.2865
[2025-11-12 04:56:42] 04:10:15<09:33:16, 15.99s/it | [Iter  939/3090] R0[848/3000]  | LR: 0.024464 | E: -63.493883 | E_img: -0.0130j E_var:     3.7168 E_err:   0.021300 | Acc: 0.2783
[2025-11-12 04:56:58] 04:10:31<09:33:00, 15.99s/it | [Iter  940/3090] R0[849/3000]  | LR: 0.024452 | E: -63.502702 | E_img: +0.0042j E_var:     3.2634 E_err:   0.019959 | Acc: 0.2852
[2025-11-12 04:57:14] 04:10:47<09:32:43, 15.99s/it | [Iter  941/3090] R0[850/3000]  | LR: 0.024440 | E: -63.483212 | E_img: +0.0091j E_var:     3.3841 E_err:   0.020325 | Acc: 0.2866
[2025-11-12 04:57:30] 04:11:03<09:32:27, 15.99s/it | [Iter  942/3090] R0[851/3000]  | LR: 0.024428 | E: -63.497571 | E_img: -0.0149j E_var:     3.2687 E_err:   0.019975 | Acc: 0.2886
[2025-11-12 04:57:46] 04:11:19<09:32:11, 15.99s/it | [Iter  943/3090] R0[852/3000]  | LR: 0.024415 | E: -63.483493 | E_img: -0.0254j E_var:     3.3620 E_err:   0.020258 | Acc: 0.2817
[2025-11-12 04:58:01] 04:11:34<09:31:54, 15.99s/it | [Iter  944/3090] R0[853/3000]  | LR: 0.024403 | E: -63.480150 | E_img: -0.0138j E_var:     3.3733 E_err:   0.020292 | Acc: 0.2798
[2025-11-12 04:58:17] 04:11:50<09:31:38, 15.99s/it | [Iter  945/3090] R0[854/3000]  | LR: 0.024391 | E: -63.472616 | E_img: -0.0136j E_var:     3.3215 E_err:   0.020136 | Acc: 0.2842
[2025-11-12 04:58:33] 04:12:06<09:31:22, 15.99s/it | [Iter  946/3090] R0[855/3000]  | LR: 0.024379 | E: -63.510033 | E_img: -0.0080j E_var:     3.4393 E_err:   0.020490 | Acc: 0.2806
[2025-11-12 04:58:49] 04:12:22<09:31:05, 15.99s/it | [Iter  947/3090] R0[856/3000]  | LR: 0.024366 | E: -63.517818 | E_img: -0.0096j E_var:     3.3797 E_err:   0.020312 | Acc: 0.2783
[2025-11-12 04:59:05] 04:12:38<09:30:49, 15.99s/it | [Iter  948/3090] R0[857/3000]  | LR: 0.024354 | E: -63.497793 | E_img: +0.0037j E_var:     3.2940 E_err:   0.020053 | Acc: 0.2890
[2025-11-12 04:59:21] 04:12:54<09:30:33, 15.99s/it | [Iter  949/3090] R0[858/3000]  | LR: 0.024342 | E: -63.466656 | E_img: +0.0002j E_var:     3.2975 E_err:   0.020063 | Acc: 0.2912
[2025-11-12 04:59:36] 04:13:10<09:30:16, 15.99s/it | [Iter  950/3090] R0[859/3000]  | LR: 0.024330 | E: -63.488641 | E_img: +0.0095j E_var:     3.4416 E_err:   0.020497 | Acc: 0.2845
[2025-11-12 04:59:52] 04:13:25<09:30:00, 15.99s/it | [Iter  951/3090] R0[860/3000]  | LR: 0.024317 | E: -63.523453 | E_img: +0.0069j E_var:     3.6259 E_err:   0.021038 | Acc: 0.2716
[2025-11-12 05:00:08] 04:13:41<09:29:44, 15.99s/it | [Iter  952/3090] R0[861/3000]  | LR: 0.024305 | E: -63.557637 | E_img: -0.0019j E_var:     3.3692 E_err:   0.020280 | Acc: 0.2709
[2025-11-12 05:00:24] 04:13:57<09:29:27, 15.99s/it | [Iter  953/3090] R0[862/3000]  | LR: 0.024293 | E: -63.531653 | E_img: -0.0006j E_var:     3.3406 E_err:   0.020194 | Acc: 0.2761
[2025-11-12 05:00:40] 04:14:13<09:29:11, 15.99s/it | [Iter  954/3090] R0[863/3000]  | LR: 0.024280 | E: -63.476642 | E_img: +0.0107j E_var:     3.2890 E_err:   0.020037 | Acc: 0.2918
[2025-11-12 05:00:56] 04:14:29<09:28:55, 15.99s/it | [Iter  955/3090] R0[864/3000]  | LR: 0.024268 | E: -63.387405 | E_img: +0.0034j E_var:     3.3737 E_err:   0.020294 | Acc: 0.3030
[2025-11-12 05:01:11] 04:14:45<09:28:38, 15.99s/it | [Iter  956/3090] R0[865/3000]  | LR: 0.024256 | E: -63.303701 | E_img: -0.0213j E_var:     3.5186 E_err:   0.020725 | Acc: 0.3106
[2025-11-12 05:01:27] 04:15:00<09:28:22, 15.99s/it | [Iter  957/3090] R0[866/3000]  | LR: 0.024243 | E: -63.354050 | E_img: +0.0119j E_var:     3.4460 E_err:   0.020510 | Acc: 0.3121
[2025-11-12 05:01:43] 04:15:16<09:28:06, 15.99s/it | [Iter  958/3090] R0[867/3000]  | LR: 0.024231 | E: -63.430589 | E_img: -0.0064j E_var:     3.4479 E_err:   0.020516 | Acc: 0.2952
[2025-11-12 05:01:59] 04:15:32<09:27:49, 15.99s/it | [Iter  959/3090] R0[868/3000]  | LR: 0.024218 | E: -63.434002 | E_img: -0.0014j E_var:     3.3193 E_err:   0.020129 | Acc: 0.2981
[2025-11-12 05:02:15] 04:15:48<09:27:33, 15.99s/it | [Iter  960/3090] R0[869/3000]  | LR: 0.024206 | E: -63.378080 | E_img: +0.0118j E_var:     3.3991 E_err:   0.020370 | Acc: 0.3029
[2025-11-12 05:02:31] 04:16:04<09:27:17, 15.99s/it | [Iter  961/3090] R0[870/3000]  | LR: 0.024194 | E: -63.396669 | E_img: -0.0093j E_var:     3.3888 E_err:   0.020339 | Acc: 0.3087
[2025-11-12 05:02:46] 04:16:20<09:27:01, 15.99s/it | [Iter  962/3090] R0[871/3000]  | LR: 0.024181 | E: -63.425142 | E_img: +0.0095j E_var:     3.2646 E_err:   0.019963 | Acc: 0.3113
[2025-11-12 05:03:02] 04:16:35<09:26:44, 15.99s/it | [Iter  963/3090] R0[872/3000]  | LR: 0.024169 | E: -63.341956 | E_img: +0.0190j E_var:     3.3162 E_err:   0.020120 | Acc: 0.3190
[2025-11-12 05:03:18] 04:16:51<09:26:28, 15.99s/it | [Iter  964/3090] R0[873/3000]  | LR: 0.024156 | E: -63.423926 | E_img: -0.0087j E_var:     3.2851 E_err:   0.020025 | Acc: 0.3089
[2025-11-12 05:03:34] 04:17:07<09:26:12, 15.99s/it | [Iter  965/3090] R0[874/3000]  | LR: 0.024144 | E: -63.466908 | E_img: +0.0119j E_var:     3.1676 E_err:   0.019664 | Acc: 0.3088
[2025-11-12 05:03:50] 04:17:23<09:25:55, 15.99s/it | [Iter  966/3090] R0[875/3000]  | LR: 0.024131 | E: -63.493212 | E_img: -0.0126j E_var:     3.1607 E_err:   0.019643 | Acc: 0.3016
[2025-11-12 05:04:06] 04:17:39<09:25:39, 15.99s/it | [Iter  967/3090] R0[876/3000]  | LR: 0.024119 | E: -63.517711 | E_img: -0.0122j E_var:     3.5635 E_err:   0.020856 | Acc: 0.2828
[2025-11-12 05:04:21] 04:17:55<09:25:23, 15.99s/it | [Iter  968/3090] R0[877/3000]  | LR: 0.024106 | E: -63.491367 | E_img: -0.0067j E_var:     3.3509 E_err:   0.020225 | Acc: 0.2823
[2025-11-12 05:04:37] 04:18:10<09:25:06, 15.99s/it | [Iter  969/3090] R0[878/3000]  | LR: 0.024094 | E: -63.508097 | E_img: +0.0176j E_var:     3.3389 E_err:   0.020189 | Acc: 0.2873
[2025-11-12 05:04:53] 04:18:26<09:24:50, 15.99s/it | [Iter  970/3090] R0[879/3000]  | LR: 0.024082 | E: -63.429707 | E_img: +0.0039j E_var:     3.3586 E_err:   0.020248 | Acc: 0.3072
[2025-11-12 05:05:09] 04:18:42<09:24:34, 15.99s/it | [Iter  971/3090] R0[880/3000]  | LR: 0.024069 | E: -63.536246 | E_img: -0.0118j E_var:     4.2663 E_err:   0.022821 | Acc: 0.2884
[2025-11-12 05:05:25] 04:18:58<09:24:17, 15.99s/it | [Iter  972/3090] R0[881/3000]  | LR: 0.024056 | E: -63.478902 | E_img: +0.0095j E_var:     3.7736 E_err:   0.021463 | Acc: 0.2715
[2025-11-12 05:05:41] 04:19:14<09:24:01, 15.99s/it | [Iter  973/3090] R0[882/3000]  | LR: 0.024044 | E: -63.489495 | E_img: -0.0078j E_var:     3.4053 E_err:   0.020388 | Acc: 0.2722
[2025-11-12 05:05:57] 04:19:30<09:23:45, 15.99s/it | [Iter  974/3090] R0[883/3000]  | LR: 0.024031 | E: -63.507612 | E_img: -0.0184j E_var:     3.2175 E_err:   0.019818 | Acc: 0.2822
[2025-11-12 05:06:12] 04:19:45<09:23:28, 15.99s/it | [Iter  975/3090] R0[884/3000]  | LR: 0.024019 | E: -63.510783 | E_img: -0.0000j E_var:     3.1830 E_err:   0.019712 | Acc: 0.2878
[2025-11-12 05:06:28] 04:20:01<09:23:12, 15.98s/it | [Iter  976/3090] R0[885/3000]  | LR: 0.024006 | E: -63.523341 | E_img: -0.0033j E_var:     3.1911 E_err:   0.019737 | Acc: 0.2928
[2025-11-12 05:06:44] 04:20:17<09:22:56, 15.98s/it | [Iter  977/3090] R0[886/3000]  | LR: 0.023994 | E: -63.506017 | E_img: -0.0041j E_var:     3.2386 E_err:   0.019883 | Acc: 0.2928
[2025-11-12 05:07:00] 04:20:33<09:22:40, 15.98s/it | [Iter  978/3090] R0[887/3000]  | LR: 0.023981 | E: -63.540510 | E_img: -0.0082j E_var:     3.2541 E_err:   0.019931 | Acc: 0.2855
[2025-11-12 05:07:16] 04:20:49<09:22:23, 15.98s/it | [Iter  979/3090] R0[888/3000]  | LR: 0.023969 | E: -63.510752 | E_img: -0.0065j E_var:     3.2163 E_err:   0.019815 | Acc: 0.2901
[2025-11-12 05:07:32] 04:21:05<09:22:07, 15.98s/it | [Iter  980/3090] R0[889/3000]  | LR: 0.023956 | E: -63.524360 | E_img: -0.0079j E_var:     3.3648 E_err:   0.020267 | Acc: 0.2888
[2025-11-12 05:07:47] 04:21:20<09:21:51, 15.98s/it | [Iter  981/3090] R0[890/3000]  | LR: 0.023943 | E: -63.511165 | E_img: -0.0062j E_var:     3.4536 E_err:   0.020532 | Acc: 0.2832
[2025-11-12 05:08:03] 04:21:36<09:21:34, 15.98s/it | [Iter  982/3090] R0[891/3000]  | LR: 0.023931 | E: -63.522431 | E_img: +0.0150j E_var:     3.3800 E_err:   0.020312 | Acc: 0.2835
[2025-11-12 05:08:19] 04:21:52<09:21:18, 15.98s/it | [Iter  983/3090] R0[892/3000]  | LR: 0.023918 | E: -63.497749 | E_img: +0.0042j E_var:     3.7087 E_err:   0.021277 | Acc: 0.2767
[2025-11-12 05:08:35] 04:22:08<09:21:02, 15.98s/it | [Iter  984/3090] R0[893/3000]  | LR: 0.023906 | E: -63.526406 | E_img: -0.0164j E_var:     3.3518 E_err:   0.020228 | Acc: 0.2765
[2025-11-12 05:08:51] 04:22:24<09:20:45, 15.98s/it | [Iter  985/3090] R0[894/3000]  | LR: 0.023893 | E: -63.470685 | E_img: -0.0207j E_var:     3.6835 E_err:   0.021205 | Acc: 0.2659
[2025-11-12 05:09:07] 04:22:40<09:20:29, 15.98s/it | [Iter  986/3090] R0[895/3000]  | LR: 0.023880 | E: -63.447824 | E_img: +0.0022j E_var:     3.7756 E_err:   0.021468 | Acc: 0.2602
[2025-11-12 05:09:22] 04:22:56<09:20:13, 15.98s/it | [Iter  987/3090] R0[896/3000]  | LR: 0.023868 | E: -63.509274 | E_img: +0.0001j E_var:     3.4947 E_err:   0.020654 | Acc: 0.2632
[2025-11-12 05:09:38] 04:23:11<09:19:57, 15.98s/it | [Iter  988/3090] R0[897/3000]  | LR: 0.023855 | E: -63.524013 | E_img: -0.0009j E_var:     3.3135 E_err:   0.020112 | Acc: 0.2700
[2025-11-12 05:09:54] 04:23:27<09:19:40, 15.98s/it | [Iter  989/3090] R0[898/3000]  | LR: 0.023842 | E: -63.534670 | E_img: +0.0075j E_var:     3.4795 E_err:   0.020609 | Acc: 0.2673
[2025-11-12 05:10:10] 04:23:43<09:19:24, 15.98s/it | [Iter  990/3090] R0[899/3000]  | LR: 0.023830 | E: -63.509360 | E_img: +0.0046j E_var:     3.5841 E_err:   0.020917 | Acc: 0.2657
[2025-11-12 05:10:10] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-11-12 05:10:26] 04:23:59<09:19:08, 15.98s/it | [Iter  991/3090] R0[900/3000]  | LR: 0.023817 | E: -63.494414 | E_img: +0.0127j E_var:     3.4889 E_err:   0.020637 | Acc: 0.2679
[2025-11-12 05:10:42] 04:24:15<09:18:52, 15.98s/it | [Iter  992/3090] R0[901/3000]  | LR: 0.023804 | E: -63.539957 | E_img: -0.0055j E_var:     3.3177 E_err:   0.020124 | Acc: 0.2720
[2025-11-12 05:10:58] 04:24:31<09:18:35, 15.98s/it | [Iter  993/3090] R0[902/3000]  | LR: 0.023791 | E: -63.540997 | E_img: +0.0037j E_var:     3.5567 E_err:   0.020837 | Acc: 0.2679
[2025-11-12 05:11:13] 04:24:46<09:18:19, 15.98s/it | [Iter  994/3090] R0[903/3000]  | LR: 0.023779 | E: -63.504189 | E_img: +0.0011j E_var:     3.3381 E_err:   0.020186 | Acc: 0.2728
[2025-11-12 05:11:29] 04:25:02<09:18:03, 15.98s/it | [Iter  995/3090] R0[904/3000]  | LR: 0.023766 | E: -63.493066 | E_img: +0.0047j E_var:     3.4939 E_err:   0.020652 | Acc: 0.2789
[2025-11-12 05:11:45] 04:25:18<09:17:46, 15.98s/it | [Iter  996/3090] R0[905/3000]  | LR: 0.023753 | E: -63.527317 | E_img: -0.0050j E_var:     3.3675 E_err:   0.020275 | Acc: 0.2854
[2025-11-12 05:12:01] 04:25:34<09:17:30, 15.98s/it | [Iter  997/3090] R0[906/3000]  | LR: 0.023740 | E: -63.533676 | E_img: -0.0085j E_var:     3.4162 E_err:   0.020421 | Acc: 0.2826
[2025-11-12 05:12:17] 04:25:50<09:17:14, 15.98s/it | [Iter  998/3090] R0[907/3000]  | LR: 0.023728 | E: -63.544793 | E_img: +0.0196j E_var:     3.2731 E_err:   0.019989 | Acc: 0.2831
[2025-11-12 05:12:33] 04:26:06<09:16:58, 15.98s/it | [Iter  999/3090] R0[908/3000]  | LR: 0.023715 | E: -63.559156 | E_img: +0.0077j E_var:     3.2632 E_err:   0.019958 | Acc: 0.2832
[2025-11-12 05:12:48] 04:26:21<09:16:41, 15.98s/it | [Iter 1000/3090] R0[909/3000]  | LR: 0.023702 | E: -63.527198 | E_img: +0.0009j E_var:     3.3665 E_err:   0.020272 | Acc: 0.2804
[2025-11-12 05:13:04] 04:26:37<09:16:25, 15.98s/it | [Iter 1001/3090] R0[910/3000]  | LR: 0.023689 | E: -63.528657 | E_img: -0.0010j E_var:     3.2848 E_err:   0.020024 | Acc: 0.2817
[2025-11-12 05:13:20] 04:26:53<09:16:09, 15.98s/it | [Iter 1002/3090] R0[911/3000]  | LR: 0.023676 | E: -63.506296 | E_img: +0.0081j E_var:     3.4625 E_err:   0.020559 | Acc: 0.2754
[2025-11-12 05:13:36] 04:27:09<09:15:52, 15.98s/it | [Iter 1003/3090] R0[912/3000]  | LR: 0.023664 | E: -63.508058 | E_img: +0.0069j E_var:     3.6035 E_err:   0.020973 | Acc: 0.2692
[2025-11-12 05:13:52] 04:27:25<09:15:36, 15.98s/it | [Iter 1004/3090] R0[913/3000]  | LR: 0.023651 | E: -63.513406 | E_img: +0.0002j E_var:     3.3435 E_err:   0.020202 | Acc: 0.2775
[2025-11-12 05:14:08] 04:27:41<09:15:20, 15.98s/it | [Iter 1005/3090] R0[914/3000]  | LR: 0.023638 | E: -63.495845 | E_img: -0.0213j E_var:     3.6347 E_err:   0.021064 | Acc: 0.2736
[2025-11-12 05:14:23] 04:27:56<09:15:04, 15.98s/it | [Iter 1006/3090] R0[915/3000]  | LR: 0.023625 | E: -63.483546 | E_img: +0.0078j E_var:     3.5006 E_err:   0.020672 | Acc: 0.2708
[2025-11-12 05:14:39] 04:28:12<09:14:47, 15.98s/it | [Iter 1007/3090] R0[916/3000]  | LR: 0.023612 | E: -63.484285 | E_img: -0.0115j E_var:     3.5503 E_err:   0.020818 | Acc: 0.2719
[2025-11-12 05:14:55] 04:28:28<09:14:31, 15.98s/it | [Iter 1008/3090] R0[917/3000]  | LR: 0.023599 | E: -63.526694 | E_img: -0.0142j E_var:     3.2621 E_err:   0.019955 | Acc: 0.2829
[2025-11-12 05:15:11] 04:28:44<09:14:15, 15.98s/it | [Iter 1009/3090] R0[918/3000]  | LR: 0.023587 | E: -63.432772 | E_img: +0.0006j E_var:     3.3959 E_err:   0.020360 | Acc: 0.2916
[2025-11-12 05:15:27] 04:29:00<09:13:58, 15.98s/it | [Iter 1010/3090] R0[919/3000]  | LR: 0.023574 | E: -63.471715 | E_img: +0.0148j E_var:     3.2184 E_err:   0.019821 | Acc: 0.2990
[2025-11-12 05:15:43] 04:29:16<09:13:42, 15.98s/it | [Iter 1011/3090] R0[920/3000]  | LR: 0.023561 | E: -63.528709 | E_img: -0.0056j E_var:     3.3871 E_err:   0.020334 | Acc: 0.2986
[2025-11-12 05:15:58] 04:29:32<09:13:26, 15.98s/it | [Iter 1012/3090] R0[921/3000]  | LR: 0.023548 | E: -63.481647 | E_img: -0.0011j E_var:     3.1870 E_err:   0.019724 | Acc: 0.3009
[2025-11-12 05:16:14] 04:29:47<09:13:10, 15.98s/it | [Iter 1013/3090] R0[922/3000]  | LR: 0.023535 | E: -63.496046 | E_img: +0.0036j E_var:     3.2469 E_err:   0.019908 | Acc: 0.2941
[2025-11-12 05:16:30] 04:30:03<09:12:53, 15.98s/it | [Iter 1014/3090] R0[923/3000]  | LR: 0.023522 | E: -63.470279 | E_img: -0.0059j E_var:     3.2060 E_err:   0.019783 | Acc: 0.3014
[2025-11-12 05:16:46] 04:30:19<09:12:37, 15.98s/it | [Iter 1015/3090] R0[924/3000]  | LR: 0.023509 | E: -63.505086 | E_img: +0.0056j E_var:     3.2400 E_err:   0.019887 | Acc: 0.2972
[2025-11-12 05:17:02] 04:30:35<09:12:21, 15.98s/it | [Iter 1016/3090] R0[925/3000]  | LR: 0.023496 | E: -63.475311 | E_img: +0.0045j E_var:     3.1241 E_err:   0.019528 | Acc: 0.3017
[2025-11-12 05:17:18] 04:30:51<09:12:05, 15.98s/it | [Iter 1017/3090] R0[926/3000]  | LR: 0.023483 | E: -63.520162 | E_img: +0.0167j E_var:     3.4576 E_err:   0.020544 | Acc: 0.2896
[2025-11-12 05:17:33] 04:31:07<09:11:48, 15.98s/it | [Iter 1018/3090] R0[927/3000]  | LR: 0.023470 | E: -63.536069 | E_img: +0.0008j E_var:     3.5966 E_err:   0.020953 | Acc: 0.2723
[2025-11-12 05:17:49] 04:31:22<09:11:32, 15.98s/it | [Iter 1019/3090] R0[928/3000]  | LR: 0.023457 | E: -63.514405 | E_img: +0.0084j E_var:     3.4488 E_err:   0.020518 | Acc: 0.2672
[2025-11-12 05:18:05] 04:31:38<09:11:16, 15.98s/it | [Iter 1020/3090] R0[929/3000]  | LR: 0.023444 | E: -63.508970 | E_img: -0.0025j E_var:     3.7748 E_err:   0.021466 | Acc: 0.2633
[2025-11-12 05:18:21] 04:31:54<09:11:00, 15.98s/it | [Iter 1021/3090] R0[930/3000]  | LR: 0.023431 | E: -63.555038 | E_img: +0.0045j E_var:     3.1097 E_err:   0.019483 | Acc: 0.2765
[2025-11-12 05:18:37] 04:32:10<09:10:43, 15.98s/it | [Iter 1022/3090] R0[931/3000]  | LR: 0.023418 | E: -63.546483 | E_img: -0.0101j E_var:     3.4941 E_err:   0.020653 | Acc: 0.2722
[2025-11-12 05:18:53] 04:32:26<09:10:27, 15.98s/it | [Iter 1023/3090] R0[932/3000]  | LR: 0.023405 | E: -63.531389 | E_img: -0.0011j E_var:     3.4377 E_err:   0.020485 | Acc: 0.2664
[2025-11-12 05:19:08] 04:32:42<09:10:11, 15.98s/it | [Iter 1024/3090] R0[933/3000]  | LR: 0.023392 | E: -63.566492 | E_img: +0.0031j E_var:     3.6643 E_err:   0.021149 | Acc: 0.2628
[2025-11-12 05:19:24] 04:32:57<09:09:55, 15.98s/it | [Iter 1025/3090] R0[934/3000]  | LR: 0.023379 | E: -63.505951 | E_img: -0.0037j E_var:     3.5120 E_err:   0.020705 | Acc: 0.2651
[2025-11-12 05:19:40] 04:33:13<09:09:38, 15.98s/it | [Iter 1026/3090] R0[935/3000]  | LR: 0.023366 | E: -63.502236 | E_img: -0.0051j E_var:     3.3925 E_err:   0.020350 | Acc: 0.2706
[2025-11-12 05:19:56] 04:33:29<09:09:22, 15.98s/it | [Iter 1027/3090] R0[936/3000]  | LR: 0.023353 | E: -63.483936 | E_img: -0.0043j E_var:     3.3942 E_err:   0.020355 | Acc: 0.2770
[2025-11-12 05:20:12] 04:33:45<09:09:06, 15.98s/it | [Iter 1028/3090] R0[937/3000]  | LR: 0.023340 | E: -63.518153 | E_img: +0.0193j E_var:     3.3820 E_err:   0.020319 | Acc: 0.2706
[2025-11-12 05:20:28] 04:34:01<09:08:50, 15.98s/it | [Iter 1029/3090] R0[938/3000]  | LR: 0.023327 | E: -63.507571 | E_img: +0.0083j E_var:     3.2379 E_err:   0.019881 | Acc: 0.2796
[2025-11-12 05:20:44] 04:34:17<09:08:33, 15.98s/it | [Iter 1030/3090] R0[939/3000]  | LR: 0.023314 | E: -63.470889 | E_img: +0.0067j E_var:     3.3341 E_err:   0.020174 | Acc: 0.2838
[2025-11-12 05:20:59] 04:34:32<09:08:17, 15.98s/it | [Iter 1031/3090] R0[940/3000]  | LR: 0.023301 | E: -63.474432 | E_img: -0.0058j E_var:     3.3926 E_err:   0.020350 | Acc: 0.2830
[2025-11-12 05:21:15] 04:34:48<09:08:01, 15.98s/it | [Iter 1032/3090] R0[941/3000]  | LR: 0.023288 | E: -63.446867 | E_img: -0.0168j E_var:     3.2817 E_err:   0.020015 | Acc: 0.2915
[2025-11-12 05:21:31] 04:35:04<09:07:44, 15.98s/it | [Iter 1033/3090] R0[942/3000]  | LR: 0.023275 | E: -63.444681 | E_img: -0.0135j E_var:     3.3687 E_err:   0.020279 | Acc: 0.2938
[2025-11-12 05:21:47] 04:35:20<09:07:28, 15.98s/it | [Iter 1034/3090] R0[943/3000]  | LR: 0.023262 | E: -63.468535 | E_img: -0.0070j E_var:     3.2249 E_err:   0.019841 | Acc: 0.2958
[2025-11-12 05:22:03] 04:35:36<09:07:12, 15.98s/it | [Iter 1035/3090] R0[944/3000]  | LR: 0.023248 | E: -63.486594 | E_img: +0.0179j E_var:     3.4852 E_err:   0.020626 | Acc: 0.2798
[2025-11-12 05:22:19] 04:35:52<09:06:56, 15.98s/it | [Iter 1036/3090] R0[945/3000]  | LR: 0.023235 | E: -63.520642 | E_img: +0.0116j E_var:     3.2516 E_err:   0.019923 | Acc: 0.2846
[2025-11-12 05:22:34] 04:36:07<09:06:39, 15.98s/it | [Iter 1037/3090] R0[946/3000]  | LR: 0.023222 | E: -63.516915 | E_img: +0.0086j E_var:     3.6036 E_err:   0.020973 | Acc: 0.2749
[2025-11-12 05:22:50] 04:36:23<09:06:23, 15.98s/it | [Iter 1038/3090] R0[947/3000]  | LR: 0.023209 | E: -63.504003 | E_img: +0.0025j E_var:     3.4650 E_err:   0.020566 | Acc: 0.2707
[2025-11-12 05:23:06] 04:36:39<09:06:07, 15.98s/it | [Iter 1039/3090] R0[948/3000]  | LR: 0.023196 | E: -63.562187 | E_img: -0.0054j E_var:     3.4138 E_err:   0.020414 | Acc: 0.2743
[2025-11-12 05:23:22] 04:36:55<09:05:51, 15.98s/it | [Iter 1040/3090] R0[949/3000]  | LR: 0.023183 | E: -63.523259 | E_img: +0.0067j E_var:     3.4021 E_err:   0.020379 | Acc: 0.2744
[2025-11-12 05:23:38] 04:37:11<09:05:34, 15.98s/it | [Iter 1041/3090] R0[950/3000]  | LR: 0.023170 | E: -63.471946 | E_img: +0.0157j E_var:     3.2613 E_err:   0.019953 | Acc: 0.2930
[2025-11-12 05:23:54] 04:37:27<09:05:18, 15.98s/it | [Iter 1042/3090] R0[951/3000]  | LR: 0.023156 | E: -63.487771 | E_img: -0.0063j E_var:     3.3277 E_err:   0.020155 | Acc: 0.2935
[2025-11-12 05:24:09] 04:37:42<09:05:02, 15.98s/it | [Iter 1043/3090] R0[952/3000]  | LR: 0.023143 | E: -63.467668 | E_img: +0.0127j E_var:     3.3490 E_err:   0.020219 | Acc: 0.2888
[2025-11-12 05:24:25] 04:37:58<09:04:46, 15.98s/it | [Iter 1044/3090] R0[953/3000]  | LR: 0.023130 | E: -63.537148 | E_img: -0.0120j E_var:     3.2574 E_err:   0.019941 | Acc: 0.2814
[2025-11-12 05:24:41] 04:38:14<09:04:29, 15.98s/it | [Iter 1045/3090] R0[954/3000]  | LR: 0.023117 | E: -63.543951 | E_img: -0.0085j E_var:     3.2353 E_err:   0.019873 | Acc: 0.2780
[2025-11-12 05:24:57] 04:38:30<09:04:13, 15.98s/it | [Iter 1046/3090] R0[955/3000]  | LR: 0.023104 | E: -63.503267 | E_img: +0.0070j E_var:     3.2018 E_err:   0.019770 | Acc: 0.2842
[2025-11-12 05:25:13] 04:38:46<09:03:57, 15.98s/it | [Iter 1047/3090] R0[956/3000]  | LR: 0.023090 | E: -63.519616 | E_img: -0.0126j E_var:     3.3948 E_err:   0.020357 | Acc: 0.2765
[2025-11-12 05:25:29] 04:39:02<09:03:41, 15.97s/it | [Iter 1048/3090] R0[957/3000]  | LR: 0.023077 | E: -63.587876 | E_img: -0.0063j E_var:     3.4289 E_err:   0.020459 | Acc: 0.2712
[2025-11-12 05:25:44] 04:39:18<09:03:24, 15.97s/it | [Iter 1049/3090] R0[958/3000]  | LR: 0.023064 | E: -63.571205 | E_img: -0.0025j E_var:     3.2012 E_err:   0.019768 | Acc: 0.2770
[2025-11-12 05:26:00] 04:39:33<09:03:08, 15.97s/it | [Iter 1050/3090] R0[959/3000]  | LR: 0.023051 | E: -63.534534 | E_img: +0.0061j E_var:     3.3385 E_err:   0.020187 | Acc: 0.2794
[2025-11-12 05:26:16] 04:39:49<09:02:52, 15.97s/it | [Iter 1051/3090] R0[960/3000]  | LR: 0.023037 | E: -63.506566 | E_img: -0.0151j E_var:     3.2056 E_err:   0.019782 | Acc: 0.2873
[2025-11-12 05:26:32] 04:40:05<09:02:36, 15.97s/it | [Iter 1052/3090] R0[961/3000]  | LR: 0.023024 | E: -63.391363 | E_img: -0.0284j E_var:     3.4312 E_err:   0.020466 | Acc: 0.3042
[2025-11-12 05:26:48] 04:40:21<09:02:19, 15.97s/it | [Iter 1053/3090] R0[962/3000]  | LR: 0.023011 | E: -63.465346 | E_img: +0.0193j E_var:     3.3136 E_err:   0.020112 | Acc: 0.2991
[2025-11-12 05:27:04] 04:40:37<09:02:03, 15.97s/it | [Iter 1054/3090] R0[963/3000]  | LR: 0.022998 | E: -63.405705 | E_img: -0.0062j E_var:     3.3827 E_err:   0.020321 | Acc: 0.2944
[2025-11-12 05:27:19] 04:40:52<09:01:47, 15.97s/it | [Iter 1055/3090] R0[964/3000]  | LR: 0.022984 | E: -63.471059 | E_img: +0.0156j E_var:     3.4691 E_err:   0.020579 | Acc: 0.2866
[2025-11-12 05:27:35] 04:41:08<09:01:31, 15.97s/it | [Iter 1056/3090] R0[965/3000]  | LR: 0.022971 | E: -63.509299 | E_img: +0.0197j E_var:     3.3560 E_err:   0.020240 | Acc: 0.2805
[2025-11-12 05:27:51] 04:41:24<09:01:14, 15.97s/it | [Iter 1057/3090] R0[966/3000]  | LR: 0.022958 | E: -63.513839 | E_img: +0.0024j E_var:     3.1793 E_err:   0.019700 | Acc: 0.2921
[2025-11-12 05:28:07] 04:41:40<09:00:58, 15.97s/it | [Iter 1058/3090] R0[967/3000]  | LR: 0.022944 | E: -63.384213 | E_img: -0.0013j E_var:     3.2636 E_err:   0.019960 | Acc: 0.3082
[2025-11-12 05:28:23] 04:41:56<09:00:42, 15.97s/it | [Iter 1059/3090] R0[968/3000]  | LR: 0.022931 | E: -63.456309 | E_img: -0.0105j E_var:     3.5418 E_err:   0.020793 | Acc: 0.2968
[2025-11-12 05:28:39] 04:42:12<09:00:26, 15.97s/it | [Iter 1060/3090] R0[969/3000]  | LR: 0.022918 | E: -63.386584 | E_img: -0.0101j E_var:     3.4885 E_err:   0.020636 | Acc: 0.2990
[2025-11-12 05:28:54] 04:42:27<09:00:09, 15.97s/it | [Iter 1061/3090] R0[970/3000]  | LR: 0.022904 | E: -63.474108 | E_img: -0.0045j E_var:     3.2884 E_err:   0.020036 | Acc: 0.2929
[2025-11-12 05:29:10] 04:42:43<08:59:53, 15.97s/it | [Iter 1062/3090] R0[971/3000]  | LR: 0.022891 | E: -63.456736 | E_img: +0.0185j E_var:     3.2487 E_err:   0.019914 | Acc: 0.2908
[2025-11-12 05:29:26] 04:42:59<08:59:37, 15.97s/it | [Iter 1063/3090] R0[972/3000]  | LR: 0.022878 | E: -63.454611 | E_img: -0.0127j E_var:     3.1772 E_err:   0.019694 | Acc: 0.2940
[2025-11-12 05:29:42] 04:43:15<08:59:21, 15.97s/it | [Iter 1064/3090] R0[973/3000]  | LR: 0.022864 | E: -63.490200 | E_img: -0.0132j E_var:     3.4590 E_err:   0.020548 | Acc: 0.2785
[2025-11-12 05:29:58] 04:43:31<08:59:05, 15.97s/it | [Iter 1065/3090] R0[974/3000]  | LR: 0.022851 | E: -63.517829 | E_img: -0.0006j E_var:     3.2780 E_err:   0.020004 | Acc: 0.2718
[2025-11-12 05:30:14] 04:43:47<08:58:48, 15.97s/it | [Iter 1066/3090] R0[975/3000]  | LR: 0.022838 | E: -63.526079 | E_img: -0.0028j E_var:     3.2727 E_err:   0.019987 | Acc: 0.2716
[2025-11-12 05:30:29] 04:44:03<08:58:32, 15.97s/it | [Iter 1067/3090] R0[976/3000]  | LR: 0.022824 | E: -63.575231 | E_img: +0.0085j E_var:     3.4237 E_err:   0.020443 | Acc: 0.2661
[2025-11-12 05:30:45] 04:44:18<08:58:16, 15.97s/it | [Iter 1068/3090] R0[977/3000]  | LR: 0.022811 | E: -63.555623 | E_img: +0.0012j E_var:     3.3685 E_err:   0.020278 | Acc: 0.2658
[2025-11-12 05:31:01] 04:44:34<08:58:00, 15.97s/it | [Iter 1069/3090] R0[978/3000]  | LR: 0.022797 | E: -63.531507 | E_img: -0.0033j E_var:     3.6064 E_err:   0.020982 | Acc: 0.2607
[2025-11-12 05:31:17] 04:44:50<08:57:43, 15.97s/it | [Iter 1070/3090] R0[979/3000]  | LR: 0.022784 | E: -63.533485 | E_img: -0.0024j E_var:     3.1836 E_err:   0.019714 | Acc: 0.2682
[2025-11-12 05:31:33] 04:45:06<08:57:27, 15.97s/it | [Iter 1071/3090] R0[980/3000]  | LR: 0.022770 | E: -63.548711 | E_img: -0.0028j E_var:     3.2570 E_err:   0.019939 | Acc: 0.2736
[2025-11-12 05:31:49] 04:45:22<08:57:11, 15.97s/it | [Iter 1072/3090] R0[981/3000]  | LR: 0.022757 | E: -63.529336 | E_img: +0.0012j E_var:     3.6187 E_err:   0.021017 | Acc: 0.2648
[2025-11-12 05:32:04] 04:45:38<08:56:55, 15.97s/it | [Iter 1073/3090] R0[982/3000]  | LR: 0.022744 | E: -63.586702 | E_img: -0.0039j E_var:     3.3571 E_err:   0.020244 | Acc: 0.2639
[2025-11-12 05:32:20] 04:45:53<08:56:39, 15.97s/it | [Iter 1074/3090] R0[983/3000]  | LR: 0.022730 | E: -63.563265 | E_img: -0.0060j E_var:     3.3812 E_err:   0.020316 | Acc: 0.2700
[2025-11-12 05:32:36] 04:46:09<08:56:22, 15.97s/it | [Iter 1075/3090] R0[984/3000]  | LR: 0.022717 | E: -63.547385 | E_img: -0.0006j E_var:     3.1457 E_err:   0.019596 | Acc: 0.2861
[2025-11-12 05:32:52] 04:46:25<08:56:06, 15.97s/it | [Iter 1076/3090] R0[985/3000]  | LR: 0.022703 | E: -63.543075 | E_img: -0.0100j E_var:     3.1833 E_err:   0.019713 | Acc: 0.2892
[2025-11-12 05:33:08] 04:46:41<08:55:50, 15.97s/it | [Iter 1077/3090] R0[986/3000]  | LR: 0.022690 | E: -63.536196 | E_img: +0.0013j E_var:     3.4052 E_err:   0.020388 | Acc: 0.2833
[2025-11-12 05:33:24] 04:46:57<08:55:34, 15.97s/it | [Iter 1078/3090] R0[987/3000]  | LR: 0.022676 | E: -63.538272 | E_img: +0.0161j E_var:     3.2601 E_err:   0.019949 | Acc: 0.2795
[2025-11-12 05:33:40] 04:47:13<08:55:18, 15.97s/it | [Iter 1079/3090] R0[988/3000]  | LR: 0.022663 | E: -63.494076 | E_img: +0.0042j E_var:     3.2582 E_err:   0.019943 | Acc: 0.2952
[2025-11-12 05:33:55] 04:47:28<08:55:01, 15.97s/it | [Iter 1080/3090] R0[989/3000]  | LR: 0.022649 | E: -63.408327 | E_img: -0.0053j E_var:     3.2401 E_err:   0.019888 | Acc: 0.3090
[2025-11-12 05:34:11] 04:47:44<08:54:45, 15.97s/it | [Iter 1081/3090] R0[990/3000]  | LR: 0.022636 | E: -63.550271 | E_img: +0.0002j E_var:     3.2602 E_err:   0.019949 | Acc: 0.2953
[2025-11-12 05:34:27] 04:48:00<08:54:29, 15.97s/it | [Iter 1082/3090] R0[991/3000]  | LR: 0.022622 | E: -63.475755 | E_img: +0.0043j E_var:     3.4226 E_err:   0.020440 | Acc: 0.2870
[2025-11-12 05:34:43] 04:48:16<08:54:13, 15.97s/it | [Iter 1083/3090] R0[992/3000]  | LR: 0.022609 | E: -63.495815 | E_img: +0.0203j E_var:     3.3654 E_err:   0.020268 | Acc: 0.2787
[2025-11-12 05:34:59] 04:48:32<08:53:56, 15.97s/it | [Iter 1084/3090] R0[993/3000]  | LR: 0.022595 | E: -63.561467 | E_img: -0.0115j E_var:     3.7420 E_err:   0.021373 | Acc: 0.2620
[2025-11-12 05:35:15] 04:48:48<08:53:40, 15.97s/it | [Iter 1085/3090] R0[994/3000]  | LR: 0.022581 | E: -63.541636 | E_img: +0.0226j E_var:     3.4100 E_err:   0.020403 | Acc: 0.2648
[2025-11-12 05:35:30] 04:49:04<08:53:24, 15.97s/it | [Iter 1086/3090] R0[995/3000]  | LR: 0.022568 | E: -63.557908 | E_img: -0.0062j E_var:     3.2671 E_err:   0.019971 | Acc: 0.2650
[2025-11-12 05:35:46] 04:49:19<08:53:08, 15.97s/it | [Iter 1087/3090] R0[996/3000]  | LR: 0.022554 | E: -63.530985 | E_img: +0.0015j E_var:     3.1811 E_err:   0.019706 | Acc: 0.2743
[2025-11-12 05:36:02] 04:49:35<08:52:52, 15.97s/it | [Iter 1088/3090] R0[997/3000]  | LR: 0.022541 | E: -63.512871 | E_img: +0.0091j E_var:     3.3408 E_err:   0.020194 | Acc: 0.2839
[2025-11-12 05:36:18] 04:49:51<08:52:35, 15.97s/it | [Iter 1089/3090] R0[998/3000]  | LR: 0.022527 | E: -63.492514 | E_img: +0.0085j E_var:     3.5090 E_err:   0.020697 | Acc: 0.2738
[2025-11-12 05:36:34] 04:50:07<08:52:19, 15.97s/it | [Iter 1090/3090] R0[999/3000]  | LR: 0.022514 | E: -63.510205 | E_img: +0.0079j E_var:     3.4817 E_err:   0.020616 | Acc: 0.2659
[2025-11-12 05:36:50] 04:50:23<08:52:03, 15.97s/it | [Iter 1091/3090] R0[1000/3000] | LR: 0.022500 | E: -63.511636 | E_img: -0.0096j E_var:     3.6339 E_err:   0.021062 | Acc: 0.2619
[2025-11-12 05:37:05] 04:50:39<08:51:47, 15.97s/it | [Iter 1092/3090] R0[1001/3000] | LR: 0.022486 | E: -63.489291 | E_img: +0.0163j E_var:     3.4871 E_err:   0.020632 | Acc: 0.2630
[2025-11-12 05:37:21] 04:50:54<08:51:31, 15.97s/it | [Iter 1093/3090] R0[1002/3000] | LR: 0.022473 | E: -63.563212 | E_img: +0.0071j E_var:     3.4164 E_err:   0.020421 | Acc: 0.2647
[2025-11-12 05:37:37] 04:51:10<08:51:14, 15.97s/it | [Iter 1094/3090] R0[1003/3000] | LR: 0.022459 | E: -63.604766 | E_img: +0.0057j E_var:     3.4564 E_err:   0.020541 | Acc: 0.2653
[2025-11-12 05:37:53] 04:51:26<08:50:58, 15.97s/it | [Iter 1095/3090] R0[1004/3000] | LR: 0.022446 | E: -63.580001 | E_img: +0.0007j E_var:     3.3665 E_err:   0.020272 | Acc: 0.2706
[2025-11-12 05:38:09] 04:51:42<08:50:42, 15.97s/it | [Iter 1096/3090] R0[1005/3000] | LR: 0.022432 | E: -63.549381 | E_img: +0.0012j E_var:     3.4959 E_err:   0.020658 | Acc: 0.2665
[2025-11-12 05:38:25] 04:51:58<08:50:26, 15.97s/it | [Iter 1097/3090] R0[1006/3000] | LR: 0.022418 | E: -63.557725 | E_img: -0.0078j E_var:     3.2551 E_err:   0.019934 | Acc: 0.2735
[2025-11-12 05:38:40] 04:52:14<08:50:10, 15.97s/it | [Iter 1098/3090] R0[1007/3000] | LR: 0.022405 | E: -63.562808 | E_img: +0.0120j E_var:     3.2299 E_err:   0.019856 | Acc: 0.2775
[2025-11-12 05:38:56] 04:52:29<08:49:53, 15.97s/it | [Iter 1099/3090] R0[1008/3000] | LR: 0.022391 | E: -63.527281 | E_img: +0.0007j E_var:     3.5353 E_err:   0.020774 | Acc: 0.2729
[2025-11-12 05:39:12] 04:52:45<08:49:37, 15.97s/it | [Iter 1100/3090] R0[1009/3000] | LR: 0.022377 | E: -63.471666 | E_img: -0.0164j E_var:     3.7374 E_err:   0.021359 | Acc: 0.2665
[2025-11-12 05:39:28] 04:53:01<08:49:21, 15.97s/it | [Iter 1101/3090] R0[1010/3000] | LR: 0.022364 | E: -63.528914 | E_img: +0.0002j E_var:     3.8046 E_err:   0.021551 | Acc: 0.2554
[2025-11-12 05:39:44] 04:53:17<08:49:05, 15.97s/it | [Iter 1102/3090] R0[1011/3000] | LR: 0.022350 | E: -63.563348 | E_img: +0.0029j E_var:     3.4418 E_err:   0.020497 | Acc: 0.2582
[2025-11-12 05:40:00] 04:53:33<08:48:49, 15.97s/it | [Iter 1103/3090] R0[1012/3000] | LR: 0.022336 | E: -63.546462 | E_img: -0.0187j E_var:     3.4480 E_err:   0.020516 | Acc: 0.2607
[2025-11-12 05:40:15] 04:53:49<08:48:32, 15.97s/it | [Iter 1104/3090] R0[1013/3000] | LR: 0.022322 | E: -63.557923 | E_img: -0.0193j E_var:     3.3263 E_err:   0.020151 | Acc: 0.2650
[2025-11-12 05:40:31] 04:54:04<08:48:16, 15.97s/it | [Iter 1105/3090] R0[1014/3000] | LR: 0.022309 | E: -63.565735 | E_img: -0.0005j E_var:     3.6234 E_err:   0.021031 | Acc: 0.2572
[2025-11-12 05:40:47] 04:54:20<08:48:00, 15.97s/it | [Iter 1106/3090] R0[1015/3000] | LR: 0.022295 | E: -63.601728 | E_img: +0.0018j E_var:     3.3457 E_err:   0.020209 | Acc: 0.2627
[2025-11-12 05:41:03] 04:54:36<08:47:44, 15.97s/it | [Iter 1107/3090] R0[1016/3000] | LR: 0.022281 | E: -63.599070 | E_img: -0.0059j E_var:     3.4070 E_err:   0.020393 | Acc: 0.2671
[2025-11-12 05:41:19] 04:54:52<08:47:28, 15.97s/it | [Iter 1108/3090] R0[1017/3000] | LR: 0.022268 | E: -63.560509 | E_img: +0.0027j E_var:     3.6945 E_err:   0.021237 | Acc: 0.2576
[2025-11-12 05:41:35] 04:55:08<08:47:11, 15.97s/it | [Iter 1109/3090] R0[1018/3000] | LR: 0.022254 | E: -63.590792 | E_img: +0.0071j E_var:     3.3871 E_err:   0.020334 | Acc: 0.2608
[2025-11-12 05:41:51] 04:55:24<08:46:55, 15.97s/it | [Iter 1110/3090] R0[1019/3000] | LR: 0.022240 | E: -63.541645 | E_img: +0.0085j E_var:     3.6313 E_err:   0.021054 | Acc: 0.2593
[2025-11-12 05:42:06] 04:55:39<08:46:39, 15.97s/it | [Iter 1111/3090] R0[1020/3000] | LR: 0.022226 | E: -63.578344 | E_img: +0.0116j E_var:     3.3440 E_err:   0.020204 | Acc: 0.2631
[2025-11-12 05:42:22] 04:55:55<08:46:23, 15.97s/it | [Iter 1112/3090] R0[1021/3000] | LR: 0.022213 | E: -63.468663 | E_img: -0.0044j E_var:     3.7278 E_err:   0.021332 | Acc: 0.2652
[2025-11-12 05:42:38] 04:56:11<08:46:07, 15.97s/it | [Iter 1113/3090] R0[1022/3000] | LR: 0.022199 | E: -63.526034 | E_img: -0.0035j E_var:     3.4538 E_err:   0.020533 | Acc: 0.2681
[2025-11-12 05:42:54] 04:56:27<08:45:50, 15.97s/it | [Iter 1114/3090] R0[1023/3000] | LR: 0.022185 | E: -63.565227 | E_img: +0.0092j E_var:     3.4409 E_err:   0.020495 | Acc: 0.2650
[2025-11-12 05:43:10] 04:56:43<08:45:34, 15.97s/it | [Iter 1115/3090] R0[1024/3000] | LR: 0.022171 | E: -63.528387 | E_img: +0.0015j E_var:     3.9686 E_err:   0.022010 | Acc: 0.2496
[2025-11-12 05:43:26] 04:56:59<08:45:18, 15.97s/it | [Iter 1116/3090] R0[1025/3000] | LR: 0.022157 | E: -63.551392 | E_img: -0.0148j E_var:     3.2982 E_err:   0.020065 | Acc: 0.2611
[2025-11-12 05:43:41] 04:57:15<08:45:02, 15.97s/it | [Iter 1117/3090] R0[1026/3000] | LR: 0.022144 | E: -63.552691 | E_img: +0.0136j E_var:     3.1557 E_err:   0.019627 | Acc: 0.2747
[2025-11-12 05:43:57] 04:57:30<08:44:46, 15.97s/it | [Iter 1118/3090] R0[1027/3000] | LR: 0.022130 | E: -63.510611 | E_img: +0.0066j E_var:     3.4005 E_err:   0.020374 | Acc: 0.2757
[2025-11-12 05:44:13] 04:57:46<08:44:30, 15.97s/it | [Iter 1119/3090] R0[1028/3000] | LR: 0.022116 | E: -63.471413 | E_img: -0.0005j E_var:     3.3050 E_err:   0.020086 | Acc: 0.2832
[2025-11-12 05:44:29] 04:58:02<08:44:13, 15.97s/it | [Iter 1120/3090] R0[1029/3000] | LR: 0.022102 | E: -63.504608 | E_img: -0.0029j E_var:     3.2156 E_err:   0.019812 | Acc: 0.2884
[2025-11-12 05:44:45] 04:58:18<08:43:57, 15.97s/it | [Iter 1121/3090] R0[1030/3000] | LR: 0.022088 | E: -63.537215 | E_img: +0.0014j E_var:     3.1749 E_err:   0.019687 | Acc: 0.2885
[2025-11-12 05:45:01] 04:58:34<08:43:41, 15.97s/it | [Iter 1122/3090] R0[1031/3000] | LR: 0.022074 | E: -63.523501 | E_img: +0.0014j E_var:     3.1456 E_err:   0.019596 | Acc: 0.2907
[2025-11-12 05:45:17] 04:58:50<08:43:25, 15.97s/it | [Iter 1123/3090] R0[1032/3000] | LR: 0.022061 | E: -63.554383 | E_img: -0.0006j E_var:     3.1357 E_err:   0.019565 | Acc: 0.2874
[2025-11-12 05:45:32] 04:59:05<08:43:09, 15.97s/it | [Iter 1124/3090] R0[1033/3000] | LR: 0.022047 | E: -63.568203 | E_img: -0.0218j E_var:     3.2946 E_err:   0.020054 | Acc: 0.2758
[2025-11-12 05:45:48] 04:59:21<08:42:52, 15.97s/it | [Iter 1125/3090] R0[1034/3000] | LR: 0.022033 | E: -63.553003 | E_img: +0.0057j E_var:     3.6238 E_err:   0.021032 | Acc: 0.2678
[2025-11-12 05:46:04] 04:59:37<08:42:36, 15.97s/it | [Iter 1126/3090] R0[1035/3000] | LR: 0.022019 | E: -63.551172 | E_img: +0.0054j E_var:     3.5777 E_err:   0.020898 | Acc: 0.2617
[2025-11-12 05:46:20] 04:59:53<08:42:20, 15.97s/it | [Iter 1127/3090] R0[1036/3000] | LR: 0.022005 | E: -63.580438 | E_img: +0.0154j E_var:     3.0747 E_err:   0.019373 | Acc: 0.2759
[2025-11-12 05:46:36] 05:00:09<08:42:04, 15.97s/it | [Iter 1128/3090] R0[1037/3000] | LR: 0.021991 | E: -63.575454 | E_img: -0.0092j E_var:     3.3049 E_err:   0.020086 | Acc: 0.2735
[2025-11-12 05:46:52] 05:00:25<08:41:48, 15.97s/it | [Iter 1129/3090] R0[1038/3000] | LR: 0.021977 | E: -63.548275 | E_img: +0.0114j E_var:     3.2377 E_err:   0.019880 | Acc: 0.2723
[2025-11-12 05:47:07] 05:00:41<08:41:32, 15.97s/it | [Iter 1130/3090] R0[1039/3000] | LR: 0.021963 | E: -63.610025 | E_img: +0.0088j E_var:     3.1316 E_err:   0.019552 | Acc: 0.2728
[2025-11-12 05:47:23] 05:00:56<08:41:15, 15.96s/it | [Iter 1131/3090] R0[1040/3000] | LR: 0.021949 | E: -63.558534 | E_img: -0.0167j E_var:     3.1517 E_err:   0.019614 | Acc: 0.2795
[2025-11-12 05:47:39] 05:01:12<08:40:59, 15.96s/it | [Iter 1132/3090] R0[1041/3000] | LR: 0.021936 | E: -63.576783 | E_img: +0.0044j E_var:     3.4195 E_err:   0.020431 | Acc: 0.2711
[2025-11-12 05:47:55] 05:01:28<08:40:43, 15.96s/it | [Iter 1133/3090] R0[1042/3000] | LR: 0.021922 | E: -63.556724 | E_img: +0.0085j E_var:     3.0382 E_err:   0.019258 | Acc: 0.2876
[2025-11-12 05:48:11] 05:01:44<08:40:27, 15.96s/it | [Iter 1134/3090] R0[1043/3000] | LR: 0.021908 | E: -63.495706 | E_img: -0.0033j E_var:     3.1429 E_err:   0.019587 | Acc: 0.3066
[2025-11-12 05:48:27] 05:02:00<08:40:11, 15.96s/it | [Iter 1135/3090] R0[1044/3000] | LR: 0.021894 | E: -63.409586 | E_img: -0.0097j E_var:     3.2795 E_err:   0.020008 | Acc: 0.3137
[2025-11-12 05:48:42] 05:02:16<08:39:54, 15.96s/it | [Iter 1136/3090] R0[1045/3000] | LR: 0.021880 | E: -63.442501 | E_img: -0.0262j E_var:     3.2030 E_err:   0.019773 | Acc: 0.3117
[2025-11-12 05:48:58] 05:02:31<08:39:38, 15.96s/it | [Iter 1137/3090] R0[1046/3000] | LR: 0.021866 | E: -63.478130 | E_img: +0.0183j E_var:     3.1547 E_err:   0.019624 | Acc: 0.2971
[2025-11-12 05:49:14] 05:02:47<08:39:22, 15.96s/it | [Iter 1138/3090] R0[1047/3000] | LR: 0.021852 | E: -63.454355 | E_img: +0.0170j E_var:     3.0491 E_err:   0.019293 | Acc: 0.3001
[2025-11-12 05:49:30] 05:03:03<08:39:06, 15.96s/it | [Iter 1139/3090] R0[1048/3000] | LR: 0.021838 | E: -63.508819 | E_img: +0.0124j E_var:     3.0907 E_err:   0.019424 | Acc: 0.2975
[2025-11-12 05:49:46] 05:03:19<08:38:50, 15.96s/it | [Iter 1140/3090] R0[1049/3000] | LR: 0.021824 | E: -63.582358 | E_img: -0.0094j E_var:     3.1970 E_err:   0.019755 | Acc: 0.2851
[2025-11-12 05:50:02] 05:03:35<08:38:34, 15.96s/it | [Iter 1141/3090] R0[1050/3000] | LR: 0.021810 | E: -63.591362 | E_img: +0.0079j E_var:     3.1470 E_err:   0.019600 | Acc: 0.2799
[2025-11-12 05:50:18] 05:03:51<08:38:17, 15.96s/it | [Iter 1142/3090] R0[1051/3000] | LR: 0.021796 | E: -63.507042 | E_img: +0.0182j E_var:     3.2847 E_err:   0.020024 | Acc: 0.2750
[2025-11-12 05:50:33] 05:04:06<08:38:01, 15.96s/it | [Iter 1143/3090] R0[1052/3000] | LR: 0.021782 | E: -63.425867 | E_img: +0.0376j E_var:     3.5379 E_err:   0.020782 | Acc: 0.2694
[2025-11-12 05:50:49] 05:04:22<08:37:45, 15.96s/it | [Iter 1144/3090] R0[1053/3000] | LR: 0.021768 | E: -63.370957 | E_img: +0.0109j E_var:     3.8215 E_err:   0.021598 | Acc: 0.2602
[2025-11-12 05:51:05] 05:04:38<08:37:29, 15.96s/it | [Iter 1145/3090] R0[1054/3000] | LR: 0.021754 | E: -63.320797 | E_img: -0.0140j E_var:     4.3796 E_err:   0.023122 | Acc: 0.2452
[2025-11-12 05:51:21] 05:04:54<08:37:13, 15.96s/it | [Iter 1146/3090] R0[1055/3000] | LR: 0.021740 | E: -63.508082 | E_img: +0.0008j E_var:     3.2408 E_err:   0.019890 | Acc: 0.2567
[2025-11-12 05:51:37] 05:05:10<08:36:57, 15.96s/it | [Iter 1147/3090] R0[1056/3000] | LR: 0.021726 | E: -63.432635 | E_img: +0.0073j E_var:     3.8255 E_err:   0.021610 | Acc: 0.2547
[2025-11-12 05:51:53] 05:05:26<08:36:40, 15.96s/it | [Iter 1148/3090] R0[1057/3000] | LR: 0.021712 | E: -63.477458 | E_img: +0.0124j E_var:     3.4890 E_err:   0.020637 | Acc: 0.2550
[2025-11-12 05:52:08] 05:05:42<08:36:24, 15.96s/it | [Iter 1149/3090] R0[1058/3000] | LR: 0.021698 | E: -63.549059 | E_img: +0.0140j E_var:     3.0451 E_err:   0.019280 | Acc: 0.2744
[2025-11-12 05:52:24] 05:05:57<08:36:08, 15.96s/it | [Iter 1150/3090] R0[1059/3000] | LR: 0.021684 | E: -63.593343 | E_img: +0.0000j E_var:     3.2552 E_err:   0.019934 | Acc: 0.2798
[2025-11-12 05:52:40] 05:06:13<08:35:52, 15.96s/it | [Iter 1151/3090] R0[1060/3000] | LR: 0.021670 | E: -63.555652 | E_img: +0.0177j E_var:     3.2429 E_err:   0.019896 | Acc: 0.2829
[2025-11-12 05:52:56] 05:06:29<08:35:36, 15.96s/it | [Iter 1152/3090] R0[1061/3000] | LR: 0.021655 | E: -63.568321 | E_img: +0.0107j E_var:     3.5713 E_err:   0.020879 | Acc: 0.2721
[2025-11-12 05:53:12] 05:06:45<08:35:20, 15.96s/it | [Iter 1153/3090] R0[1062/3000] | LR: 0.021641 | E: -63.539645 | E_img: +0.0082j E_var:     3.4790 E_err:   0.020608 | Acc: 0.2642
[2025-11-12 05:53:28] 05:07:01<08:35:03, 15.96s/it | [Iter 1154/3090] R0[1063/3000] | LR: 0.021627 | E: -63.598796 | E_img: -0.0069j E_var:     3.1499 E_err:   0.019609 | Acc: 0.2722
[2025-11-12 05:53:43] 05:07:17<08:34:47, 15.96s/it | [Iter 1155/3090] R0[1064/3000] | LR: 0.021613 | E: -63.597742 | E_img: -0.0182j E_var:     3.2404 E_err:   0.019889 | Acc: 0.2729
[2025-11-12 05:53:59] 05:07:32<08:34:31, 15.96s/it | [Iter 1156/3090] R0[1065/3000] | LR: 0.021599 | E: -63.558338 | E_img: -0.0092j E_var:     3.3462 E_err:   0.020211 | Acc: 0.2732
[2025-11-12 05:54:15] 05:07:48<08:34:15, 15.96s/it | [Iter 1157/3090] R0[1066/3000] | LR: 0.021585 | E: -63.560177 | E_img: +0.0026j E_var:     3.2215 E_err:   0.019830 | Acc: 0.2735
[2025-11-12 05:54:31] 05:08:04<08:33:59, 15.96s/it | [Iter 1158/3090] R0[1067/3000] | LR: 0.021571 | E: -63.592023 | E_img: -0.0013j E_var:     3.4936 E_err:   0.020651 | Acc: 0.2678
[2025-11-12 05:54:47] 05:08:20<08:33:43, 15.96s/it | [Iter 1159/3090] R0[1068/3000] | LR: 0.021557 | E: -63.605067 | E_img: +0.0175j E_var:     3.4558 E_err:   0.020539 | Acc: 0.2587
[2025-11-12 05:55:03] 05:08:36<08:33:26, 15.96s/it | [Iter 1160/3090] R0[1069/3000] | LR: 0.021543 | E: -63.582917 | E_img: +0.0166j E_var:     3.6272 E_err:   0.021042 | Acc: 0.2504
[2025-11-12 05:55:19] 05:08:52<08:33:10, 15.96s/it | [Iter 1161/3090] R0[1070/3000] | LR: 0.021528 | E: -63.587070 | E_img: +0.0037j E_var:     3.2726 E_err:   0.019987 | Acc: 0.2583
[2025-11-12 05:55:34] 05:09:07<08:32:54, 15.96s/it | [Iter 1162/3090] R0[1071/3000] | LR: 0.021514 | E: -63.602507 | E_img: -0.0011j E_var:     3.5109 E_err:   0.020702 | Acc: 0.2586
[2025-11-12 05:55:50] 05:09:23<08:32:38, 15.96s/it | [Iter 1163/3090] R0[1072/3000] | LR: 0.021500 | E: -63.578537 | E_img: +0.0129j E_var:     3.4805 E_err:   0.020612 | Acc: 0.2539
[2025-11-12 05:56:06] 05:09:39<08:32:22, 15.96s/it | [Iter 1164/3090] R0[1073/3000] | LR: 0.021486 | E: -63.580943 | E_img: +0.0081j E_var:     3.4165 E_err:   0.020422 | Acc: 0.2523
[2025-11-12 05:56:22] 05:09:55<08:32:06, 15.96s/it | [Iter 1165/3090] R0[1074/3000] | LR: 0.021472 | E: -63.593352 | E_img: +0.0033j E_var:     3.1254 E_err:   0.019532 | Acc: 0.2663
[2025-11-12 05:56:38] 05:10:11<08:31:50, 15.96s/it | [Iter 1166/3090] R0[1075/3000] | LR: 0.021458 | E: -63.587509 | E_img: -0.0089j E_var:     3.0322 E_err:   0.019239 | Acc: 0.2853
[2025-11-12 05:56:54] 05:10:27<08:31:33, 15.96s/it | [Iter 1167/3090] R0[1076/3000] | LR: 0.021444 | E: -63.523180 | E_img: -0.0035j E_var:     3.1622 E_err:   0.019647 | Acc: 0.2875
[2025-11-12 05:57:09] 05:10:43<08:31:17, 15.96s/it | [Iter 1168/3090] R0[1077/3000] | LR: 0.021429 | E: -63.618127 | E_img: -0.0020j E_var:     3.2559 E_err:   0.019936 | Acc: 0.2841
[2025-11-12 05:57:25] 05:10:58<08:31:01, 15.96s/it | [Iter 1169/3090] R0[1078/3000] | LR: 0.021415 | E: -63.577325 | E_img: +0.0035j E_var:     3.2858 E_err:   0.020027 | Acc: 0.2814
[2025-11-12 05:57:41] 05:11:14<08:30:45, 15.96s/it | [Iter 1170/3090] R0[1079/3000] | LR: 0.021401 | E: -63.606725 | E_img: -0.0106j E_var:     3.0772 E_err:   0.019381 | Acc: 0.2787
[2025-11-12 05:57:57] 05:11:30<08:30:29, 15.96s/it | [Iter 1171/3090] R0[1080/3000] | LR: 0.021387 | E: -63.609047 | E_img: +0.0074j E_var:     3.1192 E_err:   0.019513 | Acc: 0.2800
[2025-11-12 05:58:13] 05:11:46<08:30:13, 15.96s/it | [Iter 1172/3090] R0[1081/3000] | LR: 0.021373 | E: -63.580532 | E_img: -0.0059j E_var:     3.1586 E_err:   0.019636 | Acc: 0.2779
[2025-11-12 05:58:29] 05:12:02<08:29:56, 15.96s/it | [Iter 1173/3090] R0[1082/3000] | LR: 0.021358 | E: -63.617472 | E_img: +0.0070j E_var:     3.4139 E_err:   0.020414 | Acc: 0.2665
[2025-11-12 05:58:44] 05:12:18<08:29:40, 15.96s/it | [Iter 1174/3090] R0[1083/3000] | LR: 0.021344 | E: -63.593516 | E_img: +0.0156j E_var:     3.4989 E_err:   0.020667 | Acc: 0.2565
[2025-11-12 05:59:00] 05:12:33<08:29:24, 15.96s/it | [Iter 1175/3090] R0[1084/3000] | LR: 0.021330 | E: -63.581292 | E_img: -0.0056j E_var:     3.0561 E_err:   0.019315 | Acc: 0.2641
[2025-11-12 05:59:16] 05:12:49<08:29:08, 15.96s/it | [Iter 1176/3090] R0[1085/3000] | LR: 0.021316 | E: -63.609298 | E_img: +0.0099j E_var:     3.1889 E_err:   0.019730 | Acc: 0.2685
[2025-11-12 05:59:32] 05:13:05<08:28:52, 15.96s/it | [Iter 1177/3090] R0[1086/3000] | LR: 0.021301 | E: -63.592949 | E_img: -0.0005j E_var:     3.4098 E_err:   0.020402 | Acc: 0.2644
[2025-11-12 05:59:48] 05:13:21<08:28:36, 15.96s/it | [Iter 1178/3090] R0[1087/3000] | LR: 0.021287 | E: -63.606893 | E_img: -0.0063j E_var:     3.8601 E_err:   0.021707 | Acc: 0.2530
[2025-11-12 06:00:04] 05:13:37<08:28:19, 15.96s/it | [Iter 1179/3090] R0[1088/3000] | LR: 0.021273 | E: -63.595825 | E_img: +0.0020j E_var:     3.3928 E_err:   0.020351 | Acc: 0.2573
[2025-11-12 06:00:20] 05:13:53<08:28:03, 15.96s/it | [Iter 1180/3090] R0[1089/3000] | LR: 0.021259 | E: -63.614978 | E_img: -0.0043j E_var:     3.2302 E_err:   0.019857 | Acc: 0.2658
[2025-11-12 06:00:35] 05:14:09<08:27:47, 15.96s/it | [Iter 1181/3090] R0[1090/3000] | LR: 0.021244 | E: -63.599808 | E_img: +0.0086j E_var:     3.1733 E_err:   0.019682 | Acc: 0.2718
[2025-11-12 06:00:51] 05:14:24<08:27:31, 15.96s/it | [Iter 1182/3090] R0[1091/3000] | LR: 0.021230 | E: -63.569021 | E_img: -0.0035j E_var:     3.4550 E_err:   0.020537 | Acc: 0.2737
[2025-11-12 06:01:07] 05:14:40<08:27:15, 15.96s/it | [Iter 1183/3090] R0[1092/3000] | LR: 0.021216 | E: -63.542782 | E_img: +0.0002j E_var:     3.2466 E_err:   0.019908 | Acc: 0.2728
[2025-11-12 06:01:23] 05:14:56<08:26:59, 15.96s/it | [Iter 1184/3090] R0[1093/3000] | LR: 0.021201 | E: -63.515298 | E_img: +0.0014j E_var:     3.2073 E_err:   0.019787 | Acc: 0.2749
[2025-11-12 06:01:39] 05:15:12<08:26:43, 15.96s/it | [Iter 1185/3090] R0[1094/3000] | LR: 0.021187 | E: -63.535795 | E_img: +0.0008j E_var:     3.3261 E_err:   0.020150 | Acc: 0.2731
[2025-11-12 06:01:55] 05:15:28<08:26:26, 15.96s/it | [Iter 1186/3090] R0[1095/3000] | LR: 0.021173 | E: -63.526632 | E_img: +0.0004j E_var:     3.6921 E_err:   0.021230 | Acc: 0.2692
[2025-11-12 06:02:10] 05:15:44<08:26:10, 15.96s/it | [Iter 1187/3090] R0[1096/3000] | LR: 0.021158 | E: -63.563786 | E_img: +0.0088j E_var:     3.5982 E_err:   0.020958 | Acc: 0.2680
[2025-11-12 06:02:26] 05:15:59<08:25:54, 15.96s/it | [Iter 1188/3090] R0[1097/3000] | LR: 0.021144 | E: -63.545447 | E_img: -0.0205j E_var:     3.5884 E_err:   0.020929 | Acc: 0.2605
[2025-11-12 06:02:42] 05:16:15<08:25:38, 15.96s/it | [Iter 1189/3090] R0[1098/3000] | LR: 0.021130 | E: -63.561067 | E_img: -0.0014j E_var:     3.3428 E_err:   0.020200 | Acc: 0.2593
[2025-11-12 06:02:58] 05:16:31<08:25:22, 15.96s/it | [Iter 1190/3090] R0[1099/3000] | LR: 0.021115 | E: -63.542509 | E_img: +0.0131j E_var:     3.2745 E_err:   0.019993 | Acc: 0.2728
[2025-11-12 06:03:14] 05:16:47<08:25:06, 15.96s/it | [Iter 1191/3090] R0[1100/3000] | LR: 0.021101 | E: -63.535661 | E_img: +0.0000j E_var:     3.2927 E_err:   0.020048 | Acc: 0.2727
[2025-11-12 06:03:30] 05:17:03<08:24:49, 15.96s/it | [Iter 1192/3090] R0[1101/3000] | LR: 0.021087 | E: -63.525739 | E_img: +0.0005j E_var:     3.4546 E_err:   0.020535 | Acc: 0.2674
[2025-11-12 06:03:45] 05:17:19<08:24:33, 15.96s/it | [Iter 1193/3090] R0[1102/3000] | LR: 0.021072 | E: -63.514541 | E_img: +0.0208j E_var:     3.2207 E_err:   0.019828 | Acc: 0.2792
[2025-11-12 06:04:01] 05:17:34<08:24:17, 15.96s/it | [Iter 1194/3090] R0[1103/3000] | LR: 0.021058 | E: -63.497898 | E_img: +0.0119j E_var:     3.3598 E_err:   0.020252 | Acc: 0.2833
[2025-11-12 06:04:17] 05:17:50<08:24:01, 15.96s/it | [Iter 1195/3090] R0[1104/3000] | LR: 0.021044 | E: -63.544213 | E_img: -0.0027j E_var:     3.2130 E_err:   0.019804 | Acc: 0.2777
[2025-11-12 06:04:33] 05:18:06<08:23:45, 15.96s/it | [Iter 1196/3090] R0[1105/3000] | LR: 0.021029 | E: -63.464229 | E_img: -0.0092j E_var:     3.8187 E_err:   0.021590 | Acc: 0.2566
[2025-11-12 06:04:49] 05:18:22<08:23:29, 15.96s/it | [Iter 1197/3090] R0[1106/3000] | LR: 0.021015 | E: -63.480975 | E_img: -0.0170j E_var:     3.8693 E_err:   0.021733 | Acc: 0.2439
[2025-11-12 06:05:05] 05:18:38<08:23:13, 15.96s/it | [Iter 1198/3090] R0[1107/3000] | LR: 0.021000 | E: -63.515621 | E_img: +0.0032j E_var:     3.1333 E_err:   0.019557 | Acc: 0.2565
[2025-11-12 06:05:20] 05:18:54<08:22:57, 15.96s/it | [Iter 1199/3090] R0[1108/3000] | LR: 0.020986 | E: -63.555347 | E_img: +0.0168j E_var:     3.1217 E_err:   0.019521 | Acc: 0.2771
[2025-11-12 06:05:36] 05:19:09<08:22:40, 15.96s/it | [Iter 1200/3090] R0[1109/3000] | LR: 0.020972 | E: -63.562290 | E_img: -0.0090j E_var:     3.2073 E_err:   0.019787 | Acc: 0.2826
[2025-11-12 06:05:52] 05:19:25<08:22:24, 15.96s/it | [Iter 1201/3090] R0[1110/3000] | LR: 0.020957 | E: -63.541114 | E_img: +0.0084j E_var:     3.1825 E_err:   0.019710 | Acc: 0.2846
[2025-11-12 06:06:08] 05:19:41<08:22:08, 15.96s/it | [Iter 1202/3090] R0[1111/3000] | LR: 0.020943 | E: -63.569925 | E_img: +0.0086j E_var:     3.0960 E_err:   0.019441 | Acc: 0.2894
[2025-11-12 06:06:24] 05:19:57<08:21:52, 15.96s/it | [Iter 1203/3090] R0[1112/3000] | LR: 0.020928 | E: -63.566926 | E_img: -0.0100j E_var:     3.1740 E_err:   0.019684 | Acc: 0.2858
[2025-11-12 06:06:40] 05:20:13<08:21:36, 15.96s/it | [Iter 1204/3090] R0[1113/3000] | LR: 0.020914 | E: -63.551905 | E_img: +0.0058j E_var:     3.1310 E_err:   0.019550 | Acc: 0.2865
[2025-11-12 06:06:56] 05:20:29<08:21:20, 15.96s/it | [Iter 1205/3090] R0[1114/3000] | LR: 0.020900 | E: -63.549447 | E_img: +0.0036j E_var:     3.9199 E_err:   0.021875 | Acc: 0.2635
[2025-11-12 06:07:11] 05:20:45<08:21:04, 15.96s/it | [Iter 1206/3090] R0[1115/3000] | LR: 0.020885 | E: -63.620671 | E_img: +0.0124j E_var:     3.0929 E_err:   0.019431 | Acc: 0.2695
[2025-11-12 06:07:27] 05:21:00<08:20:47, 15.96s/it | [Iter 1207/3090] R0[1116/3000] | LR: 0.020871 | E: -63.574504 | E_img: +0.0022j E_var:     3.1554 E_err:   0.019626 | Acc: 0.2741
[2025-11-12 06:07:43] 05:21:16<08:20:31, 15.96s/it | [Iter 1208/3090] R0[1117/3000] | LR: 0.020856 | E: -63.607980 | E_img: +0.0049j E_var:     3.1062 E_err:   0.019472 | Acc: 0.2849
[2025-11-12 06:07:59] 05:21:32<08:20:15, 15.96s/it | [Iter 1209/3090] R0[1118/3000] | LR: 0.020842 | E: -63.580308 | E_img: +0.0071j E_var:     3.2400 E_err:   0.019887 | Acc: 0.2899
[2025-11-12 06:08:15] 05:21:48<08:19:59, 15.96s/it | [Iter 1210/3090] R0[1119/3000] | LR: 0.020827 | E: -63.531598 | E_img: -0.0156j E_var:     3.0962 E_err:   0.019441 | Acc: 0.2912
[2025-11-12 06:08:31] 05:22:04<08:19:43, 15.96s/it | [Iter 1211/3090] R0[1120/3000] | LR: 0.020813 | E: -63.496541 | E_img: -0.0081j E_var:     3.3399 E_err:   0.020192 | Acc: 0.2915
[2025-11-12 06:08:46] 05:22:20<08:19:27, 15.96s/it | [Iter 1212/3090] R0[1121/3000] | LR: 0.020798 | E: -63.559571 | E_img: +0.0051j E_var:     3.2354 E_err:   0.019873 | Acc: 0.2777
[2025-11-12 06:09:02] 05:22:35<08:19:11, 15.96s/it | [Iter 1213/3090] R0[1122/3000] | LR: 0.020784 | E: -63.506565 | E_img: +0.0131j E_var:     3.4402 E_err:   0.020493 | Acc: 0.2671
[2025-11-12 06:09:18] 05:22:51<08:18:55, 15.96s/it | [Iter 1214/3090] R0[1123/3000] | LR: 0.020769 | E: -63.549541 | E_img: +0.0056j E_var:     3.3106 E_err:   0.020103 | Acc: 0.2626
[2025-11-12 06:09:34] 05:23:07<08:18:38, 15.96s/it | [Iter 1215/3090] R0[1124/3000] | LR: 0.020755 | E: -63.606934 | E_img: -0.0109j E_var:     3.3203 E_err:   0.020132 | Acc: 0.2673
[2025-11-12 06:09:50] 05:23:23<08:18:22, 15.96s/it | [Iter 1216/3090] R0[1125/3000] | LR: 0.020740 | E: -63.557787 | E_img: +0.0032j E_var:     3.4468 E_err:   0.020512 | Acc: 0.2588
[2025-11-12 06:10:06] 05:23:39<08:18:06, 15.96s/it | [Iter 1217/3090] R0[1126/3000] | LR: 0.020726 | E: -63.481989 | E_img: -0.0200j E_var:     3.9875 E_err:   0.022063 | Acc: 0.2389
[2025-11-12 06:10:22] 05:23:55<08:17:50, 15.96s/it | [Iter 1218/3090] R0[1127/3000] | LR: 0.020711 | E: -63.436141 | E_img: -0.0004j E_var:     3.7405 E_err:   0.021368 | Acc: 0.2370
[2025-11-12 06:10:37] 05:24:10<08:17:34, 15.96s/it | [Iter 1219/3090] R0[1128/3000] | LR: 0.020697 | E: -63.544527 | E_img: +0.0151j E_var:     3.1893 E_err:   0.019731 | Acc: 0.2570
[2025-11-12 06:10:53] 05:24:26<08:17:18, 15.96s/it | [Iter 1220/3090] R0[1129/3000] | LR: 0.020682 | E: -63.535655 | E_img: -0.0000j E_var:     3.3799 E_err:   0.020312 | Acc: 0.2627
[2025-11-12 06:11:09] 05:24:42<08:17:02, 15.96s/it | [Iter 1221/3090] R0[1130/3000] | LR: 0.020668 | E: -63.557678 | E_img: +0.0144j E_var:     3.3623 E_err:   0.020259 | Acc: 0.2588
[2025-11-12 06:11:25] 05:24:58<08:16:45, 15.96s/it | [Iter 1222/3090] R0[1131/3000] | LR: 0.020653 | E: -63.554514 | E_img: +0.0132j E_var:     3.6083 E_err:   0.020987 | Acc: 0.2571
[2025-11-12 06:11:41] 05:25:14<08:16:29, 15.96s/it | [Iter 1223/3090] R0[1132/3000] | LR: 0.020639 | E: -63.571445 | E_img: -0.0021j E_var:     3.2938 E_err:   0.020052 | Acc: 0.2598
[2025-11-12 06:11:57] 05:25:30<08:16:13, 15.96s/it | [Iter 1224/3090] R0[1133/3000] | LR: 0.020624 | E: -63.565467 | E_img: -0.0215j E_var:     3.3903 E_err:   0.020343 | Acc: 0.2623
[2025-11-12 06:12:12] 05:25:46<08:15:57, 15.96s/it | [Iter 1225/3090] R0[1134/3000] | LR: 0.020609 | E: -63.618111 | E_img: -0.0120j E_var:     3.3521 E_err:   0.020229 | Acc: 0.2622
[2025-11-12 06:12:28] 05:26:01<08:15:41, 15.96s/it | [Iter 1226/3090] R0[1135/3000] | LR: 0.020595 | E: -63.583079 | E_img: -0.0043j E_var:     3.5645 E_err:   0.020860 | Acc: 0.2633
[2025-11-12 06:12:44] 05:26:17<08:15:25, 15.96s/it | [Iter 1227/3090] R0[1136/3000] | LR: 0.020580 | E: -63.600374 | E_img: +0.0042j E_var:     3.0916 E_err:   0.019427 | Acc: 0.2869
[2025-11-12 06:13:00] 05:26:33<08:15:09, 15.96s/it | [Iter 1228/3090] R0[1137/3000] | LR: 0.020566 | E: -63.524338 | E_img: +0.0098j E_var:     3.2504 E_err:   0.019919 | Acc: 0.3006
[2025-11-12 06:13:16] 05:26:49<08:14:53, 15.96s/it | [Iter 1229/3090] R0[1138/3000] | LR: 0.020551 | E: -63.527019 | E_img: +0.0065j E_var:     3.1413 E_err:   0.019582 | Acc: 0.3003
[2025-11-12 06:13:32] 05:27:05<08:14:36, 15.96s/it | [Iter 1230/3090] R0[1139/3000] | LR: 0.020537 | E: -63.571885 | E_img: -0.0001j E_var:     3.1704 E_err:   0.019673 | Acc: 0.2939
[2025-11-12 06:13:47] 05:27:21<08:14:20, 15.95s/it | [Iter 1231/3090] R0[1140/3000] | LR: 0.020522 | E: -63.587194 | E_img: -0.0070j E_var:     3.1691 E_err:   0.019669 | Acc: 0.2793
[2025-11-12 06:14:03] 05:27:36<08:14:04, 15.95s/it | [Iter 1232/3090] R0[1141/3000] | LR: 0.020507 | E: -63.610149 | E_img: -0.0024j E_var:     2.9099 E_err:   0.018847 | Acc: 0.2880
[2025-11-12 06:14:19] 05:27:52<08:13:48, 15.95s/it | [Iter 1233/3090] R0[1142/3000] | LR: 0.020493 | E: -63.625646 | E_img: -0.0081j E_var:     3.1604 E_err:   0.019641 | Acc: 0.2803
[2025-11-12 06:14:35] 05:28:08<08:13:32, 15.95s/it | [Iter 1234/3090] R0[1143/3000] | LR: 0.020478 | E: -63.596107 | E_img: -0.0090j E_var:     3.2976 E_err:   0.020063 | Acc: 0.2717
[2025-11-12 06:14:51] 05:28:24<08:13:16, 15.95s/it | [Iter 1235/3090] R0[1144/3000] | LR: 0.020463 | E: -63.645631 | E_img: +0.0043j E_var:     3.1233 E_err:   0.019526 | Acc: 0.2685
[2025-11-12 06:15:07] 05:28:40<08:13:00, 15.95s/it | [Iter 1236/3090] R0[1145/3000] | LR: 0.020449 | E: -63.569544 | E_img: -0.0128j E_var:     3.5271 E_err:   0.020750 | Acc: 0.2623
[2025-11-12 06:15:22] 05:28:56<08:12:44, 15.95s/it | [Iter 1237/3090] R0[1146/3000] | LR: 0.020434 | E: -63.572209 | E_img: +0.0002j E_var:     3.4358 E_err:   0.020479 | Acc: 0.2604
[2025-11-12 06:15:38] 05:29:11<08:12:27, 15.95s/it | [Iter 1238/3090] R0[1147/3000] | LR: 0.020420 | E: -63.586052 | E_img: -0.0183j E_var:     3.0285 E_err:   0.019227 | Acc: 0.2715
[2025-11-12 06:15:54] 05:29:27<08:12:11, 15.95s/it | [Iter 1239/3090] R0[1148/3000] | LR: 0.020405 | E: -63.582973 | E_img: +0.0058j E_var:     3.0129 E_err:   0.019178 | Acc: 0.2788
[2025-11-12 06:16:10] 05:29:43<08:11:55, 15.95s/it | [Iter 1240/3090] R0[1149/3000] | LR: 0.020390 | E: -63.603513 | E_img: -0.0292j E_var:     3.2931 E_err:   0.020050 | Acc: 0.2720
[2025-11-12 06:16:26] 05:29:59<08:11:39, 15.95s/it | [Iter 1241/3090] R0[1150/3000] | LR: 0.020376 | E: -63.602220 | E_img: +0.0246j E_var:     3.2269 E_err:   0.019847 | Acc: 0.2691
[2025-11-12 06:16:42] 05:30:15<08:11:23, 15.95s/it | [Iter 1242/3090] R0[1151/3000] | LR: 0.020361 | E: -63.609115 | E_img: +0.0013j E_var:     3.5858 E_err:   0.020922 | Acc: 0.2636
[2025-11-12 06:16:57] 05:30:31<08:11:07, 15.95s/it | [Iter 1243/3090] R0[1152/3000] | LR: 0.020346 | E: -63.588747 | E_img: +0.0079j E_var:     3.3684 E_err:   0.020278 | Acc: 0.2610
[2025-11-12 06:17:13] 05:30:46<08:10:51, 15.95s/it | [Iter 1244/3090] R0[1153/3000] | LR: 0.020332 | E: -63.569953 | E_img: -0.0022j E_var:     3.4347 E_err:   0.020476 | Acc: 0.2548
[2025-11-12 06:17:29] 05:31:02<08:10:34, 15.95s/it | [Iter 1245/3090] R0[1154/3000] | LR: 0.020317 | E: -63.554642 | E_img: -0.0070j E_var:     3.5809 E_err:   0.020907 | Acc: 0.2530
[2025-11-12 06:17:45] 05:31:18<08:10:18, 15.95s/it | [Iter 1246/3090] R0[1155/3000] | LR: 0.020302 | E: -63.545943 | E_img: +0.0124j E_var:     3.1872 E_err:   0.019725 | Acc: 0.2623
[2025-11-12 06:18:01] 05:31:34<08:10:02, 15.95s/it | [Iter 1247/3090] R0[1156/3000] | LR: 0.020287 | E: -63.560461 | E_img: -0.0044j E_var:     3.1605 E_err:   0.019642 | Acc: 0.2782
[2025-11-12 06:18:17] 05:31:50<08:09:46, 15.95s/it | [Iter 1248/3090] R0[1157/3000] | LR: 0.020273 | E: -63.573302 | E_img: -0.0005j E_var:     3.1695 E_err:   0.019670 | Acc: 0.2771
[2025-11-12 06:18:33] 05:32:06<08:09:30, 15.95s/it | [Iter 1249/3090] R0[1158/3000] | LR: 0.020258 | E: -63.577885 | E_img: -0.0082j E_var:     3.2839 E_err:   0.020022 | Acc: 0.2730
[2025-11-12 06:18:48] 05:32:22<08:09:14, 15.95s/it | [Iter 1250/3090] R0[1159/3000] | LR: 0.020243 | E: -63.587648 | E_img: -0.0076j E_var:     3.2151 E_err:   0.019811 | Acc: 0.2727
[2025-11-12 06:19:04] 05:32:37<08:08:58, 15.95s/it | [Iter 1251/3090] R0[1160/3000] | LR: 0.020229 | E: -63.564334 | E_img: -0.0094j E_var:     2.9974 E_err:   0.019128 | Acc: 0.2863
[2025-11-12 06:19:20] 05:32:53<08:08:42, 15.95s/it | [Iter 1252/3090] R0[1161/3000] | LR: 0.020214 | E: -63.612601 | E_img: -0.0019j E_var:     3.2280 E_err:   0.019851 | Acc: 0.2827
[2025-11-12 06:19:36] 05:33:09<08:08:26, 15.95s/it | [Iter 1253/3090] R0[1162/3000] | LR: 0.020199 | E: -63.568856 | E_img: +0.0060j E_var:     3.2569 E_err:   0.019939 | Acc: 0.2796
[2025-11-12 06:19:52] 05:33:25<08:08:10, 15.95s/it | [Iter 1254/3090] R0[1163/3000] | LR: 0.020184 | E: -63.591376 | E_img: +0.0037j E_var:     3.1397 E_err:   0.019577 | Acc: 0.2719
[2025-11-12 06:20:08] 05:33:41<08:07:53, 15.95s/it | [Iter 1255/3090] R0[1164/3000] | LR: 0.020170 | E: -63.595189 | E_img: +0.0061j E_var:     3.3738 E_err:   0.020294 | Acc: 0.2639
[2025-11-12 06:20:24] 05:33:57<08:07:37, 15.95s/it | [Iter 1256/3090] R0[1165/3000] | LR: 0.020155 | E: -63.600068 | E_img: -0.0009j E_var:     3.0384 E_err:   0.019259 | Acc: 0.2765
[2025-11-12 06:20:39] 05:34:12<08:07:21, 15.95s/it | [Iter 1257/3090] R0[1166/3000] | LR: 0.020140 | E: -63.627113 | E_img: -0.0094j E_var:     3.0808 E_err:   0.019393 | Acc: 0.2788
[2025-11-12 06:20:55] 05:34:28<08:07:05, 15.95s/it | [Iter 1258/3090] R0[1167/3000] | LR: 0.020125 | E: -63.621218 | E_img: +0.0064j E_var:     3.3814 E_err:   0.020317 | Acc: 0.2775
[2025-11-12 06:21:11] 05:34:44<08:06:49, 15.95s/it | [Iter 1259/3090] R0[1168/3000] | LR: 0.020111 | E: -63.604833 | E_img: +0.0092j E_var:     3.4233 E_err:   0.020442 | Acc: 0.2798
[2025-11-12 06:21:27] 05:35:00<08:06:33, 15.95s/it | [Iter 1260/3090] R0[1169/3000] | LR: 0.020096 | E: -63.627626 | E_img: +0.0128j E_var:     3.0986 E_err:   0.019449 | Acc: 0.2799
[2025-11-12 06:21:43] 05:35:16<08:06:17, 15.95s/it | [Iter 1261/3090] R0[1170/3000] | LR: 0.020081 | E: -63.585816 | E_img: -0.0050j E_var:     3.1234 E_err:   0.019526 | Acc: 0.2811
[2025-11-12 06:21:59] 05:35:32<08:06:01, 15.95s/it | [Iter 1262/3090] R0[1171/3000] | LR: 0.020066 | E: -63.555564 | E_img: -0.0194j E_var:     3.1154 E_err:   0.019501 | Acc: 0.2818
[2025-11-12 06:22:14] 05:35:48<08:05:45, 15.95s/it | [Iter 1263/3090] R0[1172/3000] | LR: 0.020052 | E: -63.552153 | E_img: +0.0185j E_var:     3.0047 E_err:   0.019152 | Acc: 0.2900
[2025-11-12 06:22:30] 05:36:03<08:05:28, 15.95s/it | [Iter 1264/3090] R0[1173/3000] | LR: 0.020037 | E: -63.575768 | E_img: +0.0028j E_var:     3.0861 E_err:   0.019409 | Acc: 0.2875
[2025-11-12 06:22:46] 05:36:19<08:05:12, 15.95s/it | [Iter 1265/3090] R0[1174/3000] | LR: 0.020022 | E: -63.579697 | E_img: +0.0008j E_var:     3.1684 E_err:   0.019666 | Acc: 0.2796
[2025-11-12 06:23:02] 05:36:35<08:04:56, 15.95s/it | [Iter 1266/3090] R0[1175/3000] | LR: 0.020007 | E: -63.538890 | E_img: +0.0060j E_var:     3.0116 E_err:   0.019174 | Acc: 0.2876
[2025-11-12 06:23:18] 05:36:51<08:04:40, 15.95s/it | [Iter 1267/3090] R0[1176/3000] | LR: 0.019992 | E: -63.401981 | E_img: -0.0389j E_var:     3.1379 E_err:   0.019571 | Acc: 0.3004
[2025-11-12 06:23:34] 05:37:07<08:04:24, 15.95s/it | [Iter 1268/3090] R0[1177/3000] | LR: 0.019978 | E: -63.395935 | E_img: +0.0098j E_var:     3.2085 E_err:   0.019791 | Acc: 0.3064
[2025-11-12 06:23:50] 05:37:23<08:04:08, 15.95s/it | [Iter 1269/3090] R0[1178/3000] | LR: 0.019963 | E: -63.426049 | E_img: -0.0120j E_var:     3.1419 E_err:   0.019584 | Acc: 0.3036
[2025-11-12 06:24:05] 05:37:38<08:03:52, 15.95s/it | [Iter 1270/3090] R0[1179/3000] | LR: 0.019948 | E: -63.494332 | E_img: +0.0107j E_var:     3.1130 E_err:   0.019494 | Acc: 0.2944
[2025-11-12 06:24:21] 05:37:54<08:03:36, 15.95s/it | [Iter 1271/3090] R0[1180/3000] | LR: 0.019933 | E: -63.563532 | E_img: -0.0123j E_var:     3.1938 E_err:   0.019745 | Acc: 0.2796
[2025-11-12 06:24:37] 05:38:10<08:03:20, 15.95s/it | [Iter 1272/3090] R0[1181/3000] | LR: 0.019918 | E: -63.642890 | E_img: +0.0028j E_var:     3.2078 E_err:   0.019788 | Acc: 0.2684
[2025-11-12 06:24:53] 05:38:26<08:03:03, 15.95s/it | [Iter 1273/3090] R0[1182/3000] | LR: 0.019903 | E: -63.586399 | E_img: -0.0014j E_var:     3.0828 E_err:   0.019399 | Acc: 0.2690
[2025-11-12 06:25:09] 05:38:42<08:02:47, 15.95s/it | [Iter 1274/3090] R0[1183/3000] | LR: 0.019889 | E: -63.604469 | E_img: +0.0106j E_var:     3.5459 E_err:   0.020805 | Acc: 0.2700
[2025-11-12 06:25:25] 05:38:58<08:02:31, 15.95s/it | [Iter 1275/3090] R0[1184/3000] | LR: 0.019874 | E: -63.599448 | E_img: +0.0019j E_var:     3.2029 E_err:   0.019773 | Acc: 0.2683
[2025-11-12 06:25:40] 05:39:13<08:02:15, 15.95s/it | [Iter 1276/3090] R0[1185/3000] | LR: 0.019859 | E: -63.639513 | E_img: -0.0016j E_var:     2.9733 E_err:   0.019051 | Acc: 0.2688
[2025-11-12 06:25:56] 05:39:29<08:01:59, 15.95s/it | [Iter 1277/3090] R0[1186/3000] | LR: 0.019844 | E: -63.587952 | E_img: -0.0042j E_var:     3.2244 E_err:   0.019839 | Acc: 0.2735
[2025-11-12 06:26:12] 05:39:45<08:01:43, 15.95s/it | [Iter 1278/3090] R0[1187/3000] | LR: 0.019829 | E: -63.617634 | E_img: -0.0062j E_var:     3.1740 E_err:   0.019684 | Acc: 0.2721
[2025-11-12 06:26:28] 05:40:01<08:01:27, 15.95s/it | [Iter 1279/3090] R0[1188/3000] | LR: 0.019814 | E: -63.642529 | E_img: +0.0083j E_var:     3.2127 E_err:   0.019803 | Acc: 0.2720
[2025-11-12 06:26:44] 05:40:17<08:01:11, 15.95s/it | [Iter 1280/3090] R0[1189/3000] | LR: 0.019799 | E: -63.587511 | E_img: +0.0078j E_var:     2.9996 E_err:   0.019135 | Acc: 0.2813
[2025-11-12 06:27:00] 05:40:33<08:00:55, 15.95s/it | [Iter 1281/3090] R0[1190/3000] | LR: 0.019784 | E: -63.574730 | E_img: -0.0122j E_var:     3.1394 E_err:   0.019576 | Acc: 0.2822
[2025-11-12 06:27:15] 05:40:49<08:00:38, 15.95s/it | [Iter 1282/3090] R0[1191/3000] | LR: 0.019770 | E: -63.596013 | E_img: +0.0093j E_var:     3.2204 E_err:   0.019827 | Acc: 0.2761
[2025-11-12 06:27:31] 05:41:04<08:00:22, 15.95s/it | [Iter 1283/3090] R0[1192/3000] | LR: 0.019755 | E: -63.613236 | E_img: -0.0023j E_var:     3.1128 E_err:   0.019493 | Acc: 0.2756
[2025-11-12 06:27:47] 05:41:20<08:00:06, 15.95s/it | [Iter 1284/3090] R0[1193/3000] | LR: 0.019740 | E: -63.601685 | E_img: +0.0005j E_var:     3.3504 E_err:   0.020223 | Acc: 0.2678
[2025-11-12 06:28:03] 05:41:36<07:59:50, 15.95s/it | [Iter 1285/3090] R0[1194/3000] | LR: 0.019725 | E: -63.606415 | E_img: -0.0133j E_var:     3.0416 E_err:   0.019269 | Acc: 0.2730
[2025-11-12 06:28:19] 05:41:52<07:59:34, 15.95s/it | [Iter 1286/3090] R0[1195/3000] | LR: 0.019710 | E: -63.649074 | E_img: -0.0179j E_var:     3.2632 E_err:   0.019958 | Acc: 0.2673
[2025-11-12 06:28:35] 05:42:08<07:59:18, 15.95s/it | [Iter 1287/3090] R0[1196/3000] | LR: 0.019695 | E: -63.646421 | E_img: +0.0075j E_var:     2.9971 E_err:   0.019127 | Acc: 0.2705
[2025-11-12 06:28:50] 05:42:24<07:59:02, 15.95s/it | [Iter 1288/3090] R0[1197/3000] | LR: 0.019680 | E: -63.633652 | E_img: -0.0112j E_var:     3.0447 E_err:   0.019279 | Acc: 0.2685
[2025-11-12 06:29:06] 05:42:39<07:58:46, 15.95s/it | [Iter 1289/3090] R0[1198/3000] | LR: 0.019665 | E: -63.632786 | E_img: +0.0117j E_var:     3.2042 E_err:   0.019777 | Acc: 0.2687
[2025-11-12 06:29:22] 05:42:55<07:58:30, 15.95s/it | [Iter 1290/3090] R0[1199/3000] | LR: 0.019650 | E: -63.601144 | E_img: -0.0037j E_var:     3.0094 E_err:   0.019166 | Acc: 0.2789
[2025-11-12 06:29:22] ✓ Checkpoint saved: checkpoint_iter_001200.pkl
[2025-11-12 06:29:38] 05:43:11<07:58:14, 15.95s/it | [Iter 1291/3090] R0[1200/3000] | LR: 0.019635 | E: -63.654035 | E_img: +0.0118j E_var:     3.1319 E_err:   0.019553 | Acc: 0.2786
[2025-11-12 06:29:54] 05:43:27<07:57:57, 15.95s/it | [Iter 1292/3090] R0[1201/3000] | LR: 0.019620 | E: -63.640441 | E_img: +0.0069j E_var:     3.0747 E_err:   0.019374 | Acc: 0.2766
[2025-11-12 06:30:10] 05:43:43<07:57:41, 15.95s/it | [Iter 1293/3090] R0[1202/3000] | LR: 0.019605 | E: -63.612005 | E_img: -0.0041j E_var:     3.0320 E_err:   0.019238 | Acc: 0.2771
[2025-11-12 06:30:26] 05:43:59<07:57:25, 15.95s/it | [Iter 1294/3090] R0[1203/3000] | LR: 0.019590 | E: -63.631576 | E_img: -0.0072j E_var:     3.0722 E_err:   0.019366 | Acc: 0.2817
[2025-11-12 06:30:41] 05:44:14<07:57:09, 15.95s/it | [Iter 1295/3090] R0[1204/3000] | LR: 0.019575 | E: -63.566345 | E_img: +0.0159j E_var:     3.3637 E_err:   0.020264 | Acc: 0.2690
[2025-11-12 06:30:57] 05:44:30<07:56:53, 15.95s/it | [Iter 1296/3090] R0[1205/3000] | LR: 0.019561 | E: -63.599187 | E_img: +0.0025j E_var:     3.2287 E_err:   0.019853 | Acc: 0.2638
[2025-11-12 06:31:13] 05:44:46<07:56:37, 15.95s/it | [Iter 1297/3090] R0[1206/3000] | LR: 0.019546 | E: -63.593943 | E_img: +0.0091j E_var:     3.3415 E_err:   0.020196 | Acc: 0.2608
[2025-11-12 06:31:29] 05:45:02<07:56:21, 15.95s/it | [Iter 1298/3090] R0[1207/3000] | LR: 0.019531 | E: -63.632556 | E_img: -0.0016j E_var:     2.9345 E_err:   0.018927 | Acc: 0.2754
[2025-11-12 06:31:45] 05:45:18<07:56:05, 15.95s/it | [Iter 1299/3090] R0[1208/3000] | LR: 0.019516 | E: -63.587961 | E_img: -0.0053j E_var:     2.9744 E_err:   0.019055 | Acc: 0.2930
[2025-11-12 06:32:01] 05:45:34<07:55:49, 15.95s/it | [Iter 1300/3090] R0[1209/3000] | LR: 0.019501 | E: -63.596500 | E_img: -0.0077j E_var:     3.0416 E_err:   0.019269 | Acc: 0.2884
[2025-11-12 06:32:16] 05:45:50<07:55:33, 15.95s/it | [Iter 1301/3090] R0[1210/3000] | LR: 0.019486 | E: -63.658752 | E_img: +0.0033j E_var:     2.9991 E_err:   0.019134 | Acc: 0.2811
[2025-11-12 06:32:32] 05:46:05<07:55:17, 15.95s/it | [Iter 1302/3090] R0[1211/3000] | LR: 0.019471 | E: -63.634394 | E_img: +0.0019j E_var:     3.0835 E_err:   0.019401 | Acc: 0.2706
[2025-11-12 06:32:48] 05:46:21<07:55:00, 15.95s/it | [Iter 1303/3090] R0[1212/3000] | LR: 0.019456 | E: -63.596339 | E_img: +0.0222j E_var:     3.1898 E_err:   0.019733 | Acc: 0.2719
[2025-11-12 06:33:04] 05:46:37<07:54:44, 15.95s/it | [Iter 1304/3090] R0[1213/3000] | LR: 0.019441 | E: -63.606005 | E_img: +0.0165j E_var:     3.1163 E_err:   0.019504 | Acc: 0.2708
[2025-11-12 06:33:20] 05:46:53<07:54:28, 15.95s/it | [Iter 1305/3090] R0[1214/3000] | LR: 0.019426 | E: -63.631030 | E_img: +0.0127j E_var:     3.1717 E_err:   0.019677 | Acc: 0.2666
[2025-11-12 06:33:36] 05:47:09<07:54:12, 15.95s/it | [Iter 1306/3090] R0[1215/3000] | LR: 0.019411 | E: -63.606423 | E_img: -0.0122j E_var:     3.0127 E_err:   0.019177 | Acc: 0.2792
[2025-11-12 06:33:51] 05:47:25<07:53:56, 15.95s/it | [Iter 1307/3090] R0[1216/3000] | LR: 0.019396 | E: -63.564271 | E_img: -0.0074j E_var:     3.0302 E_err:   0.019233 | Acc: 0.2833
[2025-11-12 06:34:07] 05:47:40<07:53:40, 15.95s/it | [Iter 1308/3090] R0[1217/3000] | LR: 0.019381 | E: -63.591372 | E_img: -0.0164j E_var:     2.9901 E_err:   0.019105 | Acc: 0.2795
[2025-11-12 06:34:23] 05:47:56<07:53:24, 15.95s/it | [Iter 1309/3090] R0[1218/3000] | LR: 0.019366 | E: -63.506736 | E_img: +0.0042j E_var:     3.0528 E_err:   0.019304 | Acc: 0.3044
[2025-11-12 06:34:39] 05:48:12<07:53:08, 15.95s/it | [Iter 1310/3090] R0[1219/3000] | LR: 0.019351 | E: -63.361575 | E_img: -0.0235j E_var:     3.1631 E_err:   0.019650 | Acc: 0.3177
[2025-11-12 06:34:55] 05:48:28<07:52:52, 15.95s/it | [Iter 1311/3090] R0[1220/3000] | LR: 0.019336 | E: -63.331379 | E_img: +0.0119j E_var:     3.1655 E_err:   0.019658 | Acc: 0.3179
[2025-11-12 06:35:11] 05:48:44<07:52:36, 15.95s/it | [Iter 1312/3090] R0[1221/3000] | LR: 0.019320 | E: -63.471721 | E_img: +0.0038j E_var:     3.1129 E_err:   0.019493 | Acc: 0.3078
[2025-11-12 06:35:27] 05:49:00<07:52:20, 15.95s/it | [Iter 1313/3090] R0[1222/3000] | LR: 0.019305 | E: -63.528474 | E_img: -0.0033j E_var:     2.9821 E_err:   0.019080 | Acc: 0.3068
[2025-11-12 06:35:42] 05:49:16<07:52:04, 15.95s/it | [Iter 1314/3090] R0[1223/3000] | LR: 0.019290 | E: -63.585195 | E_img: -0.0088j E_var:     2.9019 E_err:   0.018821 | Acc: 0.3017
[2025-11-12 06:35:58] 05:49:31<07:51:47, 15.95s/it | [Iter 1315/3090] R0[1224/3000] | LR: 0.019275 | E: -63.559373 | E_img: -0.0077j E_var:     3.0769 E_err:   0.019380 | Acc: 0.2876
[2025-11-12 06:36:14] 05:49:47<07:51:31, 15.95s/it | [Iter 1316/3090] R0[1225/3000] | LR: 0.019260 | E: -63.549448 | E_img: +0.0031j E_var:     3.3898 E_err:   0.020342 | Acc: 0.2752
[2025-11-12 06:36:30] 05:50:03<07:51:15, 15.95s/it | [Iter 1317/3090] R0[1226/3000] | LR: 0.019245 | E: -63.617332 | E_img: -0.0029j E_var:     2.9759 E_err:   0.019059 | Acc: 0.2793
[2025-11-12 06:36:46] 05:50:19<07:50:59, 15.95s/it | [Iter 1318/3090] R0[1227/3000] | LR: 0.019230 | E: -63.557616 | E_img: -0.0082j E_var:     3.0399 E_err:   0.019264 | Acc: 0.2815
[2025-11-12 06:37:02] 05:50:35<07:50:43, 15.95s/it | [Iter 1319/3090] R0[1228/3000] | LR: 0.019215 | E: -63.558685 | E_img: -0.0080j E_var:     3.1318 E_err:   0.019552 | Acc: 0.2817
[2025-11-12 06:37:18] 05:50:51<07:50:27, 15.95s/it | [Iter 1320/3090] R0[1229/3000] | LR: 0.019200 | E: -63.479614 | E_img: -0.0116j E_var:     3.1644 E_err:   0.019654 | Acc: 0.2888
[2025-11-12 06:37:33] 05:51:06<07:50:11, 15.95s/it | [Iter 1321/3090] R0[1230/3000] | LR: 0.019185 | E: -63.493560 | E_img: +0.0076j E_var:     3.3953 E_err:   0.020358 | Acc: 0.2792
[2025-11-12 06:37:49] 05:51:22<07:49:55, 15.95s/it | [Iter 1322/3090] R0[1231/3000] | LR: 0.019170 | E: -63.566954 | E_img: +0.0178j E_var:     3.0552 E_err:   0.019312 | Acc: 0.2795
[2025-11-12 06:38:05] 05:51:38<07:49:39, 15.95s/it | [Iter 1323/3090] R0[1232/3000] | LR: 0.019155 | E: -63.616364 | E_img: -0.0085j E_var:     3.0020 E_err:   0.019143 | Acc: 0.2765
[2025-11-12 06:38:21] 05:51:54<07:49:23, 15.95s/it | [Iter 1324/3090] R0[1233/3000] | LR: 0.019140 | E: -63.552033 | E_img: -0.0155j E_var:     3.1377 E_err:   0.019571 | Acc: 0.2793
[2025-11-12 06:38:37] 05:52:10<07:49:07, 15.95s/it | [Iter 1325/3090] R0[1234/3000] | LR: 0.019125 | E: -63.579828 | E_img: -0.0043j E_var:     3.0726 E_err:   0.019367 | Acc: 0.2816
[2025-11-12 06:38:53] 05:52:26<07:48:50, 15.95s/it | [Iter 1326/3090] R0[1235/3000] | LR: 0.019109 | E: -63.580793 | E_img: +0.0139j E_var:     3.2859 E_err:   0.020028 | Acc: 0.2713
[2025-11-12 06:39:08] 05:52:42<07:48:34, 15.95s/it | [Iter 1327/3090] R0[1236/3000] | LR: 0.019094 | E: -63.592375 | E_img: +0.0025j E_var:     3.1991 E_err:   0.019761 | Acc: 0.2709
[2025-11-12 06:39:24] 05:52:57<07:48:18, 15.95s/it | [Iter 1328/3090] R0[1237/3000] | LR: 0.019079 | E: -63.597793 | E_img: +0.0034j E_var:     3.0620 E_err:   0.019333 | Acc: 0.2790
[2025-11-12 06:39:40] 05:53:13<07:48:02, 15.95s/it | [Iter 1329/3090] R0[1238/3000] | LR: 0.019064 | E: -63.625192 | E_img: -0.0080j E_var:     2.9643 E_err:   0.019022 | Acc: 0.2853
[2025-11-12 06:39:56] 05:53:29<07:47:46, 15.95s/it | [Iter 1330/3090] R0[1239/3000] | LR: 0.019049 | E: -63.607151 | E_img: -0.0133j E_var:     3.1148 E_err:   0.019499 | Acc: 0.2837
[2025-11-12 06:40:12] 05:53:45<07:47:30, 15.95s/it | [Iter 1331/3090] R0[1240/3000] | LR: 0.019034 | E: -63.587264 | E_img: +0.0088j E_var:     3.3215 E_err:   0.020136 | Acc: 0.2694
[2025-11-12 06:40:28] 05:54:01<07:47:14, 15.95s/it | [Iter 1332/3090] R0[1241/3000] | LR: 0.019019 | E: -63.626148 | E_img: -0.0008j E_var:     3.0088 E_err:   0.019165 | Acc: 0.2710
[2025-11-12 06:40:43] 05:54:17<07:46:58, 15.95s/it | [Iter 1333/3090] R0[1242/3000] | LR: 0.019004 | E: -63.632892 | E_img: +0.0014j E_var:     3.1276 E_err:   0.019540 | Acc: 0.2795
[2025-11-12 06:40:59] 05:54:32<07:46:42, 15.95s/it | [Iter 1334/3090] R0[1243/3000] | LR: 0.018988 | E: -63.591267 | E_img: -0.0019j E_var:     2.9117 E_err:   0.018853 | Acc: 0.2988
[2025-11-12 06:41:15] 05:54:48<07:46:26, 15.95s/it | [Iter 1335/3090] R0[1244/3000] | LR: 0.018973 | E: -63.559001 | E_img: -0.0033j E_var:     3.0473 E_err:   0.019287 | Acc: 0.3041
[2025-11-12 06:41:31] 05:55:04<07:46:10, 15.95s/it | [Iter 1336/3090] R0[1245/3000] | LR: 0.018958 | E: -63.600118 | E_img: -0.0054j E_var:     3.0497 E_err:   0.019295 | Acc: 0.3008
[2025-11-12 06:41:47] 05:55:20<07:45:53, 15.95s/it | [Iter 1337/3090] R0[1246/3000] | LR: 0.018943 | E: -63.550384 | E_img: +0.0031j E_var:     2.9524 E_err:   0.018984 | Acc: 0.3099
[2025-11-12 06:42:03] 05:55:36<07:45:37, 15.95s/it | [Iter 1338/3090] R0[1247/3000] | LR: 0.018928 | E: -63.565321 | E_img: -0.0031j E_var:     2.9632 E_err:   0.019019 | Acc: 0.3080
[2025-11-12 06:42:18] 05:55:52<07:45:21, 15.95s/it | [Iter 1339/3090] R0[1248/3000] | LR: 0.018913 | E: -63.605199 | E_img: +0.0179j E_var:     2.9236 E_err:   0.018891 | Acc: 0.2980
[2025-11-12 06:42:34] 05:56:07<07:45:05, 15.95s/it | [Iter 1340/3090] R0[1249/3000] | LR: 0.018897 | E: -63.578289 | E_img: +0.0015j E_var:     2.9842 E_err:   0.019086 | Acc: 0.2945
[2025-11-12 06:42:50] 05:56:23<07:44:49, 15.95s/it | [Iter 1341/3090] R0[1250/3000] | LR: 0.018882 | E: -63.569456 | E_img: +0.0213j E_var:     2.9577 E_err:   0.019001 | Acc: 0.2963
[2025-11-12 06:43:06] 05:56:39<07:44:33, 15.95s/it | [Iter 1342/3090] R0[1251/3000] | LR: 0.018867 | E: -63.524412 | E_img: +0.0025j E_var:     2.9469 E_err:   0.018966 | Acc: 0.3082
[2025-11-12 06:43:22] 05:56:55<07:44:17, 15.95s/it | [Iter 1343/3090] R0[1252/3000] | LR: 0.018852 | E: -63.540974 | E_img: +0.0022j E_var:     2.9475 E_err:   0.018968 | Acc: 0.3046
[2025-11-12 06:43:38] 05:57:11<07:44:01, 15.95s/it | [Iter 1344/3090] R0[1253/3000] | LR: 0.018837 | E: -63.581024 | E_img: -0.0113j E_var:     3.0814 E_err:   0.019395 | Acc: 0.2880
[2025-11-12 06:43:54] 05:57:27<07:43:45, 15.95s/it | [Iter 1345/3090] R0[1254/3000] | LR: 0.018822 | E: -63.626346 | E_img: -0.0085j E_var:     2.9114 E_err:   0.018852 | Acc: 0.2844
[2025-11-12 06:44:09] 05:57:42<07:43:29, 15.95s/it | [Iter 1346/3090] R0[1255/3000] | LR: 0.018806 | E: -63.573770 | E_img: -0.0074j E_var:     2.9090 E_err:   0.018844 | Acc: 0.2898
[2025-11-12 06:44:25] 05:57:58<07:43:13, 15.95s/it | [Iter 1347/3090] R0[1256/3000] | LR: 0.018791 | E: -63.550160 | E_img: +0.0095j E_var:     2.9188 E_err:   0.018876 | Acc: 0.2962
[2025-11-12 06:44:41] 05:58:14<07:42:57, 15.95s/it | [Iter 1348/3090] R0[1257/3000] | LR: 0.018776 | E: -63.573104 | E_img: -0.0344j E_var:     3.3669 E_err:   0.020273 | Acc: 0.2804
[2025-11-12 06:44:57] 05:58:30<07:42:40, 15.95s/it | [Iter 1349/3090] R0[1258/3000] | LR: 0.018761 | E: -63.524354 | E_img: -0.0169j E_var:     3.2736 E_err:   0.019990 | Acc: 0.2687
[2025-11-12 06:45:13] 05:58:46<07:42:24, 15.95s/it | [Iter 1350/3090] R0[1259/3000] | LR: 0.018746 | E: -63.566832 | E_img: +0.0009j E_var:     3.0174 E_err:   0.019192 | Acc: 0.2730
[2025-11-12 06:45:29] 05:59:02<07:42:08, 15.94s/it | [Iter 1351/3090] R0[1260/3000] | LR: 0.018730 | E: -63.589276 | E_img: -0.0078j E_var:     3.0844 E_err:   0.019404 | Acc: 0.2810
[2025-11-12 06:45:44] 05:59:18<07:41:52, 15.94s/it | [Iter 1352/3090] R0[1261/3000] | LR: 0.018715 | E: -63.552103 | E_img: -0.0086j E_var:     2.9220 E_err:   0.018886 | Acc: 0.2917
[2025-11-12 06:46:00] 05:59:33<07:41:36, 15.94s/it | [Iter 1353/3090] R0[1262/3000] | LR: 0.018700 | E: -63.653403 | E_img: -0.0049j E_var:     3.3172 E_err:   0.020123 | Acc: 0.2764
[2025-11-12 06:46:16] 05:59:49<07:41:20, 15.94s/it | [Iter 1354/3090] R0[1263/3000] | LR: 0.018685 | E: -63.684122 | E_img: +0.0042j E_var:     3.0102 E_err:   0.019169 | Acc: 0.2710
[2025-11-12 06:46:32] 06:00:05<07:41:04, 15.94s/it | [Iter 1355/3090] R0[1264/3000] | LR: 0.018669 | E: -63.637317 | E_img: -0.0201j E_var:     3.0191 E_err:   0.019198 | Acc: 0.2803
[2025-11-12 06:46:48] 06:00:21<07:40:48, 15.94s/it | [Iter 1356/3090] R0[1265/3000] | LR: 0.018654 | E: -63.551635 | E_img: -0.0116j E_var:     3.0263 E_err:   0.019220 | Acc: 0.2915
[2025-11-12 06:47:04] 06:00:37<07:40:32, 15.94s/it | [Iter 1357/3090] R0[1266/3000] | LR: 0.018639 | E: -63.573263 | E_img: -0.0204j E_var:     3.1412 E_err:   0.019582 | Acc: 0.2875
[2025-11-12 06:47:19] 06:00:53<07:40:16, 15.94s/it | [Iter 1358/3090] R0[1267/3000] | LR: 0.018624 | E: -63.580245 | E_img: +0.0106j E_var:     3.1924 E_err:   0.019741 | Acc: 0.2770
[2025-11-12 06:47:35] 06:01:08<07:40:00, 15.94s/it | [Iter 1359/3090] R0[1268/3000] | LR: 0.018609 | E: -63.549593 | E_img: -0.0002j E_var:     3.0192 E_err:   0.019198 | Acc: 0.2828
[2025-11-12 06:47:51] 06:01:24<07:39:44, 15.94s/it | [Iter 1360/3090] R0[1269/3000] | LR: 0.018593 | E: -63.499658 | E_img: -0.0059j E_var:     3.0769 E_err:   0.019380 | Acc: 0.2901
[2025-11-12 06:48:07] 06:01:40<07:39:28, 15.94s/it | [Iter 1361/3090] R0[1270/3000] | LR: 0.018578 | E: -63.510713 | E_img: +0.0184j E_var:     3.2211 E_err:   0.019829 | Acc: 0.2802
[2025-11-12 06:48:23] 06:01:56<07:39:12, 15.94s/it | [Iter 1362/3090] R0[1271/3000] | LR: 0.018563 | E: -63.600538 | E_img: -0.0031j E_var:     3.2005 E_err:   0.019766 | Acc: 0.2740
[2025-11-12 06:48:39] 06:02:12<07:38:55, 15.94s/it | [Iter 1363/3090] R0[1272/3000] | LR: 0.018548 | E: -63.625583 | E_img: +0.0013j E_var:     3.3079 E_err:   0.020095 | Acc: 0.2695
[2025-11-12 06:48:55] 06:02:28<07:38:39, 15.94s/it | [Iter 1364/3090] R0[1273/3000] | LR: 0.018532 | E: -63.603925 | E_img: +0.0075j E_var:     3.4281 E_err:   0.020457 | Acc: 0.2608
[2025-11-12 06:49:10] 06:02:43<07:38:23, 15.94s/it | [Iter 1365/3090] R0[1274/3000] | LR: 0.018517 | E: -63.596608 | E_img: -0.0049j E_var:     3.5329 E_err:   0.020767 | Acc: 0.2501
[2025-11-12 06:49:26] 06:02:59<07:38:07, 15.94s/it | [Iter 1366/3090] R0[1275/3000] | LR: 0.018502 | E: -63.596888 | E_img: -0.0061j E_var:     3.5043 E_err:   0.020683 | Acc: 0.2469
[2025-11-12 06:49:42] 06:03:15<07:37:51, 15.94s/it | [Iter 1367/3090] R0[1276/3000] | LR: 0.018486 | E: -63.594449 | E_img: +0.0034j E_var:     3.3830 E_err:   0.020322 | Acc: 0.2491
[2025-11-12 06:49:58] 06:03:31<07:37:35, 15.94s/it | [Iter 1368/3090] R0[1277/3000] | LR: 0.018471 | E: -63.614985 | E_img: -0.0052j E_var:     3.1669 E_err:   0.019662 | Acc: 0.2562
[2025-11-12 06:50:14] 06:03:47<07:37:19, 15.94s/it | [Iter 1369/3090] R0[1278/3000] | LR: 0.018456 | E: -63.634913 | E_img: -0.0026j E_var:     3.1629 E_err:   0.019649 | Acc: 0.2631
[2025-11-12 06:50:30] 06:04:03<07:37:03, 15.94s/it | [Iter 1370/3090] R0[1279/3000] | LR: 0.018441 | E: -63.630648 | E_img: +0.0077j E_var:     3.3121 E_err:   0.020108 | Acc: 0.2616
[2025-11-12 06:50:45] 06:04:19<07:36:47, 15.94s/it | [Iter 1371/3090] R0[1280/3000] | LR: 0.018425 | E: -63.618474 | E_img: +0.0021j E_var:     3.2095 E_err:   0.019794 | Acc: 0.2616
[2025-11-12 06:51:01] 06:04:34<07:36:31, 15.94s/it | [Iter 1372/3090] R0[1281/3000] | LR: 0.018410 | E: -63.578171 | E_img: -0.0046j E_var:     3.2521 E_err:   0.019924 | Acc: 0.2588
[2025-11-12 06:51:17] 06:04:50<07:36:15, 15.94s/it | [Iter 1373/3090] R0[1282/3000] | LR: 0.018395 | E: -63.538249 | E_img: -0.0101j E_var:     3.4661 E_err:   0.020569 | Acc: 0.2556
[2025-11-12 06:51:33] 06:05:06<07:35:59, 15.94s/it | [Iter 1374/3090] R0[1283/3000] | LR: 0.018379 | E: -63.609635 | E_img: -0.0075j E_var:     3.1187 E_err:   0.019512 | Acc: 0.2639
[2025-11-12 06:51:49] 06:05:22<07:35:43, 15.94s/it | [Iter 1375/3090] R0[1284/3000] | LR: 0.018364 | E: -63.586531 | E_img: +0.0005j E_var:     3.1758 E_err:   0.019689 | Acc: 0.2682
[2025-11-12 06:52:05] 06:05:38<07:35:26, 15.94s/it | [Iter 1376/3090] R0[1285/3000] | LR: 0.018349 | E: -63.577342 | E_img: -0.0046j E_var:     3.1482 E_err:   0.019604 | Acc: 0.2757
[2025-11-12 06:52:20] 06:05:54<07:35:10, 15.94s/it | [Iter 1377/3090] R0[1286/3000] | LR: 0.018333 | E: -63.496409 | E_img: +0.0043j E_var:     3.2051 E_err:   0.019780 | Acc: 0.2865
[2025-11-12 06:52:36] 06:06:09<07:34:54, 15.94s/it | [Iter 1378/3090] R0[1287/3000] | LR: 0.018318 | E: -63.595879 | E_img: +0.0024j E_var:     3.0609 E_err:   0.019330 | Acc: 0.2844
[2025-11-12 06:52:52] 06:06:25<07:34:38, 15.94s/it | [Iter 1379/3090] R0[1288/3000] | LR: 0.018303 | E: -63.548326 | E_img: -0.0040j E_var:     3.0302 E_err:   0.019233 | Acc: 0.2895
[2025-11-12 06:53:08] 06:06:41<07:34:22, 15.94s/it | [Iter 1380/3090] R0[1289/3000] | LR: 0.018288 | E: -63.589175 | E_img: +0.0152j E_var:     2.9482 E_err:   0.018971 | Acc: 0.2930
[2025-11-12 06:53:24] 06:06:57<07:34:06, 15.94s/it | [Iter 1381/3090] R0[1290/3000] | LR: 0.018272 | E: -63.554865 | E_img: -0.0179j E_var:     3.0658 E_err:   0.019345 | Acc: 0.2947
[2025-11-12 06:53:40] 06:07:13<07:33:50, 15.94s/it | [Iter 1382/3090] R0[1291/3000] | LR: 0.018257 | E: -63.552061 | E_img: -0.0143j E_var:     2.9729 E_err:   0.019050 | Acc: 0.2969
[2025-11-12 06:53:55] 06:07:29<07:33:34, 15.94s/it | [Iter 1383/3090] R0[1292/3000] | LR: 0.018242 | E: -63.593431 | E_img: +0.0056j E_var:     3.0605 E_err:   0.019329 | Acc: 0.2900
[2025-11-12 06:54:11] 06:07:44<07:33:18, 15.94s/it | [Iter 1384/3090] R0[1293/3000] | LR: 0.018226 | E: -63.605656 | E_img: +0.0075j E_var:     2.9730 E_err:   0.019050 | Acc: 0.2828
[2025-11-12 06:54:27] 06:08:00<07:33:02, 15.94s/it | [Iter 1385/3090] R0[1294/3000] | LR: 0.018211 | E: -63.659171 | E_img: +0.0199j E_var:     3.1264 E_err:   0.019535 | Acc: 0.2754
[2025-11-12 06:54:43] 06:08:16<07:32:46, 15.94s/it | [Iter 1386/3090] R0[1295/3000] | LR: 0.018195 | E: -63.633249 | E_img: +0.0091j E_var:     2.8095 E_err:   0.018519 | Acc: 0.2796
[2025-11-12 06:54:59] 06:08:32<07:32:30, 15.94s/it | [Iter 1387/3090] R0[1296/3000] | LR: 0.018180 | E: -63.600979 | E_img: +0.0084j E_var:     2.9702 E_err:   0.019041 | Acc: 0.2822
[2025-11-12 06:55:15] 06:08:48<07:32:14, 15.94s/it | [Iter 1388/3090] R0[1297/3000] | LR: 0.018165 | E: -63.587857 | E_img: -0.0167j E_var:     2.9682 E_err:   0.019035 | Acc: 0.2827
[2025-11-12 06:55:31] 06:09:04<07:31:58, 15.94s/it | [Iter 1389/3090] R0[1298/3000] | LR: 0.018149 | E: -63.637149 | E_img: +0.0148j E_var:     2.9766 E_err:   0.019062 | Acc: 0.2831
[2025-11-12 06:55:46] 06:09:19<07:31:41, 15.94s/it | [Iter 1390/3090] R0[1299/3000] | LR: 0.018134 | E: -63.574636 | E_img: +0.0052j E_var:     2.9603 E_err:   0.019010 | Acc: 0.2903
[2025-11-12 06:56:02] 06:09:35<07:31:25, 15.94s/it | [Iter 1391/3090] R0[1300/3000] | LR: 0.018119 | E: -63.612150 | E_img: -0.0121j E_var:     2.9876 E_err:   0.019097 | Acc: 0.2845
[2025-11-12 06:56:18] 06:09:51<07:31:09, 15.94s/it | [Iter 1392/3090] R0[1301/3000] | LR: 0.018103 | E: -63.583004 | E_img: -0.0125j E_var:     2.8991 E_err:   0.018812 | Acc: 0.2910
[2025-11-12 06:56:34] 06:10:07<07:30:53, 15.94s/it | [Iter 1393/3090] R0[1302/3000] | LR: 0.018088 | E: -63.605537 | E_img: -0.0080j E_var:     2.9995 E_err:   0.019135 | Acc: 0.2901
[2025-11-12 06:56:50] 06:10:23<07:30:37, 15.94s/it | [Iter 1394/3090] R0[1303/3000] | LR: 0.018073 | E: -63.542245 | E_img: +0.0160j E_var:     3.0571 E_err:   0.019318 | Acc: 0.2887
[2025-11-12 06:57:06] 06:10:39<07:30:21, 15.94s/it | [Iter 1395/3090] R0[1304/3000] | LR: 0.018057 | E: -63.591880 | E_img: +0.0067j E_var:     2.9732 E_err:   0.019051 | Acc: 0.2894
[2025-11-12 06:57:21] 06:10:55<07:30:05, 15.94s/it | [Iter 1396/3090] R0[1305/3000] | LR: 0.018042 | E: -63.641711 | E_img: +0.0094j E_var:     2.9774 E_err:   0.019064 | Acc: 0.2838
[2025-11-12 06:57:37] 06:11:10<07:29:49, 15.94s/it | [Iter 1397/3090] R0[1306/3000] | LR: 0.018026 | E: -63.542224 | E_img: +0.0086j E_var:     3.2737 E_err:   0.019991 | Acc: 0.2778
[2025-11-12 06:57:53] 06:11:26<07:29:33, 15.94s/it | [Iter 1398/3090] R0[1307/3000] | LR: 0.018011 | E: -63.508300 | E_img: +0.0077j E_var:     3.2446 E_err:   0.019901 | Acc: 0.2815
[2025-11-12 06:58:09] 06:11:42<07:29:17, 15.94s/it | [Iter 1399/3090] R0[1308/3000] | LR: 0.017996 | E: -63.591162 | E_img: -0.0076j E_var:     3.1779 E_err:   0.019696 | Acc: 0.2794
[2025-11-12 06:58:25] 06:11:58<07:29:01, 15.94s/it | [Iter 1400/3090] R0[1309/3000] | LR: 0.017980 | E: -63.571378 | E_img: -0.0076j E_var:     3.0327 E_err:   0.019241 | Acc: 0.2747
[2025-11-12 06:58:41] 06:12:14<07:28:45, 15.94s/it | [Iter 1401/3090] R0[1310/3000] | LR: 0.017965 | E: -63.589569 | E_img: -0.0069j E_var:     3.8303 E_err:   0.021623 | Acc: 0.2535
[2025-11-12 06:58:56] 06:12:30<07:28:29, 15.94s/it | [Iter 1402/3090] R0[1311/3000] | LR: 0.017950 | E: -63.584383 | E_img: -0.0178j E_var:     3.4720 E_err:   0.020587 | Acc: 0.2469
[2025-11-12 06:59:12] 06:12:45<07:28:13, 15.94s/it | [Iter 1403/3090] R0[1312/3000] | LR: 0.017934 | E: -63.580112 | E_img: -0.0077j E_var:     3.3594 E_err:   0.020251 | Acc: 0.2486
[2025-11-12 06:59:28] 06:13:01<07:27:57, 15.94s/it | [Iter 1404/3090] R0[1313/3000] | LR: 0.017919 | E: -63.630362 | E_img: +0.0060j E_var:     2.9925 E_err:   0.019113 | Acc: 0.2597
[2025-11-12 06:59:44] 06:13:17<07:27:41, 15.94s/it | [Iter 1405/3090] R0[1314/3000] | LR: 0.017903 | E: -63.665033 | E_img: +0.0293j E_var:     3.0568 E_err:   0.019317 | Acc: 0.2717
[2025-11-12 07:00:00] 06:13:33<07:27:24, 15.94s/it | [Iter 1406/3090] R0[1315/3000] | LR: 0.017888 | E: -63.671591 | E_img: +0.0174j E_var:     3.0530 E_err:   0.019305 | Acc: 0.2729
[2025-11-12 07:00:16] 06:13:49<07:27:08, 15.94s/it | [Iter 1407/3090] R0[1316/3000] | LR: 0.017872 | E: -63.636859 | E_img: -0.0104j E_var:     3.1671 E_err:   0.019662 | Acc: 0.2680
[2025-11-12 07:00:31] 06:14:05<07:26:52, 15.94s/it | [Iter 1408/3090] R0[1317/3000] | LR: 0.017857 | E: -63.610954 | E_img: +0.0053j E_var:     3.0056 E_err:   0.019154 | Acc: 0.2762
[2025-11-12 07:00:47] 06:14:20<07:26:36, 15.94s/it | [Iter 1409/3090] R0[1318/3000] | LR: 0.017842 | E: -63.624284 | E_img: -0.0083j E_var:     2.9479 E_err:   0.018970 | Acc: 0.2868
[2025-11-12 07:01:03] 06:14:36<07:26:20, 15.94s/it | [Iter 1410/3090] R0[1319/3000] | LR: 0.017826 | E: -63.624153 | E_img: +0.0070j E_var:     2.9552 E_err:   0.018993 | Acc: 0.2908
[2025-11-12 07:01:19] 06:14:52<07:26:04, 15.94s/it | [Iter 1411/3090] R0[1320/3000] | LR: 0.017811 | E: -63.552350 | E_img: +0.0165j E_var:     2.9576 E_err:   0.019001 | Acc: 0.2978
[2025-11-12 07:01:35] 06:15:08<07:25:48, 15.94s/it | [Iter 1412/3090] R0[1321/3000] | LR: 0.017795 | E: -63.566065 | E_img: +0.0010j E_var:     2.9608 E_err:   0.019011 | Acc: 0.3006
[2025-11-12 07:01:51] 06:15:24<07:25:32, 15.94s/it | [Iter 1413/3090] R0[1322/3000] | LR: 0.017780 | E: -63.578175 | E_img: +0.0051j E_var:     2.9319 E_err:   0.018918 | Acc: 0.2991
[2025-11-12 07:02:07] 06:15:40<07:25:16, 15.94s/it | [Iter 1414/3090] R0[1323/3000] | LR: 0.017764 | E: -63.600450 | E_img: +0.0082j E_var:     2.9762 E_err:   0.019061 | Acc: 0.2866
[2025-11-12 07:02:22] 06:15:55<07:25:00, 15.94s/it | [Iter 1415/3090] R0[1324/3000] | LR: 0.017749 | E: -63.663110 | E_img: -0.0049j E_var:     2.9395 E_err:   0.018943 | Acc: 0.2870
[2025-11-12 07:02:38] 06:16:11<07:24:44, 15.94s/it | [Iter 1416/3090] R0[1325/3000] | LR: 0.017734 | E: -63.648725 | E_img: -0.0004j E_var:     2.9921 E_err:   0.019112 | Acc: 0.2840
[2025-11-12 07:02:54] 06:16:27<07:24:28, 15.94s/it | [Iter 1417/3090] R0[1326/3000] | LR: 0.017718 | E: -63.637851 | E_img: +0.0006j E_var:     3.4783 E_err:   0.020606 | Acc: 0.2692
[2025-11-12 07:03:10] 06:16:43<07:24:12, 15.94s/it | [Iter 1418/3090] R0[1327/3000] | LR: 0.017703 | E: -63.652238 | E_img: +0.0131j E_var:     3.1106 E_err:   0.019486 | Acc: 0.2682
[2025-11-12 07:03:26] 06:16:59<07:23:56, 15.94s/it | [Iter 1419/3090] R0[1328/3000] | LR: 0.017687 | E: -63.659364 | E_img: +0.0066j E_var:     3.0498 E_err:   0.019295 | Acc: 0.2692
[2025-11-12 07:03:42] 06:17:15<07:23:40, 15.94s/it | [Iter 1420/3090] R0[1329/3000] | LR: 0.017672 | E: -63.649574 | E_img: +0.0128j E_var:     3.1720 E_err:   0.019677 | Acc: 0.2667
[2025-11-12 07:03:57] 06:17:31<07:23:24, 15.94s/it | [Iter 1421/3090] R0[1330/3000] | LR: 0.017656 | E: -63.615640 | E_img: +0.0191j E_var:     3.8467 E_err:   0.021670 | Acc: 0.2515
[2025-11-12 07:04:13] 06:17:46<07:23:08, 15.94s/it | [Iter 1422/3090] R0[1331/3000] | LR: 0.017641 | E: -63.520866 | E_img: +0.0037j E_var:     3.6318 E_err:   0.021056 | Acc: 0.2424
[2025-11-12 07:04:29] 06:18:02<07:22:51, 15.94s/it | [Iter 1423/3090] R0[1332/3000] | LR: 0.017625 | E: -63.639752 | E_img: +0.0138j E_var:     3.0579 E_err:   0.019320 | Acc: 0.2577
[2025-11-12 07:04:45] 06:18:18<07:22:35, 15.94s/it | [Iter 1424/3090] R0[1333/3000] | LR: 0.017610 | E: -63.603976 | E_img: +0.0006j E_var:     3.4105 E_err:   0.020404 | Acc: 0.2587
[2025-11-12 07:05:01] 06:18:34<07:22:19, 15.94s/it | [Iter 1425/3090] R0[1334/3000] | LR: 0.017594 | E: -63.607519 | E_img: +0.0147j E_var:     3.1959 E_err:   0.019751 | Acc: 0.2606
[2025-11-12 07:05:17] 06:18:50<07:22:03, 15.94s/it | [Iter 1426/3090] R0[1335/3000] | LR: 0.017579 | E: -63.643533 | E_img: -0.0230j E_var:     3.0799 E_err:   0.019390 | Acc: 0.2733
[2025-11-12 07:05:32] 06:19:06<07:21:47, 15.94s/it | [Iter 1427/3090] R0[1336/3000] | LR: 0.017564 | E: -63.585134 | E_img: +0.0119j E_var:     3.2906 E_err:   0.020042 | Acc: 0.2698
[2025-11-12 07:05:48] 06:19:21<07:21:31, 15.94s/it | [Iter 1428/3090] R0[1337/3000] | LR: 0.017548 | E: -63.607250 | E_img: +0.0046j E_var:     3.1355 E_err:   0.019564 | Acc: 0.2724
[2025-11-12 07:06:04] 06:19:37<07:21:15, 15.94s/it | [Iter 1429/3090] R0[1338/3000] | LR: 0.017533 | E: -63.642185 | E_img: +0.0073j E_var:     3.1081 E_err:   0.019478 | Acc: 0.2748
[2025-11-12 07:06:20] 06:19:53<07:20:59, 15.94s/it | [Iter 1430/3090] R0[1339/3000] | LR: 0.017517 | E: -63.623906 | E_img: +0.0002j E_var:     3.0131 E_err:   0.019178 | Acc: 0.2763
[2025-11-12 07:06:36] 06:20:09<07:20:43, 15.94s/it | [Iter 1431/3090] R0[1340/3000] | LR: 0.017502 | E: -63.551863 | E_img: -0.0215j E_var:     3.2301 E_err:   0.019857 | Acc: 0.2749
[2025-11-12 07:06:52] 06:20:25<07:20:27, 15.94s/it | [Iter 1432/3090] R0[1341/3000] | LR: 0.017486 | E: -63.632781 | E_img: -0.0079j E_var:     3.3410 E_err:   0.020195 | Acc: 0.2723
[2025-11-12 07:07:08] 06:20:41<07:20:11, 15.94s/it | [Iter 1433/3090] R0[1342/3000] | LR: 0.017471 | E: -63.640852 | E_img: -0.0049j E_var:     3.1192 E_err:   0.019513 | Acc: 0.2725
[2025-11-12 07:07:23] 06:20:56<07:19:55, 15.94s/it | [Iter 1434/3090] R0[1343/3000] | LR: 0.017455 | E: -63.564781 | E_img: +0.0005j E_var:     3.3834 E_err:   0.020323 | Acc: 0.2701
[2025-11-12 07:07:39] 06:21:12<07:19:39, 15.94s/it | [Iter 1435/3090] R0[1344/3000] | LR: 0.017440 | E: -63.615674 | E_img: +0.0036j E_var:     3.2412 E_err:   0.019891 | Acc: 0.2637
[2025-11-12 07:07:55] 06:21:28<07:19:23, 15.94s/it | [Iter 1436/3090] R0[1345/3000] | LR: 0.017424 | E: -63.620987 | E_img: -0.0037j E_var:     2.9807 E_err:   0.019075 | Acc: 0.2707
[2025-11-12 07:08:11] 06:21:44<07:19:07, 15.94s/it | [Iter 1437/3090] R0[1346/3000] | LR: 0.017409 | E: -63.637696 | E_img: -0.0104j E_var:     3.0861 E_err:   0.019409 | Acc: 0.2689
[2025-11-12 07:08:27] 06:22:00<07:18:51, 15.94s/it | [Iter 1438/3090] R0[1347/3000] | LR: 0.017393 | E: -63.672512 | E_img: -0.0063j E_var:     2.9544 E_err:   0.018991 | Acc: 0.2739
[2025-11-12 07:08:43] 06:22:16<07:18:35, 15.94s/it | [Iter 1439/3090] R0[1348/3000] | LR: 0.017378 | E: -63.675920 | E_img: +0.0063j E_var:     3.1194 E_err:   0.019514 | Acc: 0.2739
[2025-11-12 07:08:58] 06:22:32<07:18:19, 15.94s/it | [Iter 1440/3090] R0[1349/3000] | LR: 0.017362 | E: -63.689623 | E_img: +0.0101j E_var:     3.1056 E_err:   0.019470 | Acc: 0.2666
[2025-11-12 07:09:14] 06:22:47<07:18:03, 15.94s/it | [Iter 1441/3090] R0[1350/3000] | LR: 0.017347 | E: -63.659212 | E_img: -0.0059j E_var:     3.2334 E_err:   0.019867 | Acc: 0.2648
[2025-11-12 07:09:30] 06:23:03<07:17:47, 15.94s/it | [Iter 1442/3090] R0[1351/3000] | LR: 0.017331 | E: -63.688669 | E_img: +0.0137j E_var:     3.2687 E_err:   0.019975 | Acc: 0.2686
[2025-11-12 07:09:46] 06:23:19<07:17:30, 15.94s/it | [Iter 1443/3090] R0[1352/3000] | LR: 0.017316 | E: -63.665836 | E_img: -0.0050j E_var:     3.0041 E_err:   0.019150 | Acc: 0.2730
[2025-11-12 07:10:02] 06:23:35<07:17:14, 15.94s/it | [Iter 1444/3090] R0[1353/3000] | LR: 0.017300 | E: -63.649634 | E_img: +0.0083j E_var:     2.8498 E_err:   0.018652 | Acc: 0.2834
[2025-11-12 07:10:18] 06:23:51<07:16:58, 15.94s/it | [Iter 1445/3090] R0[1354/3000] | LR: 0.017284 | E: -63.684161 | E_img: +0.0006j E_var:     2.8704 E_err:   0.018719 | Acc: 0.2807
[2025-11-12 07:10:33] 06:24:07<07:16:42, 15.94s/it | [Iter 1446/3090] R0[1355/3000] | LR: 0.017269 | E: -63.667449 | E_img: -0.0112j E_var:     3.1098 E_err:   0.019484 | Acc: 0.2724
[2025-11-12 07:10:49] 06:24:22<07:16:26, 15.94s/it | [Iter 1447/3090] R0[1356/3000] | LR: 0.017253 | E: -63.659001 | E_img: -0.0077j E_var:     3.0002 E_err:   0.019137 | Acc: 0.2773
[2025-11-12 07:11:05] 06:24:38<07:16:10, 15.94s/it | [Iter 1448/3090] R0[1357/3000] | LR: 0.017238 | E: -63.645222 | E_img: +0.0038j E_var:     3.0868 E_err:   0.019411 | Acc: 0.2798
[2025-11-12 07:11:21] 06:24:54<07:15:54, 15.94s/it | [Iter 1449/3090] R0[1358/3000] | LR: 0.017222 | E: -63.655275 | E_img: -0.0057j E_var:     3.1529 E_err:   0.019618 | Acc: 0.2733
[2025-11-12 07:11:37] 06:25:10<07:15:38, 15.94s/it | [Iter 1450/3090] R0[1359/3000] | LR: 0.017207 | E: -63.667142 | E_img: -0.0048j E_var:     3.0209 E_err:   0.019203 | Acc: 0.2738
[2025-11-12 07:11:53] 06:25:26<07:15:22, 15.94s/it | [Iter 1451/3090] R0[1360/3000] | LR: 0.017191 | E: -63.672074 | E_img: +0.0052j E_var:     3.0097 E_err:   0.019168 | Acc: 0.2727
[2025-11-12 07:12:08] 06:25:42<07:15:06, 15.94s/it | [Iter 1452/3090] R0[1361/3000] | LR: 0.017176 | E: -63.629376 | E_img: -0.0121j E_var:     3.0726 E_err:   0.019367 | Acc: 0.2796
[2025-11-12 07:12:24] 06:25:57<07:14:50, 15.94s/it | [Iter 1453/3090] R0[1362/3000] | LR: 0.017160 | E: -63.659244 | E_img: +0.0045j E_var:     3.1645 E_err:   0.019654 | Acc: 0.2785
[2025-11-12 07:12:40] 06:26:13<07:14:34, 15.94s/it | [Iter 1454/3090] R0[1363/3000] | LR: 0.017145 | E: -63.630303 | E_img: +0.0064j E_var:     2.9723 E_err:   0.019048 | Acc: 0.2827
[2025-11-12 07:12:56] 06:26:29<07:14:18, 15.94s/it | [Iter 1455/3090] R0[1364/3000] | LR: 0.017129 | E: -63.652826 | E_img: -0.0081j E_var:     2.9157 E_err:   0.018866 | Acc: 0.2868
[2025-11-12 07:13:12] 06:26:45<07:14:02, 15.94s/it | [Iter 1456/3090] R0[1365/3000] | LR: 0.017114 | E: -63.588686 | E_img: -0.0065j E_var:     3.2238 E_err:   0.019838 | Acc: 0.2914
[2025-11-12 07:13:28] 06:27:01<07:13:46, 15.94s/it | [Iter 1457/3090] R0[1366/3000] | LR: 0.017098 | E: -63.495932 | E_img: +0.0077j E_var:     3.0139 E_err:   0.019181 | Acc: 0.2977
[2025-11-12 07:13:44] 06:27:17<07:13:30, 15.94s/it | [Iter 1458/3090] R0[1367/3000] | LR: 0.017082 | E: -63.608396 | E_img: +0.0025j E_var:     3.0711 E_err:   0.019362 | Acc: 0.2894
[2025-11-12 07:13:59] 06:27:32<07:13:14, 15.94s/it | [Iter 1459/3090] R0[1368/3000] | LR: 0.017067 | E: -63.588201 | E_img: -0.0126j E_var:     2.9578 E_err:   0.019002 | Acc: 0.2854
[2025-11-12 07:14:15] 06:27:48<07:12:58, 15.94s/it | [Iter 1460/3090] R0[1369/3000] | LR: 0.017051 | E: -63.541876 | E_img: +0.0004j E_var:     3.0129 E_err:   0.019178 | Acc: 0.2979
[2025-11-12 07:14:31] 06:28:04<07:12:42, 15.94s/it | [Iter 1461/3090] R0[1370/3000] | LR: 0.017036 | E: -63.540190 | E_img: +0.0108j E_var:     3.0022 E_err:   0.019144 | Acc: 0.3078
[2025-11-12 07:14:47] 06:28:20<07:12:26, 15.94s/it | [Iter 1462/3090] R0[1371/3000] | LR: 0.017020 | E: -63.546539 | E_img: -0.0054j E_var:     2.8797 E_err:   0.018749 | Acc: 0.3069
[2025-11-12 07:15:03] 06:28:36<07:12:10, 15.94s/it | [Iter 1463/3090] R0[1372/3000] | LR: 0.017005 | E: -63.564087 | E_img: +0.0060j E_var:     2.8601 E_err:   0.018685 | Acc: 0.3037
[2025-11-12 07:15:19] 06:28:52<07:11:53, 15.94s/it | [Iter 1464/3090] R0[1373/3000] | LR: 0.016989 | E: -63.463047 | E_img: -0.0121j E_var:     2.9194 E_err:   0.018878 | Acc: 0.3158
[2025-11-12 07:15:34] 06:29:08<07:11:37, 15.94s/it | [Iter 1465/3090] R0[1374/3000] | LR: 0.016974 | E: -63.562522 | E_img: -0.0048j E_var:     2.8134 E_err:   0.018532 | Acc: 0.3117
[2025-11-12 07:15:50] 06:29:23<07:11:21, 15.94s/it | [Iter 1466/3090] R0[1375/3000] | LR: 0.016958 | E: -63.622148 | E_img: +0.0071j E_var:     2.9134 E_err:   0.018858 | Acc: 0.3030
[2025-11-12 07:16:06] 06:29:39<07:11:05, 15.94s/it | [Iter 1467/3090] R0[1376/3000] | LR: 0.016942 | E: -63.652681 | E_img: -0.0045j E_var:     2.7088 E_err:   0.018184 | Acc: 0.3003
[2025-11-12 07:16:22] 06:29:55<07:10:49, 15.94s/it | [Iter 1468/3090] R0[1377/3000] | LR: 0.016927 | E: -63.599051 | E_img: -0.0022j E_var:     2.7768 E_err:   0.018411 | Acc: 0.3067
[2025-11-12 07:16:38] 06:30:11<07:10:33, 15.94s/it | [Iter 1469/3090] R0[1378/3000] | LR: 0.016911 | E: -63.626460 | E_img: -0.0082j E_var:     2.8009 E_err:   0.018491 | Acc: 0.3062
[2025-11-12 07:16:54] 06:30:27<07:10:17, 15.94s/it | [Iter 1470/3090] R0[1379/3000] | LR: 0.016896 | E: -63.598228 | E_img: -0.0021j E_var:     2.8313 E_err:   0.018591 | Acc: 0.3116
[2025-11-12 07:17:09] 06:30:43<07:10:01, 15.94s/it | [Iter 1471/3090] R0[1380/3000] | LR: 0.016880 | E: -63.652570 | E_img: -0.0003j E_var:     2.8077 E_err:   0.018513 | Acc: 0.3010
[2025-11-12 07:17:25] 06:30:58<07:09:45, 15.94s/it | [Iter 1472/3090] R0[1381/3000] | LR: 0.016864 | E: -63.633567 | E_img: -0.0023j E_var:     2.7728 E_err:   0.018398 | Acc: 0.2978
[2025-11-12 07:17:41] 06:31:14<07:09:29, 15.94s/it | [Iter 1473/3090] R0[1382/3000] | LR: 0.016849 | E: -63.675175 | E_img: -0.0070j E_var:     2.8101 E_err:   0.018521 | Acc: 0.2917
[2025-11-12 07:17:57] 06:31:30<07:09:13, 15.94s/it | [Iter 1474/3090] R0[1383/3000] | LR: 0.016833 | E: -63.621083 | E_img: -0.0008j E_var:     2.6718 E_err:   0.018060 | Acc: 0.3010
[2025-11-12 07:18:13] 06:31:46<07:08:57, 15.94s/it | [Iter 1475/3090] R0[1384/3000] | LR: 0.016818 | E: -63.610587 | E_img: +0.0044j E_var:     2.9766 E_err:   0.019062 | Acc: 0.2974
[2025-11-12 07:18:29] 06:32:02<07:08:41, 15.94s/it | [Iter 1476/3090] R0[1385/3000] | LR: 0.016802 | E: -63.606082 | E_img: -0.0158j E_var:     2.9915 E_err:   0.019110 | Acc: 0.2926
[2025-11-12 07:18:45] 06:32:18<07:08:25, 15.94s/it | [Iter 1477/3090] R0[1386/3000] | LR: 0.016787 | E: -63.600923 | E_img: +0.0169j E_var:     3.1307 E_err:   0.019549 | Acc: 0.2838
[2025-11-12 07:19:00] 06:32:34<07:08:09, 15.94s/it | [Iter 1478/3090] R0[1387/3000] | LR: 0.016771 | E: -63.661283 | E_img: -0.0050j E_var:     2.8401 E_err:   0.018620 | Acc: 0.2840
[2025-11-12 07:19:16] 06:32:49<07:07:53, 15.94s/it | [Iter 1479/3090] R0[1388/3000] | LR: 0.016755 | E: -63.650041 | E_img: -0.0036j E_var:     2.7350 E_err:   0.018272 | Acc: 0.2908
[2025-11-12 07:19:32] 06:33:05<07:07:37, 15.94s/it | [Iter 1480/3090] R0[1389/3000] | LR: 0.016740 | E: -63.652651 | E_img: -0.0092j E_var:     2.6792 E_err:   0.018085 | Acc: 0.2972
[2025-11-12 07:19:48] 06:33:21<07:07:21, 15.94s/it | [Iter 1481/3090] R0[1390/3000] | LR: 0.016724 | E: -63.587687 | E_img: -0.0024j E_var:     2.7621 E_err:   0.018362 | Acc: 0.3091
[2025-11-12 07:20:04] 06:33:37<07:07:05, 15.94s/it | [Iter 1482/3090] R0[1391/3000] | LR: 0.016708 | E: -63.524179 | E_img: +0.0050j E_var:     2.7947 E_err:   0.018470 | Acc: 0.3180
[2025-11-12 07:20:20] 06:33:53<07:06:49, 15.94s/it | [Iter 1483/3090] R0[1392/3000] | LR: 0.016693 | E: -63.507528 | E_img: +0.0085j E_var:     2.8197 E_err:   0.018553 | Acc: 0.3233
[2025-11-12 07:20:35] 06:34:09<07:06:33, 15.94s/it | [Iter 1484/3090] R0[1393/3000] | LR: 0.016677 | E: -63.578077 | E_img: -0.0193j E_var:     2.9196 E_err:   0.018879 | Acc: 0.3132
[2025-11-12 07:20:51] 06:34:24<07:06:17, 15.94s/it | [Iter 1485/3090] R0[1394/3000] | LR: 0.016662 | E: -63.559593 | E_img: -0.0070j E_var:     2.8311 E_err:   0.018590 | Acc: 0.3053
[2025-11-12 07:21:07] 06:34:40<07:06:01, 15.94s/it | [Iter 1486/3090] R0[1395/3000] | LR: 0.016646 | E: -63.597548 | E_img: -0.0106j E_var:     2.7976 E_err:   0.018480 | Acc: 0.3040
[2025-11-12 07:21:23] 06:34:56<07:05:45, 15.94s/it | [Iter 1487/3090] R0[1396/3000] | LR: 0.016630 | E: -63.558537 | E_img: +0.0040j E_var:     2.8996 E_err:   0.018814 | Acc: 0.3118
[2025-11-12 07:21:39] 06:35:12<07:05:29, 15.94s/it | [Iter 1488/3090] R0[1397/3000] | LR: 0.016615 | E: -63.528143 | E_img: +0.0106j E_var:     2.7975 E_err:   0.018479 | Acc: 0.3184
[2025-11-12 07:21:55] 06:35:28<07:05:13, 15.94s/it | [Iter 1489/3090] R0[1398/3000] | LR: 0.016599 | E: -63.576858 | E_img: -0.0008j E_var:     2.7626 E_err:   0.018364 | Acc: 0.3156
[2025-11-12 07:22:11] 06:35:44<07:04:57, 15.94s/it | [Iter 1490/3090] R0[1399/3000] | LR: 0.016584 | E: -63.556818 | E_img: +0.0133j E_var:     2.7480 E_err:   0.018315 | Acc: 0.3152
[2025-11-12 07:22:26] 06:35:59<07:04:41, 15.94s/it | [Iter 1491/3090] R0[1400/3000] | LR: 0.016568 | E: -63.593890 | E_img: +0.0011j E_var:     2.7883 E_err:   0.018449 | Acc: 0.3050
[2025-11-12 07:22:42] 06:36:15<07:04:24, 15.94s/it | [Iter 1492/3090] R0[1401/3000] | LR: 0.016552 | E: -63.553216 | E_img: -0.0030j E_var:     2.7732 E_err:   0.018399 | Acc: 0.3067
[2025-11-12 07:22:58] 06:36:31<07:04:08, 15.94s/it | [Iter 1493/3090] R0[1402/3000] | LR: 0.016537 | E: -63.601699 | E_img: -0.0026j E_var:     2.7701 E_err:   0.018389 | Acc: 0.3011
[2025-11-12 07:23:14] 06:36:47<07:03:52, 15.94s/it | [Iter 1494/3090] R0[1403/3000] | LR: 0.016521 | E: -63.655509 | E_img: -0.0002j E_var:     2.7479 E_err:   0.018315 | Acc: 0.3000
[2025-11-12 07:23:30] 06:37:03<07:03:36, 15.94s/it | [Iter 1495/3090] R0[1404/3000] | LR: 0.016505 | E: -63.627364 | E_img: +0.0017j E_var:     2.7989 E_err:   0.018484 | Acc: 0.2962
[2025-11-12 07:23:46] 06:37:19<07:03:20, 15.94s/it | [Iter 1496/3090] R0[1405/3000] | LR: 0.016490 | E: -63.635307 | E_img: -0.0030j E_var:     2.7017 E_err:   0.018160 | Acc: 0.2926
[2025-11-12 07:24:01] 06:37:35<07:03:04, 15.93s/it | [Iter 1497/3090] R0[1406/3000] | LR: 0.016474 | E: -63.585897 | E_img: +0.0137j E_var:     2.8115 E_err:   0.018526 | Acc: 0.3057
[2025-11-12 07:24:17] 06:37:50<07:02:48, 15.93s/it | [Iter 1498/3090] R0[1407/3000] | LR: 0.016459 | E: -63.594017 | E_img: -0.0003j E_var:     2.7104 E_err:   0.018189 | Acc: 0.3061
[2025-11-12 07:24:33] 06:38:06<07:02:32, 15.93s/it | [Iter 1499/3090] R0[1408/3000] | LR: 0.016443 | E: -63.549586 | E_img: +0.0113j E_var:     2.8160 E_err:   0.018541 | Acc: 0.3062
[2025-11-12 07:24:49] 06:38:22<07:02:16, 15.93s/it | [Iter 1500/3090] R0[1409/3000] | LR: 0.016427 | E: -63.586496 | E_img: +0.0026j E_var:     2.8173 E_err:   0.018545 | Acc: 0.3017
[2025-11-12 07:25:05] 06:38:38<07:02:00, 15.93s/it | [Iter 1501/3090] R0[1410/3000] | LR: 0.016412 | E: -63.522669 | E_img: +0.0027j E_var:     2.9456 E_err:   0.018962 | Acc: 0.2981
[2025-11-12 07:25:21] 06:38:54<07:01:44, 15.93s/it | [Iter 1502/3090] R0[1411/3000] | LR: 0.016396 | E: -63.436058 | E_img: +0.0021j E_var:     3.2110 E_err:   0.019798 | Acc: 0.3036
[2025-11-12 07:25:37] 06:39:10<07:01:28, 15.93s/it | [Iter 1503/3090] R0[1412/3000] | LR: 0.016380 | E: -63.533260 | E_img: +0.0058j E_var:     2.8885 E_err:   0.018778 | Acc: 0.3031
[2025-11-12 07:25:52] 06:39:26<07:01:12, 15.93s/it | [Iter 1504/3090] R0[1413/3000] | LR: 0.016365 | E: -63.518392 | E_img: -0.0043j E_var:     2.8436 E_err:   0.018631 | Acc: 0.3060
[2025-11-12 07:26:08] 06:39:41<07:00:56, 15.93s/it | [Iter 1505/3090] R0[1414/3000] | LR: 0.016349 | E: -63.560646 | E_img: -0.0143j E_var:     2.8457 E_err:   0.018638 | Acc: 0.3027
[2025-11-12 07:26:24] 06:39:57<07:00:40, 15.93s/it | [Iter 1506/3090] R0[1415/3000] | LR: 0.016333 | E: -63.611184 | E_img: -0.0060j E_var:     2.7932 E_err:   0.018465 | Acc: 0.2980
[2025-11-12 07:26:40] 06:40:13<07:00:24, 15.93s/it | [Iter 1507/3090] R0[1416/3000] | LR: 0.016318 | E: -63.679462 | E_img: +0.0045j E_var:     2.9150 E_err:   0.018863 | Acc: 0.2857
[2025-11-12 07:26:56] 06:40:29<07:00:08, 15.93s/it | [Iter 1508/3090] R0[1417/3000] | LR: 0.016302 | E: -63.621002 | E_img: -0.0054j E_var:     2.8824 E_err:   0.018758 | Acc: 0.2837
[2025-11-12 07:27:12] 06:40:45<06:59:52, 15.93s/it | [Iter 1509/3090] R0[1418/3000] | LR: 0.016287 | E: -63.595888 | E_img: +0.0119j E_var:     3.1487 E_err:   0.019605 | Acc: 0.2730
[2025-11-12 07:27:27] 06:41:01<06:59:36, 15.93s/it | [Iter 1510/3090] R0[1419/3000] | LR: 0.016271 | E: -63.694881 | E_img: -0.0246j E_var:     3.0359 E_err:   0.019251 | Acc: 0.2707
[2025-11-12 07:27:43] 06:41:16<06:59:20, 15.93s/it | [Iter 1511/3090] R0[1420/3000] | LR: 0.016255 | E: -63.684636 | E_img: +0.0056j E_var:     2.8872 E_err:   0.018773 | Acc: 0.2765
[2025-11-12 07:27:59] 06:41:32<06:59:04, 15.93s/it | [Iter 1512/3090] R0[1421/3000] | LR: 0.016240 | E: -63.642665 | E_img: -0.0285j E_var:     2.9088 E_err:   0.018843 | Acc: 0.2841
[2025-11-12 07:28:15] 06:41:48<06:58:48, 15.93s/it | [Iter 1513/3090] R0[1422/3000] | LR: 0.016224 | E: -63.513047 | E_img: +0.0061j E_var:     2.9334 E_err:   0.018923 | Acc: 0.2958
[2025-11-12 07:28:31] 06:42:04<06:58:32, 15.93s/it | [Iter 1514/3090] R0[1423/3000] | LR: 0.016208 | E: -63.598484 | E_img: -0.0046j E_var:     2.9355 E_err:   0.018930 | Acc: 0.2899
[2025-11-12 07:28:47] 06:42:20<06:58:16, 15.93s/it | [Iter 1515/3090] R0[1424/3000] | LR: 0.016193 | E: -63.665146 | E_img: +0.0008j E_var:     2.8314 E_err:   0.018591 | Acc: 0.2850
[2025-11-12 07:29:03] 06:42:36<06:58:00, 15.93s/it | [Iter 1516/3090] R0[1425/3000] | LR: 0.016177 | E: -63.637962 | E_img: -0.0009j E_var:     2.9070 E_err:   0.018838 | Acc: 0.2804
[2025-11-12 07:29:18] 06:42:51<06:57:44, 15.93s/it | [Iter 1517/3090] R0[1426/3000] | LR: 0.016161 | E: -63.655958 | E_img: +0.0105j E_var:     2.9614 E_err:   0.019013 | Acc: 0.2747
[2025-11-12 07:29:34] 06:43:07<06:57:28, 15.93s/it | [Iter 1518/3090] R0[1427/3000] | LR: 0.016146 | E: -63.658052 | E_img: -0.0025j E_var:     3.1942 E_err:   0.019746 | Acc: 0.2649
[2025-11-12 07:29:50] 06:43:23<06:57:12, 15.93s/it | [Iter 1519/3090] R0[1428/3000] | LR: 0.016130 | E: -63.654903 | E_img: +0.0078j E_var:     3.2422 E_err:   0.019894 | Acc: 0.2598
[2025-11-12 07:30:06] 06:43:39<06:56:56, 15.93s/it | [Iter 1520/3090] R0[1429/3000] | LR: 0.016114 | E: -63.685723 | E_img: -0.0161j E_var:     3.2459 E_err:   0.019905 | Acc: 0.2571
[2025-11-12 07:30:22] 06:43:55<06:56:40, 15.93s/it | [Iter 1521/3090] R0[1430/3000] | LR: 0.016099 | E: -63.662530 | E_img: -0.0141j E_var:     3.0456 E_err:   0.019281 | Acc: 0.2697
[2025-11-12 07:30:38] 06:44:11<06:56:24, 15.93s/it | [Iter 1522/3090] R0[1431/3000] | LR: 0.016083 | E: -63.641808 | E_img: -0.0073j E_var:     2.9939 E_err:   0.019117 | Acc: 0.2798
[2025-11-12 07:30:53] 06:44:27<06:56:08, 15.93s/it | [Iter 1523/3090] R0[1432/3000] | LR: 0.016067 | E: -63.591576 | E_img: -0.0136j E_var:     2.9262 E_err:   0.018900 | Acc: 0.2804
[2025-11-12 07:31:09] 06:44:42<06:55:52, 15.93s/it | [Iter 1524/3090] R0[1433/3000] | LR: 0.016052 | E: -63.600528 | E_img: -0.0209j E_var:     3.1164 E_err:   0.019505 | Acc: 0.2814
[2025-11-12 07:31:25] 06:44:58<06:55:36, 15.93s/it | [Iter 1525/3090] R0[1434/3000] | LR: 0.016036 | E: -63.613462 | E_img: +0.0063j E_var:     3.0488 E_err:   0.019292 | Acc: 0.2731
[2025-11-12 07:31:41] 06:45:14<06:55:20, 15.93s/it | [Iter 1526/3090] R0[1435/3000] | LR: 0.016020 | E: -63.608350 | E_img: +0.0017j E_var:     3.2424 E_err:   0.019895 | Acc: 0.2662
[2025-11-12 07:31:57] 06:45:30<06:55:04, 15.93s/it | [Iter 1527/3090] R0[1436/3000] | LR: 0.016005 | E: -63.571880 | E_img: +0.0046j E_var:     3.0673 E_err:   0.019350 | Acc: 0.2671
[2025-11-12 07:32:13] 06:45:46<06:54:47, 15.93s/it | [Iter 1528/3090] R0[1437/3000] | LR: 0.015989 | E: -63.678461 | E_img: +0.0015j E_var:     3.3303 E_err:   0.020162 | Acc: 0.2590
[2025-11-12 07:32:28] 06:46:02<06:54:31, 15.93s/it | [Iter 1529/3090] R0[1438/3000] | LR: 0.015973 | E: -63.642135 | E_img: -0.0086j E_var:     2.9882 E_err:   0.019099 | Acc: 0.2745
[2025-11-12 07:32:44] 06:46:17<06:54:15, 15.93s/it | [Iter 1530/3090] R0[1439/3000] | LR: 0.015958 | E: -63.655900 | E_img: +0.0133j E_var:     3.0295 E_err:   0.019231 | Acc: 0.2752
[2025-11-12 07:33:00] 06:46:33<06:53:59, 15.93s/it | [Iter 1531/3090] R0[1440/3000] | LR: 0.015942 | E: -63.701915 | E_img: +0.0050j E_var:     2.9958 E_err:   0.019123 | Acc: 0.2740
[2025-11-12 07:33:16] 06:46:49<06:53:43, 15.93s/it | [Iter 1532/3090] R0[1441/3000] | LR: 0.015926 | E: -63.645283 | E_img: -0.0107j E_var:     2.9902 E_err:   0.019105 | Acc: 0.2709
[2025-11-12 07:33:32] 06:47:05<06:53:27, 15.93s/it | [Iter 1533/3090] R0[1442/3000] | LR: 0.015911 | E: -63.667636 | E_img: -0.0121j E_var:     2.9795 E_err:   0.019071 | Acc: 0.2797
[2025-11-12 07:33:48] 06:47:21<06:53:11, 15.93s/it | [Iter 1534/3090] R0[1443/3000] | LR: 0.015895 | E: -63.649111 | E_img: +0.0089j E_var:     2.8589 E_err:   0.018681 | Acc: 0.2883
[2025-11-12 07:34:04] 06:47:37<06:52:55, 15.93s/it | [Iter 1535/3090] R0[1444/3000] | LR: 0.015879 | E: -63.659082 | E_img: -0.0152j E_var:     2.9377 E_err:   0.018937 | Acc: 0.2854
[2025-11-12 07:34:19] 06:47:53<06:52:39, 15.93s/it | [Iter 1536/3090] R0[1445/3000] | LR: 0.015864 | E: -63.646320 | E_img: +0.0100j E_var:     2.9167 E_err:   0.018869 | Acc: 0.2929
[2025-11-12 07:34:35] 06:48:08<06:52:23, 15.93s/it | [Iter 1537/3090] R0[1446/3000] | LR: 0.015848 | E: -63.659313 | E_img: +0.0107j E_var:     2.7670 E_err:   0.018378 | Acc: 0.2932
[2025-11-12 07:34:51] 06:48:24<06:52:07, 15.93s/it | [Iter 1538/3090] R0[1447/3000] | LR: 0.015832 | E: -63.696565 | E_img: +0.0013j E_var:     2.8302 E_err:   0.018587 | Acc: 0.2866
[2025-11-12 07:35:07] 06:48:40<06:51:51, 15.93s/it | [Iter 1539/3090] R0[1448/3000] | LR: 0.015816 | E: -63.666964 | E_img: -0.0005j E_var:     3.0138 E_err:   0.019181 | Acc: 0.2797
[2025-11-12 07:35:23] 06:48:56<06:51:35, 15.93s/it | [Iter 1540/3090] R0[1449/3000] | LR: 0.015801 | E: -63.687268 | E_img: +0.0026j E_var:     2.7285 E_err:   0.018250 | Acc: 0.2794
[2025-11-12 07:35:39] 06:49:12<06:51:19, 15.93s/it | [Iter 1541/3090] R0[1450/3000] | LR: 0.015785 | E: -63.692311 | E_img: +0.0093j E_var:     3.2806 E_err:   0.020012 | Acc: 0.2661
[2025-11-12 07:35:54] 06:49:28<06:51:03, 15.93s/it | [Iter 1542/3090] R0[1451/3000] | LR: 0.015769 | E: -63.631094 | E_img: -0.0056j E_var:     3.3731 E_err:   0.020292 | Acc: 0.2560
[2025-11-12 07:36:10] 06:49:43<06:50:47, 15.93s/it | [Iter 1543/3090] R0[1452/3000] | LR: 0.015754 | E: -63.695046 | E_img: +0.0050j E_var:     3.1040 E_err:   0.019466 | Acc: 0.2601
[2025-11-12 07:36:26] 06:49:59<06:50:31, 15.93s/it | [Iter 1544/3090] R0[1453/3000] | LR: 0.015738 | E: -63.676998 | E_img: -0.0070j E_var:     3.2659 E_err:   0.019967 | Acc: 0.2592
[2025-11-12 07:36:42] 06:50:15<06:50:15, 15.93s/it | [Iter 1545/3090] R0[1454/3000] | LR: 0.015722 | E: -63.661132 | E_img: -0.0015j E_var:     2.9229 E_err:   0.018889 | Acc: 0.2630
[2025-11-12 07:36:58] 06:50:31<06:49:59, 15.93s/it | [Iter 1546/3090] R0[1455/3000] | LR: 0.015707 | E: -63.610209 | E_img: -0.0053j E_var:     3.0595 E_err:   0.019325 | Acc: 0.2660
[2025-11-12 07:37:14] 06:50:47<06:49:43, 15.93s/it | [Iter 1547/3090] R0[1456/3000] | LR: 0.015691 | E: -63.663274 | E_img: +0.0096j E_var:     3.0739 E_err:   0.019371 | Acc: 0.2696
[2025-11-12 07:37:29] 06:51:03<06:49:27, 15.93s/it | [Iter 1548/3090] R0[1457/3000] | LR: 0.015675 | E: -63.643131 | E_img: +0.0056j E_var:     3.0592 E_err:   0.019324 | Acc: 0.2687
[2025-11-12 07:37:45] 06:51:18<06:49:11, 15.93s/it | [Iter 1549/3090] R0[1458/3000] | LR: 0.015660 | E: -63.659194 | E_img: +0.0037j E_var:     3.3430 E_err:   0.020201 | Acc: 0.2614
[2025-11-12 07:38:01] 06:51:34<06:48:55, 15.93s/it | [Iter 1550/3090] R0[1459/3000] | LR: 0.015644 | E: -63.623771 | E_img: -0.0117j E_var:     3.5705 E_err:   0.020877 | Acc: 0.2538
[2025-11-12 07:38:17] 06:51:50<06:48:39, 15.93s/it | [Iter 1551/3090] R0[1460/3000] | LR: 0.015628 | E: -63.593106 | E_img: +0.0042j E_var:     3.3130 E_err:   0.020110 | Acc: 0.2514
[2025-11-12 07:38:33] 06:52:06<06:48:23, 15.93s/it | [Iter 1552/3090] R0[1461/3000] | LR: 0.015612 | E: -63.655074 | E_img: +0.0017j E_var:     2.9503 E_err:   0.018978 | Acc: 0.2659
[2025-11-12 07:38:49] 06:52:22<06:48:07, 15.93s/it | [Iter 1553/3090] R0[1462/3000] | LR: 0.015597 | E: -63.668356 | E_img: +0.0058j E_var:     2.9755 E_err:   0.019058 | Acc: 0.2719
[2025-11-12 07:39:04] 06:52:38<06:47:51, 15.93s/it | [Iter 1554/3090] R0[1463/3000] | LR: 0.015581 | E: -63.657198 | E_img: -0.0089j E_var:     2.9706 E_err:   0.019043 | Acc: 0.2782
[2025-11-12 07:39:20] 06:52:53<06:47:35, 15.93s/it | [Iter 1555/3090] R0[1464/3000] | LR: 0.015565 | E: -63.648389 | E_img: +0.0031j E_var:     3.0726 E_err:   0.019367 | Acc: 0.2796
[2025-11-12 07:39:36] 06:53:09<06:47:19, 15.93s/it | [Iter 1556/3090] R0[1465/3000] | LR: 0.015550 | E: -63.651752 | E_img: -0.0050j E_var:     3.2287 E_err:   0.019853 | Acc: 0.2695
[2025-11-12 07:39:52] 06:53:25<06:47:03, 15.93s/it | [Iter 1557/3090] R0[1466/3000] | LR: 0.015534 | E: -63.647314 | E_img: +0.0067j E_var:     3.1364 E_err:   0.019567 | Acc: 0.2625
[2025-11-12 07:40:08] 06:53:41<06:46:47, 15.93s/it | [Iter 1558/3090] R0[1467/3000] | LR: 0.015518 | E: -63.656932 | E_img: -0.0176j E_var:     3.1000 E_err:   0.019453 | Acc: 0.2656
[2025-11-12 07:40:24] 06:53:57<06:46:31, 15.93s/it | [Iter 1559/3090] R0[1468/3000] | LR: 0.015503 | E: -63.673745 | E_img: -0.0041j E_var:     3.1423 E_err:   0.019585 | Acc: 0.2663
[2025-11-12 07:40:40] 06:54:13<06:46:15, 15.93s/it | [Iter 1560/3090] R0[1469/3000] | LR: 0.015487 | E: -63.684147 | E_img: +0.0051j E_var:     3.2493 E_err:   0.019916 | Acc: 0.2596
[2025-11-12 07:40:55] 06:54:28<06:45:59, 15.93s/it | [Iter 1561/3090] R0[1470/3000] | LR: 0.015471 | E: -63.683738 | E_img: -0.0079j E_var:     2.9557 E_err:   0.018995 | Acc: 0.2634
[2025-11-12 07:41:11] 06:54:44<06:45:43, 15.93s/it | [Iter 1562/3090] R0[1471/3000] | LR: 0.015456 | E: -63.692263 | E_img: +0.0052j E_var:     2.8886 E_err:   0.018778 | Acc: 0.2792
[2025-11-12 07:41:27] 06:55:00<06:45:27, 15.93s/it | [Iter 1563/3090] R0[1472/3000] | LR: 0.015440 | E: -63.659149 | E_img: +0.0048j E_var:     2.7929 E_err:   0.018464 | Acc: 0.2861
[2025-11-12 07:41:43] 06:55:16<06:45:11, 15.93s/it | [Iter 1564/3090] R0[1473/3000] | LR: 0.015424 | E: -63.676027 | E_img: +0.0121j E_var:     2.7546 E_err:   0.018337 | Acc: 0.2848
[2025-11-12 07:41:59] 06:55:32<06:44:55, 15.93s/it | [Iter 1565/3090] R0[1474/3000] | LR: 0.015408 | E: -63.671785 | E_img: -0.0062j E_var:     2.9280 E_err:   0.018906 | Acc: 0.2846
[2025-11-12 07:42:15] 06:55:48<06:44:39, 15.93s/it | [Iter 1566/3090] R0[1475/3000] | LR: 0.015393 | E: -63.689233 | E_img: -0.0007j E_var:     3.0879 E_err:   0.019415 | Acc: 0.2742
[2025-11-12 07:42:30] 06:56:04<06:44:23, 15.93s/it | [Iter 1567/3090] R0[1476/3000] | LR: 0.015377 | E: -63.676275 | E_img: +0.0100j E_var:     3.0950 E_err:   0.019437 | Acc: 0.2684
[2025-11-12 07:42:46] 06:56:19<06:44:07, 15.93s/it | [Iter 1568/3090] R0[1477/3000] | LR: 0.015361 | E: -63.655438 | E_img: -0.0009j E_var:     3.0532 E_err:   0.019306 | Acc: 0.2663
[2025-11-12 07:43:02] 06:56:35<06:43:51, 15.93s/it | [Iter 1569/3090] R0[1478/3000] | LR: 0.015346 | E: -63.707076 | E_img: -0.0055j E_var:     2.7826 E_err:   0.018430 | Acc: 0.2797
[2025-11-12 07:43:18] 06:56:51<06:43:35, 15.93s/it | [Iter 1570/3090] R0[1479/3000] | LR: 0.015330 | E: -63.742976 | E_img: +0.0054j E_var:     2.8340 E_err:   0.018600 | Acc: 0.2775
[2025-11-12 07:43:34] 06:57:07<06:43:19, 15.93s/it | [Iter 1571/3090] R0[1480/3000] | LR: 0.015314 | E: -63.718670 | E_img: -0.0171j E_var:     3.1629 E_err:   0.019649 | Acc: 0.2654
[2025-11-12 07:43:50] 06:57:23<06:43:02, 15.93s/it | [Iter 1572/3090] R0[1481/3000] | LR: 0.015298 | E: -63.672732 | E_img: +0.0082j E_var:     2.9963 E_err:   0.019125 | Acc: 0.2620
[2025-11-12 07:44:05] 06:57:39<06:42:46, 15.93s/it | [Iter 1573/3090] R0[1482/3000] | LR: 0.015283 | E: -63.656099 | E_img: -0.0172j E_var:     3.1936 E_err:   0.019744 | Acc: 0.2541
[2025-11-12 07:44:21] 06:57:54<06:42:30, 15.93s/it | [Iter 1574/3090] R0[1483/3000] | LR: 0.015267 | E: -63.654735 | E_img: +0.0148j E_var:     3.4357 E_err:   0.020479 | Acc: 0.2471
[2025-11-12 07:44:37] 06:58:10<06:42:14, 15.93s/it | [Iter 1575/3090] R0[1484/3000] | LR: 0.015251 | E: -63.623906 | E_img: -0.0179j E_var:     3.6260 E_err:   0.021039 | Acc: 0.2382
[2025-11-12 07:44:53] 06:58:26<06:41:58, 15.93s/it | [Iter 1576/3090] R0[1485/3000] | LR: 0.015236 | E: -63.615526 | E_img: -0.0023j E_var:     3.6954 E_err:   0.021239 | Acc: 0.2363
[2025-11-12 07:45:09] 06:58:42<06:41:42, 15.93s/it | [Iter 1577/3090] R0[1486/3000] | LR: 0.015220 | E: -63.622175 | E_img: -0.0131j E_var:     3.1911 E_err:   0.019737 | Acc: 0.2409
[2025-11-12 07:45:25] 06:58:58<06:41:26, 15.93s/it | [Iter 1578/3090] R0[1487/3000] | LR: 0.015204 | E: -63.623870 | E_img: -0.0169j E_var:     3.4715 E_err:   0.020586 | Acc: 0.2378
[2025-11-12 07:45:40] 06:59:14<06:41:10, 15.93s/it | [Iter 1579/3090] R0[1488/3000] | LR: 0.015189 | E: -63.626605 | E_img: +0.0046j E_var:     3.3687 E_err:   0.020279 | Acc: 0.2366
[2025-11-12 07:45:56] 06:59:29<06:40:54, 15.93s/it | [Iter 1580/3090] R0[1489/3000] | LR: 0.015173 | E: -63.600493 | E_img: +0.0109j E_var:     3.3480 E_err:   0.020216 | Acc: 0.2387
[2025-11-12 07:46:12] 06:59:45<06:40:38, 15.93s/it | [Iter 1581/3090] R0[1490/3000] | LR: 0.015157 | E: -63.631873 | E_img: +0.0062j E_var:     3.0006 E_err:   0.019138 | Acc: 0.2540
[2025-11-12 07:46:28] 07:00:01<06:40:22, 15.93s/it | [Iter 1582/3090] R0[1491/3000] | LR: 0.015141 | E: -63.625717 | E_img: -0.0161j E_var:     3.5854 E_err:   0.020921 | Acc: 0.2502
[2025-11-12 07:46:44] 07:00:17<06:40:06, 15.93s/it | [Iter 1583/3090] R0[1492/3000] | LR: 0.015126 | E: -63.639741 | E_img: +0.0062j E_var:     3.1626 E_err:   0.019648 | Acc: 0.2496
[2025-11-12 07:47:00] 07:00:33<06:39:50, 15.93s/it | [Iter 1584/3090] R0[1493/3000] | LR: 0.015110 | E: -63.616600 | E_img: -0.0049j E_var:     3.0841 E_err:   0.019403 | Acc: 0.2719
[2025-11-12 07:47:16] 07:00:49<06:39:34, 15.93s/it | [Iter 1585/3090] R0[1494/3000] | LR: 0.015094 | E: -63.578181 | E_img: -0.0094j E_var:     3.1018 E_err:   0.019459 | Acc: 0.2851
[2025-11-12 07:47:31] 07:01:04<06:39:18, 15.93s/it | [Iter 1586/3090] R0[1495/3000] | LR: 0.015079 | E: -63.614012 | E_img: -0.0020j E_var:     3.1875 E_err:   0.019726 | Acc: 0.2761
[2025-11-12 07:47:47] 07:01:20<06:39:02, 15.93s/it | [Iter 1587/3090] R0[1496/3000] | LR: 0.015063 | E: -63.681245 | E_img: +0.0207j E_var:     3.2531 E_err:   0.019928 | Acc: 0.2617
[2025-11-12 07:48:03] 07:01:36<06:38:46, 15.93s/it | [Iter 1588/3090] R0[1497/3000] | LR: 0.015047 | E: -63.722915 | E_img: +0.0060j E_var:     2.9805 E_err:   0.019074 | Acc: 0.2669
[2025-11-12 07:48:19] 07:01:52<06:38:30, 15.93s/it | [Iter 1589/3090] R0[1498/3000] | LR: 0.015031 | E: -63.665418 | E_img: -0.0087j E_var:     3.0006 E_err:   0.019139 | Acc: 0.2703
[2025-11-12 07:48:35] 07:02:08<06:38:14, 15.93s/it | [Iter 1590/3090] R0[1499/3000] | LR: 0.015016 | E: -63.638080 | E_img: -0.0052j E_var:     3.0256 E_err:   0.019218 | Acc: 0.2759
[2025-11-12 07:48:35] ✓ Checkpoint saved: checkpoint_iter_001500.pkl
[2025-11-12 07:48:51] 07:02:24<06:37:58, 15.93s/it | [Iter 1591/3090] R0[1500/3000] | LR: 0.015000 | E: -63.669357 | E_img: -0.0037j E_var:     3.0238 E_err:   0.019212 | Acc: 0.2695
[2025-11-12 07:49:07] 07:02:40<06:37:42, 15.93s/it | [Iter 1592/3090] R0[1501/3000] | LR: 0.014984 | E: -63.698815 | E_img: -0.0093j E_var:     3.2833 E_err:   0.020020 | Acc: 0.2622
[2025-11-12 07:49:22] 07:02:55<06:37:26, 15.93s/it | [Iter 1593/3090] R0[1502/3000] | LR: 0.014969 | E: -63.684697 | E_img: +0.0186j E_var:     2.8983 E_err:   0.018810 | Acc: 0.2719
[2025-11-12 07:49:38] 07:03:11<06:37:10, 15.93s/it | [Iter 1594/3090] R0[1503/3000] | LR: 0.014953 | E: -63.682518 | E_img: +0.0023j E_var:     3.1202 E_err:   0.019516 | Acc: 0.2703
[2025-11-12 07:49:54] 07:03:27<06:36:54, 15.93s/it | [Iter 1595/3090] R0[1504/3000] | LR: 0.014937 | E: -63.656453 | E_img: +0.0041j E_var:     3.1008 E_err:   0.019455 | Acc: 0.2796
[2025-11-12 07:50:10] 07:03:43<06:36:38, 15.93s/it | [Iter 1596/3090] R0[1505/3000] | LR: 0.014922 | E: -63.657245 | E_img: +0.0006j E_var:     3.0421 E_err:   0.019271 | Acc: 0.2783
[2025-11-12 07:50:26] 07:03:59<06:36:22, 15.93s/it | [Iter 1597/3090] R0[1506/3000] | LR: 0.014906 | E: -63.652140 | E_img: +0.0048j E_var:     2.8330 E_err:   0.018596 | Acc: 0.2853
[2025-11-12 07:50:42] 07:04:15<06:36:06, 15.93s/it | [Iter 1598/3090] R0[1507/3000] | LR: 0.014890 | E: -63.665271 | E_img: -0.0114j E_var:     2.9220 E_err:   0.018886 | Acc: 0.2816
[2025-11-12 07:50:57] 07:04:31<06:35:50, 15.93s/it | [Iter 1599/3090] R0[1508/3000] | LR: 0.014874 | E: -63.721978 | E_img: +0.0015j E_var:     2.8450 E_err:   0.018636 | Acc: 0.2791
[2025-11-12 07:51:13] 07:04:46<06:35:34, 15.93s/it | [Iter 1600/3090] R0[1509/3000] | LR: 0.014859 | E: -63.692776 | E_img: -0.0098j E_var:     2.8869 E_err:   0.018772 | Acc: 0.2778
[2025-11-12 07:51:29] 07:05:02<06:35:18, 15.93s/it | [Iter 1601/3090] R0[1510/3000] | LR: 0.014843 | E: -63.712939 | E_img: +0.0078j E_var:     2.9945 E_err:   0.019119 | Acc: 0.2761
[2025-11-12 07:51:45] 07:05:18<06:35:02, 15.93s/it | [Iter 1602/3090] R0[1511/3000] | LR: 0.014827 | E: -63.688855 | E_img: -0.0151j E_var:     2.7919 E_err:   0.018461 | Acc: 0.2790
[2025-11-12 07:52:01] 07:05:34<06:34:46, 15.93s/it | [Iter 1603/3090] R0[1512/3000] | LR: 0.014812 | E: -63.701967 | E_img: -0.0076j E_var:     2.8380 E_err:   0.018613 | Acc: 0.2831
[2025-11-12 07:52:17] 07:05:50<06:34:30, 15.93s/it | [Iter 1604/3090] R0[1513/3000] | LR: 0.014796 | E: -63.674760 | E_img: +0.0015j E_var:     2.9152 E_err:   0.018864 | Acc: 0.2860
[2025-11-12 07:52:33] 07:06:06<06:34:14, 15.93s/it | [Iter 1605/3090] R0[1514/3000] | LR: 0.014780 | E: -63.658348 | E_img: +0.0088j E_var:     2.8038 E_err:   0.018500 | Acc: 0.2860
[2025-11-12 07:52:48] 07:06:21<06:33:58, 15.93s/it | [Iter 1606/3090] R0[1515/3000] | LR: 0.014764 | E: -63.629943 | E_img: +0.0045j E_var:     2.8382 E_err:   0.018613 | Acc: 0.2842
[2025-11-12 07:53:04] 07:06:37<06:33:42, 15.93s/it | [Iter 1607/3090] R0[1516/3000] | LR: 0.014749 | E: -63.678502 | E_img: +0.0098j E_var:     2.9578 E_err:   0.019002 | Acc: 0.2770
[2025-11-12 07:53:20] 07:06:53<06:33:26, 15.93s/it | [Iter 1608/3090] R0[1517/3000] | LR: 0.014733 | E: -63.643073 | E_img: -0.0014j E_var:     2.7426 E_err:   0.018297 | Acc: 0.2833
[2025-11-12 07:53:36] 07:07:09<06:33:10, 15.93s/it | [Iter 1609/3090] R0[1518/3000] | LR: 0.014717 | E: -63.658460 | E_img: +0.0168j E_var:     2.9431 E_err:   0.018954 | Acc: 0.2873
[2025-11-12 07:53:52] 07:07:25<06:32:54, 15.93s/it | [Iter 1610/3090] R0[1519/3000] | LR: 0.014702 | E: -63.574698 | E_img: +0.0004j E_var:     2.9548 E_err:   0.018992 | Acc: 0.2874
[2025-11-12 07:54:08] 07:07:41<06:32:38, 15.93s/it | [Iter 1611/3090] R0[1520/3000] | LR: 0.014686 | E: -63.678202 | E_img: +0.0029j E_var:     3.0281 E_err:   0.019226 | Acc: 0.2773
[2025-11-12 07:54:23] 07:07:57<06:32:22, 15.93s/it | [Iter 1612/3090] R0[1521/3000] | LR: 0.014670 | E: -63.705903 | E_img: +0.0072j E_var:     3.2292 E_err:   0.019854 | Acc: 0.2619
[2025-11-12 07:54:39] 07:08:12<06:32:06, 15.93s/it | [Iter 1613/3090] R0[1522/3000] | LR: 0.014655 | E: -63.723162 | E_img: +0.0037j E_var:     3.1241 E_err:   0.019528 | Acc: 0.2541
[2025-11-12 07:54:55] 07:08:28<06:31:50, 15.93s/it | [Iter 1614/3090] R0[1523/3000] | LR: 0.014639 | E: -63.656858 | E_img: +0.0010j E_var:     3.4702 E_err:   0.020582 | Acc: 0.2450
[2025-11-12 07:55:11] 07:08:44<06:31:34, 15.93s/it | [Iter 1615/3090] R0[1524/3000] | LR: 0.014623 | E: -63.703154 | E_img: -0.0090j E_var:     3.0795 E_err:   0.019388 | Acc: 0.2462
[2025-11-12 07:55:27] 07:09:00<06:31:18, 15.93s/it | [Iter 1616/3090] R0[1525/3000] | LR: 0.014607 | E: -63.638999 | E_img: -0.0005j E_var:     3.1711 E_err:   0.019675 | Acc: 0.2459
[2025-11-12 07:55:43] 07:09:16<06:31:02, 15.93s/it | [Iter 1617/3090] R0[1526/3000] | LR: 0.014592 | E: -63.678606 | E_img: -0.0017j E_var:     3.0522 E_err:   0.019302 | Acc: 0.2501
[2025-11-12 07:55:59] 07:09:32<06:30:46, 15.93s/it | [Iter 1618/3090] R0[1527/3000] | LR: 0.014576 | E: -63.701049 | E_img: -0.0113j E_var:     2.9838 E_err:   0.019085 | Acc: 0.2585
[2025-11-12 07:56:14] 07:09:47<06:30:30, 15.93s/it | [Iter 1619/3090] R0[1528/3000] | LR: 0.014560 | E: -63.690555 | E_img: -0.0183j E_var:     3.1044 E_err:   0.019467 | Acc: 0.2569
[2025-11-12 07:56:30] 07:10:03<06:30:14, 15.93s/it | [Iter 1620/3090] R0[1529/3000] | LR: 0.014545 | E: -63.722037 | E_img: -0.0013j E_var:     2.9815 E_err:   0.019077 | Acc: 0.2576
[2025-11-12 07:56:46] 07:10:19<06:29:58, 15.93s/it | [Iter 1621/3090] R0[1530/3000] | LR: 0.014529 | E: -63.705131 | E_img: +0.0147j E_var:     2.9656 E_err:   0.019027 | Acc: 0.2584
[2025-11-12 07:57:02] 07:10:35<06:29:42, 15.93s/it | [Iter 1622/3090] R0[1531/3000] | LR: 0.014513 | E: -63.714174 | E_img: +0.0060j E_var:     3.2601 E_err:   0.019949 | Acc: 0.2513
[2025-11-12 07:57:18] 07:10:51<06:29:26, 15.93s/it | [Iter 1623/3090] R0[1532/3000] | LR: 0.014497 | E: -63.697162 | E_img: +0.0112j E_var:     2.9046 E_err:   0.018830 | Acc: 0.2690
[2025-11-12 07:57:34] 07:11:07<06:29:10, 15.93s/it | [Iter 1624/3090] R0[1533/3000] | LR: 0.014482 | E: -63.688230 | E_img: -0.0050j E_var:     2.8800 E_err:   0.018750 | Acc: 0.2878
[2025-11-12 07:57:49] 07:11:23<06:28:54, 15.93s/it | [Iter 1625/3090] R0[1534/3000] | LR: 0.014466 | E: -63.620318 | E_img: +0.0031j E_var:     2.8611 E_err:   0.018688 | Acc: 0.2960
[2025-11-12 07:58:05] 07:11:38<06:28:38, 15.93s/it | [Iter 1626/3090] R0[1535/3000] | LR: 0.014450 | E: -63.653285 | E_img: -0.0145j E_var:     2.7650 E_err:   0.018372 | Acc: 0.2913
[2025-11-12 07:58:21] 07:11:54<06:28:22, 15.93s/it | [Iter 1627/3090] R0[1536/3000] | LR: 0.014435 | E: -63.623860 | E_img: -0.0231j E_var:     2.9708 E_err:   0.019043 | Acc: 0.2923
[2025-11-12 07:58:37] 07:12:10<06:28:06, 15.93s/it | [Iter 1628/3090] R0[1537/3000] | LR: 0.014419 | E: -63.556645 | E_img: -0.0006j E_var:     3.1642 E_err:   0.019653 | Acc: 0.2854
[2025-11-12 07:58:53] 07:12:26<06:27:50, 15.93s/it | [Iter 1629/3090] R0[1538/3000] | LR: 0.014403 | E: -63.640069 | E_img: -0.0007j E_var:     3.0151 E_err:   0.019185 | Acc: 0.2797
[2025-11-12 07:59:09] 07:12:42<06:27:34, 15.93s/it | [Iter 1630/3090] R0[1539/3000] | LR: 0.014388 | E: -63.543412 | E_img: -0.0054j E_var:     3.0070 E_err:   0.019159 | Acc: 0.2852
[2025-11-12 07:59:25] 07:12:58<06:27:18, 15.93s/it | [Iter 1631/3090] R0[1540/3000] | LR: 0.014372 | E: -63.522081 | E_img: +0.0106j E_var:     3.0071 E_err:   0.019159 | Acc: 0.2886
[2025-11-12 07:59:40] 07:13:14<06:27:02, 15.93s/it | [Iter 1632/3090] R0[1541/3000] | LR: 0.014356 | E: -63.650016 | E_img: +0.0034j E_var:     2.9695 E_err:   0.019039 | Acc: 0.2855
[2025-11-12 07:59:56] 07:13:29<06:26:46, 15.93s/it | [Iter 1633/3090] R0[1542/3000] | LR: 0.014341 | E: -63.579494 | E_img: +0.0060j E_var:     3.0037 E_err:   0.019148 | Acc: 0.2837
[2025-11-12 08:00:12] 07:13:45<06:26:30, 15.93s/it | [Iter 1634/3090] R0[1543/3000] | LR: 0.014325 | E: -63.604150 | E_img: -0.0053j E_var:     2.8966 E_err:   0.018804 | Acc: 0.2872
[2025-11-12 08:00:28] 07:14:01<06:26:14, 15.93s/it | [Iter 1635/3090] R0[1544/3000] | LR: 0.014309 | E: -63.553707 | E_img: -0.0088j E_var:     2.9728 E_err:   0.019050 | Acc: 0.2875
[2025-11-12 08:00:44] 07:14:17<06:25:58, 15.93s/it | [Iter 1636/3090] R0[1545/3000] | LR: 0.014293 | E: -63.602945 | E_img: +0.0009j E_var:     2.9801 E_err:   0.019073 | Acc: 0.2816
[2025-11-12 08:01:00] 07:14:33<06:25:42, 15.93s/it | [Iter 1637/3090] R0[1546/3000] | LR: 0.014278 | E: -63.521897 | E_img: -0.0146j E_var:     3.0085 E_err:   0.019164 | Acc: 0.2914
[2025-11-12 08:01:15] 07:14:49<06:25:26, 15.93s/it | [Iter 1638/3090] R0[1547/3000] | LR: 0.014262 | E: -63.571498 | E_img: -0.0100j E_var:     3.1371 E_err:   0.019569 | Acc: 0.2808
[2025-11-12 08:01:31] 07:15:04<06:25:10, 15.93s/it | [Iter 1639/3090] R0[1548/3000] | LR: 0.014246 | E: -63.485317 | E_img: -0.0324j E_var:     3.2001 E_err:   0.019765 | Acc: 0.2753
[2025-11-12 08:01:47] 07:15:20<06:24:54, 15.93s/it | [Iter 1640/3090] R0[1549/3000] | LR: 0.014231 | E: -63.504014 | E_img: -0.0121j E_var:     3.4307 E_err:   0.020464 | Acc: 0.2635
[2025-11-12 08:02:03] 07:15:36<06:24:38, 15.93s/it | [Iter 1641/3090] R0[1550/3000] | LR: 0.014215 | E: -63.492506 | E_img: -0.0160j E_var:     3.1042 E_err:   0.019466 | Acc: 0.2691
[2025-11-12 08:02:19] 07:15:52<06:24:22, 15.93s/it | [Iter 1642/3090] R0[1551/3000] | LR: 0.014199 | E: -63.524090 | E_img: -0.0109j E_var:     3.2290 E_err:   0.019854 | Acc: 0.2670
[2025-11-12 08:02:35] 07:16:08<06:24:06, 15.93s/it | [Iter 1643/3090] R0[1552/3000] | LR: 0.014184 | E: -63.541935 | E_img: -0.0004j E_var:     3.3651 E_err:   0.020268 | Acc: 0.2580
[2025-11-12 08:02:50] 07:16:24<06:23:50, 15.93s/it | [Iter 1644/3090] R0[1553/3000] | LR: 0.014168 | E: -63.489672 | E_img: -0.0165j E_var:     3.4390 E_err:   0.020489 | Acc: 0.2579
[2025-11-12 08:03:06] 07:16:39<06:23:34, 15.93s/it | [Iter 1645/3090] R0[1554/3000] | LR: 0.014152 | E: -63.519847 | E_img: -0.0150j E_var:     3.2596 E_err:   0.019947 | Acc: 0.2620
[2025-11-12 08:03:22] 07:16:55<06:23:18, 15.93s/it | [Iter 1646/3090] R0[1555/3000] | LR: 0.014137 | E: -63.438442 | E_img: +0.0072j E_var:     3.5165 E_err:   0.020719 | Acc: 0.2576
[2025-11-12 08:03:38] 07:17:11<06:23:02, 15.93s/it | [Iter 1647/3090] R0[1556/3000] | LR: 0.014121 | E: -63.510611 | E_img: -0.0017j E_var:     3.2979 E_err:   0.020064 | Acc: 0.2575
[2025-11-12 08:03:54] 07:17:27<06:22:46, 15.93s/it | [Iter 1648/3090] R0[1557/3000] | LR: 0.014105 | E: -63.599872 | E_img: +0.0016j E_var:     3.1564 E_err:   0.019629 | Acc: 0.2624
[2025-11-12 08:04:10] 07:17:43<06:22:30, 15.93s/it | [Iter 1649/3090] R0[1558/3000] | LR: 0.014090 | E: -63.559835 | E_img: +0.0009j E_var:     3.1739 E_err:   0.019683 | Acc: 0.2670
[2025-11-12 08:04:26] 07:17:59<06:22:14, 15.93s/it | [Iter 1650/3090] R0[1559/3000] | LR: 0.014074 | E: -63.641906 | E_img: -0.0084j E_var:     2.9601 E_err:   0.019009 | Acc: 0.2732
[2025-11-12 08:04:41] 07:18:14<06:21:58, 15.93s/it | [Iter 1651/3090] R0[1560/3000] | LR: 0.014058 | E: -63.611456 | E_img: -0.0022j E_var:     3.0708 E_err:   0.019361 | Acc: 0.2682
[2025-11-12 08:04:57] 07:18:30<06:21:42, 15.93s/it | [Iter 1652/3090] R0[1561/3000] | LR: 0.014043 | E: -63.638586 | E_img: +0.0023j E_var:     3.1704 E_err:   0.019673 | Acc: 0.2624
[2025-11-12 08:05:13] 07:18:46<06:21:26, 15.93s/it | [Iter 1653/3090] R0[1562/3000] | LR: 0.014027 | E: -63.645138 | E_img: -0.0001j E_var:     3.0537 E_err:   0.019307 | Acc: 0.2612
[2025-11-12 08:05:29] 07:19:02<06:21:10, 15.93s/it | [Iter 1654/3090] R0[1563/3000] | LR: 0.014011 | E: -63.607797 | E_img: -0.0096j E_var:     3.1791 E_err:   0.019700 | Acc: 0.2679
[2025-11-12 08:05:45] 07:19:18<06:20:54, 15.93s/it | [Iter 1655/3090] R0[1564/3000] | LR: 0.013995 | E: -63.578889 | E_img: +0.0243j E_var:     2.8322 E_err:   0.018594 | Acc: 0.2805
[2025-11-12 08:06:01] 07:19:34<06:20:38, 15.93s/it | [Iter 1656/3090] R0[1565/3000] | LR: 0.013980 | E: -63.609402 | E_img: -0.0145j E_var:     2.8838 E_err:   0.018762 | Acc: 0.2916
[2025-11-12 08:06:16] 07:19:50<06:20:22, 15.93s/it | [Iter 1657/3090] R0[1566/3000] | LR: 0.013964 | E: -63.548551 | E_img: +0.0072j E_var:     2.9609 E_err:   0.019012 | Acc: 0.2943
[2025-11-12 08:06:32] 07:20:05<06:20:06, 15.93s/it | [Iter 1658/3090] R0[1567/3000] | LR: 0.013948 | E: -63.572955 | E_img: +0.0164j E_var:     2.9817 E_err:   0.019078 | Acc: 0.2861
[2025-11-12 08:06:48] 07:20:21<06:19:50, 15.93s/it | [Iter 1659/3090] R0[1568/3000] | LR: 0.013933 | E: -63.674335 | E_img: +0.0050j E_var:     2.9704 E_err:   0.019042 | Acc: 0.2749
[2025-11-12 08:07:04] 07:20:37<06:19:34, 15.93s/it | [Iter 1660/3090] R0[1569/3000] | LR: 0.013917 | E: -63.663842 | E_img: -0.0023j E_var:     2.7943 E_err:   0.018469 | Acc: 0.2827
[2025-11-12 08:07:20] 07:20:53<06:19:18, 15.93s/it | [Iter 1661/3090] R0[1570/3000] | LR: 0.013901 | E: -63.652517 | E_img: -0.0078j E_var:     2.8814 E_err:   0.018755 | Acc: 0.2825
[2025-11-12 08:07:36] 07:21:09<06:19:02, 15.93s/it | [Iter 1662/3090] R0[1571/3000] | LR: 0.013886 | E: -63.579563 | E_img: -0.0063j E_var:     2.8661 E_err:   0.018705 | Acc: 0.2871
[2025-11-12 08:07:52] 07:21:25<06:18:46, 15.93s/it | [Iter 1663/3090] R0[1572/3000] | LR: 0.013870 | E: -63.627347 | E_img: -0.0151j E_var:     3.0386 E_err:   0.019259 | Acc: 0.2798
[2025-11-12 08:08:07] 07:21:40<06:18:30, 15.93s/it | [Iter 1664/3090] R0[1573/3000] | LR: 0.013854 | E: -63.683541 | E_img: -0.0037j E_var:     3.2849 E_err:   0.020025 | Acc: 0.2643
[2025-11-12 08:08:23] 07:21:56<06:18:14, 15.93s/it | [Iter 1665/3090] R0[1574/3000] | LR: 0.013839 | E: -63.683779 | E_img: +0.0120j E_var:     3.0882 E_err:   0.019416 | Acc: 0.2606
[2025-11-12 08:08:39] 07:22:12<06:17:58, 15.93s/it | [Iter 1666/3090] R0[1575/3000] | LR: 0.013823 | E: -63.678453 | E_img: -0.0049j E_var:     3.0057 E_err:   0.019155 | Acc: 0.2629
[2025-11-12 08:08:55] 07:22:28<06:17:42, 15.93s/it | [Iter 1667/3090] R0[1576/3000] | LR: 0.013808 | E: -63.642620 | E_img: +0.0091j E_var:     2.9257 E_err:   0.018898 | Acc: 0.2744
[2025-11-12 08:09:11] 07:22:44<06:17:26, 15.93s/it | [Iter 1668/3090] R0[1577/3000] | LR: 0.013792 | E: -63.685575 | E_img: -0.0030j E_var:     2.8415 E_err:   0.018624 | Acc: 0.2782
[2025-11-12 08:09:27] 07:23:00<06:17:10, 15.93s/it | [Iter 1669/3090] R0[1578/3000] | LR: 0.013776 | E: -63.671042 | E_img: -0.0063j E_var:     2.8275 E_err:   0.018578 | Acc: 0.2794
[2025-11-12 08:09:42] 07:23:15<06:16:54, 15.93s/it | [Iter 1670/3090] R0[1579/3000] | LR: 0.013761 | E: -63.691776 | E_img: +0.0008j E_var:     3.0397 E_err:   0.019263 | Acc: 0.2705
[2025-11-12 08:09:58] 07:23:31<06:16:38, 15.93s/it | [Iter 1671/3090] R0[1580/3000] | LR: 0.013745 | E: -63.684752 | E_img: -0.0007j E_var:     2.9567 E_err:   0.018998 | Acc: 0.2699
[2025-11-12 08:10:14] 07:23:47<06:16:22, 15.93s/it | [Iter 1672/3090] R0[1581/3000] | LR: 0.013729 | E: -63.680571 | E_img: -0.0092j E_var:     3.0470 E_err:   0.019286 | Acc: 0.2609
[2025-11-12 08:10:30] 07:24:03<06:16:06, 15.93s/it | [Iter 1673/3090] R0[1582/3000] | LR: 0.013714 | E: -63.694019 | E_img: -0.0065j E_var:     2.8780 E_err:   0.018744 | Acc: 0.2661
[2025-11-12 08:10:46] 07:24:19<06:15:50, 15.93s/it | [Iter 1674/3090] R0[1583/3000] | LR: 0.013698 | E: -63.720649 | E_img: -0.0002j E_var:     2.9987 E_err:   0.019132 | Acc: 0.2696
[2025-11-12 08:11:02] 07:24:35<06:15:34, 15.93s/it | [Iter 1675/3090] R0[1584/3000] | LR: 0.013682 | E: -63.671635 | E_img: -0.0051j E_var:     2.9403 E_err:   0.018945 | Acc: 0.2684
[2025-11-12 08:11:17] 07:24:51<06:15:18, 15.93s/it | [Iter 1676/3090] R0[1585/3000] | LR: 0.013667 | E: -63.694253 | E_img: -0.0006j E_var:     2.8405 E_err:   0.018621 | Acc: 0.2700
[2025-11-12 08:11:33] 07:25:06<06:15:02, 15.93s/it | [Iter 1677/3090] R0[1586/3000] | LR: 0.013651 | E: -63.659510 | E_img: -0.0032j E_var:     2.8736 E_err:   0.018729 | Acc: 0.2766
[2025-11-12 08:11:49] 07:25:22<06:14:46, 15.93s/it | [Iter 1678/3090] R0[1587/3000] | LR: 0.013635 | E: -63.629557 | E_img: +0.0007j E_var:     2.8948 E_err:   0.018798 | Acc: 0.2938
[2025-11-12 08:12:05] 07:25:38<06:14:30, 15.93s/it | [Iter 1679/3090] R0[1588/3000] | LR: 0.013620 | E: -63.671939 | E_img: -0.0054j E_var:     3.1850 E_err:   0.019718 | Acc: 0.2866
[2025-11-12 08:12:21] 07:25:54<06:14:14, 15.92s/it | [Iter 1680/3090] R0[1589/3000] | LR: 0.013604 | E: -63.681191 | E_img: -0.0045j E_var:     3.0227 E_err:   0.019209 | Acc: 0.2737
[2025-11-12 08:12:37] 07:26:10<06:13:58, 15.92s/it | [Iter 1681/3090] R0[1590/3000] | LR: 0.013588 | E: -63.662380 | E_img: +0.0035j E_var:     2.8710 E_err:   0.018721 | Acc: 0.2758
[2025-11-12 08:12:53] 07:26:26<06:13:42, 15.92s/it | [Iter 1682/3090] R0[1591/3000] | LR: 0.013573 | E: -63.633978 | E_img: -0.0056j E_var:     2.9948 E_err:   0.019120 | Acc: 0.2698
[2025-11-12 08:13:08] 07:26:42<06:13:26, 15.92s/it | [Iter 1683/3090] R0[1592/3000] | LR: 0.013557 | E: -63.670482 | E_img: -0.0128j E_var:     3.1194 E_err:   0.019514 | Acc: 0.2656
[2025-11-12 08:13:24] 07:26:57<06:13:10, 15.92s/it | [Iter 1684/3090] R0[1593/3000] | LR: 0.013542 | E: -63.722673 | E_img: -0.0011j E_var:     2.9670 E_err:   0.019031 | Acc: 0.2656
[2025-11-12 08:13:40] 07:27:13<06:12:54, 15.92s/it | [Iter 1685/3090] R0[1594/3000] | LR: 0.013526 | E: -63.666030 | E_img: +0.0061j E_var:     2.9294 E_err:   0.018910 | Acc: 0.2640
[2025-11-12 08:13:56] 07:27:29<06:12:38, 15.92s/it | [Iter 1686/3090] R0[1595/3000] | LR: 0.013510 | E: -63.684774 | E_img: -0.0087j E_var:     3.1339 E_err:   0.019559 | Acc: 0.2585
[2025-11-12 08:14:12] 07:27:45<06:12:22, 15.92s/it | [Iter 1687/3090] R0[1596/3000] | LR: 0.013495 | E: -63.655584 | E_img: +0.0187j E_var:     3.2694 E_err:   0.019977 | Acc: 0.2564
[2025-11-12 08:14:28] 07:28:01<06:12:06, 15.92s/it | [Iter 1688/3090] R0[1597/3000] | LR: 0.013479 | E: -63.637321 | E_img: -0.0050j E_var:     3.4703 E_err:   0.020582 | Acc: 0.2480
[2025-11-12 08:14:43] 07:28:17<06:11:50, 15.92s/it | [Iter 1689/3090] R0[1598/3000] | LR: 0.013463 | E: -63.629922 | E_img: -0.0173j E_var:     3.7591 E_err:   0.021421 | Acc: 0.2403
[2025-11-12 08:14:59] 07:28:32<06:11:34, 15.92s/it | [Iter 1690/3090] R0[1599/3000] | LR: 0.013448 | E: -63.691312 | E_img: +0.0213j E_var:     3.0455 E_err:   0.019281 | Acc: 0.2464
[2025-11-12 08:15:15] 07:28:48<06:11:18, 15.92s/it | [Iter 1691/3090] R0[1600/3000] | LR: 0.013432 | E: -63.709596 | E_img: +0.0035j E_var:     2.9838 E_err:   0.019085 | Acc: 0.2545
[2025-11-12 08:15:31] 07:29:04<06:11:02, 15.92s/it | [Iter 1692/3090] R0[1601/3000] | LR: 0.013417 | E: -63.717170 | E_img: -0.0098j E_var:     3.0608 E_err:   0.019329 | Acc: 0.2602
[2025-11-12 08:15:47] 07:29:20<06:10:46, 15.92s/it | [Iter 1693/3090] R0[1602/3000] | LR: 0.013401 | E: -63.705668 | E_img: +0.0098j E_var:     2.9912 E_err:   0.019109 | Acc: 0.2656
[2025-11-12 08:16:03] 07:29:36<06:10:30, 15.92s/it | [Iter 1694/3090] R0[1603/3000] | LR: 0.013385 | E: -63.711972 | E_img: +0.0071j E_var:     2.9457 E_err:   0.018963 | Acc: 0.2638
[2025-11-12 08:16:19] 07:29:52<06:10:14, 15.92s/it | [Iter 1695/3090] R0[1604/3000] | LR: 0.013370 | E: -63.676589 | E_img: -0.0067j E_var:     3.1172 E_err:   0.019507 | Acc: 0.2640
[2025-11-12 08:16:34] 07:30:08<06:09:58, 15.92s/it | [Iter 1696/3090] R0[1605/3000] | LR: 0.013354 | E: -63.713451 | E_img: +0.0036j E_var:     3.2618 E_err:   0.019954 | Acc: 0.2559
[2025-11-12 08:16:50] 07:30:23<06:09:42, 15.92s/it | [Iter 1697/3090] R0[1606/3000] | LR: 0.013338 | E: -63.729002 | E_img: +0.0102j E_var:     2.9206 E_err:   0.018882 | Acc: 0.2565
[2025-11-12 08:17:06] 07:30:39<06:09:26, 15.92s/it | [Iter 1698/3090] R0[1607/3000] | LR: 0.013323 | E: -63.716257 | E_img: +0.0001j E_var:     2.9882 E_err:   0.019099 | Acc: 0.2633
[2025-11-12 08:17:22] 07:30:55<06:09:10, 15.92s/it | [Iter 1699/3090] R0[1608/3000] | LR: 0.013307 | E: -63.739367 | E_img: -0.0094j E_var:     2.7767 E_err:   0.018411 | Acc: 0.2668
[2025-11-12 08:17:38] 07:31:11<06:08:54, 15.92s/it | [Iter 1700/3090] R0[1609/3000] | LR: 0.013292 | E: -63.734019 | E_img: -0.0079j E_var:     2.8019 E_err:   0.018494 | Acc: 0.2718
[2025-11-12 08:17:54] 07:31:27<06:08:38, 15.92s/it | [Iter 1701/3090] R0[1610/3000] | LR: 0.013276 | E: -63.698171 | E_img: -0.0102j E_var:     2.8189 E_err:   0.018550 | Acc: 0.2812
[2025-11-12 08:18:09] 07:31:43<06:08:22, 15.92s/it | [Iter 1702/3090] R0[1611/3000] | LR: 0.013260 | E: -63.706968 | E_img: -0.0051j E_var:     2.8298 E_err:   0.018586 | Acc: 0.2789
[2025-11-12 08:18:25] 07:31:58<06:08:06, 15.92s/it | [Iter 1703/3090] R0[1612/3000] | LR: 0.013245 | E: -63.737838 | E_img: -0.0032j E_var:     3.0411 E_err:   0.019267 | Acc: 0.2693
[2025-11-12 08:18:41] 07:32:14<06:07:50, 15.92s/it | [Iter 1704/3090] R0[1613/3000] | LR: 0.013229 | E: -63.751133 | E_img: -0.0007j E_var:     2.8781 E_err:   0.018744 | Acc: 0.2705
[2025-11-12 08:18:57] 07:32:30<06:07:34, 15.92s/it | [Iter 1705/3090] R0[1614/3000] | LR: 0.013214 | E: -63.731908 | E_img: +0.0006j E_var:     3.0620 E_err:   0.019333 | Acc: 0.2644
[2025-11-12 08:19:13] 07:32:46<06:07:18, 15.92s/it | [Iter 1706/3090] R0[1615/3000] | LR: 0.013198 | E: -63.715943 | E_img: -0.0022j E_var:     3.0374 E_err:   0.019256 | Acc: 0.2629
[2025-11-12 08:19:29] 07:33:02<06:07:02, 15.92s/it | [Iter 1707/3090] R0[1616/3000] | LR: 0.013182 | E: -63.755722 | E_img: -0.0014j E_var:     2.8745 E_err:   0.018732 | Acc: 0.2625
[2025-11-12 08:19:44] 07:33:18<06:06:47, 15.92s/it | [Iter 1708/3090] R0[1617/3000] | LR: 0.013167 | E: -63.736561 | E_img: +0.0155j E_var:     3.0288 E_err:   0.019228 | Acc: 0.2612
[2025-11-12 08:20:00] 07:33:33<06:06:31, 15.92s/it | [Iter 1709/3090] R0[1618/3000] | LR: 0.013151 | E: -63.735417 | E_img: +0.0056j E_var:     2.9298 E_err:   0.018911 | Acc: 0.2649
[2025-11-12 08:20:16] 07:33:49<06:06:15, 15.92s/it | [Iter 1710/3090] R0[1619/3000] | LR: 0.013136 | E: -63.700874 | E_img: +0.0002j E_var:     2.8035 E_err:   0.018499 | Acc: 0.2764
[2025-11-12 08:20:32] 07:34:05<06:05:59, 15.92s/it | [Iter 1711/3090] R0[1620/3000] | LR: 0.013120 | E: -63.726898 | E_img: -0.0130j E_var:     2.8464 E_err:   0.018640 | Acc: 0.2776
[2025-11-12 08:20:48] 07:34:21<06:05:43, 15.92s/it | [Iter 1712/3090] R0[1621/3000] | LR: 0.013104 | E: -63.735294 | E_img: +0.0094j E_var:     2.7839 E_err:   0.018434 | Acc: 0.2796
[2025-11-12 08:21:04] 07:34:37<06:05:27, 15.92s/it | [Iter 1713/3090] R0[1622/3000] | LR: 0.013089 | E: -63.679376 | E_img: +0.0118j E_var:     2.7173 E_err:   0.018213 | Acc: 0.2935
[2025-11-12 08:21:20] 07:34:53<06:05:11, 15.92s/it | [Iter 1714/3090] R0[1623/3000] | LR: 0.013073 | E: -63.689614 | E_img: +0.0020j E_var:     2.9399 E_err:   0.018944 | Acc: 0.2873
[2025-11-12 08:21:35] 07:35:08<06:04:55, 15.92s/it | [Iter 1715/3090] R0[1624/3000] | LR: 0.013058 | E: -63.672294 | E_img: -0.0010j E_var:     2.8498 E_err:   0.018651 | Acc: 0.2862
[2025-11-12 08:21:51] 07:35:24<06:04:39, 15.92s/it | [Iter 1716/3090] R0[1625/3000] | LR: 0.013042 | E: -63.720775 | E_img: +0.0040j E_var:     2.8441 E_err:   0.018633 | Acc: 0.2845
[2025-11-12 08:22:07] 07:35:40<06:04:23, 15.92s/it | [Iter 1717/3090] R0[1626/3000] | LR: 0.013027 | E: -63.701340 | E_img: +0.0009j E_var:     3.1538 E_err:   0.019621 | Acc: 0.2715
[2025-11-12 08:22:23] 07:35:56<06:04:07, 15.92s/it | [Iter 1718/3090] R0[1627/3000] | LR: 0.013011 | E: -63.725559 | E_img: -0.0072j E_var:     2.9748 E_err:   0.019056 | Acc: 0.2678
[2025-11-12 08:22:39] 07:36:12<06:03:51, 15.92s/it | [Iter 1719/3090] R0[1628/3000] | LR: 0.012995 | E: -63.723771 | E_img: +0.0017j E_var:     3.0897 E_err:   0.019421 | Acc: 0.2620
[2025-11-12 08:22:55] 07:36:28<06:03:35, 15.92s/it | [Iter 1720/3090] R0[1629/3000] | LR: 0.012980 | E: -63.746804 | E_img: +0.0008j E_var:     2.8111 E_err:   0.018524 | Acc: 0.2706
[2025-11-12 08:23:10] 07:36:44<06:03:19, 15.92s/it | [Iter 1721/3090] R0[1630/3000] | LR: 0.012964 | E: -63.694149 | E_img: -0.0069j E_var:     2.8792 E_err:   0.018747 | Acc: 0.2756
[2025-11-12 08:23:26] 07:36:59<06:03:03, 15.92s/it | [Iter 1722/3090] R0[1631/3000] | LR: 0.012949 | E: -63.740472 | E_img: +0.0007j E_var:     2.8528 E_err:   0.018661 | Acc: 0.2739
[2025-11-12 08:23:42] 07:37:15<06:02:47, 15.92s/it | [Iter 1723/3090] R0[1632/3000] | LR: 0.012933 | E: -63.730618 | E_img: +0.0057j E_var:     2.9020 E_err:   0.018821 | Acc: 0.2701
[2025-11-12 08:23:58] 07:37:31<06:02:31, 15.92s/it | [Iter 1724/3090] R0[1633/3000] | LR: 0.012918 | E: -63.738340 | E_img: -0.0117j E_var:     3.1032 E_err:   0.019463 | Acc: 0.2640
[2025-11-12 08:24:14] 07:37:47<06:02:15, 15.92s/it | [Iter 1725/3090] R0[1634/3000] | LR: 0.012902 | E: -63.720252 | E_img: -0.0045j E_var:     2.9506 E_err:   0.018978 | Acc: 0.2623
[2025-11-12 08:24:30] 07:38:03<06:01:59, 15.92s/it | [Iter 1726/3090] R0[1635/3000] | LR: 0.012887 | E: -63.684600 | E_img: -0.0087j E_var:     3.1553 E_err:   0.019626 | Acc: 0.2618
[2025-11-12 08:24:45] 07:38:19<06:01:43, 15.92s/it | [Iter 1727/3090] R0[1636/3000] | LR: 0.012871 | E: -63.716957 | E_img: +0.0181j E_var:     2.9304 E_err:   0.018913 | Acc: 0.2664
[2025-11-12 08:25:01] 07:38:34<06:01:27, 15.92s/it | [Iter 1728/3090] R0[1637/3000] | LR: 0.012855 | E: -63.732633 | E_img: +0.0096j E_var:     2.9125 E_err:   0.018855 | Acc: 0.2695
[2025-11-12 08:25:17] 07:38:50<06:01:11, 15.92s/it | [Iter 1729/3090] R0[1638/3000] | LR: 0.012840 | E: -63.716684 | E_img: -0.0078j E_var:     2.7732 E_err:   0.018399 | Acc: 0.2797
[2025-11-12 08:25:33] 07:39:06<06:00:55, 15.92s/it | [Iter 1730/3090] R0[1639/3000] | LR: 0.012824 | E: -63.727642 | E_img: -0.0061j E_var:     2.6864 E_err:   0.018109 | Acc: 0.2893
[2025-11-12 08:25:49] 07:39:22<06:00:39, 15.92s/it | [Iter 1731/3090] R0[1640/3000] | LR: 0.012809 | E: -63.629678 | E_img: -0.0059j E_var:     2.8986 E_err:   0.018810 | Acc: 0.3013
[2025-11-12 08:26:05] 07:39:38<06:00:23, 15.92s/it | [Iter 1732/3090] R0[1641/3000] | LR: 0.012793 | E: -63.640046 | E_img: -0.0011j E_var:     2.9006 E_err:   0.018817 | Acc: 0.2975
[2025-11-12 08:26:20] 07:39:54<06:00:07, 15.92s/it | [Iter 1733/3090] R0[1642/3000] | LR: 0.012778 | E: -63.649116 | E_img: -0.0157j E_var:     2.9130 E_err:   0.018857 | Acc: 0.2933
[2025-11-12 08:26:36] 07:40:09<05:59:51, 15.92s/it | [Iter 1734/3090] R0[1643/3000] | LR: 0.012762 | E: -63.627498 | E_img: -0.0006j E_var:     2.7578 E_err:   0.018348 | Acc: 0.2954
[2025-11-12 08:26:52] 07:40:25<05:59:35, 15.92s/it | [Iter 1735/3090] R0[1644/3000] | LR: 0.012747 | E: -63.611530 | E_img: +0.0024j E_var:     2.7392 E_err:   0.018286 | Acc: 0.3000
[2025-11-12 08:27:08] 07:40:41<05:59:19, 15.92s/it | [Iter 1736/3090] R0[1645/3000] | LR: 0.012731 | E: -63.697135 | E_img: -0.0009j E_var:     2.7445 E_err:   0.018304 | Acc: 0.2940
[2025-11-12 08:27:24] 07:40:57<05:59:03, 15.92s/it | [Iter 1737/3090] R0[1646/3000] | LR: 0.012716 | E: -63.655626 | E_img: +0.0092j E_var:     2.7663 E_err:   0.018376 | Acc: 0.2901
[2025-11-12 08:27:40] 07:41:13<05:58:47, 15.92s/it | [Iter 1738/3090] R0[1647/3000] | LR: 0.012700 | E: -63.751094 | E_img: +0.0070j E_var:     2.7376 E_err:   0.018280 | Acc: 0.2862
[2025-11-12 08:27:55] 07:41:29<05:58:31, 15.92s/it | [Iter 1739/3090] R0[1648/3000] | LR: 0.012685 | E: -63.663745 | E_img: -0.0167j E_var:     2.7855 E_err:   0.018440 | Acc: 0.2822
[2025-11-12 08:28:11] 07:41:44<05:58:15, 15.92s/it | [Iter 1740/3090] R0[1649/3000] | LR: 0.012669 | E: -63.714140 | E_img: -0.0182j E_var:     2.7799 E_err:   0.018421 | Acc: 0.2821
[2025-11-12 08:28:27] 07:42:00<05:57:59, 15.92s/it | [Iter 1741/3090] R0[1650/3000] | LR: 0.012654 | E: -63.645727 | E_img: +0.0085j E_var:     2.8567 E_err:   0.018674 | Acc: 0.2833
[2025-11-12 08:28:43] 07:42:16<05:57:43, 15.92s/it | [Iter 1742/3090] R0[1651/3000] | LR: 0.012638 | E: -63.726072 | E_img: -0.0113j E_var:     2.9076 E_err:   0.018840 | Acc: 0.2785
[2025-11-12 08:28:59] 07:42:32<05:57:27, 15.92s/it | [Iter 1743/3090] R0[1652/3000] | LR: 0.012623 | E: -63.733086 | E_img: -0.0088j E_var:     2.9844 E_err:   0.019087 | Acc: 0.2698
[2025-11-12 08:29:15] 07:42:48<05:57:11, 15.92s/it | [Iter 1744/3090] R0[1653/3000] | LR: 0.012607 | E: -63.725142 | E_img: -0.0021j E_var:     2.9569 E_err:   0.018999 | Acc: 0.2649
[2025-11-12 08:29:31] 07:43:04<05:56:55, 15.92s/it | [Iter 1745/3090] R0[1654/3000] | LR: 0.012592 | E: -63.702272 | E_img: +0.0035j E_var:     2.7860 E_err:   0.018442 | Acc: 0.2795
[2025-11-12 08:29:46] 07:43:20<05:56:39, 15.92s/it | [Iter 1746/3090] R0[1655/3000] | LR: 0.012576 | E: -63.668144 | E_img: +0.0058j E_var:     3.1895 E_err:   0.019732 | Acc: 0.2679
[2025-11-12 08:30:02] 07:43:35<05:56:23, 15.92s/it | [Iter 1747/3090] R0[1656/3000] | LR: 0.012561 | E: -63.710865 | E_img: -0.0168j E_var:     2.8397 E_err:   0.018618 | Acc: 0.2732
[2025-11-12 08:30:18] 07:43:51<05:56:07, 15.92s/it | [Iter 1748/3090] R0[1657/3000] | LR: 0.012545 | E: -63.729542 | E_img: -0.0097j E_var:     2.9921 E_err:   0.019111 | Acc: 0.2683
[2025-11-12 08:30:34] 07:44:07<05:55:51, 15.92s/it | [Iter 1749/3090] R0[1658/3000] | LR: 0.012530 | E: -63.724450 | E_img: +0.0045j E_var:     3.4611 E_err:   0.020555 | Acc: 0.2524
[2025-11-12 08:30:50] 07:44:23<05:55:35, 15.92s/it | [Iter 1750/3090] R0[1659/3000] | LR: 0.012514 | E: -63.687904 | E_img: +0.0125j E_var:     3.0892 E_err:   0.019419 | Acc: 0.2602
[2025-11-12 08:31:06] 07:44:39<05:55:19, 15.92s/it | [Iter 1751/3090] R0[1660/3000] | LR: 0.012499 | E: -63.686911 | E_img: -0.0054j E_var:     3.2754 E_err:   0.019996 | Acc: 0.2553
[2025-11-12 08:31:22] 07:44:55<05:55:03, 15.92s/it | [Iter 1752/3090] R0[1661/3000] | LR: 0.012483 | E: -63.680806 | E_img: +0.0029j E_var:     2.9297 E_err:   0.018911 | Acc: 0.2612
[2025-11-12 08:31:37] 07:45:10<05:54:47, 15.92s/it | [Iter 1753/3090] R0[1662/3000] | LR: 0.012468 | E: -63.736171 | E_img: -0.0094j E_var:     2.8816 E_err:   0.018755 | Acc: 0.2697
[2025-11-12 08:31:53] 07:45:26<05:54:31, 15.92s/it | [Iter 1754/3090] R0[1663/3000] | LR: 0.012452 | E: -63.679124 | E_img: +0.0040j E_var:     3.0001 E_err:   0.019137 | Acc: 0.2668
[2025-11-12 08:32:09] 07:45:42<05:54:15, 15.92s/it | [Iter 1755/3090] R0[1664/3000] | LR: 0.012437 | E: -63.698827 | E_img: +0.0006j E_var:     3.1918 E_err:   0.019739 | Acc: 0.2575
[2025-11-12 08:32:25] 07:45:58<05:53:59, 15.92s/it | [Iter 1756/3090] R0[1665/3000] | LR: 0.012421 | E: -63.661231 | E_img: -0.0012j E_var:     2.8941 E_err:   0.018796 | Acc: 0.2678
[2025-11-12 08:32:41] 07:46:14<05:53:43, 15.92s/it | [Iter 1757/3090] R0[1666/3000] | LR: 0.012406 | E: -63.660154 | E_img: -0.0053j E_var:     3.1426 E_err:   0.019586 | Acc: 0.2664
[2025-11-12 08:32:57] 07:46:30<05:53:27, 15.92s/it | [Iter 1758/3090] R0[1667/3000] | LR: 0.012390 | E: -63.678511 | E_img: -0.0092j E_var:     2.8772 E_err:   0.018741 | Acc: 0.2682
[2025-11-12 08:33:12] 07:46:46<05:53:11, 15.92s/it | [Iter 1759/3090] R0[1668/3000] | LR: 0.012375 | E: -63.657914 | E_img: +0.0096j E_var:     2.8942 E_err:   0.018796 | Acc: 0.2748
[2025-11-12 08:33:28] 07:47:01<05:52:55, 15.92s/it | [Iter 1760/3090] R0[1669/3000] | LR: 0.012359 | E: -63.640451 | E_img: -0.0015j E_var:     2.8744 E_err:   0.018732 | Acc: 0.2840
[2025-11-12 08:33:44] 07:47:17<05:52:39, 15.92s/it | [Iter 1761/3090] R0[1670/3000] | LR: 0.012344 | E: -63.628443 | E_img: -0.0081j E_var:     2.8446 E_err:   0.018634 | Acc: 0.2842
[2025-11-12 08:34:00] 07:47:33<05:52:23, 15.92s/it | [Iter 1762/3090] R0[1671/3000] | LR: 0.012328 | E: -63.655245 | E_img: -0.0065j E_var:     2.8632 E_err:   0.018695 | Acc: 0.2897
[2025-11-12 08:34:16] 07:47:49<05:52:07, 15.92s/it | [Iter 1763/3090] R0[1672/3000] | LR: 0.012313 | E: -63.701771 | E_img: +0.0066j E_var:     2.7367 E_err:   0.018278 | Acc: 0.2913
[2025-11-12 08:34:32] 07:48:05<05:51:51, 15.92s/it | [Iter 1764/3090] R0[1673/3000] | LR: 0.012297 | E: -63.685412 | E_img: +0.0000j E_var:     2.7970 E_err:   0.018478 | Acc: 0.2917
[2025-11-12 08:34:47] 07:48:21<05:51:35, 15.92s/it | [Iter 1765/3090] R0[1674/3000] | LR: 0.012282 | E: -63.627603 | E_img: -0.0002j E_var:     2.8672 E_err:   0.018708 | Acc: 0.2977
[2025-11-12 08:35:03] 07:48:36<05:51:19, 15.92s/it | [Iter 1766/3090] R0[1675/3000] | LR: 0.012267 | E: -63.694083 | E_img: +0.0042j E_var:     2.6355 E_err:   0.017936 | Acc: 0.2972
[2025-11-12 08:35:19] 07:48:52<05:51:03, 15.92s/it | [Iter 1767/3090] R0[1676/3000] | LR: 0.012251 | E: -63.684273 | E_img: +0.0126j E_var:     2.7802 E_err:   0.018422 | Acc: 0.2929
[2025-11-12 08:35:35] 07:49:08<05:50:47, 15.92s/it | [Iter 1768/3090] R0[1677/3000] | LR: 0.012236 | E: -63.701100 | E_img: -0.0014j E_var:     2.6836 E_err:   0.018099 | Acc: 0.2898
[2025-11-12 08:35:51] 07:49:24<05:50:31, 15.92s/it | [Iter 1769/3090] R0[1678/3000] | LR: 0.012220 | E: -63.701663 | E_img: -0.0009j E_var:     2.7502 E_err:   0.018323 | Acc: 0.2899
[2025-11-12 08:36:07] 07:49:40<05:50:15, 15.92s/it | [Iter 1770/3090] R0[1679/3000] | LR: 0.012205 | E: -63.705384 | E_img: -0.0020j E_var:     2.7255 E_err:   0.018240 | Acc: 0.2854
[2025-11-12 08:36:23] 07:49:56<05:50:00, 15.92s/it | [Iter 1771/3090] R0[1680/3000] | LR: 0.012189 | E: -63.737109 | E_img: -0.0044j E_var:     3.1754 E_err:   0.019688 | Acc: 0.2721
[2025-11-12 08:36:38] 07:50:12<05:49:44, 15.92s/it | [Iter 1772/3090] R0[1681/3000] | LR: 0.012174 | E: -63.732184 | E_img: -0.0074j E_var:     2.7631 E_err:   0.018366 | Acc: 0.2757
[2025-11-12 08:36:54] 07:50:27<05:49:28, 15.92s/it | [Iter 1773/3090] R0[1682/3000] | LR: 0.012158 | E: -63.726322 | E_img: -0.0071j E_var:     2.8400 E_err:   0.018619 | Acc: 0.2740
[2025-11-12 08:37:10] 07:50:43<05:49:12, 15.92s/it | [Iter 1774/3090] R0[1683/3000] | LR: 0.012143 | E: -63.723082 | E_img: -0.0076j E_var:     2.8295 E_err:   0.018585 | Acc: 0.2832
[2025-11-12 08:37:26] 07:50:59<05:48:56, 15.92s/it | [Iter 1775/3090] R0[1684/3000] | LR: 0.012128 | E: -63.736798 | E_img: -0.0013j E_var:     2.9574 E_err:   0.019000 | Acc: 0.2788
[2025-11-12 08:37:42] 07:51:15<05:48:40, 15.92s/it | [Iter 1776/3090] R0[1685/3000] | LR: 0.012112 | E: -63.623162 | E_img: -0.0055j E_var:     2.7564 E_err:   0.018343 | Acc: 0.2924
[2025-11-12 08:37:58] 07:51:31<05:48:24, 15.92s/it | [Iter 1777/3090] R0[1686/3000] | LR: 0.012097 | E: -63.668540 | E_img: +0.0180j E_var:     2.7413 E_err:   0.018293 | Acc: 0.2925
[2025-11-12 08:38:14] 07:51:47<05:48:08, 15.92s/it | [Iter 1778/3090] R0[1687/3000] | LR: 0.012081 | E: -63.649529 | E_img: +0.0120j E_var:     2.7117 E_err:   0.018194 | Acc: 0.2984
[2025-11-12 08:38:29] 07:52:02<05:47:52, 15.92s/it | [Iter 1779/3090] R0[1688/3000] | LR: 0.012066 | E: -63.672699 | E_img: -0.0026j E_var:     2.8639 E_err:   0.018697 | Acc: 0.2950
[2025-11-12 08:38:45] 07:52:18<05:47:36, 15.92s/it | [Iter 1780/3090] R0[1689/3000] | LR: 0.012051 | E: -63.641631 | E_img: +0.0052j E_var:     2.8197 E_err:   0.018553 | Acc: 0.2908
[2025-11-12 08:39:01] 07:52:34<05:47:20, 15.92s/it | [Iter 1781/3090] R0[1690/3000] | LR: 0.012035 | E: -63.713909 | E_img: +0.0220j E_var:     2.7929 E_err:   0.018464 | Acc: 0.2845
[2025-11-12 08:39:17] 07:52:50<05:47:04, 15.92s/it | [Iter 1782/3090] R0[1691/3000] | LR: 0.012020 | E: -63.714987 | E_img: +0.0068j E_var:     2.7498 E_err:   0.018321 | Acc: 0.2785
[2025-11-12 08:39:33] 07:53:06<05:46:48, 15.92s/it | [Iter 1783/3090] R0[1692/3000] | LR: 0.012004 | E: -63.727244 | E_img: -0.0064j E_var:     2.7153 E_err:   0.018206 | Acc: 0.2777
[2025-11-12 08:39:49] 07:53:22<05:46:32, 15.92s/it | [Iter 1784/3090] R0[1693/3000] | LR: 0.011989 | E: -63.705243 | E_img: -0.0012j E_var:     2.7610 E_err:   0.018359 | Acc: 0.2805
[2025-11-12 08:40:04] 07:53:38<05:46:16, 15.92s/it | [Iter 1785/3090] R0[1694/3000] | LR: 0.011974 | E: -63.730587 | E_img: +0.0010j E_var:     2.8604 E_err:   0.018686 | Acc: 0.2747
[2025-11-12 08:40:20] 07:53:53<05:46:00, 15.92s/it | [Iter 1786/3090] R0[1695/3000] | LR: 0.011958 | E: -63.735779 | E_img: +0.0032j E_var:     2.7751 E_err:   0.018405 | Acc: 0.2793
[2025-11-12 08:40:36] 07:54:09<05:45:44, 15.92s/it | [Iter 1787/3090] R0[1696/3000] | LR: 0.011943 | E: -63.654367 | E_img: -0.0085j E_var:     2.7353 E_err:   0.018273 | Acc: 0.2948
[2025-11-12 08:40:52] 07:54:25<05:45:28, 15.92s/it | [Iter 1788/3090] R0[1697/3000] | LR: 0.011927 | E: -63.556665 | E_img: +0.0072j E_var:     2.9240 E_err:   0.018893 | Acc: 0.3083
[2025-11-12 08:41:08] 07:54:41<05:45:12, 15.92s/it | [Iter 1789/3090] R0[1698/3000] | LR: 0.011912 | E: -63.731348 | E_img: -0.0033j E_var:     2.7662 E_err:   0.018376 | Acc: 0.2876
[2025-11-12 08:41:24] 07:54:57<05:44:56, 15.92s/it | [Iter 1790/3090] R0[1699/3000] | LR: 0.011897 | E: -63.677620 | E_img: -0.0094j E_var:     2.7860 E_err:   0.018442 | Acc: 0.2843
[2025-11-12 08:41:40] 07:55:13<05:44:40, 15.92s/it | [Iter 1791/3090] R0[1700/3000] | LR: 0.011881 | E: -63.693935 | E_img: -0.0015j E_var:     2.7606 E_err:   0.018357 | Acc: 0.2797
[2025-11-12 08:41:55] 07:55:28<05:44:24, 15.92s/it | [Iter 1792/3090] R0[1701/3000] | LR: 0.011866 | E: -63.727929 | E_img: -0.0151j E_var:     2.5851 E_err:   0.017764 | Acc: 0.2873
[2025-11-12 08:42:11] 07:55:44<05:44:08, 15.92s/it | [Iter 1793/3090] R0[1702/3000] | LR: 0.011851 | E: -63.732559 | E_img: -0.0064j E_var:     2.7285 E_err:   0.018250 | Acc: 0.2837
[2025-11-12 08:42:27] 07:56:00<05:43:52, 15.92s/it | [Iter 1794/3090] R0[1703/3000] | LR: 0.011835 | E: -63.739562 | E_img: +0.0127j E_var:     2.8492 E_err:   0.018650 | Acc: 0.2769
[2025-11-12 08:42:43] 07:56:16<05:43:36, 15.92s/it | [Iter 1795/3090] R0[1704/3000] | LR: 0.011820 | E: -63.724160 | E_img: +0.0107j E_var:     2.7568 E_err:   0.018345 | Acc: 0.2802
[2025-11-12 08:42:59] 07:56:32<05:43:20, 15.92s/it | [Iter 1796/3090] R0[1705/3000] | LR: 0.011805 | E: -63.715738 | E_img: +0.0177j E_var:     3.1448 E_err:   0.019593 | Acc: 0.2658
[2025-11-12 08:43:15] 07:56:48<05:43:04, 15.92s/it | [Iter 1797/3090] R0[1706/3000] | LR: 0.011789 | E: -63.664428 | E_img: +0.0038j E_var:     3.2748 E_err:   0.019994 | Acc: 0.2541
[2025-11-12 08:43:30] 07:57:04<05:42:48, 15.92s/it | [Iter 1798/3090] R0[1707/3000] | LR: 0.011774 | E: -63.701125 | E_img: -0.0176j E_var:     2.9560 E_err:   0.018996 | Acc: 0.2573
[2025-11-12 08:43:46] 07:57:19<05:42:32, 15.92s/it | [Iter 1799/3090] R0[1708/3000] | LR: 0.011759 | E: -63.729995 | E_img: -0.0109j E_var:     3.0136 E_err:   0.019180 | Acc: 0.2610
[2025-11-12 08:44:02] 07:57:35<05:42:16, 15.92s/it | [Iter 1800/3090] R0[1709/3000] | LR: 0.011743 | E: -63.708434 | E_img: +0.0035j E_var:     2.9313 E_err:   0.018916 | Acc: 0.2625
[2025-11-12 08:44:18] 07:57:51<05:42:00, 15.92s/it | [Iter 1801/3090] R0[1710/3000] | LR: 0.011728 | E: -63.726820 | E_img: +0.0054j E_var:     3.0385 E_err:   0.019259 | Acc: 0.2663
[2025-11-12 08:44:34] 07:58:07<05:41:44, 15.92s/it | [Iter 1802/3090] R0[1711/3000] | LR: 0.011713 | E: -63.720005 | E_img: -0.0073j E_var:     2.9525 E_err:   0.018985 | Acc: 0.2637
[2025-11-12 08:44:50] 07:58:23<05:41:28, 15.92s/it | [Iter 1803/3090] R0[1712/3000] | LR: 0.011697 | E: -63.741022 | E_img: -0.0153j E_var:     2.8209 E_err:   0.018557 | Acc: 0.2668
[2025-11-12 08:45:05] 07:58:39<05:41:12, 15.92s/it | [Iter 1804/3090] R0[1713/3000] | LR: 0.011682 | E: -63.741346 | E_img: -0.0101j E_var:     3.0322 E_err:   0.019239 | Acc: 0.2671
[2025-11-12 08:45:21] 07:58:54<05:40:56, 15.92s/it | [Iter 1805/3090] R0[1714/3000] | LR: 0.011667 | E: -63.757593 | E_img: -0.0098j E_var:     3.0268 E_err:   0.019222 | Acc: 0.2691
[2025-11-12 08:45:37] 07:59:10<05:40:40, 15.92s/it | [Iter 1806/3090] R0[1715/3000] | LR: 0.011651 | E: -63.735246 | E_img: +0.0090j E_var:     3.0117 E_err:   0.019174 | Acc: 0.2631
[2025-11-12 08:45:53] 07:59:26<05:40:24, 15.92s/it | [Iter 1807/3090] R0[1716/3000] | LR: 0.011636 | E: -63.721763 | E_img: -0.0007j E_var:     2.8465 E_err:   0.018641 | Acc: 0.2676
[2025-11-12 08:46:09] 07:59:42<05:40:08, 15.92s/it | [Iter 1808/3090] R0[1717/3000] | LR: 0.011621 | E: -63.735908 | E_img: -0.0082j E_var:     2.9101 E_err:   0.018848 | Acc: 0.2620
[2025-11-12 08:46:25] 07:59:58<05:39:53, 15.92s/it | [Iter 1809/3090] R0[1718/3000] | LR: 0.011605 | E: -63.722821 | E_img: -0.0101j E_var:     2.8458 E_err:   0.018638 | Acc: 0.2594
[2025-11-12 08:46:41] 08:00:14<05:39:37, 15.92s/it | [Iter 1810/3090] R0[1719/3000] | LR: 0.011590 | E: -63.731371 | E_img: +0.0057j E_var:     3.1755 E_err:   0.019688 | Acc: 0.2545
[2025-11-12 08:46:56] 08:00:30<05:39:21, 15.92s/it | [Iter 1811/3090] R0[1720/3000] | LR: 0.011575 | E: -63.713731 | E_img: -0.0022j E_var:     3.3924 E_err:   0.020350 | Acc: 0.2438
[2025-11-12 08:47:12] 08:00:45<05:39:05, 15.92s/it | [Iter 1812/3090] R0[1721/3000] | LR: 0.011560 | E: -63.674603 | E_img: -0.0045j E_var:     3.6336 E_err:   0.021061 | Acc: 0.2334
[2025-11-12 08:47:28] 08:01:01<05:38:49, 15.92s/it | [Iter 1813/3090] R0[1722/3000] | LR: 0.011544 | E: -63.629344 | E_img: +0.0192j E_var:     3.4121 E_err:   0.020409 | Acc: 0.2294
[2025-11-12 08:47:44] 08:01:17<05:38:33, 15.92s/it | [Iter 1814/3090] R0[1723/3000] | LR: 0.011529 | E: -63.729993 | E_img: -0.0125j E_var:     2.8965 E_err:   0.018804 | Acc: 0.2450
[2025-11-12 08:48:00] 08:01:33<05:38:17, 15.92s/it | [Iter 1815/3090] R0[1724/3000] | LR: 0.011514 | E: -63.744353 | E_img: -0.0063j E_var:     3.0692 E_err:   0.019356 | Acc: 0.2478
[2025-11-12 08:48:16] 08:01:49<05:38:01, 15.92s/it | [Iter 1816/3090] R0[1725/3000] | LR: 0.011498 | E: -63.742964 | E_img: +0.0117j E_var:     2.9901 E_err:   0.019105 | Acc: 0.2542
[2025-11-12 08:48:31] 08:02:05<05:37:45, 15.92s/it | [Iter 1817/3090] R0[1726/3000] | LR: 0.011483 | E: -63.736071 | E_img: +0.0026j E_var:     3.1705 E_err:   0.019673 | Acc: 0.2505
[2025-11-12 08:48:47] 08:02:20<05:37:29, 15.92s/it | [Iter 1818/3090] R0[1727/3000] | LR: 0.011468 | E: -63.755594 | E_img: +0.0044j E_var:     2.9884 E_err:   0.019100 | Acc: 0.2542
[2025-11-12 08:49:03] 08:02:36<05:37:13, 15.92s/it | [Iter 1819/3090] R0[1728/3000] | LR: 0.011453 | E: -63.703902 | E_img: -0.0007j E_var:     3.1673 E_err:   0.019663 | Acc: 0.2504
[2025-11-12 08:49:19] 08:02:52<05:36:57, 15.92s/it | [Iter 1820/3090] R0[1729/3000] | LR: 0.011437 | E: -63.722325 | E_img: +0.0090j E_var:     3.0173 E_err:   0.019192 | Acc: 0.2545
[2025-11-12 08:49:35] 08:03:08<05:36:41, 15.92s/it | [Iter 1821/3090] R0[1730/3000] | LR: 0.011422 | E: -63.756050 | E_img: +0.0122j E_var:     2.9524 E_err:   0.018984 | Acc: 0.2693
[2025-11-12 08:49:51] 08:03:24<05:36:25, 15.92s/it | [Iter 1822/3090] R0[1731/3000] | LR: 0.011407 | E: -63.724356 | E_img: -0.0078j E_var:     2.9313 E_err:   0.018916 | Acc: 0.2738
[2025-11-12 08:50:07] 08:03:40<05:36:09, 15.92s/it | [Iter 1823/3090] R0[1732/3000] | LR: 0.011392 | E: -63.681715 | E_img: -0.0105j E_var:     2.7895 E_err:   0.018453 | Acc: 0.2855
[2025-11-12 08:50:22] 08:03:56<05:35:53, 15.92s/it | [Iter 1824/3090] R0[1733/3000] | LR: 0.011376 | E: -63.685979 | E_img: -0.0069j E_var:     2.9642 E_err:   0.019022 | Acc: 0.2848
[2025-11-12 08:50:38] 08:04:11<05:35:37, 15.92s/it | [Iter 1825/3090] R0[1734/3000] | LR: 0.011361 | E: -63.703385 | E_img: +0.0024j E_var:     2.9702 E_err:   0.019041 | Acc: 0.2754
[2025-11-12 08:50:54] 08:04:27<05:35:21, 15.92s/it | [Iter 1826/3090] R0[1735/3000] | LR: 0.011346 | E: -63.705163 | E_img: +0.0082j E_var:     2.8272 E_err:   0.018577 | Acc: 0.2759
[2025-11-12 08:51:10] 08:04:43<05:35:05, 15.92s/it | [Iter 1827/3090] R0[1736/3000] | LR: 0.011331 | E: -63.712143 | E_img: -0.0061j E_var:     3.2799 E_err:   0.020010 | Acc: 0.2663
[2025-11-12 08:51:26] 08:04:59<05:34:49, 15.92s/it | [Iter 1828/3090] R0[1737/3000] | LR: 0.011315 | E: -63.753882 | E_img: +0.0139j E_var:     2.9439 E_err:   0.018957 | Acc: 0.2588
[2025-11-12 08:51:42] 08:05:15<05:34:33, 15.92s/it | [Iter 1829/3090] R0[1738/3000] | LR: 0.011300 | E: -63.722218 | E_img: -0.0085j E_var:     3.2974 E_err:   0.020063 | Acc: 0.2500
[2025-11-12 08:51:57] 08:05:31<05:34:17, 15.92s/it | [Iter 1830/3090] R0[1739/3000] | LR: 0.011285 | E: -63.728388 | E_img: +0.0036j E_var:     3.0141 E_err:   0.019182 | Acc: 0.2493
[2025-11-12 08:52:13] 08:05:46<05:34:01, 15.92s/it | [Iter 1831/3090] R0[1740/3000] | LR: 0.011270 | E: -63.742505 | E_img: +0.0080j E_var:     2.7847 E_err:   0.018437 | Acc: 0.2557
[2025-11-12 08:52:29] 08:06:02<05:33:45, 15.92s/it | [Iter 1832/3090] R0[1741/3000] | LR: 0.011255 | E: -63.759008 | E_img: +0.0076j E_var:     2.7616 E_err:   0.018360 | Acc: 0.2650
[2025-11-12 08:52:45] 08:06:18<05:33:29, 15.92s/it | [Iter 1833/3090] R0[1742/3000] | LR: 0.011239 | E: -63.752065 | E_img: +0.0032j E_var:     2.7278 E_err:   0.018248 | Acc: 0.2722
[2025-11-12 08:53:01] 08:06:34<05:33:13, 15.92s/it | [Iter 1834/3090] R0[1743/3000] | LR: 0.011224 | E: -63.712192 | E_img: +0.0039j E_var:     2.9288 E_err:   0.018908 | Acc: 0.2711
[2025-11-12 08:53:17] 08:06:50<05:32:57, 15.92s/it | [Iter 1835/3090] R0[1744/3000] | LR: 0.011209 | E: -63.732075 | E_img: +0.0065j E_var:     2.8253 E_err:   0.018571 | Acc: 0.2680
[2025-11-12 08:53:32] 08:07:06<05:32:41, 15.92s/it | [Iter 1836/3090] R0[1745/3000] | LR: 0.011194 | E: -63.726389 | E_img: -0.0041j E_var:     3.1306 E_err:   0.019549 | Acc: 0.2593
[2025-11-12 08:53:48] 08:07:21<05:32:25, 15.92s/it | [Iter 1837/3090] R0[1746/3000] | LR: 0.011179 | E: -63.752694 | E_img: +0.0124j E_var:     2.8791 E_err:   0.018747 | Acc: 0.2597
[2025-11-12 08:54:04] 08:07:37<05:32:09, 15.92s/it | [Iter 1838/3090] R0[1747/3000] | LR: 0.011163 | E: -63.754877 | E_img: +0.0003j E_var:     2.8634 E_err:   0.018696 | Acc: 0.2599
[2025-11-12 08:54:20] 08:07:53<05:31:53, 15.92s/it | [Iter 1839/3090] R0[1748/3000] | LR: 0.011148 | E: -63.755321 | E_img: -0.0071j E_var:     3.1799 E_err:   0.019702 | Acc: 0.2554
[2025-11-12 08:54:36] 08:08:09<05:31:37, 15.92s/it | [Iter 1840/3090] R0[1749/3000] | LR: 0.011133 | E: -63.719889 | E_img: +0.0078j E_var:     2.7943 E_err:   0.018469 | Acc: 0.2620
[2025-11-12 08:54:52] 08:08:25<05:31:21, 15.92s/it | [Iter 1841/3090] R0[1750/3000] | LR: 0.011118 | E: -63.679206 | E_img: -0.0093j E_var:     3.0345 E_err:   0.019246 | Acc: 0.2625
[2025-11-12 08:55:07] 08:08:41<05:31:05, 15.92s/it | [Iter 1842/3090] R0[1751/3000] | LR: 0.011103 | E: -63.695295 | E_img: -0.0049j E_var:     3.1721 E_err:   0.019678 | Acc: 0.2578
[2025-11-12 08:55:23] 08:08:56<05:30:49, 15.92s/it | [Iter 1843/3090] R0[1752/3000] | LR: 0.011087 | E: -63.674015 | E_img: +0.0003j E_var:     3.0939 E_err:   0.019434 | Acc: 0.2539
[2025-11-12 08:55:39] 08:09:12<05:30:33, 15.92s/it | [Iter 1844/3090] R0[1753/3000] | LR: 0.011072 | E: -63.734611 | E_img: +0.0133j E_var:     2.7911 E_err:   0.018458 | Acc: 0.2587
[2025-11-12 08:55:55] 08:09:28<05:30:17, 15.92s/it | [Iter 1845/3090] R0[1754/3000] | LR: 0.011057 | E: -63.714854 | E_img: +0.0199j E_var:     2.9774 E_err:   0.019064 | Acc: 0.2627
[2025-11-12 08:56:11] 08:09:44<05:30:02, 15.92s/it | [Iter 1846/3090] R0[1755/3000] | LR: 0.011042 | E: -63.724334 | E_img: -0.0150j E_var:     2.7844 E_err:   0.018436 | Acc: 0.2668
[2025-11-12 08:56:27] 08:10:00<05:29:46, 15.92s/it | [Iter 1847/3090] R0[1756/3000] | LR: 0.011027 | E: -63.670949 | E_img: -0.0030j E_var:     2.8532 E_err:   0.018662 | Acc: 0.2724
[2025-11-12 08:56:42] 08:10:16<05:29:30, 15.92s/it | [Iter 1848/3090] R0[1757/3000] | LR: 0.011012 | E: -63.677487 | E_img: +0.0011j E_var:     2.9840 E_err:   0.019086 | Acc: 0.2816
[2025-11-12 08:56:58] 08:10:31<05:29:14, 15.92s/it | [Iter 1849/3090] R0[1758/3000] | LR: 0.010997 | E: -63.680445 | E_img: +0.0178j E_var:     2.8666 E_err:   0.018706 | Acc: 0.2870
[2025-11-12 08:57:14] 08:10:47<05:28:58, 15.92s/it | [Iter 1850/3090] R0[1759/3000] | LR: 0.010981 | E: -63.693093 | E_img: -0.0004j E_var:     2.8138 E_err:   0.018533 | Acc: 0.2843
[2025-11-12 08:57:30] 08:11:03<05:28:42, 15.92s/it | [Iter 1851/3090] R0[1760/3000] | LR: 0.010966 | E: -63.683229 | E_img: +0.0264j E_var:     2.8257 E_err:   0.018572 | Acc: 0.2850
[2025-11-12 08:57:46] 08:11:19<05:28:26, 15.92s/it | [Iter 1852/3090] R0[1761/3000] | LR: 0.010951 | E: -63.718223 | E_img: -0.0015j E_var:     2.6450 E_err:   0.017969 | Acc: 0.2818
[2025-11-12 08:58:02] 08:11:35<05:28:10, 15.92s/it | [Iter 1853/3090] R0[1762/3000] | LR: 0.010936 | E: -63.744814 | E_img: -0.0047j E_var:     2.7931 E_err:   0.018465 | Acc: 0.2761
[2025-11-12 08:58:18] 08:11:51<05:27:54, 15.92s/it | [Iter 1854/3090] R0[1763/3000] | LR: 0.010921 | E: -63.697333 | E_img: -0.0029j E_var:     2.8395 E_err:   0.018618 | Acc: 0.2795
[2025-11-12 08:58:33] 08:12:07<05:27:38, 15.92s/it | [Iter 1855/3090] R0[1764/3000] | LR: 0.010906 | E: -63.755772 | E_img: +0.0042j E_var:     2.8400 E_err:   0.018619 | Acc: 0.2750
[2025-11-12 08:58:49] 08:12:22<05:27:22, 15.92s/it | [Iter 1856/3090] R0[1765/3000] | LR: 0.010891 | E: -63.720363 | E_img: +0.0077j E_var:     2.7033 E_err:   0.018166 | Acc: 0.2837
[2025-11-12 08:59:05] 08:12:38<05:27:06, 15.92s/it | [Iter 1857/3090] R0[1766/3000] | LR: 0.010876 | E: -63.757364 | E_img: -0.0058j E_var:     2.7591 E_err:   0.018352 | Acc: 0.2817
[2025-11-12 08:59:21] 08:12:54<05:26:50, 15.92s/it | [Iter 1858/3090] R0[1767/3000] | LR: 0.010860 | E: -63.766648 | E_img: +0.0132j E_var:     2.9379 E_err:   0.018937 | Acc: 0.2689
[2025-11-12 08:59:37] 08:13:10<05:26:34, 15.92s/it | [Iter 1859/3090] R0[1768/3000] | LR: 0.010845 | E: -63.741872 | E_img: -0.0051j E_var:     2.7544 E_err:   0.018336 | Acc: 0.2752
[2025-11-12 08:59:53] 08:13:26<05:26:18, 15.92s/it | [Iter 1860/3090] R0[1769/3000] | LR: 0.010830 | E: -63.724567 | E_img: -0.0029j E_var:     2.9929 E_err:   0.019114 | Acc: 0.2682
[2025-11-12 09:00:08] 08:13:42<05:26:02, 15.92s/it | [Iter 1861/3090] R0[1770/3000] | LR: 0.010815 | E: -63.722128 | E_img: -0.0112j E_var:     3.8603 E_err:   0.021708 | Acc: 0.2524
[2025-11-12 09:00:24] 08:13:57<05:25:46, 15.92s/it | [Iter 1862/3090] R0[1771/3000] | LR: 0.010800 | E: -63.716593 | E_img: -0.0013j E_var:     3.0124 E_err:   0.019176 | Acc: 0.2485
[2025-11-12 09:00:40] 08:14:13<05:25:30, 15.92s/it | [Iter 1863/3090] R0[1772/3000] | LR: 0.010785 | E: -63.737974 | E_img: -0.0055j E_var:     3.3531 E_err:   0.020231 | Acc: 0.2448
[2025-11-12 09:00:56] 08:14:29<05:25:14, 15.92s/it | [Iter 1864/3090] R0[1773/3000] | LR: 0.010770 | E: -63.773431 | E_img: +0.0005j E_var:     2.7334 E_err:   0.018266 | Acc: 0.2654
[2025-11-12 09:01:12] 08:14:45<05:24:58, 15.92s/it | [Iter 1865/3090] R0[1774/3000] | LR: 0.010755 | E: -63.705509 | E_img: -0.0210j E_var:     2.7285 E_err:   0.018250 | Acc: 0.2756
[2025-11-12 09:01:28] 08:15:01<05:24:42, 15.92s/it | [Iter 1866/3090] R0[1775/3000] | LR: 0.010740 | E: -63.734530 | E_img: -0.0038j E_var:     2.9486 E_err:   0.018972 | Acc: 0.2757
[2025-11-12 09:01:44] 08:15:17<05:24:26, 15.92s/it | [Iter 1867/3090] R0[1776/3000] | LR: 0.010725 | E: -63.747844 | E_img: +0.0078j E_var:     2.9281 E_err:   0.018906 | Acc: 0.2729
[2025-11-12 09:01:59] 08:15:33<05:24:10, 15.92s/it | [Iter 1868/3090] R0[1777/3000] | LR: 0.010710 | E: -63.737316 | E_img: -0.0087j E_var:     3.0922 E_err:   0.019428 | Acc: 0.2674
[2025-11-12 09:02:15] 08:15:48<05:23:54, 15.92s/it | [Iter 1869/3090] R0[1778/3000] | LR: 0.010695 | E: -63.725216 | E_img: +0.0126j E_var:     3.0296 E_err:   0.019231 | Acc: 0.2648
[2025-11-12 09:02:31] 08:16:04<05:23:38, 15.92s/it | [Iter 1870/3090] R0[1779/3000] | LR: 0.010680 | E: -63.710118 | E_img: -0.0058j E_var:     2.8959 E_err:   0.018802 | Acc: 0.2759
[2025-11-12 09:02:47] 08:16:20<05:23:22, 15.92s/it | [Iter 1871/3090] R0[1780/3000] | LR: 0.010665 | E: -63.733979 | E_img: +0.0101j E_var:     2.9805 E_err:   0.019074 | Acc: 0.2747
[2025-11-12 09:03:03] 08:16:36<05:23:06, 15.92s/it | [Iter 1872/3090] R0[1781/3000] | LR: 0.010650 | E: -63.701053 | E_img: +0.0066j E_var:     2.9068 E_err:   0.018837 | Acc: 0.2789
[2025-11-12 09:03:19] 08:16:52<05:22:50, 15.92s/it | [Iter 1873/3090] R0[1782/3000] | LR: 0.010635 | E: -63.770210 | E_img: -0.0051j E_var:     3.1281 E_err:   0.019541 | Acc: 0.2674
[2025-11-12 09:03:34] 08:17:08<05:22:35, 15.92s/it | [Iter 1874/3090] R0[1783/3000] | LR: 0.010619 | E: -63.731078 | E_img: +0.0147j E_var:     2.8507 E_err:   0.018654 | Acc: 0.2641
[2025-11-12 09:03:50] 08:17:23<05:22:19, 15.92s/it | [Iter 1875/3090] R0[1784/3000] | LR: 0.010604 | E: -63.764001 | E_img: -0.0013j E_var:     2.8588 E_err:   0.018681 | Acc: 0.2647
[2025-11-12 09:04:06] 08:17:39<05:22:03, 15.92s/it | [Iter 1876/3090] R0[1785/3000] | LR: 0.010589 | E: -63.737240 | E_img: -0.0007j E_var:     2.9505 E_err:   0.018978 | Acc: 0.2634
[2025-11-12 09:04:22] 08:17:55<05:21:47, 15.92s/it | [Iter 1877/3090] R0[1786/3000] | LR: 0.010574 | E: -63.765617 | E_img: +0.0044j E_var:     2.9027 E_err:   0.018824 | Acc: 0.2663
[2025-11-12 09:04:38] 08:18:11<05:21:31, 15.92s/it | [Iter 1878/3090] R0[1787/3000] | LR: 0.010559 | E: -63.652377 | E_img: -0.0127j E_var:     2.8793 E_err:   0.018748 | Acc: 0.2731
[2025-11-12 09:04:54] 08:18:27<05:21:15, 15.92s/it | [Iter 1879/3090] R0[1788/3000] | LR: 0.010544 | E: -63.713732 | E_img: -0.0261j E_var:     3.0768 E_err:   0.019380 | Acc: 0.2672
[2025-11-12 09:05:10] 08:18:43<05:20:59, 15.92s/it | [Iter 1880/3090] R0[1789/3000] | LR: 0.010529 | E: -63.635923 | E_img: -0.0082j E_var:     3.0865 E_err:   0.019411 | Acc: 0.2646
[2025-11-12 09:05:25] 08:18:58<05:20:43, 15.92s/it | [Iter 1881/3090] R0[1790/3000] | LR: 0.010514 | E: -63.726623 | E_img: +0.0031j E_var:     2.7849 E_err:   0.018438 | Acc: 0.2715
[2025-11-12 09:05:41] 08:19:14<05:20:27, 15.92s/it | [Iter 1882/3090] R0[1791/3000] | LR: 0.010499 | E: -63.739937 | E_img: -0.0057j E_var:     2.9240 E_err:   0.018893 | Acc: 0.2739
[2025-11-12 09:05:57] 08:19:30<05:20:11, 15.92s/it | [Iter 1883/3090] R0[1792/3000] | LR: 0.010484 | E: -63.674293 | E_img: +0.0094j E_var:     2.8828 E_err:   0.018759 | Acc: 0.2804
[2025-11-12 09:06:13] 08:19:46<05:19:55, 15.92s/it | [Iter 1884/3090] R0[1793/3000] | LR: 0.010470 | E: -63.716630 | E_img: -0.0085j E_var:     2.9165 E_err:   0.018868 | Acc: 0.2794
[2025-11-12 09:06:29] 08:20:02<05:19:39, 15.92s/it | [Iter 1885/3090] R0[1794/3000] | LR: 0.010455 | E: -63.670025 | E_img: -0.0029j E_var:     3.0262 E_err:   0.019220 | Acc: 0.2784
[2025-11-12 09:06:45] 08:20:18<05:19:23, 15.92s/it | [Iter 1886/3090] R0[1795/3000] | LR: 0.010440 | E: -63.656487 | E_img: -0.0127j E_var:     2.9176 E_err:   0.018872 | Acc: 0.2839
[2025-11-12 09:07:00] 08:20:34<05:19:07, 15.92s/it | [Iter 1887/3090] R0[1796/3000] | LR: 0.010425 | E: -63.628841 | E_img: -0.0021j E_var:     2.8902 E_err:   0.018783 | Acc: 0.2828
[2025-11-12 09:07:16] 08:20:49<05:18:51, 15.92s/it | [Iter 1888/3090] R0[1797/3000] | LR: 0.010410 | E: -63.524191 | E_img: -0.0101j E_var:     3.0751 E_err:   0.019375 | Acc: 0.2880
[2025-11-12 09:07:32] 08:21:05<05:18:35, 15.92s/it | [Iter 1889/3090] R0[1798/3000] | LR: 0.010395 | E: -63.635547 | E_img: +0.0071j E_var:     2.8797 E_err:   0.018749 | Acc: 0.2854
[2025-11-12 09:07:48] 08:21:21<05:18:19, 15.92s/it | [Iter 1890/3090] R0[1799/3000] | LR: 0.010380 | E: -63.562226 | E_img: +0.0125j E_var:     2.9335 E_err:   0.018924 | Acc: 0.2850
[2025-11-12 09:07:48] ✓ Checkpoint saved: checkpoint_iter_001800.pkl
[2025-11-12 09:08:04] 08:21:37<05:18:03, 15.92s/it | [Iter 1891/3090] R0[1800/3000] | LR: 0.010365 | E: -63.621132 | E_img: -0.0141j E_var:     2.9390 E_err:   0.018941 | Acc: 0.2854
[2025-11-12 09:08:20] 08:21:53<05:17:47, 15.92s/it | [Iter 1892/3090] R0[1801/3000] | LR: 0.010350 | E: -63.427744 | E_img: +0.0259j E_var:     3.0792 E_err:   0.019388 | Acc: 0.2951
[2025-11-12 09:08:36] 08:22:09<05:17:31, 15.92s/it | [Iter 1893/3090] R0[1802/3000] | LR: 0.010335 | E: -63.439631 | E_img: +0.0160j E_var:     3.0673 E_err:   0.019350 | Acc: 0.2925
[2025-11-12 09:08:51] 08:22:25<05:17:15, 15.92s/it | [Iter 1894/3090] R0[1803/3000] | LR: 0.010320 | E: -63.497491 | E_img: -0.0087j E_var:     3.0548 E_err:   0.019311 | Acc: 0.2830
[2025-11-12 09:09:07] 08:22:40<05:16:59, 15.92s/it | [Iter 1895/3090] R0[1804/3000] | LR: 0.010305 | E: -63.502901 | E_img: -0.0090j E_var:     3.0814 E_err:   0.019395 | Acc: 0.2785
[2025-11-12 09:09:23] 08:22:56<05:16:43, 15.92s/it | [Iter 1896/3090] R0[1805/3000] | LR: 0.010290 | E: -63.523037 | E_img: +0.0287j E_var:     3.0009 E_err:   0.019140 | Acc: 0.2754
[2025-11-12 09:09:39] 08:23:12<05:16:27, 15.92s/it | [Iter 1897/3090] R0[1806/3000] | LR: 0.010275 | E: -63.456016 | E_img: -0.0131j E_var:     2.9570 E_err:   0.018999 | Acc: 0.2847
[2025-11-12 09:09:55] 08:23:28<05:16:11, 15.92s/it | [Iter 1898/3090] R0[1807/3000] | LR: 0.010260 | E: -63.471941 | E_img: +0.0155j E_var:     2.9266 E_err:   0.018901 | Acc: 0.2982
[2025-11-12 09:10:11] 08:23:44<05:15:56, 15.92s/it | [Iter 1899/3090] R0[1808/3000] | LR: 0.010245 | E: -63.459990 | E_img: -0.0258j E_var:     2.9359 E_err:   0.018931 | Acc: 0.3039
[2025-11-12 09:10:26] 08:24:00<05:15:40, 15.92s/it | [Iter 1900/3090] R0[1809/3000] | LR: 0.010231 | E: -63.537413 | E_img: +0.0056j E_var:     2.8669 E_err:   0.018707 | Acc: 0.2998
[2025-11-12 09:10:42] 08:24:15<05:15:24, 15.92s/it | [Iter 1901/3090] R0[1810/3000] | LR: 0.010216 | E: -63.481324 | E_img: -0.0052j E_var:     2.8478 E_err:   0.018645 | Acc: 0.2992
[2025-11-12 09:10:58] 08:24:31<05:15:08, 15.92s/it | [Iter 1902/3090] R0[1811/3000] | LR: 0.010201 | E: -63.516641 | E_img: +0.0012j E_var:     2.8252 E_err:   0.018571 | Acc: 0.2950
[2025-11-12 09:11:14] 08:24:47<05:14:52, 15.92s/it | [Iter 1903/3090] R0[1812/3000] | LR: 0.010186 | E: -63.529519 | E_img: +0.0139j E_var:     2.9070 E_err:   0.018838 | Acc: 0.2880
[2025-11-12 09:11:30] 08:25:03<05:14:36, 15.92s/it | [Iter 1904/3090] R0[1813/3000] | LR: 0.010171 | E: -63.558989 | E_img: +0.0139j E_var:     3.0630 E_err:   0.019336 | Acc: 0.2745
[2025-11-12 09:11:46] 08:25:19<05:14:20, 15.92s/it | [Iter 1905/3090] R0[1814/3000] | LR: 0.010156 | E: -63.599160 | E_img: +0.0042j E_var:     2.8278 E_err:   0.018579 | Acc: 0.2730
[2025-11-12 09:12:02] 08:25:35<05:14:04, 15.92s/it | [Iter 1906/3090] R0[1815/3000] | LR: 0.010141 | E: -63.591169 | E_img: +0.0097j E_var:     2.9735 E_err:   0.019052 | Acc: 0.2707
[2025-11-12 09:12:17] 08:25:51<05:13:48, 15.92s/it | [Iter 1907/3090] R0[1816/3000] | LR: 0.010126 | E: -63.647311 | E_img: +0.0156j E_var:     2.7856 E_err:   0.018440 | Acc: 0.2813
[2025-11-12 09:12:33] 08:26:06<05:13:32, 15.92s/it | [Iter 1908/3090] R0[1817/3000] | LR: 0.010112 | E: -63.653916 | E_img: +0.0087j E_var:     2.7466 E_err:   0.018311 | Acc: 0.2873
[2025-11-12 09:12:49] 08:26:22<05:13:16, 15.92s/it | [Iter 1909/3090] R0[1818/3000] | LR: 0.010097 | E: -63.692667 | E_img: -0.0074j E_var:     2.7676 E_err:   0.018380 | Acc: 0.2853
[2025-11-12 09:13:05] 08:26:38<05:13:00, 15.92s/it | [Iter 1910/3090] R0[1819/3000] | LR: 0.010082 | E: -63.687914 | E_img: +0.0037j E_var:     2.8471 E_err:   0.018643 | Acc: 0.2757
[2025-11-12 09:13:21] 08:26:54<05:12:44, 15.92s/it | [Iter 1911/3090] R0[1820/3000] | LR: 0.010067 | E: -63.698556 | E_img: +0.0016j E_var:     2.8312 E_err:   0.018591 | Acc: 0.2744
[2025-11-12 09:13:37] 08:27:10<05:12:28, 15.92s/it | [Iter 1912/3090] R0[1821/3000] | LR: 0.010052 | E: -63.675909 | E_img: +0.0004j E_var:     2.7757 E_err:   0.018407 | Acc: 0.2776
[2025-11-12 09:13:53] 08:27:26<05:12:12, 15.92s/it | [Iter 1913/3090] R0[1822/3000] | LR: 0.010037 | E: -63.686236 | E_img: +0.0072j E_var:     2.7720 E_err:   0.018395 | Acc: 0.2806
[2025-11-12 09:14:08] 08:27:41<05:11:56, 15.92s/it | [Iter 1914/3090] R0[1823/3000] | LR: 0.010023 | E: -63.685910 | E_img: +0.0115j E_var:     2.6906 E_err:   0.018123 | Acc: 0.2866
[2025-11-12 09:14:24] 08:27:57<05:11:40, 15.92s/it | [Iter 1915/3090] R0[1824/3000] | LR: 0.010008 | E: -63.623258 | E_img: +0.0026j E_var:     2.7628 E_err:   0.018365 | Acc: 0.2972
[2025-11-12 09:14:40] 08:28:13<05:11:24, 15.92s/it | [Iter 1916/3090] R0[1825/3000] | LR: 0.009993 | E: -63.652352 | E_img: -0.0051j E_var:     2.6933 E_err:   0.018132 | Acc: 0.2989
[2025-11-12 09:14:56] 08:28:29<05:11:08, 15.91s/it | [Iter 1917/3090] R0[1826/3000] | LR: 0.009978 | E: -63.648288 | E_img: -0.0155j E_var:     2.8956 E_err:   0.018801 | Acc: 0.2921
[2025-11-12 09:15:12] 08:28:45<05:10:52, 15.91s/it | [Iter 1918/3090] R0[1827/3000] | LR: 0.009963 | E: -63.591282 | E_img: +0.0004j E_var:     2.7827 E_err:   0.018431 | Acc: 0.2942
[2025-11-12 09:15:28] 08:29:01<05:10:36, 15.91s/it | [Iter 1919/3090] R0[1828/3000] | LR: 0.009949 | E: -63.626575 | E_img: +0.0016j E_var:     2.7061 E_err:   0.018175 | Acc: 0.2952
[2025-11-12 09:15:43] 08:29:17<05:10:20, 15.91s/it | [Iter 1920/3090] R0[1829/3000] | LR: 0.009934 | E: -63.655072 | E_img: -0.0032j E_var:     2.7274 E_err:   0.018246 | Acc: 0.2929
[2025-11-12 09:15:59] 08:29:32<05:10:04, 15.91s/it | [Iter 1921/3090] R0[1830/3000] | LR: 0.009919 | E: -63.578904 | E_img: -0.0141j E_var:     2.8098 E_err:   0.018520 | Acc: 0.2949
[2025-11-12 09:16:15] 08:29:48<05:09:48, 15.91s/it | [Iter 1922/3090] R0[1831/3000] | LR: 0.009904 | E: -63.511041 | E_img: -0.0086j E_var:     2.9213 E_err:   0.018884 | Acc: 0.2991
[2025-11-12 09:16:31] 08:30:04<05:09:32, 15.91s/it | [Iter 1923/3090] R0[1832/3000] | LR: 0.009889 | E: -63.683953 | E_img: -0.0128j E_var:     2.9360 E_err:   0.018931 | Acc: 0.2818
[2025-11-12 09:16:47] 08:30:20<05:09:17, 15.91s/it | [Iter 1924/3090] R0[1833/3000] | LR: 0.009875 | E: -63.688328 | E_img: -0.0243j E_var:     2.7219 E_err:   0.018228 | Acc: 0.2815
[2025-11-12 09:17:03] 08:30:36<05:09:01, 15.91s/it | [Iter 1925/3090] R0[1834/3000] | LR: 0.009860 | E: -63.666191 | E_img: -0.0067j E_var:     2.7469 E_err:   0.018311 | Acc: 0.2893
[2025-11-12 09:17:18] 08:30:52<05:08:45, 15.91s/it | [Iter 1926/3090] R0[1835/3000] | LR: 0.009845 | E: -63.651621 | E_img: +0.0233j E_var:     2.6739 E_err:   0.018067 | Acc: 0.2903
[2025-11-12 09:17:34] 08:31:07<05:08:29, 15.91s/it | [Iter 1927/3090] R0[1836/3000] | LR: 0.009830 | E: -63.687662 | E_img: +0.0108j E_var:     2.8056 E_err:   0.018506 | Acc: 0.2845
[2025-11-12 09:17:50] 08:31:23<05:08:13, 15.91s/it | [Iter 1928/3090] R0[1837/3000] | LR: 0.009816 | E: -63.720958 | E_img: +0.0105j E_var:     2.5567 E_err:   0.017666 | Acc: 0.2884
[2025-11-12 09:18:06] 08:31:39<05:07:57, 15.91s/it | [Iter 1929/3090] R0[1838/3000] | LR: 0.009801 | E: -63.747067 | E_img: +0.0043j E_var:     2.7660 E_err:   0.018375 | Acc: 0.2883
[2025-11-12 09:18:22] 08:31:55<05:07:41, 15.91s/it | [Iter 1930/3090] R0[1839/3000] | LR: 0.009786 | E: -63.648453 | E_img: +0.0004j E_var:     2.6718 E_err:   0.018059 | Acc: 0.2949
[2025-11-12 09:18:38] 08:32:11<05:07:25, 15.91s/it | [Iter 1931/3090] R0[1840/3000] | LR: 0.009771 | E: -63.701967 | E_img: +0.0058j E_var:     2.7201 E_err:   0.018222 | Acc: 0.2896
[2025-11-12 09:18:53] 08:32:27<05:07:09, 15.91s/it | [Iter 1932/3090] R0[1841/3000] | LR: 0.009757 | E: -63.753767 | E_img: +0.0051j E_var:     2.9145 E_err:   0.018862 | Acc: 0.2766
[2025-11-12 09:19:09] 08:32:42<05:06:53, 15.91s/it | [Iter 1933/3090] R0[1842/3000] | LR: 0.009742 | E: -63.774277 | E_img: +0.0101j E_var:     2.6992 E_err:   0.018152 | Acc: 0.2752
[2025-11-12 09:19:25] 08:32:58<05:06:37, 15.91s/it | [Iter 1934/3090] R0[1843/3000] | LR: 0.009727 | E: -63.780878 | E_img: -0.0126j E_var:     2.6926 E_err:   0.018130 | Acc: 0.2796
[2025-11-12 09:19:41] 08:33:14<05:06:21, 15.91s/it | [Iter 1935/3090] R0[1844/3000] | LR: 0.009713 | E: -63.771992 | E_img: -0.0010j E_var:     2.7136 E_err:   0.018200 | Acc: 0.2799
[2025-11-12 09:19:57] 08:33:30<05:06:05, 15.91s/it | [Iter 1936/3090] R0[1845/3000] | LR: 0.009698 | E: -63.657628 | E_img: -0.0074j E_var:     2.7436 E_err:   0.018301 | Acc: 0.2889
[2025-11-12 09:20:13] 08:33:46<05:05:49, 15.91s/it | [Iter 1937/3090] R0[1846/3000] | LR: 0.009683 | E: -63.683060 | E_img: -0.0158j E_var:     2.7898 E_err:   0.018454 | Acc: 0.2885
[2025-11-12 09:20:28] 08:34:02<05:05:33, 15.91s/it | [Iter 1938/3090] R0[1847/3000] | LR: 0.009669 | E: -63.727611 | E_img: -0.0081j E_var:     2.8446 E_err:   0.018634 | Acc: 0.2848
[2025-11-12 09:20:44] 08:34:17<05:05:17, 15.91s/it | [Iter 1939/3090] R0[1848/3000] | LR: 0.009654 | E: -63.754452 | E_img: +0.0137j E_var:     2.8434 E_err:   0.018630 | Acc: 0.2737
[2025-11-12 09:21:00] 08:34:33<05:05:01, 15.91s/it | [Iter 1940/3090] R0[1849/3000] | LR: 0.009639 | E: -63.783128 | E_img: -0.0099j E_var:     2.8542 E_err:   0.018666 | Acc: 0.2684
[2025-11-12 09:21:16] 08:34:49<05:04:45, 15.91s/it | [Iter 1941/3090] R0[1850/3000] | LR: 0.009625 | E: -63.769482 | E_img: +0.0039j E_var:     2.8933 E_err:   0.018793 | Acc: 0.2640
[2025-11-12 09:21:32] 08:35:05<05:04:29, 15.91s/it | [Iter 1942/3090] R0[1851/3000] | LR: 0.009610 | E: -63.766862 | E_img: +0.0075j E_var:     2.9092 E_err:   0.018845 | Acc: 0.2665
[2025-11-12 09:21:48] 08:35:21<05:04:13, 15.91s/it | [Iter 1943/3090] R0[1852/3000] | LR: 0.009595 | E: -63.751967 | E_img: +0.0046j E_var:     2.8365 E_err:   0.018608 | Acc: 0.2751
[2025-11-12 09:22:03] 08:35:37<05:03:57, 15.91s/it | [Iter 1944/3090] R0[1853/3000] | LR: 0.009581 | E: -63.739826 | E_img: -0.0052j E_var:     3.1771 E_err:   0.019693 | Acc: 0.2622
[2025-11-12 09:22:19] 08:35:52<05:03:41, 15.91s/it | [Iter 1945/3090] R0[1854/3000] | LR: 0.009566 | E: -63.759419 | E_img: -0.0036j E_var:     2.9582 E_err:   0.019003 | Acc: 0.2631
[2025-11-12 09:22:35] 08:36:08<05:03:25, 15.91s/it | [Iter 1946/3090] R0[1855/3000] | LR: 0.009551 | E: -63.733184 | E_img: -0.0011j E_var:     3.0305 E_err:   0.019234 | Acc: 0.2632
[2025-11-12 09:22:51] 08:36:24<05:03:09, 15.91s/it | [Iter 1947/3090] R0[1856/3000] | LR: 0.009537 | E: -63.724661 | E_img: +0.0022j E_var:     3.0006 E_err:   0.019139 | Acc: 0.2656
[2025-11-12 09:23:07] 08:36:40<05:02:53, 15.91s/it | [Iter 1948/3090] R0[1857/3000] | LR: 0.009522 | E: -63.713041 | E_img: +0.0102j E_var:     2.8400 E_err:   0.018619 | Acc: 0.2743
[2025-11-12 09:23:23] 08:36:56<05:02:38, 15.91s/it | [Iter 1949/3090] R0[1858/3000] | LR: 0.009507 | E: -63.689640 | E_img: +0.0098j E_var:     3.0081 E_err:   0.019162 | Acc: 0.2747
[2025-11-12 09:23:38] 08:37:12<05:02:22, 15.91s/it | [Iter 1950/3090] R0[1859/3000] | LR: 0.009493 | E: -63.710262 | E_img: -0.0025j E_var:     3.0470 E_err:   0.019286 | Acc: 0.2671
[2025-11-12 09:23:54] 08:37:27<05:02:06, 15.91s/it | [Iter 1951/3090] R0[1860/3000] | LR: 0.009478 | E: -63.685106 | E_img: -0.0084j E_var:     2.9735 E_err:   0.019052 | Acc: 0.2678
[2025-11-12 09:24:10] 08:37:43<05:01:50, 15.91s/it | [Iter 1952/3090] R0[1861/3000] | LR: 0.009464 | E: -63.742802 | E_img: -0.0035j E_var:     2.9166 E_err:   0.018869 | Acc: 0.2641
[2025-11-12 09:24:26] 08:37:59<05:01:34, 15.91s/it | [Iter 1953/3090] R0[1862/3000] | LR: 0.009449 | E: -63.711468 | E_img: +0.0172j E_var:     3.0385 E_err:   0.019259 | Acc: 0.2620
[2025-11-12 09:24:42] 08:38:15<05:01:18, 15.91s/it | [Iter 1954/3090] R0[1863/3000] | LR: 0.009434 | E: -63.687884 | E_img: -0.0003j E_var:     2.9765 E_err:   0.019062 | Acc: 0.2611
[2025-11-12 09:24:58] 08:38:31<05:01:02, 15.91s/it | [Iter 1955/3090] R0[1864/3000] | LR: 0.009420 | E: -63.661364 | E_img: -0.0020j E_var:     2.9805 E_err:   0.019074 | Acc: 0.2663
[2025-11-12 09:25:14] 08:38:47<05:00:46, 15.91s/it | [Iter 1956/3090] R0[1865/3000] | LR: 0.009405 | E: -63.667719 | E_img: -0.0291j E_var:     3.0526 E_err:   0.019304 | Acc: 0.2640
[2025-11-12 09:25:29] 08:39:02<05:00:30, 15.91s/it | [Iter 1957/3090] R0[1866/3000] | LR: 0.009391 | E: -63.619911 | E_img: +0.0207j E_var:     3.0419 E_err:   0.019270 | Acc: 0.2746
[2025-11-12 09:25:45] 08:39:18<05:00:14, 15.91s/it | [Iter 1958/3090] R0[1867/3000] | LR: 0.009376 | E: -63.613927 | E_img: +0.0009j E_var:     3.0730 E_err:   0.019368 | Acc: 0.2780
[2025-11-12 09:26:01] 08:39:34<04:59:58, 15.91s/it | [Iter 1959/3090] R0[1868/3000] | LR: 0.009362 | E: -63.674917 | E_img: +0.0159j E_var:     2.8914 E_err:   0.018787 | Acc: 0.2805
[2025-11-12 09:26:17] 08:39:50<04:59:42, 15.91s/it | [Iter 1960/3090] R0[1869/3000] | LR: 0.009347 | E: -63.686132 | E_img: -0.0030j E_var:     3.0053 E_err:   0.019154 | Acc: 0.2763
[2025-11-12 09:26:33] 08:40:06<04:59:26, 15.91s/it | [Iter 1961/3090] R0[1870/3000] | LR: 0.009332 | E: -63.705597 | E_img: -0.0009j E_var:     2.9354 E_err:   0.018929 | Acc: 0.2738
[2025-11-12 09:26:49] 08:40:22<04:59:10, 15.91s/it | [Iter 1962/3090] R0[1871/3000] | LR: 0.009318 | E: -63.684102 | E_img: +0.0026j E_var:     2.8454 E_err:   0.018637 | Acc: 0.2812
[2025-11-12 09:27:04] 08:40:38<04:58:54, 15.91s/it | [Iter 1963/3090] R0[1872/3000] | LR: 0.009303 | E: -63.714029 | E_img: +0.0082j E_var:     2.9567 E_err:   0.018998 | Acc: 0.2741
[2025-11-12 09:27:20] 08:40:53<04:58:38, 15.91s/it | [Iter 1964/3090] R0[1873/3000] | LR: 0.009289 | E: -63.704834 | E_img: +0.0014j E_var:     3.0156 E_err:   0.019186 | Acc: 0.2658
[2025-11-12 09:27:36] 08:41:09<04:58:22, 15.91s/it | [Iter 1965/3090] R0[1874/3000] | LR: 0.009274 | E: -63.708455 | E_img: +0.0143j E_var:     3.0543 E_err:   0.019309 | Acc: 0.2634
[2025-11-12 09:27:52] 08:41:25<04:58:06, 15.91s/it | [Iter 1966/3090] R0[1875/3000] | LR: 0.009260 | E: -63.642438 | E_img: +0.0112j E_var:     3.0351 E_err:   0.019248 | Acc: 0.2609
[2025-11-12 09:28:08] 08:41:41<04:57:50, 15.91s/it | [Iter 1967/3090] R0[1876/3000] | LR: 0.009245 | E: -63.673942 | E_img: +0.0080j E_var:     2.8461 E_err:   0.018639 | Acc: 0.2727
[2025-11-12 09:28:24] 08:41:57<04:57:34, 15.91s/it | [Iter 1968/3090] R0[1877/3000] | LR: 0.009231 | E: -63.679546 | E_img: +0.0062j E_var:     2.8998 E_err:   0.018815 | Acc: 0.2760
[2025-11-12 09:28:39] 08:42:13<04:57:18, 15.91s/it | [Iter 1969/3090] R0[1878/3000] | LR: 0.009216 | E: -63.644655 | E_img: -0.0050j E_var:     2.8861 E_err:   0.018770 | Acc: 0.2768
[2025-11-12 09:28:55] 08:42:28<04:57:02, 15.91s/it | [Iter 1970/3090] R0[1879/3000] | LR: 0.009202 | E: -63.682940 | E_img: -0.0198j E_var:     3.2825 E_err:   0.020017 | Acc: 0.2690
[2025-11-12 09:29:11] 08:42:44<04:56:47, 15.91s/it | [Iter 1971/3090] R0[1880/3000] | LR: 0.009187 | E: -63.661109 | E_img: +0.0015j E_var:     3.1045 E_err:   0.019467 | Acc: 0.2643
[2025-11-12 09:29:27] 08:43:00<04:56:31, 15.91s/it | [Iter 1972/3090] R0[1881/3000] | LR: 0.009173 | E: -63.659403 | E_img: +0.0102j E_var:     3.6534 E_err:   0.021118 | Acc: 0.2514
[2025-11-12 09:29:43] 08:43:16<04:56:15, 15.91s/it | [Iter 1973/3090] R0[1882/3000] | LR: 0.009158 | E: -63.641939 | E_img: -0.0037j E_var:     3.5983 E_err:   0.020958 | Acc: 0.2386
[2025-11-12 09:29:59] 08:43:32<04:55:59, 15.91s/it | [Iter 1974/3090] R0[1883/3000] | LR: 0.009144 | E: -63.716289 | E_img: -0.0133j E_var:     2.8542 E_err:   0.018666 | Acc: 0.2568
[2025-11-12 09:30:15] 08:43:48<04:55:43, 15.91s/it | [Iter 1975/3090] R0[1884/3000] | LR: 0.009129 | E: -63.737364 | E_img: +0.0075j E_var:     3.0011 E_err:   0.019140 | Acc: 0.2622
[2025-11-12 09:30:30] 08:44:03<04:55:27, 15.91s/it | [Iter 1976/3090] R0[1885/3000] | LR: 0.009115 | E: -63.731071 | E_img: -0.0136j E_var:     2.8705 E_err:   0.018719 | Acc: 0.2607
[2025-11-12 09:30:46] 08:44:19<04:55:11, 15.91s/it | [Iter 1977/3090] R0[1886/3000] | LR: 0.009101 | E: -63.730488 | E_img: +0.0123j E_var:     3.2539 E_err:   0.019930 | Acc: 0.2546
[2025-11-12 09:31:02] 08:44:35<04:54:55, 15.91s/it | [Iter 1978/3090] R0[1887/3000] | LR: 0.009086 | E: -63.737590 | E_img: -0.0004j E_var:     2.7521 E_err:   0.018329 | Acc: 0.2725
[2025-11-12 09:31:18] 08:44:51<04:54:39, 15.91s/it | [Iter 1979/3090] R0[1888/3000] | LR: 0.009072 | E: -63.770206 | E_img: -0.0053j E_var:     2.7198 E_err:   0.018221 | Acc: 0.2778
[2025-11-12 09:31:34] 08:45:07<04:54:23, 15.91s/it | [Iter 1980/3090] R0[1889/3000] | LR: 0.009057 | E: -63.727562 | E_img: -0.0001j E_var:     2.9726 E_err:   0.019049 | Acc: 0.2778
[2025-11-12 09:31:50] 08:45:23<04:54:07, 15.91s/it | [Iter 1981/3090] R0[1890/3000] | LR: 0.009043 | E: -63.740799 | E_img: -0.0039j E_var:     3.1877 E_err:   0.019726 | Acc: 0.2658
[2025-11-12 09:32:05] 08:45:39<04:53:51, 15.91s/it | [Iter 1982/3090] R0[1891/3000] | LR: 0.009028 | E: -63.713687 | E_img: -0.0010j E_var:     2.9585 E_err:   0.019004 | Acc: 0.2643
[2025-11-12 09:32:21] 08:45:54<04:53:35, 15.91s/it | [Iter 1983/3090] R0[1892/3000] | LR: 0.009014 | E: -63.784535 | E_img: +0.0029j E_var:     2.8087 E_err:   0.018516 | Acc: 0.2667
[2025-11-12 09:32:37] 08:46:10<04:53:19, 15.91s/it | [Iter 1984/3090] R0[1893/3000] | LR: 0.009000 | E: -63.746500 | E_img: +0.0208j E_var:     2.6937 E_err:   0.018134 | Acc: 0.2800
[2025-11-12 09:32:53] 08:46:26<04:53:03, 15.91s/it | [Iter 1985/3090] R0[1894/3000] | LR: 0.008985 | E: -63.729183 | E_img: +0.0018j E_var:     2.7760 E_err:   0.018408 | Acc: 0.2860
[2025-11-12 09:33:09] 08:46:42<04:52:47, 15.91s/it | [Iter 1986/3090] R0[1895/3000] | LR: 0.008971 | E: -63.709366 | E_img: +0.0073j E_var:     2.6309 E_err:   0.017921 | Acc: 0.2897
[2025-11-12 09:33:25] 08:46:58<04:52:31, 15.91s/it | [Iter 1987/3090] R0[1896/3000] | LR: 0.008956 | E: -63.701084 | E_img: +0.0150j E_var:     2.8326 E_err:   0.018595 | Acc: 0.2860
[2025-11-12 09:33:40] 08:47:14<04:52:15, 15.91s/it | [Iter 1988/3090] R0[1897/3000] | LR: 0.008942 | E: -63.678638 | E_img: -0.0108j E_var:     2.7832 E_err:   0.018432 | Acc: 0.2855
[2025-11-12 09:33:56] 08:47:29<04:51:59, 15.91s/it | [Iter 1989/3090] R0[1898/3000] | LR: 0.008928 | E: -63.694732 | E_img: -0.0043j E_var:     2.9283 E_err:   0.018907 | Acc: 0.2837
[2025-11-12 09:34:12] 08:47:45<04:51:43, 15.91s/it | [Iter 1990/3090] R0[1899/3000] | LR: 0.008913 | E: -63.672927 | E_img: -0.0024j E_var:     2.7690 E_err:   0.018385 | Acc: 0.2912
[2025-11-12 09:34:28] 08:48:01<04:51:27, 15.91s/it | [Iter 1991/3090] R0[1900/3000] | LR: 0.008899 | E: -63.683099 | E_img: +0.0149j E_var:     2.7699 E_err:   0.018388 | Acc: 0.2912
[2025-11-12 09:34:44] 08:48:17<04:51:12, 15.91s/it | [Iter 1992/3090] R0[1901/3000] | LR: 0.008885 | E: -63.688817 | E_img: +0.0143j E_var:     2.8124 E_err:   0.018529 | Acc: 0.2954
[2025-11-12 09:35:00] 08:48:33<04:50:56, 15.91s/it | [Iter 1993/3090] R0[1902/3000] | LR: 0.008870 | E: -63.726463 | E_img: -0.0121j E_var:     2.6913 E_err:   0.018125 | Acc: 0.2932
[2025-11-12 09:35:15] 08:48:49<04:50:40, 15.91s/it | [Iter 1994/3090] R0[1903/3000] | LR: 0.008856 | E: -63.696417 | E_img: +0.0082j E_var:     2.8414 E_err:   0.018624 | Acc: 0.2913
[2025-11-12 09:35:31] 08:49:04<04:50:24, 15.91s/it | [Iter 1995/3090] R0[1904/3000] | LR: 0.008842 | E: -63.683981 | E_img: -0.0202j E_var:     2.7183 E_err:   0.018216 | Acc: 0.2986
[2025-11-12 09:35:47] 08:49:20<04:50:08, 15.91s/it | [Iter 1996/3090] R0[1905/3000] | LR: 0.008827 | E: -63.716778 | E_img: +0.0066j E_var:     2.7076 E_err:   0.018180 | Acc: 0.2894
[2025-11-12 09:36:03] 08:49:36<04:49:52, 15.91s/it | [Iter 1997/3090] R0[1906/3000] | LR: 0.008813 | E: -63.718459 | E_img: -0.0072j E_var:     2.6864 E_err:   0.018109 | Acc: 0.2851
[2025-11-12 09:36:19] 08:49:52<04:49:36, 15.91s/it | [Iter 1998/3090] R0[1907/3000] | LR: 0.008799 | E: -63.715985 | E_img: -0.0025j E_var:     2.8289 E_err:   0.018583 | Acc: 0.2823
[2025-11-12 09:36:35] 08:50:08<04:49:20, 15.91s/it | [Iter 1999/3090] R0[1908/3000] | LR: 0.008784 | E: -63.741346 | E_img: -0.0074j E_var:     2.5850 E_err:   0.017764 | Acc: 0.2875
[2025-11-12 09:36:51] 08:50:24<04:49:04, 15.91s/it | [Iter 2000/3090] R0[1909/3000] | LR: 0.008770 | E: -63.747419 | E_img: -0.0044j E_var:     2.6564 E_err:   0.018007 | Acc: 0.2855
[2025-11-12 09:37:06] 08:50:39<04:48:48, 15.91s/it | [Iter 2001/3090] R0[1910/3000] | LR: 0.008756 | E: -63.761894 | E_img: -0.0102j E_var:     2.6440 E_err:   0.017965 | Acc: 0.2854
[2025-11-12 09:37:22] 08:50:55<04:48:32, 15.91s/it | [Iter 2002/3090] R0[1911/3000] | LR: 0.008742 | E: -63.763634 | E_img: +0.0146j E_var:     2.8653 E_err:   0.018702 | Acc: 0.2810
[2025-11-12 09:37:38] 08:51:11<04:48:16, 15.91s/it | [Iter 2003/3090] R0[1912/3000] | LR: 0.008727 | E: -63.773140 | E_img: +0.0103j E_var:     2.8451 E_err:   0.018636 | Acc: 0.2719
[2025-11-12 09:37:54] 08:51:27<04:48:00, 15.91s/it | [Iter 2004/3090] R0[1913/3000] | LR: 0.008713 | E: -63.766708 | E_img: -0.0109j E_var:     2.6300 E_err:   0.017918 | Acc: 0.2763
[2025-11-12 09:38:10] 08:51:43<04:47:44, 15.91s/it | [Iter 2005/3090] R0[1914/3000] | LR: 0.008699 | E: -63.806660 | E_img: +0.0061j E_var:     2.9902 E_err:   0.019105 | Acc: 0.2672
[2025-11-12 09:38:26] 08:51:59<04:47:28, 15.91s/it | [Iter 2006/3090] R0[1915/3000] | LR: 0.008685 | E: -63.756499 | E_img: -0.0014j E_var:     2.7713 E_err:   0.018393 | Acc: 0.2679
[2025-11-12 09:38:41] 08:52:15<04:47:12, 15.91s/it | [Iter 2007/3090] R0[1916/3000] | LR: 0.008670 | E: -63.664208 | E_img: +0.0084j E_var:     2.8752 E_err:   0.018734 | Acc: 0.2855
[2025-11-12 09:38:57] 08:52:30<04:46:56, 15.91s/it | [Iter 2008/3090] R0[1917/3000] | LR: 0.008656 | E: -63.646033 | E_img: +0.0164j E_var:     2.8178 E_err:   0.018546 | Acc: 0.2918
[2025-11-12 09:39:13] 08:52:46<04:46:40, 15.91s/it | [Iter 2009/3090] R0[1918/3000] | LR: 0.008642 | E: -63.607872 | E_img: +0.0128j E_var:     2.8728 E_err:   0.018727 | Acc: 0.2978
[2025-11-12 09:39:29] 08:53:02<04:46:24, 15.91s/it | [Iter 2010/3090] R0[1919/3000] | LR: 0.008628 | E: -63.705523 | E_img: -0.0207j E_var:     2.8015 E_err:   0.018493 | Acc: 0.2906
[2025-11-12 09:39:45] 08:53:18<04:46:08, 15.91s/it | [Iter 2011/3090] R0[1920/3000] | LR: 0.008613 | E: -63.699459 | E_img: +0.0045j E_var:     2.7480 E_err:   0.018315 | Acc: 0.2831
[2025-11-12 09:40:01] 08:53:34<04:45:52, 15.91s/it | [Iter 2012/3090] R0[1921/3000] | LR: 0.008599 | E: -63.753555 | E_img: +0.0024j E_var:     2.7907 E_err:   0.018457 | Acc: 0.2764
[2025-11-12 09:40:16] 08:53:50<04:45:37, 15.91s/it | [Iter 2013/3090] R0[1922/3000] | LR: 0.008585 | E: -63.797122 | E_img: +0.0077j E_var:     3.1186 E_err:   0.019511 | Acc: 0.2612
[2025-11-12 09:40:32] 08:54:05<04:45:21, 15.91s/it | [Iter 2014/3090] R0[1923/3000] | LR: 0.008571 | E: -63.741680 | E_img: +0.0030j E_var:     2.9745 E_err:   0.019055 | Acc: 0.2554
[2025-11-12 09:40:48] 08:54:21<04:45:05, 15.91s/it | [Iter 2015/3090] R0[1924/3000] | LR: 0.008557 | E: -63.710379 | E_img: -0.0047j E_var:     3.3703 E_err:   0.020283 | Acc: 0.2491
[2025-11-12 09:41:04] 08:54:37<04:44:49, 15.91s/it | [Iter 2016/3090] R0[1925/3000] | LR: 0.008542 | E: -63.728886 | E_img: +0.0095j E_var:     3.2780 E_err:   0.020004 | Acc: 0.2471
[2025-11-12 09:41:20] 08:54:53<04:44:33, 15.91s/it | [Iter 2017/3090] R0[1926/3000] | LR: 0.008528 | E: -63.729476 | E_img: +0.0084j E_var:     3.3906 E_err:   0.020344 | Acc: 0.2463
[2025-11-12 09:41:36] 08:55:09<04:44:17, 15.91s/it | [Iter 2018/3090] R0[1927/3000] | LR: 0.008514 | E: -63.685317 | E_img: -0.0018j E_var:     3.3397 E_err:   0.020191 | Acc: 0.2399
[2025-11-12 09:41:52] 08:55:25<04:44:01, 15.91s/it | [Iter 2019/3090] R0[1928/3000] | LR: 0.008500 | E: -63.687866 | E_img: -0.0235j E_var:     3.1119 E_err:   0.019490 | Acc: 0.2458
[2025-11-12 09:42:07] 08:55:40<04:43:45, 15.91s/it | [Iter 2020/3090] R0[1929/3000] | LR: 0.008486 | E: -63.712617 | E_img: -0.0039j E_var:     3.5118 E_err:   0.020705 | Acc: 0.2430
[2025-11-12 09:42:23] 08:55:56<04:43:29, 15.91s/it | [Iter 2021/3090] R0[1930/3000] | LR: 0.008472 | E: -63.678363 | E_img: +0.0015j E_var:     3.1522 E_err:   0.019616 | Acc: 0.2435
[2025-11-12 09:42:39] 08:56:12<04:43:13, 15.91s/it | [Iter 2022/3090] R0[1931/3000] | LR: 0.008457 | E: -63.773964 | E_img: +0.0029j E_var:     2.8192 E_err:   0.018551 | Acc: 0.2587
[2025-11-12 09:42:55] 08:56:28<04:42:57, 15.91s/it | [Iter 2023/3090] R0[1932/3000] | LR: 0.008443 | E: -63.722220 | E_img: +0.0011j E_var:     2.9603 E_err:   0.019010 | Acc: 0.2755
[2025-11-12 09:43:11] 08:56:44<04:42:41, 15.91s/it | [Iter 2024/3090] R0[1933/3000] | LR: 0.008429 | E: -63.740627 | E_img: -0.0052j E_var:     2.8124 E_err:   0.018529 | Acc: 0.2758
[2025-11-12 09:43:27] 08:57:00<04:42:25, 15.91s/it | [Iter 2025/3090] R0[1934/3000] | LR: 0.008415 | E: -63.717717 | E_img: +0.0077j E_var:     2.9147 E_err:   0.018862 | Acc: 0.2701
[2025-11-12 09:43:42] 08:57:16<04:42:09, 15.91s/it | [Iter 2026/3090] R0[1935/3000] | LR: 0.008401 | E: -63.684597 | E_img: +0.0301j E_var:     3.1807 E_err:   0.019705 | Acc: 0.2618
[2025-11-12 09:43:58] 08:57:31<04:41:53, 15.91s/it | [Iter 2027/3090] R0[1936/3000] | LR: 0.008387 | E: -63.770356 | E_img: -0.0048j E_var:     3.1517 E_err:   0.019615 | Acc: 0.2607
[2025-11-12 09:44:14] 08:57:47<04:41:37, 15.91s/it | [Iter 2028/3090] R0[1937/3000] | LR: 0.008373 | E: -63.740012 | E_img: +0.0153j E_var:     3.1440 E_err:   0.019590 | Acc: 0.2569
[2025-11-12 09:44:30] 08:58:03<04:41:21, 15.91s/it | [Iter 2029/3090] R0[1938/3000] | LR: 0.008359 | E: -63.721454 | E_img: -0.0015j E_var:     3.1870 E_err:   0.019724 | Acc: 0.2511
[2025-11-12 09:44:46] 08:58:19<04:41:05, 15.91s/it | [Iter 2030/3090] R0[1939/3000] | LR: 0.008345 | E: -63.789872 | E_img: +0.0009j E_var:     2.9747 E_err:   0.019056 | Acc: 0.2545
[2025-11-12 09:45:02] 08:58:35<04:40:49, 15.91s/it | [Iter 2031/3090] R0[1940/3000] | LR: 0.008331 | E: -63.761451 | E_img: -0.0070j E_var:     3.1325 E_err:   0.019555 | Acc: 0.2536
[2025-11-12 09:45:17] 08:58:51<04:40:34, 15.91s/it | [Iter 2032/3090] R0[1941/3000] | LR: 0.008316 | E: -63.761992 | E_img: -0.0056j E_var:     2.8799 E_err:   0.018750 | Acc: 0.2581
[2025-11-12 09:45:33] 08:59:06<04:40:18, 15.91s/it | [Iter 2033/3090] R0[1942/3000] | LR: 0.008302 | E: -63.725240 | E_img: -0.0006j E_var:     2.8598 E_err:   0.018684 | Acc: 0.2646
[2025-11-12 09:45:49] 08:59:22<04:40:02, 15.91s/it | [Iter 2034/3090] R0[1943/3000] | LR: 0.008288 | E: -63.778818 | E_img: -0.0108j E_var:     2.8636 E_err:   0.018697 | Acc: 0.2662
[2025-11-12 09:46:05] 08:59:38<04:39:46, 15.91s/it | [Iter 2035/3090] R0[1944/3000] | LR: 0.008274 | E: -63.787374 | E_img: -0.0038j E_var:     2.7466 E_err:   0.018311 | Acc: 0.2791
[2025-11-12 09:46:21] 08:59:54<04:39:30, 15.91s/it | [Iter 2036/3090] R0[1945/3000] | LR: 0.008260 | E: -63.741204 | E_img: -0.0153j E_var:     2.7175 E_err:   0.018213 | Acc: 0.2897
[2025-11-12 09:46:37] 09:00:10<04:39:14, 15.91s/it | [Iter 2037/3090] R0[1946/3000] | LR: 0.008246 | E: -63.751605 | E_img: +0.0079j E_var:     2.9910 E_err:   0.019108 | Acc: 0.2795
[2025-11-12 09:46:53] 09:00:26<04:38:58, 15.91s/it | [Iter 2038/3090] R0[1947/3000] | LR: 0.008232 | E: -63.774198 | E_img: -0.0072j E_var:     2.8891 E_err:   0.018779 | Acc: 0.2728
[2025-11-12 09:47:08] 09:00:41<04:38:42, 15.91s/it | [Iter 2039/3090] R0[1948/3000] | LR: 0.008218 | E: -63.774783 | E_img: +0.0242j E_var:     3.0850 E_err:   0.019406 | Acc: 0.2611
[2025-11-12 09:47:24] 09:00:57<04:38:26, 15.91s/it | [Iter 2040/3090] R0[1949/3000] | LR: 0.008204 | E: -63.767612 | E_img: +0.0093j E_var:     2.8580 E_err:   0.018678 | Acc: 0.2584
[2025-11-12 09:47:40] 09:01:13<04:38:10, 15.91s/it | [Iter 2041/3090] R0[1950/3000] | LR: 0.008190 | E: -63.752133 | E_img: +0.0035j E_var:     2.9631 E_err:   0.019019 | Acc: 0.2549
[2025-11-12 09:47:56] 09:01:29<04:37:54, 15.91s/it | [Iter 2042/3090] R0[1951/3000] | LR: 0.008176 | E: -63.706274 | E_img: -0.0168j E_var:     3.6086 E_err:   0.020988 | Acc: 0.2403
[2025-11-12 09:48:12] 09:01:45<04:37:38, 15.91s/it | [Iter 2043/3090] R0[1952/3000] | LR: 0.008162 | E: -63.781598 | E_img: -0.0010j E_var:     2.8694 E_err:   0.018715 | Acc: 0.2501
[2025-11-12 09:48:28] 09:02:01<04:37:22, 15.91s/it | [Iter 2044/3090] R0[1953/3000] | LR: 0.008148 | E: -63.765314 | E_img: +0.0032j E_var:     2.9294 E_err:   0.018910 | Acc: 0.2564
[2025-11-12 09:48:43] 09:02:17<04:37:06, 15.91s/it | [Iter 2045/3090] R0[1954/3000] | LR: 0.008134 | E: -63.738484 | E_img: +0.0127j E_var:     3.1821 E_err:   0.019709 | Acc: 0.2512
[2025-11-12 09:48:59] 09:02:32<04:36:50, 15.91s/it | [Iter 2046/3090] R0[1955/3000] | LR: 0.008120 | E: -63.692194 | E_img: -0.0003j E_var:     3.2194 E_err:   0.019824 | Acc: 0.2471
[2025-11-12 09:49:15] 09:02:48<04:36:34, 15.91s/it | [Iter 2047/3090] R0[1956/3000] | LR: 0.008106 | E: -63.752682 | E_img: -0.0012j E_var:     3.1025 E_err:   0.019461 | Acc: 0.2427
[2025-11-12 09:49:31] 09:03:04<04:36:18, 15.91s/it | [Iter 2048/3090] R0[1957/3000] | LR: 0.008092 | E: -63.726237 | E_img: -0.0058j E_var:     3.2397 E_err:   0.019887 | Acc: 0.2361
[2025-11-12 09:49:47] 09:03:20<04:36:02, 15.91s/it | [Iter 2049/3090] R0[1958/3000] | LR: 0.008078 | E: -63.741596 | E_img: +0.0043j E_var:     2.8661 E_err:   0.018705 | Acc: 0.2459
[2025-11-12 09:50:03] 09:03:36<04:35:47, 15.91s/it | [Iter 2050/3090] R0[1959/3000] | LR: 0.008065 | E: -63.763001 | E_img: +0.0169j E_var:     2.6859 E_err:   0.018107 | Acc: 0.2589
[2025-11-12 09:50:18] 09:03:52<04:35:31, 15.91s/it | [Iter 2051/3090] R0[1960/3000] | LR: 0.008051 | E: -63.762023 | E_img: -0.0006j E_var:     2.8413 E_err:   0.018624 | Acc: 0.2659
[2025-11-12 09:50:34] 09:04:07<04:35:15, 15.91s/it | [Iter 2052/3090] R0[1961/3000] | LR: 0.008037 | E: -63.746936 | E_img: -0.0000j E_var:     2.8788 E_err:   0.018746 | Acc: 0.2667
[2025-11-12 09:50:50] 09:04:23<04:34:59, 15.91s/it | [Iter 2053/3090] R0[1962/3000] | LR: 0.008023 | E: -63.776744 | E_img: -0.0078j E_var:     2.8860 E_err:   0.018770 | Acc: 0.2649
[2025-11-12 09:51:06] 09:04:39<04:34:43, 15.91s/it | [Iter 2054/3090] R0[1963/3000] | LR: 0.008009 | E: -63.740076 | E_img: +0.0058j E_var:     2.9702 E_err:   0.019041 | Acc: 0.2649
[2025-11-12 09:51:22] 09:04:55<04:34:27, 15.91s/it | [Iter 2055/3090] R0[1964/3000] | LR: 0.007995 | E: -63.757369 | E_img: +0.0037j E_var:     2.8491 E_err:   0.018649 | Acc: 0.2676
[2025-11-12 09:51:38] 09:05:11<04:34:11, 15.91s/it | [Iter 2056/3090] R0[1965/3000] | LR: 0.007981 | E: -63.765091 | E_img: -0.0052j E_var:     2.8489 E_err:   0.018648 | Acc: 0.2731
[2025-11-12 09:51:54] 09:05:27<04:33:55, 15.91s/it | [Iter 2057/3090] R0[1966/3000] | LR: 0.007967 | E: -63.722852 | E_img: +0.0104j E_var:     2.8885 E_err:   0.018778 | Acc: 0.2737
[2025-11-12 09:52:09] 09:05:42<04:33:39, 15.91s/it | [Iter 2058/3090] R0[1967/3000] | LR: 0.007953 | E: -63.721297 | E_img: -0.0066j E_var:     2.9360 E_err:   0.018931 | Acc: 0.2662
[2025-11-12 09:52:25] 09:05:58<04:33:23, 15.91s/it | [Iter 2059/3090] R0[1968/3000] | LR: 0.007940 | E: -63.713103 | E_img: -0.0061j E_var:     2.8998 E_err:   0.018814 | Acc: 0.2634
[2025-11-12 09:52:41] 09:06:14<04:33:07, 15.91s/it | [Iter 2060/3090] R0[1969/3000] | LR: 0.007926 | E: -63.759058 | E_img: -0.0097j E_var:     2.6799 E_err:   0.018087 | Acc: 0.2767
[2025-11-12 09:52:57] 09:06:30<04:32:51, 15.91s/it | [Iter 2061/3090] R0[1970/3000] | LR: 0.007912 | E: -63.769907 | E_img: -0.0058j E_var:     2.7476 E_err:   0.018314 | Acc: 0.2792
[2025-11-12 09:53:13] 09:06:46<04:32:35, 15.91s/it | [Iter 2062/3090] R0[1971/3000] | LR: 0.007898 | E: -63.719899 | E_img: -0.0126j E_var:     2.6889 E_err:   0.018117 | Acc: 0.2961
[2025-11-12 09:53:29] 09:07:02<04:32:19, 15.91s/it | [Iter 2063/3090] R0[1972/3000] | LR: 0.007884 | E: -63.656272 | E_img: +0.0011j E_var:     2.8550 E_err:   0.018668 | Acc: 0.3022
[2025-11-12 09:53:44] 09:07:18<04:32:03, 15.91s/it | [Iter 2064/3090] R0[1973/3000] | LR: 0.007870 | E: -63.723768 | E_img: +0.0088j E_var:     2.6902 E_err:   0.018121 | Acc: 0.2934
[2025-11-12 09:54:00] 09:07:33<04:31:47, 15.91s/it | [Iter 2065/3090] R0[1974/3000] | LR: 0.007857 | E: -63.710761 | E_img: +0.0080j E_var:     2.6755 E_err:   0.018072 | Acc: 0.2953
[2025-11-12 09:54:16] 09:07:49<04:31:31, 15.91s/it | [Iter 2066/3090] R0[1975/3000] | LR: 0.007843 | E: -63.712522 | E_img: -0.0034j E_var:     2.6261 E_err:   0.017904 | Acc: 0.2935
[2025-11-12 09:54:32] 09:08:05<04:31:15, 15.91s/it | [Iter 2067/3090] R0[1976/3000] | LR: 0.007829 | E: -63.775816 | E_img: -0.0126j E_var:     2.7375 E_err:   0.018280 | Acc: 0.2812
[2025-11-12 09:54:48] 09:08:21<04:31:00, 15.91s/it | [Iter 2068/3090] R0[1977/3000] | LR: 0.007815 | E: -63.714533 | E_img: -0.0051j E_var:     2.5690 E_err:   0.017709 | Acc: 0.2953
[2025-11-12 09:55:04] 09:08:37<04:30:44, 15.91s/it | [Iter 2069/3090] R0[1978/3000] | LR: 0.007801 | E: -63.637670 | E_img: +0.0008j E_var:     2.6303 E_err:   0.017919 | Acc: 0.3089
[2025-11-12 09:55:19] 09:08:53<04:30:28, 15.91s/it | [Iter 2070/3090] R0[1979/3000] | LR: 0.007788 | E: -63.632624 | E_img: -0.0038j E_var:     2.6276 E_err:   0.017910 | Acc: 0.3108
[2025-11-12 09:55:35] 09:09:08<04:30:12, 15.91s/it | [Iter 2071/3090] R0[1980/3000] | LR: 0.007774 | E: -63.601275 | E_img: +0.0049j E_var:     2.6276 E_err:   0.017910 | Acc: 0.3115
[2025-11-12 09:55:51] 09:09:24<04:29:56, 15.91s/it | [Iter 2072/3090] R0[1981/3000] | LR: 0.007760 | E: -63.638699 | E_img: +0.0188j E_var:     2.5591 E_err:   0.017675 | Acc: 0.3070
[2025-11-12 09:56:07] 09:09:40<04:29:40, 15.91s/it | [Iter 2073/3090] R0[1982/3000] | LR: 0.007746 | E: -63.584177 | E_img: +0.0010j E_var:     2.5840 E_err:   0.017760 | Acc: 0.3121
[2025-11-12 09:56:23] 09:09:56<04:29:24, 15.91s/it | [Iter 2074/3090] R0[1983/3000] | LR: 0.007733 | E: -63.556365 | E_img: +0.0014j E_var:     2.5702 E_err:   0.017713 | Acc: 0.3227
[2025-11-12 09:56:39] 09:10:12<04:29:08, 15.91s/it | [Iter 2075/3090] R0[1984/3000] | LR: 0.007719 | E: -63.629069 | E_img: +0.0042j E_var:     2.6537 E_err:   0.017998 | Acc: 0.3142
[2025-11-12 09:56:55] 09:10:28<04:28:52, 15.91s/it | [Iter 2076/3090] R0[1985/3000] | LR: 0.007705 | E: -63.592330 | E_img: -0.0077j E_var:     2.5809 E_err:   0.017750 | Acc: 0.3186
[2025-11-12 09:57:10] 09:10:44<04:28:36, 15.91s/it | [Iter 2077/3090] R0[1986/3000] | LR: 0.007691 | E: -63.651521 | E_img: +0.0054j E_var:     2.5501 E_err:   0.017644 | Acc: 0.3119
[2025-11-12 09:57:26] 09:10:59<04:28:20, 15.91s/it | [Iter 2078/3090] R0[1987/3000] | LR: 0.007678 | E: -63.751887 | E_img: +0.0065j E_var:     2.7160 E_err:   0.018208 | Acc: 0.2907
[2025-11-12 09:57:42] 09:11:15<04:28:04, 15.91s/it | [Iter 2079/3090] R0[1988/3000] | LR: 0.007664 | E: -63.762533 | E_img: +0.0061j E_var:     2.4642 E_err:   0.017344 | Acc: 0.2886
[2025-11-12 09:57:58] 09:11:31<04:27:48, 15.91s/it | [Iter 2080/3090] R0[1989/3000] | LR: 0.007650 | E: -63.741398 | E_img: -0.0118j E_var:     2.9456 E_err:   0.018962 | Acc: 0.2710
[2025-11-12 09:58:14] 09:11:47<04:27:32, 15.91s/it | [Iter 2081/3090] R0[1990/3000] | LR: 0.007637 | E: -63.777559 | E_img: -0.0038j E_var:     2.8611 E_err:   0.018688 | Acc: 0.2628
[2025-11-12 09:58:30] 09:12:03<04:27:16, 15.91s/it | [Iter 2082/3090] R0[1991/3000] | LR: 0.007623 | E: -63.753479 | E_img: +0.0087j E_var:     2.8111 E_err:   0.018524 | Acc: 0.2660
[2025-11-12 09:58:45] 09:12:19<04:27:00, 15.91s/it | [Iter 2083/3090] R0[1992/3000] | LR: 0.007609 | E: -63.706654 | E_img: -0.0118j E_var:     3.0747 E_err:   0.019373 | Acc: 0.2627
[2025-11-12 09:59:01] 09:12:34<04:26:44, 15.91s/it | [Iter 2084/3090] R0[1993/3000] | LR: 0.007595 | E: -63.588586 | E_img: -0.0282j E_var:     3.4052 E_err:   0.020388 | Acc: 0.2515
[2025-11-12 09:59:17] 09:12:50<04:26:29, 15.91s/it | [Iter 2085/3090] R0[1994/3000] | LR: 0.007582 | E: -63.657233 | E_img: -0.0205j E_var:     2.8445 E_err:   0.018634 | Acc: 0.2610
[2025-11-12 09:59:33] 09:13:06<04:26:13, 15.91s/it | [Iter 2086/3090] R0[1995/3000] | LR: 0.007568 | E: -63.723462 | E_img: -0.0019j E_var:     2.9118 E_err:   0.018853 | Acc: 0.2657
[2025-11-12 09:59:49] 09:13:22<04:25:57, 15.91s/it | [Iter 2087/3090] R0[1996/3000] | LR: 0.007555 | E: -63.674204 | E_img: +0.0060j E_var:     3.1433 E_err:   0.019588 | Acc: 0.2689
[2025-11-12 10:00:05] 09:13:38<04:25:41, 15.91s/it | [Iter 2088/3090] R0[1997/3000] | LR: 0.007541 | E: -63.596721 | E_img: -0.0110j E_var:     2.9649 E_err:   0.019024 | Acc: 0.2733
[2025-11-12 10:00:21] 09:13:54<04:25:25, 15.91s/it | [Iter 2089/3090] R0[1998/3000] | LR: 0.007527 | E: -63.654249 | E_img: +0.0149j E_var:     2.7225 E_err:   0.018230 | Acc: 0.2796
[2025-11-12 10:00:36] 09:14:10<04:25:09, 15.91s/it | [Iter 2090/3090] R0[1999/3000] | LR: 0.007514 | E: -63.687281 | E_img: +0.0194j E_var:     2.6435 E_err:   0.017964 | Acc: 0.2900
[2025-11-12 10:00:52] 09:14:25<04:24:53, 15.91s/it | [Iter 2091/3090] R0[2000/3000] | LR: 0.007500 | E: -63.636732 | E_img: +0.0187j E_var:     2.7669 E_err:   0.018378 | Acc: 0.2974
[2025-11-12 10:01:08] 09:14:41<04:24:37, 15.91s/it | [Iter 2092/3090] R0[2001/3000] | LR: 0.007486 | E: -63.722588 | E_img: -0.0077j E_var:     2.6330 E_err:   0.017928 | Acc: 0.2952
[2025-11-12 10:01:24] 09:14:57<04:24:21, 15.91s/it | [Iter 2093/3090] R0[2002/3000] | LR: 0.007473 | E: -63.696092 | E_img: -0.0058j E_var:     2.6845 E_err:   0.018102 | Acc: 0.2876
[2025-11-12 10:01:40] 09:15:13<04:24:05, 15.91s/it | [Iter 2094/3090] R0[2003/3000] | LR: 0.007459 | E: -63.699184 | E_img: -0.0049j E_var:     2.6610 E_err:   0.018023 | Acc: 0.2867
[2025-11-12 10:01:56] 09:15:29<04:23:49, 15.91s/it | [Iter 2095/3090] R0[2004/3000] | LR: 0.007446 | E: -63.728192 | E_img: -0.0049j E_var:     3.0305 E_err:   0.019234 | Acc: 0.2719
[2025-11-12 10:02:11] 09:15:45<04:23:33, 15.91s/it | [Iter 2096/3090] R0[2005/3000] | LR: 0.007432 | E: -63.710050 | E_img: -0.0192j E_var:     3.0500 E_err:   0.019295 | Acc: 0.2618
[2025-11-12 10:02:27] 09:16:00<04:23:17, 15.91s/it | [Iter 2097/3090] R0[2006/3000] | LR: 0.007419 | E: -63.716168 | E_img: -0.0034j E_var:     2.8503 E_err:   0.018653 | Acc: 0.2618
[2025-11-12 10:02:43] 09:16:16<04:23:01, 15.91s/it | [Iter 2098/3090] R0[2007/3000] | LR: 0.007405 | E: -63.719675 | E_img: +0.0057j E_var:     3.1761 E_err:   0.019690 | Acc: 0.2620
[2025-11-12 10:02:59] 09:16:32<04:22:45, 15.91s/it | [Iter 2099/3090] R0[2008/3000] | LR: 0.007392 | E: -63.770941 | E_img: +0.0099j E_var:     2.6441 E_err:   0.017966 | Acc: 0.2742
[2025-11-12 10:03:15] 09:16:48<04:22:29, 15.91s/it | [Iter 2100/3090] R0[2009/3000] | LR: 0.007378 | E: -63.757168 | E_img: -0.0057j E_var:     2.6515 E_err:   0.017991 | Acc: 0.2905
[2025-11-12 10:03:31] 09:17:04<04:22:14, 15.91s/it | [Iter 2101/3090] R0[2010/3000] | LR: 0.007364 | E: -63.673498 | E_img: -0.0022j E_var:     2.6338 E_err:   0.017931 | Acc: 0.3035
[2025-11-12 10:03:47] 09:17:20<04:21:58, 15.91s/it | [Iter 2102/3090] R0[2011/3000] | LR: 0.007351 | E: -63.702499 | E_img: +0.0042j E_var:     2.6240 E_err:   0.017897 | Acc: 0.2997
[2025-11-12 10:04:02] 09:17:35<04:21:42, 15.91s/it | [Iter 2103/3090] R0[2012/3000] | LR: 0.007337 | E: -63.713651 | E_img: -0.0149j E_var:     2.6833 E_err:   0.018098 | Acc: 0.2930
[2025-11-12 10:04:18] 09:17:51<04:21:26, 15.91s/it | [Iter 2104/3090] R0[2013/3000] | LR: 0.007324 | E: -63.754807 | E_img: -0.0005j E_var:     2.7033 E_err:   0.018166 | Acc: 0.2823
[2025-11-12 10:04:34] 09:18:07<04:21:10, 15.91s/it | [Iter 2105/3090] R0[2014/3000] | LR: 0.007310 | E: -63.761580 | E_img: +0.0203j E_var:     2.6727 E_err:   0.018063 | Acc: 0.2804
[2025-11-12 10:04:50] 09:18:23<04:20:54, 15.91s/it | [Iter 2106/3090] R0[2015/3000] | LR: 0.007297 | E: -63.776251 | E_img: +0.0012j E_var:     2.6415 E_err:   0.017957 | Acc: 0.2865
[2025-11-12 10:05:06] 09:18:39<04:20:38, 15.91s/it | [Iter 2107/3090] R0[2016/3000] | LR: 0.007283 | E: -63.743318 | E_img: +0.0080j E_var:     2.5639 E_err:   0.017691 | Acc: 0.2935
[2025-11-12 10:05:22] 09:18:55<04:20:22, 15.91s/it | [Iter 2108/3090] R0[2017/3000] | LR: 0.007270 | E: -63.736748 | E_img: +0.0002j E_var:     2.5427 E_err:   0.017618 | Acc: 0.2993
[2025-11-12 10:05:37] 09:19:10<04:20:06, 15.91s/it | [Iter 2109/3090] R0[2018/3000] | LR: 0.007257 | E: -63.698993 | E_img: +0.0109j E_var:     2.6056 E_err:   0.017834 | Acc: 0.2992
[2025-11-12 10:05:53] 09:19:26<04:19:50, 15.91s/it | [Iter 2110/3090] R0[2019/3000] | LR: 0.007243 | E: -63.743622 | E_img: -0.0056j E_var:     2.5649 E_err:   0.017695 | Acc: 0.2962
[2025-11-12 10:06:09] 09:19:42<04:19:34, 15.91s/it | [Iter 2111/3090] R0[2020/3000] | LR: 0.007230 | E: -63.680726 | E_img: -0.0079j E_var:     2.6428 E_err:   0.017961 | Acc: 0.3014
[2025-11-12 10:06:25] 09:19:58<04:19:18, 15.91s/it | [Iter 2112/3090] R0[2021/3000] | LR: 0.007216 | E: -63.710291 | E_img: -0.0059j E_var:     2.6175 E_err:   0.017875 | Acc: 0.3008
[2025-11-12 10:06:41] 09:20:14<04:19:02, 15.91s/it | [Iter 2113/3090] R0[2022/3000] | LR: 0.007203 | E: -63.713489 | E_img: +0.0067j E_var:     2.5743 E_err:   0.017727 | Acc: 0.2992
[2025-11-12 10:06:56] 09:20:30<04:18:46, 15.91s/it | [Iter 2114/3090] R0[2023/3000] | LR: 0.007189 | E: -63.666132 | E_img: -0.0016j E_var:     2.6148 E_err:   0.017866 | Acc: 0.2995
[2025-11-12 10:07:12] 09:20:45<04:18:30, 15.91s/it | [Iter 2115/3090] R0[2024/3000] | LR: 0.007176 | E: -63.685415 | E_img: -0.0050j E_var:     2.5789 E_err:   0.017743 | Acc: 0.2992
[2025-11-12 10:07:28] 09:21:01<04:18:14, 15.91s/it | [Iter 2116/3090] R0[2025/3000] | LR: 0.007163 | E: -63.629469 | E_img: -0.0066j E_var:     2.5939 E_err:   0.017794 | Acc: 0.3083
[2025-11-12 10:07:44] 09:21:17<04:17:58, 15.91s/it | [Iter 2117/3090] R0[2026/3000] | LR: 0.007149 | E: -63.731595 | E_img: +0.0019j E_var:     2.5676 E_err:   0.017704 | Acc: 0.2956
[2025-11-12 10:08:00] 09:21:33<04:17:42, 15.91s/it | [Iter 2118/3090] R0[2027/3000] | LR: 0.007136 | E: -63.769118 | E_img: -0.0027j E_var:     2.7795 E_err:   0.018420 | Acc: 0.2805
[2025-11-12 10:08:16] 09:21:49<04:17:27, 15.91s/it | [Iter 2119/3090] R0[2028/3000] | LR: 0.007122 | E: -63.780105 | E_img: -0.0043j E_var:     2.7076 E_err:   0.018180 | Acc: 0.2740
[2025-11-12 10:08:32] 09:22:05<04:17:11, 15.91s/it | [Iter 2120/3090] R0[2029/3000] | LR: 0.007109 | E: -63.785271 | E_img: +0.0034j E_var:     2.6782 E_err:   0.018081 | Acc: 0.2714
[2025-11-12 10:08:47] 09:22:21<04:16:55, 15.91s/it | [Iter 2121/3090] R0[2030/3000] | LR: 0.007096 | E: -63.784036 | E_img: -0.0014j E_var:     2.7874 E_err:   0.018446 | Acc: 0.2686
[2025-11-12 10:09:03] 09:22:36<04:16:39, 15.91s/it | [Iter 2122/3090] R0[2031/3000] | LR: 0.007082 | E: -63.786977 | E_img: -0.0220j E_var:     2.7818 E_err:   0.018427 | Acc: 0.2705
[2025-11-12 10:09:19] 09:22:52<04:16:23, 15.91s/it | [Iter 2123/3090] R0[2032/3000] | LR: 0.007069 | E: -63.793764 | E_img: +0.0074j E_var:     2.9637 E_err:   0.019020 | Acc: 0.2634
[2025-11-12 10:09:35] 09:23:08<04:16:07, 15.91s/it | [Iter 2124/3090] R0[2033/3000] | LR: 0.007056 | E: -63.796044 | E_img: -0.0027j E_var:     2.9074 E_err:   0.018839 | Acc: 0.2641
[2025-11-12 10:09:51] 09:23:24<04:15:51, 15.91s/it | [Iter 2125/3090] R0[2034/3000] | LR: 0.007042 | E: -63.803000 | E_img: +0.0127j E_var:     2.7051 E_err:   0.018172 | Acc: 0.2682
[2025-11-12 10:10:07] 09:23:40<04:15:35, 15.91s/it | [Iter 2126/3090] R0[2035/3000] | LR: 0.007029 | E: -63.776167 | E_img: +0.0021j E_var:     2.7759 E_err:   0.018408 | Acc: 0.2704
[2025-11-12 10:10:22] 09:23:56<04:15:19, 15.91s/it | [Iter 2127/3090] R0[2036/3000] | LR: 0.007016 | E: -63.775976 | E_img: +0.0028j E_var:     3.7681 E_err:   0.021447 | Acc: 0.2691
[2025-11-12 10:10:38] 09:24:11<04:15:03, 15.91s/it | [Iter 2128/3090] R0[2037/3000] | LR: 0.007003 | E: -63.774896 | E_img: +0.0153j E_var:     2.7735 E_err:   0.018400 | Acc: 0.2722
[2025-11-12 10:10:54] 09:24:27<04:14:47, 15.91s/it | [Iter 2129/3090] R0[2038/3000] | LR: 0.006989 | E: -63.791973 | E_img: -0.0057j E_var:     2.8534 E_err:   0.018663 | Acc: 0.2705
[2025-11-12 10:11:10] 09:24:43<04:14:31, 15.91s/it | [Iter 2130/3090] R0[2039/3000] | LR: 0.006976 | E: -63.729942 | E_img: -0.0028j E_var:     2.8505 E_err:   0.018654 | Acc: 0.2752
[2025-11-12 10:11:26] 09:24:59<04:14:15, 15.91s/it | [Iter 2131/3090] R0[2040/3000] | LR: 0.006963 | E: -63.733235 | E_img: -0.0053j E_var:     2.9236 E_err:   0.018892 | Acc: 0.2744
[2025-11-12 10:11:42] 09:25:15<04:13:59, 15.91s/it | [Iter 2132/3090] R0[2041/3000] | LR: 0.006949 | E: -63.772605 | E_img: +0.0042j E_var:     2.7777 E_err:   0.018414 | Acc: 0.2716
[2025-11-12 10:11:57] 09:25:31<04:13:43, 15.91s/it | [Iter 2133/3090] R0[2042/3000] | LR: 0.006936 | E: -63.710366 | E_img: -0.0097j E_var:     2.9031 E_err:   0.018825 | Acc: 0.2729
[2025-11-12 10:12:13] 09:25:46<04:13:27, 15.91s/it | [Iter 2134/3090] R0[2043/3000] | LR: 0.006923 | E: -63.699495 | E_img: -0.0147j E_var:     2.9693 E_err:   0.019039 | Acc: 0.2684
[2025-11-12 10:12:29] 09:26:02<04:13:12, 15.91s/it | [Iter 2135/3090] R0[2044/3000] | LR: 0.006910 | E: -63.769801 | E_img: +0.0175j E_var:     3.1385 E_err:   0.019573 | Acc: 0.2674
[2025-11-12 10:12:45] 09:26:18<04:12:56, 15.91s/it | [Iter 2136/3090] R0[2045/3000] | LR: 0.006896 | E: -63.765538 | E_img: +0.0007j E_var:     2.8903 E_err:   0.018784 | Acc: 0.2649
[2025-11-12 10:13:01] 09:26:34<04:12:40, 15.91s/it | [Iter 2137/3090] R0[2046/3000] | LR: 0.006883 | E: -63.737264 | E_img: -0.0160j E_var:     2.7163 E_err:   0.018209 | Acc: 0.2814
[2025-11-12 10:13:17] 09:26:50<04:12:24, 15.91s/it | [Iter 2138/3090] R0[2047/3000] | LR: 0.006870 | E: -63.771925 | E_img: -0.0047j E_var:     2.6461 E_err:   0.017972 | Acc: 0.2866
[2025-11-12 10:13:33] 09:27:06<04:12:08, 15.91s/it | [Iter 2139/3090] R0[2048/3000] | LR: 0.006857 | E: -63.772650 | E_img: -0.0155j E_var:     2.8824 E_err:   0.018758 | Acc: 0.2779
[2025-11-12 10:13:48] 09:27:21<04:11:52, 15.91s/it | [Iter 2140/3090] R0[2049/3000] | LR: 0.006844 | E: -63.696802 | E_img: -0.0168j E_var:     2.7550 E_err:   0.018339 | Acc: 0.2807
[2025-11-12 10:14:04] 09:27:37<04:11:36, 15.91s/it | [Iter 2141/3090] R0[2050/3000] | LR: 0.006830 | E: -63.710489 | E_img: -0.0068j E_var:     2.7337 E_err:   0.018268 | Acc: 0.2884
[2025-11-12 10:14:20] 09:27:53<04:11:20, 15.91s/it | [Iter 2142/3090] R0[2051/3000] | LR: 0.006817 | E: -63.746558 | E_img: +0.0162j E_var:     2.7478 E_err:   0.018314 | Acc: 0.2842
[2025-11-12 10:14:36] 09:28:09<04:11:04, 15.91s/it | [Iter 2143/3090] R0[2052/3000] | LR: 0.006804 | E: -63.734120 | E_img: -0.0029j E_var:     2.6826 E_err:   0.018096 | Acc: 0.2871
[2025-11-12 10:14:52] 09:28:25<04:10:48, 15.91s/it | [Iter 2144/3090] R0[2053/3000] | LR: 0.006791 | E: -63.768028 | E_img: -0.0019j E_var:     2.8184 E_err:   0.018548 | Acc: 0.2806
[2025-11-12 10:15:08] 09:28:41<04:10:32, 15.91s/it | [Iter 2145/3090] R0[2054/3000] | LR: 0.006778 | E: -63.798005 | E_img: -0.0022j E_var:     2.7725 E_err:   0.018397 | Acc: 0.2755
[2025-11-12 10:15:23] 09:28:56<04:10:16, 15.91s/it | [Iter 2146/3090] R0[2055/3000] | LR: 0.006765 | E: -63.786743 | E_img: -0.0007j E_var:     2.8715 E_err:   0.018722 | Acc: 0.2691
[2025-11-12 10:15:39] 09:29:12<04:10:00, 15.91s/it | [Iter 2147/3090] R0[2056/3000] | LR: 0.006752 | E: -63.757961 | E_img: -0.0040j E_var:     2.9147 E_err:   0.018863 | Acc: 0.2656
[2025-11-12 10:15:55] 09:29:28<04:09:44, 15.91s/it | [Iter 2148/3090] R0[2057/3000] | LR: 0.006738 | E: -63.748984 | E_img: +0.0003j E_var:     2.5803 E_err:   0.017748 | Acc: 0.2779
[2025-11-12 10:16:11] 09:29:44<04:09:28, 15.91s/it | [Iter 2149/3090] R0[2058/3000] | LR: 0.006725 | E: -63.725055 | E_img: +0.0021j E_var:     2.6547 E_err:   0.018002 | Acc: 0.2882
[2025-11-12 10:16:27] 09:30:00<04:09:13, 15.91s/it | [Iter 2150/3090] R0[2059/3000] | LR: 0.006712 | E: -63.700628 | E_img: +0.0127j E_var:     2.6467 E_err:   0.017975 | Acc: 0.2917
[2025-11-12 10:16:43] 09:30:16<04:08:57, 15.91s/it | [Iter 2151/3090] R0[2060/3000] | LR: 0.006699 | E: -63.735795 | E_img: +0.0071j E_var:     2.8688 E_err:   0.018714 | Acc: 0.2816
[2025-11-12 10:16:58] 09:30:32<04:08:41, 15.91s/it | [Iter 2152/3090] R0[2061/3000] | LR: 0.006686 | E: -63.734815 | E_img: +0.0008j E_var:     2.8592 E_err:   0.018682 | Acc: 0.2757
[2025-11-12 10:17:14] 09:30:47<04:08:25, 15.91s/it | [Iter 2153/3090] R0[2062/3000] | LR: 0.006673 | E: -63.804803 | E_img: +0.0059j E_var:     2.5876 E_err:   0.017773 | Acc: 0.2757
[2025-11-12 10:17:30] 09:31:03<04:08:09, 15.91s/it | [Iter 2154/3090] R0[2063/3000] | LR: 0.006660 | E: -63.746280 | E_img: +0.0088j E_var:     2.8425 E_err:   0.018627 | Acc: 0.2756
[2025-11-12 10:17:46] 09:31:19<04:07:53, 15.91s/it | [Iter 2155/3090] R0[2064/3000] | LR: 0.006647 | E: -63.741336 | E_img: -0.0055j E_var:     2.7817 E_err:   0.018427 | Acc: 0.2695
[2025-11-12 10:18:02] 09:31:35<04:07:37, 15.91s/it | [Iter 2156/3090] R0[2065/3000] | LR: 0.006634 | E: -63.769062 | E_img: +0.0119j E_var:     2.6092 E_err:   0.017847 | Acc: 0.2788
[2025-11-12 10:18:18] 09:31:51<04:07:21, 15.91s/it | [Iter 2157/3090] R0[2066/3000] | LR: 0.006621 | E: -63.715146 | E_img: -0.0072j E_var:     2.8729 E_err:   0.018727 | Acc: 0.2742
[2025-11-12 10:18:33] 09:32:07<04:07:05, 15.91s/it | [Iter 2158/3090] R0[2067/3000] | LR: 0.006608 | E: -63.676494 | E_img: -0.0031j E_var:     2.6714 E_err:   0.018058 | Acc: 0.2886
[2025-11-12 10:18:49] 09:32:22<04:06:49, 15.91s/it | [Iter 2159/3090] R0[2068/3000] | LR: 0.006595 | E: -63.699402 | E_img: +0.0059j E_var:     2.6740 E_err:   0.018067 | Acc: 0.2905
[2025-11-12 10:19:05] 09:32:38<04:06:33, 15.91s/it | [Iter 2160/3090] R0[2069/3000] | LR: 0.006582 | E: -63.698124 | E_img: -0.0120j E_var:     2.8976 E_err:   0.018807 | Acc: 0.2862
[2025-11-12 10:19:21] 09:32:54<04:06:17, 15.91s/it | [Iter 2161/3090] R0[2070/3000] | LR: 0.006569 | E: -63.719979 | E_img: +0.0005j E_var:     2.7953 E_err:   0.018472 | Acc: 0.2790
[2025-11-12 10:19:37] 09:33:10<04:06:01, 15.91s/it | [Iter 2162/3090] R0[2071/3000] | LR: 0.006556 | E: -63.752808 | E_img: -0.0025j E_var:     3.0529 E_err:   0.019305 | Acc: 0.2663
[2025-11-12 10:19:53] 09:33:26<04:05:45, 15.91s/it | [Iter 2163/3090] R0[2072/3000] | LR: 0.006543 | E: -63.744064 | E_img: -0.0077j E_var:     2.8299 E_err:   0.018586 | Acc: 0.2638
[2025-11-12 10:20:09] 09:33:42<04:05:29, 15.91s/it | [Iter 2164/3090] R0[2073/3000] | LR: 0.006530 | E: -63.782517 | E_img: +0.0009j E_var:     2.6605 E_err:   0.018021 | Acc: 0.2712
[2025-11-12 10:20:24] 09:33:57<04:05:13, 15.91s/it | [Iter 2165/3090] R0[2074/3000] | LR: 0.006517 | E: -63.757509 | E_img: +0.0182j E_var:     2.7341 E_err:   0.018269 | Acc: 0.2745
[2025-11-12 10:20:40] 09:34:13<04:04:58, 15.91s/it | [Iter 2166/3090] R0[2075/3000] | LR: 0.006504 | E: -63.778706 | E_img: +0.0261j E_var:     2.9608 E_err:   0.019011 | Acc: 0.2658
[2025-11-12 10:20:56] 09:34:29<04:04:42, 15.91s/it | [Iter 2167/3090] R0[2076/3000] | LR: 0.006491 | E: -63.772738 | E_img: +0.0071j E_var:     2.9093 E_err:   0.018845 | Acc: 0.2622
[2025-11-12 10:21:12] 09:34:45<04:04:26, 15.91s/it | [Iter 2168/3090] R0[2077/3000] | LR: 0.006478 | E: -63.775773 | E_img: -0.0101j E_var:     2.6671 E_err:   0.018044 | Acc: 0.2670
[2025-11-12 10:21:28] 09:35:01<04:04:10, 15.91s/it | [Iter 2169/3090] R0[2078/3000] | LR: 0.006465 | E: -63.760812 | E_img: -0.0018j E_var:     2.5675 E_err:   0.017704 | Acc: 0.2826
[2025-11-12 10:21:44] 09:35:17<04:03:54, 15.91s/it | [Iter 2170/3090] R0[2079/3000] | LR: 0.006452 | E: -63.705397 | E_img: +0.0055j E_var:     2.6299 E_err:   0.017917 | Acc: 0.2916
[2025-11-12 10:21:59] 09:35:32<04:03:38, 15.91s/it | [Iter 2171/3090] R0[2080/3000] | LR: 0.006439 | E: -63.754267 | E_img: -0.0116j E_var:     2.6408 E_err:   0.017955 | Acc: 0.2865
[2025-11-12 10:22:15] 09:35:48<04:03:22, 15.91s/it | [Iter 2172/3090] R0[2081/3000] | LR: 0.006426 | E: -63.730016 | E_img: -0.0004j E_var:     2.5573 E_err:   0.017668 | Acc: 0.2908
[2025-11-12 10:22:31] 09:36:04<04:03:06, 15.91s/it | [Iter 2173/3090] R0[2082/3000] | LR: 0.006414 | E: -63.619604 | E_img: -0.0126j E_var:     2.6762 E_err:   0.018075 | Acc: 0.3021
[2025-11-12 10:22:47] 09:36:20<04:02:50, 15.91s/it | [Iter 2174/3090] R0[2083/3000] | LR: 0.006401 | E: -63.708121 | E_img: +0.0192j E_var:     2.7301 E_err:   0.018255 | Acc: 0.2933
[2025-11-12 10:23:03] 09:36:36<04:02:34, 15.91s/it | [Iter 2175/3090] R0[2084/3000] | LR: 0.006388 | E: -63.713383 | E_img: -0.0056j E_var:     2.6717 E_err:   0.018059 | Acc: 0.2907
[2025-11-12 10:23:19] 09:36:52<04:02:18, 15.91s/it | [Iter 2176/3090] R0[2085/3000] | LR: 0.006375 | E: -63.753445 | E_img: -0.0061j E_var:     2.7316 E_err:   0.018260 | Acc: 0.2815
[2025-11-12 10:23:34] 09:37:07<04:02:02, 15.91s/it | [Iter 2177/3090] R0[2086/3000] | LR: 0.006362 | E: -63.784111 | E_img: -0.0023j E_var:     2.7917 E_err:   0.018460 | Acc: 0.2736
[2025-11-12 10:23:50] 09:37:23<04:01:46, 15.91s/it | [Iter 2178/3090] R0[2087/3000] | LR: 0.006349 | E: -63.772247 | E_img: +0.0025j E_var:     2.7446 E_err:   0.018304 | Acc: 0.2676
[2025-11-12 10:24:06] 09:37:39<04:01:30, 15.91s/it | [Iter 2179/3090] R0[2088/3000] | LR: 0.006336 | E: -63.758826 | E_img: +0.0005j E_var:     2.9802 E_err:   0.019073 | Acc: 0.2625
[2025-11-12 10:24:22] 09:37:55<04:01:14, 15.91s/it | [Iter 2180/3090] R0[2089/3000] | LR: 0.006324 | E: -63.810340 | E_img: -0.0068j E_var:     2.7672 E_err:   0.018379 | Acc: 0.2648
[2025-11-12 10:24:38] 09:38:11<04:00:58, 15.91s/it | [Iter 2181/3090] R0[2090/3000] | LR: 0.006311 | E: -63.800174 | E_img: +0.0079j E_var:     2.8231 E_err:   0.018564 | Acc: 0.2671
[2025-11-12 10:24:54] 09:38:27<04:00:43, 15.91s/it | [Iter 2182/3090] R0[2091/3000] | LR: 0.006298 | E: -63.803531 | E_img: +0.0115j E_var:     2.7144 E_err:   0.018203 | Acc: 0.2682
[2025-11-12 10:25:09] 09:38:43<04:00:27, 15.91s/it | [Iter 2183/3090] R0[2092/3000] | LR: 0.006285 | E: -63.835132 | E_img: -0.0199j E_var:     2.7343 E_err:   0.018269 | Acc: 0.2669
[2025-11-12 10:25:25] 09:38:58<04:00:11, 15.91s/it | [Iter 2184/3090] R0[2093/3000] | LR: 0.006272 | E: -63.812792 | E_img: -0.0010j E_var:     2.9584 E_err:   0.019004 | Acc: 0.2603
[2025-11-12 10:25:41] 09:39:14<03:59:55, 15.91s/it | [Iter 2185/3090] R0[2094/3000] | LR: 0.006260 | E: -63.814328 | E_img: -0.0054j E_var:     2.8645 E_err:   0.018699 | Acc: 0.2578
[2025-11-12 10:25:57] 09:39:30<03:59:39, 15.91s/it | [Iter 2186/3090] R0[2095/3000] | LR: 0.006247 | E: -63.714710 | E_img: -0.0026j E_var:     3.5814 E_err:   0.020909 | Acc: 0.2432
[2025-11-12 10:26:13] 09:39:46<03:59:23, 15.91s/it | [Iter 2187/3090] R0[2096/3000] | LR: 0.006234 | E: -63.762851 | E_img: +0.0019j E_var:     3.3016 E_err:   0.020076 | Acc: 0.2430
[2025-11-12 10:26:29] 09:40:02<03:59:07, 15.91s/it | [Iter 2188/3090] R0[2097/3000] | LR: 0.006221 | E: -63.745359 | E_img: +0.0060j E_var:     3.2543 E_err:   0.019931 | Acc: 0.2456
[2025-11-12 10:26:44] 09:40:18<03:58:51, 15.91s/it | [Iter 2189/3090] R0[2098/3000] | LR: 0.006209 | E: -63.759381 | E_img: +0.0010j E_var:     3.1304 E_err:   0.019548 | Acc: 0.2456
[2025-11-12 10:27:00] 09:40:33<03:58:35, 15.91s/it | [Iter 2190/3090] R0[2099/3000] | LR: 0.006196 | E: -63.806827 | E_img: +0.0019j E_var:     2.8115 E_err:   0.018526 | Acc: 0.2539
[2025-11-12 10:27:00] ✓ Checkpoint saved: checkpoint_iter_002100.pkl
[2025-11-12 10:27:16] 09:40:49<03:58:19, 15.91s/it | [Iter 2191/3090] R0[2100/3000] | LR: 0.006183 | E: -63.791213 | E_img: -0.0211j E_var:     2.9030 E_err:   0.018825 | Acc: 0.2547
[2025-11-12 10:27:32] 09:41:05<03:58:03, 15.91s/it | [Iter 2192/3090] R0[2101/3000] | LR: 0.006171 | E: -63.818894 | E_img: +0.0045j E_var:     2.7226 E_err:   0.018231 | Acc: 0.2619
[2025-11-12 10:27:48] 09:41:21<03:57:47, 15.91s/it | [Iter 2193/3090] R0[2102/3000] | LR: 0.006158 | E: -63.813087 | E_img: +0.0101j E_var:     2.7996 E_err:   0.018487 | Acc: 0.2637
[2025-11-12 10:28:04] 09:41:37<03:57:31, 15.91s/it | [Iter 2194/3090] R0[2103/3000] | LR: 0.006145 | E: -63.809949 | E_img: -0.0051j E_var:     2.6742 E_err:   0.018068 | Acc: 0.2706
[2025-11-12 10:28:20] 09:41:53<03:57:16, 15.91s/it | [Iter 2195/3090] R0[2104/3000] | LR: 0.006133 | E: -63.761588 | E_img: +0.0117j E_var:     2.6559 E_err:   0.018006 | Acc: 0.2776
[2025-11-12 10:28:36] 09:42:09<03:57:00, 15.91s/it | [Iter 2196/3090] R0[2105/3000] | LR: 0.006120 | E: -63.701233 | E_img: +0.0105j E_var:     2.7237 E_err:   0.018234 | Acc: 0.2888
[2025-11-12 10:28:51] 09:42:24<03:56:44, 15.91s/it | [Iter 2197/3090] R0[2106/3000] | LR: 0.006107 | E: -63.653685 | E_img: -0.0044j E_var:     2.7603 E_err:   0.018356 | Acc: 0.2948
[2025-11-12 10:29:07] 09:42:40<03:56:28, 15.91s/it | [Iter 2198/3090] R0[2107/3000] | LR: 0.006095 | E: -63.546880 | E_img: -0.0042j E_var:     2.9584 E_err:   0.019004 | Acc: 0.2985
[2025-11-12 10:29:23] 09:42:56<03:56:12, 15.91s/it | [Iter 2199/3090] R0[2108/3000] | LR: 0.006082 | E: -63.472722 | E_img: +0.0092j E_var:     2.9652 E_err:   0.019025 | Acc: 0.3132
[2025-11-12 10:29:39] 09:43:12<03:55:56, 15.91s/it | [Iter 2200/3090] R0[2109/3000] | LR: 0.006069 | E: -63.477389 | E_img: -0.0104j E_var:     2.9962 E_err:   0.019125 | Acc: 0.3168
[2025-11-12 10:29:55] 09:43:28<03:55:40, 15.91s/it | [Iter 2201/3090] R0[2110/3000] | LR: 0.006057 | E: -63.562671 | E_img: -0.0024j E_var:     2.7717 E_err:   0.018394 | Acc: 0.3106
[2025-11-12 10:30:11] 09:43:44<03:55:24, 15.91s/it | [Iter 2202/3090] R0[2111/3000] | LR: 0.006044 | E: -63.607980 | E_img: -0.0087j E_var:     2.7027 E_err:   0.018164 | Acc: 0.3064
[2025-11-12 10:30:26] 09:44:00<03:55:08, 15.91s/it | [Iter 2203/3090] R0[2112/3000] | LR: 0.006032 | E: -63.691011 | E_img: -0.0100j E_var:     2.6483 E_err:   0.017980 | Acc: 0.2936
[2025-11-12 10:30:42] 09:44:15<03:54:52, 15.91s/it | [Iter 2204/3090] R0[2113/3000] | LR: 0.006019 | E: -63.716652 | E_img: -0.0054j E_var:     2.6952 E_err:   0.018138 | Acc: 0.2831
[2025-11-12 10:30:58] 09:44:31<03:54:36, 15.91s/it | [Iter 2205/3090] R0[2114/3000] | LR: 0.006006 | E: -63.679812 | E_img: -0.0103j E_var:     2.5942 E_err:   0.017795 | Acc: 0.2893
[2025-11-12 10:31:14] 09:44:47<03:54:20, 15.91s/it | [Iter 2206/3090] R0[2115/3000] | LR: 0.005994 | E: -63.600906 | E_img: +0.0092j E_var:     2.6718 E_err:   0.018059 | Acc: 0.2960
[2025-11-12 10:31:30] 09:45:03<03:54:04, 15.91s/it | [Iter 2207/3090] R0[2116/3000] | LR: 0.005981 | E: -63.547107 | E_img: -0.0008j E_var:     2.7441 E_err:   0.018302 | Acc: 0.3003
[2025-11-12 10:31:46] 09:45:19<03:53:48, 15.91s/it | [Iter 2208/3090] R0[2117/3000] | LR: 0.005969 | E: -63.654752 | E_img: +0.0077j E_var:     2.7545 E_err:   0.018337 | Acc: 0.2901
[2025-11-12 10:32:01] 09:45:35<03:53:32, 15.91s/it | [Iter 2209/3090] R0[2118/3000] | LR: 0.005956 | E: -63.673394 | E_img: +0.0048j E_var:     2.8502 E_err:   0.018653 | Acc: 0.2782
[2025-11-12 10:32:17] 09:45:50<03:53:17, 15.91s/it | [Iter 2210/3090] R0[2119/3000] | LR: 0.005944 | E: -63.667207 | E_img: -0.0066j E_var:     2.6494 E_err:   0.017984 | Acc: 0.2823
[2025-11-12 10:32:33] 09:46:06<03:53:01, 15.91s/it | [Iter 2211/3090] R0[2120/3000] | LR: 0.005931 | E: -63.580145 | E_img: +0.0255j E_var:     2.7737 E_err:   0.018401 | Acc: 0.2900
[2025-11-12 10:32:49] 09:46:22<03:52:45, 15.91s/it | [Iter 2212/3090] R0[2121/3000] | LR: 0.005919 | E: -63.560369 | E_img: -0.0034j E_var:     2.8331 E_err:   0.018597 | Acc: 0.2914
[2025-11-12 10:33:05] 09:46:38<03:52:29, 15.91s/it | [Iter 2213/3090] R0[2122/3000] | LR: 0.005906 | E: -63.545180 | E_img: +0.0103j E_var:     2.7810 E_err:   0.018425 | Acc: 0.2925
[2025-11-12 10:33:21] 09:46:54<03:52:13, 15.91s/it | [Iter 2214/3090] R0[2123/3000] | LR: 0.005894 | E: -63.622257 | E_img: -0.0043j E_var:     2.7224 E_err:   0.018230 | Acc: 0.2886
[2025-11-12 10:33:36] 09:47:10<03:51:57, 15.90s/it | [Iter 2215/3090] R0[2124/3000] | LR: 0.005881 | E: -63.674133 | E_img: +0.0038j E_var:     2.7521 E_err:   0.018329 | Acc: 0.2864
[2025-11-12 10:33:52] 09:47:25<03:51:41, 15.90s/it | [Iter 2216/3090] R0[2125/3000] | LR: 0.005869 | E: -63.666800 | E_img: -0.0112j E_var:     2.6793 E_err:   0.018085 | Acc: 0.2831
[2025-11-12 10:34:08] 09:47:41<03:51:25, 15.90s/it | [Iter 2217/3090] R0[2126/3000] | LR: 0.005856 | E: -63.619734 | E_img: -0.0236j E_var:     2.7853 E_err:   0.018439 | Acc: 0.2789
[2025-11-12 10:34:24] 09:47:57<03:51:09, 15.90s/it | [Iter 2218/3090] R0[2127/3000] | LR: 0.005844 | E: -63.655523 | E_img: +0.0156j E_var:     2.9515 E_err:   0.018981 | Acc: 0.2765
[2025-11-12 10:34:40] 09:48:13<03:50:53, 15.90s/it | [Iter 2219/3090] R0[2128/3000] | LR: 0.005831 | E: -63.655203 | E_img: -0.0172j E_var:     2.7786 E_err:   0.018417 | Acc: 0.2758
[2025-11-12 10:34:56] 09:48:29<03:50:37, 15.90s/it | [Iter 2220/3090] R0[2129/3000] | LR: 0.005819 | E: -63.669606 | E_img: -0.0045j E_var:     2.8916 E_err:   0.018788 | Acc: 0.2750
[2025-11-12 10:35:11] 09:48:45<03:50:21, 15.90s/it | [Iter 2221/3090] R0[2130/3000] | LR: 0.005806 | E: -63.697463 | E_img: -0.0002j E_var:     2.7204 E_err:   0.018223 | Acc: 0.2733
[2025-11-12 10:35:27] 09:49:00<03:50:05, 15.90s/it | [Iter 2222/3090] R0[2131/3000] | LR: 0.005794 | E: -63.701623 | E_img: +0.0098j E_var:     2.7781 E_err:   0.018415 | Acc: 0.2785
[2025-11-12 10:35:43] 09:49:16<03:49:49, 15.90s/it | [Iter 2223/3090] R0[2132/3000] | LR: 0.005782 | E: -63.746250 | E_img: +0.0034j E_var:     2.7141 E_err:   0.018202 | Acc: 0.2811
[2025-11-12 10:35:59] 09:49:32<03:49:33, 15.90s/it | [Iter 2224/3090] R0[2133/3000] | LR: 0.005769 | E: -63.754169 | E_img: -0.0012j E_var:     2.8013 E_err:   0.018492 | Acc: 0.2778
[2025-11-12 10:36:15] 09:49:48<03:49:18, 15.90s/it | [Iter 2225/3090] R0[2134/3000] | LR: 0.005757 | E: -63.747134 | E_img: -0.0077j E_var:     2.6980 E_err:   0.018148 | Acc: 0.2724
[2025-11-12 10:36:31] 09:50:04<03:49:02, 15.90s/it | [Iter 2226/3090] R0[2135/3000] | LR: 0.005745 | E: -63.756562 | E_img: +0.0114j E_var:     2.8682 E_err:   0.018711 | Acc: 0.2679
[2025-11-12 10:36:46] 09:50:20<03:48:46, 15.90s/it | [Iter 2227/3090] R0[2136/3000] | LR: 0.005732 | E: -63.756324 | E_img: -0.0035j E_var:     2.6969 E_err:   0.018144 | Acc: 0.2682
[2025-11-12 10:37:02] 09:50:35<03:48:30, 15.90s/it | [Iter 2228/3090] R0[2137/3000] | LR: 0.005720 | E: -63.746549 | E_img: -0.0157j E_var:     2.7622 E_err:   0.018363 | Acc: 0.2726
[2025-11-12 10:37:18] 09:50:51<03:48:14, 15.90s/it | [Iter 2229/3090] R0[2138/3000] | LR: 0.005708 | E: -63.717347 | E_img: -0.0030j E_var:     2.6948 E_err:   0.018137 | Acc: 0.2926
[2025-11-12 10:37:34] 09:51:07<03:47:58, 15.90s/it | [Iter 2230/3090] R0[2139/3000] | LR: 0.005695 | E: -63.677560 | E_img: -0.0101j E_var:     2.6287 E_err:   0.017913 | Acc: 0.2978
[2025-11-12 10:37:50] 09:51:23<03:47:42, 15.90s/it | [Iter 2231/3090] R0[2140/3000] | LR: 0.005683 | E: -63.705539 | E_img: -0.0141j E_var:     2.5986 E_err:   0.017810 | Acc: 0.2998
[2025-11-12 10:38:06] 09:51:39<03:47:26, 15.90s/it | [Iter 2232/3090] R0[2141/3000] | LR: 0.005671 | E: -63.732678 | E_img: +0.0097j E_var:     2.6543 E_err:   0.018000 | Acc: 0.2943
[2025-11-12 10:38:21] 09:51:55<03:47:10, 15.90s/it | [Iter 2233/3090] R0[2142/3000] | LR: 0.005658 | E: -63.681724 | E_img: +0.0007j E_var:     2.5636 E_err:   0.017690 | Acc: 0.2989
[2025-11-12 10:38:37] 09:52:10<03:46:54, 15.90s/it | [Iter 2234/3090] R0[2143/3000] | LR: 0.005646 | E: -63.700572 | E_img: -0.0114j E_var:     2.5375 E_err:   0.017600 | Acc: 0.2975
[2025-11-12 10:38:53] 09:52:26<03:46:38, 15.90s/it | [Iter 2235/3090] R0[2144/3000] | LR: 0.005634 | E: -63.731291 | E_img: +0.0003j E_var:     2.6626 E_err:   0.018028 | Acc: 0.2896
[2025-11-12 10:39:09] 09:52:42<03:46:22, 15.90s/it | [Iter 2236/3090] R0[2145/3000] | LR: 0.005621 | E: -63.757745 | E_img: -0.0006j E_var:     2.7929 E_err:   0.018464 | Acc: 0.2834
[2025-11-12 10:39:25] 09:52:58<03:46:06, 15.90s/it | [Iter 2237/3090] R0[2146/3000] | LR: 0.005609 | E: -63.708377 | E_img: -0.0079j E_var:     2.4738 E_err:   0.017377 | Acc: 0.2949
[2025-11-12 10:39:41] 09:53:14<03:45:50, 15.90s/it | [Iter 2238/3090] R0[2147/3000] | LR: 0.005597 | E: -63.788005 | E_img: +0.0097j E_var:     3.1509 E_err:   0.019612 | Acc: 0.2783
[2025-11-12 10:39:56] 09:53:30<03:45:35, 15.90s/it | [Iter 2239/3090] R0[2148/3000] | LR: 0.005585 | E: -63.775920 | E_img: -0.0066j E_var:     2.9189 E_err:   0.018876 | Acc: 0.2717
[2025-11-12 10:40:12] 09:53:45<03:45:19, 15.90s/it | [Iter 2240/3090] R0[2149/3000] | LR: 0.005572 | E: -63.780494 | E_img: +0.0040j E_var:     2.7329 E_err:   0.018265 | Acc: 0.2754
[2025-11-12 10:40:28] 09:54:01<03:45:03, 15.90s/it | [Iter 2241/3090] R0[2150/3000] | LR: 0.005560 | E: -63.783985 | E_img: -0.0069j E_var:     2.6105 E_err:   0.017851 | Acc: 0.2825
[2025-11-12 10:40:44] 09:54:17<03:44:47, 15.90s/it | [Iter 2242/3090] R0[2151/3000] | LR: 0.005548 | E: -63.761639 | E_img: +0.0092j E_var:     2.6056 E_err:   0.017834 | Acc: 0.2878
[2025-11-12 10:41:00] 09:54:33<03:44:31, 15.90s/it | [Iter 2243/3090] R0[2152/3000] | LR: 0.005536 | E: -63.692293 | E_img: -0.0113j E_var:     2.5749 E_err:   0.017729 | Acc: 0.3031
[2025-11-12 10:41:16] 09:54:49<03:44:15, 15.90s/it | [Iter 2244/3090] R0[2153/3000] | LR: 0.005524 | E: -63.668216 | E_img: -0.0261j E_var:     2.5923 E_err:   0.017789 | Acc: 0.3028
[2025-11-12 10:41:31] 09:55:05<03:43:59, 15.90s/it | [Iter 2245/3090] R0[2154/3000] | LR: 0.005512 | E: -63.731931 | E_img: +0.0082j E_var:     2.5765 E_err:   0.017734 | Acc: 0.2971
[2025-11-12 10:41:47] 09:55:20<03:43:43, 15.90s/it | [Iter 2246/3090] R0[2155/3000] | LR: 0.005499 | E: -63.786543 | E_img: +0.0066j E_var:     2.5564 E_err:   0.017665 | Acc: 0.2891
[2025-11-12 10:42:03] 09:55:36<03:43:27, 15.90s/it | [Iter 2247/3090] R0[2156/3000] | LR: 0.005487 | E: -63.736982 | E_img: +0.0050j E_var:     2.5426 E_err:   0.017617 | Acc: 0.2949
[2025-11-12 10:42:19] 09:55:52<03:43:11, 15.90s/it | [Iter 2248/3090] R0[2157/3000] | LR: 0.005475 | E: -63.647928 | E_img: -0.0191j E_var:     2.7650 E_err:   0.018372 | Acc: 0.3045
[2025-11-12 10:42:35] 09:56:08<03:42:55, 15.90s/it | [Iter 2249/3090] R0[2158/3000] | LR: 0.005463 | E: -63.715067 | E_img: -0.0096j E_var:     2.6658 E_err:   0.018039 | Acc: 0.3005
[2025-11-12 10:42:51] 09:56:24<03:42:39, 15.90s/it | [Iter 2250/3090] R0[2159/3000] | LR: 0.005451 | E: -63.773701 | E_img: -0.0070j E_var:     2.7658 E_err:   0.018375 | Acc: 0.2830
[2025-11-12 10:43:07] 09:56:40<03:42:23, 15.90s/it | [Iter 2251/3090] R0[2160/3000] | LR: 0.005439 | E: -63.784708 | E_img: -0.0044j E_var:     2.8583 E_err:   0.018679 | Acc: 0.2710
[2025-11-12 10:43:22] 09:56:56<03:42:07, 15.90s/it | [Iter 2252/3090] R0[2161/3000] | LR: 0.005427 | E: -63.803111 | E_img: +0.0117j E_var:     2.5506 E_err:   0.017645 | Acc: 0.2723
[2025-11-12 10:43:38] 09:57:11<03:41:52, 15.90s/it | [Iter 2253/3090] R0[2162/3000] | LR: 0.005415 | E: -63.811229 | E_img: +0.0091j E_var:     2.8961 E_err:   0.018802 | Acc: 0.2643
[2025-11-12 10:43:54] 09:57:27<03:41:36, 15.90s/it | [Iter 2254/3090] R0[2163/3000] | LR: 0.005402 | E: -63.817055 | E_img: -0.0081j E_var:     2.9638 E_err:   0.019021 | Acc: 0.2619
[2025-11-12 10:44:10] 09:57:43<03:41:20, 15.90s/it | [Iter 2255/3090] R0[2164/3000] | LR: 0.005390 | E: -63.796850 | E_img: +0.0137j E_var:     2.8358 E_err:   0.018605 | Acc: 0.2577
[2025-11-12 10:44:26] 09:57:59<03:41:04, 15.90s/it | [Iter 2256/3090] R0[2165/3000] | LR: 0.005378 | E: -63.781811 | E_img: +0.0041j E_var:     3.0868 E_err:   0.019412 | Acc: 0.2537
[2025-11-12 10:44:42] 09:58:15<03:40:48, 15.90s/it | [Iter 2257/3090] R0[2166/3000] | LR: 0.005366 | E: -63.796890 | E_img: +0.0053j E_var:     2.7019 E_err:   0.018161 | Acc: 0.2637
[2025-11-12 10:44:57] 09:58:31<03:40:32, 15.90s/it | [Iter 2258/3090] R0[2167/3000] | LR: 0.005354 | E: -63.803812 | E_img: +0.0044j E_var:     2.8195 E_err:   0.018552 | Acc: 0.2680
[2025-11-12 10:45:13] 09:58:46<03:40:16, 15.90s/it | [Iter 2259/3090] R0[2168/3000] | LR: 0.005342 | E: -63.805939 | E_img: -0.0128j E_var:     2.7053 E_err:   0.018172 | Acc: 0.2741
[2025-11-12 10:45:29] 09:59:02<03:40:00, 15.90s/it | [Iter 2260/3090] R0[2169/3000] | LR: 0.005330 | E: -63.767275 | E_img: -0.0022j E_var:     2.5995 E_err:   0.017813 | Acc: 0.2843
[2025-11-12 10:45:45] 09:59:18<03:39:44, 15.90s/it | [Iter 2261/3090] R0[2170/3000] | LR: 0.005318 | E: -63.804801 | E_img: -0.0066j E_var:     2.7959 E_err:   0.018474 | Acc: 0.2753
[2025-11-12 10:46:01] 09:59:34<03:39:28, 15.90s/it | [Iter 2262/3090] R0[2171/3000] | LR: 0.005306 | E: -63.800127 | E_img: -0.0035j E_var:     2.8111 E_err:   0.018524 | Acc: 0.2655
[2025-11-12 10:46:17] 09:59:50<03:39:12, 15.90s/it | [Iter 2263/3090] R0[2172/3000] | LR: 0.005294 | E: -63.794444 | E_img: +0.0045j E_var:     2.9098 E_err:   0.018847 | Acc: 0.2598
[2025-11-12 10:46:33] 10:00:06<03:38:56, 15.90s/it | [Iter 2264/3090] R0[2173/3000] | LR: 0.005282 | E: -63.760012 | E_img: -0.0083j E_var:     2.9436 E_err:   0.018956 | Acc: 0.2539
[2025-11-12 10:46:48] 10:00:21<03:38:40, 15.90s/it | [Iter 2265/3090] R0[2174/3000] | LR: 0.005270 | E: -63.811711 | E_img: -0.0008j E_var:     3.0472 E_err:   0.019287 | Acc: 0.2522
[2025-11-12 10:47:04] 10:00:37<03:38:24, 15.90s/it | [Iter 2266/3090] R0[2175/3000] | LR: 0.005258 | E: -63.813609 | E_img: -0.0032j E_var:     3.0650 E_err:   0.019343 | Acc: 0.2533
[2025-11-12 10:47:20] 10:00:53<03:38:09, 15.90s/it | [Iter 2267/3090] R0[2176/3000] | LR: 0.005246 | E: -63.810332 | E_img: -0.0011j E_var:     2.7889 E_err:   0.018451 | Acc: 0.2608
[2025-11-12 10:47:36] 10:01:09<03:37:53, 15.90s/it | [Iter 2268/3090] R0[2177/3000] | LR: 0.005234 | E: -63.773022 | E_img: -0.0066j E_var:     3.3326 E_err:   0.020170 | Acc: 0.2555
[2025-11-12 10:47:52] 10:01:25<03:37:37, 15.90s/it | [Iter 2269/3090] R0[2178/3000] | LR: 0.005223 | E: -63.835846 | E_img: +0.0032j E_var:     2.7683 E_err:   0.018383 | Acc: 0.2640
[2025-11-12 10:48:08] 10:01:41<03:37:21, 15.90s/it | [Iter 2270/3090] R0[2179/3000] | LR: 0.005211 | E: -63.792101 | E_img: +0.0058j E_var:     2.7297 E_err:   0.018254 | Acc: 0.2663
[2025-11-12 10:48:23] 10:01:57<03:37:05, 15.90s/it | [Iter 2271/3090] R0[2180/3000] | LR: 0.005199 | E: -63.819747 | E_img: +0.0157j E_var:     2.9016 E_err:   0.018820 | Acc: 0.2584
[2025-11-12 10:48:39] 10:02:12<03:36:49, 15.90s/it | [Iter 2272/3090] R0[2181/3000] | LR: 0.005187 | E: -63.809529 | E_img: +0.0081j E_var:     2.9602 E_err:   0.019009 | Acc: 0.2537
[2025-11-12 10:48:55] 10:02:28<03:36:33, 15.90s/it | [Iter 2273/3090] R0[2182/3000] | LR: 0.005175 | E: -63.805156 | E_img: +0.0044j E_var:     2.8863 E_err:   0.018771 | Acc: 0.2569
[2025-11-12 10:49:11] 10:02:44<03:36:17, 15.90s/it | [Iter 2274/3090] R0[2183/3000] | LR: 0.005163 | E: -63.795967 | E_img: -0.0075j E_var:     3.1601 E_err:   0.019641 | Acc: 0.2522
[2025-11-12 10:49:27] 10:03:00<03:36:01, 15.90s/it | [Iter 2275/3090] R0[2184/3000] | LR: 0.005151 | E: -63.748932 | E_img: +0.0061j E_var:     3.3406 E_err:   0.020194 | Acc: 0.2485
[2025-11-12 10:49:43] 10:03:16<03:35:45, 15.90s/it | [Iter 2276/3090] R0[2185/3000] | LR: 0.005139 | E: -63.728134 | E_img: -0.0091j E_var:     2.8734 E_err:   0.018728 | Acc: 0.2534
[2025-11-12 10:49:58] 10:03:32<03:35:29, 15.90s/it | [Iter 2277/3090] R0[2186/3000] | LR: 0.005128 | E: -63.717987 | E_img: +0.0162j E_var:     2.8008 E_err:   0.018490 | Acc: 0.2754
[2025-11-12 10:50:14] 10:03:47<03:35:13, 15.90s/it | [Iter 2278/3090] R0[2187/3000] | LR: 0.005116 | E: -63.641573 | E_img: -0.0051j E_var:     2.8137 E_err:   0.018533 | Acc: 0.2814
[2025-11-12 10:50:30] 10:04:03<03:34:57, 15.90s/it | [Iter 2279/3090] R0[2188/3000] | LR: 0.005104 | E: -63.649760 | E_img: +0.0098j E_var:     2.8828 E_err:   0.018759 | Acc: 0.2839
[2025-11-12 10:50:46] 10:04:19<03:34:42, 15.90s/it | [Iter 2280/3090] R0[2189/3000] | LR: 0.005092 | E: -63.732879 | E_img: -0.0008j E_var:     2.7861 E_err:   0.018442 | Acc: 0.2766
[2025-11-12 10:51:02] 10:04:35<03:34:26, 15.90s/it | [Iter 2281/3090] R0[2190/3000] | LR: 0.005080 | E: -63.747178 | E_img: +0.0005j E_var:     2.7469 E_err:   0.018312 | Acc: 0.2750
[2025-11-12 10:51:18] 10:04:51<03:34:10, 15.90s/it | [Iter 2282/3090] R0[2191/3000] | LR: 0.005069 | E: -63.771812 | E_img: +0.0172j E_var:     2.6658 E_err:   0.018039 | Acc: 0.2749
[2025-11-12 10:51:33] 10:05:07<03:33:54, 15.90s/it | [Iter 2283/3090] R0[2192/3000] | LR: 0.005057 | E: -63.754179 | E_img: +0.0009j E_var:     2.7028 E_err:   0.018164 | Acc: 0.2832
[2025-11-12 10:51:49] 10:05:22<03:33:38, 15.90s/it | [Iter 2284/3090] R0[2193/3000] | LR: 0.005045 | E: -63.783217 | E_img: +0.0027j E_var:     2.9862 E_err:   0.019092 | Acc: 0.2728
[2025-11-12 10:52:05] 10:05:38<03:33:22, 15.90s/it | [Iter 2285/3090] R0[2194/3000] | LR: 0.005033 | E: -63.806185 | E_img: -0.0098j E_var:     3.2397 E_err:   0.019886 | Acc: 0.2633
[2025-11-12 10:52:21] 10:05:54<03:33:06, 15.90s/it | [Iter 2286/3090] R0[2195/3000] | LR: 0.005022 | E: -63.750624 | E_img: -0.0193j E_var:     2.8210 E_err:   0.018557 | Acc: 0.2676
[2025-11-12 10:52:37] 10:06:10<03:32:50, 15.90s/it | [Iter 2287/3090] R0[2196/3000] | LR: 0.005010 | E: -63.732680 | E_img: +0.0087j E_var:     2.8645 E_err:   0.018700 | Acc: 0.2771
[2025-11-12 10:52:53] 10:06:26<03:32:34, 15.90s/it | [Iter 2288/3090] R0[2197/3000] | LR: 0.004998 | E: -63.715864 | E_img: +0.0088j E_var:     2.6202 E_err:   0.017884 | Acc: 0.2801
[2025-11-12 10:53:09] 10:06:42<03:32:18, 15.90s/it | [Iter 2289/3090] R0[2198/3000] | LR: 0.004986 | E: -63.773900 | E_img: +0.0055j E_var:     2.7116 E_err:   0.018193 | Acc: 0.2833
[2025-11-12 10:53:24] 10:06:58<03:32:02, 15.90s/it | [Iter 2290/3090] R0[2199/3000] | LR: 0.004975 | E: -63.742540 | E_img: +0.0122j E_var:     2.5826 E_err:   0.017756 | Acc: 0.2858
[2025-11-12 10:53:40] 10:07:13<03:31:46, 15.90s/it | [Iter 2291/3090] R0[2200/3000] | LR: 0.004963 | E: -63.770389 | E_img: +0.0116j E_var:     2.6894 E_err:   0.018119 | Acc: 0.2824
[2025-11-12 10:53:56] 10:07:29<03:31:30, 15.90s/it | [Iter 2292/3090] R0[2201/3000] | LR: 0.004951 | E: -63.692063 | E_img: +0.0172j E_var:     2.7336 E_err:   0.018267 | Acc: 0.2891
[2025-11-12 10:54:12] 10:07:45<03:31:14, 15.90s/it | [Iter 2293/3090] R0[2202/3000] | LR: 0.004940 | E: -63.723546 | E_img: +0.0048j E_var:     2.6380 E_err:   0.017945 | Acc: 0.2960
[2025-11-12 10:54:28] 10:08:01<03:30:59, 15.90s/it | [Iter 2294/3090] R0[2203/3000] | LR: 0.004928 | E: -63.713567 | E_img: -0.0109j E_var:     2.5906 E_err:   0.017783 | Acc: 0.3016
[2025-11-12 10:54:44] 10:08:17<03:30:43, 15.90s/it | [Iter 2295/3090] R0[2204/3000] | LR: 0.004917 | E: -63.767521 | E_img: +0.0136j E_var:     2.6407 E_err:   0.017954 | Acc: 0.2913
[2025-11-12 10:54:59] 10:08:33<03:30:27, 15.90s/it | [Iter 2296/3090] R0[2205/3000] | LR: 0.004905 | E: -63.765253 | E_img: +0.0097j E_var:     2.8253 E_err:   0.018571 | Acc: 0.2731
[2025-11-12 10:55:15] 10:08:48<03:30:11, 15.90s/it | [Iter 2297/3090] R0[2206/3000] | LR: 0.004893 | E: -63.791748 | E_img: +0.0016j E_var:     2.5980 E_err:   0.017808 | Acc: 0.2799
[2025-11-12 10:55:31] 10:09:04<03:29:55, 15.90s/it | [Iter 2298/3090] R0[2207/3000] | LR: 0.004882 | E: -63.794129 | E_img: -0.0113j E_var:     2.6490 E_err:   0.017982 | Acc: 0.2813
[2025-11-12 10:55:47] 10:09:20<03:29:39, 15.90s/it | [Iter 2299/3090] R0[2208/3000] | LR: 0.004870 | E: -63.750277 | E_img: +0.0034j E_var:     2.6237 E_err:   0.017896 | Acc: 0.2867
[2025-11-12 10:56:03] 10:09:36<03:29:23, 15.90s/it | [Iter 2300/3090] R0[2209/3000] | LR: 0.004859 | E: -63.772664 | E_img: -0.0004j E_var:     2.5640 E_err:   0.017692 | Acc: 0.2889
[2025-11-12 10:56:19] 10:09:52<03:29:07, 15.90s/it | [Iter 2301/3090] R0[2210/3000] | LR: 0.004847 | E: -63.742736 | E_img: -0.0048j E_var:     2.5900 E_err:   0.017781 | Acc: 0.2904
[2025-11-12 10:56:35] 10:10:08<03:28:51, 15.90s/it | [Iter 2302/3090] R0[2211/3000] | LR: 0.004835 | E: -63.716269 | E_img: -0.0006j E_var:     3.0609 E_err:   0.019330 | Acc: 0.2711
[2025-11-12 10:56:50] 10:10:23<03:28:35, 15.90s/it | [Iter 2303/3090] R0[2212/3000] | LR: 0.004824 | E: -63.760776 | E_img: -0.0052j E_var:     2.7782 E_err:   0.018416 | Acc: 0.2720
[2025-11-12 10:57:06] 10:10:39<03:28:19, 15.90s/it | [Iter 2304/3090] R0[2213/3000] | LR: 0.004812 | E: -63.795820 | E_img: +0.0169j E_var:     2.7323 E_err:   0.018263 | Acc: 0.2664
[2025-11-12 10:57:22] 10:10:55<03:28:03, 15.90s/it | [Iter 2305/3090] R0[2214/3000] | LR: 0.004801 | E: -63.793424 | E_img: -0.0048j E_var:     2.7969 E_err:   0.018477 | Acc: 0.2644
[2025-11-12 10:57:38] 10:11:11<03:27:47, 15.90s/it | [Iter 2306/3090] R0[2215/3000] | LR: 0.004789 | E: -63.772392 | E_img: +0.0082j E_var:     2.7343 E_err:   0.018270 | Acc: 0.2683
[2025-11-12 10:57:54] 10:11:27<03:27:32, 15.90s/it | [Iter 2307/3090] R0[2216/3000] | LR: 0.004778 | E: -63.798278 | E_img: +0.0017j E_var:     2.4813 E_err:   0.017404 | Acc: 0.2812
[2025-11-12 10:58:10] 10:11:43<03:27:16, 15.90s/it | [Iter 2308/3090] R0[2217/3000] | LR: 0.004766 | E: -63.801355 | E_img: -0.0037j E_var:     2.7191 E_err:   0.018219 | Acc: 0.2784
[2025-11-12 10:58:25] 10:11:59<03:27:00, 15.90s/it | [Iter 2309/3090] R0[2218/3000] | LR: 0.004755 | E: -63.808447 | E_img: +0.0028j E_var:     2.7383 E_err:   0.018283 | Acc: 0.2676
[2025-11-12 10:58:41] 10:12:14<03:26:44, 15.90s/it | [Iter 2310/3090] R0[2219/3000] | LR: 0.004743 | E: -63.786169 | E_img: -0.0033j E_var:     3.1359 E_err:   0.019565 | Acc: 0.2546
[2025-11-12 10:58:57] 10:12:30<03:26:28, 15.90s/it | [Iter 2311/3090] R0[2220/3000] | LR: 0.004732 | E: -63.720645 | E_img: +0.0155j E_var:     3.2454 E_err:   0.019904 | Acc: 0.2431
[2025-11-12 10:59:13] 10:12:46<03:26:12, 15.90s/it | [Iter 2312/3090] R0[2221/3000] | LR: 0.004720 | E: -63.764644 | E_img: -0.0133j E_var:     3.2300 E_err:   0.019857 | Acc: 0.2408
[2025-11-12 10:59:29] 10:13:02<03:25:56, 15.90s/it | [Iter 2313/3090] R0[2222/3000] | LR: 0.004709 | E: -63.712139 | E_img: -0.0029j E_var:     3.0819 E_err:   0.019396 | Acc: 0.2419
[2025-11-12 10:59:45] 10:13:18<03:25:40, 15.90s/it | [Iter 2314/3090] R0[2223/3000] | LR: 0.004698 | E: -63.670426 | E_img: -0.0007j E_var:     3.6629 E_err:   0.021145 | Acc: 0.2299
[2025-11-12 11:00:00] 10:13:34<03:25:24, 15.90s/it | [Iter 2315/3090] R0[2224/3000] | LR: 0.004686 | E: -63.585664 | E_img: +0.0139j E_var:     4.4952 E_err:   0.023425 | Acc: 0.2204
[2025-11-12 11:00:16] 10:13:49<03:25:08, 15.90s/it | [Iter 2316/3090] R0[2225/3000] | LR: 0.004675 | E: -63.770581 | E_img: -0.0007j E_var:     2.9813 E_err:   0.019077 | Acc: 0.2368
[2025-11-12 11:00:32] 10:14:05<03:24:52, 15.90s/it | [Iter 2317/3090] R0[2226/3000] | LR: 0.004663 | E: -63.810054 | E_img: +0.0021j E_var:     3.0079 E_err:   0.019162 | Acc: 0.2486
[2025-11-12 11:00:48] 10:14:21<03:24:36, 15.90s/it | [Iter 2318/3090] R0[2227/3000] | LR: 0.004652 | E: -63.746296 | E_img: +0.0185j E_var:     3.2428 E_err:   0.019896 | Acc: 0.2438
[2025-11-12 11:01:04] 10:14:37<03:24:21, 15.90s/it | [Iter 2319/3090] R0[2228/3000] | LR: 0.004641 | E: -63.711967 | E_img: +0.0023j E_var:     3.1164 E_err:   0.019505 | Acc: 0.2459
[2025-11-12 11:01:20] 10:14:53<03:24:05, 15.90s/it | [Iter 2320/3090] R0[2229/3000] | LR: 0.004629 | E: -63.715140 | E_img: +0.0138j E_var:     3.6397 E_err:   0.021078 | Acc: 0.2372
[2025-11-12 11:01:36] 10:15:09<03:23:49, 15.90s/it | [Iter 2321/3090] R0[2230/3000] | LR: 0.004618 | E: -63.731312 | E_img: +0.0124j E_var:     2.9710 E_err:   0.019044 | Acc: 0.2460
[2025-11-12 11:01:51] 10:15:25<03:23:33, 15.90s/it | [Iter 2322/3090] R0[2231/3000] | LR: 0.004607 | E: -63.743007 | E_img: -0.0027j E_var:     4.0045 E_err:   0.022109 | Acc: 0.2373
[2025-11-12 11:02:07] 10:15:40<03:23:17, 15.90s/it | [Iter 2323/3090] R0[2232/3000] | LR: 0.004595 | E: -63.703501 | E_img: -0.0017j E_var:     3.3719 E_err:   0.020288 | Acc: 0.2345
[2025-11-12 11:02:23] 10:15:56<03:23:01, 15.90s/it | [Iter 2324/3090] R0[2233/3000] | LR: 0.004584 | E: -63.689948 | E_img: +0.0080j E_var:     3.8193 E_err:   0.021592 | Acc: 0.2264
[2025-11-12 11:02:39] 10:16:12<03:22:45, 15.90s/it | [Iter 2325/3090] R0[2234/3000] | LR: 0.004573 | E: -63.748404 | E_img: +0.0030j E_var:     3.1980 E_err:   0.019758 | Acc: 0.2337
[2025-11-12 11:02:55] 10:16:28<03:22:29, 15.90s/it | [Iter 2326/3090] R0[2235/3000] | LR: 0.004561 | E: -63.778065 | E_img: +0.0021j E_var:     2.8842 E_err:   0.018764 | Acc: 0.2483
[2025-11-12 11:03:11] 10:16:44<03:22:13, 15.90s/it | [Iter 2327/3090] R0[2236/3000] | LR: 0.004550 | E: -63.747235 | E_img: +0.0212j E_var:     3.2706 E_err:   0.019981 | Acc: 0.2483
[2025-11-12 11:03:26] 10:17:00<03:21:57, 15.90s/it | [Iter 2328/3090] R0[2237/3000] | LR: 0.004539 | E: -63.777218 | E_img: -0.0174j E_var:     2.9641 E_err:   0.019022 | Acc: 0.2543
[2025-11-12 11:03:42] 10:17:15<03:21:41, 15.90s/it | [Iter 2329/3090] R0[2238/3000] | LR: 0.004528 | E: -63.800441 | E_img: +0.0082j E_var:     3.0788 E_err:   0.019386 | Acc: 0.2593
[2025-11-12 11:03:58] 10:17:31<03:21:25, 15.90s/it | [Iter 2330/3090] R0[2239/3000] | LR: 0.004516 | E: -63.748097 | E_img: -0.0013j E_var:     3.1479 E_err:   0.019603 | Acc: 0.2515
[2025-11-12 11:04:14] 10:17:47<03:21:09, 15.90s/it | [Iter 2331/3090] R0[2240/3000] | LR: 0.004505 | E: -63.782275 | E_img: -0.0040j E_var:     2.9503 E_err:   0.018978 | Acc: 0.2554
[2025-11-12 11:04:30] 10:18:03<03:20:54, 15.90s/it | [Iter 2332/3090] R0[2241/3000] | LR: 0.004494 | E: -63.790464 | E_img: +0.0044j E_var:     2.9932 E_err:   0.019115 | Acc: 0.2566
[2025-11-12 11:04:46] 10:18:19<03:20:38, 15.90s/it | [Iter 2333/3090] R0[2242/3000] | LR: 0.004483 | E: -63.808265 | E_img: +0.0038j E_var:     2.9149 E_err:   0.018863 | Acc: 0.2595
[2025-11-12 11:05:01] 10:18:35<03:20:22, 15.90s/it | [Iter 2334/3090] R0[2243/3000] | LR: 0.004472 | E: -63.799069 | E_img: -0.0104j E_var:     3.0045 E_err:   0.019151 | Acc: 0.2575
[2025-11-12 11:05:17] 10:18:50<03:20:06, 15.90s/it | [Iter 2335/3090] R0[2244/3000] | LR: 0.004460 | E: -63.788626 | E_img: +0.0003j E_var:     2.7324 E_err:   0.018263 | Acc: 0.2783
[2025-11-12 11:05:33] 10:19:06<03:19:50, 15.90s/it | [Iter 2336/3090] R0[2245/3000] | LR: 0.004449 | E: -63.732183 | E_img: +0.0017j E_var:     2.8125 E_err:   0.018529 | Acc: 0.2922
[2025-11-12 11:05:49] 10:19:22<03:19:34, 15.90s/it | [Iter 2337/3090] R0[2246/3000] | LR: 0.004438 | E: -63.685263 | E_img: +0.0023j E_var:     2.7721 E_err:   0.018395 | Acc: 0.3011
[2025-11-12 11:06:05] 10:19:38<03:19:18, 15.90s/it | [Iter 2338/3090] R0[2247/3000] | LR: 0.004427 | E: -63.744898 | E_img: -0.0024j E_var:     2.7281 E_err:   0.018249 | Acc: 0.2963
[2025-11-12 11:06:21] 10:19:54<03:19:02, 15.90s/it | [Iter 2339/3090] R0[2248/3000] | LR: 0.004416 | E: -63.705745 | E_img: -0.0015j E_var:     2.6533 E_err:   0.017997 | Acc: 0.3006
[2025-11-12 11:06:37] 10:20:10<03:18:46, 15.90s/it | [Iter 2340/3090] R0[2249/3000] | LR: 0.004405 | E: -63.790596 | E_img: -0.0211j E_var:     2.7495 E_err:   0.018320 | Acc: 0.2859
[2025-11-12 11:06:52] 10:20:26<03:18:30, 15.90s/it | [Iter 2341/3090] R0[2250/3000] | LR: 0.004393 | E: -63.779014 | E_img: -0.0037j E_var:     2.6255 E_err:   0.017903 | Acc: 0.2806
[2025-11-12 11:07:08] 10:20:41<03:18:14, 15.90s/it | [Iter 2342/3090] R0[2251/3000] | LR: 0.004382 | E: -63.806646 | E_img: -0.0102j E_var:     2.6725 E_err:   0.018062 | Acc: 0.2753
[2025-11-12 11:07:24] 10:20:57<03:17:58, 15.90s/it | [Iter 2343/3090] R0[2252/3000] | LR: 0.004371 | E: -63.788842 | E_img: -0.0073j E_var:     2.8463 E_err:   0.018640 | Acc: 0.2747
[2025-11-12 11:07:40] 10:21:13<03:17:43, 15.90s/it | [Iter 2344/3090] R0[2253/3000] | LR: 0.004360 | E: -63.807254 | E_img: -0.0035j E_var:     2.7095 E_err:   0.018186 | Acc: 0.2729
[2025-11-12 11:07:56] 10:21:29<03:17:27, 15.90s/it | [Iter 2345/3090] R0[2254/3000] | LR: 0.004349 | E: -63.799740 | E_img: +0.0043j E_var:     2.7152 E_err:   0.018206 | Acc: 0.2731
[2025-11-12 11:08:12] 10:21:45<03:17:11, 15.90s/it | [Iter 2346/3090] R0[2255/3000] | LR: 0.004338 | E: -63.751778 | E_img: -0.0201j E_var:     3.0625 E_err:   0.019335 | Acc: 0.2595
[2025-11-12 11:08:27] 10:22:01<03:16:55, 15.90s/it | [Iter 2347/3090] R0[2256/3000] | LR: 0.004327 | E: -63.807318 | E_img: +0.0101j E_var:     2.6204 E_err:   0.017885 | Acc: 0.2615
[2025-11-12 11:08:43] 10:22:16<03:16:39, 15.90s/it | [Iter 2348/3090] R0[2257/3000] | LR: 0.004316 | E: -63.716553 | E_img: +0.0160j E_var:     2.8081 E_err:   0.018514 | Acc: 0.2660
[2025-11-12 11:08:59] 10:22:32<03:16:23, 15.90s/it | [Iter 2349/3090] R0[2258/3000] | LR: 0.004305 | E: -63.676132 | E_img: +0.0169j E_var:     3.3972 E_err:   0.020364 | Acc: 0.2557
[2025-11-12 11:09:15] 10:22:48<03:16:07, 15.90s/it | [Iter 2350/3090] R0[2259/3000] | LR: 0.004294 | E: -63.658014 | E_img: +0.0099j E_var:     3.9289 E_err:   0.021900 | Acc: 0.2354
[2025-11-12 11:09:31] 10:23:04<03:15:51, 15.90s/it | [Iter 2351/3090] R0[2260/3000] | LR: 0.004283 | E: -63.620211 | E_img: +0.0312j E_var:     3.6968 E_err:   0.021243 | Acc: 0.2273
[2025-11-12 11:09:47] 10:23:20<03:15:35, 15.90s/it | [Iter 2352/3090] R0[2261/3000] | LR: 0.004272 | E: -63.620129 | E_img: +0.0158j E_var:     3.7269 E_err:   0.021329 | Acc: 0.2227
[2025-11-12 11:10:03] 10:23:36<03:15:19, 15.90s/it | [Iter 2353/3090] R0[2262/3000] | LR: 0.004261 | E: -63.775090 | E_img: +0.0079j E_var:     2.6954 E_err:   0.018139 | Acc: 0.2454
[2025-11-12 11:10:18] 10:23:51<03:15:03, 15.90s/it | [Iter 2354/3090] R0[2263/3000] | LR: 0.004250 | E: -63.741085 | E_img: -0.0050j E_var:     2.9746 E_err:   0.019055 | Acc: 0.2539
[2025-11-12 11:10:34] 10:24:07<03:14:47, 15.90s/it | [Iter 2355/3090] R0[2264/3000] | LR: 0.004239 | E: -63.749305 | E_img: -0.0058j E_var:     2.8944 E_err:   0.018797 | Acc: 0.2549
[2025-11-12 11:10:50] 10:24:23<03:14:31, 15.90s/it | [Iter 2356/3090] R0[2265/3000] | LR: 0.004228 | E: -63.801083 | E_img: -0.0134j E_var:     2.7227 E_err:   0.018231 | Acc: 0.2616
[2025-11-12 11:11:06] 10:24:39<03:14:16, 15.90s/it | [Iter 2357/3090] R0[2266/3000] | LR: 0.004217 | E: -63.772075 | E_img: -0.0043j E_var:     2.7854 E_err:   0.018440 | Acc: 0.2615
[2025-11-12 11:11:22] 10:24:55<03:14:00, 15.90s/it | [Iter 2358/3090] R0[2267/3000] | LR: 0.004206 | E: -63.772653 | E_img: +0.0109j E_var:     3.0341 E_err:   0.019245 | Acc: 0.2552
[2025-11-12 11:11:38] 10:25:11<03:13:44, 15.90s/it | [Iter 2359/3090] R0[2268/3000] | LR: 0.004195 | E: -63.805850 | E_img: -0.0024j E_var:     2.9383 E_err:   0.018939 | Acc: 0.2577
[2025-11-12 11:11:53] 10:25:26<03:13:28, 15.90s/it | [Iter 2360/3090] R0[2269/3000] | LR: 0.004185 | E: -63.806145 | E_img: +0.0039j E_var:     2.6793 E_err:   0.018085 | Acc: 0.2670
[2025-11-12 11:12:09] 10:25:42<03:13:12, 15.90s/it | [Iter 2361/3090] R0[2270/3000] | LR: 0.004174 | E: -63.809317 | E_img: -0.0027j E_var:     2.9609 E_err:   0.019012 | Acc: 0.2612
[2025-11-12 11:12:25] 10:25:58<03:12:56, 15.90s/it | [Iter 2362/3090] R0[2271/3000] | LR: 0.004163 | E: -63.828435 | E_img: +0.0115j E_var:     2.7898 E_err:   0.018454 | Acc: 0.2580
[2025-11-12 11:12:41] 10:26:14<03:12:40, 15.90s/it | [Iter 2363/3090] R0[2272/3000] | LR: 0.004152 | E: -63.788865 | E_img: -0.0039j E_var:     2.6911 E_err:   0.018125 | Acc: 0.2699
[2025-11-12 11:12:57] 10:26:30<03:12:24, 15.90s/it | [Iter 2364/3090] R0[2273/3000] | LR: 0.004141 | E: -63.800048 | E_img: -0.0039j E_var:     2.7333 E_err:   0.018266 | Acc: 0.2697
[2025-11-12 11:13:13] 10:26:46<03:12:08, 15.90s/it | [Iter 2365/3090] R0[2274/3000] | LR: 0.004130 | E: -63.837684 | E_img: +0.0034j E_var:     2.6155 E_err:   0.017868 | Acc: 0.2711
[2025-11-12 11:13:28] 10:27:02<03:11:52, 15.90s/it | [Iter 2366/3090] R0[2275/3000] | LR: 0.004119 | E: -63.782587 | E_img: +0.0128j E_var:     2.5286 E_err:   0.017569 | Acc: 0.2817
[2025-11-12 11:13:44] 10:27:17<03:11:36, 15.90s/it | [Iter 2367/3090] R0[2276/3000] | LR: 0.004109 | E: -63.774506 | E_img: +0.0027j E_var:     2.6022 E_err:   0.017823 | Acc: 0.2826
[2025-11-12 11:14:00] 10:27:33<03:11:20, 15.90s/it | [Iter 2368/3090] R0[2277/3000] | LR: 0.004098 | E: -63.813392 | E_img: +0.0011j E_var:     2.5641 E_err:   0.017692 | Acc: 0.2815
[2025-11-12 11:14:16] 10:27:49<03:11:04, 15.90s/it | [Iter 2369/3090] R0[2278/3000] | LR: 0.004087 | E: -63.800142 | E_img: +0.0004j E_var:     2.5539 E_err:   0.017657 | Acc: 0.2862
[2025-11-12 11:14:32] 10:28:05<03:10:49, 15.90s/it | [Iter 2370/3090] R0[2279/3000] | LR: 0.004076 | E: -63.821310 | E_img: -0.0091j E_var:     2.7755 E_err:   0.018407 | Acc: 0.2774
[2025-11-12 11:14:48] 10:28:21<03:10:33, 15.90s/it | [Iter 2371/3090] R0[2280/3000] | LR: 0.004066 | E: -63.824685 | E_img: +0.0109j E_var:     2.4302 E_err:   0.017224 | Acc: 0.2766
[2025-11-12 11:15:03] 10:28:37<03:10:17, 15.90s/it | [Iter 2372/3090] R0[2281/3000] | LR: 0.004055 | E: -63.790398 | E_img: -0.0004j E_var:     2.4947 E_err:   0.017451 | Acc: 0.2859
[2025-11-12 11:15:19] 10:28:52<03:10:01, 15.90s/it | [Iter 2373/3090] R0[2282/3000] | LR: 0.004044 | E: -63.763981 | E_img: -0.0055j E_var:     2.7430 E_err:   0.018299 | Acc: 0.2797
[2025-11-12 11:15:35] 10:29:08<03:09:45, 15.90s/it | [Iter 2374/3090] R0[2283/3000] | LR: 0.004033 | E: -63.742685 | E_img: -0.0175j E_var:     2.6292 E_err:   0.017915 | Acc: 0.2827
[2025-11-12 11:15:51] 10:29:24<03:09:29, 15.90s/it | [Iter 2375/3090] R0[2284/3000] | LR: 0.004023 | E: -63.773820 | E_img: +0.0008j E_var:     2.8034 E_err:   0.018499 | Acc: 0.2790
[2025-11-12 11:16:07] 10:29:40<03:09:13, 15.90s/it | [Iter 2376/3090] R0[2285/3000] | LR: 0.004012 | E: -63.792589 | E_img: +0.0011j E_var:     2.6182 E_err:   0.017878 | Acc: 0.2732
[2025-11-12 11:16:23] 10:29:56<03:08:57, 15.90s/it | [Iter 2377/3090] R0[2286/3000] | LR: 0.004001 | E: -63.795077 | E_img: -0.0029j E_var:     2.8243 E_err:   0.018568 | Acc: 0.2647
[2025-11-12 11:16:38] 10:30:12<03:08:41, 15.90s/it | [Iter 2378/3090] R0[2287/3000] | LR: 0.003991 | E: -63.814109 | E_img: +0.0154j E_var:     2.6287 E_err:   0.017913 | Acc: 0.2737
[2025-11-12 11:16:54] 10:30:27<03:08:25, 15.90s/it | [Iter 2379/3090] R0[2288/3000] | LR: 0.003980 | E: -63.771370 | E_img: +0.0093j E_var:     2.7094 E_err:   0.018186 | Acc: 0.2773
[2025-11-12 11:17:10] 10:30:43<03:08:09, 15.90s/it | [Iter 2380/3090] R0[2289/3000] | LR: 0.003969 | E: -63.772620 | E_img: +0.0002j E_var:     2.7269 E_err:   0.018245 | Acc: 0.2767
[2025-11-12 11:17:26] 10:30:59<03:07:53, 15.90s/it | [Iter 2381/3090] R0[2290/3000] | LR: 0.003959 | E: -63.789393 | E_img: +0.0084j E_var:     2.6422 E_err:   0.017959 | Acc: 0.2772
[2025-11-12 11:17:42] 10:31:15<03:07:38, 15.90s/it | [Iter 2382/3090] R0[2291/3000] | LR: 0.003948 | E: -63.720495 | E_img: -0.0030j E_var:     2.7223 E_err:   0.018230 | Acc: 0.2783
[2025-11-12 11:17:58] 10:31:31<03:07:22, 15.90s/it | [Iter 2383/3090] R0[2292/3000] | LR: 0.003937 | E: -63.636495 | E_img: -0.0306j E_var:     2.7706 E_err:   0.018391 | Acc: 0.2926
[2025-11-12 11:18:14] 10:31:47<03:07:06, 15.90s/it | [Iter 2384/3090] R0[2293/3000] | LR: 0.003927 | E: -63.696434 | E_img: +0.0163j E_var:     2.6846 E_err:   0.018103 | Acc: 0.2892
[2025-11-12 11:18:29] 10:32:03<03:06:50, 15.90s/it | [Iter 2385/3090] R0[2294/3000] | LR: 0.003916 | E: -63.669867 | E_img: +0.0139j E_var:     2.6370 E_err:   0.017942 | Acc: 0.2925
[2025-11-12 11:18:45] 10:32:18<03:06:34, 15.90s/it | [Iter 2386/3090] R0[2295/3000] | LR: 0.003906 | E: -63.679959 | E_img: +0.0160j E_var:     2.6642 E_err:   0.018034 | Acc: 0.2903
[2025-11-12 11:19:01] 10:32:34<03:06:18, 15.90s/it | [Iter 2387/3090] R0[2296/3000] | LR: 0.003895 | E: -63.654392 | E_img: +0.0010j E_var:     2.6800 E_err:   0.018087 | Acc: 0.2900
[2025-11-12 11:19:17] 10:32:50<03:06:02, 15.90s/it | [Iter 2388/3090] R0[2297/3000] | LR: 0.003885 | E: -63.711726 | E_img: +0.0174j E_var:     2.8777 E_err:   0.018743 | Acc: 0.2806
[2025-11-12 11:19:33] 10:33:06<03:05:46, 15.90s/it | [Iter 2389/3090] R0[2298/3000] | LR: 0.003874 | E: -63.718823 | E_img: +0.0138j E_var:     2.6272 E_err:   0.017908 | Acc: 0.2900
[2025-11-12 11:19:49] 10:33:22<03:05:30, 15.90s/it | [Iter 2390/3090] R0[2299/3000] | LR: 0.003863 | E: -63.791094 | E_img: -0.0093j E_var:     2.7103 E_err:   0.018189 | Acc: 0.2811
[2025-11-12 11:20:04] 10:33:38<03:05:14, 15.90s/it | [Iter 2391/3090] R0[2300/3000] | LR: 0.003853 | E: -63.784340 | E_img: +0.0217j E_var:     2.8437 E_err:   0.018631 | Acc: 0.2668
[2025-11-12 11:20:20] 10:33:53<03:04:58, 15.90s/it | [Iter 2392/3090] R0[2301/3000] | LR: 0.003842 | E: -63.748053 | E_img: -0.0100j E_var:     2.9925 E_err:   0.019113 | Acc: 0.2590
[2025-11-12 11:20:36] 10:34:09<03:04:42, 15.90s/it | [Iter 2393/3090] R0[2302/3000] | LR: 0.003832 | E: -63.693570 | E_img: +0.0022j E_var:     2.9292 E_err:   0.018909 | Acc: 0.2601
[2025-11-12 11:20:52] 10:34:25<03:04:27, 15.90s/it | [Iter 2394/3090] R0[2303/3000] | LR: 0.003821 | E: -63.646681 | E_img: +0.0189j E_var:     3.2654 E_err:   0.019965 | Acc: 0.2588
[2025-11-12 11:21:08] 10:34:41<03:04:11, 15.90s/it | [Iter 2395/3090] R0[2304/3000] | LR: 0.003811 | E: -63.771093 | E_img: +0.0131j E_var:     3.0809 E_err:   0.019393 | Acc: 0.2534
[2025-11-12 11:21:24] 10:34:57<03:03:55, 15.90s/it | [Iter 2396/3090] R0[2305/3000] | LR: 0.003801 | E: -63.711136 | E_img: +0.0132j E_var:     2.8636 E_err:   0.018696 | Acc: 0.2607
[2025-11-12 11:21:40] 10:35:13<03:03:39, 15.90s/it | [Iter 2397/3090] R0[2306/3000] | LR: 0.003790 | E: -63.759762 | E_img: -0.0112j E_var:     3.0598 E_err:   0.019326 | Acc: 0.2576
[2025-11-12 11:21:55] 10:35:29<03:03:23, 15.90s/it | [Iter 2398/3090] R0[2307/3000] | LR: 0.003780 | E: -63.782517 | E_img: -0.0051j E_var:     2.5838 E_err:   0.017760 | Acc: 0.2702
[2025-11-12 11:22:11] 10:35:44<03:03:07, 15.90s/it | [Iter 2399/3090] R0[2308/3000] | LR: 0.003769 | E: -63.749547 | E_img: -0.0105j E_var:     2.6749 E_err:   0.018070 | Acc: 0.2781
[2025-11-12 11:22:27] 10:36:00<03:02:51, 15.90s/it | [Iter 2400/3090] R0[2309/3000] | LR: 0.003759 | E: -63.761669 | E_img: +0.0022j E_var:     2.8207 E_err:   0.018556 | Acc: 0.2721
[2025-11-12 11:22:43] 10:36:16<03:02:35, 15.90s/it | [Iter 2401/3090] R0[2310/3000] | LR: 0.003748 | E: -63.799855 | E_img: -0.0283j E_var:     2.8347 E_err:   0.018602 | Acc: 0.2666
[2025-11-12 11:22:59] 10:36:32<03:02:19, 15.90s/it | [Iter 2402/3090] R0[2311/3000] | LR: 0.003738 | E: -63.780570 | E_img: -0.0018j E_var:     2.7324 E_err:   0.018263 | Acc: 0.2679
[2025-11-12 11:23:15] 10:36:48<03:02:03, 15.90s/it | [Iter 2403/3090] R0[2312/3000] | LR: 0.003728 | E: -63.776064 | E_img: +0.0024j E_var:     2.6743 E_err:   0.018068 | Acc: 0.2753
[2025-11-12 11:23:31] 10:37:04<03:01:47, 15.90s/it | [Iter 2404/3090] R0[2313/3000] | LR: 0.003717 | E: -63.697280 | E_img: +0.0021j E_var:     2.6256 E_err:   0.017903 | Acc: 0.2961
[2025-11-12 11:23:46] 10:37:19<03:01:31, 15.90s/it | [Iter 2405/3090] R0[2314/3000] | LR: 0.003707 | E: -63.645787 | E_img: +0.0160j E_var:     2.6920 E_err:   0.018128 | Acc: 0.3042
[2025-11-12 11:24:02] 10:37:35<03:01:16, 15.90s/it | [Iter 2406/3090] R0[2315/3000] | LR: 0.003697 | E: -63.595954 | E_img: +0.0205j E_var:     2.7129 E_err:   0.018198 | Acc: 0.3055
[2025-11-12 11:24:18] 10:37:51<03:01:00, 15.90s/it | [Iter 2407/3090] R0[2316/3000] | LR: 0.003686 | E: -63.747555 | E_img: -0.0074j E_var:     2.6008 E_err:   0.017818 | Acc: 0.2963
[2025-11-12 11:24:34] 10:38:07<03:00:44, 15.90s/it | [Iter 2408/3090] R0[2317/3000] | LR: 0.003676 | E: -63.795009 | E_img: +0.0162j E_var:     2.5942 E_err:   0.017795 | Acc: 0.2843
[2025-11-12 11:24:50] 10:38:23<03:00:28, 15.90s/it | [Iter 2409/3090] R0[2318/3000] | LR: 0.003666 | E: -63.792373 | E_img: -0.0110j E_var:     2.6552 E_err:   0.018003 | Acc: 0.2767
[2025-11-12 11:25:06] 10:38:39<03:00:12, 15.90s/it | [Iter 2410/3090] R0[2319/3000] | LR: 0.003655 | E: -63.748798 | E_img: +0.0094j E_var:     2.5788 E_err:   0.017742 | Acc: 0.2894
[2025-11-12 11:25:21] 10:38:55<02:59:56, 15.90s/it | [Iter 2411/3090] R0[2320/3000] | LR: 0.003645 | E: -63.678031 | E_img: +0.0109j E_var:     2.5976 E_err:   0.017807 | Acc: 0.2986
[2025-11-12 11:25:37] 10:39:10<02:59:40, 15.90s/it | [Iter 2412/3090] R0[2321/3000] | LR: 0.003635 | E: -63.572380 | E_img: +0.0092j E_var:     2.8383 E_err:   0.018614 | Acc: 0.3027
[2025-11-12 11:25:53] 10:39:26<02:59:24, 15.90s/it | [Iter 2413/3090] R0[2322/3000] | LR: 0.003625 | E: -63.475573 | E_img: -0.0203j E_var:     2.8745 E_err:   0.018732 | Acc: 0.3048
[2025-11-12 11:26:09] 10:39:42<02:59:08, 15.90s/it | [Iter 2414/3090] R0[2323/3000] | LR: 0.003614 | E: -63.499394 | E_img: +0.0074j E_var:     2.7820 E_err:   0.018428 | Acc: 0.3062
[2025-11-12 11:26:25] 10:39:58<02:58:52, 15.90s/it | [Iter 2415/3090] R0[2324/3000] | LR: 0.003604 | E: -63.575232 | E_img: -0.0304j E_var:     2.7716 E_err:   0.018394 | Acc: 0.3034
[2025-11-12 11:26:41] 10:40:14<02:58:36, 15.90s/it | [Iter 2416/3090] R0[2325/3000] | LR: 0.003594 | E: -63.465585 | E_img: +0.0031j E_var:     2.8934 E_err:   0.018794 | Acc: 0.3038
[2025-11-12 11:26:56] 10:40:30<02:58:21, 15.90s/it | [Iter 2417/3090] R0[2326/3000] | LR: 0.003584 | E: -63.268202 | E_img: +0.0030j E_var:     3.1958 E_err:   0.019751 | Acc: 0.3128
[2025-11-12 11:27:12] 10:40:45<02:58:05, 15.90s/it | [Iter 2418/3090] R0[2327/3000] | LR: 0.003574 | E: -63.417751 | E_img: -0.0047j E_var:     3.0006 E_err:   0.019138 | Acc: 0.3030
[2025-11-12 11:27:28] 10:41:01<02:57:49, 15.90s/it | [Iter 2419/3090] R0[2328/3000] | LR: 0.003563 | E: -63.333784 | E_img: -0.0144j E_var:     3.1775 E_err:   0.019695 | Acc: 0.2923
[2025-11-12 11:27:44] 10:41:17<02:57:33, 15.90s/it | [Iter 2420/3090] R0[2329/3000] | LR: 0.003553 | E: -63.412969 | E_img: +0.0005j E_var:     3.2102 E_err:   0.019796 | Acc: 0.2790
[2025-11-12 11:28:00] 10:41:33<02:57:17, 15.90s/it | [Iter 2421/3090] R0[2330/3000] | LR: 0.003543 | E: -63.343115 | E_img: +0.0113j E_var:     3.1022 E_err:   0.019460 | Acc: 0.2831
[2025-11-12 11:28:16] 10:41:49<02:57:01, 15.90s/it | [Iter 2422/3090] R0[2331/3000] | LR: 0.003533 | E: -63.319580 | E_img: -0.0385j E_var:     3.1197 E_err:   0.019515 | Acc: 0.2908
[2025-11-12 11:28:31] 10:42:05<02:56:45, 15.90s/it | [Iter 2423/3090] R0[2332/3000] | LR: 0.003523 | E: -63.405254 | E_img: +0.0009j E_var:     2.9723 E_err:   0.019048 | Acc: 0.2955
[2025-11-12 11:28:47] 10:42:20<02:56:29, 15.90s/it | [Iter 2424/3090] R0[2333/3000] | LR: 0.003513 | E: -63.571301 | E_img: +0.0181j E_var:     2.8502 E_err:   0.018653 | Acc: 0.2837
[2025-11-12 11:29:03] 10:42:36<02:56:13, 15.90s/it | [Iter 2425/3090] R0[2334/3000] | LR: 0.003503 | E: -63.526030 | E_img: -0.0041j E_var:     3.0363 E_err:   0.019252 | Acc: 0.2774
[2025-11-12 11:29:19] 10:42:52<02:55:57, 15.90s/it | [Iter 2426/3090] R0[2335/3000] | LR: 0.003493 | E: -63.524736 | E_img: -0.0347j E_var:     2.8750 E_err:   0.018734 | Acc: 0.2822
[2025-11-12 11:29:35] 10:43:08<02:55:41, 15.90s/it | [Iter 2427/3090] R0[2336/3000] | LR: 0.003483 | E: -63.333927 | E_img: -0.0012j E_var:     3.1742 E_err:   0.019685 | Acc: 0.2930
[2025-11-12 11:29:51] 10:43:24<02:55:25, 15.90s/it | [Iter 2428/3090] R0[2337/3000] | LR: 0.003472 | E: -63.428207 | E_img: +0.0337j E_var:     3.2904 E_err:   0.020041 | Acc: 0.2789
[2025-11-12 11:30:07] 10:43:40<02:55:10, 15.90s/it | [Iter 2429/3090] R0[2338/3000] | LR: 0.003462 | E: -63.440139 | E_img: +0.0179j E_var:     3.3589 E_err:   0.020249 | Acc: 0.2631
[2025-11-12 11:30:22] 10:43:55<02:54:54, 15.90s/it | [Iter 2430/3090] R0[2339/3000] | LR: 0.003452 | E: -63.296158 | E_img: -0.0183j E_var:     3.3095 E_err:   0.020100 | Acc: 0.2703
[2025-11-12 11:30:38] 10:44:11<02:54:38, 15.90s/it | [Iter 2431/3090] R0[2340/3000] | LR: 0.003442 | E: -63.345480 | E_img: +0.0467j E_var:     3.3508 E_err:   0.020224 | Acc: 0.2707
[2025-11-12 11:30:54] 10:44:27<02:54:22, 15.90s/it | [Iter 2432/3090] R0[2341/3000] | LR: 0.003432 | E: -63.384807 | E_img: +0.0117j E_var:     4.0764 E_err:   0.022307 | Acc: 0.2464
[2025-11-12 11:31:10] 10:44:43<02:54:06, 15.90s/it | [Iter 2433/3090] R0[2342/3000] | LR: 0.003422 | E: -63.590222 | E_img: +0.0102j E_var:     3.0387 E_err:   0.019260 | Acc: 0.2471
[2025-11-12 11:31:26] 10:44:59<02:53:50, 15.90s/it | [Iter 2434/3090] R0[2343/3000] | LR: 0.003412 | E: -63.666847 | E_img: -0.0228j E_var:     3.1826 E_err:   0.019711 | Acc: 0.2502
[2025-11-12 11:31:42] 10:45:15<02:53:34, 15.90s/it | [Iter 2435/3090] R0[2344/3000] | LR: 0.003402 | E: -63.671260 | E_img: +0.0046j E_var:     2.9667 E_err:   0.019030 | Acc: 0.2572
[2025-11-12 11:31:57] 10:45:31<02:53:18, 15.90s/it | [Iter 2436/3090] R0[2345/3000] | LR: 0.003392 | E: -63.643069 | E_img: +0.0392j E_var:     3.0709 E_err:   0.019361 | Acc: 0.2600
[2025-11-12 11:32:13] 10:45:46<02:53:02, 15.90s/it | [Iter 2437/3090] R0[2346/3000] | LR: 0.003383 | E: -63.699040 | E_img: +0.0130j E_var:     2.8477 E_err:   0.018645 | Acc: 0.2671
[2025-11-12 11:32:29] 10:46:02<02:52:46, 15.90s/it | [Iter 2438/3090] R0[2347/3000] | LR: 0.003373 | E: -63.664499 | E_img: +0.0105j E_var:     2.7169 E_err:   0.018211 | Acc: 0.2770
[2025-11-12 11:32:45] 10:46:18<02:52:30, 15.90s/it | [Iter 2439/3090] R0[2348/3000] | LR: 0.003363 | E: -63.685515 | E_img: +0.0095j E_var:     2.8076 E_err:   0.018513 | Acc: 0.2732
[2025-11-12 11:33:01] 10:46:34<02:52:14, 15.90s/it | [Iter 2440/3090] R0[2349/3000] | LR: 0.003353 | E: -63.735776 | E_img: +0.0030j E_var:     2.8750 E_err:   0.018734 | Acc: 0.2694
[2025-11-12 11:33:17] 10:46:50<02:51:59, 15.90s/it | [Iter 2441/3090] R0[2350/3000] | LR: 0.003343 | E: -63.714314 | E_img: +0.0300j E_var:     2.8165 E_err:   0.018542 | Acc: 0.2655
[2025-11-12 11:33:33] 10:47:06<02:51:43, 15.90s/it | [Iter 2442/3090] R0[2351/3000] | LR: 0.003333 | E: -63.783662 | E_img: -0.0155j E_var:     2.9401 E_err:   0.018945 | Acc: 0.2592
[2025-11-12 11:33:48] 10:47:21<02:51:27, 15.90s/it | [Iter 2443/3090] R0[2352/3000] | LR: 0.003323 | E: -63.740778 | E_img: +0.0015j E_var:     2.6789 E_err:   0.018083 | Acc: 0.2653
[2025-11-12 11:34:04] 10:47:37<02:51:11, 15.90s/it | [Iter 2444/3090] R0[2353/3000] | LR: 0.003313 | E: -63.727094 | E_img: +0.0081j E_var:     2.7759 E_err:   0.018408 | Acc: 0.2681
[2025-11-12 11:34:20] 10:47:53<02:50:55, 15.90s/it | [Iter 2445/3090] R0[2354/3000] | LR: 0.003303 | E: -63.681002 | E_img: -0.0114j E_var:     2.8964 E_err:   0.018803 | Acc: 0.2644
[2025-11-12 11:34:36] 10:48:09<02:50:39, 15.90s/it | [Iter 2446/3090] R0[2355/3000] | LR: 0.003294 | E: -63.704031 | E_img: +0.0056j E_var:     2.8356 E_err:   0.018605 | Acc: 0.2714
[2025-11-12 11:34:52] 10:48:25<02:50:23, 15.90s/it | [Iter 2447/3090] R0[2356/3000] | LR: 0.003284 | E: -63.735536 | E_img: +0.0039j E_var:     2.8658 E_err:   0.018704 | Acc: 0.2670
[2025-11-12 11:35:08] 10:48:41<02:50:07, 15.90s/it | [Iter 2448/3090] R0[2357/3000] | LR: 0.003274 | E: -63.756284 | E_img: +0.0267j E_var:     2.7874 E_err:   0.018446 | Acc: 0.2707
[2025-11-12 11:35:23] 10:48:57<02:49:51, 15.90s/it | [Iter 2449/3090] R0[2358/3000] | LR: 0.003264 | E: -63.722689 | E_img: +0.0118j E_var:     2.6611 E_err:   0.018023 | Acc: 0.2796
[2025-11-12 11:35:39] 10:49:12<02:49:35, 15.90s/it | [Iter 2450/3090] R0[2359/3000] | LR: 0.003254 | E: -63.779218 | E_img: -0.0207j E_var:     3.2190 E_err:   0.019823 | Acc: 0.2756
[2025-11-12 11:35:55] 10:49:28<02:49:19, 15.90s/it | [Iter 2451/3090] R0[2360/3000] | LR: 0.003245 | E: -63.790582 | E_img: -0.0205j E_var:     2.8400 E_err:   0.018619 | Acc: 0.2659
[2025-11-12 11:36:11] 10:49:44<02:49:04, 15.90s/it | [Iter 2452/3090] R0[2361/3000] | LR: 0.003235 | E: -63.771195 | E_img: -0.0069j E_var:     2.6671 E_err:   0.018044 | Acc: 0.2693
[2025-11-12 11:36:27] 10:50:00<02:48:48, 15.90s/it | [Iter 2453/3090] R0[2362/3000] | LR: 0.003225 | E: -63.797693 | E_img: -0.0126j E_var:     2.6852 E_err:   0.018105 | Acc: 0.2712
[2025-11-12 11:36:43] 10:50:16<02:48:32, 15.90s/it | [Iter 2454/3090] R0[2363/3000] | LR: 0.003215 | E: -63.734395 | E_img: +0.0033j E_var:     2.6614 E_err:   0.018025 | Acc: 0.2771
[2025-11-12 11:36:59] 10:50:32<02:48:16, 15.90s/it | [Iter 2455/3090] R0[2364/3000] | LR: 0.003206 | E: -63.685060 | E_img: -0.0136j E_var:     2.9863 E_err:   0.019093 | Acc: 0.2853
[2025-11-12 11:37:14] 10:50:48<02:48:00, 15.90s/it | [Iter 2456/3090] R0[2365/3000] | LR: 0.003196 | E: -63.740224 | E_img: -0.0065j E_var:     2.5342 E_err:   0.017588 | Acc: 0.2826
[2025-11-12 11:37:30] 10:51:03<02:47:44, 15.90s/it | [Iter 2457/3090] R0[2366/3000] | LR: 0.003186 | E: -63.761322 | E_img: -0.0028j E_var:     2.7153 E_err:   0.018206 | Acc: 0.2752
[2025-11-12 11:37:46] 10:51:19<02:47:28, 15.90s/it | [Iter 2458/3090] R0[2367/3000] | LR: 0.003177 | E: -63.727304 | E_img: +0.0050j E_var:     2.6696 E_err:   0.018052 | Acc: 0.2794
[2025-11-12 11:38:02] 10:51:35<02:47:12, 15.90s/it | [Iter 2459/3090] R0[2368/3000] | LR: 0.003167 | E: -63.750264 | E_img: +0.0133j E_var:     2.6441 E_err:   0.017966 | Acc: 0.2799
[2025-11-12 11:38:18] 10:51:51<02:46:56, 15.90s/it | [Iter 2460/3090] R0[2369/3000] | LR: 0.003157 | E: -63.762250 | E_img: +0.0037j E_var:     2.7592 E_err:   0.018353 | Acc: 0.2794
[2025-11-12 11:38:34] 10:52:07<02:46:40, 15.90s/it | [Iter 2461/3090] R0[2370/3000] | LR: 0.003148 | E: -63.800988 | E_img: -0.0007j E_var:     2.5804 E_err:   0.017748 | Acc: 0.2825
[2025-11-12 11:38:49] 10:52:23<02:46:24, 15.90s/it | [Iter 2462/3090] R0[2371/3000] | LR: 0.003138 | E: -63.751760 | E_img: +0.0113j E_var:     2.6466 E_err:   0.017974 | Acc: 0.2855
[2025-11-12 11:39:05] 10:52:38<02:46:08, 15.90s/it | [Iter 2463/3090] R0[2372/3000] | LR: 0.003129 | E: -63.791056 | E_img: -0.0040j E_var:     2.6062 E_err:   0.017837 | Acc: 0.2829
[2025-11-12 11:39:21] 10:52:54<02:45:53, 15.90s/it | [Iter 2464/3090] R0[2373/3000] | LR: 0.003119 | E: -63.766588 | E_img: -0.0003j E_var:     2.7989 E_err:   0.018484 | Acc: 0.2793
[2025-11-12 11:39:37] 10:53:10<02:45:37, 15.90s/it | [Iter 2465/3090] R0[2374/3000] | LR: 0.003109 | E: -63.738089 | E_img: -0.0013j E_var:     2.5909 E_err:   0.017784 | Acc: 0.2930
[2025-11-12 11:39:53] 10:53:26<02:45:21, 15.90s/it | [Iter 2466/3090] R0[2375/3000] | LR: 0.003100 | E: -63.598528 | E_img: -0.0099j E_var:     2.6744 E_err:   0.018068 | Acc: 0.2982
[2025-11-12 11:40:09] 10:53:42<02:45:05, 15.90s/it | [Iter 2467/3090] R0[2376/3000] | LR: 0.003090 | E: -63.568917 | E_img: +0.0047j E_var:     2.9328 E_err:   0.018921 | Acc: 0.2886
[2025-11-12 11:40:24] 10:53:58<02:44:49, 15.90s/it | [Iter 2468/3090] R0[2377/3000] | LR: 0.003081 | E: -63.612063 | E_img: +0.0227j E_var:     2.9506 E_err:   0.018979 | Acc: 0.2828
[2025-11-12 11:40:40] 10:54:13<02:44:33, 15.90s/it | [Iter 2469/3090] R0[2378/3000] | LR: 0.003071 | E: -63.653733 | E_img: +0.0096j E_var:     2.9626 E_err:   0.019017 | Acc: 0.2717
[2025-11-12 11:40:56] 10:54:29<02:44:17, 15.90s/it | [Iter 2470/3090] R0[2379/3000] | LR: 0.003062 | E: -63.590527 | E_img: -0.0137j E_var:     2.9466 E_err:   0.018966 | Acc: 0.2692
[2025-11-12 11:41:12] 10:54:45<02:44:01, 15.90s/it | [Iter 2471/3090] R0[2380/3000] | LR: 0.003052 | E: -63.520782 | E_img: +0.0055j E_var:     2.8693 E_err:   0.018715 | Acc: 0.2809
[2025-11-12 11:41:28] 10:55:01<02:43:45, 15.90s/it | [Iter 2472/3090] R0[2381/3000] | LR: 0.003043 | E: -63.605546 | E_img: -0.0011j E_var:     2.9745 E_err:   0.019055 | Acc: 0.2733
[2025-11-12 11:41:44] 10:55:17<02:43:29, 15.90s/it | [Iter 2473/3090] R0[2382/3000] | LR: 0.003033 | E: -63.669042 | E_img: -0.0216j E_var:     2.8203 E_err:   0.018555 | Acc: 0.2663
[2025-11-12 11:42:00] 10:55:33<02:43:13, 15.90s/it | [Iter 2474/3090] R0[2383/3000] | LR: 0.003024 | E: -63.723152 | E_img: -0.0022j E_var:     2.6495 E_err:   0.017984 | Acc: 0.2739
[2025-11-12 11:42:15] 10:55:48<02:42:57, 15.90s/it | [Iter 2475/3090] R0[2384/3000] | LR: 0.003014 | E: -63.735489 | E_img: -0.0050j E_var:     2.7805 E_err:   0.018423 | Acc: 0.2720
[2025-11-12 11:42:31] 10:56:04<02:42:42, 15.90s/it | [Iter 2476/3090] R0[2385/3000] | LR: 0.003005 | E: -63.759644 | E_img: +0.0028j E_var:     2.6449 E_err:   0.017968 | Acc: 0.2743
[2025-11-12 11:42:47] 10:56:20<02:42:26, 15.90s/it | [Iter 2477/3090] R0[2386/3000] | LR: 0.002995 | E: -63.752826 | E_img: -0.0166j E_var:     2.5225 E_err:   0.017548 | Acc: 0.2838
[2025-11-12 11:43:03] 10:56:36<02:42:10, 15.90s/it | [Iter 2478/3090] R0[2387/3000] | LR: 0.002986 | E: -63.732044 | E_img: -0.0093j E_var:     2.5713 E_err:   0.017717 | Acc: 0.2865
[2025-11-12 11:43:19] 10:56:52<02:41:54, 15.90s/it | [Iter 2479/3090] R0[2388/3000] | LR: 0.002977 | E: -63.751035 | E_img: -0.0171j E_var:     2.6334 E_err:   0.017929 | Acc: 0.2832
[2025-11-12 11:43:35] 10:57:08<02:41:38, 15.90s/it | [Iter 2480/3090] R0[2389/3000] | LR: 0.002967 | E: -63.797593 | E_img: -0.0119j E_var:     2.7622 E_err:   0.018363 | Acc: 0.2790
[2025-11-12 11:43:50] 10:57:24<02:41:22, 15.90s/it | [Iter 2481/3090] R0[2390/3000] | LR: 0.002958 | E: -63.723602 | E_img: +0.0089j E_var:     2.5200 E_err:   0.017539 | Acc: 0.2877
[2025-11-12 11:44:06] 10:57:39<02:41:06, 15.90s/it | [Iter 2482/3090] R0[2391/3000] | LR: 0.002948 | E: -63.707268 | E_img: +0.0022j E_var:     2.4826 E_err:   0.017408 | Acc: 0.2984
[2025-11-12 11:44:22] 10:57:55<02:40:50, 15.90s/it | [Iter 2483/3090] R0[2392/3000] | LR: 0.002939 | E: -63.765004 | E_img: -0.0017j E_var:     2.4674 E_err:   0.017355 | Acc: 0.2949
[2025-11-12 11:44:38] 10:58:11<02:40:34, 15.90s/it | [Iter 2484/3090] R0[2393/3000] | LR: 0.002930 | E: -63.815376 | E_img: +0.0143j E_var:     2.7029 E_err:   0.018164 | Acc: 0.2819
[2025-11-12 11:44:54] 10:58:27<02:40:18, 15.90s/it | [Iter 2485/3090] R0[2394/3000] | LR: 0.002920 | E: -63.835948 | E_img: -0.0024j E_var:     2.5967 E_err:   0.017804 | Acc: 0.2746
[2025-11-12 11:45:10] 10:58:43<02:40:02, 15.90s/it | [Iter 2486/3090] R0[2395/3000] | LR: 0.002911 | E: -63.801211 | E_img: +0.0037j E_var:     2.6557 E_err:   0.018005 | Acc: 0.2752
[2025-11-12 11:45:26] 10:58:59<02:39:47, 15.90s/it | [Iter 2487/3090] R0[2396/3000] | LR: 0.002902 | E: -63.825596 | E_img: +0.0052j E_var:     2.7803 E_err:   0.018423 | Acc: 0.2659
[2025-11-12 11:45:41] 10:59:14<02:39:31, 15.90s/it | [Iter 2488/3090] R0[2397/3000] | LR: 0.002893 | E: -63.829852 | E_img: -0.0077j E_var:     2.7893 E_err:   0.018453 | Acc: 0.2584
[2025-11-12 11:45:57] 10:59:30<02:39:15, 15.90s/it | [Iter 2489/3090] R0[2398/3000] | LR: 0.002883 | E: -63.790337 | E_img: -0.0030j E_var:     2.5369 E_err:   0.017598 | Acc: 0.2719
[2025-11-12 11:46:13] 10:59:46<02:38:59, 15.90s/it | [Iter 2490/3090] R0[2399/3000] | LR: 0.002874 | E: -63.791477 | E_img: +0.0132j E_var:     2.5277 E_err:   0.017566 | Acc: 0.2838
[2025-11-12 11:46:13] ✓ Checkpoint saved: checkpoint_iter_002400.pkl
[2025-11-13 13:03:11] ✓ 创建部分等变ViT模型（嵌入层等变）
[2025-11-13 13:03:11]   对称性组大小: 8 (C4v点群)
[2025-11-13 13:03:11]   嵌入层使用等变权重共享，后续Transformer层使用普通结构
[2025-11-13 13:07:21] 🔥 预热编译: 执行模型前向传播以触发JIT编译...
[2025-11-13 13:10:42] ✓ 预热编译完成 | 耗时: 200.68s
[2025-11-13 13:10:42] ======================================================================================================
[2025-11-13 13:10:42] ViT for Shastry-Sutherland Model
[2025-11-13 13:10:42] ======================================================================================================
[2025-11-13 13:10:42] System Parameters:
[2025-11-13 13:10:42]   - Lattice size: L = 6
[2025-11-13 13:10:42]   - Total sites: N = 144
[2025-11-13 13:10:42]   - J1 coupling: 0.8
[2025-11-13 13:10:42]   - J2 coupling: 1.0
[2025-11-13 13:10:42]   - Q (4-spin): 0.0
[2025-11-13 13:10:42] ------------------------------------------------------------------------------------------------------
[2025-11-13 13:10:42] Model Parameters:
[2025-11-13 13:10:42]   ViT Architecture:
[2025-11-13 13:10:42]     • Layers: 4
[2025-11-13 13:10:42]     • Embedding dimension (d_model): 32
[2025-11-13 13:10:42]     • Attention heads: 4
[2025-11-13 13:10:42]     • Patch size: 2x2
[2025-11-13 13:10:42]     • Number of patches: 36
[2025-11-13 13:10:42]     • Config: [4, 32, 4, 2]
[2025-11-13 13:10:42]     • Total parameters: 458,064
[2025-11-13 13:10:42]   Regularization:
[2025-11-13 13:10:42]     • Relative Position Encoding (RPE): True
[2025-11-13 13:10:42]     • Dropout rate: 0.0
[2025-11-13 13:10:42]   Optimizer:
[2025-11-13 13:10:42]     • Diagonal shift (SR): 0.005
[2025-11-13 13:10:42]   Mixed Precision Training: True
[2025-11-13 13:10:42]     • Parameters: float64 (high precision)
[2025-11-13 13:10:42]     • Computation: bfloat16 (accelerated)
[2025-11-13 13:10:42]     • Critical ops (LayerNorm/Softmax/Output): float64
[2025-11-13 13:10:42]     • Expected speedup: 1.5-2x, Memory reduction: ~50%
[2025-11-13 13:10:42] ------------------------------------------------------------------------------------------------------
[2025-11-13 13:10:42] Training Hyperparameters:
[2025-11-13 13:10:42]   Learning Rate Schedule:
[2025-11-13 13:10:42]     • Max LR: 0.05
[2025-11-13 13:10:42]     • Min LR: 1e-07
[2025-11-13 13:10:42]     • Annealing cycles: 3
[2025-11-13 13:10:42]     • Initial period: 100
[2025-11-13 13:10:42]     • Period multiplier: 2.0
[2025-11-13 13:10:42]     • Warm-up: 21 iterations (3.0%)
[2025-11-13 13:10:42]     • Total iterations: 700 + 21 (warm-up) = 721
[2025-11-13 13:10:42]   Sampling Parameters:
[2025-11-13 13:10:42]     • Samples (n_samples): 8192
[2025-11-13 13:10:42]     • Parallel chains (n_chains): 8192
[2025-11-13 13:10:42]     • Max exchange distance (d_max): 9.00 (auto from lattice)
[2025-11-13 13:10:42]     • Chunk size: 8192
[2025-11-13 13:10:42]     • Discarded samples per chain: 0
[2025-11-13 13:10:42]     • Parameter-to-sample ratio: 55.92
[2025-11-13 13:10:42]       ⚠ High ratio (> 50), high overfitting risk!
[2025-11-13 13:10:42] ------------------------------------------------------------------------------------------------------
[2025-11-13 13:10:42] Checkpoint Configuration:
[2025-11-13 13:10:42]   • Enabled: Yes
[2025-11-13 13:10:42]   • Save interval: 100 iterations
[2025-11-13 13:10:42]   • Keep history: True
[2025-11-13 13:10:42]   • Directory: saved_models/L=6/J2=1.00/J1=0.80/checkpoints
[2025-11-13 13:10:42] ------------------------------------------------------------------------------------------------------
[2025-11-13 13:10:42] Device Status:
[2025-11-13 13:10:42]   • Device type: H200
[2025-11-13 13:10:42]   • Number of devices: 1
[2025-11-13 13:10:42]   • Sharding enabled: True
[2025-11-13 13:10:46] ======================================================================================================
[2025-11-13 13:10:46] 🔥 Linear Warm-up: 21 iterations (3.0% of 700) | LR: 0 -> 0.050000
[2025-11-13 13:10:46]    Total iterations: 21 (warm-up) + 700 (training) = 721
[2025-11-13 13:10:46] 🚀 Training iterations started at 2025-11-13 13:10:46

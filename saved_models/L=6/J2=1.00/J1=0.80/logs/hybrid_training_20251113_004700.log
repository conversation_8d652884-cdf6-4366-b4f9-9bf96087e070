[2025-11-13 00:47:23] ======================================================================================================
[2025-11-13 00:47:23] 🔥 Linear Warm-up: 90 iterations (3.0% of 3000) | LR: 0 -> 0.025000
[2025-11-13 00:47:23]    Total iterations: 90 (warm-up) + 3000 (training) = 3090
[2025-11-13 00:47:23] 🚀 Training iterations started at 2025-11-13 00:47:23
[2025-11-13 00:50:23] 3:00<9273:13, 180.12s/it | [Iter    1/3090] WARMUP[1/90]  | LR: 0.000278 | E:   5.090175 | E_var:    92.3887 E_err:   0.150186 | NF_loss: 33.804219
[2025-11-13 00:50:31] 3:08<4838:21, 94.01s/it | [Iter    2/3090] WARMUP[2/90]  | LR: 0.000556 | E:   3.550003 | E_var:    75.5301 E_err:   0.135794 | NF_loss: 30.181199
[2025-11-13 00:50:39] 3:15<3359:02, 65.29s/it | [Iter    3/3090] WARMUP[3/90]  | LR: 0.000833 | E:   2.436819 | E_var:    72.4026 E_err:   0.132953 | NF_loss: 33.835306
[2025-11-13 00:50:47] 3:23<2619:12, 50.92s/it | [Iter    4/3090] WARMUP[4/90]  | LR: 0.001111 | E:   1.520020 | E_var:    72.1848 E_err:   0.132753 | NF_loss: 30.497000
[2025-11-13 00:50:55] 3:31<2175:19, 42.31s/it | [Iter    5/3090] WARMUP[5/90]  | LR: 0.001389 | E:   1.061923 | E_var:    61.8842 E_err:   0.122916 | NF_loss: 42.013596
[2025-11-13 00:51:02] 3:39<1879:26, 36.57s/it | [Iter    6/3090] WARMUP[6/90]  | LR: 0.001667 | E:   0.426663 | E_var:    54.7862 E_err:   0.115653 | NF_loss: 44.292118
[2025-11-13 00:51:10] 3:47<1668:00, 32.46s/it | [Iter    7/3090] WARMUP[7/90]  | LR: 0.001944 | E:   0.027068 | E_var:    59.2870 E_err:   0.120309 | NF_loss: 58.956052
[2025-11-13 00:51:18] 3:55<1509:25, 29.39s/it | [Iter    8/3090] WARMUP[8/90]  | LR: 0.002222 | E:  -0.429199 | E_var:    54.6775 E_err:   0.115538 | NF_loss: 52.207421
[2025-11-13 00:51:26] 4:02<1386:02, 26.99s/it | [Iter    9/3090] WARMUP[9/90]  | LR: 0.002500 | E:  -0.838207 | E_var:    53.6188 E_err:   0.114414 | NF_loss: 45.657638
[2025-11-13 00:51:34] 4:10<1287:17, 25.08s/it | [Iter   10/3090] WARMUP[10/90] | LR: 0.002778 | E:  -1.142748 | E_var:    49.5488 E_err:   0.109986 | NF_loss: 50.372694
[2025-11-13 00:51:42] 4:18<1206:26, 23.51s/it | [Iter   11/3090] WARMUP[11/90] | LR: 0.003056 | E:  -1.318599 | E_var:    68.9212 E_err:   0.129717 | NF_loss: 44.718713
[2025-11-13 00:51:49] 4:26<1139:04, 22.20s/it | [Iter   12/3090] WARMUP[12/90] | LR: 0.003333 | E:  -1.649054 | E_var:    49.9723 E_err:   0.110455 | NF_loss: 34.754265
[2025-11-13 00:51:57] 4:34<1082:06, 21.10s/it | [Iter   13/3090] WARMUP[13/90] | LR: 0.003611 | E:  -1.849121 | E_var:    47.9717 E_err:   0.108221 | NF_loss: 54.141099
[2025-11-13 00:52:05] 4:42<1033:08, 20.15s/it | [Iter   14/3090] WARMUP[14/90] | LR: 0.003889 | E:  -2.203442 | E_var:    62.0547 E_err:   0.123086 | NF_loss: 40.746588
[2025-11-13 00:52:13] 4:49<990:44, 19.33s/it | [Iter   15/3090] WARMUP[15/90] | LR: 0.004167 | E:  -2.592962 | E_var:    57.1575 E_err:   0.118129 | NF_loss: 52.875707
[2025-11-13 00:52:21] 4:57<953:37, 18.61s/it | [Iter   16/3090] WARMUP[16/90] | LR: 0.004444 | E:  -2.706407 | E_var:    47.5142 E_err:   0.107704 | NF_loss: 46.028417
[2025-11-13 00:52:29] 5:05<920:54, 17.98s/it | [Iter   17/3090] WARMUP[17/90] | LR: 0.004722 | E:  -2.850980 | E_var:    50.0315 E_err:   0.110520 | NF_loss: 44.665634
[2025-11-13 00:52:36] 5:13<891:45, 17.42s/it | [Iter   18/3090] WARMUP[18/90] | LR: 0.005000 | E:  -3.382105 | E_var:    45.4630 E_err:   0.105354 | NF_loss: 28.857334
[2025-11-13 00:52:44] 5:21<865:40, 16.91s/it | [Iter   19/3090] WARMUP[19/90] | LR: 0.005278 | E:  -3.778294 | E_var:   100.0071 E_err:   0.156256 | NF_loss: 60.738122
[2025-11-13 00:52:52] 5:29<842:11, 16.46s/it | [Iter   20/3090] WARMUP[20/90] | LR: 0.005556 | E:  -4.134800 | E_var:    64.1502 E_err:   0.125147 | NF_loss: 38.708972
[2025-11-13 00:53:00] 5:37<820:55, 16.05s/it | [Iter   21/3090] WARMUP[21/90] | LR: 0.005833 | E:  -4.374921 | E_var:    42.4243 E_err:   0.101772 | NF_loss: 44.837184
[2025-11-13 00:53:08] 5:44<801:34, 15.68s/it | [Iter   22/3090] WARMUP[22/90] | LR: 0.006111 | E:  -4.779384 | E_var:    45.5950 E_err:   0.105506 | NF_loss: 63.408438
[2025-11-13 00:53:16] 5:52<783:53, 15.34s/it | [Iter   23/3090] WARMUP[23/90] | LR: 0.006389 | E:  -5.231808 | E_var:    47.5145 E_err:   0.107704 | NF_loss: 52.798257
[2025-11-13 00:53:24] 6:00<767:41, 15.02s/it | [Iter   24/3090] WARMUP[24/90] | LR: 0.006667 | E:  -5.466929 | E_var:    48.1007 E_err:   0.108367 | NF_loss: 40.890346
[2025-11-13 00:53:32] 6:08<753:16, 14.75s/it | [Iter   25/3090] WARMUP[25/90] | LR: 0.006944 | E:  -6.063200 | E_var:    49.3438 E_err:   0.109758 | NF_loss: 49.669237
[2025-11-13 00:53:39] 6:16<739:28, 14.48s/it | [Iter   26/3090] WARMUP[26/90] | LR: 0.007222 | E:  -6.397888 | E_var:    51.4941 E_err:   0.112124 | NF_loss: 67.608283
[2025-11-13 00:53:47] 6:24<726:42, 14.24s/it | [Iter   27/3090] WARMUP[27/90] | LR: 0.007500 | E:  -6.702854 | E_var:    49.9874 E_err:   0.110472 | NF_loss: 49.806698
[2025-11-13 00:53:55] 6:32<714:51, 14.01s/it | [Iter   28/3090] WARMUP[28/90] | LR: 0.007778 | E:  -7.220514 | E_var:    42.9402 E_err:   0.102389 | NF_loss: 58.366557
[2025-11-13 00:54:03] 6:40<703:45, 13.79s/it | [Iter   29/3090] WARMUP[29/90] | LR: 0.008056 | E:  -7.772327 | E_var:    47.2722 E_err:   0.107429 | NF_loss: 60.769061
[2025-11-13 00:54:11] 6:47<693:24, 13.60s/it | [Iter   30/3090] WARMUP[30/90] | LR: 0.008333 | E:  -8.163374 | E_var:    50.2245 E_err:   0.110733 | NF_loss: 87.674163
[2025-11-13 00:54:19] 6:55<683:44, 13.41s/it | [Iter   31/3090] WARMUP[31/90] | LR: 0.008611 | E:  -8.532509 | E_var:    54.3573 E_err:   0.115199 | NF_loss: 170.557841
[2025-11-13 00:54:27] 7:03<674:40, 13.24s/it | [Iter   32/3090] WARMUP[32/90] | LR: 0.008889 | E:  -8.919754 | E_var:    51.4886 E_err:   0.112118 | NF_loss: 99.366791
[2025-11-13 00:54:34] 7:11<666:07, 13.07s/it | [Iter   33/3090] WARMUP[33/90] | LR: 0.009167 | E:  -9.385184 | E_var:    47.8785 E_err:   0.108116 | NF_loss: 119.005273
[2025-11-13 00:54:42] 7:19<658:05, 12.92s/it | [Iter   34/3090] WARMUP[34/90] | LR: 0.009444 | E: -10.012225 | E_var:    51.5356 E_err:   0.112169 | NF_loss: 139.743210
[2025-11-13 00:54:50] 7:27<650:29, 12.78s/it | [Iter   35/3090] WARMUP[35/90] | LR: 0.009722 | E: -10.576162 | E_var:    46.7534 E_err:   0.106838 | NF_loss: 86.562084
[2025-11-13 00:54:58] 7:34<643:18, 12.64s/it | [Iter   36/3090] WARMUP[36/90] | LR: 0.010000 | E: -11.069850 | E_var:    46.0931 E_err:   0.106081 | NF_loss: 91.512981
[2025-11-13 00:55:06] 7:42<636:29, 12.51s/it | [Iter   37/3090] WARMUP[37/90] | LR: 0.010278 | E: -11.610973 | E_var:    47.3155 E_err:   0.107479 | NF_loss: 121.690386
[2025-11-13 00:55:14] 7:50<630:02, 12.39s/it | [Iter   38/3090] WARMUP[38/90] | LR: 0.010556 | E: -12.294747 | E_var:    51.0030 E_err:   0.111588 | NF_loss: 118.014228
[2025-11-13 00:55:22] 7:58<623:56, 12.27s/it | [Iter   39/3090] WARMUP[39/90] | LR: 0.010833 | E: -12.774340 | E_var:    44.9899 E_err:   0.104804 | NF_loss: 85.837371
[2025-11-13 00:55:29] 8:06<618:06, 12.16s/it | [Iter   40/3090] WARMUP[40/90] | LR: 0.011111 | E: -13.493312 | E_var:    46.9101 E_err:   0.107017 | NF_loss: 90.781061
[2025-11-13 00:55:37] 8:14<612:34, 12.05s/it | [Iter   41/3090] WARMUP[41/90] | LR: 0.011389 | E: -14.116098 | E_var:    50.0719 E_err:   0.110565 | NF_loss: 76.804358
[2025-11-13 00:55:45] 8:22<607:19, 11.96s/it | [Iter   42/3090] WARMUP[42/90] | LR: 0.011667 | E: -14.643039 | E_var:    49.5823 E_err:   0.110023 | NF_loss: 47.032307
[2025-11-13 00:55:53] 8:29<602:17, 11.86s/it | [Iter   43/3090] WARMUP[43/90] | LR: 0.011944 | E: -15.360857 | E_var:    46.9453 E_err:   0.107057 | NF_loss: 27.741395
[2025-11-13 00:56:01] 8:37<597:27, 11.77s/it | [Iter   44/3090] WARMUP[44/90] | LR: 0.012222 | E: -16.093071 | E_var:    48.4053 E_err:   0.108709 | NF_loss: 48.922924
[2025-11-13 00:56:09] 8:45<592:50, 11.68s/it | [Iter   45/3090] WARMUP[45/90] | LR: 0.012500 | E: -16.889459 | E_var:    51.1378 E_err:   0.111735 | NF_loss: 127.777232
[2025-11-13 00:56:16] 8:53<588:25, 11.60s/it | [Iter   46/3090] WARMUP[46/90] | LR: 0.012778 | E: -17.541444 | E_var:    50.4581 E_err:   0.110990 | NF_loss: 69.907198
[2025-11-13 00:56:24] 9:01<584:11, 11.52s/it | [Iter   47/3090] WARMUP[47/90] | LR: 0.013056 | E: -18.475073 | E_var:    44.1018 E_err:   0.103764 | NF_loss: 77.952129
[2025-11-13 00:56:32] 9:09<580:07, 11.44s/it | [Iter   48/3090] WARMUP[48/90] | LR: 0.013333 | E: -19.467310 | E_var:    44.0327 E_err:   0.103683 | NF_loss: 29.814973
[2025-11-13 00:56:40] 9:17<576:13, 11.37s/it | [Iter   49/3090] WARMUP[49/90] | LR: 0.013611 | E: -20.347613 | E_var:    44.9855 E_err:   0.104799 | NF_loss: 26.339645
[2025-11-13 00:56:48] 9:24<572:29, 11.30s/it | [Iter   50/3090] WARMUP[50/90] | LR: 0.013889 | E: -21.404505 | E_var:    46.5672 E_err:   0.106625 | NF_loss: 71.055516
[2025-11-13 00:56:56] 9:32<568:52, 11.23s/it | [Iter   51/3090] WARMUP[51/90] | LR: 0.014167 | E: -22.071672 | E_var:    44.8877 E_err:   0.104685 | NF_loss: 63.172301
[2025-11-13 00:57:04] 9:40<565:24, 11.17s/it | [Iter   52/3090] WARMUP[52/90] | LR: 0.014444 | E: -23.101709 | E_var:    43.7509 E_err:   0.103351 | NF_loss: 53.600565
[2025-11-13 00:57:12] 9:48<562:18, 11.11s/it | [Iter   53/3090] WARMUP[53/90] | LR: 0.014722 | E: -23.919603 | E_var:    43.1028 E_err:   0.102582 | NF_loss: 30.309910
[2025-11-13 00:57:20] 9:56<559:05, 11.05s/it | [Iter   54/3090] WARMUP[54/90] | LR: 0.015000 | E: -24.994247 | E_var:    46.5804 E_err:   0.106640 | NF_loss: 40.467318
[2025-11-13 00:57:27] 10:04<555:57, 10.99s/it | [Iter   55/3090] WARMUP[55/90] | LR: 0.015278 | E: -25.605930 | E_var:    43.2622 E_err:   0.102772 | NF_loss: 70.469688
[2025-11-13 00:57:35] 10:12<552:56, 10.93s/it | [Iter   56/3090] WARMUP[56/90] | LR: 0.015556 | E: -26.573287 | E_var:    45.0317 E_err:   0.104853 | NF_loss: 36.949902
[2025-11-13 00:57:43] 10:20<550:02, 10.88s/it | [Iter   57/3090] WARMUP[57/90] | LR: 0.015833 | E: -27.505874 | E_var:    41.4320 E_err:   0.100575 | NF_loss: 48.894166
[2025-11-13 00:57:51] 10:28<547:13, 10.83s/it | [Iter   58/3090] WARMUP[58/90] | LR: 0.016111 | E: -28.576349 | E_var:    41.5813 E_err:   0.100756 | NF_loss: 33.367372
[2025-11-13 00:57:59] 10:35<544:29, 10.78s/it | [Iter   59/3090] WARMUP[59/90] | LR: 0.016389 | E: -29.929787 | E_var:    41.8792 E_err:   0.101116 | NF_loss: 32.100997
[2025-11-13 00:58:07] 10:43<541:51, 10.73s/it | [Iter   60/3090] WARMUP[60/90] | LR: 0.016667 | E: -30.647786 | E_var:    40.5122 E_err:   0.099452 | NF_loss: 31.974117
[2025-11-13 00:58:15] 10:51<539:18, 10.68s/it | [Iter   61/3090] WARMUP[61/90] | LR: 0.016944 | E: -31.631961 | E_var:    41.1899 E_err:   0.100280 | NF_loss: 38.141941
[2025-11-13 00:58:22] 10:59<536:49, 10.64s/it | [Iter   62/3090] WARMUP[62/90] | LR: 0.017222 | E: -32.334612 | E_var:    42.0041 E_err:   0.101267 | NF_loss: 38.509201
[2025-11-13 00:58:30] 11:07<534:24, 10.59s/it | [Iter   63/3090] WARMUP[63/90] | LR: 0.017500 | E: -32.991001 | E_var:    36.1440 E_err:   0.093937 | NF_loss: 26.366467
[2025-11-13 00:58:38] 11:15<532:05, 10.55s/it | [Iter   64/3090] WARMUP[64/90] | LR: 0.017778 | E: -33.589041 | E_var:    33.7828 E_err:   0.090817 | NF_loss: 23.077962
[2025-11-13 00:58:46] 11:23<529:50, 10.51s/it | [Iter   65/3090] WARMUP[65/90] | LR: 0.018056 | E: -34.643897 | E_var:    39.3546 E_err:   0.098021 | NF_loss: 32.874549
[2025-11-13 00:58:54] 11:30<527:38, 10.47s/it | [Iter   66/3090] WARMUP[66/90] | LR: 0.018333 | E: -35.483414 | E_var:    36.8489 E_err:   0.094849 | NF_loss: 35.165579
[2025-11-13 00:59:02] 11:38<525:29, 10.43s/it | [Iter   67/3090] WARMUP[67/90] | LR: 0.018611 | E: -36.378936 | E_var:    32.8889 E_err:   0.089608 | NF_loss: 35.945598
[2025-11-13 00:59:10] 11:46<523:25, 10.39s/it | [Iter   68/3090] WARMUP[68/90] | LR: 0.018889 | E: -37.354534 | E_var:    34.8690 E_err:   0.092266 | NF_loss: 38.427035
[2025-11-13 00:59:18] 11:54<521:24, 10.36s/it | [Iter   69/3090] WARMUP[69/90] | LR: 0.019167 | E: -38.014398 | E_var:    32.2248 E_err:   0.088698 | NF_loss: 26.905880
[2025-11-13 00:59:25] 12:02<519:26, 10.32s/it | [Iter   70/3090] WARMUP[70/90] | LR: 0.019444 | E: -38.755444 | E_var:    34.1291 E_err:   0.091281 | NF_loss: 18.105305
[2025-11-13 00:59:33] 12:10<517:32, 10.29s/it | [Iter   71/3090] WARMUP[71/90] | LR: 0.019722 | E: -39.100556 | E_var:    31.4427 E_err:   0.087615 | NF_loss: 18.086764
[2025-11-13 00:59:41] 12:18<515:41, 10.25s/it | [Iter   72/3090] WARMUP[72/90] | LR: 0.020000 | E: -38.973343 | E_var:    30.7006 E_err:   0.086575 | NF_loss: 11.062374
[2025-11-13 00:59:49] 12:26<513:52, 10.22s/it | [Iter   73/3090] WARMUP[73/90] | LR: 0.020278 | E: -39.585463 | E_var:    30.3646 E_err:   0.086100 | NF_loss: 39.257619
[2025-11-13 00:59:57] 12:33<512:05, 10.19s/it | [Iter   74/3090] WARMUP[74/90] | LR: 0.020556 | E: -39.823396 | E_var:    31.6946 E_err:   0.087966 | NF_loss: 26.051982
[2025-11-13 01:00:05] 12:41<510:22, 10.16s/it | [Iter   75/3090] WARMUP[75/90] | LR: 0.020833 | E: -40.937525 | E_var:    31.4839 E_err:   0.087673 | NF_loss: 35.341881
[2025-11-13 01:00:13] 12:49<508:41, 10.13s/it | [Iter   76/3090] WARMUP[76/90] | LR: 0.021111 | E: -41.440323 | E_var:    27.4885 E_err:   0.081921 | NF_loss: 36.393497
[2025-11-13 01:00:20] 12:57<507:02, 10.10s/it | [Iter   77/3090] WARMUP[77/90] | LR: 0.021389 | E: -42.417306 | E_var:    29.7195 E_err:   0.085181 | NF_loss: 31.973836
[2025-11-13 01:00:28] 13:05<505:27, 10.07s/it | [Iter   78/3090] WARMUP[78/90] | LR: 0.021667 | E: -42.535972 | E_var:    26.4758 E_err:   0.080398 | NF_loss: 39.172653
[2025-11-13 01:00:36] 13:13<503:54, 10.04s/it | [Iter   79/3090] WARMUP[79/90] | LR: 0.021944 | E: -42.383847 | E_var:    26.8023 E_err:   0.080892 | NF_loss: 41.223996
[2025-11-13 01:00:44] 13:21<502:22, 10.01s/it | [Iter   80/3090] WARMUP[80/90] | LR: 0.022222 | E: -42.753469 | E_var:    27.0824 E_err:   0.081314 | NF_loss: 25.787378
[2025-11-13 01:00:52] 13:28<500:52, 9.99s/it | [Iter   81/3090] WARMUP[81/90] | LR: 0.022500 | E: -42.796155 | E_var:    27.0226 E_err:   0.081224 | NF_loss: 29.440385
[2025-11-13 01:01:00] 13:36<499:25, 9.96s/it | [Iter   82/3090] WARMUP[82/90] | LR: 0.022778 | E: -43.399805 | E_var:    28.4138 E_err:   0.083288 | NF_loss: 33.956436
[2025-11-13 01:01:08] 13:44<497:59, 9.94s/it | [Iter   83/3090] WARMUP[83/90] | LR: 0.023056 | E: -42.118858 | E_var:    27.3340 E_err:   0.081691 | NF_loss: 33.597379
[2025-11-13 01:01:16] 13:52<496:35, 9.91s/it | [Iter   84/3090] WARMUP[84/90] | LR: 0.023333 | E: -42.974356 | E_var:    27.4339 E_err:   0.081840 | NF_loss: 25.711725
[2025-11-13 01:01:23] 14:00<495:12, 9.89s/it | [Iter   85/3090] WARMUP[85/90] | LR: 0.023611 | E: -43.096869 | E_var:    28.9675 E_err:   0.084096 | NF_loss: 28.059900
[2025-11-13 01:01:31] 14:08<493:52, 9.86s/it | [Iter   86/3090] WARMUP[86/90] | LR: 0.023889 | E: -43.423783 | E_var:    26.7476 E_err:   0.080809 | NF_loss: 32.726756
[2025-11-13 01:01:39] 14:16<492:34, 9.84s/it | [Iter   87/3090] WARMUP[87/90] | LR: 0.024167 | E: -44.379861 | E_var:    27.0858 E_err:   0.081319 | NF_loss: 41.739877
[2025-11-13 01:01:47] 14:24<491:16, 9.82s/it | [Iter   88/3090] WARMUP[88/90] | LR: 0.024444 | E: -44.344278 | E_var:    28.8115 E_err:   0.083869 | NF_loss: 30.696233
[2025-11-13 01:01:55] 14:31<490:00, 9.80s/it | [Iter   89/3090] WARMUP[89/90] | LR: 0.024722 | E: -42.551467 | E_var:    62.4535 E_err:   0.123481 | NF_loss: 33.264837
[2025-11-13 01:02:03] ✅ Warm-up completed | Starting cosine annealing from LR=0.025000
[2025-11-13 01:02:03] 14:39<488:46, 9.78s/it | [Iter   90/3090] WARMUP[90/90] | LR: 0.025000 | E: -43.752915 | E_var:    49.0712 E_err:   0.109454 | NF_loss: 35.682834
[2025-11-13 01:02:11] 14:47<487:42, 9.76s/it | [Iter   91/3090] R0[1/3000]    | LR: 0.025000 | E: -44.166318 | E_var:    32.6950 E_err:   0.089343 | NF_loss: 35.073636
[2025-11-13 01:02:19] 14:55<486:31, 9.74s/it | [Iter   92/3090] R0[2/3000]    | LR: 0.025000 | E: -43.763647 | E_var:    28.8939 E_err:   0.083989 | NF_loss: 31.766541
[2025-11-13 01:02:27] 15:03<485:21, 9.72s/it | [Iter   93/3090] R0[3/3000]    | LR: 0.025000 | E: -45.331297 | E_var:    24.8276 E_err:   0.077855 | NF_loss: 34.836604
[2025-11-13 01:02:35] 15:11<484:12, 9.70s/it | [Iter   94/3090] R0[4/3000]    | LR: 0.025000 | E: -46.199406 | E_var:    24.7649 E_err:   0.077757 | NF_loss: 24.468290
[2025-11-13 01:02:42] 15:19<483:05, 9.68s/it | [Iter   95/3090] R0[5/3000]    | LR: 0.025000 | E: -45.920129 | E_var:    23.7168 E_err:   0.076094 | NF_loss: 40.294488
[2025-11-13 01:02:50] 15:27<481:59, 9.66s/it | [Iter   96/3090] R0[6/3000]    | LR: 0.025000 | E: -46.638804 | E_var:    24.4601 E_err:   0.077277 | NF_loss: 32.235972
[2025-11-13 01:02:58] 15:35<480:54, 9.64s/it | [Iter   97/3090] R0[7/3000]    | LR: 0.025000 | E: -46.940661 | E_var:    23.5517 E_err:   0.075828 | NF_loss: 42.400556
[2025-11-13 01:03:06] 15:43<479:51, 9.62s/it | [Iter   98/3090] R0[8/3000]    | LR: 0.025000 | E: -46.323481 | E_var:    22.3843 E_err:   0.073925 | NF_loss: 32.114211
[2025-11-13 01:03:14] 15:50<478:48, 9.60s/it | [Iter   99/3090] R0[9/3000]    | LR: 0.025000 | E: -46.336003 | E_var:    24.1106 E_err:   0.076723 | NF_loss: 39.159903
[2025-11-13 01:03:22] 15:58<477:46, 9.59s/it | [Iter  100/3090] R0[10/3000]   | LR: 0.024999 | E: -45.008992 | E_var:    25.7788 E_err:   0.079333 | NF_loss: 36.137742
[2025-11-13 01:03:30] 16:06<476:45, 9.57s/it | [Iter  101/3090] R0[11/3000]   | LR: 0.024999 | E: -46.383860 | E_var:    24.0668 E_err:   0.076653 | NF_loss: 40.998949
[2025-11-13 01:03:37] 16:14<475:46, 9.55s/it | [Iter  102/3090] R0[12/3000]   | LR: 0.024999 | E: -47.175735 | E_var:    24.4840 E_err:   0.077315 | NF_loss: 46.621583
[2025-11-13 01:03:45] 16:22<474:48, 9.54s/it | [Iter  103/3090] R0[13/3000]   | LR: 0.024999 | E: -45.872236 | E_var:    26.0271 E_err:   0.079714 | NF_loss: 32.166837
[2025-11-13 01:03:53] 16:30<473:50, 9.52s/it | [Iter  104/3090] R0[14/3000]   | LR: 0.024999 | E: -45.429068 | E_var:    25.5326 E_err:   0.078953 | NF_loss: 31.738631
[2025-11-13 01:04:01] 16:38<472:54, 9.51s/it | [Iter  105/3090] R0[15/3000]   | LR: 0.024999 | E: -46.390241 | E_var:    24.2584 E_err:   0.076958 | NF_loss: 26.362286
[2025-11-13 01:04:09] 16:45<471:59, 9.49s/it | [Iter  106/3090] R0[16/3000]   | LR: 0.024998 | E: -47.377030 | E_var:    24.0327 E_err:   0.076599 | NF_loss: 27.871745
[2025-11-13 01:04:17] 16:53<471:04, 9.48s/it | [Iter  107/3090] R0[17/3000]   | LR: 0.024998 | E: -46.994331 | E_var:    23.5752 E_err:   0.075866 | NF_loss: 33.344004
[2025-11-13 01:04:25] 17:01<470:10, 9.46s/it | [Iter  108/3090] R0[18/3000]   | LR: 0.024998 | E: -48.254759 | E_var:    25.3668 E_err:   0.078696 | NF_loss: 35.607696
[2025-11-13 01:04:33] 17:09<469:18, 9.45s/it | [Iter  109/3090] R0[19/3000]   | LR: 0.024998 | E: -49.574980 | E_var:    24.1695 E_err:   0.076816 | NF_loss: 28.320335
[2025-11-13 01:04:40] 17:17<468:25, 9.43s/it | [Iter  110/3090] R0[20/3000]   | LR: 0.024998 | E: -49.262446 | E_var:    20.7319 E_err:   0.071144 | NF_loss: 31.957218
[2025-11-13 01:04:48] 17:25<467:34, 9.42s/it | [Iter  111/3090] R0[21/3000]   | LR: 0.024997 | E: -48.246747 | E_var:    22.7201 E_err:   0.074477 | NF_loss: 34.783144
[2025-11-13 01:04:56] 17:33<466:43, 9.40s/it | [Iter  112/3090] R0[22/3000]   | LR: 0.024997 | E: -49.447566 | E_var:    23.8165 E_err:   0.076253 | NF_loss: 35.088830
[2025-11-13 01:05:04] 17:41<465:53, 9.39s/it | [Iter  113/3090] R0[23/3000]   | LR: 0.024997 | E: -49.663741 | E_var:    21.8042 E_err:   0.072961 | NF_loss: 32.053456
[2025-11-13 01:05:12] 17:48<465:04, 9.38s/it | [Iter  114/3090] R0[24/3000]   | LR: 0.024996 | E: -50.273691 | E_var:    21.8202 E_err:   0.072988 | NF_loss: 27.741799
[2025-11-13 01:05:20] 17:56<464:16, 9.36s/it | [Iter  115/3090] R0[25/3000]   | LR: 0.024996 | E: -50.668871 | E_var:    22.4177 E_err:   0.073980 | NF_loss: 32.954174
[2025-11-13 01:05:28] 18:04<463:28, 9.35s/it | [Iter  116/3090] R0[26/3000]   | LR: 0.024996 | E: -50.466446 | E_var:    24.1517 E_err:   0.076788 | NF_loss: 47.767787
[2025-11-13 01:05:36] 18:12<462:41, 9.34s/it | [Iter  117/3090] R0[27/3000]   | LR: 0.024995 | E: -51.360868 | E_var:    19.8193 E_err:   0.069561 | NF_loss: 42.245898
[2025-11-13 01:05:43] 18:20<461:55, 9.33s/it | [Iter  118/3090] R0[28/3000]   | LR: 0.024995 | E: -50.932241 | E_var:    19.6071 E_err:   0.069187 | NF_loss: 44.497972
[2025-11-13 01:05:51] 18:28<461:09, 9.31s/it | [Iter  119/3090] R0[29/3000]   | LR: 0.024995 | E: -51.302172 | E_var:    18.0864 E_err:   0.066450 | NF_loss: 53.746117
[2025-11-13 01:05:59] 18:36<460:25, 9.30s/it | [Iter  120/3090] R0[30/3000]   | LR: 0.024994 | E: -51.388628 | E_var:    18.8391 E_err:   0.067819 | NF_loss: 86.841236
[2025-11-13 01:06:07] 18:44<459:40, 9.29s/it | [Iter  121/3090] R0[31/3000]   | LR: 0.024994 | E: -51.483862 | E_var:    18.5921 E_err:   0.067373 | NF_loss: 112.575309
[2025-11-13 01:06:15] 18:51<458:56, 9.28s/it | [Iter  122/3090] R0[32/3000]   | LR: 0.024993 | E: -49.475566 | E_var:    21.8070 E_err:   0.072966 | NF_loss: 94.056898
[2025-11-13 01:06:23] 18:59<458:13, 9.27s/it | [Iter  123/3090] R0[33/3000]   | LR: 0.024993 | E: -49.983517 | E_var:    22.4010 E_err:   0.073953 | NF_loss: 179.150093
[2025-11-13 01:06:31] 19:07<457:30, 9.26s/it | [Iter  124/3090] R0[34/3000]   | LR: 0.024993 | E: -48.530961 | E_var:    24.0305 E_err:   0.076595 | NF_loss: 517.658006
[2025-11-13 01:06:38] 19:15<456:48, 9.24s/it | [Iter  125/3090] R0[35/3000]   | LR: 0.024992 | E: -49.821445 | E_var:    23.5545 E_err:   0.075833 | NF_loss: 121.198179
[2025-11-13 01:06:46] 19:23<456:06, 9.23s/it | [Iter  126/3090] R0[36/3000]   | LR: 0.024992 | E: -48.050990 | E_var:    24.7862 E_err:   0.077790 | NF_loss: 46.991248
[2025-11-13 01:06:54] 19:31<455:25, 9.22s/it | [Iter  127/3090] R0[37/3000]   | LR: 0.024991 | E: -44.281030 | E_var:    29.3537 E_err:   0.084655 | NF_loss: 31.778470
[2025-11-13 01:07:02] 19:39<454:51, 9.21s/it | [Iter  128/3090] R0[38/3000]   | LR: 0.024991 | E: -45.261436 | E_var:    27.6273 E_err:   0.082128 | NF_loss: 27.808363
[2025-11-13 01:07:10] 19:47<454:11, 9.20s/it | [Iter  129/3090] R0[39/3000]   | LR: 0.024990 | E: -47.684056 | E_var:    23.8091 E_err:   0.076241 | NF_loss: 26.425451
[2025-11-13 01:07:18] 19:55<453:32, 9.19s/it | [Iter  130/3090] R0[40/3000]   | LR: 0.024990 | E: -47.319284 | E_var:    23.9011 E_err:   0.076389 | NF_loss: 33.708766
[2025-11-13 01:07:26] 20:03<452:53, 9.18s/it | [Iter  131/3090] R0[41/3000]   | LR: 0.024989 | E: -50.183031 | E_var:    20.9165 E_err:   0.071460 | NF_loss: 27.379914
[2025-11-13 01:07:34] 20:10<452:14, 9.17s/it | [Iter  132/3090] R0[42/3000]   | LR: 0.024988 | E: -50.940913 | E_var:    19.8250 E_err:   0.069571 | NF_loss: 20.237067
[2025-11-13 01:07:42] 20:18<451:36, 9.16s/it | [Iter  133/3090] R0[43/3000]   | LR: 0.024988 | E: -50.734906 | E_var:    35.7301 E_err:   0.093398 | NF_loss: 30.420115
[2025-11-13 01:07:50] 20:26<450:58, 9.15s/it | [Iter  134/3090] R0[44/3000]   | LR: 0.024987 | E: -48.958240 | E_var:    44.6270 E_err:   0.104380 | NF_loss: 22.845608
[2025-11-13 01:07:57] 20:34<450:21, 9.14s/it | [Iter  135/3090] R0[45/3000]   | LR: 0.024987 | E: -50.560452 | E_var:    21.9824 E_err:   0.073258 | NF_loss: 28.279941
[2025-11-13 01:08:05] 20:42<449:44, 9.14s/it | [Iter  136/3090] R0[46/3000]   | LR: 0.024986 | E: -51.756164 | E_var:    19.2282 E_err:   0.068516 | NF_loss: 23.364831
[2025-11-13 01:08:13] 20:50<449:08, 9.13s/it | [Iter  137/3090] R0[47/3000]   | LR: 0.024986 | E: -52.668560 | E_var:    17.9833 E_err:   0.066261 | NF_loss: 30.854557
[2025-11-13 01:08:21] 20:58<448:32, 9.12s/it | [Iter  138/3090] R0[48/3000]   | LR: 0.024985 | E: -42.979528 | E_var:    70.7662 E_err:   0.131442 | NF_loss: 27.183339
[2025-11-13 01:08:29] 21:05<447:57, 9.11s/it | [Iter  139/3090] R0[49/3000]   | LR: 0.024984 | E: -44.358941 | E_var:    44.9880 E_err:   0.104802 | NF_loss: 34.590509
[2025-11-13 01:08:37] 21:13<447:22, 9.10s/it | [Iter  140/3090] R0[50/3000]   | LR: 0.024984 | E: -47.701170 | E_var:    26.4515 E_err:   0.080361 | NF_loss: 28.170670
[2025-11-13 01:08:45] 21:21<446:47, 9.09s/it | [Iter  141/3090] R0[51/3000]   | LR: 0.024983 | E: -47.176816 | E_var:    26.8379 E_err:   0.080946 | NF_loss: 35.453466
[2025-11-13 01:08:53] 21:29<446:12, 9.08s/it | [Iter  142/3090] R0[52/3000]   | LR: 0.024982 | E: -47.765242 | E_var:    23.6405 E_err:   0.075971 | NF_loss: 24.438574
[2025-11-13 01:09:00] 21:37<445:38, 9.07s/it | [Iter  143/3090] R0[53/3000]   | LR: 0.024981 | E: -49.250319 | E_var:    23.8762 E_err:   0.076349 | NF_loss: 21.713452
[2025-11-13 01:09:08] 21:45<445:05, 9.06s/it | [Iter  144/3090] R0[54/3000]   | LR: 0.024981 | E: -48.632276 | E_var:    22.7262 E_err:   0.074487 | NF_loss: 20.502574
[2025-11-13 01:09:16] 21:53<444:31, 9.06s/it | [Iter  145/3090] R0[55/3000]   | LR: 0.024980 | E: -48.875949 | E_var:    20.9956 E_err:   0.071595 | NF_loss: 31.266105
[2025-11-13 01:09:24] 22:01<443:59, 9.05s/it | [Iter  146/3090] R0[56/3000]   | LR: 0.024979 | E: -43.241041 | E_var:    28.8689 E_err:   0.083953 | NF_loss: 30.822973
[2025-11-13 01:09:32] 22:08<443:26, 9.04s/it | [Iter  147/3090] R0[57/3000]   | LR: 0.024979 | E: -50.226739 | E_var:    21.4271 E_err:   0.072327 | NF_loss: 27.592538
[2025-11-13 01:09:40] 22:16<442:53, 9.03s/it | [Iter  148/3090] R0[58/3000]   | LR: 0.024978 | E: -51.698684 | E_var:    17.5068 E_err:   0.065377 | NF_loss: 30.236395
[2025-11-13 01:09:48] 22:24<442:21, 9.02s/it | [Iter  149/3090] R0[59/3000]   | LR: 0.024977 | E: -52.353156 | E_var:    18.0163 E_err:   0.066321 | NF_loss: 23.218427
[2025-11-13 01:09:56] 22:32<441:50, 9.02s/it | [Iter  150/3090] R0[60/3000]   | LR: 0.024976 | E: -51.885173 | E_var:    16.0778 E_err:   0.062652 | NF_loss: 22.124941
[2025-11-13 01:10:03] 22:40<441:18, 9.01s/it | [Iter  151/3090] R0[61/3000]   | LR: 0.024975 | E: -53.430176 | E_var:    15.7973 E_err:   0.062103 | NF_loss: 21.311780
[2025-11-13 01:10:11] 22:48<440:47, 9.00s/it | [Iter  152/3090] R0[62/3000]   | LR: 0.024975 | E: -54.004441 | E_var:    16.4913 E_err:   0.063452 | NF_loss: 24.023230
[2025-11-13 01:10:19] 22:56<440:17, 8.99s/it | [Iter  153/3090] R0[63/3000]   | LR: 0.024974 | E: -53.531692 | E_var:    15.1393 E_err:   0.060796 | NF_loss: 19.127333
[2025-11-13 01:10:27] 23:04<439:46, 8.99s/it | [Iter  154/3090] R0[64/3000]   | LR: 0.024973 | E: -53.388425 | E_var:    16.5580 E_err:   0.063581 | NF_loss: 20.118172
[2025-11-13 01:10:35] 23:11<439:16, 8.98s/it | [Iter  155/3090] R0[65/3000]   | LR: 0.024972 | E: -53.652146 | E_var:    14.7344 E_err:   0.059977 | NF_loss: 18.850455
[2025-11-13 01:10:43] 23:19<438:46, 8.97s/it | [Iter  156/3090] R0[66/3000]   | LR: 0.024971 | E: -53.881753 | E_var:    17.4125 E_err:   0.065201 | NF_loss: 26.301486
[2025-11-13 01:10:51] 23:27<438:17, 8.97s/it | [Iter  157/3090] R0[67/3000]   | LR: 0.024970 | E: -52.097220 | E_var:    16.5247 E_err:   0.063517 | NF_loss: 40.862445
[2025-11-13 01:10:58] 23:35<437:47, 8.96s/it | [Iter  158/3090] R0[68/3000]   | LR: 0.024969 | E: -52.896508 | E_var:    15.1196 E_err:   0.060756 | NF_loss: 30.952329
[2025-11-13 01:11:06] 23:43<437:18, 8.95s/it | [Iter  159/3090] R0[69/3000]   | LR: 0.024968 | E: -53.171969 | E_var:    15.0394 E_err:   0.060595 | NF_loss: 29.054883
[2025-11-13 01:11:14] 23:51<436:54, 8.95s/it | [Iter  160/3090] R0[70/3000]   | LR: 0.024967 | E: -54.070136 | E_var:    14.0607 E_err:   0.058590 | NF_loss: 24.961881
[2025-11-13 01:11:22] 23:59<436:26, 8.94s/it | [Iter  161/3090] R0[71/3000]   | LR: 0.024966 | E: -49.125589 | E_var:    21.0137 E_err:   0.071626 | NF_loss: 21.605644
[2025-11-13 01:11:30] 24:07<435:58, 8.93s/it | [Iter  162/3090] R0[72/3000]   | LR: 0.024965 | E: -52.404204 | E_var:    15.7980 E_err:   0.062104 | NF_loss: 11.636377
[2025-11-13 01:11:38] 24:15<435:30, 8.93s/it | [Iter  163/3090] R0[73/3000]   | LR: 0.024964 | E: -53.805233 | E_var:    14.4472 E_err:   0.059390 | NF_loss: 32.901614
[2025-11-13 01:11:46] 24:23<435:02, 8.92s/it | [Iter  164/3090] R0[74/3000]   | LR: 0.024963 | E: -54.989501 | E_var:    14.6283 E_err:   0.059761 | NF_loss: 28.883744
[2025-11-13 01:11:54] 24:30<434:35, 8.91s/it | [Iter  165/3090] R0[75/3000]   | LR: 0.024962 | E: -55.940376 | E_var:    13.6544 E_err:   0.057737 | NF_loss: 32.505366
[2025-11-13 01:12:02] 24:38<434:07, 8.91s/it | [Iter  166/3090] R0[76/3000]   | LR: 0.024961 | E: -55.772687 | E_var:    14.9971 E_err:   0.060509 | NF_loss: 31.119592
[2025-11-13 01:12:10] 24:46<433:40, 8.90s/it | [Iter  167/3090] R0[77/3000]   | LR: 0.024960 | E: -55.392557 | E_var:    13.3975 E_err:   0.057192 | NF_loss: 26.361093
[2025-11-13 01:12:17] 24:54<433:13, 8.90s/it | [Iter  168/3090] R0[78/3000]   | LR: 0.024959 | E: -56.140520 | E_var:    12.8187 E_err:   0.055943 | NF_loss: 25.778929
[2025-11-13 01:12:25] 25:02<432:47, 8.89s/it | [Iter  169/3090] R0[79/3000]   | LR: 0.024958 | E: -56.506392 | E_var:    14.4012 E_err:   0.059295 | NF_loss: 37.745135
[2025-11-13 01:12:33] 25:10<432:20, 8.88s/it | [Iter  170/3090] R0[80/3000]   | LR: 0.024957 | E: -55.699748 | E_var:    18.4175 E_err:   0.067056 | NF_loss: 15.858247
[2025-11-13 01:12:41] 25:18<431:54, 8.88s/it | [Iter  171/3090] R0[81/3000]   | LR: 0.024956 | E: -55.854550 | E_var:    13.6233 E_err:   0.057672 | NF_loss: 19.470291
[2025-11-13 01:12:49] 25:25<431:28, 8.87s/it | [Iter  172/3090] R0[82/3000]   | LR: 0.024955 | E: -56.213712 | E_var:    12.4066 E_err:   0.055036 | NF_loss: 24.188609
[2025-11-13 01:12:57] 25:33<431:02, 8.87s/it | [Iter  173/3090] R0[83/3000]   | LR: 0.024954 | E: -55.341896 | E_var:    12.8761 E_err:   0.056068 | NF_loss: 24.331756
[2025-11-13 01:13:05] 25:41<430:37, 8.86s/it | [Iter  174/3090] R0[84/3000]   | LR: 0.024953 | E: -55.832658 | E_var:    12.9266 E_err:   0.056178 | NF_loss: 31.792806
[2025-11-13 01:13:13] 25:49<430:11, 8.85s/it | [Iter  175/3090] R0[85/3000]   | LR: 0.024952 | E: -56.525096 | E_var:    11.7142 E_err:   0.053478 | NF_loss: 23.348882
[2025-11-13 01:13:20] 25:57<429:46, 8.85s/it | [Iter  176/3090] R0[86/3000]   | LR: 0.024951 | E: -56.333619 | E_var:    11.7593 E_err:   0.053581 | NF_loss: 27.869119
[2025-11-13 01:13:28] 26:05<429:21, 8.84s/it | [Iter  177/3090] R0[87/3000]   | LR: 0.024949 | E: -56.973023 | E_var:    13.4419 E_err:   0.057286 | NF_loss: 25.276887
[2025-11-13 01:13:36] 26:13<428:57, 8.84s/it | [Iter  178/3090] R0[88/3000]   | LR: 0.024948 | E: -56.546613 | E_var:    11.6171 E_err:   0.053256 | NF_loss: 31.897765
[2025-11-13 01:13:44] 26:21<428:32, 8.83s/it | [Iter  179/3090] R0[89/3000]   | LR: 0.024947 | E: -57.496262 | E_var:    11.7488 E_err:   0.053557 | NF_loss: 27.518792
[2025-11-13 01:13:52] 26:28<428:07, 8.83s/it | [Iter  180/3090] R0[90/3000]   | LR: 0.024946 | E: -57.432370 | E_var:    11.3734 E_err:   0.052694 | NF_loss: 22.024465
[2025-11-13 01:14:00] 26:36<427:43, 8.82s/it | [Iter  181/3090] R0[91/3000]   | LR: 0.024945 | E: -57.765901 | E_var:    11.5174 E_err:   0.053027 | NF_loss: 23.151518
[2025-11-13 01:14:08] 26:44<427:19, 8.82s/it | [Iter  182/3090] R0[92/3000]   | LR: 0.024943 | E: -57.488307 | E_var:    14.1657 E_err:   0.058808 | NF_loss: 25.935228
[2025-11-13 01:14:16] 26:52<426:55, 8.81s/it | [Iter  183/3090] R0[93/3000]   | LR: 0.024942 | E: -51.169073 | E_var:    16.7683 E_err:   0.063983 | NF_loss: 29.008671
[2025-11-13 01:14:23] 27:00<426:31, 8.81s/it | [Iter  184/3090] R0[94/3000]   | LR: 0.024941 | E: -54.195892 | E_var:    12.9549 E_err:   0.056239 | NF_loss: 29.471600
[2025-11-13 01:14:31] 27:08<426:08, 8.80s/it | [Iter  185/3090] R0[95/3000]   | LR: 0.024939 | E: -55.986982 | E_var:    12.0051 E_err:   0.054138 | NF_loss: 27.736168
[2025-11-13 01:14:39] 27:16<425:45, 8.80s/it | [Iter  186/3090] R0[96/3000]   | LR: 0.024938 | E: -56.574240 | E_var:    11.2800 E_err:   0.052478 | NF_loss: 26.767029
[2025-11-13 01:14:47] 27:24<425:22, 8.79s/it | [Iter  187/3090] R0[97/3000]   | LR: 0.024937 | E: -56.207515 | E_var:    16.3580 E_err:   0.063195 | NF_loss: 27.783902
[2025-11-13 01:14:55] 27:31<424:58, 8.79s/it | [Iter  188/3090] R0[98/3000]   | LR: 0.024936 | E: -56.228314 | E_var:    11.8198 E_err:   0.053719 | NF_loss: 26.827151
[2025-11-13 01:15:03] 27:39<424:36, 8.78s/it | [Iter  189/3090] R0[99/3000]   | LR: 0.024934 | E: -56.914621 | E_var:    12.1990 E_err:   0.054574 | NF_loss: 24.382508
[2025-11-13 01:15:11] 27:47<424:13, 8.78s/it | [Iter  190/3090] R0[100/3000]  | LR: 0.024933 | E: -56.734477 | E_var:    11.0459 E_err:   0.051930 | NF_loss: 26.752885
[2025-11-13 01:15:18] 27:55<423:50, 8.77s/it | [Iter  191/3090] R0[101/3000]  | LR: 0.024932 | E: -56.287645 | E_var:    11.3413 E_err:   0.052620 | NF_loss: 31.842666
[2025-11-13 01:15:26] 28:03<423:28, 8.77s/it | [Iter  192/3090] R0[102/3000]  | LR: 0.024930 | E: -55.961533 | E_var:    14.9134 E_err:   0.060340 | NF_loss: 28.786120
[2025-11-13 01:15:34] 28:11<423:05, 8.76s/it | [Iter  193/3090] R0[103/3000]  | LR: 0.024929 | E: -54.994210 | E_var:    20.2166 E_err:   0.070255 | NF_loss: 22.684409
[2025-11-13 01:15:42] 28:19<422:44, 8.76s/it | [Iter  194/3090] R0[104/3000]  | LR: 0.024927 | E: -54.814663 | E_var:    17.2527 E_err:   0.064901 | NF_loss: 26.926582
[2025-11-13 01:15:50] 28:26<422:22, 8.75s/it | [Iter  195/3090] R0[105/3000]  | LR: 0.024926 | E: -56.569875 | E_var:    12.2734 E_err:   0.054740 | NF_loss: 28.227652
[2025-11-13 01:15:58] 28:34<422:00, 8.75s/it | [Iter  196/3090] R0[106/3000]  | LR: 0.024925 | E: -57.213285 | E_var:    12.7726 E_err:   0.055842 | NF_loss: 27.881991
[2025-11-13 01:16:06] 28:42<421:38, 8.74s/it | [Iter  197/3090] R0[107/3000]  | LR: 0.024923 | E: -57.237750 | E_var:    11.7706 E_err:   0.053607 | NF_loss: 28.819452
[2025-11-13 01:16:14] 28:50<421:17, 8.74s/it | [Iter  198/3090] R0[108/3000]  | LR: 0.024922 | E: -57.510264 | E_var:    10.8713 E_err:   0.051518 | NF_loss: 29.223177
[2025-11-13 01:16:21] 28:58<420:55, 8.74s/it | [Iter  199/3090] R0[109/3000]  | LR: 0.024920 | E: -57.353592 | E_var:    11.8296 E_err:   0.053741 | NF_loss: 26.921267
[2025-11-13 01:16:29] 29:06<420:34, 8.73s/it | [Iter  200/3090] R0[110/3000]  | LR: 0.024919 | E: -56.697512 | E_var:    17.8721 E_err:   0.066055 | NF_loss: 32.660791
[2025-11-13 01:16:37] 29:14<420:13, 8.73s/it | [Iter  201/3090] R0[111/3000]  | LR: 0.024917 | E: -55.767998 | E_var:    28.2183 E_err:   0.083001 | NF_loss: 27.168887
[2025-11-13 01:16:45] 29:22<419:52, 8.72s/it | [Iter  202/3090] R0[112/3000]  | LR: 0.024916 | E: -57.225339 | E_var:    11.2829 E_err:   0.052485 | NF_loss: 28.833865
[2025-11-13 01:16:53] 29:29<419:31, 8.72s/it | [Iter  203/3090] R0[113/3000]  | LR: 0.024914 | E: -57.501376 | E_var:    10.8018 E_err:   0.051353 | NF_loss: 27.811009
[2025-11-13 01:17:01] 29:37<419:10, 8.71s/it | [Iter  204/3090] R0[114/3000]  | LR: 0.024913 | E: -57.332498 | E_var:    11.1784 E_err:   0.052241 | NF_loss: 29.939014
[2025-11-13 01:17:09] 29:45<418:53, 8.71s/it | [Iter  205/3090] R0[115/3000]  | LR: 0.024911 | E: -57.876435 | E_var:    10.3652 E_err:   0.050305 | NF_loss: 32.024677
[2025-11-13 01:17:17] 29:53<418:33, 8.71s/it | [Iter  206/3090] R0[116/3000]  | LR: 0.024909 | E: -58.249652 | E_var:    11.1705 E_err:   0.052222 | NF_loss: 33.435085
[2025-11-13 01:17:25] 30:01<418:13, 8.70s/it | [Iter  207/3090] R0[117/3000]  | LR: 0.024908 | E: -57.333232 | E_var:    17.1906 E_err:   0.064784 | NF_loss: 27.269777
[2025-11-13 01:17:33] 30:09<417:52, 8.70s/it | [Iter  208/3090] R0[118/3000]  | LR: 0.024906 | E: -57.580988 | E_var:    11.9392 E_err:   0.053989 | NF_loss: 26.579530
[2025-11-13 01:17:40] 30:17<417:32, 8.70s/it | [Iter  209/3090] R0[119/3000]  | LR: 0.024905 | E: -58.090839 | E_var:    10.0781 E_err:   0.049603 | NF_loss: 45.943646
[2025-11-13 01:17:48] 30:25<417:12, 8.69s/it | [Iter  210/3090] R0[120/3000]  | LR: 0.024903 | E: -58.024135 | E_var:    10.2432 E_err:   0.050008 | NF_loss: 37.614422
[2025-11-13 01:17:56] 30:33<416:52, 8.69s/it | [Iter  211/3090] R0[121/3000]  | LR: 0.024901 | E: -57.847126 | E_var:    11.0506 E_err:   0.051941 | NF_loss: 71.835453
[2025-11-13 01:18:04] 30:41<416:33, 8.68s/it | [Iter  212/3090] R0[122/3000]  | LR: 0.024900 | E: -57.603119 | E_var:    13.3197 E_err:   0.057025 | NF_loss: 35.993745
[2025-11-13 01:18:12] 30:48<416:13, 8.68s/it | [Iter  213/3090] R0[123/3000]  | LR: 0.024898 | E: -57.870534 | E_var:    11.8161 E_err:   0.053710 | NF_loss: 38.375733
[2025-11-13 01:18:20] 30:56<415:53, 8.68s/it | [Iter  214/3090] R0[124/3000]  | LR: 0.024896 | E: -57.557355 | E_var:    10.5402 E_err:   0.050728 | NF_loss: 66.170793
[2025-11-13 01:18:28] 31:04<415:34, 8.67s/it | [Iter  215/3090] R0[125/3000]  | LR: 0.024895 | E: -57.915118 | E_var:    11.3519 E_err:   0.052645 | NF_loss: 53.717887
[2025-11-13 01:18:36] 31:12<415:15, 8.67s/it | [Iter  216/3090] R0[126/3000]  | LR: 0.024893 | E: -57.732114 | E_var:    10.5667 E_err:   0.050791 | NF_loss: 108.465647
[2025-11-13 01:18:43] 31:20<414:55, 8.67s/it | [Iter  217/3090] R0[127/3000]  | LR: 0.024891 | E: -57.553571 | E_var:    10.5952 E_err:   0.050860 | NF_loss: 134.142956
[2025-11-13 01:18:51] 31:28<414:36, 8.66s/it | [Iter  218/3090] R0[128/3000]  | LR: 0.024890 | E: -57.721280 | E_var:    12.5611 E_err:   0.055378 | NF_loss: 214.440497
[2025-11-13 01:18:59] 31:36<414:17, 8.66s/it | [Iter  219/3090] R0[129/3000]  | LR: 0.024888 | E: -57.614079 | E_var:    11.1341 E_err:   0.052137 | NF_loss: 41.861992
[2025-11-13 01:19:07] 31:44<413:58, 8.65s/it | [Iter  220/3090] R0[130/3000]  | LR: 0.024886 | E: -57.916032 | E_var:    12.1374 E_err:   0.054436 | NF_loss: 53.262684
[2025-11-13 01:19:15] 31:51<413:39, 8.65s/it | [Iter  221/3090] R0[131/3000]  | LR: 0.024884 | E: -58.063679 | E_var:     9.8392 E_err:   0.049012 | NF_loss: 71.882982
[2025-11-13 01:19:23] 31:59<413:20, 8.65s/it | [Iter  222/3090] R0[132/3000]  | LR: 0.024883 | E: -57.834227 | E_var:    13.0439 E_err:   0.056432 | NF_loss: 76.859765
[2025-11-13 01:19:31] 32:07<413:02, 8.64s/it | [Iter  223/3090] R0[133/3000]  | LR: 0.024881 | E: -57.022795 | E_var:    11.9376 E_err:   0.053986 | NF_loss: 41.478496
[2025-11-13 01:19:38] 32:15<412:43, 8.64s/it | [Iter  224/3090] R0[134/3000]  | LR: 0.024879 | E: -56.387220 | E_var:    13.3167 E_err:   0.057019 | NF_loss: 58.912629
[2025-11-13 01:19:46] 32:23<412:25, 8.64s/it | [Iter  225/3090] R0[135/3000]  | LR: 0.024877 | E: -56.608003 | E_var:    13.4597 E_err:   0.057324 | NF_loss: 52.670707
[2025-11-13 01:19:54] 32:31<412:07, 8.63s/it | [Iter  226/3090] R0[136/3000]  | LR: 0.024875 | E: -56.967249 | E_var:    12.1084 E_err:   0.054370 | NF_loss: 38.237958
[2025-11-13 01:20:02] 32:39<411:48, 8.63s/it | [Iter  227/3090] R0[137/3000]  | LR: 0.024873 | E: -56.571305 | E_var:    12.3774 E_err:   0.054971 | NF_loss: 32.825465
[2025-11-13 01:20:10] 32:46<411:30, 8.63s/it | [Iter  228/3090] R0[138/3000]  | LR: 0.024872 | E: -57.233695 | E_var:    12.6483 E_err:   0.055569 | NF_loss: 27.414241
[2025-11-13 01:20:18] 32:54<411:12, 8.62s/it | [Iter  229/3090] R0[139/3000]  | LR: 0.024870 | E: -56.991672 | E_var:    12.1960 E_err:   0.054567 | NF_loss: 27.733030
[2025-11-13 01:20:26] 33:02<410:54, 8.62s/it | [Iter  230/3090] R0[140/3000]  | LR: 0.024868 | E: -56.984476 | E_var:    11.9063 E_err:   0.053915 | NF_loss: 24.541827
[2025-11-13 01:20:34] 33:10<410:36, 8.62s/it | [Iter  231/3090] R0[141/3000]  | LR: 0.024866 | E: -57.196948 | E_var:    11.3198 E_err:   0.052570 | NF_loss: 27.029835
[2025-11-13 01:20:41] 33:18<410:18, 8.61s/it | [Iter  232/3090] R0[142/3000]  | LR: 0.024864 | E: -56.377736 | E_var:    12.2801 E_err:   0.054755 | NF_loss: 35.992295
[2025-11-13 01:20:49] 33:26<410:00, 8.61s/it | [Iter  233/3090] R0[143/3000]  | LR: 0.024862 | E: -56.501003 | E_var:    13.5706 E_err:   0.057560 | NF_loss: 7.997829
[2025-11-13 01:20:57] 33:34<409:43, 8.61s/it | [Iter  234/3090] R0[144/3000]  | LR: 0.024860 | E: -56.370660 | E_var:    12.3410 E_err:   0.054890 | NF_loss: 28.338725
[2025-11-13 01:21:05] 33:42<409:25, 8.60s/it | [Iter  235/3090] R0[145/3000]  | LR: 0.024858 | E: -57.020555 | E_var:    11.7773 E_err:   0.053622 | NF_loss: 29.136124
[2025-11-13 01:21:13] 33:49<409:08, 8.60s/it | [Iter  236/3090] R0[146/3000]  | LR: 0.024856 | E: -57.519805 | E_var:    11.4536 E_err:   0.052880 | NF_loss: 55.032105
[2025-11-13 01:21:21] 33:57<408:50, 8.60s/it | [Iter  237/3090] R0[147/3000]  | LR: 0.024854 | E: -57.573653 | E_var:    11.4947 E_err:   0.052975 | NF_loss: 36.328669
[2025-11-13 01:21:29] 34:05<408:33, 8.60s/it | [Iter  238/3090] R0[148/3000]  | LR: 0.024852 | E: -58.047719 | E_var:    11.1298 E_err:   0.052127 | NF_loss: 38.909427
[2025-11-13 01:21:36] 34:13<408:16, 8.59s/it | [Iter  239/3090] R0[149/3000]  | LR: 0.024850 | E: -58.227332 | E_var:     9.8975 E_err:   0.049157 | NF_loss: 20.880267
[2025-11-13 01:21:44] 34:21<407:58, 8.59s/it | [Iter  240/3090] R0[150/3000]  | LR: 0.024848 | E: -58.692740 | E_var:     9.7130 E_err:   0.048696 | NF_loss: 27.989718
[2025-11-13 01:21:52] 34:29<407:44, 8.59s/it | [Iter  241/3090] R0[151/3000]  | LR: 0.024846 | E: -58.546951 | E_var:    10.3862 E_err:   0.050356 | NF_loss: 25.259837
[2025-11-13 01:22:00] 34:37<407:28, 8.58s/it | [Iter  242/3090] R0[152/3000]  | LR: 0.024844 | E: -58.248222 | E_var:     9.8455 E_err:   0.049027 | NF_loss: 31.360761
[2025-11-13 01:22:08] 34:45<407:10, 8.58s/it | [Iter  243/3090] R0[153/3000]  | LR: 0.024842 | E: -58.503496 | E_var:    10.8986 E_err:   0.051583 | NF_loss: 32.725275
[2025-11-13 01:22:16] 34:53<406:54, 8.58s/it | [Iter  244/3090] R0[154/3000]  | LR: 0.024840 | E: -59.177391 | E_var:    11.0562 E_err:   0.051954 | NF_loss: 31.383148
[2025-11-13 01:22:24] 35:01<406:37, 8.58s/it | [Iter  245/3090] R0[155/3000]  | LR: 0.024838 | E: -59.329884 | E_var:     9.5265 E_err:   0.048227 | NF_loss: 37.235917
[2025-11-13 01:22:32] 35:08<406:20, 8.57s/it | [Iter  246/3090] R0[156/3000]  | LR: 0.024836 | E: -58.751414 | E_var:     9.3978 E_err:   0.047900 | NF_loss: 27.109340
[2025-11-13 01:22:40] 35:16<406:03, 8.57s/it | [Iter  247/3090] R0[157/3000]  | LR: 0.024834 | E: -58.964231 | E_var:     9.5496 E_err:   0.048285 | NF_loss: 42.208473
[2025-11-13 01:22:48] 35:24<405:47, 8.57s/it | [Iter  248/3090] R0[158/3000]  | LR: 0.024831 | E: -58.718035 | E_var:     9.4711 E_err:   0.048086 | NF_loss: 29.362064
[2025-11-13 01:22:55] 35:32<405:31, 8.56s/it | [Iter  249/3090] R0[159/3000]  | LR: 0.024829 | E: -58.612449 | E_var:     9.3629 E_err:   0.047811 | NF_loss: 26.216504
[2025-11-13 01:23:03] 35:40<405:14, 8.56s/it | [Iter  250/3090] R0[160/3000]  | LR: 0.024827 | E: -59.280410 | E_var:     8.8246 E_err:   0.046416 | NF_loss: 38.960222
[2025-11-13 01:23:11] 35:48<404:58, 8.56s/it | [Iter  251/3090] R0[161/3000]  | LR: 0.024825 | E: -59.483589 | E_var:     9.1040 E_err:   0.047145 | NF_loss: 32.864475
[2025-11-13 01:23:19] 35:56<404:42, 8.56s/it | [Iter  252/3090] R0[162/3000]  | LR: 0.024823 | E: -59.572580 | E_var:     9.0339 E_err:   0.046963 | NF_loss: 39.939675
[2025-11-13 01:23:27] 36:04<404:26, 8.55s/it | [Iter  253/3090] R0[163/3000]  | LR: 0.024821 | E: -58.846945 | E_var:     9.3950 E_err:   0.047893 | NF_loss: 58.331355
[2025-11-13 01:23:35] 36:11<404:09, 8.55s/it | [Iter  254/3090] R0[164/3000]  | LR: 0.024818 | E: -58.673184 | E_var:     9.3524 E_err:   0.047784 | NF_loss: 143.867720
[2025-11-13 01:23:43] 36:19<403:53, 8.55s/it | [Iter  255/3090] R0[165/3000]  | LR: 0.024816 | E: -59.022571 | E_var:     8.9310 E_err:   0.046695 | NF_loss: 170.047413
[2025-11-13 01:23:51] 36:27<403:37, 8.55s/it | [Iter  256/3090] R0[166/3000]  | LR: 0.024814 | E: -59.116652 | E_var:     9.2710 E_err:   0.047576 | NF_loss: 181.179509
[2025-11-13 01:23:58] 36:35<403:21, 8.54s/it | [Iter  257/3090] R0[167/3000]  | LR: 0.024812 | E: -58.597398 | E_var:    10.1635 E_err:   0.049813 | NF_loss: 57.810092
[2025-11-13 01:24:06] 36:43<403:05, 8.54s/it | [Iter  258/3090] R0[168/3000]  | LR: 0.024809 | E: -58.603512 | E_var:     9.7232 E_err:   0.048722 | NF_loss: 32.392970
[2025-11-13 01:24:14] 36:51<402:49, 8.54s/it | [Iter  259/3090] R0[169/3000]  | LR: 0.024807 | E: -58.144691 | E_var:    10.2873 E_err:   0.050115 | NF_loss: 162.222961
[2025-11-13 01:24:22] 36:59<402:34, 8.54s/it | [Iter  260/3090] R0[170/3000]  | LR: 0.024805 | E: -58.428040 | E_var:     9.7598 E_err:   0.048814 | NF_loss: 28.494278
[2025-11-13 01:24:30] 37:06<402:18, 8.53s/it | [Iter  261/3090] R0[171/3000]  | LR: 0.024802 | E: -58.075660 | E_var:    10.1426 E_err:   0.049762 | NF_loss: 34.858778
[2025-11-13 01:24:38] 37:14<402:02, 8.53s/it | [Iter  262/3090] R0[172/3000]  | LR: 0.024800 | E: -59.065851 | E_var:     9.0171 E_err:   0.046919 | NF_loss: 8.806073
[2025-11-13 01:24:46] 37:22<401:46, 8.53s/it | [Iter  263/3090] R0[173/3000]  | LR: 0.024798 | E: -59.249394 | E_var:     8.8737 E_err:   0.046545 | NF_loss: 29.709071
[2025-11-13 01:24:54] 37:30<401:31, 8.52s/it | [Iter  264/3090] R0[174/3000]  | LR: 0.024795 | E: -58.243981 | E_var:    10.3309 E_err:   0.050221 | NF_loss: 41.030453
[2025-11-13 01:25:01] 37:38<401:15, 8.52s/it | [Iter  265/3090] R0[175/3000]  | LR: 0.024793 | E: -57.361063 | E_var:    11.7081 E_err:   0.053464 | NF_loss: 43.677392
[2025-11-13 01:25:09] 37:46<401:00, 8.52s/it | [Iter  266/3090] R0[176/3000]  | LR: 0.024791 | E: -57.156925 | E_var:    11.9231 E_err:   0.053953 | NF_loss: 34.194456
[2025-11-13 01:25:17] 37:54<400:45, 8.52s/it | [Iter  267/3090] R0[177/3000]  | LR: 0.024788 | E: -58.546529 | E_var:    10.0264 E_err:   0.049476 | NF_loss: 36.226980
[2025-11-13 01:25:25] 38:02<400:29, 8.52s/it | [Iter  268/3090] R0[178/3000]  | LR: 0.024786 | E: -59.039933 | E_var:     9.0741 E_err:   0.047068 | NF_loss: 37.159292
[2025-11-13 01:25:33] 38:10<400:17, 8.51s/it | [Iter  269/3090] R0[179/3000]  | LR: 0.024783 | E: -59.591996 | E_var:     8.4786 E_err:   0.045497 | NF_loss: 105.643441
[2025-11-13 01:25:41] 38:18<400:02, 8.51s/it | [Iter  270/3090] R0[180/3000]  | LR: 0.024781 | E: -59.563967 | E_var:     8.5491 E_err:   0.045686 | NF_loss: 228.432359
[2025-11-13 01:25:49] 38:25<399:47, 8.51s/it | [Iter  271/3090] R0[181/3000]  | LR: 0.024779 | E: -59.649400 | E_var:     8.6210 E_err:   0.045877 | NF_loss: 399.364066
[2025-11-13 01:25:57] 38:33<399:32, 8.51s/it | [Iter  272/3090] R0[182/3000]  | LR: 0.024776 | E: -59.582487 | E_var:     8.5652 E_err:   0.045729 | NF_loss: 152.419545
[2025-11-13 01:26:05] 38:41<399:16, 8.50s/it | [Iter  273/3090] R0[183/3000]  | LR: 0.024774 | E: -59.908029 | E_var:     8.6792 E_err:   0.046032 | NF_loss: 1680.205360
[2025-11-13 01:26:13] 38:49<399:01, 8.50s/it | [Iter  274/3090] R0[184/3000]  | LR: 0.024771 | E: -59.736888 | E_var:     8.8755 E_err:   0.046550 | NF_loss: 338.337522
[2025-11-13 01:26:20] 38:57<398:47, 8.50s/it | [Iter  275/3090] R0[185/3000]  | LR: 0.024769 | E: -60.001894 | E_var:     8.7701 E_err:   0.046272 | NF_loss: 166.991755
[2025-11-13 01:26:28] 39:05<398:32, 8.50s/it | [Iter  276/3090] R0[186/3000]  | LR: 0.024766 | E: -60.192607 | E_var:     8.6611 E_err:   0.045984 | NF_loss: 206.314714
[2025-11-13 01:26:36] 39:13<398:17, 8.50s/it | [Iter  277/3090] R0[187/3000]  | LR: 0.024764 | E: -60.063655 | E_var:     8.5127 E_err:   0.045588 | NF_loss: 22.106100
[2025-11-13 01:26:44] 39:21<398:02, 8.49s/it | [Iter  278/3090] R0[188/3000]  | LR: 0.024761 | E: -59.991240 | E_var:     9.2915 E_err:   0.047628 | NF_loss: 42.743901
[2025-11-13 01:26:52] 39:28<397:47, 8.49s/it | [Iter  279/3090] R0[189/3000]  | LR: 0.024759 | E: -59.916471 | E_var:     8.8453 E_err:   0.046470 | NF_loss: 102.434522
[2025-11-13 01:27:00] 39:36<397:32, 8.49s/it | [Iter  280/3090] R0[190/3000]  | LR: 0.024756 | E: -60.116550 | E_var:     8.3445 E_err:   0.045136 | NF_loss: 47.094500
[2025-11-13 01:27:08] 39:44<397:18, 8.49s/it | [Iter  281/3090] R0[191/3000]  | LR: 0.024753 | E: -59.931093 | E_var:     8.0760 E_err:   0.044403 | NF_loss: 63.889341
[2025-11-13 01:27:16] 39:52<397:03, 8.48s/it | [Iter  282/3090] R0[192/3000]  | LR: 0.024751 | E: -60.250836 | E_var:     9.6609 E_err:   0.048566 | NF_loss: 31.917859
[2025-11-13 01:27:23] 40:00<396:49, 8.48s/it | [Iter  283/3090] R0[193/3000]  | LR: 0.024748 | E: -60.159456 | E_var:     8.4625 E_err:   0.045454 | NF_loss: 51.159687
[2025-11-13 01:27:31] 40:08<396:34, 8.48s/it | [Iter  284/3090] R0[194/3000]  | LR: 0.024746 | E: -60.388866 | E_var:     8.8011 E_err:   0.046354 | NF_loss: 81.510239
[2025-11-13 01:27:39] 40:16<396:20, 8.48s/it | [Iter  285/3090] R0[195/3000]  | LR: 0.024743 | E: -60.401931 | E_var:     9.2197 E_err:   0.047444 | NF_loss: 47.581450
[2025-11-13 01:27:47] 40:24<396:05, 8.48s/it | [Iter  286/3090] R0[196/3000]  | LR: 0.024740 | E: -60.269220 | E_var:     8.1263 E_err:   0.044542 | NF_loss: 33.898532
[2025-11-13 01:27:55] 40:31<395:51, 8.47s/it | [Iter  287/3090] R0[197/3000]  | LR: 0.024738 | E: -60.445525 | E_var:     8.4036 E_err:   0.045295 | NF_loss: 44.831359
[2025-11-13 01:28:03] 40:39<395:37, 8.47s/it | [Iter  288/3090] R0[198/3000]  | LR: 0.024735 | E: -60.239030 | E_var:     7.8187 E_err:   0.043691 | NF_loss: 44.153271
[2025-11-13 01:28:11] 40:47<395:22, 8.47s/it | [Iter  289/3090] R0[199/3000]  | LR: 0.024732 | E: -59.818753 | E_var:     8.5011 E_err:   0.045557 | NF_loss: 38.848655
[2025-11-13 01:28:19] 40:55<395:08, 8.47s/it | [Iter  290/3090] R0[200/3000]  | LR: 0.024730 | E: -59.776618 | E_var:     8.6045 E_err:   0.045834 | NF_loss: 34.290564
[2025-11-13 01:28:26] 41:03<394:54, 8.47s/it | [Iter  291/3090] R0[201/3000]  | LR: 0.024727 | E: -59.203229 | E_var:     8.9568 E_err:   0.046762 | NF_loss: 48.157727
[2025-11-13 01:28:34] 41:11<394:40, 8.46s/it | [Iter  292/3090] R0[202/3000]  | LR: 0.024724 | E: -59.651310 | E_var:     8.3309 E_err:   0.045099 | NF_loss: 23.888388
[2025-11-13 01:28:42] 41:19<394:26, 8.46s/it | [Iter  293/3090] R0[203/3000]  | LR: 0.024721 | E: -59.546377 | E_var:     8.9913 E_err:   0.046852 | NF_loss: 38.892444
[2025-11-13 01:28:50] 41:27<394:12, 8.46s/it | [Iter  294/3090] R0[204/3000]  | LR: 0.024719 | E: -59.776043 | E_var:     9.3342 E_err:   0.047737 | NF_loss: 40.171717
[2025-11-13 01:28:58] 41:34<393:58, 8.46s/it | [Iter  295/3090] R0[205/3000]  | LR: 0.024716 | E: -59.908397 | E_var:     8.1122 E_err:   0.044503 | NF_loss: 29.650513
[2025-11-13 01:29:06] 41:42<393:44, 8.46s/it | [Iter  296/3090] R0[206/3000]  | LR: 0.024713 | E: -60.048656 | E_var:     7.7244 E_err:   0.043426 | NF_loss: 28.642498
[2025-11-13 01:29:14] 41:50<393:30, 8.45s/it | [Iter  297/3090] R0[207/3000]  | LR: 0.024710 | E: -60.196335 | E_var:     7.6335 E_err:   0.043170 | NF_loss: 26.954551
[2025-11-13 01:29:21] 41:58<393:16, 8.45s/it | [Iter  298/3090] R0[208/3000]  | LR: 0.024707 | E: -60.412469 | E_var:     7.7795 E_err:   0.043581 | NF_loss: 25.241512
[2025-11-13 01:29:29] 42:06<393:02, 8.45s/it | [Iter  299/3090] R0[209/3000]  | LR: 0.024705 | E: -60.648624 | E_var:     8.7012 E_err:   0.046090 | NF_loss: 24.838747
[2025-11-13 01:29:37] 42:14<392:48, 8.45s/it | [Iter  300/3090] R0[210/3000]  | LR: 0.024702 | E: -60.454461 | E_var:     7.3504 E_err:   0.042362 | NF_loss: 24.326660
[2025-11-13 01:29:37] 保存checkpoint: hybrid_checkpoint_iter_000300.pkl
[2025-11-13 01:29:45] 42:22<392:36, 8.45s/it | [Iter  301/3090] R0[211/3000]  | LR: 0.024699 | E: -60.427959 | E_var:     7.9767 E_err:   0.044130 | NF_loss: 23.279124
[2025-11-13 01:29:53] 42:30<392:22, 8.44s/it | [Iter  302/3090] R0[212/3000]  | LR: 0.024696 | E: -60.655820 | E_var:    10.7842 E_err:   0.051311 | NF_loss: 94.868978
[2025-11-13 01:30:01] 42:38<392:08, 8.44s/it | [Iter  303/3090] R0[213/3000]  | LR: 0.024693 | E: -60.458735 | E_var:     8.2970 E_err:   0.045007 | NF_loss: 23.154396
[2025-11-13 01:30:09] 42:45<391:55, 8.44s/it | [Iter  304/3090] R0[214/3000]  | LR: 0.024690 | E: -60.636145 | E_var:     7.0474 E_err:   0.041480 | NF_loss: 51.280446
[2025-11-13 01:30:17] 42:54<391:43, 8.44s/it | [Iter  305/3090] R0[215/3000]  | LR: 0.024687 | E: -60.727283 | E_var:     8.9777 E_err:   0.046817 | NF_loss: 24.643687
[2025-11-13 01:30:25] 43:01<391:30, 8.44s/it | [Iter  306/3090] R0[216/3000]  | LR: 0.024685 | E: -60.801097 | E_var:     9.2840 E_err:   0.047609 | NF_loss: 33.222475
[2025-11-13 01:30:33] 43:09<391:16, 8.44s/it | [Iter  307/3090] R0[217/3000]  | LR: 0.024682 | E: -60.812113 | E_var:     8.9034 E_err:   0.046623 | NF_loss: 190.577262
[2025-11-13 01:30:41] 43:17<391:03, 8.43s/it | [Iter  308/3090] R0[218/3000]  | LR: 0.024679 | E: -60.716388 | E_var:     8.7324 E_err:   0.046173 | NF_loss: 266.758063
[2025-11-13 01:30:48] 43:25<390:49, 8.43s/it | [Iter  309/3090] R0[219/3000]  | LR: 0.024676 | E: -60.486669 | E_var:     9.7336 E_err:   0.048748 | NF_loss: 25.492350
[2025-11-13 01:30:56] 43:33<390:36, 8.43s/it | [Iter  310/3090] R0[220/3000]  | LR: 0.024673 | E: -60.805906 | E_var:     8.0831 E_err:   0.044423 | NF_loss: 41.802075
[2025-11-13 01:31:04] 43:41<390:22, 8.43s/it | [Iter  311/3090] R0[221/3000]  | LR: 0.024670 | E: -60.812883 | E_var:     7.2382 E_err:   0.042037 | NF_loss: 30.236074
[2025-11-13 01:31:12] 43:49<390:09, 8.43s/it | [Iter  312/3090] R0[222/3000]  | LR: 0.024667 | E: -60.839319 | E_var:     7.8726 E_err:   0.043841 | NF_loss: 40.616293
[2025-11-13 01:31:20] 43:56<389:55, 8.42s/it | [Iter  313/3090] R0[223/3000]  | LR: 0.024664 | E: -60.812504 | E_var:     9.5618 E_err:   0.048316 | NF_loss: 53.514142
[2025-11-13 01:31:28] 44:04<389:42, 8.42s/it | [Iter  314/3090] R0[224/3000]  | LR: 0.024661 | E: -60.762108 | E_var:     8.1846 E_err:   0.044701 | NF_loss: 27.909333
[2025-11-13 01:31:36] 44:12<389:29, 8.42s/it | [Iter  315/3090] R0[225/3000]  | LR: 0.024658 | E: -60.847373 | E_var:     8.6073 E_err:   0.045841 | NF_loss: 62.878150
[2025-11-13 01:31:44] 44:20<389:16, 8.42s/it | [Iter  316/3090] R0[226/3000]  | LR: 0.024655 | E: -60.864753 | E_var:     8.6910 E_err:   0.046063 | NF_loss: 46.991962
[2025-11-13 01:31:51] 44:28<389:02, 8.42s/it | [Iter  317/3090] R0[227/3000]  | LR: 0.024652 | E: -60.814955 | E_var:     8.5701 E_err:   0.045742 | NF_loss: 40.517953
[2025-11-13 01:31:59] 44:36<388:49, 8.42s/it | [Iter  318/3090] R0[228/3000]  | LR: 0.024648 | E: -60.685246 | E_var:     8.5658 E_err:   0.045730 | NF_loss: 55.703221
[2025-11-13 01:32:07] 44:44<388:36, 8.41s/it | [Iter  319/3090] R0[229/3000]  | LR: 0.024645 | E: -60.774948 | E_var:     7.6379 E_err:   0.043182 | NF_loss: 37.906466
[2025-11-13 01:32:15] 44:52<388:23, 8.41s/it | [Iter  320/3090] R0[230/3000]  | LR: 0.024642 | E: -60.701144 | E_var:     7.8145 E_err:   0.043679 | NF_loss: 43.241180
[2025-11-13 01:32:23] 44:59<388:10, 8.41s/it | [Iter  321/3090] R0[231/3000]  | LR: 0.024639 | E: -60.777796 | E_var:     7.7120 E_err:   0.043391 | NF_loss: 43.101536
[2025-11-13 01:32:31] 45:07<387:57, 8.41s/it | [Iter  322/3090] R0[232/3000]  | LR: 0.024636 | E: -60.583658 | E_var:     7.9485 E_err:   0.044052 | NF_loss: 35.466421
[2025-11-13 01:32:39] 45:15<387:44, 8.41s/it | [Iter  323/3090] R0[233/3000]  | LR: 0.024633 | E: -60.232609 | E_var:     7.9933 E_err:   0.044176 | NF_loss: 16.924761
[2025-11-13 01:32:47] 45:23<387:31, 8.41s/it | [Iter  324/3090] R0[234/3000]  | LR: 0.024630 | E: -60.302184 | E_var:     8.3237 E_err:   0.045079 | NF_loss: 78.322832
[2025-11-13 01:32:54] 45:31<387:18, 8.40s/it | [Iter  325/3090] R0[235/3000]  | LR: 0.024627 | E: -59.776492 | E_var:     9.1100 E_err:   0.047161 | NF_loss: 43.006916
[2025-11-13 01:33:02] 45:39<387:05, 8.40s/it | [Iter  326/3090] R0[236/3000]  | LR: 0.024623 | E: -59.963453 | E_var:     8.3000 E_err:   0.045015 | NF_loss: 40.665784
[2025-11-13 01:33:10] 45:47<386:52, 8.40s/it | [Iter  327/3090] R0[237/3000]  | LR: 0.024620 | E: -60.027076 | E_var:     8.3251 E_err:   0.045083 | NF_loss: 36.625519
[2025-11-13 01:33:18] 45:55<386:39, 8.40s/it | [Iter  328/3090] R0[238/3000]  | LR: 0.024617 | E: -59.701831 | E_var:     8.6806 E_err:   0.046036 | NF_loss: 33.040437
[2025-11-13 01:33:26] 46:02<386:26, 8.40s/it | [Iter  329/3090] R0[239/3000]  | LR: 0.024614 | E: -60.033845 | E_var:     8.3254 E_err:   0.045084 | NF_loss: 24.355732
[2025-11-13 01:33:34] 46:10<386:14, 8.40s/it | [Iter  330/3090] R0[240/3000]  | LR: 0.024611 | E: -60.183502 | E_var:     7.9823 E_err:   0.044145 | NF_loss: 32.714273
[2025-11-13 01:33:42] 46:18<386:01, 8.39s/it | [Iter  331/3090] R0[241/3000]  | LR: 0.024607 | E: -60.098190 | E_var:     8.1989 E_err:   0.044740 | NF_loss: 37.255366
[2025-11-13 01:33:50] 46:26<385:48, 8.39s/it | [Iter  332/3090] R0[242/3000]  | LR: 0.024604 | E: -60.202018 | E_var:     8.0004 E_err:   0.044195 | NF_loss: 32.098237
[2025-11-13 01:33:57] 46:34<385:35, 8.39s/it | [Iter  333/3090] R0[243/3000]  | LR: 0.024601 | E: -60.292626 | E_var:     8.2348 E_err:   0.044838 | NF_loss: 60.644217
[2025-11-13 01:34:05] 46:42<385:23, 8.39s/it | [Iter  334/3090] R0[244/3000]  | LR: 0.024597 | E: -60.191891 | E_var:     7.8857 E_err:   0.043877 | NF_loss: 31.225989
[2025-11-13 01:34:13] 46:50<385:10, 8.39s/it | [Iter  335/3090] R0[245/3000]  | LR: 0.024594 | E: -60.191245 | E_var:     8.5464 E_err:   0.045678 | NF_loss: 269.832921
[2025-11-13 01:34:21] 46:58<384:57, 8.39s/it | [Iter  336/3090] R0[246/3000]  | LR: 0.024591 | E: -60.498898 | E_var:     7.8896 E_err:   0.043888 | NF_loss: 29.976683
[2025-11-13 01:34:29] 47:05<384:45, 8.39s/it | [Iter  337/3090] R0[247/3000]  | LR: 0.024588 | E: -60.736679 | E_var:     7.9440 E_err:   0.044039 | NF_loss: 37.524470
[2025-11-13 01:34:37] 47:13<384:32, 8.38s/it | [Iter  338/3090] R0[248/3000]  | LR: 0.024584 | E: -60.923109 | E_var:     7.8257 E_err:   0.043710 | NF_loss: 37.008476
[2025-11-13 01:34:45] 47:21<384:20, 8.38s/it | [Iter  339/3090] R0[249/3000]  | LR: 0.024581 | E: -60.835763 | E_var:     6.8413 E_err:   0.040868 | NF_loss: 41.373000
[2025-11-13 01:34:52] 47:29<384:07, 8.38s/it | [Iter  340/3090] R0[250/3000]  | LR: 0.024577 | E: -60.940517 | E_var:     7.3102 E_err:   0.042246 | NF_loss: 35.338644
[2025-11-13 01:35:00] 47:37<383:55, 8.38s/it | [Iter  341/3090] R0[251/3000]  | LR: 0.024574 | E: -60.767901 | E_var:     7.2923 E_err:   0.042194 | NF_loss: 30.969769
[2025-11-13 01:35:08] 47:45<383:42, 8.38s/it | [Iter  342/3090] R0[252/3000]  | LR: 0.024571 | E: -60.939405 | E_var:     7.5662 E_err:   0.042979 | NF_loss: 31.217215
[2025-11-13 01:35:16] 47:53<383:30, 8.38s/it | [Iter  343/3090] R0[253/3000]  | LR: 0.024567 | E: -61.045892 | E_var:     8.7015 E_err:   0.046091 | NF_loss: 22.315846
[2025-11-13 01:35:24] 48:01<383:17, 8.38s/it | [Iter  344/3090] R0[254/3000]  | LR: 0.024564 | E: -61.052792 | E_var:     7.2427 E_err:   0.042050 | NF_loss: 29.789012
[2025-11-13 01:35:32] 48:08<383:05, 8.37s/it | [Iter  345/3090] R0[255/3000]  | LR: 0.024560 | E: -61.046563 | E_var:     7.2388 E_err:   0.042039 | NF_loss: 39.380537
[2025-11-13 01:35:40] 48:16<382:53, 8.37s/it | [Iter  346/3090] R0[256/3000]  | LR: 0.024557 | E: -61.005398 | E_var:     7.2328 E_err:   0.042022 | NF_loss: 41.664339
[2025-11-13 01:35:48] 48:24<382:40, 8.37s/it | [Iter  347/3090] R0[257/3000]  | LR: 0.024554 | E: -60.876030 | E_var:     6.9270 E_err:   0.041124 | NF_loss: 37.385799
[2025-11-13 01:35:55] 48:32<382:28, 8.37s/it | [Iter  348/3090] R0[258/3000]  | LR: 0.024550 | E: -60.665097 | E_var:     7.4120 E_err:   0.042539 | NF_loss: 32.553929
[2025-11-13 01:36:03] 48:40<382:16, 8.37s/it | [Iter  349/3090] R0[259/3000]  | LR: 0.024547 | E: -60.857476 | E_var:     8.1952 E_err:   0.044730 | NF_loss: 45.262923
[2025-11-13 01:36:11] 48:48<382:05, 8.37s/it | [Iter  350/3090] R0[260/3000]  | LR: 0.024543 | E: -60.281795 | E_var:     7.8432 E_err:   0.043759 | NF_loss: 28.139543
[2025-11-13 01:36:19] 48:56<381:53, 8.37s/it | [Iter  351/3090] R0[261/3000]  | LR: 0.024540 | E: -60.489562 | E_var:     7.4858 E_err:   0.042750 | NF_loss: 31.559155
[2025-11-13 01:36:27] 49:04<381:41, 8.36s/it | [Iter  352/3090] R0[262/3000]  | LR: 0.024536 | E: -60.322499 | E_var:     8.1040 E_err:   0.044481 | NF_loss: 18.660049
[2025-11-13 01:36:35] 49:12<381:29, 8.36s/it | [Iter  353/3090] R0[263/3000]  | LR: 0.024532 | E: -60.712160 | E_var:     7.4564 E_err:   0.042666 | NF_loss: 26.933463
[2025-11-13 01:36:43] 49:19<381:17, 8.36s/it | [Iter  354/3090] R0[264/3000]  | LR: 0.024529 | E: -60.612663 | E_var:     8.4613 E_err:   0.045451 | NF_loss: 34.373797
[2025-11-13 01:36:51] 49:27<381:05, 8.36s/it | [Iter  355/3090] R0[265/3000]  | LR: 0.024525 | E: -60.525118 | E_var:     7.9001 E_err:   0.043917 | NF_loss: 29.237596
[2025-11-13 01:36:59] 49:35<380:52, 8.36s/it | [Iter  356/3090] R0[266/3000]  | LR: 0.024522 | E: -60.752898 | E_var:     7.8740 E_err:   0.043845 | NF_loss: 27.649522
[2025-11-13 01:37:07] 49:43<380:40, 8.36s/it | [Iter  357/3090] R0[267/3000]  | LR: 0.024518 | E: -60.584709 | E_var:     7.4063 E_err:   0.042523 | NF_loss: 24.765830
[2025-11-13 01:37:14] 49:51<380:28, 8.36s/it | [Iter  358/3090] R0[268/3000]  | LR: 0.024515 | E: -60.721057 | E_var:     7.5332 E_err:   0.042885 | NF_loss: 37.092347
[2025-11-13 01:37:22] 49:59<380:16, 8.35s/it | [Iter  359/3090] R0[269/3000]  | LR: 0.024511 | E: -60.944792 | E_var:     7.1200 E_err:   0.041693 | NF_loss: 29.176441
[2025-11-13 01:37:30] 50:07<380:04, 8.35s/it | [Iter  360/3090] R0[270/3000]  | LR: 0.024507 | E: -60.891036 | E_var:     7.5228 E_err:   0.042856 | NF_loss: 31.123452
[2025-11-13 01:37:38] 50:15<379:52, 8.35s/it | [Iter  361/3090] R0[271/3000]  | LR: 0.024504 | E: -60.945468 | E_var:     7.0802 E_err:   0.041576 | NF_loss: 33.306314
[2025-11-13 01:37:46] 50:22<379:40, 8.35s/it | [Iter  362/3090] R0[272/3000]  | LR: 0.024500 | E: -60.987503 | E_var:     7.0578 E_err:   0.041510 | NF_loss: 27.285848
[2025-11-13 01:37:54] 50:30<379:28, 8.35s/it | [Iter  363/3090] R0[273/3000]  | LR: 0.024496 | E: -61.059585 | E_var:     6.6951 E_err:   0.040429 | NF_loss: 28.855850
[2025-11-13 01:38:02] 50:38<379:16, 8.35s/it | [Iter  364/3090] R0[274/3000]  | LR: 0.024493 | E: -61.099974 | E_var:     6.7464 E_err:   0.040584 | NF_loss: 28.388159
[2025-11-13 01:38:10] 50:46<379:04, 8.35s/it | [Iter  365/3090] R0[275/3000]  | LR: 0.024489 | E: -61.060298 | E_var:     7.4495 E_err:   0.042647 | NF_loss: 39.410905
[2025-11-13 01:38:17] 50:54<378:53, 8.35s/it | [Iter  366/3090] R0[276/3000]  | LR: 0.024485 | E: -61.185430 | E_var:     7.5552 E_err:   0.042948 | NF_loss: 27.459959
[2025-11-13 01:38:25] 51:02<378:41, 8.34s/it | [Iter  367/3090] R0[277/3000]  | LR: 0.024482 | E: -61.242440 | E_var:     6.7184 E_err:   0.040500 | NF_loss: 30.290054
[2025-11-13 01:38:33] 51:10<378:29, 8.34s/it | [Iter  368/3090] R0[278/3000]  | LR: 0.024478 | E: -61.216527 | E_var:     7.6467 E_err:   0.043207 | NF_loss: 26.247318
[2025-11-13 01:38:41] 51:18<378:17, 8.34s/it | [Iter  369/3090] R0[279/3000]  | LR: 0.024474 | E: -61.278728 | E_var:     7.9266 E_err:   0.043991 | NF_loss: 32.619139
[2025-11-13 01:38:49] 51:25<378:05, 8.34s/it | [Iter  370/3090] R0[280/3000]  | LR: 0.024470 | E: -61.177335 | E_var:     7.3311 E_err:   0.042306 | NF_loss: 34.657246
[2025-11-13 01:38:57] 51:33<377:54, 8.34s/it | [Iter  371/3090] R0[281/3000]  | LR: 0.024466 | E: -61.212305 | E_var:     8.5983 E_err:   0.045817 | NF_loss: 28.282103
[2025-11-13 01:39:05] 51:41<377:42, 8.34s/it | [Iter  372/3090] R0[282/3000]  | LR: 0.024463 | E: -61.173712 | E_var:    11.1685 E_err:   0.052218 | NF_loss: 8.677528
[2025-11-13 01:39:13] 51:49<377:30, 8.34s/it | [Iter  373/3090] R0[283/3000]  | LR: 0.024459 | E: -61.237647 | E_var:     7.3297 E_err:   0.042302 | NF_loss: 33.970327
[2025-11-13 01:39:20] 51:57<377:18, 8.34s/it | [Iter  374/3090] R0[284/3000]  | LR: 0.024455 | E: -61.145688 | E_var:     7.9526 E_err:   0.044063 | NF_loss: 30.786202
[2025-11-13 01:39:28] 52:05<377:07, 8.33s/it | [Iter  375/3090] R0[285/3000]  | LR: 0.024451 | E: -61.074161 | E_var:     8.1159 E_err:   0.044513 | NF_loss: 30.389868
[2025-11-13 01:39:36] 52:13<376:55, 8.33s/it | [Iter  376/3090] R0[286/3000]  | LR: 0.024447 | E: -61.126016 | E_var:     7.3595 E_err:   0.042388 | NF_loss: 37.215662
[2025-11-13 01:39:44] 52:21<376:43, 8.33s/it | [Iter  377/3090] R0[287/3000]  | LR: 0.024444 | E: -61.290961 | E_var:     6.7135 E_err:   0.040485 | NF_loss: 28.432828
[2025-11-13 01:39:52] 52:28<376:32, 8.33s/it | [Iter  378/3090] R0[288/3000]  | LR: 0.024440 | E: -61.296637 | E_var:     7.2745 E_err:   0.042143 | NF_loss: 27.856138
[2025-11-13 01:40:00] 52:36<376:20, 8.33s/it | [Iter  379/3090] R0[289/3000]  | LR: 0.024436 | E: -61.296549 | E_var:     7.4107 E_err:   0.042535 | NF_loss: 36.290802
[2025-11-13 01:40:08] 52:44<376:08, 8.33s/it | [Iter  380/3090] R0[290/3000]  | LR: 0.024432 | E: -61.369578 | E_var:     6.8237 E_err:   0.040816 | NF_loss: 28.979036
[2025-11-13 01:40:15] 52:52<375:57, 8.33s/it | [Iter  381/3090] R0[291/3000]  | LR: 0.024428 | E: -61.371728 | E_var:     6.6882 E_err:   0.040409 | NF_loss: 25.594702
[2025-11-13 01:40:23] 53:00<375:45, 8.33s/it | [Iter  382/3090] R0[292/3000]  | LR: 0.024424 | E: -61.352973 | E_var:     7.5258 E_err:   0.042864 | NF_loss: 32.745750
[2025-11-13 01:40:31] 53:08<375:34, 8.32s/it | [Iter  383/3090] R0[293/3000]  | LR: 0.024420 | E: -61.328178 | E_var:     8.6036 E_err:   0.045831 | NF_loss: 33.651039
[2025-11-13 01:40:39] 53:16<375:22, 8.32s/it | [Iter  384/3090] R0[294/3000]  | LR: 0.024416 | E: -61.288947 | E_var:     7.7735 E_err:   0.043564 | NF_loss: 25.111822
[2025-11-13 01:40:47] 53:23<375:11, 8.32s/it | [Iter  385/3090] R0[295/3000]  | LR: 0.024412 | E: -61.280100 | E_var:     7.5185 E_err:   0.042844 | NF_loss: 23.018814
[2025-11-13 01:40:55] 53:32<375:01, 8.32s/it | [Iter  386/3090] R0[296/3000]  | LR: 0.024408 | E: -61.270848 | E_var:     7.6440 E_err:   0.043200 | NF_loss: 29.677135
[2025-11-13 01:41:03] 53:39<374:49, 8.32s/it | [Iter  387/3090] R0[297/3000]  | LR: 0.024404 | E: -61.223293 | E_var:     7.9327 E_err:   0.044008 | NF_loss: 36.220328
[2025-11-13 01:41:11] 53:47<374:38, 8.32s/it | [Iter  388/3090] R0[298/3000]  | LR: 0.024400 | E: -61.241123 | E_var:     7.2394 E_err:   0.042041 | NF_loss: 24.794175
[2025-11-13 01:41:19] 53:55<374:27, 8.32s/it | [Iter  389/3090] R0[299/3000]  | LR: 0.024396 | E: -61.287219 | E_var:     7.4189 E_err:   0.042559 | NF_loss: 20.441638
[2025-11-13 01:41:27] 54:03<374:15, 8.32s/it | [Iter  390/3090] R0[300/3000]  | LR: 0.024392 | E: -61.330228 | E_var:     8.4581 E_err:   0.045442 | NF_loss: 31.591175
[2025-11-13 01:41:34] 54:11<374:04, 8.32s/it | [Iter  391/3090] R0[301/3000]  | LR: 0.024388 | E: -61.269840 | E_var:     8.6642 E_err:   0.045992 | NF_loss: 34.715837
[2025-11-13 01:41:42] 54:19<373:52, 8.31s/it | [Iter  392/3090] R0[302/3000]  | LR: 0.024384 | E: -61.265227 | E_var:     8.0193 E_err:   0.044248 | NF_loss: 76.295760
[2025-11-13 01:41:50] 54:27<373:41, 8.31s/it | [Iter  393/3090] R0[303/3000]  | LR: 0.024380 | E: -61.320924 | E_var:     7.0358 E_err:   0.041446 | NF_loss: 37.103571
[2025-11-13 01:41:58] 54:35<373:30, 8.31s/it | [Iter  394/3090] R0[304/3000]  | LR: 0.024376 | E: -61.407019 | E_var:     6.7593 E_err:   0.040623 | NF_loss: 31.729374
[2025-11-13 01:42:06] 54:42<373:18, 8.31s/it | [Iter  395/3090] R0[305/3000]  | LR: 0.024372 | E: -61.285255 | E_var:     8.1913 E_err:   0.044720 | NF_loss: 17.980057
[2025-11-13 01:42:14] 54:50<373:07, 8.31s/it | [Iter  396/3090] R0[306/3000]  | LR: 0.024368 | E: -61.207200 | E_var:     7.7323 E_err:   0.043448 | NF_loss: 17.997050
[2025-11-13 01:42:22] 54:58<372:56, 8.31s/it | [Iter  397/3090] R0[307/3000]  | LR: 0.024364 | E: -61.035386 | E_var:    10.2275 E_err:   0.049969 | NF_loss: 22.155861
[2025-11-13 01:42:30] 55:06<372:44, 8.31s/it | [Iter  398/3090] R0[308/3000]  | LR: 0.024360 | E: -60.876380 | E_var:     9.0728 E_err:   0.047064 | NF_loss: 10.254824
[2025-11-13 01:42:37] 55:14<372:33, 8.31s/it | [Iter  399/3090] R0[309/3000]  | LR: 0.024355 | E: -61.073902 | E_var:     7.8624 E_err:   0.043813 | NF_loss: 275.249394
[2025-11-13 01:42:45] 55:22<372:22, 8.31s/it | [Iter  400/3090] R0[310/3000]  | LR: 0.024351 | E: -61.052541 | E_var:     8.1478 E_err:   0.044601 | NF_loss: 6.169128
[2025-11-13 01:42:53] 55:30<372:11, 8.30s/it | [Iter  401/3090] R0[311/3000]  | LR: 0.024347 | E: -61.291780 | E_var:     7.6607 E_err:   0.043247 | NF_loss: 23.687930
[2025-11-13 01:43:01] 55:38<372:00, 8.30s/it | [Iter  402/3090] R0[312/3000]  | LR: 0.024343 | E: -61.457056 | E_var:     6.7162 E_err:   0.040493 | NF_loss: 51.465657
[2025-11-13 01:43:09] 55:45<371:48, 8.30s/it | [Iter  403/3090] R0[313/3000]  | LR: 0.024339 | E: -61.344431 | E_var:     7.0721 E_err:   0.041552 | NF_loss: 28.577573
[2025-11-13 01:43:17] 55:53<371:37, 8.30s/it | [Iter  404/3090] R0[314/3000]  | LR: 0.024335 | E: -61.436033 | E_var:     7.2968 E_err:   0.042207 | NF_loss: 48.819689
[2025-11-13 01:43:25] 56:01<371:26, 8.30s/it | [Iter  405/3090] R0[315/3000]  | LR: 0.024330 | E: -61.407005 | E_var:     7.8426 E_err:   0.043757 | NF_loss: 36.276148
[2025-11-13 01:43:32] 56:09<371:15, 8.30s/it | [Iter  406/3090] R0[316/3000]  | LR: 0.024326 | E: -61.487945 | E_var:     7.3719 E_err:   0.042424 | NF_loss: 35.751747
[2025-11-13 01:43:40] 56:17<371:04, 8.30s/it | [Iter  407/3090] R0[317/3000]  | LR: 0.024322 | E: -61.510845 | E_var:     8.1164 E_err:   0.044515 | NF_loss: 48.276503
[2025-11-13 01:43:48] 56:25<370:53, 8.30s/it | [Iter  408/3090] R0[318/3000]  | LR: 0.024318 | E: -61.260469 | E_var:     8.1082 E_err:   0.044492 | NF_loss: 35.750214
[2025-11-13 01:43:56] 56:33<370:42, 8.30s/it | [Iter  409/3090] R0[319/3000]  | LR: 0.024313 | E: -61.309879 | E_var:     8.6460 E_err:   0.045944 | NF_loss: 24.674614
[2025-11-13 01:44:04] 56:41<370:31, 8.30s/it | [Iter  410/3090] R0[320/3000]  | LR: 0.024309 | E: -61.387543 | E_var:     7.0396 E_err:   0.041457 | NF_loss: 46.880556
[2025-11-13 01:44:12] 56:48<370:20, 8.29s/it | [Iter  411/3090] R0[321/3000]  | LR: 0.024305 | E: -61.465346 | E_var:     7.2073 E_err:   0.041948 | NF_loss: 37.857634
[2025-11-13 01:44:20] 56:56<370:09, 8.29s/it | [Iter  412/3090] R0[322/3000]  | LR: 0.024300 | E: -61.631653 | E_var:     6.8456 E_err:   0.040881 | NF_loss: 32.451518
[2025-11-13 01:44:28] 57:04<369:58, 8.29s/it | [Iter  413/3090] R0[323/3000]  | LR: 0.024296 | E: -61.560145 | E_var:     7.0363 E_err:   0.041447 | NF_loss: 63.753064
[2025-11-13 01:44:36] 57:12<369:48, 8.29s/it | [Iter  414/3090] R0[324/3000]  | LR: 0.024292 | E: -61.526039 | E_var:     7.0083 E_err:   0.041365 | NF_loss: 370.676421
[2025-11-13 01:44:44] 57:20<369:37, 8.29s/it | [Iter  415/3090] R0[325/3000]  | LR: 0.024287 | E: -61.591060 | E_var:     7.5805 E_err:   0.043020 | NF_loss: 9.710900
[2025-11-13 01:44:52] 57:28<369:26, 8.29s/it | [Iter  416/3090] R0[326/3000]  | LR: 0.024283 | E: -61.598471 | E_var:     7.0289 E_err:   0.041425 | NF_loss: 54.565153
[2025-11-13 01:44:59] 57:36<369:15, 8.29s/it | [Iter  417/3090] R0[327/3000]  | LR: 0.024279 | E: -61.598143 | E_var:     6.6036 E_err:   0.040152 | NF_loss: 208.413728
[2025-11-13 01:45:07] 57:44<369:04, 8.29s/it | [Iter  418/3090] R0[328/3000]  | LR: 0.024274 | E: -61.631960 | E_var:     7.1983 E_err:   0.041921 | NF_loss: 209.936703
[2025-11-13 01:45:15] 57:52<368:53, 8.29s/it | [Iter  419/3090] R0[329/3000]  | LR: 0.024270 | E: -61.650102 | E_var:     6.6477 E_err:   0.040286 | NF_loss: 35.334729
[2025-11-13 01:45:23] 58:00<368:42, 8.29s/it | [Iter  420/3090] R0[330/3000]  | LR: 0.024265 | E: -61.621853 | E_var:     6.7632 E_err:   0.040634 | NF_loss: 77.567596
[2025-11-13 01:45:31] 58:07<368:32, 8.28s/it | [Iter  421/3090] R0[331/3000]  | LR: 0.024261 | E: -61.672855 | E_var:     7.4822 E_err:   0.042740 | NF_loss: 301.334652
[2025-11-13 01:45:39] 58:15<368:21, 8.28s/it | [Iter  422/3090] R0[332/3000]  | LR: 0.024257 | E: -61.738055 | E_var:     7.1839 E_err:   0.041879 | NF_loss: 85.782774
[2025-11-13 01:45:47] 58:23<368:10, 8.28s/it | [Iter  423/3090] R0[333/3000]  | LR: 0.024252 | E: -61.704222 | E_var:     7.4885 E_err:   0.042758 | NF_loss: 86.967684
[2025-11-13 01:45:54] 58:31<367:59, 8.28s/it | [Iter  424/3090] R0[334/3000]  | LR: 0.024248 | E: -61.505412 | E_var:     6.7022 E_err:   0.040451 | NF_loss: 96.840579
[2025-11-13 01:46:02] 58:39<367:48, 8.28s/it | [Iter  425/3090] R0[335/3000]  | LR: 0.024243 | E: -61.519776 | E_var:     7.6535 E_err:   0.043226 | NF_loss: 115.918978
[2025-11-13 01:46:10] 58:47<367:37, 8.28s/it | [Iter  426/3090] R0[336/3000]  | LR: 0.024239 | E: -61.530391 | E_var:     7.1632 E_err:   0.041819 | NF_loss: 400.957933
[2025-11-13 01:46:18] 58:55<367:26, 8.28s/it | [Iter  427/3090] R0[337/3000]  | LR: 0.024234 | E: -61.627037 | E_var:    10.0024 E_err:   0.049417 | NF_loss: 105.183277
[2025-11-13 01:46:26] 59:02<367:16, 8.28s/it | [Iter  428/3090] R0[338/3000]  | LR: 0.024230 | E: -61.578982 | E_var:     6.8257 E_err:   0.040822 | NF_loss: 7346.086030
[2025-11-13 01:46:34] 59:10<367:05, 8.28s/it | [Iter  429/3090] R0[339/3000]  | LR: 0.024225 | E: -61.667384 | E_var:    11.0674 E_err:   0.051981 | NF_loss: 288.800851
[2025-11-13 01:46:42] 59:18<366:54, 8.28s/it | [Iter  430/3090] R0[340/3000]  | LR: 0.024221 | E: -61.654534 | E_var:     6.9770 E_err:   0.041272 | NF_loss: 3004.069526
[2025-11-13 01:46:50] 59:26<366:43, 8.28s/it | [Iter  431/3090] R0[341/3000]  | LR: 0.024216 | E: -61.715352 | E_var:     6.6061 E_err:   0.040160 | NF_loss: 220.839695
[2025-11-13 01:46:57] 59:34<366:33, 8.27s/it | [Iter  432/3090] R0[342/3000]  | LR: 0.024211 | E: -61.671246 | E_var:     6.7918 E_err:   0.040721 | NF_loss: 2115.468602
[2025-11-13 01:47:05] 59:42<366:22, 8.27s/it | [Iter  433/3090] R0[343/3000]  | LR: 0.024207 | E: -61.681214 | E_var:     6.6510 E_err:   0.040296 | NF_loss: 152.449753
[2025-11-13 01:47:13] 59:50<366:11, 8.27s/it | [Iter  434/3090] R0[344/3000]  | LR: 0.024202 | E: -61.307683 | E_var:     6.9656 E_err:   0.041238 | NF_loss: 782.426578
[2025-11-13 01:47:21] 59:58<366:00, 8.27s/it | [Iter  435/3090] R0[345/3000]  | LR: 0.024198 | E: -61.379556 | E_var:     7.0896 E_err:   0.041604 | NF_loss: 550.805277
[2025-11-13 01:47:29] 60:05<365:50, 8.27s/it | [Iter  436/3090] R0[346/3000]  | LR: 0.024193 | E: -61.337871 | E_var:     6.8305 E_err:   0.040836 | NF_loss: 280.479507
[2025-11-13 01:47:37] 60:13<365:39, 8.27s/it | [Iter  437/3090] R0[347/3000]  | LR: 0.024188 | E: -61.406765 | E_var:     7.3532 E_err:   0.042370 | NF_loss: 707.313324
[2025-11-13 01:47:45] 60:21<365:28, 8.27s/it | [Iter  438/3090] R0[348/3000]  | LR: 0.024184 | E: -61.325403 | E_var:     6.9107 E_err:   0.041075 | NF_loss: 88.403398
[2025-11-13 01:47:53] 60:29<365:18, 8.27s/it | [Iter  439/3090] R0[349/3000]  | LR: 0.024179 | E: -61.416911 | E_var:     6.7680 E_err:   0.040649 | NF_loss: 30.619092
[2025-11-13 01:48:00] 60:37<365:07, 8.27s/it | [Iter  440/3090] R0[350/3000]  | LR: 0.024174 | E: -61.152959 | E_var:     7.0798 E_err:   0.041575 | NF_loss: 624.270286
[2025-11-13 01:48:08] 60:45<364:56, 8.27s/it | [Iter  441/3090] R0[351/3000]  | LR: 0.024170 | E: -61.436767 | E_var:     7.4136 E_err:   0.042544 | NF_loss: 824.706410
[2025-11-13 01:48:16] 60:53<364:46, 8.27s/it | [Iter  442/3090] R0[352/3000]  | LR: 0.024165 | E: -61.662556 | E_var:     6.8560 E_err:   0.040912 | NF_loss: 246.366285
[2025-11-13 01:48:24] 61:01<364:35, 8.26s/it | [Iter  443/3090] R0[353/3000]  | LR: 0.024160 | E: -61.667431 | E_var:     6.6769 E_err:   0.040374 | NF_loss: 124.736983
[2025-11-13 01:48:32] 61:08<364:24, 8.26s/it | [Iter  444/3090] R0[354/3000]  | LR: 0.024156 | E: -61.595338 | E_var:     6.6839 E_err:   0.040396 | NF_loss: 133.038999
[2025-11-13 01:48:40] 61:16<364:14, 8.26s/it | [Iter  445/3090] R0[355/3000]  | LR: 0.024151 | E: -61.678280 | E_var:     6.3604 E_err:   0.039406 | NF_loss: 394.181100
[2025-11-13 01:48:48] 61:24<364:03, 8.26s/it | [Iter  446/3090] R0[356/3000]  | LR: 0.024146 | E: -61.604002 | E_var:    11.3890 E_err:   0.052731 | NF_loss: 239.214003
[2025-11-13 01:48:56] 61:32<363:53, 8.26s/it | [Iter  447/3090] R0[357/3000]  | LR: 0.024141 | E: -61.424494 | E_var:     6.5306 E_err:   0.039930 | NF_loss: 730.600340
[2025-11-13 01:49:03] 61:40<363:42, 8.26s/it | [Iter  448/3090] R0[358/3000]  | LR: 0.024137 | E: -61.530702 | E_var:     6.3354 E_err:   0.039328 | NF_loss: 958.775216
[2025-11-13 01:49:11] 61:48<363:32, 8.26s/it | [Iter  449/3090] R0[359/3000]  | LR: 0.024132 | E: -61.329395 | E_var:     6.7767 E_err:   0.040675 | NF_loss: 241.614502
[2025-11-13 01:49:19] 61:56<363:21, 8.26s/it | [Iter  450/3090] R0[360/3000]  | LR: 0.024127 | E: -61.529698 | E_var:     6.4613 E_err:   0.039717 | NF_loss: 410.418468
[2025-11-13 01:49:27] 62:04<363:10, 8.26s/it | [Iter  451/3090] R0[361/3000]  | LR: 0.024122 | E: -61.700645 | E_var:     6.4386 E_err:   0.039647 | NF_loss: 547.659769
[2025-11-13 01:49:35] 62:12<363:01, 8.26s/it | [Iter  452/3090] R0[362/3000]  | LR: 0.024117 | E: -61.555719 | E_var:     6.3138 E_err:   0.039261 | NF_loss: 849.368758
[2025-11-13 01:49:43] 62:20<362:51, 8.26s/it | [Iter  453/3090] R0[363/3000]  | LR: 0.024113 | E: -61.519733 | E_var:     6.8245 E_err:   0.040818 | NF_loss: 1656.898922
[2025-11-13 01:49:51] 62:27<362:40, 8.26s/it | [Iter  454/3090] R0[364/3000]  | LR: 0.024108 | E: -61.547381 | E_var:     6.3347 E_err:   0.039326 | NF_loss: 359.317072
[2025-11-13 01:49:59] 62:35<362:30, 8.25s/it | [Iter  455/3090] R0[365/3000]  | LR: 0.024103 | E: -61.627561 | E_var:     6.3981 E_err:   0.039523 | NF_loss: 396.534543
[2025-11-13 01:50:07] 62:43<362:20, 8.25s/it | [Iter  456/3090] R0[366/3000]  | LR: 0.024098 | E: -61.617591 | E_var:     6.9662 E_err:   0.041240 | NF_loss: 744.597051
[2025-11-13 01:50:14] 62:51<362:09, 8.25s/it | [Iter  457/3090] R0[367/3000]  | LR: 0.024093 | E: -61.643701 | E_var:     7.6906 E_err:   0.043331 | NF_loss: 687.865825
[2025-11-13 01:50:22] 62:59<361:59, 8.25s/it | [Iter  458/3090] R0[368/3000]  | LR: 0.024088 | E: -61.673210 | E_var:     6.5681 E_err:   0.040044 | NF_loss: 184.025868
[2025-11-13 01:50:30] 63:07<361:48, 8.25s/it | [Iter  459/3090] R0[369/3000]  | LR: 0.024083 | E: -61.655066 | E_var:     6.1821 E_err:   0.038850 | NF_loss: 258.709591
[2025-11-13 01:50:38] 63:15<361:38, 8.25s/it | [Iter  460/3090] R0[370/3000]  | LR: 0.024078 | E: -61.735686 | E_var:     6.1539 E_err:   0.038761 | NF_loss: 415.672705
[2025-11-13 01:50:46] 63:23<361:27, 8.25s/it | [Iter  461/3090] R0[371/3000]  | LR: 0.024073 | E: -61.739688 | E_var:     6.5549 E_err:   0.040004 | NF_loss: 49.480603
[2025-11-13 01:50:54] 63:30<361:17, 8.25s/it | [Iter  462/3090] R0[372/3000]  | LR: 0.024068 | E: -61.711653 | E_var:     6.4583 E_err:   0.039708 | NF_loss: 237.397547
[2025-11-13 01:51:02] 63:38<361:07, 8.25s/it | [Iter  463/3090] R0[373/3000]  | LR: 0.024063 | E: -61.697844 | E_var:     6.4170 E_err:   0.039581 | NF_loss: 939.993687
[2025-11-13 01:51:10] 63:46<360:56, 8.25s/it | [Iter  464/3090] R0[374/3000]  | LR: 0.024058 | E: -61.608737 | E_var:     6.5114 E_err:   0.039871 | NF_loss: 123.149325
[2025-11-13 01:51:17] 63:54<360:46, 8.25s/it | [Iter  465/3090] R0[375/3000]  | LR: 0.024054 | E: -61.577585 | E_var:     6.4647 E_err:   0.039728 | NF_loss: 334.592977
[2025-11-13 01:51:25] 64:02<360:36, 8.25s/it | [Iter  466/3090] R0[376/3000]  | LR: 0.024048 | E: -61.606646 | E_var:     6.4384 E_err:   0.039647 | NF_loss: 77.540939
[2025-11-13 01:51:33] 64:10<360:25, 8.24s/it | [Iter  467/3090] R0[377/3000]  | LR: 0.024043 | E: -61.632155 | E_var:     6.7997 E_err:   0.040744 | NF_loss: 388.671341
[2025-11-13 01:51:41] 64:18<360:15, 8.24s/it | [Iter  468/3090] R0[378/3000]  | LR: 0.024038 | E: -61.623992 | E_var:     7.4716 E_err:   0.042710 | NF_loss: 172.275527
[2025-11-13 01:51:49] 64:26<360:05, 8.24s/it | [Iter  469/3090] R0[379/3000]  | LR: 0.024033 | E: -61.666919 | E_var:     6.9104 E_err:   0.041074 | NF_loss: 1032.120096
[2025-11-13 01:51:57] 64:33<359:54, 8.24s/it | [Iter  470/3090] R0[380/3000]  | LR: 0.024028 | E: -61.657628 | E_var:     6.4970 E_err:   0.039827 | NF_loss: 342.947240
[2025-11-13 01:52:05] 64:41<359:44, 8.24s/it | [Iter  471/3090] R0[381/3000]  | LR: 0.024023 | E: -61.604557 | E_var:     6.1529 E_err:   0.038758 | NF_loss: 178.909618
[2025-11-13 01:52:13] 64:49<359:34, 8.24s/it | [Iter  472/3090] R0[382/3000]  | LR: 0.024018 | E: -61.580659 | E_var:     7.3677 E_err:   0.042412 | NF_loss: 172.242406
[2025-11-13 01:52:20] 64:57<359:23, 8.24s/it | [Iter  473/3090] R0[383/3000]  | LR: 0.024013 | E: -61.690519 | E_var:     6.2559 E_err:   0.039081 | NF_loss: 439.744343
[2025-11-13 01:52:28] 65:05<359:13, 8.24s/it | [Iter  474/3090] R0[384/3000]  | LR: 0.024008 | E: -61.691719 | E_var:     6.3605 E_err:   0.039406 | NF_loss: 162.344840
[2025-11-13 01:52:36] 65:13<359:03, 8.24s/it | [Iter  475/3090] R0[385/3000]  | LR: 0.024003 | E: -61.678383 | E_var:     7.4855 E_err:   0.042749 | NF_loss: 195.779564
[2025-11-13 01:52:44] 65:21<358:53, 8.24s/it | [Iter  476/3090] R0[386/3000]  | LR: 0.023998 | E: -61.623689 | E_var:     6.4689 E_err:   0.039741 | NF_loss: 485.468166
[2025-11-13 01:52:52] 65:28<358:42, 8.24s/it | [Iter  477/3090] R0[387/3000]  | LR: 0.023993 | E: -61.443488 | E_var:     6.7469 E_err:   0.040586 | NF_loss: 151.034955
[2025-11-13 01:53:00] 65:36<358:32, 8.24s/it | [Iter  478/3090] R0[388/3000]  | LR: 0.023987 | E: -61.411145 | E_var:     6.5592 E_err:   0.040017 | NF_loss: 157.194891
[2025-11-13 01:53:08] 65:44<358:22, 8.24s/it | [Iter  479/3090] R0[389/3000]  | LR: 0.023982 | E: -61.380575 | E_var:     6.5921 E_err:   0.040117 | NF_loss: 576.074844
[2025-11-13 01:53:16] 65:52<358:12, 8.23s/it | [Iter  480/3090] R0[390/3000]  | LR: 0.023977 | E: -61.317223 | E_var:     6.3902 E_err:   0.039498 | NF_loss: 1210.343589
[2025-11-13 01:53:23] 66:00<358:02, 8.23s/it | [Iter  481/3090] R0[391/3000]  | LR: 0.023972 | E: -61.530621 | E_var:     6.6351 E_err:   0.040248 | NF_loss: 117.578840
[2025-11-13 01:53:31] 66:08<357:52, 8.23s/it | [Iter  482/3090] R0[392/3000]  | LR: 0.023967 | E: -61.649032 | E_var:     6.4050 E_err:   0.039544 | NF_loss: 410.014658
[2025-11-13 01:53:39] 66:16<357:41, 8.23s/it | [Iter  483/3090] R0[393/3000]  | LR: 0.023962 | E: -61.662823 | E_var:     6.2038 E_err:   0.038918 | NF_loss: 294.560537
[2025-11-13 01:53:47] 66:24<357:31, 8.23s/it | [Iter  484/3090] R0[394/3000]  | LR: 0.023956 | E: -61.693753 | E_var:     6.3006 E_err:   0.039220 | NF_loss: 155.131472
[2025-11-13 01:53:55] 66:31<357:21, 8.23s/it | [Iter  485/3090] R0[395/3000]  | LR: 0.023951 | E: -61.745141 | E_var:     6.6609 E_err:   0.040326 | NF_loss: 246.630411
[2025-11-13 01:54:03] 66:39<357:11, 8.23s/it | [Iter  486/3090] R0[396/3000]  | LR: 0.023946 | E: -61.679627 | E_var:     8.4630 E_err:   0.045455 | NF_loss: 106.494800
[2025-11-13 01:54:11] 66:47<357:01, 8.23s/it | [Iter  487/3090] R0[397/3000]  | LR: 0.023941 | E: -61.589869 | E_var:     6.5961 E_err:   0.040130 | NF_loss: 231.936467
[2025-11-13 01:54:19] 66:55<356:51, 8.23s/it | [Iter  488/3090] R0[398/3000]  | LR: 0.023935 | E: -61.311232 | E_var:     7.0481 E_err:   0.041482 | NF_loss: 399.110653
[2025-11-13 01:54:27] 67:03<356:42, 8.23s/it | [Iter  489/3090] R0[399/3000]  | LR: 0.023930 | E: -61.332708 | E_var:     7.1163 E_err:   0.041682 | NF_loss: 212.614696
[2025-11-13 01:54:35] 67:11<356:32, 8.23s/it | [Iter  490/3090] R0[400/3000]  | LR: 0.023925 | E: -61.592705 | E_var:     6.1533 E_err:   0.038759 | NF_loss: 137.738977
[2025-11-13 01:54:42] 67:19<356:22, 8.23s/it | [Iter  491/3090] R0[401/3000]  | LR: 0.023919 | E: -61.684637 | E_var:     6.1949 E_err:   0.038890 | NF_loss: 320.260708
[2025-11-13 01:54:50] 67:27<356:12, 8.23s/it | [Iter  492/3090] R0[402/3000]  | LR: 0.023914 | E: -61.774999 | E_var:     6.5263 E_err:   0.039917 | NF_loss: 820.010329
[2025-11-13 01:54:58] 67:35<356:02, 8.23s/it | [Iter  493/3090] R0[403/3000]  | LR: 0.023909 | E: -61.774566 | E_var:     7.2801 E_err:   0.042159 | NF_loss: 32.971654
[2025-11-13 01:55:06] 67:43<355:52, 8.22s/it | [Iter  494/3090] R0[404/3000]  | LR: 0.023903 | E: -61.797552 | E_var:     6.1084 E_err:   0.038617 | NF_loss: 380.771672
[2025-11-13 01:55:14] 67:51<355:41, 8.22s/it | [Iter  495/3090] R0[405/3000]  | LR: 0.023898 | E: -61.776056 | E_var:     6.3264 E_err:   0.039300 | NF_loss: 274.355677
[2025-11-13 01:55:22] 67:58<355:31, 8.22s/it | [Iter  496/3090] R0[406/3000]  | LR: 0.023893 | E: -61.756043 | E_var:     6.4968 E_err:   0.039826 | NF_loss: 238.699289
[2025-11-13 01:55:30] 68:06<355:21, 8.22s/it | [Iter  497/3090] R0[407/3000]  | LR: 0.023887 | E: -61.664475 | E_var:     6.3188 E_err:   0.039277 | NF_loss: 187.274560
[2025-11-13 01:55:38] 68:14<355:11, 8.22s/it | [Iter  498/3090] R0[408/3000]  | LR: 0.023882 | E: -61.533432 | E_var:     6.9031 E_err:   0.041053 | NF_loss: 54.723607
[2025-11-13 01:55:45] 68:22<355:01, 8.22s/it | [Iter  499/3090] R0[409/3000]  | LR: 0.023876 | E: -61.495857 | E_var:     6.2455 E_err:   0.039048 | NF_loss: 136.223154
[2025-11-13 01:55:53] 68:30<354:51, 8.22s/it | [Iter  500/3090] R0[410/3000]  | LR: 0.023871 | E: -61.534415 | E_var:     6.3858 E_err:   0.039485 | NF_loss: 349.481143
[2025-11-13 01:56:01] 68:38<354:41, 8.22s/it | [Iter  501/3090] R0[411/3000]  | LR: 0.023865 | E: -61.519238 | E_var:     6.5627 E_err:   0.040028 | NF_loss: 132.806266
[2025-11-13 01:56:09] 68:46<354:31, 8.22s/it | [Iter  502/3090] R0[412/3000]  | LR: 0.023860 | E: -61.440442 | E_var:     6.2144 E_err:   0.038951 | NF_loss: 230.831269
[2025-11-13 01:56:17] 68:53<354:21, 8.22s/it | [Iter  503/3090] R0[413/3000]  | LR: 0.023855 | E: -61.339020 | E_var:     7.1628 E_err:   0.041818 | NF_loss: 210.403455
[2025-11-13 01:56:25] 69:01<354:11, 8.22s/it | [Iter  504/3090] R0[414/3000]  | LR: 0.023849 | E: -61.490614 | E_var:     7.1845 E_err:   0.041881 | NF_loss: 471.544854
[2025-11-13 01:56:33] 69:09<354:01, 8.22s/it | [Iter  505/3090] R0[415/3000]  | LR: 0.023844 | E: -61.356214 | E_var:     6.9725 E_err:   0.041259 | NF_loss: 141.338134
[2025-11-13 01:56:41] 69:17<353:51, 8.22s/it | [Iter  506/3090] R0[416/3000]  | LR: 0.023838 | E: -61.468785 | E_var:     6.0981 E_err:   0.038585 | NF_loss: 438.020715
[2025-11-13 01:56:48] 69:25<353:41, 8.22s/it | [Iter  507/3090] R0[417/3000]  | LR: 0.023833 | E: -61.334304 | E_var:     6.4346 E_err:   0.039635 | NF_loss: 545.259035
[2025-11-13 01:56:56] 69:33<353:31, 8.22s/it | [Iter  508/3090] R0[418/3000]  | LR: 0.023827 | E: -61.506180 | E_var:     6.5228 E_err:   0.039906 | NF_loss: 387.039728
[2025-11-13 01:57:04] 69:41<353:21, 8.21s/it | [Iter  509/3090] R0[419/3000]  | LR: 0.023821 | E: -61.522023 | E_var:     6.5796 E_err:   0.040079 | NF_loss: 220.692482
[2025-11-13 01:57:12] 69:49<353:11, 8.21s/it | [Iter  510/3090] R0[420/3000]  | LR: 0.023816 | E: -61.530859 | E_var:     6.8963 E_err:   0.041033 | NF_loss: 229.871826
[2025-11-13 01:57:20] 69:56<353:01, 8.21s/it | [Iter  511/3090] R0[421/3000]  | LR: 0.023810 | E: -61.504480 | E_var:     6.7346 E_err:   0.040549 | NF_loss: 145.279873
[2025-11-13 01:57:28] 70:04<352:52, 8.21s/it | [Iter  512/3090] R0[422/3000]  | LR: 0.023805 | E: -61.553290 | E_var:     6.3333 E_err:   0.039322 | NF_loss: 346.203089
[2025-11-13 01:57:36] 70:12<352:42, 8.21s/it | [Iter  513/3090] R0[423/3000]  | LR: 0.023799 | E: -61.457528 | E_var:     6.2097 E_err:   0.038936 | NF_loss: 342.986034
[2025-11-13 01:57:44] 70:20<352:32, 8.21s/it | [Iter  514/3090] R0[424/3000]  | LR: 0.023794 | E: -61.303610 | E_var:     6.4373 E_err:   0.039643 | NF_loss: 244.072125
[2025-11-13 01:57:51] 70:28<352:22, 8.21s/it | [Iter  515/3090] R0[425/3000]  | LR: 0.023788 | E: -61.217144 | E_var:     6.5556 E_err:   0.040006 | NF_loss: 278.691343
[2025-11-13 01:57:59] 70:36<352:12, 8.21s/it | [Iter  516/3090] R0[426/3000]  | LR: 0.023782 | E: -61.140684 | E_var:     7.0206 E_err:   0.041401 | NF_loss: 448.149364
[2025-11-13 01:58:07] 70:44<352:02, 8.21s/it | [Iter  517/3090] R0[427/3000]  | LR: 0.023777 | E: -61.416033 | E_var:     6.7315 E_err:   0.040539 | NF_loss: 753.811213
[2025-11-13 01:58:15] 70:52<351:52, 8.21s/it | [Iter  518/3090] R0[428/3000]  | LR: 0.023771 | E: -61.267682 | E_var:     6.6306 E_err:   0.040234 | NF_loss: 244.788291
[2025-11-13 01:58:23] 70:59<351:42, 8.21s/it | [Iter  519/3090] R0[429/3000]  | LR: 0.023765 | E: -61.073223 | E_var:     6.9270 E_err:   0.041124 | NF_loss: 298.567237
[2025-11-13 01:58:31] 71:07<351:32, 8.21s/it | [Iter  520/3090] R0[430/3000]  | LR: 0.023760 | E: -61.519653 | E_var:     6.7321 E_err:   0.040541 | NF_loss: 224.190324
[2025-11-13 01:58:39] 71:15<351:24, 8.21s/it | [Iter  521/3090] R0[431/3000]  | LR: 0.023754 | E: -61.220646 | E_var:     6.5173 E_err:   0.039889 | NF_loss: 216.430478
[2025-11-13 01:58:47] 71:23<351:14, 8.21s/it | [Iter  522/3090] R0[432/3000]  | LR: 0.023748 | E: -60.935174 | E_var:     7.1745 E_err:   0.041852 | NF_loss: 162.927214
[2025-11-13 01:58:55] 71:31<351:04, 8.21s/it | [Iter  523/3090] R0[433/3000]  | LR: 0.023743 | E: -61.165142 | E_var:     7.2005 E_err:   0.041928 | NF_loss: 105.721896
[2025-11-13 01:59:03] 71:39<350:54, 8.21s/it | [Iter  524/3090] R0[434/3000]  | LR: 0.023737 | E: -60.638782 | E_var:     8.1976 E_err:   0.044737 | NF_loss: 274.035906
[2025-11-13 01:59:10] 71:47<350:44, 8.20s/it | [Iter  525/3090] R0[435/3000]  | LR: 0.023731 | E: -60.737779 | E_var:     7.4374 E_err:   0.042612 | NF_loss: 159.670632
[2025-11-13 01:59:18] 71:55<350:35, 8.20s/it | [Iter  526/3090] R0[436/3000]  | LR: 0.023725 | E: -60.927070 | E_var:     7.4418 E_err:   0.042625 | NF_loss: 194.410599
[2025-11-13 01:59:26] 72:03<350:25, 8.20s/it | [Iter  527/3090] R0[437/3000]  | LR: 0.023720 | E: -61.205878 | E_var:     6.7483 E_err:   0.040590 | NF_loss: 251.063613
[2025-11-13 01:59:34] 72:11<350:15, 8.20s/it | [Iter  528/3090] R0[438/3000]  | LR: 0.023714 | E: -61.460599 | E_var:     6.3260 E_err:   0.039299 | NF_loss: 158.485964
[2025-11-13 01:59:42] 72:18<350:05, 8.20s/it | [Iter  529/3090] R0[439/3000]  | LR: 0.023708 | E: -61.597046 | E_var:     6.0637 E_err:   0.038476 | NF_loss: 161.766640
[2025-11-13 01:59:50] 72:26<349:55, 8.20s/it | [Iter  530/3090] R0[440/3000]  | LR: 0.023702 | E: -61.522695 | E_var:     6.4332 E_err:   0.039631 | NF_loss: 173.019084
[2025-11-13 01:59:58] 72:34<349:46, 8.20s/it | [Iter  531/3090] R0[441/3000]  | LR: 0.023696 | E: -61.606002 | E_var:     6.6999 E_err:   0.040444 | NF_loss: 204.784610
[2025-11-13 02:00:06] 72:42<349:36, 8.20s/it | [Iter  532/3090] R0[442/3000]  | LR: 0.023691 | E: -61.621632 | E_var:     6.4774 E_err:   0.039767 | NF_loss: 48.233301
[2025-11-13 02:00:13] 72:50<349:26, 8.20s/it | [Iter  533/3090] R0[443/3000]  | LR: 0.023685 | E: -61.648367 | E_var:     7.0201 E_err:   0.041399 | NF_loss: 119.526829
[2025-11-13 02:00:21] 72:58<349:16, 8.20s/it | [Iter  534/3090] R0[444/3000]  | LR: 0.023679 | E: -61.830996 | E_var:     6.6845 E_err:   0.040397 | NF_loss: 132.786417
[2025-11-13 02:00:29] 73:06<349:07, 8.20s/it | [Iter  535/3090] R0[445/3000]  | LR: 0.023673 | E: -61.972392 | E_var:     6.8492 E_err:   0.040892 | NF_loss: 16.462404
[2025-11-13 02:00:37] 73:14<348:57, 8.20s/it | [Iter  536/3090] R0[446/3000]  | LR: 0.023667 | E: -61.851694 | E_var:     6.3064 E_err:   0.039238 | NF_loss: 55.497264
[2025-11-13 02:00:45] 73:21<348:47, 8.20s/it | [Iter  537/3090] R0[447/3000]  | LR: 0.023661 | E: -61.998709 | E_var:     5.7614 E_err:   0.037504 | NF_loss: 121.928839
[2025-11-13 02:00:53] 73:29<348:37, 8.20s/it | [Iter  538/3090] R0[448/3000]  | LR: 0.023655 | E: -61.902049 | E_var:     5.8172 E_err:   0.037686 | NF_loss: 103.416915
[2025-11-13 02:01:01] 73:37<348:28, 8.20s/it | [Iter  539/3090] R0[449/3000]  | LR: 0.023649 | E: -61.997555 | E_var:     6.3225 E_err:   0.039288 | NF_loss: 89.293414
[2025-11-13 02:01:09] 73:45<348:18, 8.20s/it | [Iter  540/3090] R0[450/3000]  | LR: 0.023644 | E: -61.983065 | E_var:     6.1396 E_err:   0.038716 | NF_loss: 290.743698
[2025-11-13 02:01:16] 73:53<348:08, 8.19s/it | [Iter  541/3090] R0[451/3000]  | LR: 0.023638 | E: -61.958896 | E_var:     6.0209 E_err:   0.038340 | NF_loss: 17.530896
[2025-11-13 02:01:24] 74:01<347:59, 8.19s/it | [Iter  542/3090] R0[452/3000]  | LR: 0.023632 | E: -61.969291 | E_var:     5.8538 E_err:   0.037804 | NF_loss: 162.762720
[2025-11-13 02:01:32] 74:09<347:49, 8.19s/it | [Iter  543/3090] R0[453/3000]  | LR: 0.023626 | E: -61.849596 | E_var:     6.1639 E_err:   0.038792 | NF_loss: 326.113496
[2025-11-13 02:01:40] 74:17<347:39, 8.19s/it | [Iter  544/3090] R0[454/3000]  | LR: 0.023620 | E: -61.935113 | E_var:     6.2284 E_err:   0.038995 | NF_loss: 92.621738
[2025-11-13 02:01:48] 74:24<347:29, 8.19s/it | [Iter  545/3090] R0[455/3000]  | LR: 0.023614 | E: -61.965897 | E_var:     5.6183 E_err:   0.037036 | NF_loss: 127.491734
[2025-11-13 02:01:56] 74:32<347:20, 8.19s/it | [Iter  546/3090] R0[456/3000]  | LR: 0.023608 | E: -61.988888 | E_var:     5.6859 E_err:   0.037258 | NF_loss: 93.293883
[2025-11-13 02:02:04] 74:40<347:10, 8.19s/it | [Iter  547/3090] R0[457/3000]  | LR: 0.023602 | E: -61.864858 | E_var:     5.8387 E_err:   0.037755 | NF_loss: 64.581910
[2025-11-13 02:02:11] 74:48<347:00, 8.19s/it | [Iter  548/3090] R0[458/3000]  | LR: 0.023596 | E: -61.887334 | E_var:     6.0261 E_err:   0.038356 | NF_loss: 185.970261
[2025-11-13 02:02:19] 74:56<346:51, 8.19s/it | [Iter  549/3090] R0[459/3000]  | LR: 0.023590 | E: -61.937469 | E_var:     6.2726 E_err:   0.039133 | NF_loss: 141.736042
[2025-11-13 02:02:27] 75:04<346:41, 8.19s/it | [Iter  550/3090] R0[460/3000]  | LR: 0.023584 | E: -62.061920 | E_var:     5.9765 E_err:   0.038198 | NF_loss: 64.440562
[2025-11-13 02:02:35] 75:12<346:31, 8.19s/it | [Iter  551/3090] R0[461/3000]  | LR: 0.023578 | E: -61.961636 | E_var:     6.1794 E_err:   0.038841 | NF_loss: 172.894065
[2025-11-13 02:02:43] 75:20<346:22, 8.19s/it | [Iter  552/3090] R0[462/3000]  | LR: 0.023571 | E: -62.002381 | E_var:     6.3013 E_err:   0.039223 | NF_loss: 69.467729
[2025-11-13 02:02:51] 75:27<346:12, 8.19s/it | [Iter  553/3090] R0[463/3000]  | LR: 0.023565 | E: -61.974415 | E_var:     5.9757 E_err:   0.038196 | NF_loss: 38.693572
[2025-11-13 02:02:59] 75:35<346:02, 8.19s/it | [Iter  554/3090] R0[464/3000]  | LR: 0.023559 | E: -62.020578 | E_var:     6.2433 E_err:   0.039042 | NF_loss: 73.306284
[2025-11-13 02:03:07] 75:43<345:53, 8.19s/it | [Iter  555/3090] R0[465/3000]  | LR: 0.023553 | E: -61.990873 | E_var:     5.9680 E_err:   0.038171 | NF_loss: 63.347227
[2025-11-13 02:03:14] 75:51<345:43, 8.19s/it | [Iter  556/3090] R0[466/3000]  | LR: 0.023547 | E: -62.076521 | E_var:     6.7808 E_err:   0.040687 | NF_loss: 129.640497
[2025-11-13 02:03:22] 75:59<345:34, 8.19s/it | [Iter  557/3090] R0[467/3000]  | LR: 0.023541 | E: -62.055641 | E_var:     6.4386 E_err:   0.039647 | NF_loss: 58.023183
[2025-11-13 02:03:30] 76:07<345:24, 8.19s/it | [Iter  558/3090] R0[468/3000]  | LR: 0.023535 | E: -61.878986 | E_var:     7.2600 E_err:   0.042101 | NF_loss: 109.717511
[2025-11-13 02:03:38] 76:15<345:14, 8.18s/it | [Iter  559/3090] R0[469/3000]  | LR: 0.023529 | E: -61.731372 | E_var:     6.9363 E_err:   0.041151 | NF_loss: 57.378765
[2025-11-13 02:03:46] 76:22<345:05, 8.18s/it | [Iter  560/3090] R0[470/3000]  | LR: 0.023522 | E: -61.798045 | E_var:     8.2838 E_err:   0.044971 | NF_loss: 102.347308
[2025-11-13 02:03:54] 76:30<344:55, 8.18s/it | [Iter  561/3090] R0[471/3000]  | LR: 0.023516 | E: -61.542613 | E_var:     7.4035 E_err:   0.042515 | NF_loss: 126.149300
[2025-11-13 02:04:02] 76:38<344:46, 8.18s/it | [Iter  562/3090] R0[472/3000]  | LR: 0.023510 | E: -61.684782 | E_var:     7.0747 E_err:   0.041560 | NF_loss: 118.489448
[2025-11-13 02:04:10] 76:46<344:36, 8.18s/it | [Iter  563/3090] R0[473/3000]  | LR: 0.023504 | E: -61.945355 | E_var:     5.8729 E_err:   0.037866 | NF_loss: 32.086913
[2025-11-13 02:04:17] 76:54<344:26, 8.18s/it | [Iter  564/3090] R0[474/3000]  | LR: 0.023498 | E: -61.973319 | E_var:     5.8599 E_err:   0.037824 | NF_loss: 50.141479
[2025-11-13 02:04:25] 77:02<344:17, 8.18s/it | [Iter  565/3090] R0[475/3000]  | LR: 0.023491 | E: -61.993985 | E_var:     6.3674 E_err:   0.039428 | NF_loss: 47.681274
[2025-11-13 02:04:33] 77:10<344:08, 8.18s/it | [Iter  566/3090] R0[476/3000]  | LR: 0.023485 | E: -62.017486 | E_var:     5.7979 E_err:   0.037623 | NF_loss: 48.505284
[2025-11-13 02:04:41] 77:18<343:59, 8.18s/it | [Iter  567/3090] R0[477/3000]  | LR: 0.023479 | E: -62.003297 | E_var:     6.0459 E_err:   0.038420 | NF_loss: 52.536992
[2025-11-13 02:04:49] 77:26<343:49, 8.18s/it | [Iter  568/3090] R0[478/3000]  | LR: 0.023473 | E: -61.976910 | E_var:     5.8937 E_err:   0.037933 | NF_loss: 19.763186
[2025-11-13 02:04:57] 77:34<343:40, 8.18s/it | [Iter  569/3090] R0[479/3000]  | LR: 0.023466 | E: -62.052204 | E_var:     6.6351 E_err:   0.040248 | NF_loss: 80.894899
[2025-11-13 02:05:05] 77:41<343:30, 8.18s/it | [Iter  570/3090] R0[480/3000]  | LR: 0.023460 | E: -61.978373 | E_var:     6.4255 E_err:   0.039607 | NF_loss: 72.496405
[2025-11-13 02:05:13] 77:49<343:21, 8.18s/it | [Iter  571/3090] R0[481/3000]  | LR: 0.023454 | E: -61.997707 | E_var:     6.0558 E_err:   0.038451 | NF_loss: 191.606192
[2025-11-13 02:05:21] 77:57<343:11, 8.18s/it | [Iter  572/3090] R0[482/3000]  | LR: 0.023448 | E: -62.004051 | E_var:     5.8923 E_err:   0.037928 | NF_loss: 42.328764
[2025-11-13 02:05:29] 78:05<343:02, 8.18s/it | [Iter  573/3090] R0[483/3000]  | LR: 0.023441 | E: -61.977291 | E_var:     6.2790 E_err:   0.039153 | NF_loss: 44.783856
[2025-11-13 02:05:36] 78:13<342:52, 8.18s/it | [Iter  574/3090] R0[484/3000]  | LR: 0.023435 | E: -61.993581 | E_var:     6.5439 E_err:   0.039970 | NF_loss: 55.875575
[2025-11-13 02:05:44] 78:21<342:43, 8.18s/it | [Iter  575/3090] R0[485/3000]  | LR: 0.023429 | E: -62.135773 | E_var:     6.3984 E_err:   0.039524 | NF_loss: 143.534176
[2025-11-13 02:05:52] 78:29<342:33, 8.18s/it | [Iter  576/3090] R0[486/3000]  | LR: 0.023422 | E: -61.998406 | E_var:     5.7802 E_err:   0.037566 | NF_loss: 86.264376
[2025-11-13 02:06:00] 78:37<342:24, 8.18s/it | [Iter  577/3090] R0[487/3000]  | LR: 0.023416 | E: -61.963262 | E_var:     6.4599 E_err:   0.039713 | NF_loss: 63.908368
[2025-11-13 02:06:08] 78:44<342:14, 8.17s/it | [Iter  578/3090] R0[488/3000]  | LR: 0.023409 | E: -61.972315 | E_var:     6.2148 E_err:   0.038952 | NF_loss: 28.906478
[2025-11-13 02:06:16] 78:52<342:05, 8.17s/it | [Iter  579/3090] R0[489/3000]  | LR: 0.023403 | E: -61.887586 | E_var:     5.9797 E_err:   0.038209 | NF_loss: 79.743353
[2025-11-13 02:06:24] 79:00<341:55, 8.17s/it | [Iter  580/3090] R0[490/3000]  | LR: 0.023397 | E: -61.771378 | E_var:     6.1093 E_err:   0.038620 | NF_loss: 115.127057
[2025-11-13 02:06:32] 79:08<341:46, 8.17s/it | [Iter  581/3090] R0[491/3000]  | LR: 0.023390 | E: -61.790799 | E_var:     6.0249 E_err:   0.038353 | NF_loss: 187.290683
[2025-11-13 02:06:39] 79:16<341:36, 8.17s/it | [Iter  582/3090] R0[492/3000]  | LR: 0.023384 | E: -61.928501 | E_var:     6.2227 E_err:   0.038977 | NF_loss: 60.825224
[2025-11-13 02:06:47] 79:24<341:27, 8.17s/it | [Iter  583/3090] R0[493/3000]  | LR: 0.023377 | E: -61.968135 | E_var:     6.2498 E_err:   0.039062 | NF_loss: 61.159525
[2025-11-13 02:06:55] 79:32<341:17, 8.17s/it | [Iter  584/3090] R0[494/3000]  | LR: 0.023371 | E: -62.000745 | E_var:     5.9332 E_err:   0.038060 | NF_loss: 87.797166
[2025-11-13 02:07:03] 79:40<341:08, 8.17s/it | [Iter  585/3090] R0[495/3000]  | LR: 0.023364 | E: -62.027707 | E_var:     5.9701 E_err:   0.038178 | NF_loss: 544.011016
[2025-11-13 02:07:11] 79:47<340:58, 8.17s/it | [Iter  586/3090] R0[496/3000]  | LR: 0.023358 | E: -62.003780 | E_var:     6.0053 E_err:   0.038290 | NF_loss: 50.070141
[2025-11-13 02:07:19] 79:55<340:49, 8.17s/it | [Iter  587/3090] R0[497/3000]  | LR: 0.023351 | E: -61.935444 | E_var:     5.9143 E_err:   0.037999 | NF_loss: 156.605206
[2025-11-13 02:07:27] 80:03<340:39, 8.17s/it | [Iter  588/3090] R0[498/3000]  | LR: 0.023345 | E: -61.915319 | E_var:     6.0092 E_err:   0.038303 | NF_loss: 126.183773
[2025-11-13 02:07:34] 80:11<340:30, 8.17s/it | [Iter  589/3090] R0[499/3000]  | LR: 0.023338 | E: -61.843641 | E_var:     5.9538 E_err:   0.038126 | NF_loss: 34.739081
[2025-11-13 02:07:42] 80:19<340:21, 8.17s/it | [Iter  590/3090] R0[500/3000]  | LR: 0.023332 | E: -61.959592 | E_var:     6.4165 E_err:   0.039579 | NF_loss: 94.873869
[2025-11-13 02:07:50] 80:27<340:11, 8.17s/it | [Iter  591/3090] R0[501/3000]  | LR: 0.023325 | E: -61.898518 | E_var:     6.1118 E_err:   0.038628 | NF_loss: 157.471950
[2025-11-13 02:07:58] 80:35<340:02, 8.17s/it | [Iter  592/3090] R0[502/3000]  | LR: 0.023319 | E: -61.915788 | E_var:     6.1144 E_err:   0.038636 | NF_loss: 217.491595
[2025-11-13 02:08:06] 80:42<339:52, 8.17s/it | [Iter  593/3090] R0[503/3000]  | LR: 0.023312 | E: -61.885660 | E_var:     5.9622 E_err:   0.038153 | NF_loss: 114.707354
[2025-11-13 02:08:14] 80:50<339:43, 8.17s/it | [Iter  594/3090] R0[504/3000]  | LR: 0.023306 | E: -61.841628 | E_var:     5.8611 E_err:   0.037828 | NF_loss: 65.746411
[2025-11-13 02:08:22] 80:58<339:34, 8.17s/it | [Iter  595/3090] R0[505/3000]  | LR: 0.023299 | E: -61.887183 | E_var:     5.9482 E_err:   0.038108 | NF_loss: 96.856078
[2025-11-13 02:08:30] 81:06<339:24, 8.17s/it | [Iter  596/3090] R0[506/3000]  | LR: 0.023292 | E: -61.852512 | E_var:     5.7427 E_err:   0.037444 | NF_loss: 45.657777
[2025-11-13 02:08:37] 81:14<339:15, 8.16s/it | [Iter  597/3090] R0[507/3000]  | LR: 0.023286 | E: -61.915380 | E_var:     5.8583 E_err:   0.037819 | NF_loss: 31.272384
[2025-11-13 02:08:45] 81:22<339:05, 8.16s/it | [Iter  598/3090] R0[508/3000]  | LR: 0.023279 | E: -61.983139 | E_var:     6.0638 E_err:   0.038476 | NF_loss: 65.659573
[2025-11-13 02:08:53] 81:30<338:56, 8.16s/it | [Iter  599/3090] R0[509/3000]  | LR: 0.023273 | E: -61.817575 | E_var:     5.8625 E_err:   0.037832 | NF_loss: 65.775604
[2025-11-13 02:09:01] 81:38<338:47, 8.16s/it | [Iter  600/3090] R0[510/3000]  | LR: 0.023266 | E: -61.967012 | E_var:     6.3250 E_err:   0.039296 | NF_loss: 42.419908
[2025-11-13 02:09:01] 保存checkpoint: hybrid_checkpoint_iter_000600.pkl
[2025-11-13 02:09:09] 81:46<338:39, 8.16s/it | [Iter  601/3090] R0[511/3000]  | LR: 0.023259 | E: -61.880829 | E_var:     6.0408 E_err:   0.038403 | NF_loss: 48.010973
[2025-11-13 02:09:17] 81:54<338:30, 8.16s/it | [Iter  602/3090] R0[512/3000]  | LR: 0.023253 | E: -61.962896 | E_var:     6.4006 E_err:   0.039530 | NF_loss: 117.630223
[2025-11-13 02:09:25] 82:02<338:20, 8.16s/it | [Iter  603/3090] R0[513/3000]  | LR: 0.023246 | E: -61.843955 | E_var:     6.4302 E_err:   0.039622 | NF_loss: 43.764823
[2025-11-13 02:09:33] 82:10<338:11, 8.16s/it | [Iter  604/3090] R0[514/3000]  | LR: 0.023239 | E: -61.610863 | E_var:     6.4245 E_err:   0.039604 | NF_loss: 47.175017
[2025-11-13 02:09:41] 82:17<338:02, 8.16s/it | [Iter  605/3090] R0[515/3000]  | LR: 0.023233 | E: -61.763300 | E_var:     6.2777 E_err:   0.039149 | NF_loss: 66.780786
[2025-11-13 02:09:49] 82:25<337:52, 8.16s/it | [Iter  606/3090] R0[516/3000]  | LR: 0.023226 | E: -61.687362 | E_var:     6.2922 E_err:   0.039194 | NF_loss: 52.952926
[2025-11-13 02:09:57] 82:33<337:43, 8.16s/it | [Iter  607/3090] R0[517/3000]  | LR: 0.023219 | E: -61.735091 | E_var:     5.9380 E_err:   0.038075 | NF_loss: 71.514693
[2025-11-13 02:10:04] 82:41<337:34, 8.16s/it | [Iter  608/3090] R0[518/3000]  | LR: 0.023212 | E: -61.876649 | E_var:     5.8034 E_err:   0.037641 | NF_loss: 31.742663
[2025-11-13 02:10:12] 82:49<337:24, 8.16s/it | [Iter  609/3090] R0[519/3000]  | LR: 0.023206 | E: -61.901468 | E_var:     6.3352 E_err:   0.039328 | NF_loss: 56.906212
[2025-11-13 02:10:20] 82:57<337:15, 8.16s/it | [Iter  610/3090] R0[520/3000]  | LR: 0.023199 | E: -61.899806 | E_var:     5.5872 E_err:   0.036933 | NF_loss: 35.650446
[2025-11-13 02:10:28] 83:05<337:06, 8.16s/it | [Iter  611/3090] R0[521/3000]  | LR: 0.023192 | E: -61.955532 | E_var:     6.2480 E_err:   0.039056 | NF_loss: 14.505496
[2025-11-13 02:10:36] 83:13<336:56, 8.16s/it | [Iter  612/3090] R0[522/3000]  | LR: 0.023185 | E: -61.999325 | E_var:     6.1669 E_err:   0.038802 | NF_loss: 28.473770
[2025-11-13 02:10:44] 83:20<336:47, 8.16s/it | [Iter  613/3090] R0[523/3000]  | LR: 0.023178 | E: -62.127206 | E_var:     6.2816 E_err:   0.039161 | NF_loss: 56.088310
[2025-11-13 02:10:52] 83:28<336:38, 8.16s/it | [Iter  614/3090] R0[524/3000]  | LR: 0.023172 | E: -62.038842 | E_var:     6.0341 E_err:   0.038382 | NF_loss: 24.782421
[2025-11-13 02:11:00] 83:36<336:28, 8.16s/it | [Iter  615/3090] R0[525/3000]  | LR: 0.023165 | E: -62.100732 | E_var:     6.0887 E_err:   0.038555 | NF_loss: 44.511611
[2025-11-13 02:11:07] 83:44<336:19, 8.16s/it | [Iter  616/3090] R0[526/3000]  | LR: 0.023158 | E: -62.166358 | E_var:     5.5867 E_err:   0.036931 | NF_loss: 28.895742
[2025-11-13 02:11:15] 83:52<336:10, 8.16s/it | [Iter  617/3090] R0[527/3000]  | LR: 0.023151 | E: -62.192570 | E_var:     5.9531 E_err:   0.038123 | NF_loss: 29.659540
[2025-11-13 02:11:23] 84:00<336:00, 8.16s/it | [Iter  618/3090] R0[528/3000]  | LR: 0.023144 | E: -62.154200 | E_var:     5.8948 E_err:   0.037936 | NF_loss: 41.429940
[2025-11-13 02:11:31] 84:08<335:51, 8.16s/it | [Iter  619/3090] R0[529/3000]  | LR: 0.023137 | E: -62.053278 | E_var:     5.6012 E_err:   0.036980 | NF_loss: 25.808777
[2025-11-13 02:11:39] 84:15<335:42, 8.15s/it | [Iter  620/3090] R0[530/3000]  | LR: 0.023131 | E: -62.109471 | E_var:     5.5259 E_err:   0.036730 | NF_loss: 29.078450
[2025-11-13 02:11:47] 84:23<335:33, 8.15s/it | [Iter  621/3090] R0[531/3000]  | LR: 0.023124 | E: -62.173404 | E_var:     5.6722 E_err:   0.037213 | NF_loss: 50.136185
[2025-11-13 02:11:55] 84:31<335:23, 8.15s/it | [Iter  622/3090] R0[532/3000]  | LR: 0.023117 | E: -62.095224 | E_var:     6.0963 E_err:   0.038579 | NF_loss: 34.344416
[2025-11-13 02:12:03] 84:39<335:14, 8.15s/it | [Iter  623/3090] R0[533/3000]  | LR: 0.023110 | E: -62.178164 | E_var:     5.8603 E_err:   0.037825 | NF_loss: 28.884999
[2025-11-13 02:12:10] 84:47<335:05, 8.15s/it | [Iter  624/3090] R0[534/3000]  | LR: 0.023103 | E: -62.234467 | E_var:     5.9834 E_err:   0.038220 | NF_loss: 27.205622
[2025-11-13 02:12:18] 84:55<334:56, 8.15s/it | [Iter  625/3090] R0[535/3000]  | LR: 0.023096 | E: -62.237321 | E_var:     5.7735 E_err:   0.037544 | NF_loss: 32.555906
[2025-11-13 02:12:26] 85:03<334:46, 8.15s/it | [Iter  626/3090] R0[536/3000]  | LR: 0.023089 | E: -62.288201 | E_var:     6.0238 E_err:   0.038349 | NF_loss: 29.393946
[2025-11-13 02:12:34] 85:11<334:37, 8.15s/it | [Iter  627/3090] R0[537/3000]  | LR: 0.023082 | E: -62.298925 | E_var:     5.5324 E_err:   0.036752 | NF_loss: 44.002921
[2025-11-13 02:12:42] 85:18<334:28, 8.15s/it | [Iter  628/3090] R0[538/3000]  | LR: 0.023075 | E: -62.120734 | E_var:     5.7868 E_err:   0.037587 | NF_loss: 62.384351
[2025-11-13 02:12:50] 85:26<334:19, 8.15s/it | [Iter  629/3090] R0[539/3000]  | LR: 0.023068 | E: -62.040047 | E_var:     6.2567 E_err:   0.039083 | NF_loss: 44.824994
[2025-11-13 02:12:58] 85:34<334:09, 8.15s/it | [Iter  630/3090] R0[540/3000]  | LR: 0.023061 | E: -61.976744 | E_var:     6.4176 E_err:   0.039583 | NF_loss: 57.524088
[2025-11-13 02:13:06] 85:42<334:00, 8.15s/it | [Iter  631/3090] R0[541/3000]  | LR: 0.023054 | E: -62.006887 | E_var:     6.3395 E_err:   0.039341 | NF_loss: 43.887627
[2025-11-13 02:13:13] 85:50<333:51, 8.15s/it | [Iter  632/3090] R0[542/3000]  | LR: 0.023047 | E: -62.083613 | E_var:     5.7736 E_err:   0.037544 | NF_loss: 32.998060
[2025-11-13 02:13:21] 85:58<333:42, 8.15s/it | [Iter  633/3090] R0[543/3000]  | LR: 0.023040 | E: -62.106632 | E_var:     6.1725 E_err:   0.038820 | NF_loss: 52.966898
[2025-11-13 02:13:29] 86:06<333:32, 8.15s/it | [Iter  634/3090] R0[544/3000]  | LR: 0.023033 | E: -62.131626 | E_var:     5.5022 E_err:   0.036651 | NF_loss: 35.873008
[2025-11-13 02:13:37] 86:14<333:23, 8.15s/it | [Iter  635/3090] R0[545/3000]  | LR: 0.023026 | E: -61.901616 | E_var:     7.2787 E_err:   0.042155 | NF_loss: 53.850745
[2025-11-13 02:13:45] 86:21<333:14, 8.15s/it | [Iter  636/3090] R0[546/3000]  | LR: 0.023019 | E: -62.104405 | E_var:     5.9225 E_err:   0.038025 | NF_loss: 47.391369
[2025-11-13 02:13:53] 86:29<333:05, 8.15s/it | [Iter  637/3090] R0[547/3000]  | LR: 0.023012 | E: -62.309015 | E_var:     5.7269 E_err:   0.037392 | NF_loss: 28.960628
[2025-11-13 02:14:01] 86:37<332:56, 8.15s/it | [Iter  638/3090] R0[548/3000]  | LR: 0.023005 | E: -62.231991 | E_var:     5.4635 E_err:   0.036522 | NF_loss: 56.893367
[2025-11-13 02:14:09] 86:45<332:46, 8.15s/it | [Iter  639/3090] R0[549/3000]  | LR: 0.022998 | E: -62.270324 | E_var:     5.4508 E_err:   0.036479 | NF_loss: 34.554550
[2025-11-13 02:14:16] 86:53<332:37, 8.15s/it | [Iter  640/3090] R0[550/3000]  | LR: 0.022991 | E: -62.195778 | E_var:     5.4808 E_err:   0.036580 | NF_loss: 40.117078
[2025-11-13 02:14:24] 87:01<332:28, 8.15s/it | [Iter  641/3090] R0[551/3000]  | LR: 0.022983 | E: -62.275703 | E_var:     5.3636 E_err:   0.036187 | NF_loss: 31.762124
[2025-11-13 02:14:32] 87:09<332:19, 8.15s/it | [Iter  642/3090] R0[552/3000]  | LR: 0.022976 | E: -62.299136 | E_var:     5.8057 E_err:   0.037648 | NF_loss: 29.078940
[2025-11-13 02:14:40] 87:17<332:10, 8.14s/it | [Iter  643/3090] R0[553/3000]  | LR: 0.022969 | E: -62.306105 | E_var:     5.5157 E_err:   0.036696 | NF_loss: 35.467494
[2025-11-13 02:14:48] 87:24<332:01, 8.14s/it | [Iter  644/3090] R0[554/3000]  | LR: 0.022962 | E: -62.352563 | E_var:     5.3712 E_err:   0.036212 | NF_loss: 34.154778
[2025-11-13 02:14:56] 87:32<331:51, 8.14s/it | [Iter  645/3090] R0[555/3000]  | LR: 0.022955 | E: -62.304278 | E_var:     5.1845 E_err:   0.035577 | NF_loss: 28.633809
[2025-11-13 02:15:04] 87:40<331:43, 8.14s/it | [Iter  646/3090] R0[556/3000]  | LR: 0.022948 | E: -62.296806 | E_var:     5.5703 E_err:   0.036877 | NF_loss: 40.335310
[2025-11-13 02:15:12] 87:48<331:34, 8.14s/it | [Iter  647/3090] R0[557/3000]  | LR: 0.022940 | E: -62.323284 | E_var:     5.3746 E_err:   0.036224 | NF_loss: 36.794760
[2025-11-13 02:15:20] 87:56<331:25, 8.14s/it | [Iter  648/3090] R0[558/3000]  | LR: 0.022933 | E: -62.244339 | E_var:     5.8274 E_err:   0.037719 | NF_loss: 26.389423
[2025-11-13 02:15:28] 88:04<331:16, 8.14s/it | [Iter  649/3090] R0[559/3000]  | LR: 0.022926 | E: -62.285384 | E_var:     5.6390 E_err:   0.037104 | NF_loss: 21.906196
[2025-11-13 02:15:35] 88:12<331:07, 8.14s/it | [Iter  650/3090] R0[560/3000]  | LR: 0.022919 | E: -62.276887 | E_var:     6.0593 E_err:   0.038462 | NF_loss: 30.640881
[2025-11-13 02:15:43] 88:20<330:57, 8.14s/it | [Iter  651/3090] R0[561/3000]  | LR: 0.022912 | E: -62.312788 | E_var:     6.0109 E_err:   0.038308 | NF_loss: 25.523040
[2025-11-13 02:15:51] 88:28<330:48, 8.14s/it | [Iter  652/3090] R0[562/3000]  | LR: 0.022904 | E: -62.292213 | E_var:     5.9331 E_err:   0.038059 | NF_loss: 23.648220
[2025-11-13 02:15:59] 88:36<330:39, 8.14s/it | [Iter  653/3090] R0[563/3000]  | LR: 0.022897 | E: -62.240645 | E_var:     5.7975 E_err:   0.037622 | NF_loss: 22.296448
[2025-11-13 02:16:07] 88:43<330:30, 8.14s/it | [Iter  654/3090] R0[564/3000]  | LR: 0.022890 | E: -62.171567 | E_var:     5.6638 E_err:   0.037186 | NF_loss: 34.345982
[2025-11-13 02:16:15] 88:51<330:21, 8.14s/it | [Iter  655/3090] R0[565/3000]  | LR: 0.022882 | E: -62.080553 | E_var:     5.8333 E_err:   0.037738 | NF_loss: 21.576692
[2025-11-13 02:16:23] 88:59<330:12, 8.14s/it | [Iter  656/3090] R0[566/3000]  | LR: 0.022875 | E: -62.115922 | E_var:     6.2537 E_err:   0.039074 | NF_loss: 45.013806
[2025-11-13 02:16:31] 89:07<330:03, 8.14s/it | [Iter  657/3090] R0[567/3000]  | LR: 0.022868 | E: -62.197384 | E_var:     6.4102 E_err:   0.039560 | NF_loss: 29.883559
[2025-11-13 02:16:38] 89:15<329:53, 8.14s/it | [Iter  658/3090] R0[568/3000]  | LR: 0.022861 | E: -62.036639 | E_var:     6.3395 E_err:   0.039341 | NF_loss: 27.151164
[2025-11-13 02:16:46] 89:23<329:44, 8.14s/it | [Iter  659/3090] R0[569/3000]  | LR: 0.022853 | E: -61.997866 | E_var:     7.0404 E_err:   0.041459 | NF_loss: 21.166796
[2025-11-13 02:16:54] 89:31<329:35, 8.14s/it | [Iter  660/3090] R0[570/3000]  | LR: 0.022846 | E: -62.131009 | E_var:     6.0343 E_err:   0.038382 | NF_loss: 26.226248
[2025-11-13 02:17:02] 89:39<329:26, 8.14s/it | [Iter  661/3090] R0[571/3000]  | LR: 0.022839 | E: -62.241924 | E_var:     6.0388 E_err:   0.038397 | NF_loss: 21.287746
[2025-11-13 02:17:10] 89:46<329:17, 8.14s/it | [Iter  662/3090] R0[572/3000]  | LR: 0.022831 | E: -62.190662 | E_var:     5.6149 E_err:   0.037025 | NF_loss: 34.647957
[2025-11-13 02:17:18] 89:54<329:08, 8.14s/it | [Iter  663/3090] R0[573/3000]  | LR: 0.022824 | E: -62.217950 | E_var:     6.1837 E_err:   0.038855 | NF_loss: 28.730838
[2025-11-13 02:17:26] 90:02<328:59, 8.14s/it | [Iter  664/3090] R0[574/3000]  | LR: 0.022816 | E: -62.232994 | E_var:     5.6560 E_err:   0.037160 | NF_loss: 27.597013
[2025-11-13 02:17:33] 90:10<328:50, 8.14s/it | [Iter  665/3090] R0[575/3000]  | LR: 0.022809 | E: -62.298090 | E_var:     5.5561 E_err:   0.036830 | NF_loss: 22.071652
[2025-11-13 02:17:41] 90:18<328:41, 8.14s/it | [Iter  666/3090] R0[576/3000]  | LR: 0.022802 | E: -62.301548 | E_var:     6.1442 E_err:   0.038730 | NF_loss: 30.307355
[2025-11-13 02:17:49] 90:26<328:31, 8.14s/it | [Iter  667/3090] R0[577/3000]  | LR: 0.022794 | E: -62.258443 | E_var:     6.5313 E_err:   0.039932 | NF_loss: 42.207203
[2025-11-13 02:17:57] 90:34<328:22, 8.13s/it | [Iter  668/3090] R0[578/3000]  | LR: 0.022787 | E: -62.332437 | E_var:     5.9267 E_err:   0.038039 | NF_loss: 50.093549
[2025-11-13 02:18:05] 90:42<328:13, 8.13s/it | [Iter  669/3090] R0[579/3000]  | LR: 0.022779 | E: -62.327019 | E_var:     5.8016 E_err:   0.037635 | NF_loss: 35.123656
[2025-11-13 02:18:13] 90:49<328:04, 8.13s/it | [Iter  670/3090] R0[580/3000]  | LR: 0.022772 | E: -62.310266 | E_var:     6.4009 E_err:   0.039531 | NF_loss: 32.786565
[2025-11-13 02:18:21] 90:57<327:55, 8.13s/it | [Iter  671/3090] R0[581/3000]  | LR: 0.022764 | E: -62.280023 | E_var:     7.7279 E_err:   0.043436 | NF_loss: 30.980601
[2025-11-13 02:18:29] 91:05<327:46, 8.13s/it | [Iter  672/3090] R0[582/3000]  | LR: 0.022757 | E: -62.261113 | E_var:     5.6967 E_err:   0.037294 | NF_loss: 29.413222
[2025-11-13 02:18:36] 91:13<327:37, 8.13s/it | [Iter  673/3090] R0[583/3000]  | LR: 0.022749 | E: -62.212217 | E_var:     6.3593 E_err:   0.039402 | NF_loss: 25.087471
[2025-11-13 02:18:44] 91:21<327:28, 8.13s/it | [Iter  674/3090] R0[584/3000]  | LR: 0.022742 | E: -62.203262 | E_var:     6.7801 E_err:   0.040685 | NF_loss: 76.009928
[2025-11-13 02:18:52] 91:29<327:19, 8.13s/it | [Iter  675/3090] R0[585/3000]  | LR: 0.022734 | E: -62.198021 | E_var:     5.8275 E_err:   0.037719 | NF_loss: 28.258069
[2025-11-13 02:19:00] 91:37<327:10, 8.13s/it | [Iter  676/3090] R0[586/3000]  | LR: 0.022727 | E: -62.147353 | E_var:     5.6391 E_err:   0.037104 | NF_loss: 51.465680
[2025-11-13 02:19:08] 91:44<327:01, 8.13s/it | [Iter  677/3090] R0[587/3000]  | LR: 0.022719 | E: -62.160037 | E_var:     5.6551 E_err:   0.037157 | NF_loss: 38.272967
[2025-11-13 02:19:16] 91:52<326:52, 8.13s/it | [Iter  678/3090] R0[588/3000]  | LR: 0.022712 | E: -62.127571 | E_var:     5.8060 E_err:   0.037649 | NF_loss: 43.451664
[2025-11-13 02:19:24] 92:00<326:43, 8.13s/it | [Iter  679/3090] R0[589/3000]  | LR: 0.022704 | E: -62.207960 | E_var:     6.3150 E_err:   0.039265 | NF_loss: 41.883583
[2025-11-13 02:19:32] 92:08<326:33, 8.13s/it | [Iter  680/3090] R0[590/3000]  | LR: 0.022697 | E: -62.155894 | E_var:     5.5231 E_err:   0.036721 | NF_loss: 25.832577
[2025-11-13 02:19:39] 92:16<326:24, 8.13s/it | [Iter  681/3090] R0[591/3000]  | LR: 0.022689 | E: -62.284417 | E_var:     5.8062 E_err:   0.037650 | NF_loss: 44.197119
[2025-11-13 02:19:48] 92:24<326:16, 8.13s/it | [Iter  682/3090] R0[592/3000]  | LR: 0.022682 | E: -62.326143 | E_var:     5.9161 E_err:   0.038005 | NF_loss: 35.603086
[2025-11-13 02:19:55] 92:32<326:07, 8.13s/it | [Iter  683/3090] R0[593/3000]  | LR: 0.022674 | E: -62.385144 | E_var:     5.7820 E_err:   0.037571 | NF_loss: 29.274182
[2025-11-13 02:20:03] 92:40<325:58, 8.13s/it | [Iter  684/3090] R0[594/3000]  | LR: 0.022666 | E: -62.298246 | E_var:     5.5482 E_err:   0.036804 | NF_loss: 21.482409
[2025-11-13 02:20:11] 92:48<325:49, 8.13s/it | [Iter  685/3090] R0[595/3000]  | LR: 0.022659 | E: -62.286566 | E_var:     5.9539 E_err:   0.038126 | NF_loss: 25.121925
[2025-11-13 02:20:19] 92:56<325:40, 8.13s/it | [Iter  686/3090] R0[596/3000]  | LR: 0.022651 | E: -62.262231 | E_var:     5.9208 E_err:   0.038020 | NF_loss: 27.437101
[2025-11-13 02:20:27] 93:03<325:31, 8.13s/it | [Iter  687/3090] R0[597/3000]  | LR: 0.022643 | E: -62.225073 | E_var:     5.6478 E_err:   0.037133 | NF_loss: 23.145380
[2025-11-13 02:20:35] 93:11<325:22, 8.13s/it | [Iter  688/3090] R0[598/3000]  | LR: 0.022636 | E: -62.286303 | E_var:     5.5810 E_err:   0.036913 | NF_loss: 27.306385
[2025-11-13 02:20:43] 93:19<325:13, 8.13s/it | [Iter  689/3090] R0[599/3000]  | LR: 0.022628 | E: -62.224873 | E_var:     5.8965 E_err:   0.037942 | NF_loss: 314.929123
[2025-11-13 02:20:51] 93:27<325:04, 8.13s/it | [Iter  690/3090] R0[600/3000]  | LR: 0.022620 | E: -62.179712 | E_var:     5.7216 E_err:   0.037375 | NF_loss: 39.999442
[2025-11-13 02:20:58] 93:35<324:55, 8.13s/it | [Iter  691/3090] R0[601/3000]  | LR: 0.022613 | E: -62.126016 | E_var:     6.0010 E_err:   0.038276 | NF_loss: 33.579507
[2025-11-13 02:21:06] 93:43<324:46, 8.13s/it | [Iter  692/3090] R0[602/3000]  | LR: 0.022605 | E: -62.265930 | E_var:     5.8036 E_err:   0.037642 | NF_loss: 40.728205
[2025-11-13 02:21:14] 93:51<324:37, 8.13s/it | [Iter  693/3090] R0[603/3000]  | LR: 0.022597 | E: -62.275518 | E_var:     6.0638 E_err:   0.038476 | NF_loss: 27.848774
[2025-11-13 02:21:22] 93:59<324:28, 8.13s/it | [Iter  694/3090] R0[604/3000]  | LR: 0.022590 | E: -62.403680 | E_var:     5.4758 E_err:   0.036563 | NF_loss: 25.597269
[2025-11-13 02:21:30] 94:06<324:19, 8.13s/it | [Iter  695/3090] R0[605/3000]  | LR: 0.022582 | E: -62.337007 | E_var:     5.9072 E_err:   0.037976 | NF_loss: 24.503807
[2025-11-13 02:21:38] 94:14<324:10, 8.12s/it | [Iter  696/3090] R0[606/3000]  | LR: 0.022574 | E: -62.357007 | E_var:     5.3574 E_err:   0.036166 | NF_loss: 70.509435
[2025-11-13 02:21:46] 94:22<324:01, 8.12s/it | [Iter  697/3090] R0[607/3000]  | LR: 0.022566 | E: -62.376247 | E_var:     5.6050 E_err:   0.036992 | NF_loss: 39.034453
[2025-11-13 02:21:54] 94:30<323:52, 8.12s/it | [Iter  698/3090] R0[608/3000]  | LR: 0.022559 | E: -62.374712 | E_var:     5.6303 E_err:   0.037075 | NF_loss: 17.973878
[2025-11-13 02:22:01] 94:38<323:43, 8.12s/it | [Iter  699/3090] R0[609/3000]  | LR: 0.022551 | E: -62.421048 | E_var:     5.6561 E_err:   0.037160 | NF_loss: 127.675867
[2025-11-13 02:22:09] 94:46<323:34, 8.12s/it | [Iter  700/3090] R0[610/3000]  | LR: 0.022543 | E: -62.407120 | E_var:     5.6287 E_err:   0.037070 | NF_loss: 63.046564
[2025-11-13 02:22:17] 94:54<323:25, 8.12s/it | [Iter  701/3090] R0[611/3000]  | LR: 0.022535 | E: -62.377571 | E_var:     5.7098 E_err:   0.037336 | NF_loss: 68.551682
[2025-11-13 02:22:25] 95:02<323:16, 8.12s/it | [Iter  702/3090] R0[612/3000]  | LR: 0.022527 | E: -62.391789 | E_var:     5.6276 E_err:   0.037067 | NF_loss: 55.225529
[2025-11-13 02:22:33] 95:09<323:07, 8.12s/it | [Iter  703/3090] R0[613/3000]  | LR: 0.022520 | E: -62.403887 | E_var:     5.0963 E_err:   0.035273 | NF_loss: 33.833845
[2025-11-13 02:22:41] 95:17<322:58, 8.12s/it | [Iter  704/3090] R0[614/3000]  | LR: 0.022512 | E: -62.335280 | E_var:     5.7167 E_err:   0.037359 | NF_loss: 29.947930
[2025-11-13 02:22:49] 95:25<322:49, 8.12s/it | [Iter  705/3090] R0[615/3000]  | LR: 0.022504 | E: -62.331757 | E_var:     5.5605 E_err:   0.036845 | NF_loss: 20.929737
[2025-11-13 02:22:57] 95:33<322:40, 8.12s/it | [Iter  706/3090] R0[616/3000]  | LR: 0.022496 | E: -62.312446 | E_var:     5.4973 E_err:   0.036635 | NF_loss: 46.805048
[2025-11-13 02:23:04] 95:41<322:31, 8.12s/it | [Iter  707/3090] R0[617/3000]  | LR: 0.022488 | E: -62.323097 | E_var:     6.0220 E_err:   0.038343 | NF_loss: 55.664686
[2025-11-13 02:23:12] 95:49<322:22, 8.12s/it | [Iter  708/3090] R0[618/3000]  | LR: 0.022480 | E: -62.260875 | E_var:     5.4304 E_err:   0.036411 | NF_loss: 64.547954
[2025-11-13 02:23:20] 95:57<322:14, 8.12s/it | [Iter  709/3090] R0[619/3000]  | LR: 0.022472 | E: -62.319009 | E_var:     5.3688 E_err:   0.036204 | NF_loss: 10.124351
[2025-11-13 02:23:28] 96:05<322:05, 8.12s/it | [Iter  710/3090] R0[620/3000]  | LR: 0.022465 | E: -62.251281 | E_var:     5.6278 E_err:   0.037067 | NF_loss: 37.540873
[2025-11-13 02:23:36] 96:13<321:57, 8.12s/it | [Iter  711/3090] R0[621/3000]  | LR: 0.022457 | E: -62.164316 | E_var:     5.5689 E_err:   0.036873 | NF_loss: 39.444454
[2025-11-13 02:23:44] 96:21<321:48, 8.12s/it | [Iter  712/3090] R0[622/3000]  | LR: 0.022449 | E: -62.104874 | E_var:     5.6523 E_err:   0.037148 | NF_loss: 110.087004
[2025-11-13 02:23:52] 96:28<321:39, 8.12s/it | [Iter  713/3090] R0[623/3000]  | LR: 0.022441 | E: -62.238833 | E_var:     5.9594 E_err:   0.038143 | NF_loss: 43.570194
[2025-11-13 02:24:00] 96:36<321:30, 8.12s/it | [Iter  714/3090] R0[624/3000]  | LR: 0.022433 | E: -62.212346 | E_var:     5.6572 E_err:   0.037164 | NF_loss: 534.488051
[2025-11-13 02:24:08] 96:44<321:21, 8.12s/it | [Iter  715/3090] R0[625/3000]  | LR: 0.022425 | E: -62.336261 | E_var:     5.6863 E_err:   0.037259 | NF_loss: 39.367042
[2025-11-13 02:24:16] 96:52<321:12, 8.12s/it | [Iter  716/3090] R0[626/3000]  | LR: 0.022417 | E: -62.361407 | E_var:     5.5510 E_err:   0.036813 | NF_loss: 105.304591
[2025-11-13 02:24:23] 97:00<321:03, 8.12s/it | [Iter  717/3090] R0[627/3000]  | LR: 0.022409 | E: -62.308316 | E_var:     5.6135 E_err:   0.037020 | NF_loss: 570.661624
[2025-11-13 02:24:31] 97:08<320:54, 8.12s/it | [Iter  718/3090] R0[628/3000]  | LR: 0.022401 | E: -62.268438 | E_var:     5.5837 E_err:   0.036922 | NF_loss: 57.621018
[2025-11-13 02:24:39] 97:16<320:45, 8.12s/it | [Iter  719/3090] R0[629/3000]  | LR: 0.022393 | E: -62.351417 | E_var:     5.4676 E_err:   0.036536 | NF_loss: 54.905268
[2025-11-13 02:24:47] 97:24<320:36, 8.12s/it | [Iter  720/3090] R0[630/3000]  | LR: 0.022385 | E: -62.325755 | E_var:     5.7370 E_err:   0.037425 | NF_loss: 53.648691
[2025-11-13 02:24:55] 97:31<320:27, 8.12s/it | [Iter  721/3090] R0[631/3000]  | LR: 0.022377 | E: -62.248499 | E_var:     5.3178 E_err:   0.036032 | NF_loss: 35.948777
[2025-11-13 02:25:03] 97:39<320:18, 8.12s/it | [Iter  722/3090] R0[632/3000]  | LR: 0.022369 | E: -62.257662 | E_var:     5.3389 E_err:   0.036103 | NF_loss: 64.574332
[2025-11-13 02:25:11] 97:47<320:09, 8.12s/it | [Iter  723/3090] R0[633/3000]  | LR: 0.022361 | E: -62.330957 | E_var:     5.4965 E_err:   0.036632 | NF_loss: 45.554920
[2025-11-13 02:25:18] 97:55<320:00, 8.12s/it | [Iter  724/3090] R0[634/3000]  | LR: 0.022353 | E: -62.392232 | E_var:     5.6753 E_err:   0.037223 | NF_loss: 26.753870
[2025-11-13 02:25:26] 98:03<319:52, 8.12s/it | [Iter  725/3090] R0[635/3000]  | LR: 0.022345 | E: -62.308315 | E_var:     5.8059 E_err:   0.037649 | NF_loss: 23.987319
[2025-11-13 02:25:34] 98:11<319:43, 8.11s/it | [Iter  726/3090] R0[636/3000]  | LR: 0.022337 | E: -62.293840 | E_var:     5.8327 E_err:   0.037736 | NF_loss: 36.898447
[2025-11-13 02:25:42] 98:19<319:34, 8.11s/it | [Iter  727/3090] R0[637/3000]  | LR: 0.022329 | E: -62.327220 | E_var:     5.7199 E_err:   0.037369 | NF_loss: 45.689616
[2025-11-13 02:25:50] 98:26<319:25, 8.11s/it | [Iter  728/3090] R0[638/3000]  | LR: 0.022321 | E: -62.278586 | E_var:     5.2401 E_err:   0.035768 | NF_loss: 33.394453
[2025-11-13 02:25:58] 98:34<319:16, 8.11s/it | [Iter  729/3090] R0[639/3000]  | LR: 0.022312 | E: -62.165200 | E_var:     5.4453 E_err:   0.036461 | NF_loss: 40.392169
[2025-11-13 02:26:06] 98:42<319:07, 8.11s/it | [Iter  730/3090] R0[640/3000]  | LR: 0.022304 | E: -62.269285 | E_var:     5.5633 E_err:   0.036854 | NF_loss: 21.829979
[2025-11-13 02:26:14] 98:50<318:58, 8.11s/it | [Iter  731/3090] R0[641/3000]  | LR: 0.022296 | E: -62.069082 | E_var:     5.9264 E_err:   0.038038 | NF_loss: 30.169259
[2025-11-13 02:26:21] 98:58<318:49, 8.11s/it | [Iter  732/3090] R0[642/3000]  | LR: 0.022288 | E: -62.151971 | E_var:     5.7092 E_err:   0.037334 | NF_loss: 123.127989
[2025-11-13 02:26:29] 99:06<318:40, 8.11s/it | [Iter  733/3090] R0[643/3000]  | LR: 0.022280 | E: -62.185422 | E_var:     5.3844 E_err:   0.036257 | NF_loss: 41.966604
[2025-11-13 02:26:37] 99:14<318:31, 8.11s/it | [Iter  734/3090] R0[644/3000]  | LR: 0.022272 | E: -62.123171 | E_var:     6.3008 E_err:   0.039221 | NF_loss: 39.357363
[2025-11-13 02:26:45] 99:22<318:23, 8.11s/it | [Iter  735/3090] R0[645/3000]  | LR: 0.022264 | E: -62.151660 | E_var:     5.2736 E_err:   0.035882 | NF_loss: 51.197156
[2025-11-13 02:26:53] 99:29<318:14, 8.11s/it | [Iter  736/3090] R0[646/3000]  | LR: 0.022255 | E: -62.223186 | E_var:     5.3113 E_err:   0.036010 | NF_loss: 113.788153
[2025-11-13 02:27:01] 99:37<318:05, 8.11s/it | [Iter  737/3090] R0[647/3000]  | LR: 0.022247 | E: -62.353316 | E_var:     5.4006 E_err:   0.036311 | NF_loss: 41.912613
[2025-11-13 02:27:09] 99:45<317:56, 8.11s/it | [Iter  738/3090] R0[648/3000]  | LR: 0.022239 | E: -62.178956 | E_var:     5.0810 E_err:   0.035220 | NF_loss: 30.799690
[2025-11-13 02:27:17] 99:53<317:47, 8.11s/it | [Iter  739/3090] R0[649/3000]  | LR: 0.022231 | E: -62.232184 | E_var:     5.5020 E_err:   0.036651 | NF_loss: 42.593951
[2025-11-13 02:27:24] 100:01<317:38, 8.11s/it | [Iter  740/3090] R0[650/3000]  | LR: 0.022223 | E: -62.145872 | E_var:     5.5714 E_err:   0.036881 | NF_loss: 35.237892
[2025-11-13 02:27:32] 100:09<317:29, 8.11s/it | [Iter  741/3090] R0[651/3000]  | LR: 0.022214 | E: -62.118815 | E_var:     5.6219 E_err:   0.037048 | NF_loss: 41.690811
[2025-11-13 02:27:40] 100:17<317:20, 8.11s/it | [Iter  742/3090] R0[652/3000]  | LR: 0.022206 | E: -62.147095 | E_var:     5.4989 E_err:   0.036640 | NF_loss: 23.935109
[2025-11-13 02:27:48] 100:25<317:12, 8.11s/it | [Iter  743/3090] R0[653/3000]  | LR: 0.022198 | E: -62.140859 | E_var:     5.4463 E_err:   0.036465 | NF_loss: 21.831845
[2025-11-13 02:27:56] 100:32<317:03, 8.11s/it | [Iter  744/3090] R0[654/3000]  | LR: 0.022190 | E: -62.231434 | E_var:     5.2792 E_err:   0.035901 | NF_loss: 26.829634
[2025-11-13 02:28:04] 100:40<316:54, 8.11s/it | [Iter  745/3090] R0[655/3000]  | LR: 0.022181 | E: -62.245963 | E_var:     5.3770 E_err:   0.036232 | NF_loss: 23.063204
[2025-11-13 02:28:12] 100:48<316:45, 8.11s/it | [Iter  746/3090] R0[656/3000]  | LR: 0.022173 | E: -62.308365 | E_var:     5.1269 E_err:   0.035379 | NF_loss: 35.706685
[2025-11-13 02:28:20] 100:56<316:36, 8.11s/it | [Iter  747/3090] R0[657/3000]  | LR: 0.022165 | E: -62.289448 | E_var:     5.4932 E_err:   0.036621 | NF_loss: 31.995225
[2025-11-13 02:28:28] 101:04<316:28, 8.11s/it | [Iter  748/3090] R0[658/3000]  | LR: 0.022156 | E: -62.328215 | E_var:     5.6725 E_err:   0.037214 | NF_loss: 431.866240
[2025-11-13 02:28:36] 101:12<316:19, 8.11s/it | [Iter  749/3090] R0[659/3000]  | LR: 0.022148 | E: -62.319780 | E_var:     5.2138 E_err:   0.035678 | NF_loss: 25.882439
[2025-11-13 02:28:43] 101:20<316:11, 8.11s/it | [Iter  750/3090] R0[660/3000]  | LR: 0.022140 | E: -62.333650 | E_var:     5.0051 E_err:   0.034956 | NF_loss: 50.030568
[2025-11-13 02:28:51] 101:28<316:02, 8.11s/it | [Iter  751/3090] R0[661/3000]  | LR: 0.022131 | E: -62.366302 | E_var:     5.3115 E_err:   0.036011 | NF_loss: 34.283228
[2025-11-13 02:28:59] 101:36<315:53, 8.11s/it | [Iter  752/3090] R0[662/3000]  | LR: 0.022123 | E: -62.241225 | E_var:     5.4350 E_err:   0.036427 | NF_loss: 34.597867
[2025-11-13 02:29:07] 101:44<315:44, 8.11s/it | [Iter  753/3090] R0[663/3000]  | LR: 0.022115 | E: -62.208146 | E_var:     5.5635 E_err:   0.036855 | NF_loss: 32.718720
[2025-11-13 02:29:15] 101:51<315:35, 8.11s/it | [Iter  754/3090] R0[664/3000]  | LR: 0.022106 | E: -62.231783 | E_var:     5.5077 E_err:   0.036670 | NF_loss: 25.646649
[2025-11-13 02:29:23] 101:59<315:26, 8.11s/it | [Iter  755/3090] R0[665/3000]  | LR: 0.022098 | E: -62.319150 | E_var:     5.3858 E_err:   0.036261 | NF_loss: 34.881114
[2025-11-13 02:29:31] 102:07<315:18, 8.11s/it | [Iter  756/3090] R0[666/3000]  | LR: 0.022090 | E: -62.266388 | E_var:     5.3594 E_err:   0.036173 | NF_loss: 27.985943
[2025-11-13 02:29:39] 102:15<315:09, 8.11s/it | [Iter  757/3090] R0[667/3000]  | LR: 0.022081 | E: -62.101631 | E_var:     5.5363 E_err:   0.036765 | NF_loss: 31.915657
[2025-11-13 02:29:46] 102:23<315:00, 8.10s/it | [Iter  758/3090] R0[668/3000]  | LR: 0.022073 | E: -62.069402 | E_var:     5.5299 E_err:   0.036743 | NF_loss: 33.089088
[2025-11-13 02:29:54] 102:31<314:51, 8.10s/it | [Iter  759/3090] R0[669/3000]  | LR: 0.022064 | E: -62.132287 | E_var:     5.5198 E_err:   0.036710 | NF_loss: 36.105991
[2025-11-13 02:30:02] 102:39<314:42, 8.10s/it | [Iter  760/3090] R0[670/3000]  | LR: 0.022056 | E: -62.251943 | E_var:     5.3177 E_err:   0.036031 | NF_loss: 26.930737
[2025-11-13 02:30:10] 102:47<314:34, 8.10s/it | [Iter  761/3090] R0[671/3000]  | LR: 0.022047 | E: -62.330335 | E_var:     5.5123 E_err:   0.036685 | NF_loss: 27.836487
[2025-11-13 02:30:18] 102:54<314:25, 8.10s/it | [Iter  762/3090] R0[672/3000]  | LR: 0.022039 | E: -62.321813 | E_var:     5.1207 E_err:   0.035358 | NF_loss: 26.802917
[2025-11-13 02:30:26] 103:02<314:16, 8.10s/it | [Iter  763/3090] R0[673/3000]  | LR: 0.022031 | E: -62.261261 | E_var:     5.4074 E_err:   0.036334 | NF_loss: 25.358171
[2025-11-13 02:30:34] 103:10<314:07, 8.10s/it | [Iter  764/3090] R0[674/3000]  | LR: 0.022022 | E: -62.210240 | E_var:     5.3984 E_err:   0.036304 | NF_loss: 37.334611
[2025-11-13 02:30:42] 103:18<313:58, 8.10s/it | [Iter  765/3090] R0[675/3000]  | LR: 0.022014 | E: -62.118699 | E_var:     5.3602 E_err:   0.036175 | NF_loss: 23.325678
[2025-11-13 02:30:49] 103:26<313:50, 8.10s/it | [Iter  766/3090] R0[676/3000]  | LR: 0.022005 | E: -62.182829 | E_var:     5.5700 E_err:   0.036876 | NF_loss: 32.907193
[2025-11-13 02:30:57] 103:34<313:41, 8.10s/it | [Iter  767/3090] R0[677/3000]  | LR: 0.021997 | E: -62.031289 | E_var:     5.5538 E_err:   0.036823 | NF_loss: 24.149206
[2025-11-13 02:31:05] 103:42<313:32, 8.10s/it | [Iter  768/3090] R0[678/3000]  | LR: 0.021988 | E: -62.235092 | E_var:     5.1363 E_err:   0.035412 | NF_loss: 25.130559
[2025-11-13 02:31:13] 103:50<313:23, 8.10s/it | [Iter  769/3090] R0[679/3000]  | LR: 0.021980 | E: -62.279664 | E_var:     5.2962 E_err:   0.035959 | NF_loss: 18.666752
[2025-11-13 02:31:21] 103:57<313:14, 8.10s/it | [Iter  770/3090] R0[680/3000]  | LR: 0.021971 | E: -62.235266 | E_var:     5.5162 E_err:   0.036698 | NF_loss: 22.854680
[2025-11-13 02:31:29] 104:05<313:06, 8.10s/it | [Iter  771/3090] R0[681/3000]  | LR: 0.021962 | E: -62.172893 | E_var:     5.2120 E_err:   0.035672 | NF_loss: 25.672364
[2025-11-13 02:31:37] 104:13<312:57, 8.10s/it | [Iter  772/3090] R0[682/3000]  | LR: 0.021954 | E: -62.204507 | E_var:     5.5077 E_err:   0.036670 | NF_loss: 26.448043
[2025-11-13 02:31:45] 104:21<312:48, 8.10s/it | [Iter  773/3090] R0[683/3000]  | LR: 0.021945 | E: -62.352211 | E_var:     5.5091 E_err:   0.036674 | NF_loss: 18.411707
[2025-11-13 02:31:52] 104:29<312:39, 8.10s/it | [Iter  774/3090] R0[684/3000]  | LR: 0.021937 | E: -62.368249 | E_var:     5.4357 E_err:   0.036429 | NF_loss: 27.960268
[2025-11-13 02:32:00] 104:37<312:31, 8.10s/it | [Iter  775/3090] R0[685/3000]  | LR: 0.021928 | E: -62.377635 | E_var:     5.5366 E_err:   0.036766 | NF_loss: 17.716320
[2025-11-13 02:32:08] 104:45<312:22, 8.10s/it | [Iter  776/3090] R0[686/3000]  | LR: 0.021920 | E: -62.368168 | E_var:     5.8664 E_err:   0.037845 | NF_loss: 19.274680
[2025-11-13 02:32:16] 104:53<312:13, 8.10s/it | [Iter  777/3090] R0[687/3000]  | LR: 0.021911 | E: -62.378796 | E_var:     5.1075 E_err:   0.035312 | NF_loss: 16.679696
[2025-11-13 02:32:24] 105:00<312:04, 8.10s/it | [Iter  778/3090] R0[688/3000]  | LR: 0.021902 | E: -62.427094 | E_var:     4.9209 E_err:   0.034661 | NF_loss: 20.724628
[2025-11-13 02:32:32] 105:08<311:55, 8.10s/it | [Iter  779/3090] R0[689/3000]  | LR: 0.021894 | E: -62.342328 | E_var:     4.9946 E_err:   0.034920 | NF_loss: 19.975093
[2025-11-13 02:32:40] 105:16<311:47, 8.10s/it | [Iter  780/3090] R0[690/3000]  | LR: 0.021885 | E: -62.372306 | E_var:     5.2654 E_err:   0.035854 | NF_loss: 19.764776
[2025-11-13 02:32:48] 105:24<311:38, 8.10s/it | [Iter  781/3090] R0[691/3000]  | LR: 0.021876 | E: -62.389270 | E_var:     6.0654 E_err:   0.038481 | NF_loss: 22.093724
[2025-11-13 02:32:55] 105:32<311:29, 8.10s/it | [Iter  782/3090] R0[692/3000]  | LR: 0.021868 | E: -62.290799 | E_var:     5.5381 E_err:   0.036770 | NF_loss: 22.978461
[2025-11-13 02:33:03] 105:40<311:20, 8.10s/it | [Iter  783/3090] R0[693/3000]  | LR: 0.021859 | E: -62.382898 | E_var:     5.2347 E_err:   0.035749 | NF_loss: 20.327078
[2025-11-13 02:33:11] 105:48<311:12, 8.10s/it | [Iter  784/3090] R0[694/3000]  | LR: 0.021850 | E: -62.382309 | E_var:     5.0920 E_err:   0.035258 | NF_loss: 19.624227
[2025-11-13 02:33:19] 105:56<311:04, 8.10s/it | [Iter  785/3090] R0[695/3000]  | LR: 0.021842 | E: -62.387751 | E_var:     5.0706 E_err:   0.035184 | NF_loss: 25.586237
[2025-11-13 02:33:27] 106:04<310:55, 8.10s/it | [Iter  786/3090] R0[696/3000]  | LR: 0.021833 | E: -62.399295 | E_var:     5.5322 E_err:   0.036751 | NF_loss: 18.942720
[2025-11-13 02:33:35] 106:12<310:46, 8.10s/it | [Iter  787/3090] R0[697/3000]  | LR: 0.021824 | E: -62.442767 | E_var:     5.4869 E_err:   0.036600 | NF_loss: 20.316870
[2025-11-13 02:33:43] 106:20<310:38, 8.10s/it | [Iter  788/3090] R0[698/3000]  | LR: 0.021816 | E: -62.448732 | E_var:     5.3550 E_err:   0.036158 | NF_loss: 17.351442
[2025-11-13 02:33:51] 106:27<310:29, 8.10s/it | [Iter  789/3090] R0[699/3000]  | LR: 0.021807 | E: -62.390368 | E_var:     5.5322 E_err:   0.036751 | NF_loss: 19.836120
[2025-11-13 02:33:59] 106:35<310:20, 8.10s/it | [Iter  790/3090] R0[700/3000]  | LR: 0.021798 | E: -62.419672 | E_var:     5.1446 E_err:   0.035440 | NF_loss: 13.164109
[2025-11-13 02:34:07] 106:43<310:11, 8.10s/it | [Iter  791/3090] R0[701/3000]  | LR: 0.021789 | E: -62.461009 | E_var:     5.6757 E_err:   0.037225 | NF_loss: 27.874751
[2025-11-13 02:34:14] 106:51<310:03, 8.10s/it | [Iter  792/3090] R0[702/3000]  | LR: 0.021781 | E: -62.407214 | E_var:     5.1397 E_err:   0.035423 | NF_loss: 24.067684
[2025-11-13 02:34:22] 106:59<309:54, 8.10s/it | [Iter  793/3090] R0[703/3000]  | LR: 0.021772 | E: -62.370406 | E_var:     4.9545 E_err:   0.034779 | NF_loss: 18.523560
[2025-11-13 02:34:30] 107:07<309:45, 8.09s/it | [Iter  794/3090] R0[704/3000]  | LR: 0.021763 | E: -62.488309 | E_var:     5.1510 E_err:   0.035462 | NF_loss: 14.186377
[2025-11-13 02:34:38] 107:15<309:36, 8.09s/it | [Iter  795/3090] R0[705/3000]  | LR: 0.021754 | E: -62.439056 | E_var:     4.8983 E_err:   0.034582 | NF_loss: 23.796493
[2025-11-13 02:34:46] 107:23<309:28, 8.09s/it | [Iter  796/3090] R0[706/3000]  | LR: 0.021745 | E: -62.442970 | E_var:     5.2194 E_err:   0.035697 | NF_loss: 25.548696
[2025-11-13 02:34:54] 107:30<309:19, 8.09s/it | [Iter  797/3090] R0[707/3000]  | LR: 0.021737 | E: -62.449050 | E_var:     5.2811 E_err:   0.035907 | NF_loss: 15.971459
[2025-11-13 02:35:02] 107:38<309:10, 8.09s/it | [Iter  798/3090] R0[708/3000]  | LR: 0.021728 | E: -62.395190 | E_var:     5.3430 E_err:   0.036117 | NF_loss: 15.937707
[2025-11-13 02:35:10] 107:46<309:02, 8.09s/it | [Iter  799/3090] R0[709/3000]  | LR: 0.021719 | E: -62.434084 | E_var:     5.5743 E_err:   0.036890 | NF_loss: 32.319556
[2025-11-13 02:35:17] 107:54<308:53, 8.09s/it | [Iter  800/3090] R0[710/3000]  | LR: 0.021710 | E: -62.467168 | E_var:     5.3706 E_err:   0.036210 | NF_loss: 22.819088
[2025-11-13 02:35:25] 108:02<308:44, 8.09s/it | [Iter  801/3090] R0[711/3000]  | LR: 0.021701 | E: -62.323365 | E_var:     5.2523 E_err:   0.035809 | NF_loss: 25.628318
[2025-11-13 02:35:33] 108:10<308:35, 8.09s/it | [Iter  802/3090] R0[712/3000]  | LR: 0.021692 | E: -62.426045 | E_var:     5.2261 E_err:   0.035720 | NF_loss: 22.872997
[2025-11-13 02:35:41] 108:18<308:27, 8.09s/it | [Iter  803/3090] R0[713/3000]  | LR: 0.021683 | E: -62.308461 | E_var:     5.1100 E_err:   0.035321 | NF_loss: 19.283899
[2025-11-13 02:35:49] 108:25<308:18, 8.09s/it | [Iter  804/3090] R0[714/3000]  | LR: 0.021675 | E: -62.328099 | E_var:     5.3009 E_err:   0.035975 | NF_loss: 25.875724
[2025-11-13 02:35:57] 108:33<308:09, 8.09s/it | [Iter  805/3090] R0[715/3000]  | LR: 0.021666 | E: -62.452188 | E_var:     5.2708 E_err:   0.035872 | NF_loss: 29.950731
[2025-11-13 02:36:05] 108:41<308:00, 8.09s/it | [Iter  806/3090] R0[716/3000]  | LR: 0.021657 | E: -62.420882 | E_var:     5.3748 E_err:   0.036225 | NF_loss: 26.516568
[2025-11-13 02:36:13] 108:49<307:52, 8.09s/it | [Iter  807/3090] R0[717/3000]  | LR: 0.021648 | E: -62.512686 | E_var:     5.0555 E_err:   0.035132 | NF_loss: 15.974637
[2025-11-13 02:36:20] 108:57<307:43, 8.09s/it | [Iter  808/3090] R0[718/3000]  | LR: 0.021639 | E: -62.471921 | E_var:     5.2864 E_err:   0.035925 | NF_loss: 24.513180
[2025-11-13 02:36:28] 109:05<307:34, 8.09s/it | [Iter  809/3090] R0[719/3000]  | LR: 0.021630 | E: -62.519000 | E_var:     5.1634 E_err:   0.035505 | NF_loss: 17.838034
[2025-11-13 02:36:36] 109:13<307:26, 8.09s/it | [Iter  810/3090] R0[720/3000]  | LR: 0.021621 | E: -62.472356 | E_var:     5.5440 E_err:   0.036790 | NF_loss: 22.840806
[2025-11-13 02:36:44] 109:21<307:17, 8.09s/it | [Iter  811/3090] R0[721/3000]  | LR: 0.021612 | E: -62.524738 | E_var:     5.9100 E_err:   0.037985 | NF_loss: 22.948609
[2025-11-13 02:36:52] 109:28<307:08, 8.09s/it | [Iter  812/3090] R0[722/3000]  | LR: 0.021603 | E: -62.546549 | E_var:     5.4765 E_err:   0.036566 | NF_loss: 21.161046
[2025-11-13 02:37:00] 109:36<307:00, 8.09s/it | [Iter  813/3090] R0[723/3000]  | LR: 0.021594 | E: -62.565698 | E_var:     6.1190 E_err:   0.038651 | NF_loss: 21.830840
[2025-11-13 02:37:08] 109:44<306:51, 8.09s/it | [Iter  814/3090] R0[724/3000]  | LR: 0.021585 | E: -62.488187 | E_var:     5.9518 E_err:   0.038119 | NF_loss: 13.513722
[2025-11-13 02:37:16] 109:52<306:42, 8.09s/it | [Iter  815/3090] R0[725/3000]  | LR: 0.021576 | E: -62.480764 | E_var:     4.9593 E_err:   0.034796 | NF_loss: 13.161619
[2025-11-13 02:37:23] 110:00<306:33, 8.09s/it | [Iter  816/3090] R0[726/3000]  | LR: 0.021567 | E: -62.400144 | E_var:     5.0618 E_err:   0.035154 | NF_loss: 20.164178
[2025-11-13 02:37:32] 110:08<306:25, 8.09s/it | [Iter  817/3090] R0[727/3000]  | LR: 0.021558 | E: -62.428890 | E_var:     5.0011 E_err:   0.034943 | NF_loss: 19.937386
[2025-11-13 02:37:39] 110:16<306:17, 8.09s/it | [Iter  818/3090] R0[728/3000]  | LR: 0.021549 | E: -62.481078 | E_var:     5.1252 E_err:   0.035373 | NF_loss: 24.051098
[2025-11-13 02:37:47] 110:24<306:08, 8.09s/it | [Iter  819/3090] R0[729/3000]  | LR: 0.021540 | E: -62.491190 | E_var:     5.0339 E_err:   0.035057 | NF_loss: 19.459633
[2025-11-13 02:37:55] 110:32<305:59, 8.09s/it | [Iter  820/3090] R0[730/3000]  | LR: 0.021531 | E: -62.407314 | E_var:     5.3350 E_err:   0.036090 | NF_loss: 25.084792
[2025-11-13 02:38:03] 110:40<305:51, 8.09s/it | [Iter  821/3090] R0[731/3000]  | LR: 0.021522 | E: -62.448425 | E_var:     5.2739 E_err:   0.035883 | NF_loss: 21.175192
[2025-11-13 02:38:11] 110:47<305:42, 8.09s/it | [Iter  822/3090] R0[732/3000]  | LR: 0.021513 | E: -62.332590 | E_var:     5.4459 E_err:   0.036463 | NF_loss: 33.303200
[2025-11-13 02:38:19] 110:55<305:33, 8.09s/it | [Iter  823/3090] R0[733/3000]  | LR: 0.021504 | E: -62.247501 | E_var:     5.7676 E_err:   0.037525 | NF_loss: 15.261132
[2025-11-13 02:38:27] 111:03<305:25, 8.09s/it | [Iter  824/3090] R0[734/3000]  | LR: 0.021495 | E: -62.397352 | E_var:     5.2380 E_err:   0.035761 | NF_loss: 21.877022
[2025-11-13 02:38:35] 111:11<305:16, 8.09s/it | [Iter  825/3090] R0[735/3000]  | LR: 0.021486 | E: -62.484148 | E_var:     5.2504 E_err:   0.035803 | NF_loss: 38.526795
[2025-11-13 02:38:42] 111:19<305:07, 8.09s/it | [Iter  826/3090] R0[736/3000]  | LR: 0.021477 | E: -62.460241 | E_var:     5.2876 E_err:   0.035929 | NF_loss: 19.878218
[2025-11-13 02:38:50] 111:27<304:59, 8.09s/it | [Iter  827/3090] R0[737/3000]  | LR: 0.021467 | E: -62.396921 | E_var:     5.0643 E_err:   0.035162 | NF_loss: 19.883062
[2025-11-13 02:38:58] 111:35<304:50, 8.09s/it | [Iter  828/3090] R0[738/3000]  | LR: 0.021458 | E: -62.427305 | E_var:     5.2056 E_err:   0.035650 | NF_loss: 32.533937
[2025-11-13 02:39:06] 111:43<304:41, 8.09s/it | [Iter  829/3090] R0[739/3000]  | LR: 0.021449 | E: -62.388746 | E_var:     5.0134 E_err:   0.034985 | NF_loss: 12.541596
[2025-11-13 02:39:14] 111:50<304:33, 8.09s/it | [Iter  830/3090] R0[740/3000]  | LR: 0.021440 | E: -62.365712 | E_var:     5.1752 E_err:   0.035545 | NF_loss: 16.732217
[2025-11-13 02:39:22] 111:58<304:24, 8.09s/it | [Iter  831/3090] R0[741/3000]  | LR: 0.021431 | E: -62.466317 | E_var:     5.0873 E_err:   0.035242 | NF_loss: 19.087287
[2025-11-13 02:39:30] 112:06<304:16, 8.09s/it | [Iter  832/3090] R0[742/3000]  | LR: 0.021422 | E: -62.476178 | E_var:     5.2297 E_err:   0.035732 | NF_loss: 26.135783
[2025-11-13 02:39:38] 112:14<304:07, 8.08s/it | [Iter  833/3090] R0[743/3000]  | LR: 0.021413 | E: -62.459282 | E_var:     5.1372 E_err:   0.035415 | NF_loss: 17.276466
[2025-11-13 02:39:45] 112:22<303:58, 8.08s/it | [Iter  834/3090] R0[744/3000]  | LR: 0.021403 | E: -62.442494 | E_var:     5.1406 E_err:   0.035427 | NF_loss: 22.103332
[2025-11-13 02:39:53] 112:30<303:50, 8.08s/it | [Iter  835/3090] R0[745/3000]  | LR: 0.021394 | E: -62.464020 | E_var:     4.9971 E_err:   0.034928 | NF_loss: 36.867587
[2025-11-13 02:40:01] 112:38<303:41, 8.08s/it | [Iter  836/3090] R0[746/3000]  | LR: 0.021385 | E: -62.459175 | E_var:     5.1352 E_err:   0.035408 | NF_loss: 21.105333
[2025-11-13 02:40:09] 112:46<303:32, 8.08s/it | [Iter  837/3090] R0[747/3000]  | LR: 0.021376 | E: -62.591703 | E_var:     5.5611 E_err:   0.036847 | NF_loss: 21.204120
[2025-11-13 02:40:17] 112:53<303:24, 8.08s/it | [Iter  838/3090] R0[748/3000]  | LR: 0.021367 | E: -62.463204 | E_var:     5.3268 E_err:   0.036062 | NF_loss: 18.971514
[2025-11-13 02:40:25] 113:01<303:15, 8.08s/it | [Iter  839/3090] R0[749/3000]  | LR: 0.021357 | E: -62.541909 | E_var:     5.2807 E_err:   0.035906 | NF_loss: 23.050881
[2025-11-13 02:40:33] 113:09<303:06, 8.08s/it | [Iter  840/3090] R0[750/3000]  | LR: 0.021348 | E: -62.575788 | E_var:     5.5148 E_err:   0.036693 | NF_loss: 67.892423
[2025-11-13 02:40:41] 113:17<302:58, 8.08s/it | [Iter  841/3090] R0[751/3000]  | LR: 0.021339 | E: -62.577849 | E_var:     5.1518 E_err:   0.035465 | NF_loss: 27.495666
[2025-11-13 02:40:48] 113:25<302:49, 8.08s/it | [Iter  842/3090] R0[752/3000]  | LR: 0.021330 | E: -62.546407 | E_var:     5.0234 E_err:   0.035020 | NF_loss: 23.400709
[2025-11-13 02:40:56] 113:33<302:40, 8.08s/it | [Iter  843/3090] R0[753/3000]  | LR: 0.021320 | E: -62.540755 | E_var:     5.5182 E_err:   0.036704 | NF_loss: 17.662188
[2025-11-13 02:41:04] 113:41<302:32, 8.08s/it | [Iter  844/3090] R0[754/3000]  | LR: 0.021311 | E: -62.493445 | E_var:     5.4870 E_err:   0.036601 | NF_loss: 43.423618
[2025-11-13 02:41:12] 113:49<302:23, 8.08s/it | [Iter  845/3090] R0[755/3000]  | LR: 0.021302 | E: -62.471364 | E_var:     5.6790 E_err:   0.037235 | NF_loss: 30.632966
[2025-11-13 02:41:20] 113:56<302:14, 8.08s/it | [Iter  846/3090] R0[756/3000]  | LR: 0.021292 | E: -62.487436 | E_var:     5.5339 E_err:   0.036756 | NF_loss: 18.064236
[2025-11-13 02:41:28] 114:04<302:06, 8.08s/it | [Iter  847/3090] R0[757/3000]  | LR: 0.021283 | E: -62.425929 | E_var:     5.6169 E_err:   0.037031 | NF_loss: 21.378095
[2025-11-13 02:41:36] 114:12<301:57, 8.08s/it | [Iter  848/3090] R0[758/3000]  | LR: 0.021274 | E: -62.449000 | E_var:     5.6105 E_err:   0.037010 | NF_loss: 23.181020
[2025-11-13 02:41:44] 114:20<301:49, 8.08s/it | [Iter  849/3090] R0[759/3000]  | LR: 0.021264 | E: -62.500946 | E_var:     5.6008 E_err:   0.036978 | NF_loss: 19.916532
[2025-11-13 02:41:51] 114:28<301:40, 8.08s/it | [Iter  850/3090] R0[760/3000]  | LR: 0.021255 | E: -62.506921 | E_var:     5.3788 E_err:   0.036238 | NF_loss: 22.169275
[2025-11-13 02:41:59] 114:36<301:31, 8.08s/it | [Iter  851/3090] R0[761/3000]  | LR: 0.021246 | E: -62.478140 | E_var:     5.2473 E_err:   0.035792 | NF_loss: 34.016862
[2025-11-13 02:42:07] 114:44<301:23, 8.08s/it | [Iter  852/3090] R0[762/3000]  | LR: 0.021236 | E: -62.508656 | E_var:     5.7779 E_err:   0.037558 | NF_loss: 24.236748
[2025-11-13 02:42:15] 114:52<301:14, 8.08s/it | [Iter  853/3090] R0[763/3000]  | LR: 0.021227 | E: -62.531003 | E_var:     4.9291 E_err:   0.034690 | NF_loss: 20.554606
[2025-11-13 02:42:23] 114:59<301:06, 8.08s/it | [Iter  854/3090] R0[764/3000]  | LR: 0.021218 | E: -62.530745 | E_var:     5.2060 E_err:   0.035651 | NF_loss: 15.383412
[2025-11-13 02:42:31] 115:07<300:57, 8.08s/it | [Iter  855/3090] R0[765/3000]  | LR: 0.021208 | E: -62.532147 | E_var:     4.8768 E_err:   0.034506 | NF_loss: 14.755576
[2025-11-13 02:42:39] 115:15<300:48, 8.08s/it | [Iter  856/3090] R0[766/3000]  | LR: 0.021199 | E: -62.520555 | E_var:     4.9341 E_err:   0.034708 | NF_loss: 17.381709
[2025-11-13 02:42:47] 115:23<300:40, 8.08s/it | [Iter  857/3090] R0[767/3000]  | LR: 0.021190 | E: -62.532855 | E_var:     5.0281 E_err:   0.035037 | NF_loss: 15.123455
[2025-11-13 02:42:54] 115:31<300:31, 8.08s/it | [Iter  858/3090] R0[768/3000]  | LR: 0.021180 | E: -62.500703 | E_var:     4.8660 E_err:   0.034467 | NF_loss: 27.769089
[2025-11-13 02:43:02] 115:39<300:22, 8.08s/it | [Iter  859/3090] R0[769/3000]  | LR: 0.021171 | E: -62.526026 | E_var:     4.7727 E_err:   0.034135 | NF_loss: 25.818728
[2025-11-13 02:43:10] 115:47<300:14, 8.08s/it | [Iter  860/3090] R0[770/3000]  | LR: 0.021161 | E: -62.554473 | E_var:     5.0405 E_err:   0.035080 | NF_loss: 21.463678
[2025-11-13 02:43:18] 115:55<300:05, 8.08s/it | [Iter  861/3090] R0[771/3000]  | LR: 0.021152 | E: -62.585364 | E_var:     5.2570 E_err:   0.035825 | NF_loss: 27.030789
[2025-11-13 02:43:26] 116:03<299:57, 8.08s/it | [Iter  862/3090] R0[772/3000]  | LR: 0.021142 | E: -62.602020 | E_var:     5.1955 E_err:   0.035615 | NF_loss: 21.025045
[2025-11-13 02:43:34] 116:11<299:49, 8.08s/it | [Iter  863/3090] R0[773/3000]  | LR: 0.021133 | E: -62.607914 | E_var:     5.9199 E_err:   0.038017 | NF_loss: 14.820372
[2025-11-13 02:43:42] 116:19<299:40, 8.08s/it | [Iter  864/3090] R0[774/3000]  | LR: 0.021123 | E: -62.580763 | E_var:     5.3348 E_err:   0.036089 | NF_loss: 31.011782
[2025-11-13 02:43:50] 116:26<299:32, 8.08s/it | [Iter  865/3090] R0[775/3000]  | LR: 0.021114 | E: -62.600404 | E_var:     4.9165 E_err:   0.034645 | NF_loss: 23.768775
[2025-11-13 02:43:58] 116:34<299:23, 8.08s/it | [Iter  866/3090] R0[776/3000]  | LR: 0.021104 | E: -62.585293 | E_var:     5.2250 E_err:   0.035716 | NF_loss: 28.770656
[2025-11-13 02:44:06] 116:42<299:14, 8.08s/it | [Iter  867/3090] R0[777/3000]  | LR: 0.021095 | E: -62.611015 | E_var:     5.2323 E_err:   0.035741 | NF_loss: 27.435410
[2025-11-13 02:44:13] 116:50<299:06, 8.08s/it | [Iter  868/3090] R0[778/3000]  | LR: 0.021085 | E: -62.585946 | E_var:     4.8582 E_err:   0.034440 | NF_loss: 29.978488
[2025-11-13 02:44:21] 116:58<298:57, 8.08s/it | [Iter  869/3090] R0[779/3000]  | LR: 0.021076 | E: -62.595277 | E_var:     5.3366 E_err:   0.036096 | NF_loss: 20.353417
[2025-11-13 02:44:29] 117:06<298:49, 8.08s/it | [Iter  870/3090] R0[780/3000]  | LR: 0.021066 | E: -62.540294 | E_var:     5.3832 E_err:   0.036253 | NF_loss: 19.027057
[2025-11-13 02:44:37] 117:14<298:40, 8.08s/it | [Iter  871/3090] R0[781/3000]  | LR: 0.021057 | E: -62.540477 | E_var:     4.9023 E_err:   0.034595 | NF_loss: 28.724209
[2025-11-13 02:44:45] 117:21<298:31, 8.08s/it | [Iter  872/3090] R0[782/3000]  | LR: 0.021047 | E: -62.540452 | E_var:     5.3378 E_err:   0.036100 | NF_loss: 24.527086
[2025-11-13 02:44:53] 117:29<298:23, 8.08s/it | [Iter  873/3090] R0[783/3000]  | LR: 0.021038 | E: -62.543967 | E_var:     5.0146 E_err:   0.034990 | NF_loss: 17.249028
[2025-11-13 02:45:01] 117:37<298:14, 8.08s/it | [Iter  874/3090] R0[784/3000]  | LR: 0.021028 | E: -62.520430 | E_var:     4.9225 E_err:   0.034667 | NF_loss: 17.948856
[2025-11-13 02:45:09] 117:45<298:06, 8.08s/it | [Iter  875/3090] R0[785/3000]  | LR: 0.021019 | E: -62.540586 | E_var:     5.0318 E_err:   0.035049 | NF_loss: 16.130270
[2025-11-13 02:45:16] 117:53<297:57, 8.07s/it | [Iter  876/3090] R0[786/3000]  | LR: 0.021009 | E: -62.506877 | E_var:     4.9868 E_err:   0.034893 | NF_loss: 9.673477
[2025-11-13 02:45:24] 118:01<297:48, 8.07s/it | [Iter  877/3090] R0[787/3000]  | LR: 0.020999 | E: -62.549012 | E_var:     5.0785 E_err:   0.035212 | NF_loss: 21.265756
[2025-11-13 02:45:32] 118:09<297:40, 8.07s/it | [Iter  878/3090] R0[788/3000]  | LR: 0.020990 | E: -62.556642 | E_var:     5.0731 E_err:   0.035193 | NF_loss: 14.765430
[2025-11-13 02:45:40] 118:17<297:31, 8.07s/it | [Iter  879/3090] R0[789/3000]  | LR: 0.020980 | E: -62.567194 | E_var:     5.5111 E_err:   0.036681 | NF_loss: 12.778405
[2025-11-13 02:45:48] 118:24<297:23, 8.07s/it | [Iter  880/3090] R0[790/3000]  | LR: 0.020971 | E: -62.583265 | E_var:     5.3599 E_err:   0.036174 | NF_loss: 14.897278
[2025-11-13 02:45:56] 118:32<297:14, 8.07s/it | [Iter  881/3090] R0[791/3000]  | LR: 0.020961 | E: -62.575805 | E_var:     5.5317 E_err:   0.036749 | NF_loss: 24.266171
[2025-11-13 02:46:04] 118:40<297:06, 8.07s/it | [Iter  882/3090] R0[792/3000]  | LR: 0.020951 | E: -62.564090 | E_var:     5.2232 E_err:   0.035710 | NF_loss: 15.527120
[2025-11-13 02:46:12] 118:48<296:57, 8.07s/it | [Iter  883/3090] R0[793/3000]  | LR: 0.020942 | E: -62.607423 | E_var:     4.9722 E_err:   0.034841 | NF_loss: 18.368993
[2025-11-13 02:46:19] 118:56<296:48, 8.07s/it | [Iter  884/3090] R0[794/3000]  | LR: 0.020932 | E: -62.558691 | E_var:     4.7610 E_err:   0.034093 | NF_loss: 23.256537
[2025-11-13 02:46:27] 119:04<296:40, 8.07s/it | [Iter  885/3090] R0[795/3000]  | LR: 0.020922 | E: -62.523269 | E_var:     5.0311 E_err:   0.035047 | NF_loss: 37.027448
[2025-11-13 02:46:35] 119:12<296:31, 8.07s/it | [Iter  886/3090] R0[796/3000]  | LR: 0.020913 | E: -62.473041 | E_var:     5.3449 E_err:   0.036124 | NF_loss: 21.481816
[2025-11-13 02:46:43] 119:20<296:23, 8.07s/it | [Iter  887/3090] R0[797/3000]  | LR: 0.020903 | E: -62.532308 | E_var:     5.3367 E_err:   0.036096 | NF_loss: 22.976535
[2025-11-13 02:46:51] 119:27<296:14, 8.07s/it | [Iter  888/3090] R0[798/3000]  | LR: 0.020893 | E: -62.472757 | E_var:     5.1791 E_err:   0.035559 | NF_loss: 22.092308
[2025-11-13 02:46:59] 119:35<296:06, 8.07s/it | [Iter  889/3090] R0[799/3000]  | LR: 0.020884 | E: -62.539821 | E_var:     5.1714 E_err:   0.035532 | NF_loss: 18.369102
[2025-11-13 02:47:07] 119:43<295:57, 8.07s/it | [Iter  890/3090] R0[800/3000]  | LR: 0.020874 | E: -62.532842 | E_var:     4.7009 E_err:   0.033877 | NF_loss: 28.946770
[2025-11-13 02:47:15] 119:51<295:48, 8.07s/it | [Iter  891/3090] R0[801/3000]  | LR: 0.020864 | E: -62.558300 | E_var:     5.0270 E_err:   0.035033 | NF_loss: 16.621804
[2025-11-13 02:47:22] 119:59<295:40, 8.07s/it | [Iter  892/3090] R0[802/3000]  | LR: 0.020854 | E: -62.551624 | E_var:     5.0929 E_err:   0.035262 | NF_loss: 25.028931
[2025-11-13 02:47:30] 120:07<295:31, 8.07s/it | [Iter  893/3090] R0[803/3000]  | LR: 0.020845 | E: -62.469459 | E_var:     5.1116 E_err:   0.035326 | NF_loss: 26.486463
[2025-11-13 02:47:38] 120:15<295:23, 8.07s/it | [Iter  894/3090] R0[804/3000]  | LR: 0.020835 | E: -62.547114 | E_var:     4.9880 E_err:   0.034897 | NF_loss: 20.794800
[2025-11-13 02:47:46] 120:23<295:14, 8.07s/it | [Iter  895/3090] R0[805/3000]  | LR: 0.020825 | E: -62.481494 | E_var:     5.0675 E_err:   0.035174 | NF_loss: 16.918094
[2025-11-13 02:47:54] 120:30<295:06, 8.07s/it | [Iter  896/3090] R0[806/3000]  | LR: 0.020815 | E: -62.451041 | E_var:     5.0091 E_err:   0.034970 | NF_loss: 19.828396
[2025-11-13 02:48:02] 120:38<294:57, 8.07s/it | [Iter  897/3090] R0[807/3000]  | LR: 0.020806 | E: -62.565034 | E_var:     5.0425 E_err:   0.035087 | NF_loss: 18.902636
[2025-11-13 02:48:10] 120:46<294:49, 8.07s/it | [Iter  898/3090] R0[808/3000]  | LR: 0.020796 | E: -62.538135 | E_var:     5.4396 E_err:   0.036442 | NF_loss: 10.838266
[2025-11-13 02:48:18] 120:54<294:41, 8.07s/it | [Iter  899/3090] R0[809/3000]  | LR: 0.020786 | E: -62.635942 | E_var:     5.4431 E_err:   0.036454 | NF_loss: 11.300208
[2025-11-13 02:48:26] 121:02<294:32, 8.07s/it | [Iter  900/3090] R0[810/3000]  | LR: 0.020776 | E: -62.612269 | E_var:     5.7511 E_err:   0.037471 | NF_loss: 15.790952
[2025-11-13 02:48:26] 保存checkpoint: hybrid_checkpoint_iter_000900.pkl
[2025-11-13 02:48:34] 121:10<294:24, 8.07s/it | [Iter  901/3090] R0[811/3000]  | LR: 0.020766 | E: -62.599134 | E_var:     5.4829 E_err:   0.036587 | NF_loss: 16.062008
[2025-11-13 02:48:42] 121:18<294:15, 8.07s/it | [Iter  902/3090] R0[812/3000]  | LR: 0.020757 | E: -62.542783 | E_var:     5.1163 E_err:   0.035343 | NF_loss: 23.652391
[2025-11-13 02:48:49] 121:26<294:07, 8.07s/it | [Iter  903/3090] R0[813/3000]  | LR: 0.020747 | E: -62.566726 | E_var:     5.0332 E_err:   0.035054 | NF_loss: 12.606572
[2025-11-13 02:48:57] 121:34<293:58, 8.07s/it | [Iter  904/3090] R0[814/3000]  | LR: 0.020737 | E: -62.586773 | E_var:     5.3744 E_err:   0.036223 | NF_loss: 19.587093
[2025-11-13 02:49:05] 121:42<293:50, 8.07s/it | [Iter  905/3090] R0[815/3000]  | LR: 0.020727 | E: -62.585663 | E_var:     5.1069 E_err:   0.035310 | NF_loss: 19.843581
[2025-11-13 02:49:13] 121:50<293:41, 8.07s/it | [Iter  906/3090] R0[816/3000]  | LR: 0.020717 | E: -62.515482 | E_var:     5.1191 E_err:   0.035352 | NF_loss: 14.300624
[2025-11-13 02:49:21] 121:57<293:33, 8.07s/it | [Iter  907/3090] R0[817/3000]  | LR: 0.020707 | E: -62.595574 | E_var:     5.2596 E_err:   0.035834 | NF_loss: 18.412478
[2025-11-13 02:49:29] 122:05<293:24, 8.07s/it | [Iter  908/3090] R0[818/3000]  | LR: 0.020697 | E: -62.558956 | E_var:     4.8736 E_err:   0.034494 | NF_loss: 20.302497
[2025-11-13 02:49:37] 122:13<293:16, 8.07s/it | [Iter  909/3090] R0[819/3000]  | LR: 0.020688 | E: -62.534190 | E_var:     5.4775 E_err:   0.036569 | NF_loss: 30.675313
[2025-11-13 02:49:45] 122:21<293:07, 8.07s/it | [Iter  910/3090] R0[820/3000]  | LR: 0.020678 | E: -62.567654 | E_var:     5.1212 E_err:   0.035359 | NF_loss: 31.318701
[2025-11-13 02:49:52] 122:29<292:58, 8.07s/it | [Iter  911/3090] R0[821/3000]  | LR: 0.020668 | E: -62.607135 | E_var:     5.0184 E_err:   0.035003 | NF_loss: 19.772526
[2025-11-13 02:50:00] 122:37<292:50, 8.07s/it | [Iter  912/3090] R0[822/3000]  | LR: 0.020658 | E: -62.560726 | E_var:     5.3215 E_err:   0.036044 | NF_loss: 14.762712
[2025-11-13 02:50:08] 122:45<292:41, 8.07s/it | [Iter  913/3090] R0[823/3000]  | LR: 0.020648 | E: -62.614466 | E_var:     5.0873 E_err:   0.035242 | NF_loss: 24.222595
[2025-11-13 02:50:16] 122:53<292:33, 8.07s/it | [Iter  914/3090] R0[824/3000]  | LR: 0.020638 | E: -62.630235 | E_var:     4.8818 E_err:   0.034523 | NF_loss: 20.551501
[2025-11-13 02:50:24] 123:00<292:24, 8.07s/it | [Iter  915/3090] R0[825/3000]  | LR: 0.020628 | E: -62.681991 | E_var:     5.1659 E_err:   0.035513 | NF_loss: 24.593990
[2025-11-13 02:50:32] 123:08<292:16, 8.07s/it | [Iter  916/3090] R0[826/3000]  | LR: 0.020618 | E: -62.616162 | E_var:     5.1206 E_err:   0.035358 | NF_loss: 14.855141
[2025-11-13 02:50:40] 123:16<292:07, 8.07s/it | [Iter  917/3090] R0[827/3000]  | LR: 0.020608 | E: -62.605898 | E_var:     6.7564 E_err:   0.040614 | NF_loss: 19.260654
[2025-11-13 02:50:48] 123:24<291:59, 8.07s/it | [Iter  918/3090] R0[828/3000]  | LR: 0.020598 | E: -62.602742 | E_var:     5.2805 E_err:   0.035905 | NF_loss: 17.445320
[2025-11-13 02:50:55] 123:32<291:50, 8.07s/it | [Iter  919/3090] R0[829/3000]  | LR: 0.020588 | E: -62.622364 | E_var:     5.2315 E_err:   0.035738 | NF_loss: 22.100760
[2025-11-13 02:51:03] 123:40<291:42, 8.07s/it | [Iter  920/3090] R0[830/3000]  | LR: 0.020578 | E: -62.577652 | E_var:     5.0523 E_err:   0.035121 | NF_loss: 17.733633
[2025-11-13 02:51:11] 123:48<291:33, 8.07s/it | [Iter  921/3090] R0[831/3000]  | LR: 0.020568 | E: -62.597342 | E_var:     5.1605 E_err:   0.035495 | NF_loss: 14.407341
[2025-11-13 02:51:19] 123:56<291:25, 8.07s/it | [Iter  922/3090] R0[832/3000]  | LR: 0.020558 | E: -62.591278 | E_var:     4.8597 E_err:   0.034445 | NF_loss: 20.045379
[2025-11-13 02:51:27] 124:03<291:16, 8.06s/it | [Iter  923/3090] R0[833/3000]  | LR: 0.020548 | E: -62.592683 | E_var:     4.7417 E_err:   0.034024 | NF_loss: 16.329583
[2025-11-13 02:51:35] 124:11<291:08, 8.06s/it | [Iter  924/3090] R0[834/3000]  | LR: 0.020538 | E: -62.538867 | E_var:     5.0967 E_err:   0.035275 | NF_loss: 15.917607
[2025-11-13 02:51:43] 124:19<290:59, 8.06s/it | [Iter  925/3090] R0[835/3000]  | LR: 0.020528 | E: -62.614896 | E_var:     5.3677 E_err:   0.036200 | NF_loss: 22.006772
[2025-11-13 02:51:51] 124:27<290:51, 8.06s/it | [Iter  926/3090] R0[836/3000]  | LR: 0.020518 | E: -62.599952 | E_var:     5.0323 E_err:   0.035051 | NF_loss: 21.333151
[2025-11-13 02:51:58] 124:35<290:42, 8.06s/it | [Iter  927/3090] R0[837/3000]  | LR: 0.020508 | E: -62.641412 | E_var:     5.2882 E_err:   0.035931 | NF_loss: 15.303369
[2025-11-13 02:52:06] 124:43<290:34, 8.06s/it | [Iter  928/3090] R0[838/3000]  | LR: 0.020498 | E: -62.589413 | E_var:     5.3061 E_err:   0.035992 | NF_loss: 20.943744
[2025-11-13 02:52:14] 124:51<290:25, 8.06s/it | [Iter  929/3090] R0[839/3000]  | LR: 0.020488 | E: -62.500699 | E_var:     5.5703 E_err:   0.036877 | NF_loss: 11.582160
[2025-11-13 02:52:22] 124:59<290:17, 8.06s/it | [Iter  930/3090] R0[840/3000]  | LR: 0.020478 | E: -62.530161 | E_var:     5.6950 E_err:   0.037288 | NF_loss: 17.687517
[2025-11-13 02:52:30] 125:06<290:08, 8.06s/it | [Iter  931/3090] R0[841/3000]  | LR: 0.020468 | E: -62.634793 | E_var:     4.9901 E_err:   0.034904 | NF_loss: 19.936794
[2025-11-13 02:52:38] 125:14<290:00, 8.06s/it | [Iter  932/3090] R0[842/3000]  | LR: 0.020458 | E: -62.592224 | E_var:     5.1184 E_err:   0.035350 | NF_loss: 17.319109
[2025-11-13 02:52:46] 125:22<289:51, 8.06s/it | [Iter  933/3090] R0[843/3000]  | LR: 0.020448 | E: -62.631074 | E_var:     4.8674 E_err:   0.034472 | NF_loss: 25.696074
[2025-11-13 02:52:54] 125:30<289:43, 8.06s/it | [Iter  934/3090] R0[844/3000]  | LR: 0.020438 | E: -62.598346 | E_var:     4.8167 E_err:   0.034292 | NF_loss: 20.415872
[2025-11-13 02:53:02] 125:38<289:35, 8.06s/it | [Iter  935/3090] R0[845/3000]  | LR: 0.020427 | E: -62.580434 | E_var:     4.9330 E_err:   0.034704 | NF_loss: 20.574781
[2025-11-13 02:53:10] 125:46<289:26, 8.06s/it | [Iter  936/3090] R0[846/3000]  | LR: 0.020417 | E: -62.604239 | E_var:     5.1254 E_err:   0.035374 | NF_loss: 22.620785
[2025-11-13 02:53:17] 125:54<289:18, 8.06s/it | [Iter  937/3090] R0[847/3000]  | LR: 0.020407 | E: -62.565741 | E_var:     5.2511 E_err:   0.035805 | NF_loss: 23.281487
[2025-11-13 02:53:25] 126:02<289:09, 8.06s/it | [Iter  938/3090] R0[848/3000]  | LR: 0.020397 | E: -62.556909 | E_var:     4.8946 E_err:   0.034568 | NF_loss: 23.329659
[2025-11-13 02:53:33] 126:10<289:01, 8.06s/it | [Iter  939/3090] R0[849/3000]  | LR: 0.020387 | E: -62.569743 | E_var:     4.8423 E_err:   0.034383 | NF_loss: 22.687421
[2025-11-13 02:53:41] 126:18<288:52, 8.06s/it | [Iter  940/3090] R0[850/3000]  | LR: 0.020377 | E: -62.608425 | E_var:     4.7918 E_err:   0.034203 | NF_loss: 24.123378
[2025-11-13 02:53:49] 126:25<288:44, 8.06s/it | [Iter  941/3090] R0[851/3000]  | LR: 0.020367 | E: -62.576157 | E_var:     4.7943 E_err:   0.034212 | NF_loss: 21.240733
[2025-11-13 02:53:57] 126:33<288:35, 8.06s/it | [Iter  942/3090] R0[852/3000]  | LR: 0.020356 | E: -62.601614 | E_var:     4.9151 E_err:   0.034641 | NF_loss: 20.902225
[2025-11-13 02:54:05] 126:41<288:27, 8.06s/it | [Iter  943/3090] R0[853/3000]  | LR: 0.020346 | E: -62.641256 | E_var:     4.8817 E_err:   0.034523 | NF_loss: 23.245347
[2025-11-13 02:54:13] 126:49<288:18, 8.06s/it | [Iter  944/3090] R0[854/3000]  | LR: 0.020336 | E: -62.595418 | E_var:     5.0220 E_err:   0.035015 | NF_loss: 22.991353
[2025-11-13 02:54:20] 126:57<288:10, 8.06s/it | [Iter  945/3090] R0[855/3000]  | LR: 0.020326 | E: -62.639727 | E_var:     4.8551 E_err:   0.034429 | NF_loss: 22.313731
[2025-11-13 02:54:28] 127:05<288:01, 8.06s/it | [Iter  946/3090] R0[856/3000]  | LR: 0.020316 | E: -62.620654 | E_var:     5.0172 E_err:   0.034999 | NF_loss: 25.377886
[2025-11-13 02:54:36] 127:13<287:53, 8.06s/it | [Iter  947/3090] R0[857/3000]  | LR: 0.020305 | E: -62.590766 | E_var:     4.9719 E_err:   0.034840 | NF_loss: 20.787408
[2025-11-13 02:54:44] 127:21<287:44, 8.06s/it | [Iter  948/3090] R0[858/3000]  | LR: 0.020295 | E: -62.564072 | E_var:     4.9113 E_err:   0.034627 | NF_loss: 24.475035
[2025-11-13 02:54:52] 127:28<287:36, 8.06s/it | [Iter  949/3090] R0[859/3000]  | LR: 0.020285 | E: -62.584056 | E_var:     4.9546 E_err:   0.034780 | NF_loss: 23.584741
[2025-11-13 02:55:00] 127:36<287:27, 8.06s/it | [Iter  950/3090] R0[860/3000]  | LR: 0.020275 | E: -62.576471 | E_var:     4.9067 E_err:   0.034611 | NF_loss: 17.666004
[2025-11-13 02:55:08] 127:44<287:19, 8.06s/it | [Iter  951/3090] R0[861/3000]  | LR: 0.020264 | E: -62.579799 | E_var:     4.9438 E_err:   0.034742 | NF_loss: 23.397474
[2025-11-13 02:55:16] 127:52<287:11, 8.06s/it | [Iter  952/3090] R0[862/3000]  | LR: 0.020254 | E: -62.557116 | E_var:     5.2958 E_err:   0.035957 | NF_loss: 21.801263
[2025-11-13 02:55:23] 128:00<287:02, 8.06s/it | [Iter  953/3090] R0[863/3000]  | LR: 0.020244 | E: -62.534281 | E_var:     5.5130 E_err:   0.036687 | NF_loss: 25.073624
[2025-11-13 02:55:31] 128:08<286:54, 8.06s/it | [Iter  954/3090] R0[864/3000]  | LR: 0.020234 | E: -62.598690 | E_var:     5.4656 E_err:   0.036529 | NF_loss: 18.421325
[2025-11-13 02:55:39] 128:16<286:45, 8.06s/it | [Iter  955/3090] R0[865/3000]  | LR: 0.020223 | E: -62.572218 | E_var:     4.7560 E_err:   0.034075 | NF_loss: 23.056411
[2025-11-13 02:55:47] 128:24<286:37, 8.06s/it | [Iter  956/3090] R0[866/3000]  | LR: 0.020213 | E: -62.620493 | E_var:     4.8570 E_err:   0.034435 | NF_loss: 21.761080
[2025-11-13 02:55:55] 128:31<286:28, 8.06s/it | [Iter  957/3090] R0[867/3000]  | LR: 0.020203 | E: -62.578266 | E_var:     4.8412 E_err:   0.034379 | NF_loss: 22.223838
[2025-11-13 02:56:03] 128:39<286:20, 8.06s/it | [Iter  958/3090] R0[868/3000]  | LR: 0.020192 | E: -62.626330 | E_var:     4.8621 E_err:   0.034453 | NF_loss: 23.753273
[2025-11-13 02:56:11] 128:47<286:11, 8.06s/it | [Iter  959/3090] R0[869/3000]  | LR: 0.020182 | E: -62.689068 | E_var:     4.9286 E_err:   0.034688 | NF_loss: 23.045734
[2025-11-13 02:56:19] 128:55<286:03, 8.06s/it | [Iter  960/3090] R0[870/3000]  | LR: 0.020172 | E: -62.636534 | E_var:     5.2721 E_err:   0.035877 | NF_loss: 13.978511
[2025-11-13 02:56:26] 129:03<285:54, 8.06s/it | [Iter  961/3090] R0[871/3000]  | LR: 0.020161 | E: -62.594496 | E_var:     4.7540 E_err:   0.034068 | NF_loss: 20.642006
[2025-11-13 02:56:34] 129:11<285:46, 8.06s/it | [Iter  962/3090] R0[872/3000]  | LR: 0.020151 | E: -62.574191 | E_var:     5.2683 E_err:   0.035864 | NF_loss: 22.764649
[2025-11-13 02:56:42] 129:19<285:37, 8.06s/it | [Iter  963/3090] R0[873/3000]  | LR: 0.020141 | E: -62.582284 | E_var:     5.0454 E_err:   0.035097 | NF_loss: 20.327251
[2025-11-13 02:56:50] 129:27<285:29, 8.06s/it | [Iter  964/3090] R0[874/3000]  | LR: 0.020130 | E: -62.587416 | E_var:     4.8898 E_err:   0.034551 | NF_loss: 18.906403
[2025-11-13 02:56:58] 129:34<285:20, 8.06s/it | [Iter  965/3090] R0[875/3000]  | LR: 0.020120 | E: -62.623382 | E_var:     5.0402 E_err:   0.035079 | NF_loss: 24.101557
[2025-11-13 02:57:06] 129:42<285:12, 8.06s/it | [Iter  966/3090] R0[876/3000]  | LR: 0.020110 | E: -62.652952 | E_var:     4.9446 E_err:   0.034745 | NF_loss: 14.718872
[2025-11-13 02:57:14] 129:50<285:04, 8.06s/it | [Iter  967/3090] R0[877/3000]  | LR: 0.020099 | E: -62.566273 | E_var:     4.9050 E_err:   0.034605 | NF_loss: 25.819490
[2025-11-13 02:57:22] 129:58<284:56, 8.06s/it | [Iter  968/3090] R0[878/3000]  | LR: 0.020089 | E: -62.637586 | E_var:     5.1907 E_err:   0.035599 | NF_loss: 23.921666
[2025-11-13 02:57:30] 130:06<284:47, 8.06s/it | [Iter  969/3090] R0[879/3000]  | LR: 0.020078 | E: -62.623762 | E_var:     5.1008 E_err:   0.035289 | NF_loss: 22.157563
[2025-11-13 02:57:38] 130:14<284:39, 8.06s/it | [Iter  970/3090] R0[880/3000]  | LR: 0.020068 | E: -62.636695 | E_var:     4.9920 E_err:   0.034911 | NF_loss: 46.544207
[2025-11-13 02:57:45] 130:22<284:30, 8.06s/it | [Iter  971/3090] R0[881/3000]  | LR: 0.020058 | E: -62.618968 | E_var:     5.2586 E_err:   0.035831 | NF_loss: 20.325220
[2025-11-13 02:57:53] 130:30<284:22, 8.06s/it | [Iter  972/3090] R0[882/3000]  | LR: 0.020047 | E: -62.619532 | E_var:     4.7784 E_err:   0.034155 | NF_loss: 35.559782
[2025-11-13 02:58:01] 130:38<284:13, 8.06s/it | [Iter  973/3090] R0[883/3000]  | LR: 0.020037 | E: -62.670930 | E_var:     5.0269 E_err:   0.035033 | NF_loss: 33.545042
[2025-11-13 02:58:09] 130:46<284:05, 8.06s/it | [Iter  974/3090] R0[884/3000]  | LR: 0.020026 | E: -62.684850 | E_var:     4.9610 E_err:   0.034802 | NF_loss: 35.707746
[2025-11-13 02:58:17] 130:53<283:57, 8.06s/it | [Iter  975/3090] R0[885/3000]  | LR: 0.020016 | E: -62.607972 | E_var:     5.1967 E_err:   0.035619 | NF_loss: 23.963803
[2025-11-13 02:58:25] 131:01<283:48, 8.06s/it | [Iter  976/3090] R0[886/3000]  | LR: 0.020005 | E: -62.664929 | E_var:     5.1200 E_err:   0.035355 | NF_loss: 27.735185
[2025-11-13 02:58:33] 131:09<283:40, 8.05s/it | [Iter  977/3090] R0[887/3000]  | LR: 0.019995 | E: -62.610020 | E_var:     5.1093 E_err:   0.035319 | NF_loss: 33.829557
[2025-11-13 02:58:41] 131:17<283:31, 8.05s/it | [Iter  978/3090] R0[888/3000]  | LR: 0.019984 | E: -62.605780 | E_var:     5.2515 E_err:   0.035806 | NF_loss: 30.393731
[2025-11-13 02:58:48] 131:25<283:23, 8.05s/it | [Iter  979/3090] R0[889/3000]  | LR: 0.019974 | E: -62.589110 | E_var:     5.1564 E_err:   0.035481 | NF_loss: 37.053213
[2025-11-13 02:58:56] 131:33<283:14, 8.05s/it | [Iter  980/3090] R0[890/3000]  | LR: 0.019963 | E: -62.602960 | E_var:     4.8365 E_err:   0.034363 | NF_loss: 27.022760
[2025-11-13 02:59:04] 131:41<283:06, 8.05s/it | [Iter  981/3090] R0[891/3000]  | LR: 0.019953 | E: -62.577922 | E_var:     5.2540 E_err:   0.035815 | NF_loss: 21.037982
[2025-11-13 02:59:12] 131:49<282:57, 8.05s/it | [Iter  982/3090] R0[892/3000]  | LR: 0.019942 | E: -62.621505 | E_var:     4.9458 E_err:   0.034749 | NF_loss: 17.594435
[2025-11-13 02:59:20] 131:56<282:49, 8.05s/it | [Iter  983/3090] R0[893/3000]  | LR: 0.019932 | E: -62.568732 | E_var:     5.0299 E_err:   0.035043 | NF_loss: 26.427344
[2025-11-13 02:59:28] 132:04<282:41, 8.05s/it | [Iter  984/3090] R0[894/3000]  | LR: 0.019921 | E: -62.565463 | E_var:     4.7681 E_err:   0.034119 | NF_loss: 25.588756
[2025-11-13 02:59:36] 132:12<282:32, 8.05s/it | [Iter  985/3090] R0[895/3000]  | LR: 0.019911 | E: -62.577266 | E_var:     4.7265 E_err:   0.033970 | NF_loss: 26.967564
[2025-11-13 02:59:44] 132:20<282:24, 8.05s/it | [Iter  986/3090] R0[896/3000]  | LR: 0.019900 | E: -62.555258 | E_var:     4.7771 E_err:   0.034151 | NF_loss: 23.782770
[2025-11-13 02:59:51] 132:28<282:15, 8.05s/it | [Iter  987/3090] R0[897/3000]  | LR: 0.019890 | E: -62.570202 | E_var:     4.8609 E_err:   0.034449 | NF_loss: 17.258296
[2025-11-13 02:59:59] 132:36<282:07, 8.05s/it | [Iter  988/3090] R0[898/3000]  | LR: 0.019879 | E: -62.562021 | E_var:     4.7808 E_err:   0.034164 | NF_loss: 20.466329
[2025-11-13 03:00:07] 132:44<281:58, 8.05s/it | [Iter  989/3090] R0[899/3000]  | LR: 0.019869 | E: -62.477329 | E_var:     4.8486 E_err:   0.034405 | NF_loss: 19.967422
[2025-11-13 03:00:15] 132:52<281:50, 8.05s/it | [Iter  990/3090] R0[900/3000]  | LR: 0.019858 | E: -62.532667 | E_var:     4.7773 E_err:   0.034152 | NF_loss: 23.995203
[2025-11-13 03:00:23] 132:59<281:42, 8.05s/it | [Iter  991/3090] R0[901/3000]  | LR: 0.019847 | E: -62.515602 | E_var:     5.0192 E_err:   0.035006 | NF_loss: 17.429970
[2025-11-13 03:00:31] 133:07<281:33, 8.05s/it | [Iter  992/3090] R0[902/3000]  | LR: 0.019837 | E: -62.434812 | E_var:     5.0834 E_err:   0.035229 | NF_loss: 16.545444
[2025-11-13 03:00:39] 133:15<281:25, 8.05s/it | [Iter  993/3090] R0[903/3000]  | LR: 0.019826 | E: -62.501601 | E_var:     4.7885 E_err:   0.034191 | NF_loss: 19.507739
[2025-11-13 03:00:47] 133:23<281:16, 8.05s/it | [Iter  994/3090] R0[904/3000]  | LR: 0.019816 | E: -62.461675 | E_var:     4.8571 E_err:   0.034436 | NF_loss: 24.072584
[2025-11-13 03:00:54] 133:31<281:08, 8.05s/it | [Iter  995/3090] R0[905/3000]  | LR: 0.019805 | E: -62.532301 | E_var:     4.8821 E_err:   0.034524 | NF_loss: 25.023505
[2025-11-13 03:01:02] 133:39<280:59, 8.05s/it | [Iter  996/3090] R0[906/3000]  | LR: 0.019794 | E: -62.604871 | E_var:     4.7155 E_err:   0.033930 | NF_loss: 18.131356
[2025-11-13 03:01:10] 133:47<280:51, 8.05s/it | [Iter  997/3090] R0[907/3000]  | LR: 0.019784 | E: -62.610165 | E_var:     4.6842 E_err:   0.033817 | NF_loss: 22.890763
[2025-11-13 03:01:18] 133:55<280:43, 8.05s/it | [Iter  998/3090] R0[908/3000]  | LR: 0.019773 | E: -62.601387 | E_var:     4.5704 E_err:   0.033404 | NF_loss: 22.279916
[2025-11-13 03:01:26] 134:02<280:34, 8.05s/it | [Iter  999/3090] R0[909/3000]  | LR: 0.019762 | E: -62.645588 | E_var:     4.9480 E_err:   0.034756 | NF_loss: 20.566107
[2025-11-13 03:01:34] 134:10<280:26, 8.05s/it | [Iter 1000/3090] R0[910/3000]  | LR: 0.019752 | E: -62.692603 | E_var:     4.7143 E_err:   0.033926 | NF_loss: 19.827680
[2025-11-13 03:01:42] 134:18<280:17, 8.05s/it | [Iter 1001/3090] R0[911/3000]  | LR: 0.019741 | E: -62.717816 | E_var:     4.6564 E_err:   0.033717 | NF_loss: 20.398383
[2025-11-13 03:01:50] 134:26<280:09, 8.05s/it | [Iter 1002/3090] R0[912/3000]  | LR: 0.019730 | E: -62.641836 | E_var:     4.6053 E_err:   0.033531 | NF_loss: 21.170045
[2025-11-13 03:01:57] 134:34<280:01, 8.05s/it | [Iter 1003/3090] R0[913/3000]  | LR: 0.019720 | E: -62.567915 | E_var:     4.7822 E_err:   0.034169 | NF_loss: 18.361584
[2025-11-13 03:02:05] 134:42<279:52, 8.05s/it | [Iter 1004/3090] R0[914/3000]  | LR: 0.019709 | E: -62.528025 | E_var:     4.6741 E_err:   0.033781 | NF_loss: 19.212411
[2025-11-13 03:02:13] 134:50<279:44, 8.05s/it | [Iter 1005/3090] R0[915/3000]  | LR: 0.019698 | E: -62.578095 | E_var:     4.7378 E_err:   0.034010 | NF_loss: 17.837161
[2025-11-13 03:02:21] 134:58<279:35, 8.05s/it | [Iter 1006/3090] R0[916/3000]  | LR: 0.019688 | E: -62.630939 | E_var:     4.6835 E_err:   0.033815 | NF_loss: 15.504259
[2025-11-13 03:02:29] 135:05<279:27, 8.05s/it | [Iter 1007/3090] R0[917/3000]  | LR: 0.019677 | E: -62.639185 | E_var:     4.5563 E_err:   0.033352 | NF_loss: 21.154941
[2025-11-13 03:02:37] 135:13<279:18, 8.05s/it | [Iter 1008/3090] R0[918/3000]  | LR: 0.019666 | E: -62.608295 | E_var:     4.4353 E_err:   0.032906 | NF_loss: 18.883005
[2025-11-13 03:02:45] 135:21<279:10, 8.05s/it | [Iter 1009/3090] R0[919/3000]  | LR: 0.019655 | E: -62.529370 | E_var:     4.7082 E_err:   0.033904 | NF_loss: 19.090380
[2025-11-13 03:02:53] 135:29<279:02, 8.05s/it | [Iter 1010/3090] R0[920/3000]  | LR: 0.019645 | E: -62.600785 | E_var:     4.7148 E_err:   0.033928 | NF_loss: 17.404463
[2025-11-13 03:03:00] 135:37<278:53, 8.05s/it | [Iter 1011/3090] R0[921/3000]  | LR: 0.019634 | E: -62.665304 | E_var:     4.6238 E_err:   0.033598 | NF_loss: 18.672533
[2025-11-13 03:03:09] 135:45<278:45, 8.05s/it | [Iter 1012/3090] R0[922/3000]  | LR: 0.019623 | E: -62.639088 | E_var:     4.5710 E_err:   0.033406 | NF_loss: 23.940852
[2025-11-13 03:03:16] 135:53<278:37, 8.05s/it | [Iter 1013/3090] R0[923/3000]  | LR: 0.019612 | E: -62.599990 | E_var:     4.4822 E_err:   0.033080 | NF_loss: 18.270959
[2025-11-13 03:03:24] 136:01<278:29, 8.05s/it | [Iter 1014/3090] R0[924/3000]  | LR: 0.019602 | E: -62.592326 | E_var:     4.9089 E_err:   0.034619 | NF_loss: 14.851522
[2025-11-13 03:03:32] 136:09<278:20, 8.05s/it | [Iter 1015/3090] R0[925/3000]  | LR: 0.019591 | E: -62.676980 | E_var:     4.7603 E_err:   0.034091 | NF_loss: 21.081154
[2025-11-13 03:03:40] 136:17<278:12, 8.05s/it | [Iter 1016/3090] R0[926/3000]  | LR: 0.019580 | E: -62.693507 | E_var:     4.6863 E_err:   0.033825 | NF_loss: 23.134643
[2025-11-13 03:03:48] 136:24<278:03, 8.05s/it | [Iter 1017/3090] R0[927/3000]  | LR: 0.019569 | E: -62.672866 | E_var:     4.7718 E_err:   0.034132 | NF_loss: 19.021079
[2025-11-13 03:03:56] 136:32<277:55, 8.05s/it | [Iter 1018/3090] R0[928/3000]  | LR: 0.019559 | E: -62.644660 | E_var:     4.7593 E_err:   0.034087 | NF_loss: 17.915344
[2025-11-13 03:04:04] 136:40<277:46, 8.05s/it | [Iter 1019/3090] R0[929/3000]  | LR: 0.019548 | E: -62.667551 | E_var:     4.5822 E_err:   0.033447 | NF_loss: 16.673845
[2025-11-13 03:04:12] 136:48<277:38, 8.05s/it | [Iter 1020/3090] R0[930/3000]  | LR: 0.019537 | E: -62.715050 | E_var:     4.6178 E_err:   0.033577 | NF_loss: 13.791421
[2025-11-13 03:04:19] 136:56<277:30, 8.05s/it | [Iter 1021/3090] R0[931/3000]  | LR: 0.019526 | E: -62.682090 | E_var:     4.5011 E_err:   0.033150 | NF_loss: 19.583895
[2025-11-13 03:04:27] 137:04<277:21, 8.05s/it | [Iter 1022/3090] R0[932/3000]  | LR: 0.019515 | E: -62.646674 | E_var:     4.6885 E_err:   0.033833 | NF_loss: 19.588933
[2025-11-13 03:04:35] 137:12<277:13, 8.05s/it | [Iter 1023/3090] R0[933/3000]  | LR: 0.019504 | E: -62.610397 | E_var:     4.7022 E_err:   0.033882 | NF_loss: 18.476529
[2025-11-13 03:04:43] 137:20<277:05, 8.05s/it | [Iter 1024/3090] R0[934/3000]  | LR: 0.019494 | E: -62.585554 | E_var:     4.4766 E_err:   0.033060 | NF_loss: 18.236400
[2025-11-13 03:04:51] 137:27<276:56, 8.05s/it | [Iter 1025/3090] R0[935/3000]  | LR: 0.019483 | E: -62.546460 | E_var:     4.8245 E_err:   0.034320 | NF_loss: 16.451787
[2025-11-13 03:04:59] 137:35<276:48, 8.05s/it | [Iter 1026/3090] R0[936/3000]  | LR: 0.019472 | E: -62.588497 | E_var:     4.6926 E_err:   0.033848 | NF_loss: 16.962527
[2025-11-13 03:05:07] 137:43<276:39, 8.05s/it | [Iter 1027/3090] R0[937/3000]  | LR: 0.019461 | E: -62.640561 | E_var:     4.8107 E_err:   0.034271 | NF_loss: 16.449635
[2025-11-13 03:05:15] 137:51<276:31, 8.05s/it | [Iter 1028/3090] R0[938/3000]  | LR: 0.019450 | E: -62.626689 | E_var:     4.8147 E_err:   0.034285 | NF_loss: 23.411001
[2025-11-13 03:05:22] 137:59<276:23, 8.05s/it | [Iter 1029/3090] R0[939/3000]  | LR: 0.019439 | E: -62.593430 | E_var:     4.6327 E_err:   0.033631 | NF_loss: 26.991542
[2025-11-13 03:05:30] 138:07<276:14, 8.05s/it | [Iter 1030/3090] R0[940/3000]  | LR: 0.019428 | E: -62.587859 | E_var:     4.9836 E_err:   0.034881 | NF_loss: 20.207270
[2025-11-13 03:05:38] 138:15<276:06, 8.05s/it | [Iter 1031/3090] R0[941/3000]  | LR: 0.019417 | E: -62.669699 | E_var:     4.8624 E_err:   0.034454 | NF_loss: 27.274141
[2025-11-13 03:05:46] 138:23<275:57, 8.05s/it | [Iter 1032/3090] R0[942/3000]  | LR: 0.019407 | E: -62.633906 | E_var:     4.7996 E_err:   0.034231 | NF_loss: 14.854259
[2025-11-13 03:05:54] 138:30<275:49, 8.05s/it | [Iter 1033/3090] R0[943/3000]  | LR: 0.019396 | E: -62.615391 | E_var:     4.7558 E_err:   0.034075 | NF_loss: 23.365105
[2025-11-13 03:06:02] 138:38<275:41, 8.05s/it | [Iter 1034/3090] R0[944/3000]  | LR: 0.019385 | E: -62.625681 | E_var:     4.8184 E_err:   0.034298 | NF_loss: 16.652915
[2025-11-13 03:06:10] 138:46<275:32, 8.05s/it | [Iter 1035/3090] R0[945/3000]  | LR: 0.019374 | E: -62.688645 | E_var:     4.7465 E_err:   0.034041 | NF_loss: 21.541235
[2025-11-13 03:06:18] 138:54<275:24, 8.04s/it | [Iter 1036/3090] R0[946/3000]  | LR: 0.019363 | E: -62.643864 | E_var:     4.6145 E_err:   0.033565 | NF_loss: 34.156773
[2025-11-13 03:06:25] 139:02<275:15, 8.04s/it | [Iter 1037/3090] R0[947/3000]  | LR: 0.019352 | E: -62.644648 | E_var:     4.6492 E_err:   0.033691 | NF_loss: 27.585508
[2025-11-13 03:06:33] 139:10<275:07, 8.04s/it | [Iter 1038/3090] R0[948/3000]  | LR: 0.019341 | E: -62.664794 | E_var:     4.7226 E_err:   0.033955 | NF_loss: 23.059714
[2025-11-13 03:06:41] 139:18<274:59, 8.04s/it | [Iter 1039/3090] R0[949/3000]  | LR: 0.019330 | E: -62.696474 | E_var:     4.3870 E_err:   0.032727 | NF_loss: 15.893053
[2025-11-13 03:06:49] 139:26<274:50, 8.04s/it | [Iter 1040/3090] R0[950/3000]  | LR: 0.019319 | E: -62.707834 | E_var:     4.5114 E_err:   0.033188 | NF_loss: 27.189844
[2025-11-13 03:06:57] 139:33<274:42, 8.04s/it | [Iter 1041/3090] R0[951/3000]  | LR: 0.019308 | E: -62.683907 | E_var:     4.8054 E_err:   0.034252 | NF_loss: 32.409013
[2025-11-13 03:07:05] 139:41<274:34, 8.04s/it | [Iter 1042/3090] R0[952/3000]  | LR: 0.019297 | E: -62.647557 | E_var:     4.6390 E_err:   0.033654 | NF_loss: 36.786538
[2025-11-13 03:07:13] 139:49<274:25, 8.04s/it | [Iter 1043/3090] R0[953/3000]  | LR: 0.019286 | E: -62.597370 | E_var:     4.7074 E_err:   0.033901 | NF_loss: 22.695378
[2025-11-13 03:07:21] 139:57<274:17, 8.04s/it | [Iter 1044/3090] R0[954/3000]  | LR: 0.019275 | E: -62.556856 | E_var:     4.6187 E_err:   0.033580 | NF_loss: 19.090165
[2025-11-13 03:07:28] 140:05<274:08, 8.04s/it | [Iter 1045/3090] R0[955/3000]  | LR: 0.019264 | E: -62.367809 | E_var:     4.9836 E_err:   0.034881 | NF_loss: 30.262370
[2025-11-13 03:07:36] 140:13<274:00, 8.04s/it | [Iter 1046/3090] R0[956/3000]  | LR: 0.019253 | E: -62.498593 | E_var:     5.0382 E_err:   0.035072 | NF_loss: 23.363102
[2025-11-13 03:07:44] 140:21<273:52, 8.04s/it | [Iter 1047/3090] R0[957/3000]  | LR: 0.019242 | E: -62.572372 | E_var:     4.7684 E_err:   0.034120 | NF_loss: 26.386266
[2025-11-13 03:07:52] 140:29<273:44, 8.04s/it | [Iter 1048/3090] R0[958/3000]  | LR: 0.019231 | E: -62.616362 | E_var:     4.6374 E_err:   0.033648 | NF_loss: 21.808815
[2025-11-13 03:08:00] 140:37<273:35, 8.04s/it | [Iter 1049/3090] R0[959/3000]  | LR: 0.019220 | E: -62.670158 | E_var:     4.6734 E_err:   0.033778 | NF_loss: 24.394386
[2025-11-13 03:08:08] 140:45<273:27, 8.04s/it | [Iter 1050/3090] R0[960/3000]  | LR: 0.019209 | E: -62.699822 | E_var:     4.6547 E_err:   0.033711 | NF_loss: 21.986824
[2025-11-13 03:08:16] 140:52<273:19, 8.04s/it | [Iter 1051/3090] R0[961/3000]  | LR: 0.019198 | E: -62.726794 | E_var:     4.5009 E_err:   0.033149 | NF_loss: 18.688799
[2025-11-13 03:08:24] 141:00<273:10, 8.04s/it | [Iter 1052/3090] R0[962/3000]  | LR: 0.019187 | E: -62.732452 | E_var:     4.4841 E_err:   0.033087 | NF_loss: 13.398878
[2025-11-13 03:08:32] 141:08<273:02, 8.04s/it | [Iter 1053/3090] R0[963/3000]  | LR: 0.019176 | E: -62.651963 | E_var:     4.3999 E_err:   0.032775 | NF_loss: 19.225423
[2025-11-13 03:08:40] 141:16<272:54, 8.04s/it | [Iter 1054/3090] R0[964/3000]  | LR: 0.019165 | E: -62.656993 | E_var:     4.6310 E_err:   0.033625 | NF_loss: 19.469993
[2025-11-13 03:08:47] 141:24<272:45, 8.04s/it | [Iter 1055/3090] R0[965/3000]  | LR: 0.019154 | E: -62.642564 | E_var:     4.5715 E_err:   0.033408 | NF_loss: 19.401726
[2025-11-13 03:08:55] 141:32<272:37, 8.04s/it | [Iter 1056/3090] R0[966/3000]  | LR: 0.019143 | E: -62.720763 | E_var:     4.5435 E_err:   0.033305 | NF_loss: 21.078757
[2025-11-13 03:09:03] 141:40<272:29, 8.04s/it | [Iter 1057/3090] R0[967/3000]  | LR: 0.019131 | E: -62.687263 | E_var:     4.9574 E_err:   0.034790 | NF_loss: 20.377399
[2025-11-13 03:09:11] 141:48<272:20, 8.04s/it | [Iter 1058/3090] R0[968/3000]  | LR: 0.019120 | E: -62.656399 | E_var:     4.5806 E_err:   0.033441 | NF_loss: 18.429735
[2025-11-13 03:09:19] 141:55<272:12, 8.04s/it | [Iter 1059/3090] R0[969/3000]  | LR: 0.019109 | E: -62.688303 | E_var:     4.5814 E_err:   0.033444 | NF_loss: 16.418754
[2025-11-13 03:09:27] 142:03<272:03, 8.04s/it | [Iter 1060/3090] R0[970/3000]  | LR: 0.019098 | E: -62.684471 | E_var:     4.8594 E_err:   0.034444 | NF_loss: 19.183077
[2025-11-13 03:09:35] 142:11<271:55, 8.04s/it | [Iter 1061/3090] R0[971/3000]  | LR: 0.019087 | E: -62.644934 | E_var:     5.2257 E_err:   0.035718 | NF_loss: 23.331930
[2025-11-13 03:09:43] 142:19<271:47, 8.04s/it | [Iter 1062/3090] R0[972/3000]  | LR: 0.019076 | E: -62.620522 | E_var:     4.8674 E_err:   0.034472 | NF_loss: 11.784701
[2025-11-13 03:09:50] 142:27<271:38, 8.04s/it | [Iter 1063/3090] R0[973/3000]  | LR: 0.019065 | E: -62.566050 | E_var:     4.4980 E_err:   0.033138 | NF_loss: 18.986950
[2025-11-13 03:09:58] 142:35<271:30, 8.04s/it | [Iter 1064/3090] R0[974/3000]  | LR: 0.019054 | E: -62.655035 | E_var:     4.5403 E_err:   0.033294 | NF_loss: 21.664192
[2025-11-13 03:10:06] 142:43<271:22, 8.04s/it | [Iter 1065/3090] R0[975/3000]  | LR: 0.019042 | E: -62.576687 | E_var:     4.6892 E_err:   0.033835 | NF_loss: 17.692570
[2025-11-13 03:10:14] 142:51<271:13, 8.04s/it | [Iter 1066/3090] R0[976/3000]  | LR: 0.019031 | E: -62.647176 | E_var:     4.7259 E_err:   0.033968 | NF_loss: 14.390105
[2025-11-13 03:10:22] 142:59<271:05, 8.04s/it | [Iter 1067/3090] R0[977/3000]  | LR: 0.019020 | E: -62.625553 | E_var:     4.6434 E_err:   0.033670 | NF_loss: 17.366645
[2025-11-13 03:10:30] 143:06<270:57, 8.04s/it | [Iter 1068/3090] R0[978/3000]  | LR: 0.019009 | E: -62.656512 | E_var:     4.6927 E_err:   0.033848 | NF_loss: 20.932096
[2025-11-13 03:10:38] 143:14<270:48, 8.04s/it | [Iter 1069/3090] R0[979/3000]  | LR: 0.018998 | E: -62.759326 | E_var:     4.6923 E_err:   0.033846 | NF_loss: 14.510683
[2025-11-13 03:10:46] 143:22<270:40, 8.04s/it | [Iter 1070/3090] R0[980/3000]  | LR: 0.018987 | E: -62.719287 | E_var:     4.6070 E_err:   0.033537 | NF_loss: 21.359703
[2025-11-13 03:10:53] 143:30<270:32, 8.04s/it | [Iter 1071/3090] R0[981/3000]  | LR: 0.018975 | E: -62.547859 | E_var:     4.5658 E_err:   0.033387 | NF_loss: 16.976373
[2025-11-13 03:11:01] 143:38<270:23, 8.04s/it | [Iter 1072/3090] R0[982/3000]  | LR: 0.018964 | E: -62.560115 | E_var:     4.7969 E_err:   0.034222 | NF_loss: 16.707462
[2025-11-13 03:11:09] 143:46<270:15, 8.04s/it | [Iter 1073/3090] R0[983/3000]  | LR: 0.018953 | E: -62.558889 | E_var:     4.8441 E_err:   0.034390 | NF_loss: 18.399702
[2025-11-13 03:11:17] 143:54<270:07, 8.04s/it | [Iter 1074/3090] R0[984/3000]  | LR: 0.018942 | E: -62.504518 | E_var:     4.5997 E_err:   0.033511 | NF_loss: 13.905103
[2025-11-13 03:11:25] 144:02<269:58, 8.04s/it | [Iter 1075/3090] R0[985/3000]  | LR: 0.018931 | E: -62.441965 | E_var:     4.6727 E_err:   0.033776 | NF_loss: 16.898413
[2025-11-13 03:11:33] 144:10<269:50, 8.04s/it | [Iter 1076/3090] R0[986/3000]  | LR: 0.018919 | E: -62.584874 | E_var:     4.7109 E_err:   0.033914 | NF_loss: 22.204630
[2025-11-13 03:11:41] 144:18<269:42, 8.04s/it | [Iter 1077/3090] R0[987/3000]  | LR: 0.018908 | E: -62.471027 | E_var:     4.5345 E_err:   0.033272 | NF_loss: 16.474550
[2025-11-13 03:11:49] 144:25<269:34, 8.04s/it | [Iter 1078/3090] R0[988/3000]  | LR: 0.018897 | E: -62.471298 | E_var:     4.5694 E_err:   0.033400 | NF_loss: 15.754527
[2025-11-13 03:11:57] 144:33<269:25, 8.04s/it | [Iter 1079/3090] R0[989/3000]  | LR: 0.018886 | E: -62.479198 | E_var:     4.6241 E_err:   0.033599 | NF_loss: 19.002926
[2025-11-13 03:12:05] 144:41<269:17, 8.04s/it | [Iter 1080/3090] R0[990/3000]  | LR: 0.018874 | E: -62.498299 | E_var:     4.5880 E_err:   0.033468 | NF_loss: 20.094676
[2025-11-13 03:12:12] 144:49<269:09, 8.04s/it | [Iter 1081/3090] R0[991/3000]  | LR: 0.018863 | E: -62.559544 | E_var:     4.6720 E_err:   0.033773 | NF_loss: 16.546147
[2025-11-13 03:12:20] 144:57<269:00, 8.04s/it | [Iter 1082/3090] R0[992/3000]  | LR: 0.018852 | E: -62.513889 | E_var:     4.6316 E_err:   0.033627 | NF_loss: 16.597119
[2025-11-13 03:12:28] 145:05<268:52, 8.04s/it | [Iter 1083/3090] R0[993/3000]  | LR: 0.018840 | E: -62.559533 | E_var:     4.7444 E_err:   0.034034 | NF_loss: 12.813406
[2025-11-13 03:12:36] 145:13<268:44, 8.04s/it | [Iter 1084/3090] R0[994/3000]  | LR: 0.018829 | E: -62.600365 | E_var:     4.4650 E_err:   0.033016 | NF_loss: 15.293131
[2025-11-13 03:12:44] 145:21<268:35, 8.04s/it | [Iter 1085/3090] R0[995/3000]  | LR: 0.018818 | E: -62.489554 | E_var:     4.5388 E_err:   0.033288 | NF_loss: 14.075902
[2025-11-13 03:12:52] 145:28<268:27, 8.04s/it | [Iter 1086/3090] R0[996/3000]  | LR: 0.018807 | E: -62.434883 | E_var:     4.6035 E_err:   0.033524 | NF_loss: 14.435588
[2025-11-13 03:13:00] 145:36<268:19, 8.04s/it | [Iter 1087/3090] R0[997/3000]  | LR: 0.018795 | E: -62.407652 | E_var:     4.6271 E_err:   0.033611 | NF_loss: 18.653724
[2025-11-13 03:13:08] 145:44<268:10, 8.04s/it | [Iter 1088/3090] R0[998/3000]  | LR: 0.018784 | E: -62.436037 | E_var:     4.5205 E_err:   0.033221 | NF_loss: 18.558741
[2025-11-13 03:13:15] 145:52<268:02, 8.04s/it | [Iter 1089/3090] R0[999/3000]  | LR: 0.018773 | E: -62.559203 | E_var:     4.5441 E_err:   0.033308 | NF_loss: 12.339673
[2025-11-13 03:13:23] 146:00<267:54, 8.04s/it | [Iter 1090/3090] R0[1000/3000] | LR: 0.018761 | E: -62.558648 | E_var:     4.4891 E_err:   0.033106 | NF_loss: 19.476863
[2025-11-13 03:13:31] 146:08<267:45, 8.04s/it | [Iter 1091/3090] R0[1001/3000] | LR: 0.018750 | E: -62.523536 | E_var:     4.6855 E_err:   0.033822 | NF_loss: 16.418095
[2025-11-13 03:13:39] 146:16<267:37, 8.04s/it | [Iter 1092/3090] R0[1002/3000] | LR: 0.018739 | E: -62.618494 | E_var:     4.6751 E_err:   0.033784 | NF_loss: 26.161921
[2025-11-13 03:13:47] 146:24<267:29, 8.04s/it | [Iter 1093/3090] R0[1003/3000] | LR: 0.018727 | E: -62.687052 | E_var:     4.3576 E_err:   0.032617 | NF_loss: 13.934390
[2025-11-13 03:13:55] 146:31<267:20, 8.04s/it | [Iter 1094/3090] R0[1004/3000] | LR: 0.018716 | E: -62.623263 | E_var:     4.3043 E_err:   0.032417 | NF_loss: 18.575663
[2025-11-13 03:14:03] 146:39<267:12, 8.04s/it | [Iter 1095/3090] R0[1005/3000] | LR: 0.018705 | E: -62.665856 | E_var:     4.2851 E_err:   0.032345 | NF_loss: 20.361620
[2025-11-13 03:14:11] 146:47<267:04, 8.04s/it | [Iter 1096/3090] R0[1006/3000] | LR: 0.018693 | E: -62.678621 | E_var:     4.5508 E_err:   0.033332 | NF_loss: 17.729944
[2025-11-13 03:14:18] 146:55<266:55, 8.04s/it | [Iter 1097/3090] R0[1007/3000] | LR: 0.018682 | E: -62.658213 | E_var:     5.3689 E_err:   0.036205 | NF_loss: 15.656833
[2025-11-13 03:14:26] 147:03<266:47, 8.04s/it | [Iter 1098/3090] R0[1008/3000] | LR: 0.018671 | E: -62.728636 | E_var:     4.3910 E_err:   0.032742 | NF_loss: 20.709899
[2025-11-13 03:14:34] 147:11<266:39, 8.04s/it | [Iter 1099/3090] R0[1009/3000] | LR: 0.018659 | E: -62.699536 | E_var:     4.4995 E_err:   0.033144 | NF_loss: 17.208357
[2025-11-13 03:14:42] 147:19<266:30, 8.04s/it | [Iter 1100/3090] R0[1010/3000] | LR: 0.018648 | E: -62.709922 | E_var:     4.6349 E_err:   0.033639 | NF_loss: 31.606206
[2025-11-13 03:14:50] 147:27<266:22, 8.04s/it | [Iter 1101/3090] R0[1011/3000] | LR: 0.018636 | E: -62.703972 | E_var:     4.4121 E_err:   0.032820 | NF_loss: 17.332677
[2025-11-13 03:14:58] 147:34<266:14, 8.04s/it | [Iter 1102/3090] R0[1012/3000] | LR: 0.018625 | E: -62.710580 | E_var:     4.4781 E_err:   0.033065 | NF_loss: 17.488426
[2025-11-13 03:15:06] 147:42<266:05, 8.04s/it | [Iter 1103/3090] R0[1013/3000] | LR: 0.018614 | E: -62.781009 | E_var:     4.7322 E_err:   0.033990 | NF_loss: 21.501294
[2025-11-13 03:15:14] 147:50<265:57, 8.03s/it | [Iter 1104/3090] R0[1014/3000] | LR: 0.018602 | E: -62.712774 | E_var:     4.8510 E_err:   0.034414 | NF_loss: 14.012341
[2025-11-13 03:15:21] 147:58<265:49, 8.03s/it | [Iter 1105/3090] R0[1015/3000] | LR: 0.018591 | E: -62.704595 | E_var:     5.2872 E_err:   0.035928 | NF_loss: 15.255898
[2025-11-13 03:15:29] 148:06<265:40, 8.03s/it | [Iter 1106/3090] R0[1016/3000] | LR: 0.018579 | E: -62.658567 | E_var:     5.1412 E_err:   0.035428 | NF_loss: 16.695584
[2025-11-13 03:15:37] 148:14<265:32, 8.03s/it | [Iter 1107/3090] R0[1017/3000] | LR: 0.018568 | E: -62.679181 | E_var:     4.7134 E_err:   0.033923 | NF_loss: 13.529190
[2025-11-13 03:15:45] 148:22<265:24, 8.03s/it | [Iter 1108/3090] R0[1018/3000] | LR: 0.018556 | E: -62.682240 | E_var:     4.6862 E_err:   0.033824 | NF_loss: 23.184471
[2025-11-13 03:15:53] 148:30<265:15, 8.03s/it | [Iter 1109/3090] R0[1019/3000] | LR: 0.018545 | E: -62.741998 | E_var:     4.4660 E_err:   0.033020 | NF_loss: 28.750044
[2025-11-13 03:16:01] 148:37<265:07, 8.03s/it | [Iter 1110/3090] R0[1020/3000] | LR: 0.018533 | E: -62.710376 | E_var:     4.5137 E_err:   0.033196 | NF_loss: 16.634453
[2025-11-13 03:16:09] 148:45<264:59, 8.03s/it | [Iter 1111/3090] R0[1021/3000] | LR: 0.018522 | E: -62.709903 | E_var:     4.5409 E_err:   0.033296 | NF_loss: 16.693538
[2025-11-13 03:16:17] 148:53<264:50, 8.03s/it | [Iter 1112/3090] R0[1022/3000] | LR: 0.018510 | E: -62.692927 | E_var:     4.5959 E_err:   0.033497 | NF_loss: 20.284776
[2025-11-13 03:16:24] 149:01<264:42, 8.03s/it | [Iter 1113/3090] R0[1023/3000] | LR: 0.018499 | E: -62.716209 | E_var:     4.7599 E_err:   0.034089 | NF_loss: 23.055413
[2025-11-13 03:16:33] 149:09<264:34, 8.03s/it | [Iter 1114/3090] R0[1024/3000] | LR: 0.018488 | E: -62.745437 | E_var:     4.8440 E_err:   0.034389 | NF_loss: 18.771597
[2025-11-13 03:16:40] 149:17<264:26, 8.03s/it | [Iter 1115/3090] R0[1025/3000] | LR: 0.018476 | E: -62.655734 | E_var:     4.7440 E_err:   0.034032 | NF_loss: 21.798045
[2025-11-13 03:16:48] 149:25<264:18, 8.03s/it | [Iter 1116/3090] R0[1026/3000] | LR: 0.018465 | E: -62.737731 | E_var:     4.4871 E_err:   0.033098 | NF_loss: 16.976577
[2025-11-13 03:16:56] 149:33<264:09, 8.03s/it | [Iter 1117/3090] R0[1027/3000] | LR: 0.018453 | E: -62.704869 | E_var:     4.3639 E_err:   0.032640 | NF_loss: 16.925642
[2025-11-13 03:17:04] 149:41<264:01, 8.03s/it | [Iter 1118/3090] R0[1028/3000] | LR: 0.018441 | E: -62.748334 | E_var:     4.5117 E_err:   0.033189 | NF_loss: 19.103243
[2025-11-13 03:17:12] 149:49<263:53, 8.03s/it | [Iter 1119/3090] R0[1029/3000] | LR: 0.018430 | E: -62.746812 | E_var:     4.5620 E_err:   0.033373 | NF_loss: 25.498186
[2025-11-13 03:17:20] 149:56<263:44, 8.03s/it | [Iter 1120/3090] R0[1030/3000] | LR: 0.018418 | E: -62.773032 | E_var:     4.5794 E_err:   0.033437 | NF_loss: 16.493060
[2025-11-13 03:17:28] 150:04<263:36, 8.03s/it | [Iter 1121/3090] R0[1031/3000] | LR: 0.018407 | E: -62.770265 | E_var:     4.5032 E_err:   0.033158 | NF_loss: 14.300403
[2025-11-13 03:17:36] 150:12<263:28, 8.03s/it | [Iter 1122/3090] R0[1032/3000] | LR: 0.018395 | E: -62.690261 | E_var:     4.6129 E_err:   0.033559 | NF_loss: 19.770267
[2025-11-13 03:17:44] 150:20<263:19, 8.03s/it | [Iter 1123/3090] R0[1033/3000] | LR: 0.018384 | E: -62.709439 | E_var:     4.6514 E_err:   0.033699 | NF_loss: 28.021344
[2025-11-13 03:17:51] 150:28<263:11, 8.03s/it | [Iter 1124/3090] R0[1034/3000] | LR: 0.018372 | E: -62.711424 | E_var:     5.0579 E_err:   0.035140 | NF_loss: 13.435154
[2025-11-13 03:17:59] 150:36<263:03, 8.03s/it | [Iter 1125/3090] R0[1035/3000] | LR: 0.018361 | E: -62.699821 | E_var:     4.8343 E_err:   0.034355 | NF_loss: 13.857432
[2025-11-13 03:18:07] 150:44<262:55, 8.03s/it | [Iter 1126/3090] R0[1036/3000] | LR: 0.018349 | E: -62.688571 | E_var:     4.8153 E_err:   0.034287 | NF_loss: 14.942884
[2025-11-13 03:18:15] 150:52<262:46, 8.03s/it | [Iter 1127/3090] R0[1037/3000] | LR: 0.018338 | E: -62.683510 | E_var:     4.6915 E_err:   0.033844 | NF_loss: 16.774812
[2025-11-13 03:18:23] 150:59<262:38, 8.03s/it | [Iter 1128/3090] R0[1038/3000] | LR: 0.018326 | E: -62.719061 | E_var:     4.6383 E_err:   0.033651 | NF_loss: 24.698346
[2025-11-13 03:18:31] 151:07<262:30, 8.03s/it | [Iter 1129/3090] R0[1039/3000] | LR: 0.018314 | E: -62.607601 | E_var:     5.0902 E_err:   0.035252 | NF_loss: 15.201708
[2025-11-13 03:18:39] 151:15<262:21, 8.03s/it | [Iter 1130/3090] R0[1040/3000] | LR: 0.018303 | E: -62.622929 | E_var:     4.6218 E_err:   0.033591 | NF_loss: 25.450146
[2025-11-13 03:18:47] 151:23<262:13, 8.03s/it | [Iter 1131/3090] R0[1041/3000] | LR: 0.018291 | E: -62.581483 | E_var:     5.1065 E_err:   0.035309 | NF_loss: 12.464379
[2025-11-13 03:18:54] 151:31<262:05, 8.03s/it | [Iter 1132/3090] R0[1042/3000] | LR: 0.018280 | E: -62.572309 | E_var:     4.9418 E_err:   0.034735 | NF_loss: 15.084291
[2025-11-13 03:19:02] 151:39<261:56, 8.03s/it | [Iter 1133/3090] R0[1043/3000] | LR: 0.018268 | E: -62.704565 | E_var:     4.5314 E_err:   0.033261 | NF_loss: 25.721386
[2025-11-13 03:19:10] 151:47<261:48, 8.03s/it | [Iter 1134/3090] R0[1044/3000] | LR: 0.018256 | E: -62.673988 | E_var:     4.5923 E_err:   0.033484 | NF_loss: 29.221406
[2025-11-13 03:19:18] 151:55<261:40, 8.03s/it | [Iter 1135/3090] R0[1045/3000] | LR: 0.018245 | E: -62.724203 | E_var:     4.6249 E_err:   0.033602 | NF_loss: 24.340616
[2025-11-13 03:19:26] 152:02<261:32, 8.03s/it | [Iter 1136/3090] R0[1046/3000] | LR: 0.018233 | E: -62.663040 | E_var:     4.7815 E_err:   0.034167 | NF_loss: 19.657003
[2025-11-13 03:19:34] 152:10<261:23, 8.03s/it | [Iter 1137/3090] R0[1047/3000] | LR: 0.018222 | E: -62.555938 | E_var:     4.9719 E_err:   0.034840 | NF_loss: 21.902949
[2025-11-13 03:19:42] 152:18<261:15, 8.03s/it | [Iter 1138/3090] R0[1048/3000] | LR: 0.018210 | E: -62.462096 | E_var:     5.6626 E_err:   0.037182 | NF_loss: 27.120236
[2025-11-13 03:19:50] 152:26<261:07, 8.03s/it | [Iter 1139/3090] R0[1049/3000] | LR: 0.018198 | E: -62.543864 | E_var:     4.9588 E_err:   0.034794 | NF_loss: 23.552533
[2025-11-13 03:19:57] 152:34<260:58, 8.03s/it | [Iter 1140/3090] R0[1050/3000] | LR: 0.018187 | E: -62.614331 | E_var:     5.4909 E_err:   0.036614 | NF_loss: 16.197889
[2025-11-13 03:20:05] 152:42<260:50, 8.03s/it | [Iter 1141/3090] R0[1051/3000] | LR: 0.018175 | E: -62.562611 | E_var:     5.1013 E_err:   0.035291 | NF_loss: 45.828146
[2025-11-13 03:20:13] 152:50<260:42, 8.03s/it | [Iter 1142/3090] R0[1052/3000] | LR: 0.018163 | E: -62.663592 | E_var:     4.9435 E_err:   0.034741 | NF_loss: 14.744435
[2025-11-13 03:20:21] 152:58<260:34, 8.03s/it | [Iter 1143/3090] R0[1053/3000] | LR: 0.018152 | E: -62.705738 | E_var:     4.4663 E_err:   0.033021 | NF_loss: 20.079878
[2025-11-13 03:20:29] 153:05<260:25, 8.03s/it | [Iter 1144/3090] R0[1054/3000] | LR: 0.018140 | E: -62.579773 | E_var:     4.4413 E_err:   0.032929 | NF_loss: 15.420133
[2025-11-13 03:20:37] 153:13<260:17, 8.03s/it | [Iter 1145/3090] R0[1055/3000] | LR: 0.018128 | E: -62.623255 | E_var:     4.8276 E_err:   0.034331 | NF_loss: 14.277478
[2025-11-13 03:20:45] 153:21<260:09, 8.03s/it | [Iter 1146/3090] R0[1056/3000] | LR: 0.018117 | E: -62.625868 | E_var:     4.8718 E_err:   0.034488 | NF_loss: 15.956369
[2025-11-13 03:20:53] 153:29<260:00, 8.03s/it | [Iter 1147/3090] R0[1057/3000] | LR: 0.018105 | E: -62.598103 | E_var:     5.0479 E_err:   0.035106 | NF_loss: 11.260133
[2025-11-13 03:21:00] 153:37<259:52, 8.03s/it | [Iter 1148/3090] R0[1058/3000] | LR: 0.018093 | E: -62.727247 | E_var:     4.4905 E_err:   0.033111 | NF_loss: 19.515051
[2025-11-13 03:21:08] 153:45<259:44, 8.03s/it | [Iter 1149/3090] R0[1059/3000] | LR: 0.018081 | E: -62.655431 | E_var:     4.6917 E_err:   0.033844 | NF_loss: 13.395052
[2025-11-13 03:21:16] 153:53<259:36, 8.03s/it | [Iter 1150/3090] R0[1060/3000] | LR: 0.018070 | E: -62.646154 | E_var:     4.6092 E_err:   0.033546 | NF_loss: 20.565889
[2025-11-13 03:21:24] 154:01<259:28, 8.03s/it | [Iter 1151/3090] R0[1061/3000] | LR: 0.018058 | E: -62.698221 | E_var:     4.7405 E_err:   0.034020 | NF_loss: 15.330847
[2025-11-13 03:21:32] 154:09<259:19, 8.03s/it | [Iter 1152/3090] R0[1062/3000] | LR: 0.018046 | E: -62.641894 | E_var:     4.4278 E_err:   0.032879 | NF_loss: 8.555644
[2025-11-13 03:21:40] 154:17<259:11, 8.03s/it | [Iter 1153/3090] R0[1063/3000] | LR: 0.018035 | E: -62.599745 | E_var:     4.5735 E_err:   0.033415 | NF_loss: 14.705444
[2025-11-13 03:21:48] 154:24<259:03, 8.03s/it | [Iter 1154/3090] R0[1064/3000] | LR: 0.018023 | E: -62.653225 | E_var:     4.6145 E_err:   0.033565 | NF_loss: 3.898054
[2025-11-13 03:21:56] 154:32<258:55, 8.03s/it | [Iter 1155/3090] R0[1065/3000] | LR: 0.018011 | E: -62.429482 | E_var:     5.0222 E_err:   0.035016 | NF_loss: 15.634132
[2025-11-13 03:22:04] 154:40<258:46, 8.03s/it | [Iter 1156/3090] R0[1066/3000] | LR: 0.017999 | E: -62.559355 | E_var:     4.6043 E_err:   0.033527 | NF_loss: 26.902902
[2025-11-13 03:22:12] 154:48<258:38, 8.03s/it | [Iter 1157/3090] R0[1067/3000] | LR: 0.017988 | E: -62.524850 | E_var:     4.7426 E_err:   0.034027 | NF_loss: 23.786615
[2025-11-13 03:22:19] 154:56<258:30, 8.03s/it | [Iter 1158/3090] R0[1068/3000] | LR: 0.017976 | E: -62.437728 | E_var:     4.7301 E_err:   0.033982 | NF_loss: 14.776804
[2025-11-13 03:22:27] 155:04<258:21, 8.03s/it | [Iter 1159/3090] R0[1069/3000] | LR: 0.017964 | E: -62.403586 | E_var:     4.8489 E_err:   0.034407 | NF_loss: 22.702123
[2025-11-13 03:22:35] 155:12<258:13, 8.03s/it | [Iter 1160/3090] R0[1070/3000] | LR: 0.017952 | E: -62.462029 | E_var:     4.7470 E_err:   0.034043 | NF_loss: 22.438622
[2025-11-13 03:22:43] 155:20<258:05, 8.03s/it | [Iter 1161/3090] R0[1071/3000] | LR: 0.017940 | E: -62.562919 | E_var:     4.4491 E_err:   0.032958 | NF_loss: 21.916259
[2025-11-13 03:22:51] 155:27<257:57, 8.03s/it | [Iter 1162/3090] R0[1072/3000] | LR: 0.017929 | E: -62.598771 | E_var:     4.4784 E_err:   0.033066 | NF_loss: 21.448425
[2025-11-13 03:22:59] 155:35<257:48, 8.03s/it | [Iter 1163/3090] R0[1073/3000] | LR: 0.017917 | E: -62.648457 | E_var:     4.5296 E_err:   0.033255 | NF_loss: 16.886354
[2025-11-13 03:23:07] 155:43<257:40, 8.03s/it | [Iter 1164/3090] R0[1074/3000] | LR: 0.017905 | E: -62.620264 | E_var:     4.3046 E_err:   0.032418 | NF_loss: 15.686122
[2025-11-13 03:23:15] 155:51<257:32, 8.03s/it | [Iter 1165/3090] R0[1075/3000] | LR: 0.017893 | E: -62.559382 | E_var:     4.3021 E_err:   0.032408 | NF_loss: 16.082648
[2025-11-13 03:23:22] 155:59<257:23, 8.03s/it | [Iter 1166/3090] R0[1076/3000] | LR: 0.017881 | E: -62.499842 | E_var:     4.4858 E_err:   0.033093 | NF_loss: 16.652097
[2025-11-13 03:23:30] 156:07<257:15, 8.03s/it | [Iter 1167/3090] R0[1077/3000] | LR: 0.017870 | E: -62.536785 | E_var:     4.7310 E_err:   0.033986 | NF_loss: 19.131695
[2025-11-13 03:23:38] 156:15<257:07, 8.03s/it | [Iter 1168/3090] R0[1078/3000] | LR: 0.017858 | E: -62.585029 | E_var:     4.4643 E_err:   0.033014 | NF_loss: 18.277685
[2025-11-13 03:23:46] 156:23<256:59, 8.03s/it | [Iter 1169/3090] R0[1079/3000] | LR: 0.017846 | E: -62.542935 | E_var:     4.3938 E_err:   0.032752 | NF_loss: 19.324682
[2025-11-13 03:23:54] 156:30<256:50, 8.03s/it | [Iter 1170/3090] R0[1080/3000] | LR: 0.017834 | E: -62.635425 | E_var:     4.2342 E_err:   0.032152 | NF_loss: 20.346144
[2025-11-13 03:24:02] 156:38<256:42, 8.03s/it | [Iter 1171/3090] R0[1081/3000] | LR: 0.017822 | E: -62.588522 | E_var:     4.4814 E_err:   0.033077 | NF_loss: 19.794310
[2025-11-13 03:24:10] 156:46<256:34, 8.03s/it | [Iter 1172/3090] R0[1082/3000] | LR: 0.017810 | E: -62.599887 | E_var:     4.4653 E_err:   0.033017 | NF_loss: 18.078881
[2025-11-13 03:24:18] 156:54<256:25, 8.03s/it | [Iter 1173/3090] R0[1083/3000] | LR: 0.017799 | E: -62.588911 | E_var:     4.3719 E_err:   0.032670 | NF_loss: 20.219349
[2025-11-13 03:24:25] 157:02<256:17, 8.03s/it | [Iter 1174/3090] R0[1084/3000] | LR: 0.017787 | E: -62.601468 | E_var:     4.6800 E_err:   0.033802 | NF_loss: 15.563216
[2025-11-13 03:24:33] 157:10<256:09, 8.03s/it | [Iter 1175/3090] R0[1085/3000] | LR: 0.017775 | E: -62.637409 | E_var:     4.5453 E_err:   0.033312 | NF_loss: 19.109366
[2025-11-13 03:24:41] 157:18<256:01, 8.03s/it | [Iter 1176/3090] R0[1086/3000] | LR: 0.017763 | E: -62.619949 | E_var:     4.3415 E_err:   0.032557 | NF_loss: 24.779634
[2025-11-13 03:24:49] 157:26<255:52, 8.03s/it | [Iter 1177/3090] R0[1087/3000] | LR: 0.017751 | E: -62.636517 | E_var:     4.4737 E_err:   0.033048 | NF_loss: 20.719687
[2025-11-13 03:24:57] 157:33<255:44, 8.03s/it | [Iter 1178/3090] R0[1088/3000] | LR: 0.017739 | E: -62.671444 | E_var:     4.5032 E_err:   0.033157 | NF_loss: 17.792476
[2025-11-13 03:25:05] 157:41<255:36, 8.03s/it | [Iter 1179/3090] R0[1089/3000] | LR: 0.017727 | E: -62.744660 | E_var:     4.4361 E_err:   0.032910 | NF_loss: 17.870046
[2025-11-13 03:25:13] 157:49<255:28, 8.03s/it | [Iter 1180/3090] R0[1090/3000] | LR: 0.017715 | E: -62.740511 | E_var:     4.6491 E_err:   0.033690 | NF_loss: 24.488859
[2025-11-13 03:25:21] 157:57<255:19, 8.03s/it | [Iter 1181/3090] R0[1091/3000] | LR: 0.017704 | E: -62.685622 | E_var:     4.4520 E_err:   0.032968 | NF_loss: 21.585152
[2025-11-13 03:25:28] 158:05<255:11, 8.02s/it | [Iter 1182/3090] R0[1092/3000] | LR: 0.017692 | E: -62.681348 | E_var:     4.3002 E_err:   0.032402 | NF_loss: 17.612804
[2025-11-13 03:25:37] 158:13<255:03, 8.03s/it | [Iter 1183/3090] R0[1093/3000] | LR: 0.017680 | E: -62.662179 | E_var:     4.5676 E_err:   0.033394 | NF_loss: 20.675892
[2025-11-13 03:25:44] 158:21<254:55, 8.02s/it | [Iter 1184/3090] R0[1094/3000] | LR: 0.017668 | E: -62.735929 | E_var:     4.3128 E_err:   0.032449 | NF_loss: 18.308795
[2025-11-13 03:25:52] 158:29<254:47, 8.02s/it | [Iter 1185/3090] R0[1095/3000] | LR: 0.017656 | E: -62.675093 | E_var:     4.8634 E_err:   0.034458 | NF_loss: 13.515504
[2025-11-13 03:26:00] 158:37<254:38, 8.02s/it | [Iter 1186/3090] R0[1096/3000] | LR: 0.017644 | E: -62.731498 | E_var:     4.5014 E_err:   0.033151 | NF_loss: 20.596711
[2025-11-13 03:26:08] 158:45<254:30, 8.02s/it | [Iter 1187/3090] R0[1097/3000] | LR: 0.017632 | E: -62.702473 | E_var:     4.2197 E_err:   0.032097 | NF_loss: 10.512528
[2025-11-13 03:26:16] 158:52<254:22, 8.02s/it | [Iter 1188/3090] R0[1098/3000] | LR: 0.017620 | E: -62.655368 | E_var:     4.3857 E_err:   0.032722 | NF_loss: 13.815403
[2025-11-13 03:26:24] 159:00<254:14, 8.02s/it | [Iter 1189/3090] R0[1099/3000] | LR: 0.017608 | E: -62.622888 | E_var:     4.3649 E_err:   0.032644 | NF_loss: 15.852996
[2025-11-13 03:26:32] 159:08<254:05, 8.02s/it | [Iter 1190/3090] R0[1100/3000] | LR: 0.017596 | E: -62.635656 | E_var:     4.6436 E_err:   0.033670 | NF_loss: 23.729588
[2025-11-13 03:26:40] 159:16<253:57, 8.02s/it | [Iter 1191/3090] R0[1101/3000] | LR: 0.017584 | E: -62.679850 | E_var:     4.4957 E_err:   0.033130 | NF_loss: 17.974002
[2025-11-13 03:26:47] 159:24<253:49, 8.02s/it | [Iter 1192/3090] R0[1102/3000] | LR: 0.017572 | E: -62.657634 | E_var:     4.1864 E_err:   0.031970 | NF_loss: 17.317978
[2025-11-13 03:26:55] 159:32<253:41, 8.02s/it | [Iter 1193/3090] R0[1103/3000] | LR: 0.017560 | E: -62.602653 | E_var:     4.2291 E_err:   0.032132 | NF_loss: 16.265755
[2025-11-13 03:27:03] 159:40<253:32, 8.02s/it | [Iter 1194/3090] R0[1104/3000] | LR: 0.017548 | E: -62.612641 | E_var:     4.4444 E_err:   0.032940 | NF_loss: 21.765604
[2025-11-13 03:27:11] 159:48<253:24, 8.02s/it | [Iter 1195/3090] R0[1105/3000] | LR: 0.017536 | E: -62.604450 | E_var:     4.3993 E_err:   0.032773 | NF_loss: 19.414188
[2025-11-13 03:27:19] 159:55<253:16, 8.02s/it | [Iter 1196/3090] R0[1106/3000] | LR: 0.017524 | E: -62.671275 | E_var:     4.3542 E_err:   0.032604 | NF_loss: 13.117139
[2025-11-13 03:27:27] 160:03<253:08, 8.02s/it | [Iter 1197/3090] R0[1107/3000] | LR: 0.017512 | E: -62.633313 | E_var:     4.4022 E_err:   0.032784 | NF_loss: 23.407541
[2025-11-13 03:27:35] 160:11<252:59, 8.02s/it | [Iter 1198/3090] R0[1108/3000] | LR: 0.017500 | E: -62.704361 | E_var:     4.2843 E_err:   0.032342 | NF_loss: 30.235116
[2025-11-13 03:27:43] 160:19<252:51, 8.02s/it | [Iter 1199/3090] R0[1109/3000] | LR: 0.017488 | E: -62.670163 | E_var:     4.2730 E_err:   0.032299 | NF_loss: 22.559426
[2025-11-13 03:27:50] 160:27<252:43, 8.02s/it | [Iter 1200/3090] R0[1110/3000] | LR: 0.017476 | E: -62.590966 | E_var:     4.5006 E_err:   0.033148 | NF_loss: 24.356378
[2025-11-13 03:27:51] 保存checkpoint: hybrid_checkpoint_iter_001200.pkl
[2025-11-13 03:27:58] 160:35<252:35, 8.02s/it | [Iter 1201/3090] R0[1111/3000] | LR: 0.017464 | E: -62.589794 | E_var:     4.3881 E_err:   0.032731 | NF_loss: 28.162288
[2025-11-13 03:28:06] 160:43<252:27, 8.02s/it | [Iter 1202/3090] R0[1112/3000] | LR: 0.017452 | E: -62.596986 | E_var:     4.5471 E_err:   0.033319 | NF_loss: 20.885134
[2025-11-13 03:28:14] 160:51<252:18, 8.02s/it | [Iter 1203/3090] R0[1113/3000] | LR: 0.017440 | E: -62.581359 | E_var:     4.5134 E_err:   0.033195 | NF_loss: 16.015938
[2025-11-13 03:28:22] 160:59<252:10, 8.02s/it | [Iter 1204/3090] R0[1114/3000] | LR: 0.017428 | E: -62.554596 | E_var:     4.2127 E_err:   0.032070 | NF_loss: 25.686954
[2025-11-13 03:28:30] 161:07<252:02, 8.02s/it | [Iter 1205/3090] R0[1115/3000] | LR: 0.017416 | E: -62.572862 | E_var:     4.3860 E_err:   0.032723 | NF_loss: 25.306635
[2025-11-13 03:28:38] 161:14<251:54, 8.02s/it | [Iter 1206/3090] R0[1116/3000] | LR: 0.017404 | E: -62.553978 | E_var:     4.9674 E_err:   0.034824 | NF_loss: 23.679511
[2025-11-13 03:28:46] 161:22<251:45, 8.02s/it | [Iter 1207/3090] R0[1117/3000] | LR: 0.017392 | E: -62.690225 | E_var:     4.3329 E_err:   0.032524 | NF_loss: 14.775212
[2025-11-13 03:28:54] 161:30<251:37, 8.02s/it | [Iter 1208/3090] R0[1118/3000] | LR: 0.017380 | E: -62.693355 | E_var:     4.3479 E_err:   0.032580 | NF_loss: 28.921016
[2025-11-13 03:29:02] 161:38<251:29, 8.02s/it | [Iter 1209/3090] R0[1119/3000] | LR: 0.017368 | E: -62.670776 | E_var:     4.4938 E_err:   0.033123 | NF_loss: 59.083121
[2025-11-13 03:29:09] 161:46<251:21, 8.02s/it | [Iter 1210/3090] R0[1120/3000] | LR: 0.017356 | E: -62.700741 | E_var:     4.6015 E_err:   0.033517 | NF_loss: 20.417523
[2025-11-13 03:29:17] 161:54<251:12, 8.02s/it | [Iter 1211/3090] R0[1121/3000] | LR: 0.017344 | E: -62.701624 | E_var:     4.3631 E_err:   0.032638 | NF_loss: 36.759408
[2025-11-13 03:29:25] 162:02<251:04, 8.02s/it | [Iter 1212/3090] R0[1122/3000] | LR: 0.017332 | E: -62.725758 | E_var:     4.3314 E_err:   0.032519 | NF_loss: 44.557021
[2025-11-13 03:29:33] 162:10<250:56, 8.02s/it | [Iter 1213/3090] R0[1123/3000] | LR: 0.017320 | E: -62.679832 | E_var:     4.1983 E_err:   0.032015 | NF_loss: 32.177215
[2025-11-13 03:29:41] 162:17<250:48, 8.02s/it | [Iter 1214/3090] R0[1124/3000] | LR: 0.017308 | E: -62.652031 | E_var:     4.3868 E_err:   0.032726 | NF_loss: 15.638925
[2025-11-13 03:29:49] 162:25<250:39, 8.02s/it | [Iter 1215/3090] R0[1125/3000] | LR: 0.017296 | E: -62.672696 | E_var:     4.4176 E_err:   0.032841 | NF_loss: 17.119915
[2025-11-13 03:29:57] 162:33<250:31, 8.02s/it | [Iter 1216/3090] R0[1126/3000] | LR: 0.017284 | E: -62.598015 | E_var:     4.3372 E_err:   0.032541 | NF_loss: 23.519119
[2025-11-13 03:30:05] 162:41<250:23, 8.02s/it | [Iter 1217/3090] R0[1127/3000] | LR: 0.017271 | E: -62.594706 | E_var:     4.8334 E_err:   0.034352 | NF_loss: 26.025016
[2025-11-13 03:30:12] 162:49<250:15, 8.02s/it | [Iter 1218/3090] R0[1128/3000] | LR: 0.017259 | E: -62.681858 | E_var:     4.3739 E_err:   0.032678 | NF_loss: 16.220102
[2025-11-13 03:30:20] 162:57<250:06, 8.02s/it | [Iter 1219/3090] R0[1129/3000] | LR: 0.017247 | E: -62.649618 | E_var:     4.2911 E_err:   0.032367 | NF_loss: 21.253574
[2025-11-13 03:30:28] 163:05<249:58, 8.02s/it | [Iter 1220/3090] R0[1130/3000] | LR: 0.017235 | E: -62.562827 | E_var:     4.5113 E_err:   0.033187 | NF_loss: 32.788800
[2025-11-13 03:30:36] 163:13<249:50, 8.02s/it | [Iter 1221/3090] R0[1131/3000] | LR: 0.017223 | E: -62.630361 | E_var:     4.4371 E_err:   0.032913 | NF_loss: 23.020460
[2025-11-13 03:30:44] 163:20<249:42, 8.02s/it | [Iter 1222/3090] R0[1132/3000] | LR: 0.017211 | E: -62.636609 | E_var:     4.5514 E_err:   0.033334 | NF_loss: 21.457106
[2025-11-13 03:30:52] 163:28<249:33, 8.02s/it | [Iter 1223/3090] R0[1133/3000] | LR: 0.017199 | E: -62.652635 | E_var:     4.4298 E_err:   0.032886 | NF_loss: 17.519396
[2025-11-13 03:31:00] 163:36<249:25, 8.02s/it | [Iter 1224/3090] R0[1134/3000] | LR: 0.017187 | E: -62.613608 | E_var:     4.4998 E_err:   0.033145 | NF_loss: 13.980247
[2025-11-13 03:31:08] 163:44<249:17, 8.02s/it | [Iter 1225/3090] R0[1135/3000] | LR: 0.017175 | E: -62.683174 | E_var:     4.3955 E_err:   0.032758 | NF_loss: 22.788503
[2025-11-13 03:31:15] 163:52<249:09, 8.02s/it | [Iter 1226/3090] R0[1136/3000] | LR: 0.017162 | E: -62.681077 | E_var:     4.2612 E_err:   0.032254 | NF_loss: 32.300432
[2025-11-13 03:31:24] 164:00<249:01, 8.02s/it | [Iter 1227/3090] R0[1137/3000] | LR: 0.017150 | E: -62.680670 | E_var:     4.3182 E_err:   0.032469 | NF_loss: 16.680409
[2025-11-13 03:31:31] 164:08<248:53, 8.02s/it | [Iter 1228/3090] R0[1138/3000] | LR: 0.017138 | E: -62.573523 | E_var:     4.4722 E_err:   0.033043 | NF_loss: 18.744446
[2025-11-13 03:31:39] 164:16<248:44, 8.02s/it | [Iter 1229/3090] R0[1139/3000] | LR: 0.017126 | E: -62.636492 | E_var:     4.3917 E_err:   0.032744 | NF_loss: 21.198237
[2025-11-13 03:31:47] 164:24<248:36, 8.02s/it | [Iter 1230/3090] R0[1140/3000] | LR: 0.017114 | E: -62.704703 | E_var:     4.3096 E_err:   0.032437 | NF_loss: 17.004137
[2025-11-13 03:31:55] 164:32<248:28, 8.02s/it | [Iter 1231/3090] R0[1141/3000] | LR: 0.017102 | E: -62.660969 | E_var:     4.4853 E_err:   0.033091 | NF_loss: 17.039120
[2025-11-13 03:32:03] 164:39<248:20, 8.02s/it | [Iter 1232/3090] R0[1142/3000] | LR: 0.017089 | E: -62.725843 | E_var:     4.4342 E_err:   0.032903 | NF_loss: 38.982385
[2025-11-13 03:32:11] 164:47<248:11, 8.02s/it | [Iter 1233/3090] R0[1143/3000] | LR: 0.017077 | E: -62.743582 | E_var:     4.2411 E_err:   0.032178 | NF_loss: 13.634998
[2025-11-13 03:32:19] 164:55<248:03, 8.02s/it | [Iter 1234/3090] R0[1144/3000] | LR: 0.017065 | E: -62.761271 | E_var:     4.6492 E_err:   0.033691 | NF_loss: 20.362526
[2025-11-13 03:32:27] 165:03<247:55, 8.02s/it | [Iter 1235/3090] R0[1145/3000] | LR: 0.017053 | E: -62.782437 | E_var:     4.6053 E_err:   0.033531 | NF_loss: 23.969446
[2025-11-13 03:32:34] 165:11<247:47, 8.02s/it | [Iter 1236/3090] R0[1146/3000] | LR: 0.017041 | E: -62.756436 | E_var:     4.3968 E_err:   0.032763 | NF_loss: 38.415821
[2025-11-13 03:32:42] 165:19<247:38, 8.02s/it | [Iter 1237/3090] R0[1147/3000] | LR: 0.017028 | E: -62.776051 | E_var:     6.1002 E_err:   0.038591 | NF_loss: 17.724544
[2025-11-13 03:32:50] 165:27<247:30, 8.02s/it | [Iter 1238/3090] R0[1148/3000] | LR: 0.017016 | E: -62.739025 | E_var:     4.4362 E_err:   0.032910 | NF_loss: 17.273765
[2025-11-13 03:32:58] 165:35<247:22, 8.02s/it | [Iter 1239/3090] R0[1149/3000] | LR: 0.017004 | E: -62.800910 | E_var:     4.4291 E_err:   0.032883 | NF_loss: 31.916749
[2025-11-13 03:33:06] 165:42<247:14, 8.02s/it | [Iter 1240/3090] R0[1150/3000] | LR: 0.016992 | E: -62.739858 | E_var:     4.5530 E_err:   0.033340 | NF_loss: 20.947895
[2025-11-13 03:33:14] 165:50<247:06, 8.02s/it | [Iter 1241/3090] R0[1151/3000] | LR: 0.016980 | E: -62.688139 | E_var:     4.5314 E_err:   0.033261 | NF_loss: 21.632350
[2025-11-13 03:33:22] 165:58<246:57, 8.02s/it | [Iter 1242/3090] R0[1152/3000] | LR: 0.016967 | E: -62.711664 | E_var:     4.8157 E_err:   0.034289 | NF_loss: 21.752508
[2025-11-13 03:33:30] 166:06<246:49, 8.02s/it | [Iter 1243/3090] R0[1153/3000] | LR: 0.016955 | E: -62.714586 | E_var:     4.3817 E_err:   0.032707 | NF_loss: 19.995738
[2025-11-13 03:33:37] 166:14<246:41, 8.02s/it | [Iter 1244/3090] R0[1154/3000] | LR: 0.016943 | E: -62.781613 | E_var:     4.5780 E_err:   0.033432 | NF_loss: 18.983959
[2025-11-13 03:33:45] 166:22<246:33, 8.02s/it | [Iter 1245/3090] R0[1155/3000] | LR: 0.016931 | E: -62.753465 | E_var:     4.3860 E_err:   0.032723 | NF_loss: 26.204678
[2025-11-13 03:33:53] 166:30<246:24, 8.02s/it | [Iter 1246/3090] R0[1156/3000] | LR: 0.016918 | E: -62.741696 | E_var:     4.5029 E_err:   0.033156 | NF_loss: 35.715681
[2025-11-13 03:34:01] 166:38<246:16, 8.02s/it | [Iter 1247/3090] R0[1157/3000] | LR: 0.016906 | E: -62.759466 | E_var:     4.8830 E_err:   0.034527 | NF_loss: 20.699144
[2025-11-13 03:34:09] 166:45<246:08, 8.02s/it | [Iter 1248/3090] R0[1158/3000] | LR: 0.016894 | E: -62.744572 | E_var:     4.7717 E_err:   0.034131 | NF_loss: 35.361908
[2025-11-13 03:34:17] 166:53<246:00, 8.02s/it | [Iter 1249/3090] R0[1159/3000] | LR: 0.016882 | E: -62.731680 | E_var:     4.6192 E_err:   0.033582 | NF_loss: 34.765410
[2025-11-13 03:34:25] 167:01<245:52, 8.02s/it | [Iter 1250/3090] R0[1160/3000] | LR: 0.016869 | E: -62.789577 | E_var:     4.4864 E_err:   0.033096 | NF_loss: 21.275588
[2025-11-13 03:34:33] 167:09<245:43, 8.02s/it | [Iter 1251/3090] R0[1161/3000] | LR: 0.016857 | E: -62.766918 | E_var:     4.5809 E_err:   0.033442 | NF_loss: 24.430141
[2025-11-13 03:34:40] 167:17<245:35, 8.02s/it | [Iter 1252/3090] R0[1162/3000] | LR: 0.016845 | E: -62.747238 | E_var:     4.6535 E_err:   0.033706 | NF_loss: 23.379494
[2025-11-13 03:34:48] 167:25<245:27, 8.02s/it | [Iter 1253/3090] R0[1163/3000] | LR: 0.016833 | E: -62.768084 | E_var:     4.4254 E_err:   0.032870 | NF_loss: 33.009783
[2025-11-13 03:34:56] 167:33<245:19, 8.02s/it | [Iter 1254/3090] R0[1164/3000] | LR: 0.016820 | E: -62.759525 | E_var:     4.5577 E_err:   0.033357 | NF_loss: 24.999621
[2025-11-13 03:35:04] 167:41<245:10, 8.02s/it | [Iter 1255/3090] R0[1165/3000] | LR: 0.016808 | E: -62.766028 | E_var:     4.3523 E_err:   0.032597 | NF_loss: 23.979376
[2025-11-13 03:35:12] 167:49<245:02, 8.02s/it | [Iter 1256/3090] R0[1166/3000] | LR: 0.016796 | E: -62.796700 | E_var:     4.5960 E_err:   0.033497 | NF_loss: 25.937593
[2025-11-13 03:35:20] 167:56<244:54, 8.02s/it | [Iter 1257/3090] R0[1167/3000] | LR: 0.016783 | E: -62.826716 | E_var:     4.3781 E_err:   0.032694 | NF_loss: 39.479421
[2025-11-13 03:35:28] 168:04<244:46, 8.02s/it | [Iter 1258/3090] R0[1168/3000] | LR: 0.016771 | E: -62.792588 | E_var:     4.2408 E_err:   0.032177 | NF_loss: 18.238438
[2025-11-13 03:35:36] 168:12<244:37, 8.02s/it | [Iter 1259/3090] R0[1169/3000] | LR: 0.016759 | E: -62.746324 | E_var:     4.3767 E_err:   0.032688 | NF_loss: 35.024911
[2025-11-13 03:35:43] 168:20<244:29, 8.02s/it | [Iter 1260/3090] R0[1170/3000] | LR: 0.016747 | E: -62.666959 | E_var:     4.3441 E_err:   0.032567 | NF_loss: 20.437575
[2025-11-13 03:35:51] 168:28<244:21, 8.02s/it | [Iter 1261/3090] R0[1171/3000] | LR: 0.016734 | E: -62.620453 | E_var:     4.6947 E_err:   0.033855 | NF_loss: 27.463920
[2025-11-13 03:35:59] 168:36<244:13, 8.02s/it | [Iter 1262/3090] R0[1172/3000] | LR: 0.016722 | E: -62.676377 | E_var:     4.3222 E_err:   0.032484 | NF_loss: 26.763560
[2025-11-13 03:36:07] 168:44<244:05, 8.02s/it | [Iter 1263/3090] R0[1173/3000] | LR: 0.016710 | E: -62.633410 | E_var:     4.6824 E_err:   0.033811 | NF_loss: 36.189728
[2025-11-13 03:36:15] 168:52<243:57, 8.02s/it | [Iter 1264/3090] R0[1174/3000] | LR: 0.016697 | E: -62.663400 | E_var:     4.6589 E_err:   0.033726 | NF_loss: 26.478964
[2025-11-13 03:36:23] 169:00<243:49, 8.02s/it | [Iter 1265/3090] R0[1175/3000] | LR: 0.016685 | E: -62.758518 | E_var:     4.5055 E_err:   0.033166 | NF_loss: 18.555549
[2025-11-13 03:36:31] 169:08<243:40, 8.02s/it | [Iter 1266/3090] R0[1176/3000] | LR: 0.016673 | E: -62.709500 | E_var:     4.4932 E_err:   0.033120 | NF_loss: 26.569203
[2025-11-13 03:36:39] 169:15<243:32, 8.02s/it | [Iter 1267/3090] R0[1177/3000] | LR: 0.016660 | E: -62.690233 | E_var:     4.4439 E_err:   0.032938 | NF_loss: 31.217996
[2025-11-13 03:36:47] 169:23<243:24, 8.02s/it | [Iter 1268/3090] R0[1178/3000] | LR: 0.016648 | E: -62.756692 | E_var:     4.5305 E_err:   0.033258 | NF_loss: 24.743101
[2025-11-13 03:36:55] 169:31<243:16, 8.02s/it | [Iter 1269/3090] R0[1179/3000] | LR: 0.016636 | E: -62.632580 | E_var:     4.4254 E_err:   0.032870 | NF_loss: 28.869868
[2025-11-13 03:37:02] 169:39<243:07, 8.02s/it | [Iter 1270/3090] R0[1180/3000] | LR: 0.016623 | E: -62.760662 | E_var:     4.4350 E_err:   0.032905 | NF_loss: 41.098239
[2025-11-13 03:37:10] 169:47<242:59, 8.02s/it | [Iter 1271/3090] R0[1181/3000] | LR: 0.016611 | E: -62.801369 | E_var:     4.5179 E_err:   0.033211 | NF_loss: 18.763997
[2025-11-13 03:37:18] 169:55<242:51, 8.02s/it | [Iter 1272/3090] R0[1182/3000] | LR: 0.016599 | E: -62.722927 | E_var:     4.5102 E_err:   0.033183 | NF_loss: 18.515276
[2025-11-13 03:37:26] 170:03<242:43, 8.02s/it | [Iter 1273/3090] R0[1183/3000] | LR: 0.016586 | E: -62.686324 | E_var:     4.3360 E_err:   0.032536 | NF_loss: 21.528138
[2025-11-13 03:37:34] 170:11<242:35, 8.01s/it | [Iter 1274/3090] R0[1184/3000] | LR: 0.016574 | E: -62.713615 | E_var:     4.3312 E_err:   0.032518 | NF_loss: 25.528403
[2025-11-13 03:37:42] 170:18<242:26, 8.01s/it | [Iter 1275/3090] R0[1185/3000] | LR: 0.016561 | E: -62.747934 | E_var:     4.4960 E_err:   0.033131 | NF_loss: 32.009558
[2025-11-13 03:37:50] 170:26<242:18, 8.01s/it | [Iter 1276/3090] R0[1186/3000] | LR: 0.016549 | E: -62.482471 | E_var:     4.5250 E_err:   0.033238 | NF_loss: 27.766483
[2025-11-13 03:37:58] 170:34<242:10, 8.01s/it | [Iter 1277/3090] R0[1187/3000] | LR: 0.016537 | E: -62.512059 | E_var:     4.6970 E_err:   0.033864 | NF_loss: 26.444101
[2025-11-13 03:38:06] 170:42<242:02, 8.01s/it | [Iter 1278/3090] R0[1188/3000] | LR: 0.016524 | E: -62.506247 | E_var:     4.3854 E_err:   0.032721 | NF_loss: 52.475499
[2025-11-13 03:38:13] 170:50<241:54, 8.01s/it | [Iter 1279/3090] R0[1189/3000] | LR: 0.016512 | E: -62.596032 | E_var:     4.5514 E_err:   0.033335 | NF_loss: 25.674428
[2025-11-13 03:38:21] 170:58<241:45, 8.01s/it | [Iter 1280/3090] R0[1190/3000] | LR: 0.016499 | E: -62.623071 | E_var:     4.3865 E_err:   0.032725 | NF_loss: 29.503720
[2025-11-13 03:38:29] 171:06<241:37, 8.01s/it | [Iter 1281/3090] R0[1191/3000] | LR: 0.016487 | E: -62.704551 | E_var:     4.1174 E_err:   0.031705 | NF_loss: 25.659679
[2025-11-13 03:38:37] 171:14<241:29, 8.01s/it | [Iter 1282/3090] R0[1192/3000] | LR: 0.016475 | E: -62.671602 | E_var:     4.3302 E_err:   0.032514 | NF_loss: 16.421683
[2025-11-13 03:38:45] 171:21<241:21, 8.01s/it | [Iter 1283/3090] R0[1193/3000] | LR: 0.016462 | E: -62.656918 | E_var:     4.5360 E_err:   0.033278 | NF_loss: 29.446802
[2025-11-13 03:38:53] 171:29<241:13, 8.01s/it | [Iter 1284/3090] R0[1194/3000] | LR: 0.016450 | E: -62.674058 | E_var:     4.3814 E_err:   0.032706 | NF_loss: 34.993978
[2025-11-13 03:39:01] 171:37<241:04, 8.01s/it | [Iter 1285/3090] R0[1195/3000] | LR: 0.016437 | E: -62.676288 | E_var:     4.3165 E_err:   0.032463 | NF_loss: 24.919985
[2025-11-13 03:39:09] 171:45<240:56, 8.01s/it | [Iter 1286/3090] R0[1196/3000] | LR: 0.016425 | E: -62.651301 | E_var:     4.3870 E_err:   0.032727 | NF_loss: 22.706980
[2025-11-13 03:39:16] 171:53<240:48, 8.01s/it | [Iter 1287/3090] R0[1197/3000] | LR: 0.016413 | E: -62.530599 | E_var:     4.4079 E_err:   0.032805 | NF_loss: 23.397946
[2025-11-13 03:39:24] 172:01<240:40, 8.01s/it | [Iter 1288/3090] R0[1198/3000] | LR: 0.016400 | E: -62.596203 | E_var:     4.3920 E_err:   0.032745 | NF_loss: 29.872949
[2025-11-13 03:39:32] 172:09<240:32, 8.01s/it | [Iter 1289/3090] R0[1199/3000] | LR: 0.016388 | E: -62.690659 | E_var:     4.3432 E_err:   0.032563 | NF_loss: 20.753412
[2025-11-13 03:39:40] 172:17<240:23, 8.01s/it | [Iter 1290/3090] R0[1200/3000] | LR: 0.016375 | E: -62.721209 | E_var:     4.3455 E_err:   0.032572 | NF_loss: 22.233328
[2025-11-13 03:39:48] 172:24<240:15, 8.01s/it | [Iter 1291/3090] R0[1201/3000] | LR: 0.016363 | E: -62.728933 | E_var:     4.3290 E_err:   0.032510 | NF_loss: 22.699649
[2025-11-13 03:39:56] 172:32<240:07, 8.01s/it | [Iter 1292/3090] R0[1202/3000] | LR: 0.016350 | E: -62.735572 | E_var:     4.2852 E_err:   0.032345 | NF_loss: 24.757056
[2025-11-13 03:40:04] 172:40<239:59, 8.01s/it | [Iter 1293/3090] R0[1203/3000] | LR: 0.016338 | E: -62.675256 | E_var:     4.1512 E_err:   0.031835 | NF_loss: 23.125357
[2025-11-13 03:40:12] 172:48<239:51, 8.01s/it | [Iter 1294/3090] R0[1204/3000] | LR: 0.016325 | E: -62.750008 | E_var:     4.3077 E_err:   0.032430 | NF_loss: 35.486937
[2025-11-13 03:40:19] 172:56<239:42, 8.01s/it | [Iter 1295/3090] R0[1205/3000] | LR: 0.016313 | E: -62.750243 | E_var:     4.3025 E_err:   0.032410 | NF_loss: 25.218311
[2025-11-13 03:40:28] 173:04<239:34, 8.01s/it | [Iter 1296/3090] R0[1206/3000] | LR: 0.016300 | E: -62.713327 | E_var:     4.3708 E_err:   0.032666 | NF_loss: 25.939367
[2025-11-13 03:40:35] 173:12<239:26, 8.01s/it | [Iter 1297/3090] R0[1207/3000] | LR: 0.016288 | E: -62.683018 | E_var:     4.3149 E_err:   0.032457 | NF_loss: 26.703413
[2025-11-13 03:40:43] 173:20<239:18, 8.01s/it | [Iter 1298/3090] R0[1208/3000] | LR: 0.016275 | E: -62.745679 | E_var:     4.3428 E_err:   0.032562 | NF_loss: 20.651431
[2025-11-13 03:40:51] 173:28<239:10, 8.01s/it | [Iter 1299/3090] R0[1209/3000] | LR: 0.016263 | E: -62.689882 | E_var:     4.3723 E_err:   0.032672 | NF_loss: 24.920942
[2025-11-13 03:40:59] 173:36<239:02, 8.01s/it | [Iter 1300/3090] R0[1210/3000] | LR: 0.016251 | E: -62.716108 | E_var:     4.2722 E_err:   0.032296 | NF_loss: 25.714922
[2025-11-13 03:41:07] 173:43<238:53, 8.01s/it | [Iter 1301/3090] R0[1211/3000] | LR: 0.016238 | E: -62.667752 | E_var:     4.4717 E_err:   0.033041 | NF_loss: 24.552587
[2025-11-13 03:41:15] 173:51<238:45, 8.01s/it | [Iter 1302/3090] R0[1212/3000] | LR: 0.016226 | E: -62.681066 | E_var:     4.5110 E_err:   0.033186 | NF_loss: 18.747334
[2025-11-13 03:41:23] 173:59<238:37, 8.01s/it | [Iter 1303/3090] R0[1213/3000] | LR: 0.016213 | E: -62.627288 | E_var:     4.3808 E_err:   0.032704 | NF_loss: 21.629568
[2025-11-13 03:41:31] 174:07<238:29, 8.01s/it | [Iter 1304/3090] R0[1214/3000] | LR: 0.016201 | E: -62.583337 | E_var:     4.3299 E_err:   0.032513 | NF_loss: 21.867960
[2025-11-13 03:41:38] 174:15<238:21, 8.01s/it | [Iter 1305/3090] R0[1215/3000] | LR: 0.016188 | E: -62.667598 | E_var:     4.2160 E_err:   0.032083 | NF_loss: 19.762039
[2025-11-13 03:41:46] 174:23<238:12, 8.01s/it | [Iter 1306/3090] R0[1216/3000] | LR: 0.016176 | E: -62.598735 | E_var:     4.3308 E_err:   0.032517 | NF_loss: 28.470527
[2025-11-13 03:41:54] 174:31<238:04, 8.01s/it | [Iter 1307/3090] R0[1217/3000] | LR: 0.016163 | E: -62.610723 | E_var:     4.2836 E_err:   0.032339 | NF_loss: 15.257839
[2025-11-13 03:42:02] 174:39<237:56, 8.01s/it | [Iter 1308/3090] R0[1218/3000] | LR: 0.016151 | E: -62.656681 | E_var:     4.2210 E_err:   0.032102 | NF_loss: 17.492819
[2025-11-13 03:42:10] 174:46<237:48, 8.01s/it | [Iter 1309/3090] R0[1219/3000] | LR: 0.016138 | E: -62.708479 | E_var:     4.3142 E_err:   0.032454 | NF_loss: 14.384607
[2025-11-13 03:42:18] 174:54<237:40, 8.01s/it | [Iter 1310/3090] R0[1220/3000] | LR: 0.016125 | E: -62.806425 | E_var:     4.1806 E_err:   0.031948 | NF_loss: 13.970467
[2025-11-13 03:42:26] 175:02<237:31, 8.01s/it | [Iter 1311/3090] R0[1221/3000] | LR: 0.016113 | E: -62.744586 | E_var:     4.2835 E_err:   0.032339 | NF_loss: 18.248701
[2025-11-13 03:42:34] 175:10<237:23, 8.01s/it | [Iter 1312/3090] R0[1222/3000] | LR: 0.016100 | E: -62.817051 | E_var:     4.0440 E_err:   0.031422 | NF_loss: 18.474497
[2025-11-13 03:42:41] 175:18<237:15, 8.01s/it | [Iter 1313/3090] R0[1223/3000] | LR: 0.016088 | E: -62.791534 | E_var:     4.3075 E_err:   0.032429 | NF_loss: 20.259729
[2025-11-13 03:42:49] 175:26<237:07, 8.01s/it | [Iter 1314/3090] R0[1224/3000] | LR: 0.016075 | E: -62.727424 | E_var:     4.1709 E_err:   0.031911 | NF_loss: 21.459624
[2025-11-13 03:42:57] 175:34<236:59, 8.01s/it | [Iter 1315/3090] R0[1225/3000] | LR: 0.016063 | E: -62.779821 | E_var:     4.5375 E_err:   0.033283 | NF_loss: 14.695923
[2025-11-13 03:43:05] 175:42<236:51, 8.01s/it | [Iter 1316/3090] R0[1226/3000] | LR: 0.016050 | E: -62.806817 | E_var:     4.3999 E_err:   0.032775 | NF_loss: 10.393027
[2025-11-13 03:43:13] 175:49<236:42, 8.01s/it | [Iter 1317/3090] R0[1227/3000] | LR: 0.016038 | E: -62.778349 | E_var:     4.3462 E_err:   0.032574 | NF_loss: 19.955305
[2025-11-13 03:43:21] 175:57<236:34, 8.01s/it | [Iter 1318/3090] R0[1228/3000] | LR: 0.016025 | E: -62.766464 | E_var:     4.3690 E_err:   0.032659 | NF_loss: 14.212985
[2025-11-13 03:43:29] 176:05<236:26, 8.01s/it | [Iter 1319/3090] R0[1229/3000] | LR: 0.016013 | E: -62.797979 | E_var:     4.4145 E_err:   0.032829 | NF_loss: 13.034927
[2025-11-13 03:43:37] 176:13<236:18, 8.01s/it | [Iter 1320/3090] R0[1230/3000] | LR: 0.016000 | E: -62.818070 | E_var:     7.7357 E_err:   0.043458 | NF_loss: 22.590465
[2025-11-13 03:43:44] 176:21<236:10, 8.01s/it | [Iter 1321/3090] R0[1231/3000] | LR: 0.015987 | E: -62.790815 | E_var:     4.5565 E_err:   0.033353 | NF_loss: 18.550446
[2025-11-13 03:43:52] 176:29<236:01, 8.01s/it | [Iter 1322/3090] R0[1232/3000] | LR: 0.015975 | E: -62.774518 | E_var:     4.7251 E_err:   0.033964 | NF_loss: 21.574219
[2025-11-13 03:44:00] 176:37<235:53, 8.01s/it | [Iter 1323/3090] R0[1233/3000] | LR: 0.015962 | E: -62.746291 | E_var:     4.5298 E_err:   0.033255 | NF_loss: 13.954269
[2025-11-13 03:44:08] 176:45<235:45, 8.01s/it | [Iter 1324/3090] R0[1234/3000] | LR: 0.015950 | E: -62.805428 | E_var:     4.3959 E_err:   0.032760 | NF_loss: 8.386158
[2025-11-13 03:44:16] 176:52<235:37, 8.01s/it | [Iter 1325/3090] R0[1235/3000] | LR: 0.015937 | E: -62.799556 | E_var:     4.2850 E_err:   0.032344 | NF_loss: 16.172319
[2025-11-13 03:44:24] 177:00<235:29, 8.01s/it | [Iter 1326/3090] R0[1236/3000] | LR: 0.015925 | E: -62.736944 | E_var:     4.3113 E_err:   0.032443 | NF_loss: 13.500909
[2025-11-13 03:44:32] 177:08<235:20, 8.01s/it | [Iter 1327/3090] R0[1237/3000] | LR: 0.015912 | E: -62.778234 | E_var:     4.5435 E_err:   0.033305 | NF_loss: 20.517097
[2025-11-13 03:44:40] 177:16<235:12, 8.01s/it | [Iter 1328/3090] R0[1238/3000] | LR: 0.015899 | E: -62.742879 | E_var:     4.4953 E_err:   0.033128 | NF_loss: 17.330528
[2025-11-13 03:44:47] 177:24<235:04, 8.01s/it | [Iter 1329/3090] R0[1239/3000] | LR: 0.015887 | E: -62.737664 | E_var:     4.4560 E_err:   0.032983 | NF_loss: 25.134008
[2025-11-13 03:44:55] 177:32<234:56, 8.01s/it | [Iter 1330/3090] R0[1240/3000] | LR: 0.015874 | E: -62.798909 | E_var:     4.6310 E_err:   0.033625 | NF_loss: 24.276170
[2025-11-13 03:45:03] 177:40<234:48, 8.01s/it | [Iter 1331/3090] R0[1241/3000] | LR: 0.015862 | E: -62.786060 | E_var:     4.1372 E_err:   0.031782 | NF_loss: 25.266182
[2025-11-13 03:45:11] 177:48<234:39, 8.01s/it | [Iter 1332/3090] R0[1242/3000] | LR: 0.015849 | E: -62.783125 | E_var:     4.3569 E_err:   0.032614 | NF_loss: 22.722062
[2025-11-13 03:45:19] 177:55<234:31, 8.01s/it | [Iter 1333/3090] R0[1243/3000] | LR: 0.015836 | E: -62.770310 | E_var:     4.3722 E_err:   0.032672 | NF_loss: 24.420203
[2025-11-13 03:45:27] 178:03<234:23, 8.01s/it | [Iter 1334/3090] R0[1244/3000] | LR: 0.015824 | E: -62.793517 | E_var:     4.7010 E_err:   0.033878 | NF_loss: 28.277867
[2025-11-13 03:45:35] 178:11<234:15, 8.01s/it | [Iter 1335/3090] R0[1245/3000] | LR: 0.015811 | E: -62.783741 | E_var:     4.3100 E_err:   0.032438 | NF_loss: 25.974769
[2025-11-13 03:45:43] 178:19<234:07, 8.01s/it | [Iter 1336/3090] R0[1246/3000] | LR: 0.015798 | E: -62.794621 | E_var:     4.2640 E_err:   0.032265 | NF_loss: 22.814690
[2025-11-13 03:45:50] 178:27<233:59, 8.01s/it | [Iter 1337/3090] R0[1247/3000] | LR: 0.015786 | E: -62.828555 | E_var:     4.4866 E_err:   0.033096 | NF_loss: 20.992480
[2025-11-13 03:45:58] 178:35<233:50, 8.01s/it | [Iter 1338/3090] R0[1248/3000] | LR: 0.015773 | E: -62.802335 | E_var:     4.2035 E_err:   0.032035 | NF_loss: 18.304108
[2025-11-13 03:46:06] 178:43<233:42, 8.01s/it | [Iter 1339/3090] R0[1249/3000] | LR: 0.015761 | E: -62.813206 | E_var:     4.1972 E_err:   0.032011 | NF_loss: 23.511629
[2025-11-13 03:46:14] 178:51<233:34, 8.01s/it | [Iter 1340/3090] R0[1250/3000] | LR: 0.015748 | E: -62.814818 | E_var:     4.2949 E_err:   0.032382 | NF_loss: 20.266490
[2025-11-13 03:46:22] 178:59<233:26, 8.01s/it | [Iter 1341/3090] R0[1251/3000] | LR: 0.015735 | E: -62.770261 | E_var:     4.9337 E_err:   0.034706 | NF_loss: 19.461764
[2025-11-13 03:46:30] 179:07<233:18, 8.01s/it | [Iter 1342/3090] R0[1252/3000] | LR: 0.015723 | E: -62.655963 | E_var:     4.3808 E_err:   0.032704 | NF_loss: 19.990431
[2025-11-13 03:46:38] 179:15<233:10, 8.01s/it | [Iter 1343/3090] R0[1253/3000] | LR: 0.015710 | E: -62.602629 | E_var:     4.3055 E_err:   0.032421 | NF_loss: 16.982949
[2025-11-13 03:46:46] 179:22<233:02, 8.01s/it | [Iter 1344/3090] R0[1254/3000] | LR: 0.015697 | E: -62.674969 | E_var:     4.5107 E_err:   0.033185 | NF_loss: 20.795969
[2025-11-13 03:46:54] 179:30<232:53, 8.01s/it | [Iter 1345/3090] R0[1255/3000] | LR: 0.015685 | E: -62.674529 | E_var:     4.4000 E_err:   0.032775 | NF_loss: 21.925364
[2025-11-13 03:47:02] 179:38<232:45, 8.01s/it | [Iter 1346/3090] R0[1256/3000] | LR: 0.015672 | E: -62.584586 | E_var:     4.4361 E_err:   0.032909 | NF_loss: 22.294751
[2025-11-13 03:47:09] 179:46<232:37, 8.01s/it | [Iter 1347/3090] R0[1257/3000] | LR: 0.015659 | E: -62.705605 | E_var:     4.2244 E_err:   0.032115 | NF_loss: 20.321289
[2025-11-13 03:47:17] 179:54<232:29, 8.01s/it | [Iter 1348/3090] R0[1258/3000] | LR: 0.015647 | E: -62.751188 | E_var:     4.3735 E_err:   0.032676 | NF_loss: 18.116932
[2025-11-13 03:47:25] 180:02<232:21, 8.01s/it | [Iter 1349/3090] R0[1259/3000] | LR: 0.015634 | E: -62.705810 | E_var:     4.3332 E_err:   0.032526 | NF_loss: 20.238988
[2025-11-13 03:47:33] 180:10<232:13, 8.01s/it | [Iter 1350/3090] R0[1260/3000] | LR: 0.015621 | E: -62.737911 | E_var:     4.3694 E_err:   0.032661 | NF_loss: 22.090384
[2025-11-13 03:47:41] 180:18<232:04, 8.01s/it | [Iter 1351/3090] R0[1261/3000] | LR: 0.015609 | E: -62.727659 | E_var:     4.3448 E_err:   0.032569 | NF_loss: 26.677805
[2025-11-13 03:47:49] 180:25<231:56, 8.01s/it | [Iter 1352/3090] R0[1262/3000] | LR: 0.015596 | E: -62.740081 | E_var:     4.3331 E_err:   0.032525 | NF_loss: 18.110316
[2025-11-13 03:47:57] 180:33<231:48, 8.01s/it | [Iter 1353/3090] R0[1263/3000] | LR: 0.015583 | E: -62.719863 | E_var:     4.3300 E_err:   0.032513 | NF_loss: 16.419713
[2025-11-13 03:48:05] 180:41<231:40, 8.01s/it | [Iter 1354/3090] R0[1264/3000] | LR: 0.015571 | E: -62.725853 | E_var:     4.2135 E_err:   0.032073 | NF_loss: 17.422254
[2025-11-13 03:48:13] 180:49<231:32, 8.01s/it | [Iter 1355/3090] R0[1265/3000] | LR: 0.015558 | E: -62.714169 | E_var:     4.4585 E_err:   0.032992 | NF_loss: 15.553312
[2025-11-13 03:48:20] 180:57<231:24, 8.01s/it | [Iter 1356/3090] R0[1266/3000] | LR: 0.015545 | E: -62.718702 | E_var:     4.4867 E_err:   0.033097 | NF_loss: 17.706894
[2025-11-13 03:48:28] 181:05<231:15, 8.01s/it | [Iter 1357/3090] R0[1267/3000] | LR: 0.015533 | E: -62.649495 | E_var:     4.4748 E_err:   0.033053 | NF_loss: 12.972897
[2025-11-13 03:48:36] 181:13<231:07, 8.01s/it | [Iter 1358/3090] R0[1268/3000] | LR: 0.015520 | E: -62.708243 | E_var:     4.0924 E_err:   0.031609 | NF_loss: 22.158833
[2025-11-13 03:48:44] 181:21<230:59, 8.01s/it | [Iter 1359/3090] R0[1269/3000] | LR: 0.015507 | E: -62.795834 | E_var:     4.1622 E_err:   0.031877 | NF_loss: 26.359174
[2025-11-13 03:48:52] 181:28<230:51, 8.01s/it | [Iter 1360/3090] R0[1270/3000] | LR: 0.015494 | E: -62.835144 | E_var:     4.1199 E_err:   0.031715 | NF_loss: 18.368896
[2025-11-13 03:49:00] 181:36<230:43, 8.01s/it | [Iter 1361/3090] R0[1271/3000] | LR: 0.015482 | E: -62.831878 | E_var:     4.2077 E_err:   0.032051 | NF_loss: 18.683382
[2025-11-13 03:49:08] 181:44<230:35, 8.01s/it | [Iter 1362/3090] R0[1272/3000] | LR: 0.015469 | E: -62.788422 | E_var:     4.3967 E_err:   0.032763 | NF_loss: 15.515185
[2025-11-13 03:49:16] 181:52<230:26, 8.01s/it | [Iter 1363/3090] R0[1273/3000] | LR: 0.015456 | E: -62.821735 | E_var:     4.1950 E_err:   0.032003 | NF_loss: 20.627282
[2025-11-13 03:49:23] 182:00<230:18, 8.01s/it | [Iter 1364/3090] R0[1274/3000] | LR: 0.015444 | E: -62.725728 | E_var:     4.0540 E_err:   0.031460 | NF_loss: 16.407182
[2025-11-13 03:49:31] 182:08<230:10, 8.01s/it | [Iter 1365/3090] R0[1275/3000] | LR: 0.015431 | E: -62.755781 | E_var:     4.2135 E_err:   0.032073 | NF_loss: 19.313158
[2025-11-13 03:49:39] 182:16<230:02, 8.01s/it | [Iter 1366/3090] R0[1276/3000] | LR: 0.015418 | E: -62.734223 | E_var:     4.2078 E_err:   0.032052 | NF_loss: 14.616854
[2025-11-13 03:49:47] 182:24<229:54, 8.01s/it | [Iter 1367/3090] R0[1277/3000] | LR: 0.015405 | E: -62.717316 | E_var:     4.1786 E_err:   0.031940 | NF_loss: 19.911234
[2025-11-13 03:49:55] 182:31<229:46, 8.01s/it | [Iter 1368/3090] R0[1278/3000] | LR: 0.015393 | E: -62.707640 | E_var:     4.3565 E_err:   0.032613 | NF_loss: 13.942695
[2025-11-13 03:50:03] 182:39<229:37, 8.01s/it | [Iter 1369/3090] R0[1279/3000] | LR: 0.015380 | E: -62.801083 | E_var:     4.1432 E_err:   0.031805 | NF_loss: 20.287277
[2025-11-13 03:50:11] 182:47<229:29, 8.01s/it | [Iter 1370/3090] R0[1280/3000] | LR: 0.015367 | E: -62.711340 | E_var:     4.2257 E_err:   0.032119 | NF_loss: 14.533414
[2025-11-13 03:50:19] 182:55<229:21, 8.01s/it | [Iter 1371/3090] R0[1281/3000] | LR: 0.015354 | E: -62.760764 | E_var:     4.1133 E_err:   0.031689 | NF_loss: 25.529306
[2025-11-13 03:50:26] 183:03<229:13, 8.01s/it | [Iter 1372/3090] R0[1282/3000] | LR: 0.015342 | E: -62.761957 | E_var:     4.2107 E_err:   0.032063 | NF_loss: 17.241383
[2025-11-13 03:50:34] 183:11<229:05, 8.01s/it | [Iter 1373/3090] R0[1283/3000] | LR: 0.015329 | E: -62.773490 | E_var:     4.1089 E_err:   0.031672 | NF_loss: 19.648228
[2025-11-13 03:50:42] 183:19<228:57, 8.01s/it | [Iter 1374/3090] R0[1284/3000] | LR: 0.015316 | E: -62.801714 | E_var:     4.1799 E_err:   0.031945 | NF_loss: 15.339163
[2025-11-13 03:50:50] 183:27<228:48, 8.01s/it | [Iter 1375/3090] R0[1285/3000] | LR: 0.015303 | E: -62.742332 | E_var:     4.2605 E_err:   0.032252 | NF_loss: 20.509122
[2025-11-13 03:50:58] 183:34<228:40, 8.01s/it | [Iter 1376/3090] R0[1286/3000] | LR: 0.015291 | E: -62.695000 | E_var:     4.1376 E_err:   0.031783 | NF_loss: 23.213741
[2025-11-13 03:51:06] 183:43<228:32, 8.01s/it | [Iter 1377/3090] R0[1287/3000] | LR: 0.015278 | E: -62.710271 | E_var:     4.0816 E_err:   0.031567 | NF_loss: 15.141466
[2025-11-13 03:51:14] 183:50<228:24, 8.01s/it | [Iter 1378/3090] R0[1288/3000] | LR: 0.015265 | E: -62.730258 | E_var:     4.1183 E_err:   0.031709 | NF_loss: 12.803028
[2025-11-13 03:51:22] 183:58<228:16, 8.00s/it | [Iter 1379/3090] R0[1289/3000] | LR: 0.015252 | E: -62.752521 | E_var:     4.2545 E_err:   0.032229 | NF_loss: 8.150458
[2025-11-13 03:51:30] 184:06<228:08, 8.00s/it | [Iter 1380/3090] R0[1290/3000] | LR: 0.015240 | E: -62.764721 | E_var:     4.0930 E_err:   0.031611 | NF_loss: 15.855815
[2025-11-13 03:51:38] 184:14<228:00, 8.00s/it | [Iter 1381/3090] R0[1291/3000] | LR: 0.015227 | E: -62.727968 | E_var:     4.1660 E_err:   0.031892 | NF_loss: 18.285577
[2025-11-13 03:51:45] 184:22<227:52, 8.00s/it | [Iter 1382/3090] R0[1292/3000] | LR: 0.015214 | E: -62.723411 | E_var:     4.1377 E_err:   0.031783 | NF_loss: 22.178421
[2025-11-13 03:51:53] 184:30<227:43, 8.00s/it | [Iter 1383/3090] R0[1293/3000] | LR: 0.015201 | E: -62.619335 | E_var:     4.1818 E_err:   0.031952 | NF_loss: 15.248039
[2025-11-13 03:52:01] 184:38<227:35, 8.00s/it | [Iter 1384/3090] R0[1294/3000] | LR: 0.015188 | E: -62.685416 | E_var:     4.2199 E_err:   0.032098 | NF_loss: 19.055478
[2025-11-13 03:52:09] 184:46<227:27, 8.00s/it | [Iter 1385/3090] R0[1295/3000] | LR: 0.015176 | E: -62.591372 | E_var:     4.4676 E_err:   0.033026 | NF_loss: 13.840617
[2025-11-13 03:52:17] 184:53<227:19, 8.00s/it | [Iter 1386/3090] R0[1296/3000] | LR: 0.015163 | E: -62.587364 | E_var:     4.2771 E_err:   0.032314 | NF_loss: 19.737323
[2025-11-13 03:52:25] 185:01<227:11, 8.00s/it | [Iter 1387/3090] R0[1297/3000] | LR: 0.015150 | E: -62.527706 | E_var:     4.3891 E_err:   0.032735 | NF_loss: 18.315776
[2025-11-13 03:52:33] 185:09<227:03, 8.00s/it | [Iter 1388/3090] R0[1298/3000] | LR: 0.015137 | E: -62.439922 | E_var:     4.5783 E_err:   0.033433 | NF_loss: 18.581505
[2025-11-13 03:52:41] 185:17<226:54, 8.00s/it | [Iter 1389/3090] R0[1299/3000] | LR: 0.015125 | E: -62.585755 | E_var:     4.3179 E_err:   0.032468 | NF_loss: 16.420283
[2025-11-13 03:52:48] 185:25<226:46, 8.00s/it | [Iter 1390/3090] R0[1300/3000] | LR: 0.015112 | E: -62.563341 | E_var:     4.4431 E_err:   0.032935 | NF_loss: 15.536686
[2025-11-13 03:52:56] 185:33<226:38, 8.00s/it | [Iter 1391/3090] R0[1301/3000] | LR: 0.015099 | E: -62.440731 | E_var:     4.3948 E_err:   0.032756 | NF_loss: 16.591041
[2025-11-13 03:53:04] 185:41<226:30, 8.00s/it | [Iter 1392/3090] R0[1302/3000] | LR: 0.015086 | E: -62.312549 | E_var:     4.4988 E_err:   0.033141 | NF_loss: 19.443738
[2025-11-13 03:53:12] 185:49<226:22, 8.00s/it | [Iter 1393/3090] R0[1303/3000] | LR: 0.015073 | E: -62.370986 | E_var:     4.4789 E_err:   0.033068 | NF_loss: 18.676656
[2025-11-13 03:53:20] 185:56<226:14, 8.00s/it | [Iter 1394/3090] R0[1304/3000] | LR: 0.015061 | E: -62.485667 | E_var:     4.5442 E_err:   0.033308 | NF_loss: 19.963555
[2025-11-13 03:53:28] 186:04<226:05, 8.00s/it | [Iter 1395/3090] R0[1305/3000] | LR: 0.015048 | E: -62.520190 | E_var:     4.3748 E_err:   0.032681 | NF_loss: 13.697984
[2025-11-13 03:53:36] 186:12<225:57, 8.00s/it | [Iter 1396/3090] R0[1306/3000] | LR: 0.015035 | E: -62.565248 | E_var:     4.2159 E_err:   0.032082 | NF_loss: 16.614374
[2025-11-13 03:53:44] 186:20<225:49, 8.00s/it | [Iter 1397/3090] R0[1307/3000] | LR: 0.015022 | E: -62.633425 | E_var:     4.4516 E_err:   0.032967 | NF_loss: 12.742129
[2025-11-13 03:53:51] 186:28<225:41, 8.00s/it | [Iter 1398/3090] R0[1308/3000] | LR: 0.015009 | E: -62.621830 | E_var:     4.1785 E_err:   0.031940 | NF_loss: 19.230357
[2025-11-13 03:53:59] 186:36<225:33, 8.00s/it | [Iter 1399/3090] R0[1309/3000] | LR: 0.014996 | E: -62.621512 | E_var:     4.3409 E_err:   0.032554 | NF_loss: 15.662838
[2025-11-13 03:54:07] 186:44<225:25, 8.00s/it | [Iter 1400/3090] R0[1310/3000] | LR: 0.014984 | E: -62.756878 | E_var:     4.3624 E_err:   0.032635 | NF_loss: 17.173692
[2025-11-13 03:54:15] 186:52<225:16, 8.00s/it | [Iter 1401/3090] R0[1311/3000] | LR: 0.014971 | E: -62.731591 | E_var:     3.9566 E_err:   0.031080 | NF_loss: 17.190775
[2025-11-13 03:54:23] 186:59<225:08, 8.00s/it | [Iter 1402/3090] R0[1312/3000] | LR: 0.014958 | E: -62.837829 | E_var:     4.3094 E_err:   0.032436 | NF_loss: 16.713973
[2025-11-13 03:54:31] 187:07<225:00, 8.00s/it | [Iter 1403/3090] R0[1313/3000] | LR: 0.014945 | E: -62.830350 | E_var:     4.1726 E_err:   0.031917 | NF_loss: 16.216780
[2025-11-13 03:54:39] 187:15<224:52, 8.00s/it | [Iter 1404/3090] R0[1314/3000] | LR: 0.014932 | E: -62.836200 | E_var:     4.5631 E_err:   0.033377 | NF_loss: 14.892548
[2025-11-13 03:54:47] 187:23<224:44, 8.00s/it | [Iter 1405/3090] R0[1315/3000] | LR: 0.014919 | E: -62.826521 | E_var:     4.8092 E_err:   0.034266 | NF_loss: 16.952879
[2025-11-13 03:54:55] 187:31<224:36, 8.00s/it | [Iter 1406/3090] R0[1316/3000] | LR: 0.014907 | E: -62.790226 | E_var:     4.5168 E_err:   0.033207 | NF_loss: 21.196528
[2025-11-13 03:55:03] 187:39<224:28, 8.00s/it | [Iter 1407/3090] R0[1317/3000] | LR: 0.014894 | E: -62.815449 | E_var:     4.7300 E_err:   0.033982 | NF_loss: 10.505096
[2025-11-13 03:55:10] 187:47<224:20, 8.00s/it | [Iter 1408/3090] R0[1318/3000] | LR: 0.014881 | E: -62.754062 | E_var:     5.0339 E_err:   0.035057 | NF_loss: 13.101705
[2025-11-13 03:55:18] 187:55<224:12, 8.00s/it | [Iter 1409/3090] R0[1319/3000] | LR: 0.014868 | E: -62.605547 | E_var:     5.6122 E_err:   0.037016 | NF_loss: 16.210690
[2025-11-13 03:55:26] 188:03<224:03, 8.00s/it | [Iter 1410/3090] R0[1320/3000] | LR: 0.014855 | E: -62.695808 | E_var:     5.1520 E_err:   0.035466 | NF_loss: 13.654413
[2025-11-13 03:55:34] 188:11<223:55, 8.00s/it | [Iter 1411/3090] R0[1321/3000] | LR: 0.014842 | E: -62.668622 | E_var:     5.5982 E_err:   0.036970 | NF_loss: 14.272965
[2025-11-13 03:55:42] 188:19<223:47, 8.00s/it | [Iter 1412/3090] R0[1322/3000] | LR: 0.014829 | E: -62.667726 | E_var:     4.7589 E_err:   0.034086 | NF_loss: 19.858363
[2025-11-13 03:55:50] 188:26<223:39, 8.00s/it | [Iter 1413/3090] R0[1323/3000] | LR: 0.014817 | E: -62.777252 | E_var:     4.4700 E_err:   0.033035 | NF_loss: 16.556036
[2025-11-13 03:55:58] 188:34<223:31, 8.00s/it | [Iter 1414/3090] R0[1324/3000] | LR: 0.014804 | E: -62.725308 | E_var:     4.6285 E_err:   0.033616 | NF_loss: 14.060837
[2025-11-13 03:56:06] 188:42<223:23, 8.00s/it | [Iter 1415/3090] R0[1325/3000] | LR: 0.014791 | E: -62.798897 | E_var:     4.4829 E_err:   0.033082 | NF_loss: 20.686984
[2025-11-13 03:56:14] 188:50<223:15, 8.00s/it | [Iter 1416/3090] R0[1326/3000] | LR: 0.014778 | E: -62.775220 | E_var:     4.2969 E_err:   0.032389 | NF_loss: 22.013382
[2025-11-13 03:56:21] 188:58<223:06, 8.00s/it | [Iter 1417/3090] R0[1327/3000] | LR: 0.014765 | E: -62.765496 | E_var:     4.4035 E_err:   0.032788 | NF_loss: 17.684850
[2025-11-13 03:56:29] 189:06<222:58, 8.00s/it | [Iter 1418/3090] R0[1328/3000] | LR: 0.014752 | E: -62.822840 | E_var:     4.4834 E_err:   0.033085 | NF_loss: 19.934176
[2025-11-13 03:56:37] 189:14<222:50, 8.00s/it | [Iter 1419/3090] R0[1329/3000] | LR: 0.014739 | E: -62.831414 | E_var:     4.3427 E_err:   0.032561 | NF_loss: 15.340037
[2025-11-13 03:56:45] 189:22<222:42, 8.00s/it | [Iter 1420/3090] R0[1330/3000] | LR: 0.014726 | E: -62.844855 | E_var:     4.2636 E_err:   0.032263 | NF_loss: 14.628985
[2025-11-13 03:56:53] 189:29<222:34, 8.00s/it | [Iter 1421/3090] R0[1331/3000] | LR: 0.014714 | E: -62.824792 | E_var:     4.6195 E_err:   0.033583 | NF_loss: 13.617734
[2025-11-13 03:57:01] 189:37<222:26, 8.00s/it | [Iter 1422/3090] R0[1332/3000] | LR: 0.014701 | E: -62.864465 | E_var:     4.6697 E_err:   0.033765 | NF_loss: 16.943900
[2025-11-13 03:57:09] 189:45<222:17, 8.00s/it | [Iter 1423/3090] R0[1333/3000] | LR: 0.014688 | E: -62.871004 | E_var:     4.3112 E_err:   0.032443 | NF_loss: 14.749805
[2025-11-13 03:57:17] 189:53<222:09, 8.00s/it | [Iter 1424/3090] R0[1334/3000] | LR: 0.014675 | E: -62.782002 | E_var:     4.5116 E_err:   0.033188 | NF_loss: 14.269343
[2025-11-13 03:57:24] 190:01<222:01, 8.00s/it | [Iter 1425/3090] R0[1335/3000] | LR: 0.014662 | E: -62.847570 | E_var:     4.3849 E_err:   0.032719 | NF_loss: 16.931363
[2025-11-13 03:57:32] 190:09<221:53, 8.00s/it | [Iter 1426/3090] R0[1336/3000] | LR: 0.014649 | E: -62.847991 | E_var:     4.7766 E_err:   0.034149 | NF_loss: 16.428770
[2025-11-13 03:57:40] 190:17<221:45, 8.00s/it | [Iter 1427/3090] R0[1337/3000] | LR: 0.014636 | E: -62.845682 | E_var:     4.6146 E_err:   0.033565 | NF_loss: 15.267206
[2025-11-13 03:57:48] 190:25<221:37, 8.00s/it | [Iter 1428/3090] R0[1338/3000] | LR: 0.014623 | E: -62.838818 | E_var:     4.0779 E_err:   0.031553 | NF_loss: 14.974217
[2025-11-13 03:57:56] 190:32<221:29, 8.00s/it | [Iter 1429/3090] R0[1339/3000] | LR: 0.014610 | E: -62.849930 | E_var:     4.3770 E_err:   0.032690 | NF_loss: 14.576973
[2025-11-13 03:58:04] 190:40<221:20, 8.00s/it | [Iter 1430/3090] R0[1340/3000] | LR: 0.014598 | E: -62.810862 | E_var:     4.2774 E_err:   0.032316 | NF_loss: 16.969335
[2025-11-13 03:58:12] 190:48<221:12, 8.00s/it | [Iter 1431/3090] R0[1341/3000] | LR: 0.014585 | E: -62.847251 | E_var:     4.3459 E_err:   0.032573 | NF_loss: 16.886661
[2025-11-13 03:58:20] 190:56<221:04, 8.00s/it | [Iter 1432/3090] R0[1342/3000] | LR: 0.014572 | E: -62.872787 | E_var:     4.1806 E_err:   0.031948 | NF_loss: 18.029109
[2025-11-13 03:58:27] 191:04<220:56, 8.00s/it | [Iter 1433/3090] R0[1343/3000] | LR: 0.014559 | E: -62.864049 | E_var:     4.2784 E_err:   0.032319 | NF_loss: 12.551935
[2025-11-13 03:58:35] 191:12<220:48, 8.00s/it | [Iter 1434/3090] R0[1344/3000] | LR: 0.014546 | E: -62.849851 | E_var:     4.3070 E_err:   0.032427 | NF_loss: 14.354247
[2025-11-13 03:58:43] 191:20<220:40, 8.00s/it | [Iter 1435/3090] R0[1345/3000] | LR: 0.014533 | E: -62.821149 | E_var:     4.3707 E_err:   0.032666 | NF_loss: 22.702291
[2025-11-13 03:58:51] 191:28<220:32, 8.00s/it | [Iter 1436/3090] R0[1346/3000] | LR: 0.014520 | E: -62.766045 | E_var:     4.2394 E_err:   0.032172 | NF_loss: 16.136464
[2025-11-13 03:58:59] 191:35<220:23, 8.00s/it | [Iter 1437/3090] R0[1347/3000] | LR: 0.014507 | E: -62.679671 | E_var:     4.3673 E_err:   0.032653 | NF_loss: 22.331108
[2025-11-13 03:59:07] 191:43<220:15, 8.00s/it | [Iter 1438/3090] R0[1348/3000] | LR: 0.014494 | E: -62.810089 | E_var:     4.2475 E_err:   0.032202 | NF_loss: 16.428290
[2025-11-13 03:59:15] 191:51<220:07, 8.00s/it | [Iter 1439/3090] R0[1349/3000] | LR: 0.014481 | E: -62.767018 | E_var:     4.2523 E_err:   0.032220 | NF_loss: 15.501574
[2025-11-13 03:59:23] 191:59<219:59, 8.00s/it | [Iter 1440/3090] R0[1350/3000] | LR: 0.014468 | E: -62.823556 | E_var:     4.1328 E_err:   0.031765 | NF_loss: 11.432135
[2025-11-13 03:59:30] 192:07<219:51, 8.00s/it | [Iter 1441/3090] R0[1351/3000] | LR: 0.014455 | E: -62.843748 | E_var:     4.1273 E_err:   0.031743 | NF_loss: 15.664788
[2025-11-13 03:59:38] 192:15<219:43, 8.00s/it | [Iter 1442/3090] R0[1352/3000] | LR: 0.014443 | E: -62.872307 | E_var:     4.1099 E_err:   0.031676 | NF_loss: 16.819424
[2025-11-13 03:59:46] 192:23<219:35, 8.00s/it | [Iter 1443/3090] R0[1353/3000] | LR: 0.014430 | E: -62.888313 | E_var:     4.3171 E_err:   0.032465 | NF_loss: 17.676051
[2025-11-13 03:59:54] 192:31<219:27, 8.00s/it | [Iter 1444/3090] R0[1354/3000] | LR: 0.014417 | E: -62.872917 | E_var:     4.5616 E_err:   0.033372 | NF_loss: 17.562524
[2025-11-13 04:00:02] 192:39<219:19, 8.00s/it | [Iter 1445/3090] R0[1355/3000] | LR: 0.014404 | E: -62.835815 | E_var:     4.4696 E_err:   0.033033 | NF_loss: 18.739802
[2025-11-13 04:00:10] 192:47<219:10, 8.00s/it | [Iter 1446/3090] R0[1356/3000] | LR: 0.014391 | E: -62.847735 | E_var:     4.5443 E_err:   0.033308 | NF_loss: 22.938146
[2025-11-13 04:00:18] 192:54<219:02, 8.00s/it | [Iter 1447/3090] R0[1357/3000] | LR: 0.014378 | E: -62.833071 | E_var:     4.4379 E_err:   0.032916 | NF_loss: 27.148083
[2025-11-13 04:00:26] 193:02<218:54, 8.00s/it | [Iter 1448/3090] R0[1358/3000] | LR: 0.014365 | E: -62.874827 | E_var:     4.5007 E_err:   0.033148 | NF_loss: 20.228100
[2025-11-13 04:00:34] 193:10<218:46, 8.00s/it | [Iter 1449/3090] R0[1359/3000] | LR: 0.014352 | E: -62.876957 | E_var:     4.0288 E_err:   0.031362 | NF_loss: 22.052834
[2025-11-13 04:00:42] 193:18<218:38, 8.00s/it | [Iter 1450/3090] R0[1360/3000] | LR: 0.014339 | E: -62.903959 | E_var:     4.6321 E_err:   0.033629 | NF_loss: 14.453467
[2025-11-13 04:00:49] 193:26<218:30, 8.00s/it | [Iter 1451/3090] R0[1361/3000] | LR: 0.014326 | E: -62.817350 | E_var:     4.8295 E_err:   0.034338 | NF_loss: 15.449812
[2025-11-13 04:00:57] 193:34<218:22, 8.00s/it | [Iter 1452/3090] R0[1362/3000] | LR: 0.014313 | E: -62.812698 | E_var:     4.9346 E_err:   0.034709 | NF_loss: 20.412964
[2025-11-13 04:01:05] 193:42<218:14, 8.00s/it | [Iter 1453/3090] R0[1363/3000] | LR: 0.014300 | E: -62.798628 | E_var:     4.3035 E_err:   0.032414 | NF_loss: 19.766137
[2025-11-13 04:01:13] 193:50<218:05, 8.00s/it | [Iter 1454/3090] R0[1364/3000] | LR: 0.014287 | E: -62.825035 | E_var:     4.7724 E_err:   0.034134 | NF_loss: 16.832480
[2025-11-13 04:01:21] 193:57<217:57, 8.00s/it | [Iter 1455/3090] R0[1365/3000] | LR: 0.014274 | E: -62.762263 | E_var:     4.6007 E_err:   0.033515 | NF_loss: 13.659305
[2025-11-13 04:01:29] 194:05<217:49, 8.00s/it | [Iter 1456/3090] R0[1366/3000] | LR: 0.014261 | E: -62.860192 | E_var:     4.2229 E_err:   0.032109 | NF_loss: 15.243160
[2025-11-13 04:01:37] 194:13<217:41, 8.00s/it | [Iter 1457/3090] R0[1367/3000] | LR: 0.014248 | E: -62.871266 | E_var:     4.2460 E_err:   0.032197 | NF_loss: 21.372068
[2025-11-13 04:01:45] 194:21<217:33, 8.00s/it | [Iter 1458/3090] R0[1368/3000] | LR: 0.014235 | E: -62.804146 | E_var:     4.2748 E_err:   0.032306 | NF_loss: 18.223235
[2025-11-13 04:01:53] 194:29<217:25, 8.00s/it | [Iter 1459/3090] R0[1369/3000] | LR: 0.014222 | E: -62.869840 | E_var:     4.2620 E_err:   0.032257 | NF_loss: 17.324280
[2025-11-13 04:02:00] 194:37<217:17, 8.00s/it | [Iter 1460/3090] R0[1370/3000] | LR: 0.014209 | E: -62.842737 | E_var:     4.3092 E_err:   0.032435 | NF_loss: 13.546060
[2025-11-13 04:02:08] 194:45<217:08, 8.00s/it | [Iter 1461/3090] R0[1371/3000] | LR: 0.014196 | E: -62.851001 | E_var:     4.5183 E_err:   0.033213 | NF_loss: 23.298189
[2025-11-13 04:02:16] 194:53<217:00, 8.00s/it | [Iter 1462/3090] R0[1372/3000] | LR: 0.014184 | E: -62.875075 | E_var:     4.1835 E_err:   0.031959 | NF_loss: 20.652680
[2025-11-13 04:02:24] 195:01<216:52, 8.00s/it | [Iter 1463/3090] R0[1373/3000] | LR: 0.014171 | E: -62.890502 | E_var:     4.2330 E_err:   0.032147 | NF_loss: 18.492638
[2025-11-13 04:02:32] 195:08<216:44, 8.00s/it | [Iter 1464/3090] R0[1374/3000] | LR: 0.014158 | E: -62.834316 | E_var:     4.3303 E_err:   0.032515 | NF_loss: 17.660581
[2025-11-13 04:02:40] 195:16<216:36, 8.00s/it | [Iter 1465/3090] R0[1375/3000] | LR: 0.014145 | E: -62.864986 | E_var:     4.2751 E_err:   0.032307 | NF_loss: 21.673038
[2025-11-13 04:02:48] 195:24<216:28, 8.00s/it | [Iter 1466/3090] R0[1376/3000] | LR: 0.014132 | E: -62.818951 | E_var:     4.1707 E_err:   0.031910 | NF_loss: 20.029209
[2025-11-13 04:02:56] 195:32<216:20, 8.00s/it | [Iter 1467/3090] R0[1377/3000] | LR: 0.014119 | E: -62.839253 | E_var:     4.2127 E_err:   0.032070 | NF_loss: 14.099572
[2025-11-13 04:03:03] 195:40<216:12, 8.00s/it | [Iter 1468/3090] R0[1378/3000] | LR: 0.014106 | E: -62.806872 | E_var:     4.2336 E_err:   0.032150 | NF_loss: 16.550417
[2025-11-13 04:03:11] 195:48<216:03, 8.00s/it | [Iter 1469/3090] R0[1379/3000] | LR: 0.014093 | E: -62.723797 | E_var:     4.3017 E_err:   0.032407 | NF_loss: 18.087478
[2025-11-13 04:03:19] 195:56<215:55, 8.00s/it | [Iter 1470/3090] R0[1380/3000] | LR: 0.014080 | E: -62.682142 | E_var:     4.4539 E_err:   0.032976 | NF_loss: 18.192842
[2025-11-13 04:03:27] 196:04<215:47, 8.00s/it | [Iter 1471/3090] R0[1381/3000] | LR: 0.014067 | E: -62.613458 | E_var:     4.4384 E_err:   0.032918 | NF_loss: 15.027057
[2025-11-13 04:03:35] 196:11<215:39, 8.00s/it | [Iter 1472/3090] R0[1382/3000] | LR: 0.014054 | E: -62.691377 | E_var:     4.4807 E_err:   0.033075 | NF_loss: 19.904545
[2025-11-13 04:03:43] 196:19<215:31, 8.00s/it | [Iter 1473/3090] R0[1383/3000] | LR: 0.014041 | E: -62.719565 | E_var:     4.4519 E_err:   0.032968 | NF_loss: 19.002329
[2025-11-13 04:03:51] 196:27<215:23, 8.00s/it | [Iter 1474/3090] R0[1384/3000] | LR: 0.014028 | E: -62.670103 | E_var:     4.5083 E_err:   0.033176 | NF_loss: 13.949061
[2025-11-13 04:03:59] 196:35<215:15, 8.00s/it | [Iter 1475/3090] R0[1385/3000] | LR: 0.014015 | E: -62.687309 | E_var:     4.3614 E_err:   0.032631 | NF_loss: 17.052000
[2025-11-13 04:04:07] 196:43<215:07, 8.00s/it | [Iter 1476/3090] R0[1386/3000] | LR: 0.014002 | E: -62.827520 | E_var:     4.2416 E_err:   0.032180 | NF_loss: 13.381024
[2025-11-13 04:04:15] 196:51<214:59, 8.00s/it | [Iter 1477/3090] R0[1387/3000] | LR: 0.013989 | E: -62.823078 | E_var:     4.2463 E_err:   0.032198 | NF_loss: 22.642669
[2025-11-13 04:04:22] 196:59<214:51, 8.00s/it | [Iter 1478/3090] R0[1388/3000] | LR: 0.013976 | E: -62.735015 | E_var:     4.5023 E_err:   0.033154 | NF_loss: 12.527251
[2025-11-13 04:04:30] 197:07<214:42, 8.00s/it | [Iter 1479/3090] R0[1389/3000] | LR: 0.013963 | E: -62.763204 | E_var:     4.3743 E_err:   0.032680 | NF_loss: 15.664493
[2025-11-13 04:04:38] 197:15<214:34, 8.00s/it | [Iter 1480/3090] R0[1390/3000] | LR: 0.013950 | E: -62.843574 | E_var:     4.3480 E_err:   0.032581 | NF_loss: 15.822837
[2025-11-13 04:04:46] 197:23<214:26, 8.00s/it | [Iter 1481/3090] R0[1391/3000] | LR: 0.013937 | E: -62.805232 | E_var:     4.2416 E_err:   0.032180 | NF_loss: 17.408633
[2025-11-13 04:04:54] 197:30<214:18, 8.00s/it | [Iter 1482/3090] R0[1392/3000] | LR: 0.013924 | E: -62.826415 | E_var:     4.3247 E_err:   0.032493 | NF_loss: 21.432085
[2025-11-13 04:05:02] 197:38<214:10, 8.00s/it | [Iter 1483/3090] R0[1393/3000] | LR: 0.013911 | E: -62.820981 | E_var:     4.4533 E_err:   0.032973 | NF_loss: 16.061399
[2025-11-13 04:05:10] 197:46<214:02, 8.00s/it | [Iter 1484/3090] R0[1394/3000] | LR: 0.013898 | E: -62.778922 | E_var:     4.1631 E_err:   0.031881 | NF_loss: 12.217379
[2025-11-13 04:05:18] 197:54<213:54, 8.00s/it | [Iter 1485/3090] R0[1395/3000] | LR: 0.013885 | E: -62.788409 | E_var:     4.4027 E_err:   0.032785 | NF_loss: 28.680785
[2025-11-13 04:05:25] 198:02<213:46, 8.00s/it | [Iter 1486/3090] R0[1396/3000] | LR: 0.013872 | E: -62.791632 | E_var:     4.3513 E_err:   0.032593 | NF_loss: 23.474350
[2025-11-13 04:05:33] 198:10<213:37, 8.00s/it | [Iter 1487/3090] R0[1397/3000] | LR: 0.013859 | E: -62.749640 | E_var:     4.1428 E_err:   0.031803 | NF_loss: 21.507805
[2025-11-13 04:05:41] 198:18<213:29, 8.00s/it | [Iter 1488/3090] R0[1398/3000] | LR: 0.013846 | E: -62.828478 | E_var:     4.5308 E_err:   0.033259 | NF_loss: 25.161885
[2025-11-13 04:05:49] 198:26<213:21, 8.00s/it | [Iter 1489/3090] R0[1399/3000] | LR: 0.013833 | E: -62.833790 | E_var:     4.4123 E_err:   0.032821 | NF_loss: 21.129957
[2025-11-13 04:05:57] 198:34<213:13, 8.00s/it | [Iter 1490/3090] R0[1400/3000] | LR: 0.013820 | E: -62.796554 | E_var:     4.1714 E_err:   0.031913 | NF_loss: 18.923667
[2025-11-13 04:06:05] 198:41<213:05, 8.00s/it | [Iter 1491/3090] R0[1401/3000] | LR: 0.013807 | E: -62.775218 | E_var:     4.2075 E_err:   0.032050 | NF_loss: 13.735526
[2025-11-13 04:06:13] 198:49<212:57, 8.00s/it | [Iter 1492/3090] R0[1402/3000] | LR: 0.013794 | E: -62.754451 | E_var:     4.1279 E_err:   0.031746 | NF_loss: 20.902387
[2025-11-13 04:06:21] 198:57<212:49, 8.00s/it | [Iter 1493/3090] R0[1403/3000] | LR: 0.013781 | E: -62.689541 | E_var:     4.2953 E_err:   0.032383 | NF_loss: 31.797175
[2025-11-13 04:06:28] 199:05<212:41, 8.00s/it | [Iter 1494/3090] R0[1404/3000] | LR: 0.013768 | E: -62.670422 | E_var:     4.5114 E_err:   0.033188 | NF_loss: 15.704973
[2025-11-13 04:06:36] 199:13<212:32, 8.00s/it | [Iter 1495/3090] R0[1405/3000] | LR: 0.013755 | E: -62.505630 | E_var:     4.5077 E_err:   0.033174 | NF_loss: 15.372362
[2025-11-13 04:06:44] 199:21<212:24, 8.00s/it | [Iter 1496/3090] R0[1406/3000] | LR: 0.013742 | E: -62.311086 | E_var:     4.7134 E_err:   0.033923 | NF_loss: 18.832677
[2025-11-13 04:06:52] 199:29<212:16, 8.00s/it | [Iter 1497/3090] R0[1407/3000] | LR: 0.013729 | E: -62.318769 | E_var:     4.8143 E_err:   0.034284 | NF_loss: 22.840504
[2025-11-13 04:07:00] 199:36<212:08, 8.00s/it | [Iter 1498/3090] R0[1408/3000] | LR: 0.013715 | E: -62.437665 | E_var:     4.6819 E_err:   0.033809 | NF_loss: 17.630154
[2025-11-13 04:07:08] 199:44<212:00, 8.00s/it | [Iter 1499/3090] R0[1409/3000] | LR: 0.013702 | E: -62.513428 | E_var:     4.4459 E_err:   0.032946 | NF_loss: 17.647814
[2025-11-13 04:07:16] 199:52<211:52, 8.00s/it | [Iter 1500/3090] R0[1410/3000] | LR: 0.013689 | E: -62.565614 | E_var:     4.4111 E_err:   0.032817 | NF_loss: 17.357379
[2025-11-13 04:07:16] 保存checkpoint: hybrid_checkpoint_iter_001500.pkl
[2025-11-13 04:07:24] 200:00<211:44, 8.00s/it | [Iter 1501/3090] R0[1411/3000] | LR: 0.013676 | E: -62.648609 | E_var:     4.3186 E_err:   0.032471 | NF_loss: 22.072317
[2025-11-13 04:07:32] 200:08<211:36, 8.00s/it | [Iter 1502/3090] R0[1412/3000] | LR: 0.013663 | E: -62.739144 | E_var:     4.3469 E_err:   0.032577 | NF_loss: 18.569910
[2025-11-13 04:07:40] 200:16<211:28, 8.00s/it | [Iter 1503/3090] R0[1413/3000] | LR: 0.013650 | E: -62.787093 | E_var:     4.2163 E_err:   0.032084 | NF_loss: 24.673676
[2025-11-13 04:07:48] 200:24<211:20, 8.00s/it | [Iter 1504/3090] R0[1414/3000] | LR: 0.013637 | E: -62.782782 | E_var:     4.0323 E_err:   0.031376 | NF_loss: 27.789573
[2025-11-13 04:07:55] 200:32<211:12, 7.99s/it | [Iter 1505/3090] R0[1415/3000] | LR: 0.013624 | E: -62.803069 | E_var:     4.2358 E_err:   0.032158 | NF_loss: 20.771017
[2025-11-13 04:08:03] 200:40<211:03, 7.99s/it | [Iter 1506/3090] R0[1416/3000] | LR: 0.013611 | E: -62.840017 | E_var:     4.2360 E_err:   0.032159 | NF_loss: 15.845341
[2025-11-13 04:08:11] 200:48<210:55, 7.99s/it | [Iter 1507/3090] R0[1417/3000] | LR: 0.013598 | E: -62.813436 | E_var:     4.1071 E_err:   0.031666 | NF_loss: 19.337300
[2025-11-13 04:08:19] 200:56<210:47, 7.99s/it | [Iter 1508/3090] R0[1418/3000] | LR: 0.013585 | E: -62.842976 | E_var:     4.2664 E_err:   0.032274 | NF_loss: 19.838457
[2025-11-13 04:08:27] 201:03<210:39, 7.99s/it | [Iter 1509/3090] R0[1419/3000] | LR: 0.013572 | E: -62.838842 | E_var:     4.0944 E_err:   0.031617 | NF_loss: 23.473237
[2025-11-13 04:08:35] 201:11<210:31, 7.99s/it | [Iter 1510/3090] R0[1420/3000] | LR: 0.013559 | E: -62.758724 | E_var:     4.1239 E_err:   0.031730 | NF_loss: 21.166572
[2025-11-13 04:08:43] 201:19<210:23, 7.99s/it | [Iter 1511/3090] R0[1421/3000] | LR: 0.013546 | E: -62.720860 | E_var:     4.3451 E_err:   0.032570 | NF_loss: 13.545246
[2025-11-13 04:08:51] 201:27<210:15, 7.99s/it | [Iter 1512/3090] R0[1422/3000] | LR: 0.013533 | E: -62.869136 | E_var:     4.1895 E_err:   0.031982 | NF_loss: 15.717996
[2025-11-13 04:08:58] 201:35<210:07, 7.99s/it | [Iter 1513/3090] R0[1423/3000] | LR: 0.013520 | E: -62.910878 | E_var:     4.0853 E_err:   0.031581 | NF_loss: 25.767419
[2025-11-13 04:09:07] 201:43<209:59, 7.99s/it | [Iter 1514/3090] R0[1424/3000] | LR: 0.013507 | E: -62.823196 | E_var:     4.2867 E_err:   0.032350 | NF_loss: 17.060508
[2025-11-13 04:09:14] 201:51<209:51, 7.99s/it | [Iter 1515/3090] R0[1425/3000] | LR: 0.013494 | E: -62.842165 | E_var:     3.9449 E_err:   0.031034 | NF_loss: 22.038275
[2025-11-13 04:09:22] 201:59<209:43, 7.99s/it | [Iter 1516/3090] R0[1426/3000] | LR: 0.013481 | E: -62.823023 | E_var:     4.2454 E_err:   0.032194 | NF_loss: 20.183990
[2025-11-13 04:09:30] 202:07<209:34, 7.99s/it | [Iter 1517/3090] R0[1427/3000] | LR: 0.013468 | E: -62.879194 | E_var:     4.0444 E_err:   0.031423 | NF_loss: 20.675037
[2025-11-13 04:09:38] 202:15<209:26, 7.99s/it | [Iter 1518/3090] R0[1428/3000] | LR: 0.013455 | E: -62.909766 | E_var:     4.3322 E_err:   0.032522 | NF_loss: 18.907944
[2025-11-13 04:09:46] 202:23<209:18, 7.99s/it | [Iter 1519/3090] R0[1429/3000] | LR: 0.013442 | E: -62.932685 | E_var:     4.5535 E_err:   0.033342 | NF_loss: 17.207856
[2025-11-13 04:09:54] 202:30<209:10, 7.99s/it | [Iter 1520/3090] R0[1430/3000] | LR: 0.013429 | E: -62.870006 | E_var:     4.2875 E_err:   0.032353 | NF_loss: 23.968043
[2025-11-13 04:10:02] 202:38<209:02, 7.99s/it | [Iter 1521/3090] R0[1431/3000] | LR: 0.013416 | E: -62.888281 | E_var:     4.3088 E_err:   0.032434 | NF_loss: 12.371948
[2025-11-13 04:10:10] 202:46<208:54, 7.99s/it | [Iter 1522/3090] R0[1432/3000] | LR: 0.013402 | E: -62.881150 | E_var:     4.2944 E_err:   0.032380 | NF_loss: 15.812400
[2025-11-13 04:10:17] 202:54<208:46, 7.99s/it | [Iter 1523/3090] R0[1433/3000] | LR: 0.013389 | E: -62.863221 | E_var:     4.2349 E_err:   0.032154 | NF_loss: 12.008901
[2025-11-13 04:10:25] 203:02<208:38, 7.99s/it | [Iter 1524/3090] R0[1434/3000] | LR: 0.013376 | E: -62.805852 | E_var:     4.5300 E_err:   0.033256 | NF_loss: 18.861752
[2025-11-13 04:10:33] 203:10<208:30, 7.99s/it | [Iter 1525/3090] R0[1435/3000] | LR: 0.013363 | E: -62.856786 | E_var:     4.2245 E_err:   0.032115 | NF_loss: 18.951023
[2025-11-13 04:10:41] 203:18<208:21, 7.99s/it | [Iter 1526/3090] R0[1436/3000] | LR: 0.013350 | E: -62.864897 | E_var:     4.3565 E_err:   0.032613 | NF_loss: 22.184156
[2025-11-13 04:10:49] 203:26<208:13, 7.99s/it | [Iter 1527/3090] R0[1437/3000] | LR: 0.013337 | E: -62.843903 | E_var:     4.5769 E_err:   0.033428 | NF_loss: 15.441307
[2025-11-13 04:10:57] 203:33<208:05, 7.99s/it | [Iter 1528/3090] R0[1438/3000] | LR: 0.013324 | E: -62.812420 | E_var:     4.5535 E_err:   0.033342 | NF_loss: 15.449360
[2025-11-13 04:11:05] 203:41<207:57, 7.99s/it | [Iter 1529/3090] R0[1439/3000] | LR: 0.013311 | E: -62.873835 | E_var:     4.1728 E_err:   0.031918 | NF_loss: 16.093417
[2025-11-13 04:11:13] 203:49<207:49, 7.99s/it | [Iter 1530/3090] R0[1440/3000] | LR: 0.013298 | E: -62.862564 | E_var:     4.0318 E_err:   0.031374 | NF_loss: 20.319410
[2025-11-13 04:11:20] 203:57<207:41, 7.99s/it | [Iter 1531/3090] R0[1441/3000] | LR: 0.013285 | E: -62.871248 | E_var:     4.3005 E_err:   0.032403 | NF_loss: 11.300634
[2025-11-13 04:11:28] 204:05<207:33, 7.99s/it | [Iter 1532/3090] R0[1442/3000] | LR: 0.013272 | E: -62.811636 | E_var:     4.4759 E_err:   0.033057 | NF_loss: 13.026237
[2025-11-13 04:11:36] 204:13<207:25, 7.99s/it | [Iter 1533/3090] R0[1443/3000] | LR: 0.013259 | E: -62.854000 | E_var:     4.1670 E_err:   0.031896 | NF_loss: 14.606562
[2025-11-13 04:11:44] 204:21<207:16, 7.99s/it | [Iter 1534/3090] R0[1444/3000] | LR: 0.013246 | E: -62.812768 | E_var:     4.2795 E_err:   0.032323 | NF_loss: 26.900566
[2025-11-13 04:11:52] 204:29<207:08, 7.99s/it | [Iter 1535/3090] R0[1445/3000] | LR: 0.013233 | E: -62.850394 | E_var:     4.3083 E_err:   0.032432 | NF_loss: 15.394171
[2025-11-13 04:12:00] 204:36<207:00, 7.99s/it | [Iter 1536/3090] R0[1446/3000] | LR: 0.013220 | E: -62.893791 | E_var:     4.1971 E_err:   0.032011 | NF_loss: 16.401095
[2025-11-13 04:12:08] 204:44<206:52, 7.99s/it | [Iter 1537/3090] R0[1447/3000] | LR: 0.013207 | E: -62.843024 | E_var:     4.2519 E_err:   0.032219 | NF_loss: 15.198363
[2025-11-13 04:12:16] 204:52<206:44, 7.99s/it | [Iter 1538/3090] R0[1448/3000] | LR: 0.013193 | E: -62.875352 | E_var:     4.3436 E_err:   0.032564 | NF_loss: 19.021819
[2025-11-13 04:12:23] 205:00<206:36, 7.99s/it | [Iter 1539/3090] R0[1449/3000] | LR: 0.013180 | E: -62.867488 | E_var:     4.5195 E_err:   0.033217 | NF_loss: 12.492977
[2025-11-13 04:12:31] 205:08<206:28, 7.99s/it | [Iter 1540/3090] R0[1450/3000] | LR: 0.013167 | E: -62.804315 | E_var:     4.4939 E_err:   0.033123 | NF_loss: 20.557949
[2025-11-13 04:12:39] 205:16<206:20, 7.99s/it | [Iter 1541/3090] R0[1451/3000] | LR: 0.013154 | E: -62.708047 | E_var:     4.9799 E_err:   0.034868 | NF_loss: 16.430366
[2025-11-13 04:12:47] 205:24<206:12, 7.99s/it | [Iter 1542/3090] R0[1452/3000] | LR: 0.013141 | E: -62.631577 | E_var:     5.2523 E_err:   0.035809 | NF_loss: 13.716173
[2025-11-13 04:12:55] 205:32<206:03, 7.99s/it | [Iter 1543/3090] R0[1453/3000] | LR: 0.013128 | E: -62.456424 | E_var:     5.0110 E_err:   0.034977 | NF_loss: 15.729031
[2025-11-13 04:13:03] 205:39<205:55, 7.99s/it | [Iter 1544/3090] R0[1454/3000] | LR: 0.013115 | E: -62.592975 | E_var:     4.8216 E_err:   0.034310 | NF_loss: 13.795150
[2025-11-13 04:13:11] 205:47<205:47, 7.99s/it | [Iter 1545/3090] R0[1455/3000] | LR: 0.013102 | E: -62.510409 | E_var:     4.9871 E_err:   0.034894 | NF_loss: 9.425403
[2025-11-13 04:13:19] 205:55<205:39, 7.99s/it | [Iter 1546/3090] R0[1456/3000] | LR: 0.013089 | E: -62.602446 | E_var:     4.7814 E_err:   0.034166 | NF_loss: 17.347848
[2025-11-13 04:13:26] 206:03<205:31, 7.99s/it | [Iter 1547/3090] R0[1457/3000] | LR: 0.013076 | E: -62.565814 | E_var:     4.7778 E_err:   0.034153 | NF_loss: 23.200011
[2025-11-13 04:13:34] 206:11<205:23, 7.99s/it | [Iter 1548/3090] R0[1458/3000] | LR: 0.013063 | E: -62.659871 | E_var:     4.9553 E_err:   0.034782 | NF_loss: 22.070142
[2025-11-13 04:13:42] 206:19<205:15, 7.99s/it | [Iter 1549/3090] R0[1459/3000] | LR: 0.013050 | E: -62.687932 | E_var:     5.2406 E_err:   0.035769 | NF_loss: 14.728861
[2025-11-13 04:13:50] 206:27<205:07, 7.99s/it | [Iter 1550/3090] R0[1460/3000] | LR: 0.013037 | E: -62.591434 | E_var:     5.0109 E_err:   0.034977 | NF_loss: 16.952630
[2025-11-13 04:13:58] 206:35<204:59, 7.99s/it | [Iter 1551/3090] R0[1461/3000] | LR: 0.013023 | E: -62.741722 | E_var:     4.3124 E_err:   0.032448 | NF_loss: 20.142299
[2025-11-13 04:14:06] 206:42<204:51, 7.99s/it | [Iter 1552/3090] R0[1462/3000] | LR: 0.013010 | E: -62.684249 | E_var:     4.7267 E_err:   0.033970 | NF_loss: 18.351523
[2025-11-13 04:14:14] 206:50<204:42, 7.99s/it | [Iter 1553/3090] R0[1463/3000] | LR: 0.012997 | E: -62.654329 | E_var:     4.4406 E_err:   0.032926 | NF_loss: 18.907258
[2025-11-13 04:14:22] 206:58<204:34, 7.99s/it | [Iter 1554/3090] R0[1464/3000] | LR: 0.012984 | E: -62.714938 | E_var:     4.5115 E_err:   0.033188 | NF_loss: 13.354097
[2025-11-13 04:14:30] 207:06<204:26, 7.99s/it | [Iter 1555/3090] R0[1465/3000] | LR: 0.012971 | E: -62.688354 | E_var:     4.5135 E_err:   0.033195 | NF_loss: 18.827099
[2025-11-13 04:14:37] 207:14<204:18, 7.99s/it | [Iter 1556/3090] R0[1466/3000] | LR: 0.012958 | E: -62.700696 | E_var:     4.5733 E_err:   0.033414 | NF_loss: 16.321334
[2025-11-13 04:14:45] 207:22<204:10, 7.99s/it | [Iter 1557/3090] R0[1467/3000] | LR: 0.012945 | E: -62.642591 | E_var:     4.6639 E_err:   0.033744 | NF_loss: 16.770521
[2025-11-13 04:14:53] 207:30<204:02, 7.99s/it | [Iter 1558/3090] R0[1468/3000] | LR: 0.012932 | E: -62.684573 | E_var:     4.2384 E_err:   0.032168 | NF_loss: 17.676933
[2025-11-13 04:15:01] 207:38<203:54, 7.99s/it | [Iter 1559/3090] R0[1469/3000] | LR: 0.012919 | E: -62.807480 | E_var:     4.3245 E_err:   0.032493 | NF_loss: 17.285747
[2025-11-13 04:15:09] 207:46<203:46, 7.99s/it | [Iter 1560/3090] R0[1470/3000] | LR: 0.012906 | E: -62.770049 | E_var:     4.5136 E_err:   0.033196 | NF_loss: 22.914955
[2025-11-13 04:15:17] 207:54<203:38, 7.99s/it | [Iter 1561/3090] R0[1471/3000] | LR: 0.012893 | E: -62.685080 | E_var:     4.7320 E_err:   0.033989 | NF_loss: 17.418579
[2025-11-13 04:15:25] 208:01<203:30, 7.99s/it | [Iter 1562/3090] R0[1472/3000] | LR: 0.012880 | E: -62.761306 | E_var:     4.3271 E_err:   0.032503 | NF_loss: 21.997697
[2025-11-13 04:15:33] 208:09<203:22, 7.99s/it | [Iter 1563/3090] R0[1473/3000] | LR: 0.012867 | E: -62.751270 | E_var:     4.6905 E_err:   0.033840 | NF_loss: 19.053083
[2025-11-13 04:15:41] 208:17<203:14, 7.99s/it | [Iter 1564/3090] R0[1474/3000] | LR: 0.012853 | E: -62.667354 | E_var:     4.4658 E_err:   0.033019 | NF_loss: 23.315334
[2025-11-13 04:15:49] 208:25<203:05, 7.99s/it | [Iter 1565/3090] R0[1475/3000] | LR: 0.012840 | E: -62.769984 | E_var:     4.1704 E_err:   0.031909 | NF_loss: 17.207509
[2025-11-13 04:15:56] 208:33<202:57, 7.99s/it | [Iter 1566/3090] R0[1476/3000] | LR: 0.012827 | E: -62.689644 | E_var:     4.5090 E_err:   0.033179 | NF_loss: 18.177306
[2025-11-13 04:16:04] 208:41<202:49, 7.99s/it | [Iter 1567/3090] R0[1477/3000] | LR: 0.012814 | E: -62.782754 | E_var:     4.3411 E_err:   0.032555 | NF_loss: 17.020675
[2025-11-13 04:16:12] 208:49<202:41, 7.99s/it | [Iter 1568/3090] R0[1478/3000] | LR: 0.012801 | E: -62.715237 | E_var:     4.4722 E_err:   0.033043 | NF_loss: 16.627943
[2025-11-13 04:16:20] 208:57<202:33, 7.99s/it | [Iter 1569/3090] R0[1479/3000] | LR: 0.012788 | E: -62.792867 | E_var:     4.3029 E_err:   0.032412 | NF_loss: 18.859644
[2025-11-13 04:16:28] 209:04<202:25, 7.99s/it | [Iter 1570/3090] R0[1480/3000] | LR: 0.012775 | E: -62.800764 | E_var:     4.4110 E_err:   0.032816 | NF_loss: 19.983266
[2025-11-13 04:16:36] 209:12<202:17, 7.99s/it | [Iter 1571/3090] R0[1481/3000] | LR: 0.012762 | E: -62.744211 | E_var:     4.6364 E_err:   0.033644 | NF_loss: 18.256508
[2025-11-13 04:16:44] 209:20<202:09, 7.99s/it | [Iter 1572/3090] R0[1482/3000] | LR: 0.012749 | E: -62.754744 | E_var:     4.4782 E_err:   0.033065 | NF_loss: 18.782507
[2025-11-13 04:16:52] 209:28<202:01, 7.99s/it | [Iter 1573/3090] R0[1483/3000] | LR: 0.012736 | E: -62.740321 | E_var:     4.6322 E_err:   0.033629 | NF_loss: 20.469615
[2025-11-13 04:16:59] 209:36<201:53, 7.99s/it | [Iter 1574/3090] R0[1484/3000] | LR: 0.012723 | E: -62.731188 | E_var:     4.6161 E_err:   0.033570 | NF_loss: 17.915948
[2025-11-13 04:17:07] 209:44<201:44, 7.99s/it | [Iter 1575/3090] R0[1485/3000] | LR: 0.012709 | E: -62.800336 | E_var:     4.5053 E_err:   0.033165 | NF_loss: 14.779820
[2025-11-13 04:17:15] 209:52<201:36, 7.99s/it | [Iter 1576/3090] R0[1486/3000] | LR: 0.012696 | E: -62.838137 | E_var:     4.3224 E_err:   0.032485 | NF_loss: 19.126650
[2025-11-13 04:17:23] 210:00<201:28, 7.99s/it | [Iter 1577/3090] R0[1487/3000] | LR: 0.012683 | E: -62.807697 | E_var:     4.8483 E_err:   0.034404 | NF_loss: 19.711934
[2025-11-13 04:17:31] 210:07<201:20, 7.99s/it | [Iter 1578/3090] R0[1488/3000] | LR: 0.012670 | E: -62.732510 | E_var:     5.1389 E_err:   0.035420 | NF_loss: 20.541761
[2025-11-13 04:17:39] 210:15<201:12, 7.99s/it | [Iter 1579/3090] R0[1489/3000] | LR: 0.012657 | E: -62.739878 | E_var:     4.7844 E_err:   0.034177 | NF_loss: 17.049496
[2025-11-13 04:17:47] 210:23<201:04, 7.99s/it | [Iter 1580/3090] R0[1490/3000] | LR: 0.012644 | E: -62.755232 | E_var:     4.3429 E_err:   0.032562 | NF_loss: 18.074309
[2025-11-13 04:17:55] 210:31<200:56, 7.99s/it | [Iter 1581/3090] R0[1491/3000] | LR: 0.012631 | E: -62.819296 | E_var:     4.2184 E_err:   0.032092 | NF_loss: 17.728041
[2025-11-13 04:18:02] 210:39<200:48, 7.99s/it | [Iter 1582/3090] R0[1492/3000] | LR: 0.012618 | E: -62.784473 | E_var:     4.3267 E_err:   0.032501 | NF_loss: 20.668633
[2025-11-13 04:18:10] 210:47<200:40, 7.99s/it | [Iter 1583/3090] R0[1493/3000] | LR: 0.012605 | E: -62.713998 | E_var:     4.3006 E_err:   0.032403 | NF_loss: 20.217731
[2025-11-13 04:18:18] 210:55<200:32, 7.99s/it | [Iter 1584/3090] R0[1494/3000] | LR: 0.012592 | E: -62.701965 | E_var:     4.4945 E_err:   0.033125 | NF_loss: 18.197604
[2025-11-13 04:18:26] 211:03<200:23, 7.99s/it | [Iter 1585/3090] R0[1495/3000] | LR: 0.012579 | E: -62.731812 | E_var:     4.3645 E_err:   0.032643 | NF_loss: 18.594231
[2025-11-13 04:18:34] 211:10<200:15, 7.99s/it | [Iter 1586/3090] R0[1496/3000] | LR: 0.012565 | E: -62.763909 | E_var:     4.4045 E_err:   0.032792 | NF_loss: 14.654627
[2025-11-13 04:18:42] 211:18<200:07, 7.99s/it | [Iter 1587/3090] R0[1497/3000] | LR: 0.012552 | E: -62.817187 | E_var:     4.5010 E_err:   0.033149 | NF_loss: 16.911719
[2025-11-13 04:18:50] 211:26<199:59, 7.99s/it | [Iter 1588/3090] R0[1498/3000] | LR: 0.012539 | E: -62.883946 | E_var:     4.1930 E_err:   0.031995 | NF_loss: 16.655298
[2025-11-13 04:18:58] 211:34<199:51, 7.99s/it | [Iter 1589/3090] R0[1499/3000] | LR: 0.012526 | E: -62.859128 | E_var:     4.1000 E_err:   0.031638 | NF_loss: 16.834274
[2025-11-13 04:19:05] 211:42<199:43, 7.99s/it | [Iter 1590/3090] R0[1500/3000] | LR: 0.012513 | E: -62.832842 | E_var:     4.2198 E_err:   0.032097 | NF_loss: 14.417294
[2025-11-13 04:19:13] 211:50<199:35, 7.99s/it | [Iter 1591/3090] R0[1501/3000] | LR: 0.012500 | E: -62.846925 | E_var:     4.0724 E_err:   0.031532 | NF_loss: 13.502214
[2025-11-13 04:19:21] 211:58<199:27, 7.99s/it | [Iter 1592/3090] R0[1502/3000] | LR: 0.012487 | E: -62.870367 | E_var:     4.3023 E_err:   0.032409 | NF_loss: 20.709490
[2025-11-13 04:19:29] 212:06<199:19, 7.99s/it | [Iter 1593/3090] R0[1503/3000] | LR: 0.012474 | E: -62.844135 | E_var:     4.1592 E_err:   0.031866 | NF_loss: 14.490395
[2025-11-13 04:19:37] 212:13<199:11, 7.99s/it | [Iter 1594/3090] R0[1504/3000] | LR: 0.012461 | E: -62.874218 | E_var:     4.1817 E_err:   0.031952 | NF_loss: 17.963602
[2025-11-13 04:19:45] 212:22<199:03, 7.99s/it | [Iter 1595/3090] R0[1505/3000] | LR: 0.012448 | E: -62.890849 | E_var:     4.2197 E_err:   0.032097 | NF_loss: 21.400831
[2025-11-13 04:19:53] 212:30<198:55, 7.99s/it | [Iter 1596/3090] R0[1506/3000] | LR: 0.012435 | E: -62.899818 | E_var:     4.1474 E_err:   0.031821 | NF_loss: 17.358760
[2025-11-13 04:20:01] 212:37<198:47, 7.99s/it | [Iter 1597/3090] R0[1507/3000] | LR: 0.012422 | E: -62.909922 | E_var:     4.2000 E_err:   0.032022 | NF_loss: 20.006640
[2025-11-13 04:20:09] 212:45<198:38, 7.99s/it | [Iter 1598/3090] R0[1508/3000] | LR: 0.012408 | E: -62.891222 | E_var:     4.4726 E_err:   0.033045 | NF_loss: 15.555882
[2025-11-13 04:20:17] 212:53<198:30, 7.99s/it | [Iter 1599/3090] R0[1509/3000] | LR: 0.012395 | E: -62.871881 | E_var:     4.2482 E_err:   0.032205 | NF_loss: 22.159442
[2025-11-13 04:20:24] 213:01<198:22, 7.99s/it | [Iter 1600/3090] R0[1510/3000] | LR: 0.012382 | E: -62.864375 | E_var:     4.0153 E_err:   0.031310 | NF_loss: 18.191137
[2025-11-13 04:20:32] 213:09<198:14, 7.99s/it | [Iter 1601/3090] R0[1511/3000] | LR: 0.012369 | E: -62.900873 | E_var:     4.3001 E_err:   0.032401 | NF_loss: 13.592698
[2025-11-13 04:20:40] 213:17<198:06, 7.99s/it | [Iter 1602/3090] R0[1512/3000] | LR: 0.012356 | E: -62.907832 | E_var:     4.1948 E_err:   0.032002 | NF_loss: 17.424219
[2025-11-13 04:20:48] 213:25<197:58, 7.99s/it | [Iter 1603/3090] R0[1513/3000] | LR: 0.012343 | E: -62.901590 | E_var:     4.2535 E_err:   0.032225 | NF_loss: 14.759643
[2025-11-13 04:20:56] 213:32<197:50, 7.99s/it | [Iter 1604/3090] R0[1514/3000] | LR: 0.012330 | E: -62.875704 | E_var:     4.2540 E_err:   0.032227 | NF_loss: 20.488211
[2025-11-13 04:21:04] 213:40<197:42, 7.99s/it | [Iter 1605/3090] R0[1515/3000] | LR: 0.012317 | E: -62.902241 | E_var:     4.2323 E_err:   0.032145 | NF_loss: 19.138604
[2025-11-13 04:21:12] 213:48<197:34, 7.99s/it | [Iter 1606/3090] R0[1516/3000] | LR: 0.012304 | E: -62.936075 | E_var:     4.2478 E_err:   0.032203 | NF_loss: 18.362145
[2025-11-13 04:21:20] 213:56<197:26, 7.99s/it | [Iter 1607/3090] R0[1517/3000] | LR: 0.012291 | E: -62.866569 | E_var:     4.1943 E_err:   0.032000 | NF_loss: 16.537410
[2025-11-13 04:21:27] 214:04<197:18, 7.99s/it | [Iter 1608/3090] R0[1518/3000] | LR: 0.012278 | E: -62.885026 | E_var:     3.9763 E_err:   0.031157 | NF_loss: 17.616724
[2025-11-13 04:21:35] 214:12<197:09, 7.99s/it | [Iter 1609/3090] R0[1519/3000] | LR: 0.012264 | E: -62.875534 | E_var:     4.3380 E_err:   0.032544 | NF_loss: 17.319830
[2025-11-13 04:21:43] 214:20<197:01, 7.99s/it | [Iter 1610/3090] R0[1520/3000] | LR: 0.012251 | E: -62.896949 | E_var:     4.1793 E_err:   0.031943 | NF_loss: 19.934388
[2025-11-13 04:21:51] 214:28<196:53, 7.99s/it | [Iter 1611/3090] R0[1521/3000] | LR: 0.012238 | E: -62.802774 | E_var:     4.4653 E_err:   0.033018 | NF_loss: 15.928475
[2025-11-13 04:21:59] 214:35<196:45, 7.99s/it | [Iter 1612/3090] R0[1522/3000] | LR: 0.012225 | E: -62.810090 | E_var:     4.5151 E_err:   0.033201 | NF_loss: 18.823135
[2025-11-13 04:22:07] 214:43<196:37, 7.99s/it | [Iter 1613/3090] R0[1523/3000] | LR: 0.012212 | E: -62.752675 | E_var:     4.4294 E_err:   0.032885 | NF_loss: 18.269525
[2025-11-13 04:22:15] 214:51<196:29, 7.99s/it | [Iter 1614/3090] R0[1524/3000] | LR: 0.012199 | E: -62.813109 | E_var:     4.4447 E_err:   0.032941 | NF_loss: 18.071100
[2025-11-13 04:22:23] 214:59<196:21, 7.99s/it | [Iter 1615/3090] R0[1525/3000] | LR: 0.012186 | E: -62.776934 | E_var:     4.3844 E_err:   0.032717 | NF_loss: 15.043914
[2025-11-13 04:22:30] 215:07<196:13, 7.99s/it | [Iter 1616/3090] R0[1526/3000] | LR: 0.012173 | E: -62.870593 | E_var:     4.3981 E_err:   0.032768 | NF_loss: 13.590737
[2025-11-13 04:22:38] 215:15<196:05, 7.99s/it | [Iter 1617/3090] R0[1527/3000] | LR: 0.012160 | E: -62.896718 | E_var:     4.2995 E_err:   0.032399 | NF_loss: 18.760809
[2025-11-13 04:22:46] 215:23<195:57, 7.99s/it | [Iter 1618/3090] R0[1528/3000] | LR: 0.012147 | E: -62.765890 | E_var:     4.6660 E_err:   0.033751 | NF_loss: 16.572797
[2025-11-13 04:22:54] 215:31<195:49, 7.99s/it | [Iter 1619/3090] R0[1529/3000] | LR: 0.012134 | E: -62.898523 | E_var:     4.4674 E_err:   0.033025 | NF_loss: 18.170719
[2025-11-13 04:23:02] 215:38<195:40, 7.99s/it | [Iter 1620/3090] R0[1530/3000] | LR: 0.012121 | E: -62.824800 | E_var:     4.2839 E_err:   0.032340 | NF_loss: 19.522194
[2025-11-13 04:23:10] 215:46<195:32, 7.99s/it | [Iter 1621/3090] R0[1531/3000] | LR: 0.012107 | E: -62.865348 | E_var:     4.6110 E_err:   0.033552 | NF_loss: 18.085513
[2025-11-13 04:23:18] 215:54<195:24, 7.99s/it | [Iter 1622/3090] R0[1532/3000] | LR: 0.012094 | E: -62.864756 | E_var:     4.1461 E_err:   0.031816 | NF_loss: 17.397088
[2025-11-13 04:23:26] 216:02<195:16, 7.99s/it | [Iter 1623/3090] R0[1533/3000] | LR: 0.012081 | E: -62.848385 | E_var:     4.1638 E_err:   0.031883 | NF_loss: 23.633345
[2025-11-13 04:23:34] 216:10<195:08, 7.99s/it | [Iter 1624/3090] R0[1534/3000] | LR: 0.012068 | E: -62.866625 | E_var:     4.4034 E_err:   0.032788 | NF_loss: 15.858622
[2025-11-13 04:23:42] 216:18<195:00, 7.99s/it | [Iter 1625/3090] R0[1535/3000] | LR: 0.012055 | E: -62.853054 | E_var:     4.6025 E_err:   0.033521 | NF_loss: 20.455959
[2025-11-13 04:23:49] 216:26<194:52, 7.99s/it | [Iter 1626/3090] R0[1536/3000] | LR: 0.012042 | E: -62.867603 | E_var:     4.3538 E_err:   0.032603 | NF_loss: 14.483112
[2025-11-13 04:23:57] 216:34<194:44, 7.99s/it | [Iter 1627/3090] R0[1537/3000] | LR: 0.012029 | E: -62.906138 | E_var:     4.4121 E_err:   0.032820 | NF_loss: 12.987860
[2025-11-13 04:24:05] 216:42<194:36, 7.99s/it | [Iter 1628/3090] R0[1538/3000] | LR: 0.012016 | E: -62.875638 | E_var:     4.4232 E_err:   0.032862 | NF_loss: 19.743355
[2025-11-13 04:24:13] 216:50<194:28, 7.99s/it | [Iter 1629/3090] R0[1539/3000] | LR: 0.012003 | E: -62.867272 | E_var:     4.5175 E_err:   0.033210 | NF_loss: 25.809885
[2025-11-13 04:24:21] 216:57<194:20, 7.99s/it | [Iter 1630/3090] R0[1540/3000] | LR: 0.011990 | E: -62.909208 | E_var:     4.5048 E_err:   0.033163 | NF_loss: 16.453170
[2025-11-13 04:24:29] 217:05<194:12, 7.99s/it | [Iter 1631/3090] R0[1541/3000] | LR: 0.011977 | E: -62.872826 | E_var:     4.3136 E_err:   0.032452 | NF_loss: 20.752420
[2025-11-13 04:24:37] 217:13<194:04, 7.99s/it | [Iter 1632/3090] R0[1542/3000] | LR: 0.011964 | E: -62.869681 | E_var:     4.6826 E_err:   0.033812 | NF_loss: 37.095448
[2025-11-13 04:24:45] 217:21<193:56, 7.99s/it | [Iter 1633/3090] R0[1543/3000] | LR: 0.011950 | E: -62.806678 | E_var:     4.7375 E_err:   0.034009 | NF_loss: 22.366465
[2025-11-13 04:24:52] 217:29<193:47, 7.99s/it | [Iter 1634/3090] R0[1544/3000] | LR: 0.011937 | E: -62.847491 | E_var:     4.2829 E_err:   0.032336 | NF_loss: 22.808007
[2025-11-13 04:25:00] 217:37<193:39, 7.99s/it | [Iter 1635/3090] R0[1545/3000] | LR: 0.011924 | E: -62.861689 | E_var:     4.7497 E_err:   0.034053 | NF_loss: 29.155393
[2025-11-13 04:25:08] 217:45<193:31, 7.99s/it | [Iter 1636/3090] R0[1546/3000] | LR: 0.011911 | E: -62.901235 | E_var:     4.2813 E_err:   0.032330 | NF_loss: 19.399753
[2025-11-13 04:25:16] 217:53<193:23, 7.99s/it | [Iter 1637/3090] R0[1547/3000] | LR: 0.011898 | E: -62.865178 | E_var:     4.1936 E_err:   0.031997 | NF_loss: 23.261549
[2025-11-13 04:25:24] 218:01<193:15, 7.99s/it | [Iter 1638/3090] R0[1548/3000] | LR: 0.011885 | E: -62.932632 | E_var:     4.2808 E_err:   0.032328 | NF_loss: 16.408903
[2025-11-13 04:25:32] 218:08<193:07, 7.99s/it | [Iter 1639/3090] R0[1549/3000] | LR: 0.011872 | E: -62.887534 | E_var:     4.4313 E_err:   0.032891 | NF_loss: 20.299153
[2025-11-13 04:25:40] 218:16<192:59, 7.99s/it | [Iter 1640/3090] R0[1550/3000] | LR: 0.011859 | E: -62.870790 | E_var:     4.5012 E_err:   0.033150 | NF_loss: 18.050899
[2025-11-13 04:25:48] 218:24<192:51, 7.99s/it | [Iter 1641/3090] R0[1551/3000] | LR: 0.011846 | E: -62.873663 | E_var:     4.5226 E_err:   0.033229 | NF_loss: 22.064529
[2025-11-13 04:25:55] 218:32<192:43, 7.99s/it | [Iter 1642/3090] R0[1552/3000] | LR: 0.011833 | E: -62.892095 | E_var:     4.3914 E_err:   0.032743 | NF_loss: 27.021274
[2025-11-13 04:26:03] 218:40<192:35, 7.99s/it | [Iter 1643/3090] R0[1553/3000] | LR: 0.011820 | E: -62.891768 | E_var:     4.2132 E_err:   0.032072 | NF_loss: 22.156292
[2025-11-13 04:26:11] 218:48<192:27, 7.99s/it | [Iter 1644/3090] R0[1554/3000] | LR: 0.011807 | E: -62.880325 | E_var:     4.3246 E_err:   0.032493 | NF_loss: 21.229320
[2025-11-13 04:26:19] 218:56<192:19, 7.99s/it | [Iter 1645/3090] R0[1555/3000] | LR: 0.011794 | E: -62.887515 | E_var:     4.1850 E_err:   0.031964 | NF_loss: 25.404283
[2025-11-13 04:26:27] 219:04<192:10, 7.99s/it | [Iter 1646/3090] R0[1556/3000] | LR: 0.011781 | E: -62.932588 | E_var:     4.1039 E_err:   0.031653 | NF_loss: 33.341295
[2025-11-13 04:26:35] 219:11<192:02, 7.99s/it | [Iter 1647/3090] R0[1557/3000] | LR: 0.011767 | E: -62.930705 | E_var:     3.9655 E_err:   0.031115 | NF_loss: 38.961167
[2025-11-13 04:26:43] 219:19<191:54, 7.99s/it | [Iter 1648/3090] R0[1558/3000] | LR: 0.011754 | E: -62.930503 | E_var:     4.2898 E_err:   0.032362 | NF_loss: 21.349381
[2025-11-13 04:26:51] 219:27<191:46, 7.99s/it | [Iter 1649/3090] R0[1559/3000] | LR: 0.011741 | E: -62.855404 | E_var:     4.5520 E_err:   0.033337 | NF_loss: 24.889304
[2025-11-13 04:26:59] 219:35<191:38, 7.99s/it | [Iter 1650/3090] R0[1560/3000] | LR: 0.011728 | E: -62.841607 | E_var:     4.1487 E_err:   0.031825 | NF_loss: 27.897749
[2025-11-13 04:27:06] 219:43<191:30, 7.99s/it | [Iter 1651/3090] R0[1561/3000] | LR: 0.011715 | E: -62.836820 | E_var:     4.4543 E_err:   0.032977 | NF_loss: 29.087440
[2025-11-13 04:27:14] 219:51<191:22, 7.99s/it | [Iter 1652/3090] R0[1562/3000] | LR: 0.011702 | E: -62.936013 | E_var:     4.4177 E_err:   0.032841 | NF_loss: 28.920503
[2025-11-13 04:27:22] 219:59<191:14, 7.98s/it | [Iter 1653/3090] R0[1563/3000] | LR: 0.011689 | E: -62.889562 | E_var:     4.1313 E_err:   0.031759 | NF_loss: 34.468463
[2025-11-13 04:27:30] 220:07<191:06, 7.98s/it | [Iter 1654/3090] R0[1564/3000] | LR: 0.011676 | E: -62.850293 | E_var:     4.2794 E_err:   0.032323 | NF_loss: 22.666543
[2025-11-13 04:27:38] 220:14<190:58, 7.98s/it | [Iter 1655/3090] R0[1565/3000] | LR: 0.011663 | E: -62.868704 | E_var:     4.2798 E_err:   0.032325 | NF_loss: 24.887215
[2025-11-13 04:27:46] 220:22<190:50, 7.98s/it | [Iter 1656/3090] R0[1566/3000] | LR: 0.011650 | E: -62.851284 | E_var:     4.1739 E_err:   0.031922 | NF_loss: 28.250978
[2025-11-13 04:27:54] 220:30<190:42, 7.98s/it | [Iter 1657/3090] R0[1567/3000] | LR: 0.011637 | E: -62.833061 | E_var:     4.5410 E_err:   0.033296 | NF_loss: 23.492235
[2025-11-13 04:28:02] 220:38<190:34, 7.98s/it | [Iter 1658/3090] R0[1568/3000] | LR: 0.011624 | E: -62.872701 | E_var:     4.4065 E_err:   0.032800 | NF_loss: 18.978916
[2025-11-13 04:28:09] 220:46<190:25, 7.98s/it | [Iter 1659/3090] R0[1569/3000] | LR: 0.011611 | E: -62.874805 | E_var:     4.6572 E_err:   0.033720 | NF_loss: 15.857560
[2025-11-13 04:28:17] 220:54<190:17, 7.98s/it | [Iter 1660/3090] R0[1570/3000] | LR: 0.011598 | E: -62.845165 | E_var:     4.8075 E_err:   0.034259 | NF_loss: 17.841450
[2025-11-13 04:28:25] 221:02<190:09, 7.98s/it | [Iter 1661/3090] R0[1571/3000] | LR: 0.011585 | E: -62.852774 | E_var:     4.4285 E_err:   0.032881 | NF_loss: 23.379807
[2025-11-13 04:28:33] 221:10<190:01, 7.98s/it | [Iter 1662/3090] R0[1572/3000] | LR: 0.011572 | E: -62.819325 | E_var:     4.9250 E_err:   0.034675 | NF_loss: 24.108762
[2025-11-13 04:28:41] 221:18<189:53, 7.98s/it | [Iter 1663/3090] R0[1573/3000] | LR: 0.011558 | E: -62.879297 | E_var:     5.1171 E_err:   0.035345 | NF_loss: 25.161179
[2025-11-13 04:28:49] 221:26<189:45, 7.98s/it | [Iter 1664/3090] R0[1574/3000] | LR: 0.011545 | E: -62.807899 | E_var:     4.5400 E_err:   0.033293 | NF_loss: 20.125523
[2025-11-13 04:28:57] 221:33<189:37, 7.98s/it | [Iter 1665/3090] R0[1575/3000] | LR: 0.011532 | E: -62.814373 | E_var:     4.8366 E_err:   0.034363 | NF_loss: 18.210277
[2025-11-13 04:29:05] 221:41<189:29, 7.98s/it | [Iter 1666/3090] R0[1576/3000] | LR: 0.011519 | E: -62.830876 | E_var:     5.3952 E_err:   0.036293 | NF_loss: 16.509214
[2025-11-13 04:29:13] 221:49<189:21, 7.98s/it | [Iter 1667/3090] R0[1577/3000] | LR: 0.011506 | E: -62.783811 | E_var:     4.7911 E_err:   0.034201 | NF_loss: 18.213982
[2025-11-13 04:29:21] 221:57<189:13, 7.98s/it | [Iter 1668/3090] R0[1578/3000] | LR: 0.011493 | E: -62.709114 | E_var:     4.3341 E_err:   0.032529 | NF_loss: 15.007255
[2025-11-13 04:29:28] 222:05<189:05, 7.98s/it | [Iter 1669/3090] R0[1579/3000] | LR: 0.011480 | E: -62.842355 | E_var:     4.3687 E_err:   0.032659 | NF_loss: 18.593417
[2025-11-13 04:29:36] 222:13<188:57, 7.98s/it | [Iter 1670/3090] R0[1580/3000] | LR: 0.011467 | E: -62.872712 | E_var:     4.3741 E_err:   0.032679 | NF_loss: 18.637533
[2025-11-13 04:29:44] 222:21<188:49, 7.98s/it | [Iter 1671/3090] R0[1581/3000] | LR: 0.011454 | E: -62.832950 | E_var:     4.5207 E_err:   0.033222 | NF_loss: 17.386156
[2025-11-13 04:29:52] 222:29<188:41, 7.98s/it | [Iter 1672/3090] R0[1582/3000] | LR: 0.011441 | E: -62.842008 | E_var:     4.5539 E_err:   0.033344 | NF_loss: 18.104437
[2025-11-13 04:30:00] 222:36<188:33, 7.98s/it | [Iter 1673/3090] R0[1583/3000] | LR: 0.011428 | E: -62.904811 | E_var:     4.7074 E_err:   0.033901 | NF_loss: 17.479663
[2025-11-13 04:30:08] 222:44<188:25, 7.98s/it | [Iter 1674/3090] R0[1584/3000] | LR: 0.011415 | E: -62.886424 | E_var:     4.5220 E_err:   0.033227 | NF_loss: 15.396518
[2025-11-13 04:30:16] 222:52<188:16, 7.98s/it | [Iter 1675/3090] R0[1585/3000] | LR: 0.011402 | E: -62.872535 | E_var:     4.2945 E_err:   0.032380 | NF_loss: 18.688832
[2025-11-13 04:30:24] 223:00<188:08, 7.98s/it | [Iter 1676/3090] R0[1586/3000] | LR: 0.011389 | E: -62.854897 | E_var:     4.6770 E_err:   0.033791 | NF_loss: 14.088148
[2025-11-13 04:30:31] 223:08<188:00, 7.98s/it | [Iter 1677/3090] R0[1587/3000] | LR: 0.011376 | E: -62.870838 | E_var:     4.4660 E_err:   0.033020 | NF_loss: 16.595453
[2025-11-13 04:30:39] 223:16<187:52, 7.98s/it | [Iter 1678/3090] R0[1588/3000] | LR: 0.011363 | E: -62.919347 | E_var:     4.2151 E_err:   0.032079 | NF_loss: 18.037439
[2025-11-13 04:30:47] 223:24<187:44, 7.98s/it | [Iter 1679/3090] R0[1589/3000] | LR: 0.011350 | E: -62.902916 | E_var:     4.2674 E_err:   0.032278 | NF_loss: 17.774796
[2025-11-13 04:30:55] 223:32<187:36, 7.98s/it | [Iter 1680/3090] R0[1590/3000] | LR: 0.011337 | E: -62.831500 | E_var:     4.3970 E_err:   0.032764 | NF_loss: 18.350741
[2025-11-13 04:31:03] 223:39<187:28, 7.98s/it | [Iter 1681/3090] R0[1591/3000] | LR: 0.011324 | E: -62.866305 | E_var:     4.4522 E_err:   0.032969 | NF_loss: 16.549457
[2025-11-13 04:31:11] 223:47<187:20, 7.98s/it | [Iter 1682/3090] R0[1592/3000] | LR: 0.011311 | E: -62.879802 | E_var:     4.6565 E_err:   0.033717 | NF_loss: 21.061131
[2025-11-13 04:31:19] 223:55<187:12, 7.98s/it | [Iter 1683/3090] R0[1593/3000] | LR: 0.011298 | E: -62.894349 | E_var:     4.5957 E_err:   0.033496 | NF_loss: 17.441912
[2025-11-13 04:31:27] 224:03<187:04, 7.98s/it | [Iter 1684/3090] R0[1594/3000] | LR: 0.011285 | E: -62.869947 | E_var:     4.5174 E_err:   0.033210 | NF_loss: 17.104895
[2025-11-13 04:31:34] 224:11<186:56, 7.98s/it | [Iter 1685/3090] R0[1595/3000] | LR: 0.011272 | E: -62.819653 | E_var:     4.5949 E_err:   0.033493 | NF_loss: 16.206821
[2025-11-13 04:31:42] 224:19<186:48, 7.98s/it | [Iter 1686/3090] R0[1596/3000] | LR: 0.011259 | E: -62.841855 | E_var:     4.9392 E_err:   0.034725 | NF_loss: 22.131218
[2025-11-13 04:31:50] 224:27<186:40, 7.98s/it | [Iter 1687/3090] R0[1597/3000] | LR: 0.011246 | E: -62.855870 | E_var:     4.9000 E_err:   0.034587 | NF_loss: 16.155415
[2025-11-13 04:31:58] 224:35<186:31, 7.98s/it | [Iter 1688/3090] R0[1598/3000] | LR: 0.011233 | E: -62.872159 | E_var:     4.5392 E_err:   0.033290 | NF_loss: 15.249254
[2025-11-13 04:32:06] 224:42<186:23, 7.98s/it | [Iter 1689/3090] R0[1599/3000] | LR: 0.011219 | E: -62.783143 | E_var:     4.4389 E_err:   0.032920 | NF_loss: 16.414049
[2025-11-13 04:32:14] 224:50<186:15, 7.98s/it | [Iter 1690/3090] R0[1600/3000] | LR: 0.011206 | E: -62.716107 | E_var:     4.5006 E_err:   0.033148 | NF_loss: 28.097617
[2025-11-13 04:32:22] 224:58<186:07, 7.98s/it | [Iter 1691/3090] R0[1601/3000] | LR: 0.011193 | E: -62.806062 | E_var:     4.4398 E_err:   0.032923 | NF_loss: 19.626911
[2025-11-13 04:32:30] 225:06<185:59, 7.98s/it | [Iter 1692/3090] R0[1602/3000] | LR: 0.011180 | E: -62.731672 | E_var:     4.5278 E_err:   0.033248 | NF_loss: 17.175054
[2025-11-13 04:32:37] 225:14<185:51, 7.98s/it | [Iter 1693/3090] R0[1603/3000] | LR: 0.011167 | E: -62.693579 | E_var:     4.4222 E_err:   0.032858 | NF_loss: 13.455394
[2025-11-13 04:32:46] 225:22<185:43, 7.98s/it | [Iter 1694/3090] R0[1604/3000] | LR: 0.011154 | E: -62.686748 | E_var:     4.5520 E_err:   0.033336 | NF_loss: 13.688992
[2025-11-13 04:32:53] 225:30<185:35, 7.98s/it | [Iter 1695/3090] R0[1605/3000] | LR: 0.011141 | E: -62.636917 | E_var:     4.5414 E_err:   0.033298 | NF_loss: 17.144734
[2025-11-13 04:33:01] 225:38<185:27, 7.98s/it | [Iter 1696/3090] R0[1606/3000] | LR: 0.011128 | E: -62.655740 | E_var:     4.6153 E_err:   0.033567 | NF_loss: 11.819873
[2025-11-13 04:33:09] 225:46<185:19, 7.98s/it | [Iter 1697/3090] R0[1607/3000] | LR: 0.011115 | E: -62.560860 | E_var:     4.7479 E_err:   0.034046 | NF_loss: 13.601105
[2025-11-13 04:33:17] 225:54<185:11, 7.98s/it | [Iter 1698/3090] R0[1608/3000] | LR: 0.011102 | E: -62.537757 | E_var:     4.9422 E_err:   0.034736 | NF_loss: 14.880409
[2025-11-13 04:33:25] 226:02<185:03, 7.98s/it | [Iter 1699/3090] R0[1609/3000] | LR: 0.011089 | E: -62.571827 | E_var:     4.7412 E_err:   0.034022 | NF_loss: 14.640151
[2025-11-13 04:33:33] 226:09<184:55, 7.98s/it | [Iter 1700/3090] R0[1610/3000] | LR: 0.011076 | E: -62.556065 | E_var:     4.8509 E_err:   0.034414 | NF_loss: 15.154926
[2025-11-13 04:33:41] 226:17<184:47, 7.98s/it | [Iter 1701/3090] R0[1611/3000] | LR: 0.011063 | E: -62.479090 | E_var:     4.6569 E_err:   0.033719 | NF_loss: 15.404582
[2025-11-13 04:33:49] 226:25<184:39, 7.98s/it | [Iter 1702/3090] R0[1612/3000] | LR: 0.011050 | E: -62.384338 | E_var:     4.7550 E_err:   0.034072 | NF_loss: 16.941004
[2025-11-13 04:33:56] 226:33<184:31, 7.98s/it | [Iter 1703/3090] R0[1613/3000] | LR: 0.011037 | E: -62.446732 | E_var:     4.7583 E_err:   0.034084 | NF_loss: 16.321546
[2025-11-13 04:34:04] 226:41<184:23, 7.98s/it | [Iter 1704/3090] R0[1614/3000] | LR: 0.011024 | E: -62.437576 | E_var:     4.7664 E_err:   0.034112 | NF_loss: 16.222146
[2025-11-13 04:34:12] 226:49<184:15, 7.98s/it | [Iter 1705/3090] R0[1615/3000] | LR: 0.011011 | E: -62.412273 | E_var:     4.5234 E_err:   0.033232 | NF_loss: 13.364780
[2025-11-13 04:34:20] 226:57<184:06, 7.98s/it | [Iter 1706/3090] R0[1616/3000] | LR: 0.010998 | E: -62.293473 | E_var:     4.9768 E_err:   0.034857 | NF_loss: 15.869762
[2025-11-13 04:34:28] 227:05<183:58, 7.98s/it | [Iter 1707/3090] R0[1617/3000] | LR: 0.010985 | E: -62.445693 | E_var:     4.7366 E_err:   0.034006 | NF_loss: 10.508948
[2025-11-13 04:34:36] 227:12<183:50, 7.98s/it | [Iter 1708/3090] R0[1618/3000] | LR: 0.010972 | E: -62.647513 | E_var:     4.3178 E_err:   0.032468 | NF_loss: 12.745827
[2025-11-13 04:34:44] 227:20<183:42, 7.98s/it | [Iter 1709/3090] R0[1619/3000] | LR: 0.010959 | E: -62.752262 | E_var:     4.7237 E_err:   0.033959 | NF_loss: 14.831936
[2025-11-13 04:34:52] 227:28<183:34, 7.98s/it | [Iter 1710/3090] R0[1620/3000] | LR: 0.010946 | E: -62.694371 | E_var:     4.3826 E_err:   0.032710 | NF_loss: 17.768067
[2025-11-13 04:34:59] 227:36<183:26, 7.98s/it | [Iter 1711/3090] R0[1621/3000] | LR: 0.010933 | E: -62.656176 | E_var:     4.5083 E_err:   0.033176 | NF_loss: 13.830702
[2025-11-13 04:35:07] 227:44<183:18, 7.98s/it | [Iter 1712/3090] R0[1622/3000] | LR: 0.010920 | E: -62.549265 | E_var:     4.4010 E_err:   0.032779 | NF_loss: 15.145067
[2025-11-13 04:35:15] 227:52<183:10, 7.98s/it | [Iter 1713/3090] R0[1623/3000] | LR: 0.010907 | E: -62.749237 | E_var:     4.3449 E_err:   0.032569 | NF_loss: 18.921377
[2025-11-13 04:35:23] 228:00<183:02, 7.98s/it | [Iter 1714/3090] R0[1624/3000] | LR: 0.010894 | E: -62.786853 | E_var:     4.1644 E_err:   0.031886 | NF_loss: 18.130583
[2025-11-13 04:35:31] 228:08<182:54, 7.98s/it | [Iter 1715/3090] R0[1625/3000] | LR: 0.010881 | E: -62.859609 | E_var:     4.2851 E_err:   0.032344 | NF_loss: 15.107907
[2025-11-13 04:35:39] 228:15<182:46, 7.98s/it | [Iter 1716/3090] R0[1626/3000] | LR: 0.010868 | E: -62.893981 | E_var:     4.1366 E_err:   0.031779 | NF_loss: 14.054067
[2025-11-13 04:35:47] 228:23<182:38, 7.98s/it | [Iter 1717/3090] R0[1627/3000] | LR: 0.010856 | E: -62.828954 | E_var:     4.0555 E_err:   0.031466 | NF_loss: 19.014456
[2025-11-13 04:35:55] 228:31<182:30, 7.98s/it | [Iter 1718/3090] R0[1628/3000] | LR: 0.010843 | E: -62.805109 | E_var:     4.0535 E_err:   0.031458 | NF_loss: 17.909719
[2025-11-13 04:36:02] 228:39<182:22, 7.98s/it | [Iter 1719/3090] R0[1629/3000] | LR: 0.010830 | E: -62.839778 | E_var:     4.0970 E_err:   0.031627 | NF_loss: 12.167504
[2025-11-13 04:36:10] 228:47<182:14, 7.98s/it | [Iter 1720/3090] R0[1630/3000] | LR: 0.010817 | E: -62.828114 | E_var:     4.0642 E_err:   0.031500 | NF_loss: 18.704216
[2025-11-13 04:36:18] 228:55<182:05, 7.98s/it | [Iter 1721/3090] R0[1631/3000] | LR: 0.010804 | E: -62.781652 | E_var:     4.2312 E_err:   0.032140 | NF_loss: 23.952417
[2025-11-13 04:36:26] 229:03<181:57, 7.98s/it | [Iter 1722/3090] R0[1632/3000] | LR: 0.010791 | E: -62.859005 | E_var:     4.1200 E_err:   0.031715 | NF_loss: 13.361178
[2025-11-13 04:36:34] 229:11<181:49, 7.98s/it | [Iter 1723/3090] R0[1633/3000] | LR: 0.010778 | E: -62.763717 | E_var:     4.1188 E_err:   0.031711 | NF_loss: 17.218676
[2025-11-13 04:36:42] 229:18<181:41, 7.98s/it | [Iter 1724/3090] R0[1634/3000] | LR: 0.010765 | E: -62.763100 | E_var:     4.1782 E_err:   0.031939 | NF_loss: 23.078648
[2025-11-13 04:36:50] 229:26<181:33, 7.98s/it | [Iter 1725/3090] R0[1635/3000] | LR: 0.010752 | E: -62.735311 | E_var:     4.0470 E_err:   0.031433 | NF_loss: 19.055800
[2025-11-13 04:36:58] 229:34<181:25, 7.98s/it | [Iter 1726/3090] R0[1636/3000] | LR: 0.010739 | E: -62.717304 | E_var:     4.3065 E_err:   0.032425 | NF_loss: 14.143929
[2025-11-13 04:37:05] 229:42<181:17, 7.98s/it | [Iter 1727/3090] R0[1637/3000] | LR: 0.010726 | E: -62.635958 | E_var:     4.2428 E_err:   0.032184 | NF_loss: 14.694047
[2025-11-13 04:37:13] 229:50<181:09, 7.98s/it | [Iter 1728/3090] R0[1638/3000] | LR: 0.010713 | E: -62.676167 | E_var:     3.9230 E_err:   0.030948 | NF_loss: 17.571844
[2025-11-13 04:37:21] 229:58<181:01, 7.98s/it | [Iter 1729/3090] R0[1639/3000] | LR: 0.010700 | E: -62.674049 | E_var:     4.1035 E_err:   0.031652 | NF_loss: 19.828758
[2025-11-13 04:37:29] 230:06<180:53, 7.98s/it | [Iter 1730/3090] R0[1640/3000] | LR: 0.010687 | E: -62.654904 | E_var:     3.9425 E_err:   0.031024 | NF_loss: 16.851029
[2025-11-13 04:37:37] 230:14<180:45, 7.98s/it | [Iter 1731/3090] R0[1641/3000] | LR: 0.010674 | E: -62.749363 | E_var:     3.9290 E_err:   0.030971 | NF_loss: 19.162514
[2025-11-13 04:37:45] 230:21<180:37, 7.98s/it | [Iter 1732/3090] R0[1642/3000] | LR: 0.010661 | E: -62.847515 | E_var:     4.1363 E_err:   0.031778 | NF_loss: 14.100490
[2025-11-13 04:37:53] 230:29<180:29, 7.98s/it | [Iter 1733/3090] R0[1643/3000] | LR: 0.010648 | E: -62.840311 | E_var:     3.9065 E_err:   0.030882 | NF_loss: 16.196965
[2025-11-13 04:38:01] 230:37<180:21, 7.98s/it | [Iter 1734/3090] R0[1644/3000] | LR: 0.010635 | E: -62.817739 | E_var:     4.0003 E_err:   0.031251 | NF_loss: 14.499006
[2025-11-13 04:38:08] 230:45<180:13, 7.98s/it | [Iter 1735/3090] R0[1645/3000] | LR: 0.010622 | E: -62.823617 | E_var:     3.8785 E_err:   0.030772 | NF_loss: 17.443710
[2025-11-13 04:38:16] 230:53<180:05, 7.98s/it | [Iter 1736/3090] R0[1646/3000] | LR: 0.010609 | E: -62.882622 | E_var:     4.0591 E_err:   0.031480 | NF_loss: 18.909200
[2025-11-13 04:38:24] 231:01<179:56, 7.98s/it | [Iter 1737/3090] R0[1647/3000] | LR: 0.010596 | E: -62.893369 | E_var:     3.8053 E_err:   0.030480 | NF_loss: 16.972463
[2025-11-13 04:38:32] 231:09<179:48, 7.98s/it | [Iter 1738/3090] R0[1648/3000] | LR: 0.010583 | E: -62.799864 | E_var:     4.0607 E_err:   0.031486 | NF_loss: 13.153154
[2025-11-13 04:38:40] 231:17<179:41, 7.98s/it | [Iter 1739/3090] R0[1649/3000] | LR: 0.010570 | E: -62.811470 | E_var:     3.9682 E_err:   0.031126 | NF_loss: 12.755091
[2025-11-13 04:38:48] 231:25<179:32, 7.98s/it | [Iter 1740/3090] R0[1650/3000] | LR: 0.010558 | E: -62.694045 | E_var:     3.9844 E_err:   0.031189 | NF_loss: 21.152183
[2025-11-13 04:38:56] 231:33<179:24, 7.98s/it | [Iter 1741/3090] R0[1651/3000] | LR: 0.010545 | E: -62.667662 | E_var:     4.1241 E_err:   0.031731 | NF_loss: 16.235472
[2025-11-13 04:39:04] 231:40<179:16, 7.98s/it | [Iter 1742/3090] R0[1652/3000] | LR: 0.010532 | E: -62.629630 | E_var:     4.1033 E_err:   0.031651 | NF_loss: 16.758895
[2025-11-13 04:39:12] 231:48<179:08, 7.98s/it | [Iter 1743/3090] R0[1653/3000] | LR: 0.010519 | E: -62.680525 | E_var:     4.1615 E_err:   0.031875 | NF_loss: 14.510010
[2025-11-13 04:39:20] 231:56<179:00, 7.98s/it | [Iter 1744/3090] R0[1654/3000] | LR: 0.010506 | E: -62.641152 | E_var:     4.1730 E_err:   0.031918 | NF_loss: 20.027463
[2025-11-13 04:39:28] 232:04<178:52, 7.98s/it | [Iter 1745/3090] R0[1655/3000] | LR: 0.010493 | E: -62.722385 | E_var:     4.1696 E_err:   0.031906 | NF_loss: 17.948151
[2025-11-13 04:39:35] 232:12<178:44, 7.98s/it | [Iter 1746/3090] R0[1656/3000] | LR: 0.010480 | E: -62.758689 | E_var:     4.2253 E_err:   0.032118 | NF_loss: 16.424467
[2025-11-13 04:39:43] 232:20<178:36, 7.98s/it | [Iter 1747/3090] R0[1657/3000] | LR: 0.010467 | E: -62.723489 | E_var:     4.1950 E_err:   0.032003 | NF_loss: 16.209399
[2025-11-13 04:39:51] 232:28<178:28, 7.98s/it | [Iter 1748/3090] R0[1658/3000] | LR: 0.010454 | E: -62.781879 | E_var:     3.9980 E_err:   0.031242 | NF_loss: 14.563493
[2025-11-13 04:39:59] 232:36<178:20, 7.98s/it | [Iter 1749/3090] R0[1659/3000] | LR: 0.010441 | E: -62.804107 | E_var:     4.0687 E_err:   0.031517 | NF_loss: 18.622164
[2025-11-13 04:40:07] 232:43<178:12, 7.98s/it | [Iter 1750/3090] R0[1660/3000] | LR: 0.010428 | E: -62.884963 | E_var:     3.9748 E_err:   0.031152 | NF_loss: 16.064949
[2025-11-13 04:40:15] 232:51<178:04, 7.98s/it | [Iter 1751/3090] R0[1661/3000] | LR: 0.010415 | E: -62.848467 | E_var:     3.9058 E_err:   0.030880 | NF_loss: 17.037490
[2025-11-13 04:40:23] 232:59<177:56, 7.98s/it | [Iter 1752/3090] R0[1662/3000] | LR: 0.010403 | E: -62.886121 | E_var:     4.0678 E_err:   0.031514 | NF_loss: 18.747944
[2025-11-13 04:40:30] 233:07<177:48, 7.98s/it | [Iter 1753/3090] R0[1663/3000] | LR: 0.010390 | E: -62.861476 | E_var:     4.0314 E_err:   0.031372 | NF_loss: 15.954490
[2025-11-13 04:40:38] 233:15<177:40, 7.98s/it | [Iter 1754/3090] R0[1664/3000] | LR: 0.010377 | E: -62.808510 | E_var:     4.2972 E_err:   0.032390 | NF_loss: 12.134854
[2025-11-13 04:40:46] 233:23<177:32, 7.98s/it | [Iter 1755/3090] R0[1665/3000] | LR: 0.010364 | E: -62.778296 | E_var:     4.4744 E_err:   0.033051 | NF_loss: 21.086952
[2025-11-13 04:40:54] 233:31<177:24, 7.98s/it | [Iter 1756/3090] R0[1666/3000] | LR: 0.010351 | E: -62.772929 | E_var:     4.4889 E_err:   0.033105 | NF_loss: 14.928299
[2025-11-13 04:41:02] 233:39<177:15, 7.98s/it | [Iter 1757/3090] R0[1667/3000] | LR: 0.010338 | E: -62.793654 | E_var:     4.2614 E_err:   0.032255 | NF_loss: 16.563187
[2025-11-13 04:41:10] 233:46<177:07, 7.98s/it | [Iter 1758/3090] R0[1668/3000] | LR: 0.010325 | E: -62.764244 | E_var:     4.3996 E_err:   0.032774 | NF_loss: 16.782862
[2025-11-13 04:41:18] 233:54<176:59, 7.98s/it | [Iter 1759/3090] R0[1669/3000] | LR: 0.010312 | E: -62.723883 | E_var:     4.2862 E_err:   0.032348 | NF_loss: 14.980990
[2025-11-13 04:41:26] 234:02<176:51, 7.98s/it | [Iter 1760/3090] R0[1670/3000] | LR: 0.010299 | E: -62.781505 | E_var:     4.1396 E_err:   0.031791 | NF_loss: 19.101566
[2025-11-13 04:41:33] 234:10<176:43, 7.98s/it | [Iter 1761/3090] R0[1671/3000] | LR: 0.010286 | E: -62.654841 | E_var:     4.2265 E_err:   0.032122 | NF_loss: 17.823716
[2025-11-13 04:41:41] 234:18<176:35, 7.98s/it | [Iter 1762/3090] R0[1672/3000] | LR: 0.010274 | E: -62.716582 | E_var:     4.3249 E_err:   0.032494 | NF_loss: 16.894741
[2025-11-13 04:41:49] 234:26<176:27, 7.98s/it | [Iter 1763/3090] R0[1673/3000] | LR: 0.010261 | E: -62.676372 | E_var:     4.3101 E_err:   0.032439 | NF_loss: 20.356894
[2025-11-13 04:41:57] 234:34<176:19, 7.98s/it | [Iter 1764/3090] R0[1674/3000] | LR: 0.010248 | E: -62.727803 | E_var:     4.5079 E_err:   0.033175 | NF_loss: 16.481852
[2025-11-13 04:42:05] 234:42<176:11, 7.98s/it | [Iter 1765/3090] R0[1675/3000] | LR: 0.010235 | E: -62.814570 | E_var:     4.4051 E_err:   0.032794 | NF_loss: 14.079031
[2025-11-13 04:42:13] 234:49<176:03, 7.98s/it | [Iter 1766/3090] R0[1676/3000] | LR: 0.010222 | E: -62.821613 | E_var:     4.4257 E_err:   0.032871 | NF_loss: 17.058175
[2025-11-13 04:42:21] 234:57<175:55, 7.98s/it | [Iter 1767/3090] R0[1677/3000] | LR: 0.010209 | E: -62.805043 | E_var:     4.1311 E_err:   0.031758 | NF_loss: 13.887010
[2025-11-13 04:42:29] 235:05<175:47, 7.98s/it | [Iter 1768/3090] R0[1678/3000] | LR: 0.010196 | E: -62.811726 | E_var:     4.0683 E_err:   0.031516 | NF_loss: 15.805254
[2025-11-13 04:42:37] 235:13<175:39, 7.98s/it | [Iter 1769/3090] R0[1679/3000] | LR: 0.010184 | E: -62.603531 | E_var:     4.2268 E_err:   0.032124 | NF_loss: 16.351724
[2025-11-13 04:42:44] 235:21<175:31, 7.98s/it | [Iter 1770/3090] R0[1680/3000] | LR: 0.010171 | E: -62.715984 | E_var:     4.1943 E_err:   0.032000 | NF_loss: 15.635835
[2025-11-13 04:42:52] 235:29<175:23, 7.98s/it | [Iter 1771/3090] R0[1681/3000] | LR: 0.010158 | E: -62.684435 | E_var:     4.4095 E_err:   0.032811 | NF_loss: 20.414288
[2025-11-13 04:43:00] 235:37<175:15, 7.98s/it | [Iter 1772/3090] R0[1682/3000] | LR: 0.010145 | E: -62.626851 | E_var:     4.4711 E_err:   0.033039 | NF_loss: 13.919250
[2025-11-13 04:43:08] 235:45<175:07, 7.98s/it | [Iter 1773/3090] R0[1683/3000] | LR: 0.010132 | E: -62.618631 | E_var:     4.5393 E_err:   0.033290 | NF_loss: 15.181822
[2025-11-13 04:43:16] 235:52<174:59, 7.98s/it | [Iter 1774/3090] R0[1684/3000] | LR: 0.010119 | E: -62.635527 | E_var:     4.5245 E_err:   0.033236 | NF_loss: 13.889229
[2025-11-13 04:43:24] 236:01<174:51, 7.98s/it | [Iter 1775/3090] R0[1685/3000] | LR: 0.010106 | E: -62.678028 | E_var:     4.5565 E_err:   0.033353 | NF_loss: 18.269660
[2025-11-13 04:43:32] 236:08<174:43, 7.98s/it | [Iter 1776/3090] R0[1686/3000] | LR: 0.010094 | E: -62.695585 | E_var:     4.5098 E_err:   0.033182 | NF_loss: 11.555590
[2025-11-13 04:43:40] 236:16<174:35, 7.98s/it | [Iter 1777/3090] R0[1687/3000] | LR: 0.010081 | E: -62.689395 | E_var:     4.3281 E_err:   0.032507 | NF_loss: 13.721035
[2025-11-13 04:43:48] 236:24<174:27, 7.98s/it | [Iter 1778/3090] R0[1688/3000] | LR: 0.010068 | E: -62.665868 | E_var:     4.3977 E_err:   0.032767 | NF_loss: 12.772297
[2025-11-13 04:43:56] 236:32<174:18, 7.98s/it | [Iter 1779/3090] R0[1689/3000] | LR: 0.010055 | E: -62.654889 | E_var:     4.2525 E_err:   0.032221 | NF_loss: 13.079675
[2025-11-13 04:44:03] 236:40<174:10, 7.98s/it | [Iter 1780/3090] R0[1690/3000] | LR: 0.010042 | E: -62.680780 | E_var:     4.2898 E_err:   0.032362 | NF_loss: 15.480054
[2025-11-13 04:44:11] 236:48<174:02, 7.98s/it | [Iter 1781/3090] R0[1691/3000] | LR: 0.010029 | E: -62.745447 | E_var:     4.3167 E_err:   0.032463 | NF_loss: 13.929983
[2025-11-13 04:44:19] 236:56<173:54, 7.98s/it | [Iter 1782/3090] R0[1692/3000] | LR: 0.010017 | E: -62.817382 | E_var:     4.2398 E_err:   0.032173 | NF_loss: 14.129328
[2025-11-13 04:44:27] 237:04<173:46, 7.98s/it | [Iter 1783/3090] R0[1693/3000] | LR: 0.010004 | E: -62.750223 | E_var:     4.1337 E_err:   0.031768 | NF_loss: 14.787955
[2025-11-13 04:44:35] 237:11<173:38, 7.98s/it | [Iter 1784/3090] R0[1694/3000] | LR: 0.009991 | E: -62.730246 | E_var:     4.3661 E_err:   0.032649 | NF_loss: 14.541238
[2025-11-13 04:44:43] 237:19<173:30, 7.98s/it | [Iter 1785/3090] R0[1695/3000] | LR: 0.009978 | E: -62.712370 | E_var:     4.2611 E_err:   0.032254 | NF_loss: 13.180309
[2025-11-13 04:44:51] 237:27<173:22, 7.98s/it | [Iter 1786/3090] R0[1696/3000] | LR: 0.009965 | E: -62.527965 | E_var:     4.5500 E_err:   0.033329 | NF_loss: 15.279041
[2025-11-13 04:44:59] 237:35<173:14, 7.98s/it | [Iter 1787/3090] R0[1697/3000] | LR: 0.009952 | E: -62.708561 | E_var:     4.4011 E_err:   0.032779 | NF_loss: 12.246860
[2025-11-13 04:45:06] 237:43<173:06, 7.98s/it | [Iter 1788/3090] R0[1698/3000] | LR: 0.009940 | E: -62.610893 | E_var:     4.3756 E_err:   0.032684 | NF_loss: 12.739758
[2025-11-13 04:45:14] 237:51<172:58, 7.98s/it | [Iter 1789/3090] R0[1699/3000] | LR: 0.009927 | E: -62.505125 | E_var:     4.3785 E_err:   0.032695 | NF_loss: 15.283343
[2025-11-13 04:45:22] 237:59<172:50, 7.98s/it | [Iter 1790/3090] R0[1700/3000] | LR: 0.009914 | E: -62.682239 | E_var:     4.3198 E_err:   0.032475 | NF_loss: 14.891205
[2025-11-13 04:45:30] 238:07<172:42, 7.98s/it | [Iter 1791/3090] R0[1701/3000] | LR: 0.009901 | E: -62.690766 | E_var:     4.4506 E_err:   0.032963 | NF_loss: 14.035309
[2025-11-13 04:45:38] 238:14<172:34, 7.98s/it | [Iter 1792/3090] R0[1702/3000] | LR: 0.009888 | E: -62.660719 | E_var:     4.4691 E_err:   0.033032 | NF_loss: 13.962251
[2025-11-13 04:45:46] 238:22<172:26, 7.98s/it | [Iter 1793/3090] R0[1703/3000] | LR: 0.009876 | E: -62.596575 | E_var:     4.2981 E_err:   0.032394 | NF_loss: 15.141558
[2025-11-13 04:45:54] 238:30<172:18, 7.98s/it | [Iter 1794/3090] R0[1704/3000] | LR: 0.009863 | E: -62.499545 | E_var:     4.4423 E_err:   0.032932 | NF_loss: 13.986159
[2025-11-13 04:46:02] 238:38<172:10, 7.98s/it | [Iter 1795/3090] R0[1705/3000] | LR: 0.009850 | E: -62.532663 | E_var:     4.3135 E_err:   0.032452 | NF_loss: 14.477884
[2025-11-13 04:46:09] 238:46<172:02, 7.98s/it | [Iter 1796/3090] R0[1706/3000] | LR: 0.009837 | E: -62.706455 | E_var:     4.2473 E_err:   0.032202 | NF_loss: 18.049997
[2025-11-13 04:46:17] 238:54<171:54, 7.98s/it | [Iter 1797/3090] R0[1707/3000] | LR: 0.009824 | E: -62.640920 | E_var:     4.1611 E_err:   0.031873 | NF_loss: 14.131999
[2025-11-13 04:46:25] 239:02<171:45, 7.98s/it | [Iter 1798/3090] R0[1708/3000] | LR: 0.009812 | E: -62.668099 | E_var:     4.3572 E_err:   0.032616 | NF_loss: 17.201376
[2025-11-13 04:46:33] 239:10<171:37, 7.98s/it | [Iter 1799/3090] R0[1709/3000] | LR: 0.009799 | E: -62.601547 | E_var:     4.3345 E_err:   0.032531 | NF_loss: 16.018250
[2025-11-13 04:46:41] 239:17<171:29, 7.98s/it | [Iter 1800/3090] R0[1710/3000] | LR: 0.009786 | E: -62.734003 | E_var:     4.2770 E_err:   0.032314 | NF_loss: 17.590710
[2025-11-13 04:46:41] 保存checkpoint: hybrid_checkpoint_iter_001800.pkl
[2025-11-13 04:46:49] 239:26<171:21, 7.98s/it | [Iter 1801/3090] R0[1711/3000] | LR: 0.009773 | E: -62.707212 | E_var:     4.1538 E_err:   0.031845 | NF_loss: 14.192622
[2025-11-13 04:46:57] 239:33<171:13, 7.98s/it | [Iter 1802/3090] R0[1712/3000] | LR: 0.009760 | E: -62.692412 | E_var:     4.2808 E_err:   0.032328 | NF_loss: 17.695210
[2025-11-13 04:47:05] 239:41<171:05, 7.98s/it | [Iter 1803/3090] R0[1713/3000] | LR: 0.009748 | E: -62.602937 | E_var:     4.3345 E_err:   0.032531 | NF_loss: 14.191853
[2025-11-13 04:47:13] 239:49<170:58, 7.98s/it | [Iter 1804/3090] R0[1714/3000] | LR: 0.009735 | E: -62.704356 | E_var:     4.3468 E_err:   0.032576 | NF_loss: 13.669358
[2025-11-13 04:47:21] 239:57<170:49, 7.98s/it | [Iter 1805/3090] R0[1715/3000] | LR: 0.009722 | E: -62.651577 | E_var:     4.3222 E_err:   0.032484 | NF_loss: 16.790859
[2025-11-13 04:47:29] 240:05<170:41, 7.98s/it | [Iter 1806/3090] R0[1716/3000] | LR: 0.009709 | E: -62.590088 | E_var:     4.3202 E_err:   0.032477 | NF_loss: 15.532515
[2025-11-13 04:47:37] 240:13<170:33, 7.98s/it | [Iter 1807/3090] R0[1717/3000] | LR: 0.009697 | E: -62.586978 | E_var:     4.5118 E_err:   0.033189 | NF_loss: 17.001353
[2025-11-13 04:47:44] 240:21<170:25, 7.98s/it | [Iter 1808/3090] R0[1718/3000] | LR: 0.009684 | E: -62.581599 | E_var:     4.4817 E_err:   0.033078 | NF_loss: 14.392455
[2025-11-13 04:47:52] 240:29<170:17, 7.98s/it | [Iter 1809/3090] R0[1719/3000] | LR: 0.009671 | E: -62.725926 | E_var:     4.2130 E_err:   0.032071 | NF_loss: 14.949537
[2025-11-13 04:48:00] 240:37<170:09, 7.98s/it | [Iter 1810/3090] R0[1720/3000] | LR: 0.009658 | E: -62.713372 | E_var:     4.5613 E_err:   0.033371 | NF_loss: 16.007103
[2025-11-13 04:48:08] 240:45<170:01, 7.98s/it | [Iter 1811/3090] R0[1721/3000] | LR: 0.009646 | E: -62.734187 | E_var:     4.0901 E_err:   0.031600 | NF_loss: 15.730094
[2025-11-13 04:48:16] 240:52<169:53, 7.98s/it | [Iter 1812/3090] R0[1722/3000] | LR: 0.009633 | E: -62.748046 | E_var:     4.0965 E_err:   0.031625 | NF_loss: 15.892523
[2025-11-13 04:48:24] 241:00<169:45, 7.98s/it | [Iter 1813/3090] R0[1723/3000] | LR: 0.009620 | E: -62.800438 | E_var:     4.2721 E_err:   0.032295 | NF_loss: 16.303115
[2025-11-13 04:48:32] 241:08<169:37, 7.98s/it | [Iter 1814/3090] R0[1724/3000] | LR: 0.009607 | E: -62.818145 | E_var:     3.9976 E_err:   0.031240 | NF_loss: 15.774861
[2025-11-13 04:48:40] 241:16<169:29, 7.98s/it | [Iter 1815/3090] R0[1725/3000] | LR: 0.009595 | E: -62.784346 | E_var:     4.2149 E_err:   0.032079 | NF_loss: 16.540295
[2025-11-13 04:48:47] 241:24<169:21, 7.98s/it | [Iter 1816/3090] R0[1726/3000] | LR: 0.009582 | E: -62.774774 | E_var:     4.0388 E_err:   0.031401 | NF_loss: 15.448691
[2025-11-13 04:48:55] 241:32<169:13, 7.98s/it | [Iter 1817/3090] R0[1727/3000] | LR: 0.009569 | E: -62.724230 | E_var:     4.0940 E_err:   0.031615 | NF_loss: 14.604418
[2025-11-13 04:49:03] 241:40<169:05, 7.98s/it | [Iter 1818/3090] R0[1728/3000] | LR: 0.009557 | E: -62.748806 | E_var:     4.1614 E_err:   0.031874 | NF_loss: 17.641585
[2025-11-13 04:49:11] 241:48<168:57, 7.98s/it | [Iter 1819/3090] R0[1729/3000] | LR: 0.009544 | E: -62.796329 | E_var:     4.1339 E_err:   0.031769 | NF_loss: 23.014679
[2025-11-13 04:49:19] 241:56<168:49, 7.98s/it | [Iter 1820/3090] R0[1730/3000] | LR: 0.009531 | E: -62.812243 | E_var:     4.2800 E_err:   0.032325 | NF_loss: 15.233711
[2025-11-13 04:49:27] 242:03<168:41, 7.98s/it | [Iter 1821/3090] R0[1731/3000] | LR: 0.009518 | E: -62.812461 | E_var:     4.2385 E_err:   0.032168 | NF_loss: 20.169345
[2025-11-13 04:49:35] 242:11<168:33, 7.98s/it | [Iter 1822/3090] R0[1732/3000] | LR: 0.009506 | E: -62.789634 | E_var:     4.2863 E_err:   0.032349 | NF_loss: 14.477840
[2025-11-13 04:49:43] 242:19<168:25, 7.98s/it | [Iter 1823/3090] R0[1733/3000] | LR: 0.009493 | E: -62.774011 | E_var:     4.3107 E_err:   0.032441 | NF_loss: 15.254649
[2025-11-13 04:49:50] 242:27<168:17, 7.98s/it | [Iter 1824/3090] R0[1734/3000] | LR: 0.009480 | E: -62.860817 | E_var:     4.2668 E_err:   0.032275 | NF_loss: 16.430745
[2025-11-13 04:49:58] 242:35<168:09, 7.98s/it | [Iter 1825/3090] R0[1735/3000] | LR: 0.009468 | E: -62.750783 | E_var:     4.0152 E_err:   0.031309 | NF_loss: 13.535685
[2025-11-13 04:50:06] 242:43<168:01, 7.98s/it | [Iter 1826/3090] R0[1736/3000] | LR: 0.009455 | E: -62.792391 | E_var:     4.1936 E_err:   0.031997 | NF_loss: 14.305380
[2025-11-13 04:50:14] 242:51<167:52, 7.98s/it | [Iter 1827/3090] R0[1737/3000] | LR: 0.009442 | E: -62.826784 | E_var:     3.9715 E_err:   0.031138 | NF_loss: 17.447949
[2025-11-13 04:50:22] 242:59<167:44, 7.98s/it | [Iter 1828/3090] R0[1738/3000] | LR: 0.009429 | E: -62.650421 | E_var:     4.0669 E_err:   0.031510 | NF_loss: 14.876247
[2025-11-13 04:50:30] 243:06<167:36, 7.98s/it | [Iter 1829/3090] R0[1739/3000] | LR: 0.009417 | E: -62.811129 | E_var:     4.0150 E_err:   0.031309 | NF_loss: 14.409481
[2025-11-13 04:50:38] 243:14<167:28, 7.98s/it | [Iter 1830/3090] R0[1740/3000] | LR: 0.009404 | E: -62.830415 | E_var:     3.8640 E_err:   0.030714 | NF_loss: 16.320949
[2025-11-13 04:50:46] 243:22<167:20, 7.98s/it | [Iter 1831/3090] R0[1741/3000] | LR: 0.009391 | E: -62.768373 | E_var:     4.0389 E_err:   0.031401 | NF_loss: 15.268571
[2025-11-13 04:50:54] 243:30<167:12, 7.98s/it | [Iter 1832/3090] R0[1742/3000] | LR: 0.009379 | E: -62.680962 | E_var:     4.0717 E_err:   0.031529 | NF_loss: 17.666977
[2025-11-13 04:51:01] 243:38<167:04, 7.98s/it | [Iter 1833/3090] R0[1743/3000] | LR: 0.009366 | E: -62.752229 | E_var:     3.9978 E_err:   0.031241 | NF_loss: 17.567730
[2025-11-13 04:51:09] 243:46<166:56, 7.98s/it | [Iter 1834/3090] R0[1744/3000] | LR: 0.009353 | E: -62.837568 | E_var:     4.0170 E_err:   0.031316 | NF_loss: 15.380645
[2025-11-13 04:51:17] 243:54<166:48, 7.98s/it | [Iter 1835/3090] R0[1745/3000] | LR: 0.009341 | E: -62.830179 | E_var:     3.9373 E_err:   0.031004 | NF_loss: 13.494457
[2025-11-13 04:51:25] 244:02<166:40, 7.98s/it | [Iter 1836/3090] R0[1746/3000] | LR: 0.009328 | E: -62.840862 | E_var:     4.0123 E_err:   0.031298 | NF_loss: 17.749142
[2025-11-13 04:51:33] 244:10<166:32, 7.98s/it | [Iter 1837/3090] R0[1747/3000] | LR: 0.009315 | E: -62.853447 | E_var:     3.8248 E_err:   0.030558 | NF_loss: 18.007683
[2025-11-13 04:51:41] 244:18<166:24, 7.98s/it | [Iter 1838/3090] R0[1748/3000] | LR: 0.009303 | E: -62.910597 | E_var:     3.9974 E_err:   0.031240 | NF_loss: 15.069808
[2025-11-13 04:51:49] 244:25<166:16, 7.97s/it | [Iter 1839/3090] R0[1749/3000] | LR: 0.009290 | E: -62.878105 | E_var:     3.9165 E_err:   0.030922 | NF_loss: 16.514449
[2025-11-13 04:51:57] 244:33<166:08, 7.97s/it | [Iter 1840/3090] R0[1750/3000] | LR: 0.009277 | E: -62.838492 | E_var:     3.9186 E_err:   0.030930 | NF_loss: 23.232696
[2025-11-13 04:52:05] 244:41<166:00, 7.97s/it | [Iter 1841/3090] R0[1751/3000] | LR: 0.009265 | E: -62.844963 | E_var:     3.8877 E_err:   0.030808 | NF_loss: 22.325713
[2025-11-13 04:52:13] 244:49<165:52, 7.97s/it | [Iter 1842/3090] R0[1752/3000] | LR: 0.009252 | E: -62.887853 | E_var:     4.1727 E_err:   0.031918 | NF_loss: 17.204136
[2025-11-13 04:52:20] 244:57<165:44, 7.97s/it | [Iter 1843/3090] R0[1753/3000] | LR: 0.009240 | E: -62.886356 | E_var:     4.0722 E_err:   0.031531 | NF_loss: 22.612474
[2025-11-13 04:52:28] 245:05<165:36, 7.97s/it | [Iter 1844/3090] R0[1754/3000] | LR: 0.009227 | E: -62.892845 | E_var:     3.9819 E_err:   0.031179 | NF_loss: 18.730788
[2025-11-13 04:52:36] 245:13<165:28, 7.97s/it | [Iter 1845/3090] R0[1755/3000] | LR: 0.009214 | E: -62.895328 | E_var:     3.9642 E_err:   0.031110 | NF_loss: 22.590818
[2025-11-13 04:52:44] 245:21<165:20, 7.97s/it | [Iter 1846/3090] R0[1756/3000] | LR: 0.009202 | E: -62.849129 | E_var:     4.3106 E_err:   0.032441 | NF_loss: 20.521916
[2025-11-13 04:52:52] 245:28<165:12, 7.97s/it | [Iter 1847/3090] R0[1757/3000] | LR: 0.009189 | E: -62.961517 | E_var:     4.0248 E_err:   0.031347 | NF_loss: 19.456608
[2025-11-13 04:53:00] 245:36<165:04, 7.97s/it | [Iter 1848/3090] R0[1758/3000] | LR: 0.009176 | E: -62.951068 | E_var:     3.9239 E_err:   0.030951 | NF_loss: 18.102151
[2025-11-13 04:53:08] 245:44<164:56, 7.97s/it | [Iter 1849/3090] R0[1759/3000] | LR: 0.009164 | E: -62.801125 | E_var:     3.9331 E_err:   0.030987 | NF_loss: 17.788883
[2025-11-13 04:53:16] 245:52<164:48, 7.97s/it | [Iter 1850/3090] R0[1760/3000] | LR: 0.009151 | E: -62.857526 | E_var:     3.8739 E_err:   0.030753 | NF_loss: 17.302196
[2025-11-13 04:53:23] 246:00<164:40, 7.97s/it | [Iter 1851/3090] R0[1761/3000] | LR: 0.009139 | E: -62.739490 | E_var:     4.0025 E_err:   0.031260 | NF_loss: 17.506170
[2025-11-13 04:53:31] 246:08<164:32, 7.97s/it | [Iter 1852/3090] R0[1762/3000] | LR: 0.009126 | E: -62.797463 | E_var:     3.9424 E_err:   0.031024 | NF_loss: 19.684993
[2025-11-13 04:53:39] 246:16<164:24, 7.97s/it | [Iter 1853/3090] R0[1763/3000] | LR: 0.009113 | E: -62.666815 | E_var:     4.0492 E_err:   0.031442 | NF_loss: 19.280827
[2025-11-13 04:53:47] 246:24<164:16, 7.97s/it | [Iter 1854/3090] R0[1764/3000] | LR: 0.009101 | E: -62.582649 | E_var:     4.1525 E_err:   0.031840 | NF_loss: 18.299769
[2025-11-13 04:53:55] 246:32<164:08, 7.97s/it | [Iter 1855/3090] R0[1765/3000] | LR: 0.009088 | E: -62.637257 | E_var:     4.0524 E_err:   0.031454 | NF_loss: 18.130734
[2025-11-13 04:54:03] 246:39<164:00, 7.97s/it | [Iter 1856/3090] R0[1766/3000] | LR: 0.009076 | E: -62.645547 | E_var:     4.1222 E_err:   0.031724 | NF_loss: 17.369376
[2025-11-13 04:54:11] 246:47<163:51, 7.97s/it | [Iter 1857/3090] R0[1767/3000] | LR: 0.009063 | E: -62.706405 | E_var:     4.0044 E_err:   0.031267 | NF_loss: 23.260006
[2025-11-13 04:54:19] 246:55<163:43, 7.97s/it | [Iter 1858/3090] R0[1768/3000] | LR: 0.009050 | E: -62.707355 | E_var:     4.0841 E_err:   0.031577 | NF_loss: 18.605491
[2025-11-13 04:54:27] 247:03<163:35, 7.97s/it | [Iter 1859/3090] R0[1769/3000] | LR: 0.009038 | E: -62.719472 | E_var:     3.9343 E_err:   0.030992 | NF_loss: 17.042088
[2025-11-13 04:54:34] 247:11<163:27, 7.97s/it | [Iter 1860/3090] R0[1770/3000] | LR: 0.009025 | E: -62.626877 | E_var:     4.0377 E_err:   0.031397 | NF_loss: 18.128205
[2025-11-13 04:54:42] 247:19<163:19, 7.97s/it | [Iter 1861/3090] R0[1771/3000] | LR: 0.009013 | E: -62.623823 | E_var:     4.1156 E_err:   0.031699 | NF_loss: 19.494437
[2025-11-13 04:54:50] 247:27<163:11, 7.97s/it | [Iter 1862/3090] R0[1772/3000] | LR: 0.009000 | E: -62.544803 | E_var:     4.1706 E_err:   0.031909 | NF_loss: 18.133659
[2025-11-13 04:54:58] 247:35<163:03, 7.97s/it | [Iter 1863/3090] R0[1773/3000] | LR: 0.008988 | E: -62.627443 | E_var:     4.1606 E_err:   0.031871 | NF_loss: 19.866413
[2025-11-13 04:55:06] 247:42<162:55, 7.97s/it | [Iter 1864/3090] R0[1774/3000] | LR: 0.008975 | E: -62.495677 | E_var:     4.1429 E_err:   0.031803 | NF_loss: 18.907284
[2025-11-13 04:55:14] 247:50<162:47, 7.97s/it | [Iter 1865/3090] R0[1775/3000] | LR: 0.008962 | E: -62.414195 | E_var:     4.2895 E_err:   0.032361 | NF_loss: 22.257186
[2025-11-13 04:55:22] 247:58<162:39, 7.97s/it | [Iter 1866/3090] R0[1776/3000] | LR: 0.008950 | E: -62.518138 | E_var:     4.1543 E_err:   0.031847 | NF_loss: 15.889342
[2025-11-13 04:55:30] 248:06<162:31, 7.97s/it | [Iter 1867/3090] R0[1777/3000] | LR: 0.008937 | E: -62.723171 | E_var:     3.9092 E_err:   0.030893 | NF_loss: 22.434599
[2025-11-13 04:55:37] 248:14<162:23, 7.97s/it | [Iter 1868/3090] R0[1778/3000] | LR: 0.008925 | E: -62.761333 | E_var:     3.9610 E_err:   0.031097 | NF_loss: 20.293548
[2025-11-13 04:55:45] 248:22<162:15, 7.97s/it | [Iter 1869/3090] R0[1779/3000] | LR: 0.008912 | E: -62.805941 | E_var:     3.8293 E_err:   0.030576 | NF_loss: 13.596815
[2025-11-13 04:55:53] 248:30<162:07, 7.97s/it | [Iter 1870/3090] R0[1780/3000] | LR: 0.008900 | E: -62.861282 | E_var:     3.8481 E_err:   0.030651 | NF_loss: 22.227058
[2025-11-13 04:56:01] 248:38<161:59, 7.97s/it | [Iter 1871/3090] R0[1781/3000] | LR: 0.008887 | E: -62.742528 | E_var:     4.0714 E_err:   0.031528 | NF_loss: 13.501290
[2025-11-13 04:56:09] 248:45<161:51, 7.97s/it | [Iter 1872/3090] R0[1782/3000] | LR: 0.008875 | E: -62.630220 | E_var:     4.1797 E_err:   0.031944 | NF_loss: 16.225685
[2025-11-13 04:56:17] 248:53<161:43, 7.97s/it | [Iter 1873/3090] R0[1783/3000] | LR: 0.008862 | E: -62.680228 | E_var:     4.0374 E_err:   0.031396 | NF_loss: 29.683443
[2025-11-13 04:56:25] 249:01<161:35, 7.97s/it | [Iter 1874/3090] R0[1784/3000] | LR: 0.008850 | E: -62.613419 | E_var:     4.2925 E_err:   0.032373 | NF_loss: 12.212293
[2025-11-13 04:56:33] 249:09<161:27, 7.97s/it | [Iter 1875/3090] R0[1785/3000] | LR: 0.008837 | E: -62.650756 | E_var:     4.0804 E_err:   0.031562 | NF_loss: 16.998406
[2025-11-13 04:56:40] 249:17<161:19, 7.97s/it | [Iter 1876/3090] R0[1786/3000] | LR: 0.008825 | E: -62.680662 | E_var:     4.0051 E_err:   0.031270 | NF_loss: 31.610458
[2025-11-13 04:56:48] 249:25<161:11, 7.97s/it | [Iter 1877/3090] R0[1787/3000] | LR: 0.008812 | E: -62.739211 | E_var:     4.0221 E_err:   0.031336 | NF_loss: 22.039996
[2025-11-13 04:56:56] 249:33<161:03, 7.97s/it | [Iter 1878/3090] R0[1788/3000] | LR: 0.008800 | E: -62.690181 | E_var:     4.1860 E_err:   0.031968 | NF_loss: 20.700380
[2025-11-13 04:57:04] 249:41<160:55, 7.97s/it | [Iter 1879/3090] R0[1789/3000] | LR: 0.008787 | E: -62.769474 | E_var:     4.1411 E_err:   0.031796 | NF_loss: 16.506991
[2025-11-13 04:57:12] 249:48<160:47, 7.97s/it | [Iter 1880/3090] R0[1790/3000] | LR: 0.008775 | E: -62.793381 | E_var:     3.9061 E_err:   0.030881 | NF_loss: 17.215184
[2025-11-13 04:57:20] 249:57<160:39, 7.97s/it | [Iter 1881/3090] R0[1791/3000] | LR: 0.008762 | E: -62.756293 | E_var:     4.0047 E_err:   0.031268 | NF_loss: 17.816791
[2025-11-13 04:57:28] 250:04<160:31, 7.97s/it | [Iter 1882/3090] R0[1792/3000] | LR: 0.008750 | E: -62.740493 | E_var:     4.0541 E_err:   0.031461 | NF_loss: 17.924384
[2025-11-13 04:57:36] 250:12<160:23, 7.97s/it | [Iter 1883/3090] R0[1793/3000] | LR: 0.008737 | E: -62.739406 | E_var:     4.0455 E_err:   0.031427 | NF_loss: 25.569303
[2025-11-13 04:57:44] 250:20<160:15, 7.97s/it | [Iter 1884/3090] R0[1794/3000] | LR: 0.008725 | E: -62.747192 | E_var:     3.9263 E_err:   0.030961 | NF_loss: 15.973913
[2025-11-13 04:57:52] 250:28<160:07, 7.97s/it | [Iter 1885/3090] R0[1795/3000] | LR: 0.008712 | E: -62.761555 | E_var:     4.0020 E_err:   0.031258 | NF_loss: 17.725593
[2025-11-13 04:57:59] 250:36<159:59, 7.97s/it | [Iter 1886/3090] R0[1796/3000] | LR: 0.008700 | E: -62.757703 | E_var:     3.9170 E_err:   0.030924 | NF_loss: 17.830961
[2025-11-13 04:58:07] 250:44<159:51, 7.97s/it | [Iter 1887/3090] R0[1797/3000] | LR: 0.008687 | E: -62.824748 | E_var:     4.1437 E_err:   0.031806 | NF_loss: 20.543648
[2025-11-13 04:58:15] 250:52<159:43, 7.97s/it | [Iter 1888/3090] R0[1798/3000] | LR: 0.008675 | E: -62.937662 | E_var:     4.0501 E_err:   0.031445 | NF_loss: 13.078446
[2025-11-13 04:58:23] 251:00<159:35, 7.97s/it | [Iter 1889/3090] R0[1799/3000] | LR: 0.008662 | E: -62.913324 | E_var:     4.1893 E_err:   0.031981 | NF_loss: 16.235409
[2025-11-13 04:58:31] 251:08<159:27, 7.97s/it | [Iter 1890/3090] R0[1800/3000] | LR: 0.008650 | E: -62.911855 | E_var:     3.6942 E_err:   0.030032 | NF_loss: 16.053042
[2025-11-13 04:58:39] 251:15<159:18, 7.97s/it | [Iter 1891/3090] R0[1801/3000] | LR: 0.008637 | E: -62.763619 | E_var:     3.9087 E_err:   0.030891 | NF_loss: 16.414912
[2025-11-13 04:58:47] 251:23<159:10, 7.97s/it | [Iter 1892/3090] R0[1802/3000] | LR: 0.008625 | E: -62.847022 | E_var:     3.8610 E_err:   0.030702 | NF_loss: 16.590851
[2025-11-13 04:58:55] 251:31<159:02, 7.97s/it | [Iter 1893/3090] R0[1803/3000] | LR: 0.008612 | E: -62.927392 | E_var:     3.8293 E_err:   0.030576 | NF_loss: 16.019475
[2025-11-13 04:59:03] 251:39<158:54, 7.97s/it | [Iter 1894/3090] R0[1804/3000] | LR: 0.008600 | E: -62.841838 | E_var:     3.9291 E_err:   0.030972 | NF_loss: 15.065188
[2025-11-13 04:59:10] 251:47<158:46, 7.97s/it | [Iter 1895/3090] R0[1805/3000] | LR: 0.008588 | E: -62.846084 | E_var:     3.8390 E_err:   0.030615 | NF_loss: 17.341767
[2025-11-13 04:59:18] 251:55<158:38, 7.97s/it | [Iter 1896/3090] R0[1806/3000] | LR: 0.008575 | E: -62.887789 | E_var:     3.9893 E_err:   0.031208 | NF_loss: 18.505537
[2025-11-13 04:59:26] 252:03<158:30, 7.97s/it | [Iter 1897/3090] R0[1807/3000] | LR: 0.008563 | E: -62.910987 | E_var:     3.8164 E_err:   0.030525 | NF_loss: 18.137360
[2025-11-13 04:59:34] 252:11<158:22, 7.97s/it | [Iter 1898/3090] R0[1808/3000] | LR: 0.008550 | E: -62.818398 | E_var:     3.9548 E_err:   0.031073 | NF_loss: 17.811254
[2025-11-13 04:59:42] 252:18<158:14, 7.97s/it | [Iter 1899/3090] R0[1809/3000] | LR: 0.008538 | E: -62.805166 | E_var:     3.8854 E_err:   0.030799 | NF_loss: 17.338768
[2025-11-13 04:59:50] 252:26<158:06, 7.97s/it | [Iter 1900/3090] R0[1810/3000] | LR: 0.008525 | E: -62.746098 | E_var:     3.9415 E_err:   0.031020 | NF_loss: 21.005885
[2025-11-13 04:59:58] 252:34<157:58, 7.97s/it | [Iter 1901/3090] R0[1811/3000] | LR: 0.008513 | E: -62.879646 | E_var:     3.9436 E_err:   0.031029 | NF_loss: 18.362868
[2025-11-13 05:00:06] 252:42<157:50, 7.97s/it | [Iter 1902/3090] R0[1812/3000] | LR: 0.008501 | E: -62.874316 | E_var:     3.8119 E_err:   0.030506 | NF_loss: 18.367555
[2025-11-13 05:00:13] 252:50<157:42, 7.97s/it | [Iter 1903/3090] R0[1813/3000] | LR: 0.008488 | E: -62.882130 | E_var:     3.9287 E_err:   0.030970 | NF_loss: 18.118108
[2025-11-13 05:00:21] 252:58<157:34, 7.97s/it | [Iter 1904/3090] R0[1814/3000] | LR: 0.008476 | E: -62.904771 | E_var:     3.7985 E_err:   0.030453 | NF_loss: 14.756456
[2025-11-13 05:00:29] 253:06<157:26, 7.97s/it | [Iter 1905/3090] R0[1815/3000] | LR: 0.008463 | E: -62.938575 | E_var:     3.9169 E_err:   0.030924 | NF_loss: 16.548060
[2025-11-13 05:00:37] 253:14<157:18, 7.97s/it | [Iter 1906/3090] R0[1816/3000] | LR: 0.008451 | E: -62.879281 | E_var:     3.9485 E_err:   0.031048 | NF_loss: 15.925208
[2025-11-13 05:00:45] 253:22<157:10, 7.97s/it | [Iter 1907/3090] R0[1817/3000] | LR: 0.008439 | E: -62.790053 | E_var:     3.8647 E_err:   0.030717 | NF_loss: 14.857777
[2025-11-13 05:00:53] 253:29<157:02, 7.97s/it | [Iter 1908/3090] R0[1818/3000] | LR: 0.008426 | E: -62.859239 | E_var:     3.6848 E_err:   0.029994 | NF_loss: 16.539201
[2025-11-13 05:01:01] 253:37<156:54, 7.97s/it | [Iter 1909/3090] R0[1819/3000] | LR: 0.008414 | E: -62.940652 | E_var:     4.0474 E_err:   0.031435 | NF_loss: 18.114373
[2025-11-13 05:01:09] 253:45<156:46, 7.97s/it | [Iter 1910/3090] R0[1820/3000] | LR: 0.008402 | E: -62.982241 | E_var:     3.8455 E_err:   0.030641 | NF_loss: 18.779170
[2025-11-13 05:01:16] 253:53<156:38, 7.97s/it | [Iter 1911/3090] R0[1821/3000] | LR: 0.008389 | E: -62.969415 | E_var:     4.0732 E_err:   0.031535 | NF_loss: 17.338965
[2025-11-13 05:01:24] 254:01<156:30, 7.97s/it | [Iter 1912/3090] R0[1822/3000] | LR: 0.008377 | E: -62.943569 | E_var:     3.8178 E_err:   0.030530 | NF_loss: 18.946177
[2025-11-13 05:01:32] 254:09<156:22, 7.97s/it | [Iter 1913/3090] R0[1823/3000] | LR: 0.008365 | E: -62.979351 | E_var:     4.1268 E_err:   0.031741 | NF_loss: 16.114317
[2025-11-13 05:01:40] 254:17<156:14, 7.97s/it | [Iter 1914/3090] R0[1824/3000] | LR: 0.008352 | E: -62.967629 | E_var:     3.8832 E_err:   0.030791 | NF_loss: 15.446933
[2025-11-13 05:01:48] 254:24<156:06, 7.97s/it | [Iter 1915/3090] R0[1825/3000] | LR: 0.008340 | E: -62.967751 | E_var:     3.6642 E_err:   0.029909 | NF_loss: 16.264526
[2025-11-13 05:01:56] 254:32<155:58, 7.97s/it | [Iter 1916/3090] R0[1826/3000] | LR: 0.008327 | E: -63.008383 | E_var:     4.0884 E_err:   0.031594 | NF_loss: 14.568755
[2025-11-13 05:02:04] 254:40<155:50, 7.97s/it | [Iter 1917/3090] R0[1827/3000] | LR: 0.008315 | E: -62.934764 | E_var:     3.6763 E_err:   0.029959 | NF_loss: 19.798775
[2025-11-13 05:02:12] 254:48<155:42, 7.97s/it | [Iter 1918/3090] R0[1828/3000] | LR: 0.008303 | E: -62.945261 | E_var:     3.6751 E_err:   0.029954 | NF_loss: 15.911617
[2025-11-13 05:02:20] 254:56<155:34, 7.97s/it | [Iter 1919/3090] R0[1829/3000] | LR: 0.008290 | E: -62.887807 | E_var:     3.9147 E_err:   0.030915 | NF_loss: 14.421446
[2025-11-13 05:02:28] 255:04<155:26, 7.97s/it | [Iter 1920/3090] R0[1830/3000] | LR: 0.008278 | E: -62.825729 | E_var:     3.8597 E_err:   0.030697 | NF_loss: 12.730220
[2025-11-13 05:02:35] 255:12<155:18, 7.97s/it | [Iter 1921/3090] R0[1831/3000] | LR: 0.008266 | E: -62.832238 | E_var:     3.8059 E_err:   0.030482 | NF_loss: 22.466287
[2025-11-13 05:02:43] 255:20<155:10, 7.97s/it | [Iter 1922/3090] R0[1832/3000] | LR: 0.008254 | E: -62.909122 | E_var:     3.9573 E_err:   0.031083 | NF_loss: 15.206910
[2025-11-13 05:02:51] 255:28<155:02, 7.97s/it | [Iter 1923/3090] R0[1833/3000] | LR: 0.008241 | E: -62.899519 | E_var:     3.8756 E_err:   0.030760 | NF_loss: 14.359558
[2025-11-13 05:02:59] 255:36<154:54, 7.97s/it | [Iter 1924/3090] R0[1834/3000] | LR: 0.008229 | E: -62.966158 | E_var:     3.7547 E_err:   0.030277 | NF_loss: 14.104307
[2025-11-13 05:03:07] 255:43<154:46, 7.97s/it | [Iter 1925/3090] R0[1835/3000] | LR: 0.008217 | E: -62.917087 | E_var:     3.8450 E_err:   0.030639 | NF_loss: 21.454262
[2025-11-13 05:03:15] 255:51<154:37, 7.97s/it | [Iter 1926/3090] R0[1836/3000] | LR: 0.008204 | E: -62.931251 | E_var:     3.7831 E_err:   0.030391 | NF_loss: 14.927765
[2025-11-13 05:03:23] 255:59<154:29, 7.97s/it | [Iter 1927/3090] R0[1837/3000] | LR: 0.008192 | E: -62.947478 | E_var:     3.9299 E_err:   0.030975 | NF_loss: 16.262823
[2025-11-13 05:03:30] 256:07<154:21, 7.97s/it | [Iter 1928/3090] R0[1838/3000] | LR: 0.008180 | E: -62.967061 | E_var:     3.8710 E_err:   0.030742 | NF_loss: 17.226714
[2025-11-13 05:03:38] 256:15<154:13, 7.97s/it | [Iter 1929/3090] R0[1839/3000] | LR: 0.008167 | E: -62.972982 | E_var:     3.8423 E_err:   0.030628 | NF_loss: 15.984241
[2025-11-13 05:03:46] 256:23<154:05, 7.97s/it | [Iter 1930/3090] R0[1840/3000] | LR: 0.008155 | E: -62.924303 | E_var:     3.9401 E_err:   0.031015 | NF_loss: 21.211855
[2025-11-13 05:03:54] 256:31<153:57, 7.97s/it | [Iter 1931/3090] R0[1841/3000] | LR: 0.008143 | E: -62.970370 | E_var:     3.8885 E_err:   0.030811 | NF_loss: 15.129723
[2025-11-13 05:04:02] 256:38<153:49, 7.97s/it | [Iter 1932/3090] R0[1842/3000] | LR: 0.008131 | E: -62.945721 | E_var:     3.6760 E_err:   0.029957 | NF_loss: 10.502565
[2025-11-13 05:04:10] 256:46<153:41, 7.97s/it | [Iter 1933/3090] R0[1843/3000] | LR: 0.008118 | E: -62.867507 | E_var:     3.8521 E_err:   0.030667 | NF_loss: 15.581692
[2025-11-13 05:04:18] 256:54<153:33, 7.97s/it | [Iter 1934/3090] R0[1844/3000] | LR: 0.008106 | E: -62.899904 | E_var:     3.7375 E_err:   0.030207 | NF_loss: 15.004094
[2025-11-13 05:04:25] 257:02<153:25, 7.97s/it | [Iter 1935/3090] R0[1845/3000] | LR: 0.008094 | E: -62.792941 | E_var:     3.7438 E_err:   0.030233 | NF_loss: 17.830731
[2025-11-13 05:04:33] 257:10<153:17, 7.97s/it | [Iter 1936/3090] R0[1846/3000] | LR: 0.008082 | E: -62.838286 | E_var:     3.9214 E_err:   0.030942 | NF_loss: 18.690449
[2025-11-13 05:04:41] 257:18<153:09, 7.97s/it | [Iter 1937/3090] R0[1847/3000] | LR: 0.008069 | E: -62.697017 | E_var:     3.9899 E_err:   0.031210 | NF_loss: 17.738075
[2025-11-13 05:04:49] 257:26<153:01, 7.97s/it | [Iter 1938/3090] R0[1848/3000] | LR: 0.008057 | E: -62.677313 | E_var:     4.2167 E_err:   0.032085 | NF_loss: 14.480910
[2025-11-13 05:04:57] 257:33<152:53, 7.97s/it | [Iter 1939/3090] R0[1849/3000] | LR: 0.008045 | E: -62.603461 | E_var:     4.2297 E_err:   0.032135 | NF_loss: 15.316460
[2025-11-13 05:05:05] 257:41<152:45, 7.97s/it | [Iter 1940/3090] R0[1850/3000] | LR: 0.008033 | E: -62.780018 | E_var:     4.0367 E_err:   0.031393 | NF_loss: 17.032710
[2025-11-13 05:05:13] 257:49<152:37, 7.97s/it | [Iter 1941/3090] R0[1851/3000] | LR: 0.008020 | E: -62.836100 | E_var:     3.9113 E_err:   0.030902 | NF_loss: 15.933435
[2025-11-13 05:05:20] 257:57<152:29, 7.97s/it | [Iter 1942/3090] R0[1852/3000] | LR: 0.008008 | E: -62.859376 | E_var:     3.9263 E_err:   0.030961 | NF_loss: 16.168604
[2025-11-13 05:05:28] 258:05<152:21, 7.97s/it | [Iter 1943/3090] R0[1853/3000] | LR: 0.007996 | E: -62.878428 | E_var:     3.8612 E_err:   0.030703 | NF_loss: 16.817459
[2025-11-13 05:05:36] 258:13<152:13, 7.97s/it | [Iter 1944/3090] R0[1854/3000] | LR: 0.007984 | E: -62.870503 | E_var:     3.8789 E_err:   0.030773 | NF_loss: 13.383592
[2025-11-13 05:05:44] 258:21<152:05, 7.97s/it | [Iter 1945/3090] R0[1855/3000] | LR: 0.007972 | E: -62.917004 | E_var:     3.8663 E_err:   0.030723 | NF_loss: 17.822704
[2025-11-13 05:05:52] 258:29<151:57, 7.97s/it | [Iter 1946/3090] R0[1856/3000] | LR: 0.007959 | E: -62.928803 | E_var:     4.0230 E_err:   0.031340 | NF_loss: 16.704160
[2025-11-13 05:06:00] 258:37<151:49, 7.97s/it | [Iter 1947/3090] R0[1857/3000] | LR: 0.007947 | E: -62.903376 | E_var:     4.0079 E_err:   0.031281 | NF_loss: 13.018872
[2025-11-13 05:06:08] 258:44<151:41, 7.97s/it | [Iter 1948/3090] R0[1858/3000] | LR: 0.007935 | E: -62.877223 | E_var:     3.8054 E_err:   0.030480 | NF_loss: 15.094930
[2025-11-13 05:06:16] 258:52<151:33, 7.97s/it | [Iter 1949/3090] R0[1859/3000] | LR: 0.007923 | E: -62.895834 | E_var:     3.7936 E_err:   0.030433 | NF_loss: 22.169389
[2025-11-13 05:06:24] 259:00<151:25, 7.97s/it | [Iter 1950/3090] R0[1860/3000] | LR: 0.007911 | E: -62.866571 | E_var:     4.2841 E_err:   0.032341 | NF_loss: 19.268176
[2025-11-13 05:06:31] 259:08<151:17, 7.97s/it | [Iter 1951/3090] R0[1861/3000] | LR: 0.007899 | E: -62.814566 | E_var:     4.0360 E_err:   0.031390 | NF_loss: 19.357015
[2025-11-13 05:06:39] 259:16<151:09, 7.97s/it | [Iter 1952/3090] R0[1862/3000] | LR: 0.007886 | E: -62.926195 | E_var:     3.7126 E_err:   0.030106 | NF_loss: 15.830755
[2025-11-13 05:06:47] 259:24<151:01, 7.97s/it | [Iter 1953/3090] R0[1863/3000] | LR: 0.007874 | E: -62.949731 | E_var:     3.7577 E_err:   0.030289 | NF_loss: 22.746106
[2025-11-13 05:06:55] 259:32<150:53, 7.97s/it | [Iter 1954/3090] R0[1864/3000] | LR: 0.007862 | E: -62.892374 | E_var:     3.9012 E_err:   0.030861 | NF_loss: 23.389080
[2025-11-13 05:07:03] 259:39<150:45, 7.97s/it | [Iter 1955/3090] R0[1865/3000] | LR: 0.007850 | E: -62.891814 | E_var:     4.0894 E_err:   0.031597 | NF_loss: 22.920406
[2025-11-13 05:07:11] 259:47<150:37, 7.97s/it | [Iter 1956/3090] R0[1866/3000] | LR: 0.007838 | E: -62.799079 | E_var:     4.2078 E_err:   0.032051 | NF_loss: 16.097365
[2025-11-13 05:07:19] 259:55<150:29, 7.97s/it | [Iter 1957/3090] R0[1867/3000] | LR: 0.007826 | E: -62.811867 | E_var:     4.2464 E_err:   0.032198 | NF_loss: 17.536657
[2025-11-13 05:07:26] 260:03<150:20, 7.97s/it | [Iter 1958/3090] R0[1868/3000] | LR: 0.007813 | E: -62.746199 | E_var:     4.2357 E_err:   0.032157 | NF_loss: 18.907638
[2025-11-13 05:07:34] 260:11<150:12, 7.97s/it | [Iter 1959/3090] R0[1869/3000] | LR: 0.007801 | E: -62.526871 | E_var:     4.3570 E_err:   0.032615 | NF_loss: 18.991385
[2025-11-13 05:07:42] 260:19<150:04, 7.97s/it | [Iter 1960/3090] R0[1870/3000] | LR: 0.007789 | E: -62.638337 | E_var:     4.3250 E_err:   0.032495 | NF_loss: 21.663235
[2025-11-13 05:07:50] 260:26<149:56, 7.97s/it | [Iter 1961/3090] R0[1871/3000] | LR: 0.007777 | E: -62.793851 | E_var:     4.0748 E_err:   0.031541 | NF_loss: 16.952174
[2025-11-13 05:07:58] 260:34<149:48, 7.97s/it | [Iter 1962/3090] R0[1872/3000] | LR: 0.007765 | E: -62.740610 | E_var:     4.1962 E_err:   0.032007 | NF_loss: 17.745403
[2025-11-13 05:08:06] 260:42<149:40, 7.97s/it | [Iter 1963/3090] R0[1873/3000] | LR: 0.007753 | E: -62.610391 | E_var:     4.3305 E_err:   0.032515 | NF_loss: 20.404271
[2025-11-13 05:08:14] 260:50<149:32, 7.97s/it | [Iter 1964/3090] R0[1874/3000] | LR: 0.007741 | E: -62.498209 | E_var:     4.3792 E_err:   0.032698 | NF_loss: 18.557981
[2025-11-13 05:08:21] 260:58<149:24, 7.97s/it | [Iter 1965/3090] R0[1875/3000] | LR: 0.007729 | E: -62.614435 | E_var:     4.4759 E_err:   0.033057 | NF_loss: 18.774662
[2025-11-13 05:08:29] 261:06<149:16, 7.97s/it | [Iter 1966/3090] R0[1876/3000] | LR: 0.007717 | E: -62.733048 | E_var:     4.4827 E_err:   0.033082 | NF_loss: 17.370913
[2025-11-13 05:08:37] 261:14<149:08, 7.97s/it | [Iter 1967/3090] R0[1877/3000] | LR: 0.007704 | E: -62.769923 | E_var:     4.5069 E_err:   0.033171 | NF_loss: 20.348499
[2025-11-13 05:08:45] 261:21<149:00, 7.97s/it | [Iter 1968/3090] R0[1878/3000] | LR: 0.007692 | E: -62.833895 | E_var:     4.7579 E_err:   0.034082 | NF_loss: 16.779869
[2025-11-13 05:08:53] 261:29<148:52, 7.97s/it | [Iter 1969/3090] R0[1879/3000] | LR: 0.007680 | E: -62.814018 | E_var:     3.8222 E_err:   0.030547 | NF_loss: 17.912558
[2025-11-13 05:09:01] 261:37<148:44, 7.97s/it | [Iter 1970/3090] R0[1880/3000] | LR: 0.007668 | E: -62.909979 | E_var:     4.0364 E_err:   0.031392 | NF_loss: 15.254046
[2025-11-13 05:09:08] 261:45<148:36, 7.97s/it | [Iter 1971/3090] R0[1881/3000] | LR: 0.007656 | E: -62.875367 | E_var:     4.1303 E_err:   0.031755 | NF_loss: 18.578876
[2025-11-13 05:09:16] 261:53<148:28, 7.97s/it | [Iter 1972/3090] R0[1882/3000] | LR: 0.007644 | E: -62.873323 | E_var:     4.1841 E_err:   0.031961 | NF_loss: 17.618938
[2025-11-13 05:09:24] 262:01<148:20, 7.97s/it | [Iter 1973/3090] R0[1883/3000] | LR: 0.007632 | E: -62.857644 | E_var:     3.8435 E_err:   0.030633 | NF_loss: 17.184339
[2025-11-13 05:09:32] 262:09<148:12, 7.97s/it | [Iter 1974/3090] R0[1884/3000] | LR: 0.007620 | E: -62.855663 | E_var:     4.0286 E_err:   0.031362 | NF_loss: 19.082323
[2025-11-13 05:09:40] 262:16<148:04, 7.97s/it | [Iter 1975/3090] R0[1885/3000] | LR: 0.007608 | E: -62.861365 | E_var:     4.1543 E_err:   0.031847 | NF_loss: 16.328183
[2025-11-13 05:09:48] 262:24<147:56, 7.97s/it | [Iter 1976/3090] R0[1886/3000] | LR: 0.007596 | E: -62.922721 | E_var:     3.8741 E_err:   0.030754 | NF_loss: 26.025290
[2025-11-13 05:09:56] 262:32<147:48, 7.97s/it | [Iter 1977/3090] R0[1887/3000] | LR: 0.007584 | E: -62.917743 | E_var:     3.7845 E_err:   0.030397 | NF_loss: 13.049163
[2025-11-13 05:10:03] 262:40<147:40, 7.97s/it | [Iter 1978/3090] R0[1888/3000] | LR: 0.007572 | E: -62.904769 | E_var:     3.9177 E_err:   0.030927 | NF_loss: 14.272754
[2025-11-13 05:10:11] 262:48<147:32, 7.97s/it | [Iter 1979/3090] R0[1889/3000] | LR: 0.007560 | E: -62.933287 | E_var:     3.9171 E_err:   0.030924 | NF_loss: 16.351140
[2025-11-13 05:10:19] 262:56<147:24, 7.97s/it | [Iter 1980/3090] R0[1890/3000] | LR: 0.007548 | E: -62.926954 | E_var:     4.0630 E_err:   0.031495 | NF_loss: 20.092407
[2025-11-13 05:10:27] 263:03<147:16, 7.97s/it | [Iter 1981/3090] R0[1891/3000] | LR: 0.007536 | E: -62.975173 | E_var:     4.3953 E_err:   0.032758 | NF_loss: 13.295684
[2025-11-13 05:10:35] 263:11<147:08, 7.97s/it | [Iter 1982/3090] R0[1892/3000] | LR: 0.007524 | E: -62.928463 | E_var:     4.7079 E_err:   0.033903 | NF_loss: 13.988961
[2025-11-13 05:10:43] 263:19<147:00, 7.97s/it | [Iter 1983/3090] R0[1893/3000] | LR: 0.007512 | E: -62.911070 | E_var:     4.6234 E_err:   0.033597 | NF_loss: 16.384091
[2025-11-13 05:10:51] 263:27<146:52, 7.97s/it | [Iter 1984/3090] R0[1894/3000] | LR: 0.007500 | E: -62.856950 | E_var:     4.2686 E_err:   0.032282 | NF_loss: 17.252117
[2025-11-13 05:10:59] 263:35<146:44, 7.97s/it | [Iter 1985/3090] R0[1895/3000] | LR: 0.007488 | E: -62.851903 | E_var:     4.4396 E_err:   0.032922 | NF_loss: 12.235175
[2025-11-13 05:11:06] 263:43<146:36, 7.97s/it | [Iter 1986/3090] R0[1896/3000] | LR: 0.007476 | E: -62.910609 | E_var:     4.3181 E_err:   0.032469 | NF_loss: 14.425402
[2025-11-13 05:11:14] 263:51<146:28, 7.97s/it | [Iter 1987/3090] R0[1897/3000] | LR: 0.007464 | E: -62.911868 | E_var:     4.5385 E_err:   0.033287 | NF_loss: 15.084097
[2025-11-13 05:11:22] 263:59<146:20, 7.97s/it | [Iter 1988/3090] R0[1898/3000] | LR: 0.007452 | E: -62.868521 | E_var:     4.4915 E_err:   0.033114 | NF_loss: 14.212374
[2025-11-13 05:11:30] 264:06<146:12, 7.97s/it | [Iter 1989/3090] R0[1899/3000] | LR: 0.007440 | E: -62.778784 | E_var:     4.4971 E_err:   0.033135 | NF_loss: 22.079977
[2025-11-13 05:11:38] 264:14<146:03, 7.97s/it | [Iter 1990/3090] R0[1900/3000] | LR: 0.007428 | E: -62.787166 | E_var:     4.4563 E_err:   0.032984 | NF_loss: 18.175040
[2025-11-13 05:11:46] 264:22<145:55, 7.97s/it | [Iter 1991/3090] R0[1901/3000] | LR: 0.007416 | E: -62.794919 | E_var:     4.5496 E_err:   0.033328 | NF_loss: 19.304342
[2025-11-13 05:11:54] 264:30<145:47, 7.97s/it | [Iter 1992/3090] R0[1902/3000] | LR: 0.007404 | E: -62.767837 | E_var:     4.3286 E_err:   0.032508 | NF_loss: 15.904295
[2025-11-13 05:12:01] 264:38<145:39, 7.97s/it | [Iter 1993/3090] R0[1903/3000] | LR: 0.007392 | E: -62.732710 | E_var:     4.5510 E_err:   0.033333 | NF_loss: 16.794327
[2025-11-13 05:12:09] 264:46<145:31, 7.97s/it | [Iter 1994/3090] R0[1904/3000] | LR: 0.007380 | E: -62.749078 | E_var:     4.3128 E_err:   0.032449 | NF_loss: 18.252885
[2025-11-13 05:12:17] 264:54<145:23, 7.97s/it | [Iter 1995/3090] R0[1905/3000] | LR: 0.007368 | E: -62.738929 | E_var:     4.5575 E_err:   0.033357 | NF_loss: 15.996847
[2025-11-13 05:12:25] 265:01<145:15, 7.97s/it | [Iter 1996/3090] R0[1906/3000] | LR: 0.007356 | E: -62.752870 | E_var:     4.2918 E_err:   0.032370 | NF_loss: 16.522825
[2025-11-13 05:12:33] 265:09<145:07, 7.97s/it | [Iter 1997/3090] R0[1907/3000] | LR: 0.007344 | E: -62.834433 | E_var:     4.5836 E_err:   0.033452 | NF_loss: 19.423279
[2025-11-13 05:12:41] 265:17<144:59, 7.97s/it | [Iter 1998/3090] R0[1908/3000] | LR: 0.007332 | E: -62.729741 | E_var:     4.7036 E_err:   0.033887 | NF_loss: 17.761812
[2025-11-13 05:12:48] 265:25<144:51, 7.97s/it | [Iter 1999/3090] R0[1909/3000] | LR: 0.007320 | E: -62.725052 | E_var:     4.4507 E_err:   0.032963 | NF_loss: 16.509481
[2025-11-13 05:12:56] 265:33<144:43, 7.97s/it | [Iter 2000/3090] R0[1910/3000] | LR: 0.007308 | E: -62.738591 | E_var:     4.4779 E_err:   0.033064 | NF_loss: 17.216996
[2025-11-13 05:13:04] 265:41<144:35, 7.97s/it | [Iter 2001/3090] R0[1911/3000] | LR: 0.007297 | E: -62.798600 | E_var:     4.4468 E_err:   0.032949 | NF_loss: 16.885545
[2025-11-13 05:13:12] 265:49<144:27, 7.97s/it | [Iter 2002/3090] R0[1912/3000] | LR: 0.007285 | E: -62.680684 | E_var:     4.5379 E_err:   0.033285 | NF_loss: 15.188079
[2025-11-13 05:13:20] 265:56<144:19, 7.97s/it | [Iter 2003/3090] R0[1913/3000] | LR: 0.007273 | E: -62.688506 | E_var:     4.8318 E_err:   0.034346 | NF_loss: 17.319526
[2025-11-13 05:13:28] 266:04<144:11, 7.97s/it | [Iter 2004/3090] R0[1914/3000] | LR: 0.007261 | E: -62.670615 | E_var:     4.8695 E_err:   0.034480 | NF_loss: 18.800781
[2025-11-13 05:13:35] 266:12<144:03, 7.97s/it | [Iter 2005/3090] R0[1915/3000] | LR: 0.007249 | E: -62.644095 | E_var:     5.4024 E_err:   0.036317 | NF_loss: 18.797693
[2025-11-13 05:13:43] 266:20<143:55, 7.97s/it | [Iter 2006/3090] R0[1916/3000] | LR: 0.007237 | E: -62.600794 | E_var:     5.0349 E_err:   0.035060 | NF_loss: 13.571906
[2025-11-13 05:13:51] 266:28<143:47, 7.97s/it | [Iter 2007/3090] R0[1917/3000] | LR: 0.007225 | E: -62.702080 | E_var:     4.5899 E_err:   0.033475 | NF_loss: 15.058745
[2025-11-13 05:13:59] 266:36<143:39, 7.97s/it | [Iter 2008/3090] R0[1918/3000] | LR: 0.007213 | E: -62.699330 | E_var:     5.0173 E_err:   0.034999 | NF_loss: 15.109850
[2025-11-13 05:14:07] 266:43<143:31, 7.97s/it | [Iter 2009/3090] R0[1919/3000] | LR: 0.007202 | E: -62.707639 | E_var:     5.0525 E_err:   0.035122 | NF_loss: 24.057452
[2025-11-13 05:14:15] 266:51<143:23, 7.97s/it | [Iter 2010/3090] R0[1920/3000] | LR: 0.007190 | E: -62.710422 | E_var:     4.9314 E_err:   0.034698 | NF_loss: 15.538438
[2025-11-13 05:14:23] 266:59<143:15, 7.97s/it | [Iter 2011/3090] R0[1921/3000] | LR: 0.007178 | E: -62.601703 | E_var:     4.9152 E_err:   0.034641 | NF_loss: 16.868763
[2025-11-13 05:14:30] 267:07<143:07, 7.97s/it | [Iter 2012/3090] R0[1922/3000] | LR: 0.007166 | E: -62.505059 | E_var:     5.0657 E_err:   0.035167 | NF_loss: 15.782238
[2025-11-13 05:14:38] 267:15<142:59, 7.97s/it | [Iter 2013/3090] R0[1923/3000] | LR: 0.007154 | E: -62.541413 | E_var:     5.3663 E_err:   0.036196 | NF_loss: 15.768223
[2025-11-13 05:14:46] 267:23<142:51, 7.97s/it | [Iter 2014/3090] R0[1924/3000] | LR: 0.007142 | E: -62.487726 | E_var:     5.8897 E_err:   0.037920 | NF_loss: 7.241164
[2025-11-13 05:14:54] 267:30<142:43, 7.97s/it | [Iter 2015/3090] R0[1925/3000] | LR: 0.007131 | E: -62.387284 | E_var:     5.5851 E_err:   0.036926 | NF_loss: 15.315705
[2025-11-13 05:15:02] 267:38<142:35, 7.97s/it | [Iter 2016/3090] R0[1926/3000] | LR: 0.007119 | E: -62.642086 | E_var:     4.5680 E_err:   0.033395 | NF_loss: 16.379876
[2025-11-13 05:15:10] 267:46<142:27, 7.97s/it | [Iter 2017/3090] R0[1927/3000] | LR: 0.007107 | E: -62.757684 | E_var:     4.2352 E_err:   0.032155 | NF_loss: 17.772489
[2025-11-13 05:15:18] 267:54<142:19, 7.97s/it | [Iter 2018/3090] R0[1928/3000] | LR: 0.007095 | E: -62.665132 | E_var:     4.9771 E_err:   0.034858 | NF_loss: 18.500242
[2025-11-13 05:15:25] 268:02<142:11, 7.97s/it | [Iter 2019/3090] R0[1929/3000] | LR: 0.007083 | E: -62.859073 | E_var:     4.0073 E_err:   0.031279 | NF_loss: 17.134527
[2025-11-13 05:15:33] 268:10<142:03, 7.97s/it | [Iter 2020/3090] R0[1930/3000] | LR: 0.007071 | E: -62.858589 | E_var:     4.3387 E_err:   0.032546 | NF_loss: 14.456815
[2025-11-13 05:15:41] 268:18<141:55, 7.97s/it | [Iter 2021/3090] R0[1931/3000] | LR: 0.007060 | E: -62.895051 | E_var:     4.1931 E_err:   0.031995 | NF_loss: 17.791468
[2025-11-13 05:15:49] 268:25<141:47, 7.97s/it | [Iter 2022/3090] R0[1932/3000] | LR: 0.007048 | E: -62.939277 | E_var:     4.4507 E_err:   0.032964 | NF_loss: 16.868968
[2025-11-13 05:15:57] 268:33<141:38, 7.97s/it | [Iter 2023/3090] R0[1933/3000] | LR: 0.007036 | E: -62.928747 | E_var:     4.0822 E_err:   0.031570 | NF_loss: 18.199614
[2025-11-13 05:16:05] 268:41<141:30, 7.97s/it | [Iter 2024/3090] R0[1934/3000] | LR: 0.007024 | E: -62.922935 | E_var:     3.9399 E_err:   0.031015 | NF_loss: 16.888694
[2025-11-13 05:16:12] 268:49<141:22, 7.97s/it | [Iter 2025/3090] R0[1935/3000] | LR: 0.007013 | E: -62.916014 | E_var:     4.1680 E_err:   0.031899 | NF_loss: 17.843205
[2025-11-13 05:16:20] 268:57<141:14, 7.97s/it | [Iter 2026/3090] R0[1936/3000] | LR: 0.007001 | E: -62.960456 | E_var:     4.2417 E_err:   0.032180 | NF_loss: 27.056957
[2025-11-13 05:16:28] 269:05<141:06, 7.96s/it | [Iter 2027/3090] R0[1937/3000] | LR: 0.006989 | E: -62.952126 | E_var:     4.3020 E_err:   0.032408 | NF_loss: 11.301708
[2025-11-13 05:16:36] 269:12<140:58, 7.96s/it | [Iter 2028/3090] R0[1938/3000] | LR: 0.006977 | E: -62.925171 | E_var:     4.0869 E_err:   0.031588 | NF_loss: 14.926448
[2025-11-13 05:16:44] 269:20<140:50, 7.96s/it | [Iter 2029/3090] R0[1939/3000] | LR: 0.006966 | E: -62.936669 | E_var:     3.9399 E_err:   0.031014 | NF_loss: 18.663295
[2025-11-13 05:16:51] 269:28<140:42, 7.96s/it | [Iter 2030/3090] R0[1940/3000] | LR: 0.006954 | E: -62.893962 | E_var:     4.1124 E_err:   0.031686 | NF_loss: 18.187974
[2025-11-13 05:16:59] 269:36<140:34, 7.96s/it | [Iter 2031/3090] R0[1941/3000] | LR: 0.006942 | E: -62.910788 | E_var:     4.2095 E_err:   0.032058 | NF_loss: 19.950222
[2025-11-13 05:17:07] 269:44<140:26, 7.96s/it | [Iter 2032/3090] R0[1942/3000] | LR: 0.006930 | E: -62.937771 | E_var:     4.0226 E_err:   0.031338 | NF_loss: 15.433225
[2025-11-13 05:17:15] 269:51<140:18, 7.96s/it | [Iter 2033/3090] R0[1943/3000] | LR: 0.006919 | E: -62.941955 | E_var:     4.1523 E_err:   0.031839 | NF_loss: 13.684809
[2025-11-13 05:17:23] 269:59<140:10, 7.96s/it | [Iter 2034/3090] R0[1944/3000] | LR: 0.006907 | E: -62.989564 | E_var:     4.0559 E_err:   0.031468 | NF_loss: 24.499031
[2025-11-13 05:17:30] 270:07<140:02, 7.96s/it | [Iter 2035/3090] R0[1945/3000] | LR: 0.006895 | E: -62.970279 | E_var:     3.9695 E_err:   0.031131 | NF_loss: 17.510391
[2025-11-13 05:17:38] 270:15<139:54, 7.96s/it | [Iter 2036/3090] R0[1946/3000] | LR: 0.006884 | E: -62.999931 | E_var:     4.2830 E_err:   0.032336 | NF_loss: 18.642744
[2025-11-13 05:17:46] 270:23<139:46, 7.96s/it | [Iter 2037/3090] R0[1947/3000] | LR: 0.006872 | E: -63.014046 | E_var:     4.1508 E_err:   0.031833 | NF_loss: 17.449273
[2025-11-13 05:17:54] 270:30<139:38, 7.96s/it | [Iter 2038/3090] R0[1948/3000] | LR: 0.006860 | E: -62.953779 | E_var:     4.2317 E_err:   0.032142 | NF_loss: 16.814044
[2025-11-13 05:18:02] 270:38<139:30, 7.96s/it | [Iter 2039/3090] R0[1949/3000] | LR: 0.006849 | E: -62.965252 | E_var:     3.9388 E_err:   0.031010 | NF_loss: 21.577136
[2025-11-13 05:18:09] 270:46<139:22, 7.96s/it | [Iter 2040/3090] R0[1950/3000] | LR: 0.006837 | E: -62.978797 | E_var:     4.0861 E_err:   0.031585 | NF_loss: 15.128813
[2025-11-13 05:18:17] 270:54<139:14, 7.96s/it | [Iter 2041/3090] R0[1951/3000] | LR: 0.006825 | E: -62.994204 | E_var:     4.1210 E_err:   0.031719 | NF_loss: 15.574522
[2025-11-13 05:18:25] 271:02<139:06, 7.96s/it | [Iter 2042/3090] R0[1952/3000] | LR: 0.006814 | E: -62.965719 | E_var:     3.9468 E_err:   0.031042 | NF_loss: 18.294747
[2025-11-13 05:18:33] 271:09<138:58, 7.96s/it | [Iter 2043/3090] R0[1953/3000] | LR: 0.006802 | E: -62.945526 | E_var:     3.8444 E_err:   0.030636 | NF_loss: 16.689977
[2025-11-13 05:18:41] 271:17<138:49, 7.96s/it | [Iter 2044/3090] R0[1954/3000] | LR: 0.006790 | E: -62.958390 | E_var:     3.8312 E_err:   0.030583 | NF_loss: 19.603448
[2025-11-13 05:18:48] 271:25<138:41, 7.96s/it | [Iter 2045/3090] R0[1955/3000] | LR: 0.006779 | E: -62.966124 | E_var:     3.8373 E_err:   0.030608 | NF_loss: 17.962526
[2025-11-13 05:18:56] 271:33<138:33, 7.96s/it | [Iter 2046/3090] R0[1956/3000] | LR: 0.006767 | E: -62.975888 | E_var:     3.8351 E_err:   0.030599 | NF_loss: 19.801103
[2025-11-13 05:19:04] 271:41<138:25, 7.96s/it | [Iter 2047/3090] R0[1957/3000] | LR: 0.006755 | E: -63.015260 | E_var:     3.8825 E_err:   0.030788 | NF_loss: 18.795300
[2025-11-13 05:19:12] 271:48<138:17, 7.96s/it | [Iter 2048/3090] R0[1958/3000] | LR: 0.006744 | E: -63.032607 | E_var:     4.0036 E_err:   0.031264 | NF_loss: 16.139946
[2025-11-13 05:19:20] 271:56<138:09, 7.96s/it | [Iter 2049/3090] R0[1959/3000] | LR: 0.006732 | E: -63.034528 | E_var:     4.1405 E_err:   0.031794 | NF_loss: 23.211037
[2025-11-13 05:19:27] 272:04<138:01, 7.96s/it | [Iter 2050/3090] R0[1960/3000] | LR: 0.006720 | E: -63.022214 | E_var:     3.8691 E_err:   0.030734 | NF_loss: 15.129508
[2025-11-13 05:19:35] 272:12<137:53, 7.96s/it | [Iter 2051/3090] R0[1961/3000] | LR: 0.006709 | E: -62.970763 | E_var:     4.1670 E_err:   0.031896 | NF_loss: 20.316483
[2025-11-13 05:19:43] 272:20<137:45, 7.96s/it | [Iter 2052/3090] R0[1962/3000] | LR: 0.006697 | E: -63.001622 | E_var:     3.8748 E_err:   0.030757 | NF_loss: 21.750200
[2025-11-13 05:19:51] 272:27<137:37, 7.96s/it | [Iter 2053/3090] R0[1963/3000] | LR: 0.006686 | E: -62.994294 | E_var:     4.3123 E_err:   0.032447 | NF_loss: 20.252346
[2025-11-13 05:19:59] 272:35<137:29, 7.96s/it | [Iter 2054/3090] R0[1964/3000] | LR: 0.006674 | E: -62.947733 | E_var:     4.1016 E_err:   0.031644 | NF_loss: 22.462204
[2025-11-13 05:20:06] 272:43<137:21, 7.96s/it | [Iter 2055/3090] R0[1965/3000] | LR: 0.006663 | E: -62.976541 | E_var:     3.9490 E_err:   0.031050 | NF_loss: 32.158426
[2025-11-13 05:20:14] 272:51<137:13, 7.96s/it | [Iter 2056/3090] R0[1966/3000] | LR: 0.006651 | E: -62.986524 | E_var:     4.0351 E_err:   0.031387 | NF_loss: 26.990550
[2025-11-13 05:20:22] 272:59<137:05, 7.96s/it | [Iter 2057/3090] R0[1967/3000] | LR: 0.006639 | E: -62.984564 | E_var:     4.3173 E_err:   0.032466 | NF_loss: 43.763526
[2025-11-13 05:20:30] 273:06<136:57, 7.96s/it | [Iter 2058/3090] R0[1968/3000] | LR: 0.006628 | E: -62.948849 | E_var:     3.8241 E_err:   0.030555 | NF_loss: 29.041502
[2025-11-13 05:20:38] 273:14<136:49, 7.96s/it | [Iter 2059/3090] R0[1969/3000] | LR: 0.006616 | E: -62.981180 | E_var:     3.9651 E_err:   0.031113 | NF_loss: 18.281662
[2025-11-13 05:20:46] 273:22<136:41, 7.96s/it | [Iter 2060/3090] R0[1970/3000] | LR: 0.006605 | E: -62.980086 | E_var:     3.8455 E_err:   0.030641 | NF_loss: 17.578560
[2025-11-13 05:20:53] 273:30<136:33, 7.96s/it | [Iter 2061/3090] R0[1971/3000] | LR: 0.006593 | E: -63.001164 | E_var:     4.3155 E_err:   0.032459 | NF_loss: 26.521365
[2025-11-13 05:21:01] 273:38<136:25, 7.96s/it | [Iter 2062/3090] R0[1972/3000] | LR: 0.006582 | E: -63.074559 | E_var:     3.9409 E_err:   0.031018 | NF_loss: 32.492922
[2025-11-13 05:21:09] 273:46<136:17, 7.96s/it | [Iter 2063/3090] R0[1973/3000] | LR: 0.006570 | E: -63.007058 | E_var:     4.3287 E_err:   0.032509 | NF_loss: 25.258736
[2025-11-13 05:21:17] 273:53<136:09, 7.96s/it | [Iter 2064/3090] R0[1974/3000] | LR: 0.006559 | E: -62.988420 | E_var:     4.0558 E_err:   0.031467 | NF_loss: 22.410149
[2025-11-13 05:21:25] 274:01<136:01, 7.96s/it | [Iter 2065/3090] R0[1975/3000] | LR: 0.006547 | E: -63.017151 | E_var:     3.8369 E_err:   0.030606 | NF_loss: 21.325738
[2025-11-13 05:21:33] 274:09<135:53, 7.96s/it | [Iter 2066/3090] R0[1976/3000] | LR: 0.006536 | E: -63.037523 | E_var:     3.9983 E_err:   0.031243 | NF_loss: 18.019066
[2025-11-13 05:21:40] 274:17<135:45, 7.96s/it | [Iter 2067/3090] R0[1977/3000] | LR: 0.006524 | E: -62.970723 | E_var:     4.9805 E_err:   0.034870 | NF_loss: 22.817646
[2025-11-13 05:21:48] 274:25<135:37, 7.96s/it | [Iter 2068/3090] R0[1978/3000] | LR: 0.006513 | E: -62.935621 | E_var:     4.4866 E_err:   0.033096 | NF_loss: 21.242760
[2025-11-13 05:21:56] 274:32<135:29, 7.96s/it | [Iter 2069/3090] R0[1979/3000] | LR: 0.006501 | E: -62.927254 | E_var:     4.2540 E_err:   0.032227 | NF_loss: 17.032234
[2025-11-13 05:22:04] 274:40<135:20, 7.96s/it | [Iter 2070/3090] R0[1980/3000] | LR: 0.006490 | E: -62.874950 | E_var:     4.2227 E_err:   0.032108 | NF_loss: 18.930039
[2025-11-13 05:22:12] 274:48<135:12, 7.96s/it | [Iter 2071/3090] R0[1981/3000] | LR: 0.006478 | E: -62.913230 | E_var:     4.1719 E_err:   0.031915 | NF_loss: 21.959317
[2025-11-13 05:22:19] 274:56<135:04, 7.96s/it | [Iter 2072/3090] R0[1982/3000] | LR: 0.006467 | E: -62.962972 | E_var:     4.3656 E_err:   0.032647 | NF_loss: 18.521986
[2025-11-13 05:22:27] 275:04<134:56, 7.96s/it | [Iter 2073/3090] R0[1983/3000] | LR: 0.006455 | E: -62.952187 | E_var:     4.2019 E_err:   0.032029 | NF_loss: 20.346688
[2025-11-13 05:22:35] 275:11<134:48, 7.96s/it | [Iter 2074/3090] R0[1984/3000] | LR: 0.006444 | E: -62.798703 | E_var:     4.4366 E_err:   0.032911 | NF_loss: 20.847189
[2025-11-13 05:22:43] 275:19<134:40, 7.96s/it | [Iter 2075/3090] R0[1985/3000] | LR: 0.006432 | E: -62.956568 | E_var:     4.0851 E_err:   0.031581 | NF_loss: 18.495630
[2025-11-13 05:22:51] 275:27<134:32, 7.96s/it | [Iter 2076/3090] R0[1986/3000] | LR: 0.006421 | E: -62.952199 | E_var:     3.9857 E_err:   0.031194 | NF_loss: 24.495339
[2025-11-13 05:22:58] 275:35<134:24, 7.96s/it | [Iter 2077/3090] R0[1987/3000] | LR: 0.006409 | E: -62.878133 | E_var:     4.3983 E_err:   0.032769 | NF_loss: 19.312894
[2025-11-13 05:23:06] 275:43<134:16, 7.96s/it | [Iter 2078/3090] R0[1988/3000] | LR: 0.006398 | E: -62.606326 | E_var:     5.0242 E_err:   0.035023 | NF_loss: 19.422838
[2025-11-13 05:23:14] 275:50<134:08, 7.96s/it | [Iter 2079/3090] R0[1989/3000] | LR: 0.006387 | E: -62.519937 | E_var:     4.7423 E_err:   0.034026 | NF_loss: 16.835670
[2025-11-13 05:23:22] 275:58<134:00, 7.96s/it | [Iter 2080/3090] R0[1990/3000] | LR: 0.006375 | E: -62.563958 | E_var:     4.9567 E_err:   0.034787 | NF_loss: 24.168996
[2025-11-13 05:23:30] 276:06<133:52, 7.96s/it | [Iter 2081/3090] R0[1991/3000] | LR: 0.006364 | E: -62.607332 | E_var:     5.4395 E_err:   0.036442 | NF_loss: 19.078262
[2025-11-13 05:23:37] 276:14<133:44, 7.96s/it | [Iter 2082/3090] R0[1992/3000] | LR: 0.006352 | E: -62.613274 | E_var:     4.5991 E_err:   0.033509 | NF_loss: 20.662153
[2025-11-13 05:23:45] 276:22<133:36, 7.96s/it | [Iter 2083/3090] R0[1993/3000] | LR: 0.006341 | E: -62.735275 | E_var:     4.5155 E_err:   0.033203 | NF_loss: 18.598975
[2025-11-13 05:23:53] 276:29<133:28, 7.96s/it | [Iter 2084/3090] R0[1994/3000] | LR: 0.006330 | E: -62.702758 | E_var:     5.6014 E_err:   0.036980 | NF_loss: 24.072226
[2025-11-13 05:24:01] 276:37<133:20, 7.96s/it | [Iter 2085/3090] R0[1995/3000] | LR: 0.006318 | E: -62.607158 | E_var:     5.5298 E_err:   0.036743 | NF_loss: 17.732164
[2025-11-13 05:24:09] 276:45<133:12, 7.96s/it | [Iter 2086/3090] R0[1996/3000] | LR: 0.006307 | E: -62.497810 | E_var:     5.6404 E_err:   0.037109 | NF_loss: 15.543870
[2025-11-13 05:24:16] 276:53<133:04, 7.96s/it | [Iter 2087/3090] R0[1997/3000] | LR: 0.006295 | E: -62.684247 | E_var:     4.7117 E_err:   0.033916 | NF_loss: 18.550542
[2025-11-13 05:24:24] 277:01<132:56, 7.96s/it | [Iter 2088/3090] R0[1998/3000] | LR: 0.006284 | E: -62.520895 | E_var:     5.6917 E_err:   0.037277 | NF_loss: 15.330882
[2025-11-13 05:24:32] 277:08<132:48, 7.96s/it | [Iter 2089/3090] R0[1999/3000] | LR: 0.006273 | E: -62.471005 | E_var:     5.6151 E_err:   0.037025 | NF_loss: 18.872430
[2025-11-13 05:24:40] 277:16<132:40, 7.96s/it | [Iter 2090/3090] R0[2000/3000] | LR: 0.006261 | E: -62.260749 | E_var:     6.3002 E_err:   0.039219 | NF_loss: 37.010308
[2025-11-13 05:24:48] 277:24<132:32, 7.96s/it | [Iter 2091/3090] R0[2001/3000] | LR: 0.006250 | E: -62.357646 | E_var:     5.6892 E_err:   0.037269 | NF_loss: 29.456816
[2025-11-13 05:24:55] 277:32<132:24, 7.96s/it | [Iter 2092/3090] R0[2002/3000] | LR: 0.006239 | E: -62.458422 | E_var:     5.9363 E_err:   0.038070 | NF_loss: 23.245634
[2025-11-13 05:25:03] 277:40<132:16, 7.96s/it | [Iter 2093/3090] R0[2003/3000] | LR: 0.006227 | E: -62.432207 | E_var:     5.5783 E_err:   0.036904 | NF_loss: 25.391664
[2025-11-13 05:25:11] 277:47<132:08, 7.96s/it | [Iter 2094/3090] R0[2004/3000] | LR: 0.006216 | E: -62.535272 | E_var:     5.2881 E_err:   0.035931 | NF_loss: 21.723654
[2025-11-13 05:25:19] 277:55<132:00, 7.96s/it | [Iter 2095/3090] R0[2005/3000] | LR: 0.006205 | E: -62.389306 | E_var:     6.2191 E_err:   0.038966 | NF_loss: 19.362269
[2025-11-13 05:25:27] 278:03<131:51, 7.96s/it | [Iter 2096/3090] R0[2006/3000] | LR: 0.006193 | E: -62.522697 | E_var:     5.6038 E_err:   0.036988 | NF_loss: 17.181747
[2025-11-13 05:25:35] 278:11<131:44, 7.96s/it | [Iter 2097/3090] R0[2007/3000] | LR: 0.006182 | E: -62.456635 | E_var:     5.1515 E_err:   0.035464 | NF_loss: 17.014215
[2025-11-13 05:25:42] 278:19<131:35, 7.96s/it | [Iter 2098/3090] R0[2008/3000] | LR: 0.006171 | E: -62.556226 | E_var:     4.9672 E_err:   0.034824 | NF_loss: 22.993429
[2025-11-13 05:25:50] 278:27<131:27, 7.96s/it | [Iter 2099/3090] R0[2009/3000] | LR: 0.006160 | E: -62.702561 | E_var:     4.4301 E_err:   0.032887 | NF_loss: 21.096747
[2025-11-13 05:25:58] 278:34<131:19, 7.96s/it | [Iter 2100/3090] R0[2010/3000] | LR: 0.006148 | E: -62.619193 | E_var:     4.6336 E_err:   0.033634 | NF_loss: 17.929351
[2025-11-13 05:25:58] 保存checkpoint: hybrid_checkpoint_iter_002100.pkl
[2025-11-13 05:26:06] 278:42<131:11, 7.96s/it | [Iter 2101/3090] R0[2011/3000] | LR: 0.006137 | E: -62.523680 | E_var:     5.7217 E_err:   0.037375 | NF_loss: 19.747587
[2025-11-13 05:26:14] 278:50<131:03, 7.96s/it | [Iter 2102/3090] R0[2012/3000] | LR: 0.006126 | E: -62.587389 | E_var:     5.1511 E_err:   0.035463 | NF_loss: 21.795219
[2025-11-13 05:26:22] 278:58<130:55, 7.96s/it | [Iter 2103/3090] R0[2013/3000] | LR: 0.006115 | E: -62.675593 | E_var:     4.8273 E_err:   0.034330 | NF_loss: 16.293579
[2025-11-13 05:26:29] 279:06<130:47, 7.96s/it | [Iter 2104/3090] R0[2014/3000] | LR: 0.006103 | E: -62.648042 | E_var:     4.8492 E_err:   0.034408 | NF_loss: 21.190052
[2025-11-13 05:26:37] 279:14<130:39, 7.96s/it | [Iter 2105/3090] R0[2015/3000] | LR: 0.006092 | E: -62.782421 | E_var:     4.3767 E_err:   0.032688 | NF_loss: 18.132984
[2025-11-13 05:26:45] 279:22<130:31, 7.96s/it | [Iter 2106/3090] R0[2016/3000] | LR: 0.006081 | E: -62.664938 | E_var:     4.8360 E_err:   0.034361 | NF_loss: 19.082668
[2025-11-13 05:26:53] 279:29<130:23, 7.96s/it | [Iter 2107/3090] R0[2017/3000] | LR: 0.006070 | E: -62.698988 | E_var:     5.1316 E_err:   0.035395 | NF_loss: 21.263858
[2025-11-13 05:27:01] 279:37<130:15, 7.96s/it | [Iter 2108/3090] R0[2018/3000] | LR: 0.006058 | E: -62.861236 | E_var:     4.1318 E_err:   0.031761 | NF_loss: 18.295830
[2025-11-13 05:27:08] 279:45<130:07, 7.96s/it | [Iter 2109/3090] R0[2019/3000] | LR: 0.006047 | E: -62.667888 | E_var:     4.5951 E_err:   0.033494 | NF_loss: 22.214345
[2025-11-13 05:27:16] 279:53<129:59, 7.96s/it | [Iter 2110/3090] R0[2020/3000] | LR: 0.006036 | E: -62.817702 | E_var:     4.5727 E_err:   0.033412 | NF_loss: 21.338359
[2025-11-13 05:27:24] 280:01<129:51, 7.96s/it | [Iter 2111/3090] R0[2021/3000] | LR: 0.006025 | E: -62.834580 | E_var:     4.4152 E_err:   0.032832 | NF_loss: 22.698524
[2025-11-13 05:27:32] 280:08<129:43, 7.96s/it | [Iter 2112/3090] R0[2022/3000] | LR: 0.006014 | E: -62.671899 | E_var:     4.7675 E_err:   0.034117 | NF_loss: 21.177558
[2025-11-13 05:27:40] 280:16<129:35, 7.96s/it | [Iter 2113/3090] R0[2023/3000] | LR: 0.006002 | E: -62.817652 | E_var:     4.7287 E_err:   0.033977 | NF_loss: 17.460565
[2025-11-13 05:27:47] 280:24<129:27, 7.96s/it | [Iter 2114/3090] R0[2024/3000] | LR: 0.005991 | E: -62.837045 | E_var:     4.5465 E_err:   0.033317 | NF_loss: 15.524242
[2025-11-13 05:27:55] 280:32<129:19, 7.96s/it | [Iter 2115/3090] R0[2025/3000] | LR: 0.005980 | E: -62.824719 | E_var:     4.6317 E_err:   0.033627 | NF_loss: 20.639164
[2025-11-13 05:28:03] 280:40<129:11, 7.96s/it | [Iter 2116/3090] R0[2026/3000] | LR: 0.005969 | E: -62.908399 | E_var:     4.1927 E_err:   0.031994 | NF_loss: 20.033551
[2025-11-13 05:28:11] 280:47<129:03, 7.96s/it | [Iter 2117/3090] R0[2027/3000] | LR: 0.005958 | E: -62.942147 | E_var:     4.2278 E_err:   0.032128 | NF_loss: 26.350217
[2025-11-13 05:28:19] 280:55<128:55, 7.96s/it | [Iter 2118/3090] R0[2028/3000] | LR: 0.005947 | E: -62.883349 | E_var:     4.4488 E_err:   0.032957 | NF_loss: 14.251312
[2025-11-13 05:28:26] 281:03<128:47, 7.96s/it | [Iter 2119/3090] R0[2029/3000] | LR: 0.005935 | E: -62.919065 | E_var:     4.1400 E_err:   0.031792 | NF_loss: 17.252709
[2025-11-13 05:28:34] 281:11<128:39, 7.96s/it | [Iter 2120/3090] R0[2030/3000] | LR: 0.005924 | E: -62.937145 | E_var:     4.2575 E_err:   0.032240 | NF_loss: 24.136134
[2025-11-13 05:28:42] 281:19<128:31, 7.96s/it | [Iter 2121/3090] R0[2031/3000] | LR: 0.005913 | E: -62.947413 | E_var:     4.3193 E_err:   0.032473 | NF_loss: 26.642493
[2025-11-13 05:28:50] 281:26<128:23, 7.96s/it | [Iter 2122/3090] R0[2032/3000] | LR: 0.005902 | E: -62.953373 | E_var:     4.5347 E_err:   0.033273 | NF_loss: 24.908639
[2025-11-13 05:28:58] 281:34<128:15, 7.96s/it | [Iter 2123/3090] R0[2033/3000] | LR: 0.005891 | E: -62.967937 | E_var:     4.6942 E_err:   0.033853 | NF_loss: 20.544420
[2025-11-13 05:29:05] 281:42<128:07, 7.96s/it | [Iter 2124/3090] R0[2034/3000] | LR: 0.005880 | E: -62.970427 | E_var:     4.5849 E_err:   0.033457 | NF_loss: 15.899923
[2025-11-13 05:29:13] 281:50<127:59, 7.96s/it | [Iter 2125/3090] R0[2035/3000] | LR: 0.005869 | E: -63.013098 | E_var:     4.3819 E_err:   0.032708 | NF_loss: 18.273900
[2025-11-13 05:29:21] 281:58<127:51, 7.96s/it | [Iter 2126/3090] R0[2036/3000] | LR: 0.005858 | E: -62.972047 | E_var:     4.1338 E_err:   0.031768 | NF_loss: 22.652754
[2025-11-13 05:29:29] 282:05<127:43, 7.96s/it | [Iter 2127/3090] R0[2037/3000] | LR: 0.005847 | E: -62.909414 | E_var:     4.0990 E_err:   0.031634 | NF_loss: 18.642338
[2025-11-13 05:29:37] 282:13<127:35, 7.96s/it | [Iter 2128/3090] R0[2038/3000] | LR: 0.005835 | E: -62.991229 | E_var:     4.0627 E_err:   0.031494 | NF_loss: 23.453704
[2025-11-13 05:29:44] 282:21<127:27, 7.96s/it | [Iter 2129/3090] R0[2039/3000] | LR: 0.005824 | E: -62.967055 | E_var:     4.1925 E_err:   0.031993 | NF_loss: 31.204046
[2025-11-13 05:29:52] 282:29<127:19, 7.96s/it | [Iter 2130/3090] R0[2040/3000] | LR: 0.005813 | E: -63.005161 | E_var:     4.2917 E_err:   0.032370 | NF_loss: 18.378346
[2025-11-13 05:30:00] 282:37<127:11, 7.96s/it | [Iter 2131/3090] R0[2041/3000] | LR: 0.005802 | E: -62.948591 | E_var:     4.4988 E_err:   0.033141 | NF_loss: 27.237051
[2025-11-13 05:30:08] 282:45<127:03, 7.96s/it | [Iter 2132/3090] R0[2042/3000] | LR: 0.005791 | E: -62.962111 | E_var:     4.6719 E_err:   0.033773 | NF_loss: 21.384312
[2025-11-13 05:30:16] 282:52<126:55, 7.96s/it | [Iter 2133/3090] R0[2043/3000] | LR: 0.005780 | E: -62.898319 | E_var:     4.2898 E_err:   0.032362 | NF_loss: 16.999814
[2025-11-13 05:30:24] 283:00<126:47, 7.96s/it | [Iter 2134/3090] R0[2044/3000] | LR: 0.005769 | E: -62.932788 | E_var:     4.4815 E_err:   0.033077 | NF_loss: 21.457376
[2025-11-13 05:30:31] 283:08<126:39, 7.96s/it | [Iter 2135/3090] R0[2045/3000] | LR: 0.005758 | E: -62.946204 | E_var:     4.0584 E_err:   0.031477 | NF_loss: 17.284172
[2025-11-13 05:30:39] 283:16<126:31, 7.96s/it | [Iter 2136/3090] R0[2046/3000] | LR: 0.005747 | E: -62.919686 | E_var:     3.9685 E_err:   0.031127 | NF_loss: 23.710174
[2025-11-13 05:30:47] 283:24<126:22, 7.96s/it | [Iter 2137/3090] R0[2047/3000] | LR: 0.005736 | E: -62.999183 | E_var:     4.0699 E_err:   0.031522 | NF_loss: 19.524376
[2025-11-13 05:30:55] 283:31<126:14, 7.96s/it | [Iter 2138/3090] R0[2048/3000] | LR: 0.005725 | E: -62.987349 | E_var:     4.0008 E_err:   0.031253 | NF_loss: 18.742877
[2025-11-13 05:31:03] 283:39<126:06, 7.96s/it | [Iter 2139/3090] R0[2049/3000] | LR: 0.005714 | E: -62.997144 | E_var:     4.0506 E_err:   0.031447 | NF_loss: 15.804657
[2025-11-13 05:31:10] 283:47<125:58, 7.96s/it | [Iter 2140/3090] R0[2050/3000] | LR: 0.005703 | E: -62.959062 | E_var:     3.8669 E_err:   0.030726 | NF_loss: 19.897812
[2025-11-13 05:31:18] 283:55<125:50, 7.96s/it | [Iter 2141/3090] R0[2051/3000] | LR: 0.005692 | E: -62.943587 | E_var:     3.9391 E_err:   0.031011 | NF_loss: 17.232823
[2025-11-13 05:31:26] 284:03<125:42, 7.96s/it | [Iter 2142/3090] R0[2052/3000] | LR: 0.005681 | E: -62.902351 | E_var:     4.4800 E_err:   0.033072 | NF_loss: 17.060042
[2025-11-13 05:31:34] 284:10<125:34, 7.96s/it | [Iter 2143/3090] R0[2053/3000] | LR: 0.005670 | E: -62.899361 | E_var:     4.2197 E_err:   0.032097 | NF_loss: 15.546558
[2025-11-13 05:31:42] 284:18<125:26, 7.96s/it | [Iter 2144/3090] R0[2054/3000] | LR: 0.005659 | E: -62.949124 | E_var:     3.9741 E_err:   0.031149 | NF_loss: 14.652656
[2025-11-13 05:31:49] 284:26<125:18, 7.96s/it | [Iter 2145/3090] R0[2055/3000] | LR: 0.005648 | E: -62.870137 | E_var:     4.1007 E_err:   0.031641 | NF_loss: 16.720234
[2025-11-13 05:31:57] 284:34<125:10, 7.96s/it | [Iter 2146/3090] R0[2056/3000] | LR: 0.005637 | E: -62.934584 | E_var:     3.9063 E_err:   0.030882 | NF_loss: 18.628902
[2025-11-13 05:32:05] 284:42<125:02, 7.96s/it | [Iter 2147/3090] R0[2057/3000] | LR: 0.005626 | E: -62.892071 | E_var:     4.1779 E_err:   0.031937 | NF_loss: 17.714826
[2025-11-13 05:32:13] 284:49<124:54, 7.96s/it | [Iter 2148/3090] R0[2058/3000] | LR: 0.005615 | E: -62.719328 | E_var:     4.2741 E_err:   0.032303 | NF_loss: 16.462486
[2025-11-13 05:32:21] 284:57<124:46, 7.96s/it | [Iter 2149/3090] R0[2059/3000] | LR: 0.005605 | E: -62.809465 | E_var:     4.1236 E_err:   0.031729 | NF_loss: 21.473344
[2025-11-13 05:32:28] 285:05<124:38, 7.96s/it | [Iter 2150/3090] R0[2060/3000] | LR: 0.005594 | E: -62.913249 | E_var:     4.0006 E_err:   0.031252 | NF_loss: 15.208041
[2025-11-13 05:32:36] 285:13<124:30, 7.96s/it | [Iter 2151/3090] R0[2061/3000] | LR: 0.005583 | E: -63.036354 | E_var:     4.1258 E_err:   0.031738 | NF_loss: 17.183840
[2025-11-13 05:32:44] 285:21<124:22, 7.96s/it | [Iter 2152/3090] R0[2062/3000] | LR: 0.005572 | E: -63.030464 | E_var:     4.0351 E_err:   0.031387 | NF_loss: 16.950549
[2025-11-13 05:32:52] 285:28<124:14, 7.96s/it | [Iter 2153/3090] R0[2063/3000] | LR: 0.005561 | E: -63.022725 | E_var:     4.0374 E_err:   0.031396 | NF_loss: 19.511245
[2025-11-13 05:33:00] 285:36<124:06, 7.96s/it | [Iter 2154/3090] R0[2064/3000] | LR: 0.005550 | E: -62.926075 | E_var:     3.9699 E_err:   0.031132 | NF_loss: 21.373200
[2025-11-13 05:33:07] 285:44<123:58, 7.96s/it | [Iter 2155/3090] R0[2065/3000] | LR: 0.005539 | E: -62.984327 | E_var:     3.9800 E_err:   0.031172 | NF_loss: 16.754108
[2025-11-13 05:33:15] 285:52<123:50, 7.96s/it | [Iter 2156/3090] R0[2066/3000] | LR: 0.005528 | E: -62.974739 | E_var:     4.0294 E_err:   0.031365 | NF_loss: 18.399114
[2025-11-13 05:33:23] 286:00<123:42, 7.96s/it | [Iter 2157/3090] R0[2067/3000] | LR: 0.005517 | E: -62.964225 | E_var:     3.8681 E_err:   0.030730 | NF_loss: 16.344633
[2025-11-13 05:33:31] 286:07<123:34, 7.96s/it | [Iter 2158/3090] R0[2068/3000] | LR: 0.005507 | E: -63.014638 | E_var:     3.7724 E_err:   0.030348 | NF_loss: 19.290480
[2025-11-13 05:33:39] 286:15<123:26, 7.96s/it | [Iter 2159/3090] R0[2069/3000] | LR: 0.005496 | E: -62.964161 | E_var:     4.0935 E_err:   0.031613 | NF_loss: 21.054078
[2025-11-13 05:33:47] 286:23<123:18, 7.96s/it | [Iter 2160/3090] R0[2070/3000] | LR: 0.005485 | E: -63.051403 | E_var:     4.0308 E_err:   0.031370 | NF_loss: 17.474968
[2025-11-13 05:33:54] 286:31<123:10, 7.96s/it | [Iter 2161/3090] R0[2071/3000] | LR: 0.005474 | E: -63.038533 | E_var:     3.8196 E_err:   0.030537 | NF_loss: 16.938858
[2025-11-13 05:34:02] 286:39<123:02, 7.96s/it | [Iter 2162/3090] R0[2072/3000] | LR: 0.005463 | E: -63.054504 | E_var:     3.8187 E_err:   0.030533 | NF_loss: 18.753665
[2025-11-13 05:34:10] 286:47<122:54, 7.96s/it | [Iter 2163/3090] R0[2073/3000] | LR: 0.005452 | E: -63.073441 | E_var:     4.1673 E_err:   0.031897 | NF_loss: 15.667195
[2025-11-13 05:34:18] 286:54<122:46, 7.96s/it | [Iter 2164/3090] R0[2074/3000] | LR: 0.005442 | E: -63.084537 | E_var:     4.2295 E_err:   0.032134 | NF_loss: 13.060396
[2025-11-13 05:34:26] 287:02<122:38, 7.96s/it | [Iter 2165/3090] R0[2075/3000] | LR: 0.005431 | E: -63.030385 | E_var:     3.8942 E_err:   0.030834 | NF_loss: 18.554486
[2025-11-13 05:34:34] 287:10<122:30, 7.96s/it | [Iter 2166/3090] R0[2076/3000] | LR: 0.005420 | E: -63.044778 | E_var:     3.9092 E_err:   0.030893 | NF_loss: 16.036771
[2025-11-13 05:34:41] 287:18<122:22, 7.95s/it | [Iter 2167/3090] R0[2077/3000] | LR: 0.005409 | E: -63.039824 | E_var:     3.7866 E_err:   0.030405 | NF_loss: 18.263734
[2025-11-13 05:34:49] 287:26<122:14, 7.95s/it | [Iter 2168/3090] R0[2078/3000] | LR: 0.005398 | E: -62.991709 | E_var:     4.2039 E_err:   0.032037 | NF_loss: 19.909944
[2025-11-13 05:34:57] 287:33<122:06, 7.95s/it | [Iter 2169/3090] R0[2079/3000] | LR: 0.005388 | E: -63.059757 | E_var:     3.7104 E_err:   0.030097 | NF_loss: 18.619739
[2025-11-13 05:35:05] 287:41<121:58, 7.95s/it | [Iter 2170/3090] R0[2080/3000] | LR: 0.005377 | E: -62.999291 | E_var:     3.6348 E_err:   0.029789 | NF_loss: 20.386335
[2025-11-13 05:35:13] 287:49<121:50, 7.95s/it | [Iter 2171/3090] R0[2081/3000] | LR: 0.005366 | E: -63.030890 | E_var:     4.0692 E_err:   0.031519 | NF_loss: 17.452677
[2025-11-13 05:35:20] 287:57<121:42, 7.95s/it | [Iter 2172/3090] R0[2082/3000] | LR: 0.005355 | E: -63.089654 | E_var:     3.7506 E_err:   0.030260 | NF_loss: 18.487193
[2025-11-13 05:35:28] 288:05<121:34, 7.95s/it | [Iter 2173/3090] R0[2083/3000] | LR: 0.005345 | E: -63.050861 | E_var:     4.0430 E_err:   0.031418 | NF_loss: 20.192338
[2025-11-13 05:35:36] 288:12<121:26, 7.95s/it | [Iter 2174/3090] R0[2084/3000] | LR: 0.005334 | E: -63.055109 | E_var:     4.1262 E_err:   0.031739 | NF_loss: 14.616633
[2025-11-13 05:35:44] 288:20<121:18, 7.95s/it | [Iter 2175/3090] R0[2085/3000] | LR: 0.005323 | E: -63.036464 | E_var:     4.0265 E_err:   0.031353 | NF_loss: 15.216342
[2025-11-13 05:35:52] 288:28<121:10, 7.95s/it | [Iter 2176/3090] R0[2086/3000] | LR: 0.005313 | E: -63.029049 | E_var:     3.8329 E_err:   0.030590 | NF_loss: 18.497841
[2025-11-13 05:35:59] 288:36<121:02, 7.95s/it | [Iter 2177/3090] R0[2087/3000] | LR: 0.005302 | E: -63.043415 | E_var:     3.8402 E_err:   0.030619 | NF_loss: 17.683623
[2025-11-13 05:36:07] 288:44<120:54, 7.95s/it | [Iter 2178/3090] R0[2088/3000] | LR: 0.005291 | E: -63.029016 | E_var:     3.8821 E_err:   0.030786 | NF_loss: 17.177417
[2025-11-13 05:36:15] 288:51<120:46, 7.95s/it | [Iter 2179/3090] R0[2089/3000] | LR: 0.005280 | E: -63.040884 | E_var:     3.9234 E_err:   0.030949 | NF_loss: 16.601817
[2025-11-13 05:36:23] 288:59<120:38, 7.95s/it | [Iter 2180/3090] R0[2090/3000] | LR: 0.005270 | E: -63.057590 | E_var:     3.7741 E_err:   0.030355 | NF_loss: 17.253267
[2025-11-13 05:36:31] 289:07<120:30, 7.95s/it | [Iter 2181/3090] R0[2091/3000] | LR: 0.005259 | E: -63.063841 | E_var:     3.9026 E_err:   0.030867 | NF_loss: 17.046428
[2025-11-13 05:36:38] 289:15<120:22, 7.95s/it | [Iter 2182/3090] R0[2092/3000] | LR: 0.005248 | E: -63.069058 | E_var:     3.9226 E_err:   0.030946 | NF_loss: 18.047670
[2025-11-13 05:36:46] 289:23<120:14, 7.95s/it | [Iter 2183/3090] R0[2093/3000] | LR: 0.005238 | E: -63.059850 | E_var:     3.8053 E_err:   0.030480 | NF_loss: 17.170752
[2025-11-13 05:36:54] 289:30<120:06, 7.95s/it | [Iter 2184/3090] R0[2094/3000] | LR: 0.005227 | E: -63.038729 | E_var:     3.8308 E_err:   0.030582 | NF_loss: 15.751404
[2025-11-13 05:37:02] 289:38<119:58, 7.95s/it | [Iter 2185/3090] R0[2095/3000] | LR: 0.005216 | E: -62.974216 | E_var:     4.0879 E_err:   0.031591 | NF_loss: 15.668450
[2025-11-13 05:37:10] 289:46<119:50, 7.95s/it | [Iter 2186/3090] R0[2096/3000] | LR: 0.005206 | E: -62.979588 | E_var:     3.8608 E_err:   0.030701 | NF_loss: 14.838572
[2025-11-13 05:37:17] 289:54<119:42, 7.95s/it | [Iter 2187/3090] R0[2097/3000] | LR: 0.005195 | E: -62.960884 | E_var:     4.1479 E_err:   0.031822 | NF_loss: 16.042486
[2025-11-13 05:37:25] 290:02<119:34, 7.95s/it | [Iter 2188/3090] R0[2098/3000] | LR: 0.005185 | E: -62.974149 | E_var:     3.9948 E_err:   0.031230 | NF_loss: 17.728395
[2025-11-13 05:37:33] 290:09<119:25, 7.95s/it | [Iter 2189/3090] R0[2099/3000] | LR: 0.005174 | E: -63.048250 | E_var:     4.2389 E_err:   0.032170 | NF_loss: 14.640181
[2025-11-13 05:37:41] 290:17<119:17, 7.95s/it | [Iter 2190/3090] R0[2100/3000] | LR: 0.005163 | E: -62.920225 | E_var:     4.1728 E_err:   0.031918 | NF_loss: 14.736135
[2025-11-13 05:37:49] 290:25<119:09, 7.95s/it | [Iter 2191/3090] R0[2101/3000] | LR: 0.005153 | E: -62.877349 | E_var:     4.0269 E_err:   0.031355 | NF_loss: 15.001559
[2025-11-13 05:37:56] 290:33<119:01, 7.95s/it | [Iter 2192/3090] R0[2102/3000] | LR: 0.005142 | E: -63.014711 | E_var:     4.5079 E_err:   0.033175 | NF_loss: 15.917531
[2025-11-13 05:38:04] 290:41<118:53, 7.95s/it | [Iter 2193/3090] R0[2103/3000] | LR: 0.005132 | E: -63.010778 | E_var:     4.6312 E_err:   0.033625 | NF_loss: 19.191302
[2025-11-13 05:38:12] 290:48<118:45, 7.95s/it | [Iter 2194/3090] R0[2104/3000] | LR: 0.005121 | E: -63.015942 | E_var:     4.3840 E_err:   0.032716 | NF_loss: 20.037423
[2025-11-13 05:38:20] 290:56<118:37, 7.95s/it | [Iter 2195/3090] R0[2105/3000] | LR: 0.005110 | E: -63.038538 | E_var:     4.3598 E_err:   0.032625 | NF_loss: 14.812859
[2025-11-13 05:38:28] 291:04<118:29, 7.95s/it | [Iter 2196/3090] R0[2106/3000] | LR: 0.005100 | E: -62.975582 | E_var:     4.1127 E_err:   0.031687 | NF_loss: 15.567753
[2025-11-13 05:38:35] 291:12<118:21, 7.95s/it | [Iter 2197/3090] R0[2107/3000] | LR: 0.005089 | E: -63.012435 | E_var:     4.4592 E_err:   0.032995 | NF_loss: 16.714550
[2025-11-13 05:38:43] 291:20<118:13, 7.95s/it | [Iter 2198/3090] R0[2108/3000] | LR: 0.005079 | E: -63.006309 | E_var:     4.1976 E_err:   0.032013 | NF_loss: 15.808237
[2025-11-13 05:38:51] 291:27<118:05, 7.95s/it | [Iter 2199/3090] R0[2109/3000] | LR: 0.005068 | E: -63.004257 | E_var:     4.1786 E_err:   0.031940 | NF_loss: 16.090257
[2025-11-13 05:38:59] 291:35<117:57, 7.95s/it | [Iter 2200/3090] R0[2110/3000] | LR: 0.005058 | E: -62.993456 | E_var:     3.8748 E_err:   0.030757 | NF_loss: 16.523112
[2025-11-13 05:39:07] 291:43<117:49, 7.95s/it | [Iter 2201/3090] R0[2111/3000] | LR: 0.005047 | E: -63.029311 | E_var:     3.9052 E_err:   0.030877 | NF_loss: 15.118960
[2025-11-13 05:39:14] 291:51<117:41, 7.95s/it | [Iter 2202/3090] R0[2112/3000] | LR: 0.005037 | E: -63.051326 | E_var:     4.2655 E_err:   0.032271 | NF_loss: 17.274065
[2025-11-13 05:39:22] 291:59<117:33, 7.95s/it | [Iter 2203/3090] R0[2113/3000] | LR: 0.005026 | E: -63.050136 | E_var:     4.0594 E_err:   0.031481 | NF_loss: 15.286831
[2025-11-13 05:39:30] 292:06<117:25, 7.95s/it | [Iter 2204/3090] R0[2114/3000] | LR: 0.005016 | E: -63.010937 | E_var:     4.3089 E_err:   0.032434 | NF_loss: 15.930138
[2025-11-13 05:39:38] 292:14<117:17, 7.95s/it | [Iter 2205/3090] R0[2115/3000] | LR: 0.005005 | E: -63.061435 | E_var:     4.0376 E_err:   0.031396 | NF_loss: 15.459488
[2025-11-13 05:39:46] 292:22<117:09, 7.95s/it | [Iter 2206/3090] R0[2116/3000] | LR: 0.004995 | E: -63.074353 | E_var:     4.6839 E_err:   0.033816 | NF_loss: 16.140059
[2025-11-13 05:39:53] 292:30<117:01, 7.95s/it | [Iter 2207/3090] R0[2117/3000] | LR: 0.004984 | E: -62.973924 | E_var:     5.0944 E_err:   0.035267 | NF_loss: 15.626594
[2025-11-13 05:40:01] 292:38<116:53, 7.95s/it | [Iter 2208/3090] R0[2118/3000] | LR: 0.004974 | E: -62.935106 | E_var:     4.4015 E_err:   0.032781 | NF_loss: 19.392341
[2025-11-13 05:40:09] 292:46<116:45, 7.95s/it | [Iter 2209/3090] R0[2119/3000] | LR: 0.004963 | E: -62.946187 | E_var:     4.5618 E_err:   0.033373 | NF_loss: 11.160951
[2025-11-13 05:40:17] 292:54<116:37, 7.95s/it | [Iter 2210/3090] R0[2120/3000] | LR: 0.004953 | E: -62.979037 | E_var:     4.4796 E_err:   0.033070 | NF_loss: 17.816004
[2025-11-13 05:40:25] 293:01<116:29, 7.95s/it | [Iter 2211/3090] R0[2121/3000] | LR: 0.004943 | E: -62.978033 | E_var:     4.2341 E_err:   0.032151 | NF_loss: 18.762109
[2025-11-13 05:40:33] 293:09<116:21, 7.95s/it | [Iter 2212/3090] R0[2122/3000] | LR: 0.004932 | E: -62.963815 | E_var:     4.1937 E_err:   0.031998 | NF_loss: 19.637476
[2025-11-13 05:40:40] 293:17<116:13, 7.95s/it | [Iter 2213/3090] R0[2123/3000] | LR: 0.004922 | E: -62.988374 | E_var:     4.3901 E_err:   0.032738 | NF_loss: 25.676490
[2025-11-13 05:40:48] 293:25<116:05, 7.95s/it | [Iter 2214/3090] R0[2124/3000] | LR: 0.004911 | E: -62.931316 | E_var:     5.0183 E_err:   0.035002 | NF_loss: 27.266076
[2025-11-13 05:40:56] 293:33<115:57, 7.95s/it | [Iter 2215/3090] R0[2125/3000] | LR: 0.004901 | E: -63.000355 | E_var:     4.6103 E_err:   0.033549 | NF_loss: 14.292442
[2025-11-13 05:41:04] 293:40<115:49, 7.95s/it | [Iter 2216/3090] R0[2126/3000] | LR: 0.004891 | E: -63.021540 | E_var:     4.4499 E_err:   0.032961 | NF_loss: 18.741187
[2025-11-13 05:41:12] 293:48<115:41, 7.95s/it | [Iter 2217/3090] R0[2127/3000] | LR: 0.004880 | E: -62.955628 | E_var:     4.5074 E_err:   0.033173 | NF_loss: 17.989807
[2025-11-13 05:41:19] 293:56<115:33, 7.95s/it | [Iter 2218/3090] R0[2128/3000] | LR: 0.004870 | E: -62.967300 | E_var:     4.4624 E_err:   0.033007 | NF_loss: 17.166471
[2025-11-13 05:41:27] 294:04<115:25, 7.95s/it | [Iter 2219/3090] R0[2129/3000] | LR: 0.004859 | E: -62.961410 | E_var:     4.0550 E_err:   0.031464 | NF_loss: 48.120005
[2025-11-13 05:41:35] 294:12<115:17, 7.95s/it | [Iter 2220/3090] R0[2130/3000] | LR: 0.004849 | E: -62.968503 | E_var:     4.1744 E_err:   0.031924 | NF_loss: 16.527356
[2025-11-13 05:41:43] 294:19<115:09, 7.95s/it | [Iter 2221/3090] R0[2131/3000] | LR: 0.004839 | E: -62.997843 | E_var:     4.1984 E_err:   0.032016 | NF_loss: 19.968884
[2025-11-13 05:41:51] 294:27<115:01, 7.95s/it | [Iter 2222/3090] R0[2132/3000] | LR: 0.004828 | E: -63.024768 | E_var:     4.1982 E_err:   0.032015 | NF_loss: 15.888014
[2025-11-13 05:41:58] 294:35<114:53, 7.95s/it | [Iter 2223/3090] R0[2133/3000] | LR: 0.004818 | E: -62.989465 | E_var:     4.0868 E_err:   0.031587 | NF_loss: 23.805939
[2025-11-13 05:42:06] 294:43<114:45, 7.95s/it | [Iter 2224/3090] R0[2134/3000] | LR: 0.004808 | E: -62.986875 | E_var:     4.3063 E_err:   0.032424 | NF_loss: 22.087182
[2025-11-13 05:42:14] 294:51<114:37, 7.95s/it | [Iter 2225/3090] R0[2135/3000] | LR: 0.004797 | E: -62.996329 | E_var:     4.3663 E_err:   0.032649 | NF_loss: 17.424022
[2025-11-13 05:42:22] 294:58<114:29, 7.95s/it | [Iter 2226/3090] R0[2136/3000] | LR: 0.004787 | E: -62.923426 | E_var:     4.4036 E_err:   0.032789 | NF_loss: 25.481322
[2025-11-13 05:42:30] 295:06<114:21, 7.95s/it | [Iter 2227/3090] R0[2137/3000] | LR: 0.004777 | E: -62.925250 | E_var:     4.3127 E_err:   0.032448 | NF_loss: 24.643243
[2025-11-13 05:42:37] 295:14<114:13, 7.95s/it | [Iter 2228/3090] R0[2138/3000] | LR: 0.004767 | E: -62.928835 | E_var:     3.9324 E_err:   0.030985 | NF_loss: 21.951831
[2025-11-13 05:42:45] 295:22<114:05, 7.95s/it | [Iter 2229/3090] R0[2139/3000] | LR: 0.004756 | E: -62.988215 | E_var:     4.2187 E_err:   0.032093 | NF_loss: 13.808924
[2025-11-13 05:42:53] 295:30<113:57, 7.95s/it | [Iter 2230/3090] R0[2140/3000] | LR: 0.004746 | E: -63.029701 | E_var:     4.2883 E_err:   0.032356 | NF_loss: 21.421270
[2025-11-13 05:43:01] 295:37<113:49, 7.95s/it | [Iter 2231/3090] R0[2141/3000] | LR: 0.004736 | E: -62.971554 | E_var:     4.3104 E_err:   0.032440 | NF_loss: 19.381788
[2025-11-13 05:43:09] 295:45<113:41, 7.95s/it | [Iter 2232/3090] R0[2142/3000] | LR: 0.004725 | E: -62.940455 | E_var:     4.0385 E_err:   0.031400 | NF_loss: 18.587967
[2025-11-13 05:43:16] 295:53<113:33, 7.95s/it | [Iter 2233/3090] R0[2143/3000] | LR: 0.004715 | E: -63.018581 | E_var:     4.0859 E_err:   0.031584 | NF_loss: 22.656836
[2025-11-13 05:43:24] 296:01<113:25, 7.95s/it | [Iter 2234/3090] R0[2144/3000] | LR: 0.004705 | E: -62.929664 | E_var:     4.0879 E_err:   0.031591 | NF_loss: 29.571804
[2025-11-13 05:43:32] 296:09<113:17, 7.95s/it | [Iter 2235/3090] R0[2145/3000] | LR: 0.004695 | E: -62.922281 | E_var:     4.2514 E_err:   0.032217 | NF_loss: 21.559237
[2025-11-13 05:43:40] 296:16<113:09, 7.95s/it | [Iter 2236/3090] R0[2146/3000] | LR: 0.004685 | E: -62.951723 | E_var:     4.2029 E_err:   0.032033 | NF_loss: 21.628453
[2025-11-13 05:43:48] 296:24<113:01, 7.95s/it | [Iter 2237/3090] R0[2147/3000] | LR: 0.004674 | E: -62.986447 | E_var:     4.2608 E_err:   0.032253 | NF_loss: 16.096940
[2025-11-13 05:43:55] 296:32<112:53, 7.95s/it | [Iter 2238/3090] R0[2148/3000] | LR: 0.004664 | E: -62.991275 | E_var:     4.1368 E_err:   0.031780 | NF_loss: 23.410912
[2025-11-13 05:44:03] 296:40<112:45, 7.95s/it | [Iter 2239/3090] R0[2149/3000] | LR: 0.004654 | E: -63.037000 | E_var:     4.2931 E_err:   0.032375 | NF_loss: 19.962082
[2025-11-13 05:44:11] 296:48<112:37, 7.95s/it | [Iter 2240/3090] R0[2150/3000] | LR: 0.004644 | E: -63.052613 | E_var:     3.7721 E_err:   0.030347 | NF_loss: 20.177288
[2025-11-13 05:44:19] 296:55<112:29, 7.95s/it | [Iter 2241/3090] R0[2151/3000] | LR: 0.004634 | E: -63.006856 | E_var:     3.9948 E_err:   0.031230 | NF_loss: 27.175614
[2025-11-13 05:44:27] 297:03<112:21, 7.95s/it | [Iter 2242/3090] R0[2152/3000] | LR: 0.004623 | E: -62.934848 | E_var:     4.0138 E_err:   0.031304 | NF_loss: 9.238208
[2025-11-13 05:44:34] 297:11<112:13, 7.95s/it | [Iter 2243/3090] R0[2153/3000] | LR: 0.004613 | E: -63.047596 | E_var:     4.0022 E_err:   0.031258 | NF_loss: 29.505002
[2025-11-13 05:44:42] 297:19<112:05, 7.95s/it | [Iter 2244/3090] R0[2154/3000] | LR: 0.004603 | E: -63.021231 | E_var:     4.2764 E_err:   0.032312 | NF_loss: 27.845902
[2025-11-13 05:44:50] 297:27<111:57, 7.95s/it | [Iter 2245/3090] R0[2155/3000] | LR: 0.004593 | E: -63.011404 | E_var:     4.0409 E_err:   0.031409 | NF_loss: 20.010006
[2025-11-13 05:44:58] 297:35<111:49, 7.95s/it | [Iter 2246/3090] R0[2156/3000] | LR: 0.004583 | E: -62.967210 | E_var:     4.3636 E_err:   0.032640 | NF_loss: 16.426483
[2025-11-13 05:45:06] 297:42<111:41, 7.95s/it | [Iter 2247/3090] R0[2157/3000] | LR: 0.004573 | E: -62.976062 | E_var:     3.9411 E_err:   0.031019 | NF_loss: 19.627826
[2025-11-13 05:45:14] 297:50<111:33, 7.95s/it | [Iter 2248/3090] R0[2158/3000] | LR: 0.004563 | E: -62.937483 | E_var:     3.9692 E_err:   0.031129 | NF_loss: 20.548771
[2025-11-13 05:45:21] 297:58<111:25, 7.95s/it | [Iter 2249/3090] R0[2159/3000] | LR: 0.004552 | E: -62.925541 | E_var:     4.2980 E_err:   0.032393 | NF_loss: 19.221915
[2025-11-13 05:45:29] 298:06<111:17, 7.95s/it | [Iter 2250/3090] R0[2160/3000] | LR: 0.004542 | E: -62.892396 | E_var:     4.3761 E_err:   0.032686 | NF_loss: 15.345711
[2025-11-13 05:45:37] 298:14<111:09, 7.95s/it | [Iter 2251/3090] R0[2161/3000] | LR: 0.004532 | E: -62.784922 | E_var:     4.4979 E_err:   0.033138 | NF_loss: 19.307198
[2025-11-13 05:45:45] 298:21<111:01, 7.95s/it | [Iter 2252/3090] R0[2162/3000] | LR: 0.004522 | E: -62.802178 | E_var:     4.4880 E_err:   0.033102 | NF_loss: 12.635252
[2025-11-13 05:45:53] 298:29<110:53, 7.95s/it | [Iter 2253/3090] R0[2163/3000] | LR: 0.004512 | E: -62.866771 | E_var:     4.1682 E_err:   0.031900 | NF_loss: 12.553369
[2025-11-13 05:46:00] 298:37<110:45, 7.95s/it | [Iter 2254/3090] R0[2164/3000] | LR: 0.004502 | E: -62.894958 | E_var:     4.0588 E_err:   0.031479 | NF_loss: 20.954185
[2025-11-13 05:46:08] 298:45<110:37, 7.95s/it | [Iter 2255/3090] R0[2165/3000] | LR: 0.004492 | E: -62.874986 | E_var:     4.1083 E_err:   0.031670 | NF_loss: 14.913452
[2025-11-13 05:46:16] 298:53<110:29, 7.95s/it | [Iter 2256/3090] R0[2166/3000] | LR: 0.004482 | E: -62.941949 | E_var:     4.0157 E_err:   0.031311 | NF_loss: 23.043270
[2025-11-13 05:46:24] 299:00<110:21, 7.95s/it | [Iter 2257/3090] R0[2167/3000] | LR: 0.004472 | E: -62.923364 | E_var:     4.0900 E_err:   0.031600 | NF_loss: 19.411284
[2025-11-13 05:46:32] 299:08<110:13, 7.95s/it | [Iter 2258/3090] R0[2168/3000] | LR: 0.004462 | E: -62.920914 | E_var:     4.0555 E_err:   0.031466 | NF_loss: 26.301539
[2025-11-13 05:46:40] 299:16<110:05, 7.95s/it | [Iter 2259/3090] R0[2169/3000] | LR: 0.004452 | E: -62.933816 | E_var:     4.1967 E_err:   0.032009 | NF_loss: 19.598090
[2025-11-13 05:46:47] 299:24<109:57, 7.95s/it | [Iter 2260/3090] R0[2170/3000] | LR: 0.004442 | E: -62.905747 | E_var:     4.5140 E_err:   0.033197 | NF_loss: 27.694610
[2025-11-13 05:46:55] 299:32<109:49, 7.95s/it | [Iter 2261/3090] R0[2171/3000] | LR: 0.004432 | E: -62.891818 | E_var:     3.9091 E_err:   0.030893 | NF_loss: 18.880223
[2025-11-13 05:47:03] 299:39<109:41, 7.95s/it | [Iter 2262/3090] R0[2172/3000] | LR: 0.004422 | E: -62.948787 | E_var:     3.9585 E_err:   0.031087 | NF_loss: 25.088324
[2025-11-13 05:47:11] 299:47<109:33, 7.95s/it | [Iter 2263/3090] R0[2173/3000] | LR: 0.004412 | E: -62.905223 | E_var:     4.1645 E_err:   0.031886 | NF_loss: 18.560166
[2025-11-13 05:47:19] 299:55<109:25, 7.95s/it | [Iter 2264/3090] R0[2174/3000] | LR: 0.004402 | E: -62.938543 | E_var:     4.0738 E_err:   0.031537 | NF_loss: 19.251151
[2025-11-13 05:47:26] 300:03<109:17, 7.95s/it | [Iter 2265/3090] R0[2175/3000] | LR: 0.004392 | E: -62.898893 | E_var:     4.0652 E_err:   0.031504 | NF_loss: 18.176538
[2025-11-13 05:47:34] 300:11<109:09, 7.95s/it | [Iter 2266/3090] R0[2176/3000] | LR: 0.004382 | E: -62.900745 | E_var:     4.0978 E_err:   0.031630 | NF_loss: 16.509433
[2025-11-13 05:47:42] 300:18<109:01, 7.95s/it | [Iter 2267/3090] R0[2177/3000] | LR: 0.004372 | E: -62.919486 | E_var:     4.6191 E_err:   0.033581 | NF_loss: 16.361701
[2025-11-13 05:47:50] 300:26<108:53, 7.95s/it | [Iter 2268/3090] R0[2178/3000] | LR: 0.004362 | E: -62.921017 | E_var:     4.8851 E_err:   0.034535 | NF_loss: 14.835177
[2025-11-13 05:47:58] 300:34<108:45, 7.95s/it | [Iter 2269/3090] R0[2179/3000] | LR: 0.004352 | E: -62.885557 | E_var:     4.0307 E_err:   0.031370 | NF_loss: 18.826872
[2025-11-13 05:48:05] 300:42<108:37, 7.95s/it | [Iter 2270/3090] R0[2180/3000] | LR: 0.004342 | E: -62.921505 | E_var:     4.1700 E_err:   0.031907 | NF_loss: 14.522175
[2025-11-13 05:48:13] 300:50<108:29, 7.95s/it | [Iter 2271/3090] R0[2181/3000] | LR: 0.004332 | E: -62.918199 | E_var:     3.9158 E_err:   0.030919 | NF_loss: 15.559126
[2025-11-13 05:48:21] 300:57<108:21, 7.95s/it | [Iter 2272/3090] R0[2182/3000] | LR: 0.004322 | E: -62.915142 | E_var:     4.0586 E_err:   0.031478 | NF_loss: 18.322295
[2025-11-13 05:48:29] 301:05<108:13, 7.95s/it | [Iter 2273/3090] R0[2183/3000] | LR: 0.004313 | E: -62.794145 | E_var:     4.1942 E_err:   0.032000 | NF_loss: 12.402136
[2025-11-13 05:48:37] 301:13<108:05, 7.95s/it | [Iter 2274/3090] R0[2184/3000] | LR: 0.004303 | E: -62.718396 | E_var:     4.4525 E_err:   0.032970 | NF_loss: 23.185321
[2025-11-13 05:48:45] 301:21<107:57, 7.95s/it | [Iter 2275/3090] R0[2185/3000] | LR: 0.004293 | E: -62.681224 | E_var:     4.4036 E_err:   0.032789 | NF_loss: 16.311520
[2025-11-13 05:48:52] 301:29<107:49, 7.95s/it | [Iter 2276/3090] R0[2186/3000] | LR: 0.004283 | E: -62.812607 | E_var:     3.9973 E_err:   0.031239 | NF_loss: 14.582717
[2025-11-13 05:49:00] 301:37<107:41, 7.95s/it | [Iter 2277/3090] R0[2187/3000] | LR: 0.004273 | E: -62.849450 | E_var:     4.1125 E_err:   0.031687 | NF_loss: 17.271369
[2025-11-13 05:49:08] 301:45<107:33, 7.95s/it | [Iter 2278/3090] R0[2188/3000] | LR: 0.004263 | E: -62.816135 | E_var:     4.3297 E_err:   0.032512 | NF_loss: 14.514690
[2025-11-13 05:49:16] 301:52<107:25, 7.95s/it | [Iter 2279/3090] R0[2189/3000] | LR: 0.004253 | E: -62.847383 | E_var:     4.4270 E_err:   0.032876 | NF_loss: 42.161704
[2025-11-13 05:49:24] 302:00<107:17, 7.95s/it | [Iter 2280/3090] R0[2190/3000] | LR: 0.004244 | E: -62.942663 | E_var:     4.4688 E_err:   0.033031 | NF_loss: 23.196947
[2025-11-13 05:49:31] 302:08<107:09, 7.95s/it | [Iter 2281/3090] R0[2191/3000] | LR: 0.004234 | E: -62.935072 | E_var:     4.0760 E_err:   0.031546 | NF_loss: 15.318666
[2025-11-13 05:49:39] 302:16<107:01, 7.95s/it | [Iter 2282/3090] R0[2192/3000] | LR: 0.004224 | E: -62.937390 | E_var:     3.9962 E_err:   0.031235 | NF_loss: 15.546748
[2025-11-13 05:49:47] 302:24<106:53, 7.95s/it | [Iter 2283/3090] R0[2193/3000] | LR: 0.004214 | E: -62.824591 | E_var:     3.9470 E_err:   0.031042 | NF_loss: 17.453965
[2025-11-13 05:49:55] 302:31<106:45, 7.95s/it | [Iter 2284/3090] R0[2194/3000] | LR: 0.004204 | E: -62.900119 | E_var:     4.0465 E_err:   0.031431 | NF_loss: 14.563040
[2025-11-13 05:50:03] 302:39<106:37, 7.95s/it | [Iter 2285/3090] R0[2195/3000] | LR: 0.004194 | E: -62.943696 | E_var:     4.2715 E_err:   0.032293 | NF_loss: 16.427432
[2025-11-13 05:50:10] 302:47<106:29, 7.95s/it | [Iter 2286/3090] R0[2196/3000] | LR: 0.004185 | E: -62.905918 | E_var:     4.1284 E_err:   0.031748 | NF_loss: 15.887240
[2025-11-13 05:50:18] 302:55<106:21, 7.95s/it | [Iter 2287/3090] R0[2197/3000] | LR: 0.004175 | E: -62.880915 | E_var:     4.1005 E_err:   0.031640 | NF_loss: 17.131064
[2025-11-13 05:50:26] 303:03<106:13, 7.95s/it | [Iter 2288/3090] R0[2198/3000] | LR: 0.004165 | E: -62.849156 | E_var:     4.1993 E_err:   0.032019 | NF_loss: 18.417161
[2025-11-13 05:50:34] 303:10<106:05, 7.95s/it | [Iter 2289/3090] R0[2199/3000] | LR: 0.004155 | E: -62.852183 | E_var:     4.0026 E_err:   0.031260 | NF_loss: 23.461874
[2025-11-13 05:50:42] 303:18<105:57, 7.95s/it | [Iter 2290/3090] R0[2200/3000] | LR: 0.004146 | E: -62.790297 | E_var:     4.1510 E_err:   0.031835 | NF_loss: 17.481834
[2025-11-13 05:50:49] 303:26<105:49, 7.95s/it | [Iter 2291/3090] R0[2201/3000] | LR: 0.004136 | E: -62.708309 | E_var:     4.1266 E_err:   0.031741 | NF_loss: 14.930690
[2025-11-13 05:50:57] 303:34<105:41, 7.95s/it | [Iter 2292/3090] R0[2202/3000] | LR: 0.004126 | E: -62.871404 | E_var:     4.1899 E_err:   0.031983 | NF_loss: 17.760592
[2025-11-13 05:51:05] 303:42<105:33, 7.95s/it | [Iter 2293/3090] R0[2203/3000] | LR: 0.004117 | E: -62.856678 | E_var:     4.0357 E_err:   0.031389 | NF_loss: 16.896888
[2025-11-13 05:51:13] 303:49<105:25, 7.95s/it | [Iter 2294/3090] R0[2204/3000] | LR: 0.004107 | E: -62.898705 | E_var:     4.0967 E_err:   0.031626 | NF_loss: 18.102617
[2025-11-13 05:51:21] 303:57<105:17, 7.95s/it | [Iter 2295/3090] R0[2205/3000] | LR: 0.004097 | E: -62.910240 | E_var:     3.9822 E_err:   0.031180 | NF_loss: 15.515161
[2025-11-13 05:51:28] 304:05<105:09, 7.95s/it | [Iter 2296/3090] R0[2206/3000] | LR: 0.004087 | E: -62.912533 | E_var:     3.9372 E_err:   0.031004 | NF_loss: 16.895898
[2025-11-13 05:51:36] 304:13<105:01, 7.95s/it | [Iter 2297/3090] R0[2207/3000] | LR: 0.004078 | E: -62.928378 | E_var:     4.0967 E_err:   0.031626 | NF_loss: 15.262691
[2025-11-13 05:51:44] 304:21<104:53, 7.95s/it | [Iter 2298/3090] R0[2208/3000] | LR: 0.004068 | E: -62.906495 | E_var:     4.0313 E_err:   0.031372 | NF_loss: 16.911264
[2025-11-13 05:51:52] 304:28<104:45, 7.95s/it | [Iter 2299/3090] R0[2209/3000] | LR: 0.004058 | E: -62.911773 | E_var:     4.0596 E_err:   0.031482 | NF_loss: 15.704936
[2025-11-13 05:52:00] 304:36<104:37, 7.95s/it | [Iter 2300/3090] R0[2210/3000] | LR: 0.004049 | E: -62.899024 | E_var:     4.0120 E_err:   0.031297 | NF_loss: 19.092998
[2025-11-13 05:52:07] 304:44<104:29, 7.95s/it | [Iter 2301/3090] R0[2211/3000] | LR: 0.004039 | E: -62.848346 | E_var:     3.8255 E_err:   0.030561 | NF_loss: 17.884980
[2025-11-13 05:52:15] 304:52<104:21, 7.95s/it | [Iter 2302/3090] R0[2212/3000] | LR: 0.004030 | E: -62.906534 | E_var:     4.0401 E_err:   0.031406 | NF_loss: 14.456179
[2025-11-13 05:52:23] 305:00<104:13, 7.95s/it | [Iter 2303/3090] R0[2213/3000] | LR: 0.004020 | E: -62.813517 | E_var:     3.9712 E_err:   0.031137 | NF_loss: 13.606243
[2025-11-13 05:52:31] 305:07<104:05, 7.95s/it | [Iter 2304/3090] R0[2214/3000] | LR: 0.004010 | E: -62.872180 | E_var:     3.9223 E_err:   0.030945 | NF_loss: 18.397825
[2025-11-13 05:52:39] 305:15<103:57, 7.95s/it | [Iter 2305/3090] R0[2215/3000] | LR: 0.004001 | E: -62.912682 | E_var:     3.8275 E_err:   0.030569 | NF_loss: 17.157746
[2025-11-13 05:52:47] 305:23<103:49, 7.95s/it | [Iter 2306/3090] R0[2216/3000] | LR: 0.003991 | E: -62.930622 | E_var:     3.8549 E_err:   0.030678 | NF_loss: 15.435550
[2025-11-13 05:52:54] 305:31<103:41, 7.95s/it | [Iter 2307/3090] R0[2217/3000] | LR: 0.003981 | E: -63.042049 | E_var:     4.1064 E_err:   0.031663 | NF_loss: 14.225978
[2025-11-13 05:53:02] 305:39<103:33, 7.95s/it | [Iter 2308/3090] R0[2218/3000] | LR: 0.003972 | E: -63.013809 | E_var:     4.6799 E_err:   0.033802 | NF_loss: 16.934111
[2025-11-13 05:53:10] 305:46<103:25, 7.95s/it | [Iter 2309/3090] R0[2219/3000] | LR: 0.003962 | E: -62.983866 | E_var:     4.4201 E_err:   0.032850 | NF_loss: 18.150373
[2025-11-13 05:53:18] 305:54<103:17, 7.95s/it | [Iter 2310/3090] R0[2220/3000] | LR: 0.003953 | E: -62.994299 | E_var:     4.2083 E_err:   0.032053 | NF_loss: 11.840600
[2025-11-13 05:53:26] 306:02<103:09, 7.95s/it | [Iter 2311/3090] R0[2221/3000] | LR: 0.003943 | E: -62.994555 | E_var:     4.1751 E_err:   0.031927 | NF_loss: 21.334211
[2025-11-13 05:53:33] 306:10<103:01, 7.95s/it | [Iter 2312/3090] R0[2222/3000] | LR: 0.003934 | E: -63.033111 | E_var:     3.6952 E_err:   0.030036 | NF_loss: 15.002516
[2025-11-13 05:53:41] 306:18<102:53, 7.95s/it | [Iter 2313/3090] R0[2223/3000] | LR: 0.003924 | E: -63.017273 | E_var:     4.0363 E_err:   0.031392 | NF_loss: 19.474066
[2025-11-13 05:53:49] 306:26<102:45, 7.95s/it | [Iter 2314/3090] R0[2224/3000] | LR: 0.003915 | E: -63.034579 | E_var:     4.2164 E_err:   0.032084 | NF_loss: 17.790642
[2025-11-13 05:53:57] 306:33<102:37, 7.95s/it | [Iter 2315/3090] R0[2225/3000] | LR: 0.003905 | E: -62.989499 | E_var:     4.3558 E_err:   0.032610 | NF_loss: 20.907476
[2025-11-13 05:54:05] 306:41<102:29, 7.95s/it | [Iter 2316/3090] R0[2226/3000] | LR: 0.003896 | E: -63.006770 | E_var:     4.4964 E_err:   0.033132 | NF_loss: 16.828124
[2025-11-13 05:54:13] 306:49<102:21, 7.95s/it | [Iter 2317/3090] R0[2227/3000] | LR: 0.003886 | E: -62.941535 | E_var:     4.0618 E_err:   0.031490 | NF_loss: 15.669946
[2025-11-13 05:54:20] 306:57<102:13, 7.95s/it | [Iter 2318/3090] R0[2228/3000] | LR: 0.003877 | E: -63.015440 | E_var:     4.3196 E_err:   0.032475 | NF_loss: 24.862000
[2025-11-13 05:54:28] 307:05<102:05, 7.95s/it | [Iter 2319/3090] R0[2229/3000] | LR: 0.003867 | E: -63.017591 | E_var:     4.2854 E_err:   0.032346 | NF_loss: 18.435085
[2025-11-13 05:54:36] 307:12<101:57, 7.95s/it | [Iter 2320/3090] R0[2230/3000] | LR: 0.003858 | E: -62.946388 | E_var:     4.2619 E_err:   0.032257 | NF_loss: 18.274372
[2025-11-13 05:54:44] 307:20<101:49, 7.95s/it | [Iter 2321/3090] R0[2231/3000] | LR: 0.003848 | E: -62.944730 | E_var:     3.9428 E_err:   0.031026 | NF_loss: 14.536582
[2025-11-13 05:54:52] 307:28<101:41, 7.95s/it | [Iter 2322/3090] R0[2232/3000] | LR: 0.003839 | E: -62.926318 | E_var:     4.2098 E_err:   0.032059 | NF_loss: 21.628925
[2025-11-13 05:54:59] 307:36<101:33, 7.95s/it | [Iter 2323/3090] R0[2233/3000] | LR: 0.003829 | E: -62.912952 | E_var:     4.4179 E_err:   0.032842 | NF_loss: 15.692155
[2025-11-13 05:55:07] 307:44<101:25, 7.95s/it | [Iter 2324/3090] R0[2234/3000] | LR: 0.003820 | E: -62.988974 | E_var:     4.0923 E_err:   0.031608 | NF_loss: 11.040327
[2025-11-13 05:55:15] 307:52<101:17, 7.94s/it | [Iter 2325/3090] R0[2235/3000] | LR: 0.003811 | E: -63.025711 | E_var:     4.2298 E_err:   0.032135 | NF_loss: 13.969551
[2025-11-13 05:55:23] 307:59<101:09, 7.94s/it | [Iter 2326/3090] R0[2236/3000] | LR: 0.003801 | E: -62.960060 | E_var:     4.2101 E_err:   0.032060 | NF_loss: 15.218474
[2025-11-13 05:55:31] 308:07<101:01, 7.94s/it | [Iter 2327/3090] R0[2237/3000] | LR: 0.003792 | E: -63.000517 | E_var:     4.3456 E_err:   0.032572 | NF_loss: 17.256037
[2025-11-13 05:55:38] 308:15<100:53, 7.94s/it | [Iter 2328/3090] R0[2238/3000] | LR: 0.003782 | E: -62.971031 | E_var:     4.4144 E_err:   0.032829 | NF_loss: 15.483389
[2025-11-13 05:55:46] 308:23<100:45, 7.94s/it | [Iter 2329/3090] R0[2239/3000] | LR: 0.003773 | E: -62.909601 | E_var:     4.6214 E_err:   0.033590 | NF_loss: 14.997515
[2025-11-13 05:55:54] 308:31<100:37, 7.94s/it | [Iter 2330/3090] R0[2240/3000] | LR: 0.003764 | E: -62.947057 | E_var:     4.6485 E_err:   0.033688 | NF_loss: 14.198072
[2025-11-13 05:56:02] 308:38<100:29, 7.94s/it | [Iter 2331/3090] R0[2241/3000] | LR: 0.003754 | E: -62.944808 | E_var:     4.0600 E_err:   0.031483 | NF_loss: 16.397080
[2025-11-13 05:56:10] 308:46<100:21, 7.94s/it | [Iter 2332/3090] R0[2242/3000] | LR: 0.003745 | E: -62.954678 | E_var:     3.9293 E_err:   0.030973 | NF_loss: 16.470642
[2025-11-13 05:56:17] 308:54<100:13, 7.94s/it | [Iter 2333/3090] R0[2243/3000] | LR: 0.003736 | E: -62.995069 | E_var:     4.2713 E_err:   0.032292 | NF_loss: 10.857973
[2025-11-13 05:56:25] 309:02<100:05, 7.94s/it | [Iter 2334/3090] R0[2244/3000] | LR: 0.003726 | E: -63.015667 | E_var:     4.1165 E_err:   0.031702 | NF_loss: 16.038944
[2025-11-13 05:56:33] 309:10<99:57, 7.94s/it | [Iter 2335/3090] R0[2245/3000] | LR: 0.003717 | E: -62.972521 | E_var:     4.1207 E_err:   0.031718 | NF_loss: 18.962520
[2025-11-13 05:56:41] 309:17<99:49, 7.94s/it | [Iter 2336/3090] R0[2246/3000] | LR: 0.003708 | E: -62.977631 | E_var:     3.6870 E_err:   0.030003 | NF_loss: 20.679253
[2025-11-13 05:56:49] 309:25<99:41, 7.94s/it | [Iter 2337/3090] R0[2247/3000] | LR: 0.003698 | E: -63.027299 | E_var:     3.9115 E_err:   0.030902 | NF_loss: 19.759578
[2025-11-13 05:56:56] 309:33<99:33, 7.94s/it | [Iter 2338/3090] R0[2248/3000] | LR: 0.003689 | E: -62.945984 | E_var:     3.8916 E_err:   0.030824 | NF_loss: 13.350161
[2025-11-13 05:57:04] 309:41<99:26, 7.94s/it | [Iter 2339/3090] R0[2249/3000] | LR: 0.003680 | E: -62.922058 | E_var:     4.0188 E_err:   0.031323 | NF_loss: 20.328988
[2025-11-13 05:57:12] 309:49<99:18, 7.94s/it | [Iter 2340/3090] R0[2250/3000] | LR: 0.003671 | E: -62.902296 | E_var:     4.0763 E_err:   0.031547 | NF_loss: 69.231991
[2025-11-13 05:57:20] 309:56<99:10, 7.94s/it | [Iter 2341/3090] R0[2251/3000] | LR: 0.003661 | E: -62.894742 | E_var:     4.0162 E_err:   0.031313 | NF_loss: 238.684358
[2025-11-13 05:57:28] 310:04<99:02, 7.94s/it | [Iter 2342/3090] R0[2252/3000] | LR: 0.003652 | E: -62.811999 | E_var:     3.9801 E_err:   0.031172 | NF_loss: 216.478323
[2025-11-13 05:57:35] 310:12<98:54, 7.94s/it | [Iter 2343/3090] R0[2253/3000] | LR: 0.003643 | E: -62.888652 | E_var:     4.0058 E_err:   0.031273 | NF_loss: 225.168961
[2025-11-13 05:57:43] 310:20<98:46, 7.94s/it | [Iter 2344/3090] R0[2254/3000] | LR: 0.003634 | E: -63.019525 | E_var:     3.9650 E_err:   0.031113 | NF_loss: 118.084016
[2025-11-13 05:57:51] 310:27<98:38, 7.94s/it | [Iter 2345/3090] R0[2255/3000] | LR: 0.003624 | E: -63.034861 | E_var:     3.9314 E_err:   0.030981 | NF_loss: 142.448292
[2025-11-13 05:57:59] 310:35<98:30, 7.94s/it | [Iter 2346/3090] R0[2256/3000] | LR: 0.003615 | E: -63.078062 | E_var:     3.9968 E_err:   0.031238 | NF_loss: 580.105493
[2025-11-13 05:58:07] 310:43<98:22, 7.94s/it | [Iter 2347/3090] R0[2257/3000] | LR: 0.003606 | E: -63.014183 | E_var:     3.9377 E_err:   0.031006 | NF_loss: 109.277449
[2025-11-13 05:58:14] 310:51<98:14, 7.94s/it | [Iter 2348/3090] R0[2258/3000] | LR: 0.003597 | E: -63.017350 | E_var:     3.9187 E_err:   0.030931 | NF_loss: 246.465349
[2025-11-13 05:58:22] 310:59<98:06, 7.94s/it | [Iter 2349/3090] R0[2259/3000] | LR: 0.003588 | E: -62.934237 | E_var:     3.9299 E_err:   0.030975 | NF_loss: 114.971396
[2025-11-13 05:58:30] 311:07<97:58, 7.94s/it | [Iter 2350/3090] R0[2260/3000] | LR: 0.003578 | E: -62.942381 | E_var:     3.7047 E_err:   0.030074 | NF_loss: 151.977815
[2025-11-13 05:58:38] 311:14<97:50, 7.94s/it | [Iter 2351/3090] R0[2261/3000] | LR: 0.003569 | E: -62.937770 | E_var:     3.6230 E_err:   0.029741 | NF_loss: 207.953802
[2025-11-13 05:58:46] 311:22<97:42, 7.94s/it | [Iter 2352/3090] R0[2262/3000] | LR: 0.003560 | E: -62.975343 | E_var:     3.8395 E_err:   0.030617 | NF_loss: 66.787700
[2025-11-13 05:58:54] 311:30<97:34, 7.94s/it | [Iter 2353/3090] R0[2263/3000] | LR: 0.003551 | E: -63.014065 | E_var:     3.7321 E_err:   0.030185 | NF_loss: 74.381333
[2025-11-13 05:59:01] 311:38<97:26, 7.94s/it | [Iter 2354/3090] R0[2264/3000] | LR: 0.003542 | E: -62.933804 | E_var:     3.8463 E_err:   0.030644 | NF_loss: 113.229844
[2025-11-13 05:59:09] 311:46<97:18, 7.94s/it | [Iter 2355/3090] R0[2265/3000] | LR: 0.003533 | E: -62.935400 | E_var:     3.8821 E_err:   0.030786 | NF_loss: 90.870601
[2025-11-13 05:59:17] 311:53<97:10, 7.94s/it | [Iter 2356/3090] R0[2266/3000] | LR: 0.003524 | E: -62.944559 | E_var:     3.6398 E_err:   0.029810 | NF_loss: 41.302806
[2025-11-13 05:59:25] 312:01<97:02, 7.94s/it | [Iter 2357/3090] R0[2267/3000] | LR: 0.003514 | E: -63.010600 | E_var:     3.6829 E_err:   0.029986 | NF_loss: 58.647653
[2025-11-13 05:59:32] 312:09<96:54, 7.94s/it | [Iter 2358/3090] R0[2268/3000] | LR: 0.003505 | E: -62.955837 | E_var:     3.8960 E_err:   0.030841 | NF_loss: 71.710797
[2025-11-13 05:59:40] 312:17<96:46, 7.94s/it | [Iter 2359/3090] R0[2269/3000] | LR: 0.003496 | E: -62.994344 | E_var:     3.8125 E_err:   0.030509 | NF_loss: 33.517658
[2025-11-13 05:59:48] 312:25<96:38, 7.94s/it | [Iter 2360/3090] R0[2270/3000] | LR: 0.003487 | E: -62.959199 | E_var:     3.7246 E_err:   0.030155 | NF_loss: 112.756993
[2025-11-13 05:59:56] 312:32<96:30, 7.94s/it | [Iter 2361/3090] R0[2271/3000] | LR: 0.003478 | E: -62.921615 | E_var:     3.6903 E_err:   0.030016 | NF_loss: 44.937351
[2025-11-13 06:00:04] 312:40<96:22, 7.94s/it | [Iter 2362/3090] R0[2272/3000] | LR: 0.003469 | E: -62.928631 | E_var:     3.6050 E_err:   0.029667 | NF_loss: 82.619846
[2025-11-13 06:00:11] 312:48<96:14, 7.94s/it | [Iter 2363/3090] R0[2273/3000] | LR: 0.003460 | E: -62.961646 | E_var:     3.5502 E_err:   0.029441 | NF_loss: 28.335328
[2025-11-13 06:00:19] 312:56<96:06, 7.94s/it | [Iter 2364/3090] R0[2274/3000] | LR: 0.003451 | E: -62.951352 | E_var:     3.6017 E_err:   0.029653 | NF_loss: 45.347923
[2025-11-13 06:00:27] 313:04<95:58, 7.94s/it | [Iter 2365/3090] R0[2275/3000] | LR: 0.003442 | E: -62.944963 | E_var:     3.7443 E_err:   0.030235 | NF_loss: 39.948750
[2025-11-13 06:00:35] 313:11<95:50, 7.94s/it | [Iter 2366/3090] R0[2276/3000] | LR: 0.003433 | E: -63.034917 | E_var:     3.7887 E_err:   0.030413 | NF_loss: 44.256684
[2025-11-13 06:00:43] 313:19<95:42, 7.94s/it | [Iter 2367/3090] R0[2277/3000] | LR: 0.003424 | E: -63.047961 | E_var:     3.9124 E_err:   0.030906 | NF_loss: 45.740372
[2025-11-13 06:00:50] 313:27<95:34, 7.94s/it | [Iter 2368/3090] R0[2278/3000] | LR: 0.003415 | E: -63.036193 | E_var:     3.4689 E_err:   0.029102 | NF_loss: 33.393377
[2025-11-13 06:00:58] 313:35<95:26, 7.94s/it | [Iter 2369/3090] R0[2279/3000] | LR: 0.003406 | E: -62.992085 | E_var:     3.6356 E_err:   0.029793 | NF_loss: 31.878181
[2025-11-13 06:01:06] 313:43<95:18, 7.94s/it | [Iter 2370/3090] R0[2280/3000] | LR: 0.003397 | E: -62.962348 | E_var:     3.6195 E_err:   0.029726 | NF_loss: 37.075818
[2025-11-13 06:01:14] 313:50<95:10, 7.94s/it | [Iter 2371/3090] R0[2281/3000] | LR: 0.003388 | E: -62.894936 | E_var:     3.7916 E_err:   0.030425 | NF_loss: 16.284595
[2025-11-13 06:01:22] 313:58<95:02, 7.94s/it | [Iter 2372/3090] R0[2282/3000] | LR: 0.003379 | E: -62.857345 | E_var:     3.9412 E_err:   0.031019 | NF_loss: 26.452126
[2025-11-13 06:01:29] 314:06<94:54, 7.94s/it | [Iter 2373/3090] R0[2283/3000] | LR: 0.003370 | E: -62.857982 | E_var:     3.8656 E_err:   0.030720 | NF_loss: 15.081421
[2025-11-13 06:01:37] 314:14<94:46, 7.94s/it | [Iter 2374/3090] R0[2284/3000] | LR: 0.003361 | E: -62.947093 | E_var:     3.8258 E_err:   0.030562 | NF_loss: 23.068895
[2025-11-13 06:01:45] 314:22<94:38, 7.94s/it | [Iter 2375/3090] R0[2285/3000] | LR: 0.003352 | E: -62.841982 | E_var:     3.7891 E_err:   0.030415 | NF_loss: 23.725073
[2025-11-13 06:01:53] 314:29<94:30, 7.94s/it | [Iter 2376/3090] R0[2286/3000] | LR: 0.003343 | E: -62.801230 | E_var:     3.7862 E_err:   0.030403 | NF_loss: 20.525109
[2025-11-13 06:02:01] 314:37<94:22, 7.94s/it | [Iter 2377/3090] R0[2287/3000] | LR: 0.003334 | E: -62.932793 | E_var:     3.8133 E_err:   0.030512 | NF_loss: 24.150837
[2025-11-13 06:02:08] 314:45<94:14, 7.94s/it | [Iter 2378/3090] R0[2288/3000] | LR: 0.003325 | E: -62.933199 | E_var:     3.6398 E_err:   0.029810 | NF_loss: 27.140541
[2025-11-13 06:02:16] 314:53<94:06, 7.94s/it | [Iter 2379/3090] R0[2289/3000] | LR: 0.003317 | E: -62.851429 | E_var:     4.0473 E_err:   0.031434 | NF_loss: 21.741690
[2025-11-13 06:02:24] 315:01<93:58, 7.94s/it | [Iter 2380/3090] R0[2290/3000] | LR: 0.003308 | E: -62.992607 | E_var:     3.9514 E_err:   0.031059 | NF_loss: 17.773690
[2025-11-13 06:02:32] 315:09<93:50, 7.94s/it | [Iter 2381/3090] R0[2291/3000] | LR: 0.003299 | E: -62.980968 | E_var:     3.9791 E_err:   0.031168 | NF_loss: 18.555453
[2025-11-13 06:02:40] 315:16<93:42, 7.94s/it | [Iter 2382/3090] R0[2292/3000] | LR: 0.003290 | E: -63.057492 | E_var:     3.8664 E_err:   0.030724 | NF_loss: 22.098306
[2025-11-13 06:02:48] 315:24<93:34, 7.94s/it | [Iter 2383/3090] R0[2293/3000] | LR: 0.003281 | E: -62.986461 | E_var:     3.8301 E_err:   0.030579 | NF_loss: 21.069009
[2025-11-13 06:02:55] 315:32<93:26, 7.94s/it | [Iter 2384/3090] R0[2294/3000] | LR: 0.003272 | E: -63.000547 | E_var:     4.6605 E_err:   0.033732 | NF_loss: 18.693528
[2025-11-13 06:03:03] 315:40<93:18, 7.94s/it | [Iter 2385/3090] R0[2295/3000] | LR: 0.003264 | E: -62.962984 | E_var:     4.1411 E_err:   0.031797 | NF_loss: 20.717361
[2025-11-13 06:03:11] 315:48<93:10, 7.94s/it | [Iter 2386/3090] R0[2296/3000] | LR: 0.003255 | E: -63.028151 | E_var:     3.7205 E_err:   0.030138 | NF_loss: 23.647615
[2025-11-13 06:03:19] 315:55<93:02, 7.94s/it | [Iter 2387/3090] R0[2297/3000] | LR: 0.003246 | E: -63.054728 | E_var:     3.8775 E_err:   0.030768 | NF_loss: 19.535931
[2025-11-13 06:03:27] 316:03<92:54, 7.94s/it | [Iter 2388/3090] R0[2298/3000] | LR: 0.003237 | E: -63.035964 | E_var:     3.7314 E_err:   0.030183 | NF_loss: 21.468407
[2025-11-13 06:03:34] 316:11<92:46, 7.94s/it | [Iter 2389/3090] R0[2299/3000] | LR: 0.003228 | E: -62.906534 | E_var:     3.7057 E_err:   0.030078 | NF_loss: 18.553598
[2025-11-13 06:03:42] 316:19<92:38, 7.94s/it | [Iter 2390/3090] R0[2300/3000] | LR: 0.003220 | E: -62.887981 | E_var:     3.9672 E_err:   0.031122 | NF_loss: 20.022262
[2025-11-13 06:03:50] 316:27<92:30, 7.94s/it | [Iter 2391/3090] R0[2301/3000] | LR: 0.003211 | E: -62.929505 | E_var:     3.9904 E_err:   0.031212 | NF_loss: 21.533164
[2025-11-13 06:03:58] 316:34<92:22, 7.94s/it | [Iter 2392/3090] R0[2302/3000] | LR: 0.003202 | E: -62.800941 | E_var:     4.1168 E_err:   0.031703 | NF_loss: 18.633484
[2025-11-13 06:04:06] 316:42<92:14, 7.94s/it | [Iter 2393/3090] R0[2303/3000] | LR: 0.003193 | E: -62.902420 | E_var:     4.0515 E_err:   0.031451 | NF_loss: 18.624327
[2025-11-13 06:04:13] 316:50<92:06, 7.94s/it | [Iter 2394/3090] R0[2304/3000] | LR: 0.003185 | E: -62.694015 | E_var:     4.1699 E_err:   0.031907 | NF_loss: 19.347290
[2025-11-13 06:04:21] 316:58<91:58, 7.94s/it | [Iter 2395/3090] R0[2305/3000] | LR: 0.003176 | E: -62.684653 | E_var:     4.2447 E_err:   0.032192 | NF_loss: 16.008573
[2025-11-13 06:04:29] 317:06<91:50, 7.94s/it | [Iter 2396/3090] R0[2306/3000] | LR: 0.003167 | E: -62.710228 | E_var:     4.2366 E_err:   0.032161 | NF_loss: 18.764986
[2025-11-13 06:04:37] 317:13<91:42, 7.94s/it | [Iter 2397/3090] R0[2307/3000] | LR: 0.003158 | E: -62.783772 | E_var:     4.3745 E_err:   0.032680 | NF_loss: 17.646277
[2025-11-13 06:04:45] 317:21<91:34, 7.94s/it | [Iter 2398/3090] R0[2308/3000] | LR: 0.003150 | E: -62.888467 | E_var:     3.9454 E_err:   0.031036 | NF_loss: 15.255126
[2025-11-13 06:04:52] 317:29<91:26, 7.94s/it | [Iter 2399/3090] R0[2309/3000] | LR: 0.003141 | E: -62.945573 | E_var:     3.9688 E_err:   0.031128 | NF_loss: 17.363700
[2025-11-13 06:05:00] 317:37<91:18, 7.94s/it | [Iter 2400/3090] R0[2310/3000] | LR: 0.003132 | E: -62.965551 | E_var:     3.7437 E_err:   0.030232 | NF_loss: 16.604683
[2025-11-13 06:05:00] 保存checkpoint: hybrid_checkpoint_iter_002400.pkl
[2025-11-13 06:05:08] 317:45<91:11, 7.94s/it | [Iter 2401/3090] R0[2311/3000] | LR: 0.003124 | E: -62.919877 | E_var:     3.9035 E_err:   0.030871 | NF_loss: 19.498222
[2025-11-13 06:05:16] 317:52<91:03, 7.94s/it | [Iter 2402/3090] R0[2312/3000] | LR: 0.003115 | E: -62.992668 | E_var:     3.8306 E_err:   0.030581 | NF_loss: 19.679940
[2025-11-13 06:05:24] 318:00<90:55, 7.94s/it | [Iter 2403/3090] R0[2313/3000] | LR: 0.003106 | E: -62.999533 | E_var:     3.7583 E_err:   0.030291 | NF_loss: 16.483490
[2025-11-13 06:05:32] 318:08<90:47, 7.94s/it | [Iter 2404/3090] R0[2314/3000] | LR: 0.003098 | E: -62.829635 | E_var:     4.0001 E_err:   0.031251 | NF_loss: 16.661798
[2025-11-13 06:05:39] 318:16<90:39, 7.94s/it | [Iter 2405/3090] R0[2315/3000] | LR: 0.003089 | E: -62.835920 | E_var:     3.8710 E_err:   0.030742 | NF_loss: 17.162827
[2025-11-13 06:05:47] 318:24<90:31, 7.94s/it | [Iter 2406/3090] R0[2316/3000] | LR: 0.003081 | E: -62.910286 | E_var:     3.8670 E_err:   0.030726 | NF_loss: 17.137415
[2025-11-13 06:05:55] 318:31<90:23, 7.94s/it | [Iter 2407/3090] R0[2317/3000] | LR: 0.003072 | E: -62.938366 | E_var:     3.9454 E_err:   0.031036 | NF_loss: 17.278792
[2025-11-13 06:06:03] 318:39<90:15, 7.94s/it | [Iter 2408/3090] R0[2318/3000] | LR: 0.003063 | E: -63.008473 | E_var:     3.5966 E_err:   0.029632 | NF_loss: 19.010869
[2025-11-13 06:06:11] 318:47<90:07, 7.94s/it | [Iter 2409/3090] R0[2319/3000] | LR: 0.003055 | E: -62.905826 | E_var:     3.9859 E_err:   0.031195 | NF_loss: 17.122495
[2025-11-13 06:06:18] 318:55<89:59, 7.94s/it | [Iter 2410/3090] R0[2320/3000] | LR: 0.003046 | E: -62.869109 | E_var:     3.8156 E_err:   0.030521 | NF_loss: 19.828052
[2025-11-13 06:06:26] 319:03<89:51, 7.94s/it | [Iter 2411/3090] R0[2321/3000] | LR: 0.003038 | E: -62.916029 | E_var:     4.0099 E_err:   0.031289 | NF_loss: 17.608867
[2025-11-13 06:06:34] 319:10<89:43, 7.94s/it | [Iter 2412/3090] R0[2322/3000] | LR: 0.003029 | E: -62.830587 | E_var:     4.1747 E_err:   0.031925 | NF_loss: 18.706000
[2025-11-13 06:06:42] 319:18<89:35, 7.94s/it | [Iter 2413/3090] R0[2323/3000] | LR: 0.003021 | E: -62.915445 | E_var:     3.7030 E_err:   0.030068 | NF_loss: 18.751579
[2025-11-13 06:06:50] 319:26<89:27, 7.94s/it | [Iter 2414/3090] R0[2324/3000] | LR: 0.003012 | E: -62.602657 | E_var:     4.4129 E_err:   0.032823 | NF_loss: 16.587715
[2025-11-13 06:06:57] 319:34<89:19, 7.94s/it | [Iter 2415/3090] R0[2325/3000] | LR: 0.003004 | E: -62.681805 | E_var:     4.1573 E_err:   0.031859 | NF_loss: 16.561965
[2025-11-13 06:07:05] 319:42<89:11, 7.94s/it | [Iter 2416/3090] R0[2326/3000] | LR: 0.002995 | E: -62.573620 | E_var:     4.1965 E_err:   0.032008 | NF_loss: 18.278474
[2025-11-13 06:07:13] 319:49<89:03, 7.94s/it | [Iter 2417/3090] R0[2327/3000] | LR: 0.002987 | E: -62.497560 | E_var:     4.2809 E_err:   0.032329 | NF_loss: 17.977046
[2025-11-13 06:07:21] 319:57<88:55, 7.94s/it | [Iter 2418/3090] R0[2328/3000] | LR: 0.002978 | E: -62.631440 | E_var:     3.9941 E_err:   0.031227 | NF_loss: 18.288269
[2025-11-13 06:07:29] 320:05<88:47, 7.94s/it | [Iter 2419/3090] R0[2329/3000] | LR: 0.002970 | E: -62.750858 | E_var:     3.9212 E_err:   0.030941 | NF_loss: 19.565094
[2025-11-13 06:07:36] 320:13<88:39, 7.94s/it | [Iter 2420/3090] R0[2330/3000] | LR: 0.002961 | E: -62.909548 | E_var:     4.0632 E_err:   0.031496 | NF_loss: 15.661286
[2025-11-13 06:07:44] 320:21<88:31, 7.94s/it | [Iter 2421/3090] R0[2331/3000] | LR: 0.002953 | E: -62.908625 | E_var:     3.6065 E_err:   0.029673 | NF_loss: 16.750991
[2025-11-13 06:07:52] 320:29<88:23, 7.94s/it | [Iter 2422/3090] R0[2332/3000] | LR: 0.002944 | E: -62.819979 | E_var:     3.7180 E_err:   0.030128 | NF_loss: 18.065446
[2025-11-13 06:08:00] 320:36<88:15, 7.94s/it | [Iter 2423/3090] R0[2333/3000] | LR: 0.002936 | E: -62.890099 | E_var:     3.8082 E_err:   0.030492 | NF_loss: 14.368917
[2025-11-13 06:08:08] 320:44<88:07, 7.94s/it | [Iter 2424/3090] R0[2334/3000] | LR: 0.002927 | E: -62.979703 | E_var:     3.5167 E_err:   0.029301 | NF_loss: 15.356325
[2025-11-13 06:08:15] 320:52<87:59, 7.94s/it | [Iter 2425/3090] R0[2335/3000] | LR: 0.002919 | E: -63.064655 | E_var:     3.7517 E_err:   0.030265 | NF_loss: 19.298783
[2025-11-13 06:08:23] 321:00<87:51, 7.94s/it | [Iter 2426/3090] R0[2336/3000] | LR: 0.002911 | E: -63.009827 | E_var:     3.5890 E_err:   0.029601 | NF_loss: 17.047660
[2025-11-13 06:08:31] 321:08<87:43, 7.94s/it | [Iter 2427/3090] R0[2337/3000] | LR: 0.002902 | E: -63.027634 | E_var:     3.7881 E_err:   0.030411 | NF_loss: 18.239753
[2025-11-13 06:08:39] 321:15<87:35, 7.94s/it | [Iter 2428/3090] R0[2338/3000] | LR: 0.002894 | E: -63.060839 | E_var:     4.1002 E_err:   0.031639 | NF_loss: 14.210302
[2025-11-13 06:08:47] 321:23<87:27, 7.94s/it | [Iter 2429/3090] R0[2339/3000] | LR: 0.002885 | E: -63.079002 | E_var:     4.6905 E_err:   0.033840 | NF_loss: 16.394444
[2025-11-13 06:08:54] 321:31<87:19, 7.94s/it | [Iter 2430/3090] R0[2340/3000] | LR: 0.002877 | E: -62.991259 | E_var:     4.0935 E_err:   0.031613 | NF_loss: 19.682629
[2025-11-13 06:09:02] 321:39<87:11, 7.94s/it | [Iter 2431/3090] R0[2341/3000] | LR: 0.002869 | E: -62.994986 | E_var:     3.9599 E_err:   0.031093 | NF_loss: 16.976547
[2025-11-13 06:09:10] 321:47<87:03, 7.94s/it | [Iter 2432/3090] R0[2342/3000] | LR: 0.002860 | E: -63.043025 | E_var:     3.9719 E_err:   0.031140 | NF_loss: 14.423295
[2025-11-13 06:09:18] 321:54<86:55, 7.94s/it | [Iter 2433/3090] R0[2343/3000] | LR: 0.002852 | E: -63.005656 | E_var:     3.8616 E_err:   0.030704 | NF_loss: 17.441002
[2025-11-13 06:09:26] 322:02<86:47, 7.94s/it | [Iter 2434/3090] R0[2344/3000] | LR: 0.002844 | E: -63.054707 | E_var:     3.8059 E_err:   0.030482 | NF_loss: 14.191185
[2025-11-13 06:09:33] 322:10<86:39, 7.94s/it | [Iter 2435/3090] R0[2345/3000] | LR: 0.002835 | E: -63.053070 | E_var:     3.8045 E_err:   0.030477 | NF_loss: 18.953394
[2025-11-13 06:09:41] 322:18<86:31, 7.94s/it | [Iter 2436/3090] R0[2346/3000] | LR: 0.002827 | E: -63.015467 | E_var:     3.9031 E_err:   0.030869 | NF_loss: 16.116324
[2025-11-13 06:09:49] 322:26<86:23, 7.94s/it | [Iter 2437/3090] R0[2347/3000] | LR: 0.002819 | E: -62.951715 | E_var:     4.1470 E_err:   0.031819 | NF_loss: 13.817516
[2025-11-13 06:09:57] 322:33<86:15, 7.94s/it | [Iter 2438/3090] R0[2348/3000] | LR: 0.002811 | E: -62.984192 | E_var:     4.2488 E_err:   0.032207 | NF_loss: 12.700785
[2025-11-13 06:10:05] 322:41<86:07, 7.94s/it | [Iter 2439/3090] R0[2349/3000] | LR: 0.002802 | E: -62.999842 | E_var:     4.1214 E_err:   0.031721 | NF_loss: 14.548734
[2025-11-13 06:10:12] 322:49<85:59, 7.94s/it | [Iter 2440/3090] R0[2350/3000] | LR: 0.002794 | E: -62.990207 | E_var:     3.8944 E_err:   0.030835 | NF_loss: 20.614575
[2025-11-13 06:10:20] 322:57<85:51, 7.94s/it | [Iter 2441/3090] R0[2351/3000] | LR: 0.002786 | E: -62.951223 | E_var:     4.0279 E_err:   0.031359 | NF_loss: 16.052703
[2025-11-13 06:10:28] 323:05<85:43, 7.94s/it | [Iter 2442/3090] R0[2352/3000] | LR: 0.002778 | E: -63.001758 | E_var:     3.9075 E_err:   0.030887 | NF_loss: 19.051994
[2025-11-13 06:10:36] 323:12<85:35, 7.94s/it | [Iter 2443/3090] R0[2353/3000] | LR: 0.002769 | E: -63.054012 | E_var:     3.8118 E_err:   0.030506 | NF_loss: 14.205678
[2025-11-13 06:10:44] 323:20<85:28, 7.94s/it | [Iter 2444/3090] R0[2354/3000] | LR: 0.002761 | E: -63.074914 | E_var:     3.8999 E_err:   0.030857 | NF_loss: 15.429966
[2025-11-13 06:10:51] 323:28<85:20, 7.94s/it | [Iter 2445/3090] R0[2355/3000] | LR: 0.002753 | E: -63.076057 | E_var:     3.9083 E_err:   0.030890 | NF_loss: 15.629203
[2025-11-13 06:10:59] 323:36<85:12, 7.94s/it | [Iter 2446/3090] R0[2356/3000] | LR: 0.002745 | E: -63.047013 | E_var:     3.7009 E_err:   0.030059 | NF_loss: 14.608035
[2025-11-13 06:11:07] 323:44<85:04, 7.94s/it | [Iter 2447/3090] R0[2357/3000] | LR: 0.002737 | E: -63.063806 | E_var:     3.7534 E_err:   0.030271 | NF_loss: 14.311395
[2025-11-13 06:11:15] 323:51<84:56, 7.94s/it | [Iter 2448/3090] R0[2358/3000] | LR: 0.002728 | E: -63.063824 | E_var:     3.5114 E_err:   0.029279 | NF_loss: 19.093308
[2025-11-13 06:11:23] 323:59<84:48, 7.94s/it | [Iter 2449/3090] R0[2359/3000] | LR: 0.002720 | E: -62.999861 | E_var:     3.6968 E_err:   0.030042 | NF_loss: 22.011801
[2025-11-13 06:11:30] 324:07<84:40, 7.94s/it | [Iter 2450/3090] R0[2360/3000] | LR: 0.002712 | E: -62.967884 | E_var:     3.7826 E_err:   0.030389 | NF_loss: 20.929677
[2025-11-13 06:11:38] 324:15<84:32, 7.94s/it | [Iter 2451/3090] R0[2361/3000] | LR: 0.002704 | E: -63.026189 | E_var:     3.7134 E_err:   0.030110 | NF_loss: 16.388653
[2025-11-13 06:11:46] 324:23<84:24, 7.94s/it | [Iter 2452/3090] R0[2362/3000] | LR: 0.002696 | E: -63.068407 | E_var:     3.6510 E_err:   0.029856 | NF_loss: 15.318819
[2025-11-13 06:11:54] 324:31<84:16, 7.94s/it | [Iter 2453/3090] R0[2363/3000] | LR: 0.002688 | E: -63.061370 | E_var:     3.7424 E_err:   0.030227 | NF_loss: 18.136939
[2025-11-13 06:12:02] 324:38<84:08, 7.94s/it | [Iter 2454/3090] R0[2364/3000] | LR: 0.002680 | E: -63.078326 | E_var:     3.7029 E_err:   0.030067 | NF_loss: 16.258953
[2025-11-13 06:12:10] 324:46<84:00, 7.94s/it | [Iter 2455/3090] R0[2365/3000] | LR: 0.002671 | E: -63.118128 | E_var:     3.6765 E_err:   0.029960 | NF_loss: 16.275608
[2025-11-13 06:12:17] 324:54<83:52, 7.94s/it | [Iter 2456/3090] R0[2366/3000] | LR: 0.002663 | E: -63.097216 | E_var:     3.8601 E_err:   0.030699 | NF_loss: 18.669921
[2025-11-13 06:12:25] 325:02<83:44, 7.94s/it | [Iter 2457/3090] R0[2367/3000] | LR: 0.002655 | E: -63.082260 | E_var:     3.5608 E_err:   0.029484 | NF_loss: 14.525487
[2025-11-13 06:12:33] 325:10<83:36, 7.94s/it | [Iter 2458/3090] R0[2368/3000] | LR: 0.002647 | E: -63.063055 | E_var:     3.8019 E_err:   0.030466 | NF_loss: 16.924635
[2025-11-13 06:12:41] 325:17<83:28, 7.94s/it | [Iter 2459/3090] R0[2369/3000] | LR: 0.002639 | E: -63.073196 | E_var:     4.0167 E_err:   0.031315 | NF_loss: 16.826083
[2025-11-13 06:12:49] 325:25<83:20, 7.94s/it | [Iter 2460/3090] R0[2370/3000] | LR: 0.002631 | E: -63.085133 | E_var:     4.5758 E_err:   0.033424 | NF_loss: 13.906365
[2025-11-13 06:12:56] 325:33<83:12, 7.94s/it | [Iter 2461/3090] R0[2371/3000] | LR: 0.002623 | E: -63.079933 | E_var:     3.8266 E_err:   0.030565 | NF_loss: 17.211928
[2025-11-13 06:13:04] 325:41<83:04, 7.94s/it | [Iter 2462/3090] R0[2372/3000] | LR: 0.002615 | E: -63.061248 | E_var:     4.1770 E_err:   0.031934 | NF_loss: 18.898997
[2025-11-13 06:13:12] 325:49<82:56, 7.94s/it | [Iter 2463/3090] R0[2373/3000] | LR: 0.002607 | E: -63.084872 | E_var:     3.8823 E_err:   0.030787 | NF_loss: 19.080151
[2025-11-13 06:13:20] 325:56<82:48, 7.94s/it | [Iter 2464/3090] R0[2374/3000] | LR: 0.002599 | E: -62.912800 | E_var:     4.6437 E_err:   0.033671 | NF_loss: 21.238736
[2025-11-13 06:13:28] 326:04<82:40, 7.94s/it | [Iter 2465/3090] R0[2375/3000] | LR: 0.002591 | E: -62.964590 | E_var:     4.3091 E_err:   0.032435 | NF_loss: 13.637856
[2025-11-13 06:13:35] 326:12<82:32, 7.94s/it | [Iter 2466/3090] R0[2376/3000] | LR: 0.002583 | E: -62.921469 | E_var:     4.4688 E_err:   0.033030 | NF_loss: 18.202980
[2025-11-13 06:13:43] 326:20<82:24, 7.94s/it | [Iter 2467/3090] R0[2377/3000] | LR: 0.002575 | E: -62.960058 | E_var:     4.3503 E_err:   0.032590 | NF_loss: 19.295010
[2025-11-13 06:13:51] 326:28<82:16, 7.94s/it | [Iter 2468/3090] R0[2378/3000] | LR: 0.002567 | E: -62.958966 | E_var:     4.4748 E_err:   0.033053 | NF_loss: 17.439108
[2025-11-13 06:13:59] 326:35<82:08, 7.94s/it | [Iter 2469/3090] R0[2379/3000] | LR: 0.002559 | E: -62.940203 | E_var:     4.6357 E_err:   0.033642 | NF_loss: 15.793453
[2025-11-13 06:14:07] 326:43<82:00, 7.94s/it | [Iter 2470/3090] R0[2380/3000] | LR: 0.002551 | E: -62.969862 | E_var:     4.2457 E_err:   0.032195 | NF_loss: 13.231125
[2025-11-13 06:14:14] 326:51<81:52, 7.94s/it | [Iter 2471/3090] R0[2381/3000] | LR: 0.002543 | E: -63.030784 | E_var:     3.9756 E_err:   0.031155 | NF_loss: 15.081862
[2025-11-13 06:14:22] 326:59<81:44, 7.94s/it | [Iter 2472/3090] R0[2382/3000] | LR: 0.002536 | E: -63.038173 | E_var:     3.9910 E_err:   0.031215 | NF_loss: 16.483803
[2025-11-13 06:14:30] 327:07<81:36, 7.94s/it | [Iter 2473/3090] R0[2383/3000] | LR: 0.002528 | E: -63.017316 | E_var:     4.2158 E_err:   0.032082 | NF_loss: 16.464091
[2025-11-13 06:14:38] 327:14<81:28, 7.94s/it | [Iter 2474/3090] R0[2384/3000] | LR: 0.002520 | E: -63.067375 | E_var:     3.9754 E_err:   0.031154 | NF_loss: 15.994806
[2025-11-13 06:14:46] 327:22<81:20, 7.94s/it | [Iter 2475/3090] R0[2385/3000] | LR: 0.002512 | E: -63.019814 | E_var:     3.8329 E_err:   0.030590 | NF_loss: 16.384034
[2025-11-13 06:14:53] 327:30<81:12, 7.94s/it | [Iter 2476/3090] R0[2386/3000] | LR: 0.002504 | E: -63.038263 | E_var:     4.0552 E_err:   0.031465 | NF_loss: 14.657945
[2025-11-13 06:15:01] 327:38<81:04, 7.94s/it | [Iter 2477/3090] R0[2387/3000] | LR: 0.002496 | E: -63.010901 | E_var:     4.0253 E_err:   0.031349 | NF_loss: 16.164140
[2025-11-13 06:15:09] 327:46<80:56, 7.94s/it | [Iter 2478/3090] R0[2388/3000] | LR: 0.002488 | E: -63.043107 | E_var:     4.0291 E_err:   0.031363 | NF_loss: 14.718267
[2025-11-13 06:15:17] 327:53<80:49, 7.94s/it | [Iter 2479/3090] R0[2389/3000] | LR: 0.002481 | E: -63.066157 | E_var:     3.8174 E_err:   0.030529 | NF_loss: 18.174291
[2025-11-13 06:15:25] 328:01<80:41, 7.94s/it | [Iter 2480/3090] R0[2390/3000] | LR: 0.002473 | E: -63.013213 | E_var:     3.8903 E_err:   0.030819 | NF_loss: 15.757354
[2025-11-13 06:15:33] 328:09<80:33, 7.94s/it | [Iter 2481/3090] R0[2391/3000] | LR: 0.002465 | E: -62.995091 | E_var:     3.8440 E_err:   0.030634 | NF_loss: 17.629255
[2025-11-13 06:15:40] 328:17<80:25, 7.94s/it | [Iter 2482/3090] R0[2392/3000] | LR: 0.002457 | E: -62.992261 | E_var:     3.9223 E_err:   0.030945 | NF_loss: 17.936241
[2025-11-13 06:15:48] 328:25<80:17, 7.94s/it | [Iter 2483/3090] R0[2393/3000] | LR: 0.002449 | E: -62.983727 | E_var:     4.0492 E_err:   0.031442 | NF_loss: 14.142503
[2025-11-13 06:15:56] 328:33<80:09, 7.94s/it | [Iter 2484/3090] R0[2394/3000] | LR: 0.002442 | E: -62.987783 | E_var:     3.8128 E_err:   0.030510 | NF_loss: 17.169814
[2025-11-13 06:16:04] 328:40<80:01, 7.94s/it | [Iter 2485/3090] R0[2395/3000] | LR: 0.002434 | E: -62.997911 | E_var:     3.7710 E_err:   0.030342 | NF_loss: 16.853232
[2025-11-13 06:16:12] 328:48<79:53, 7.94s/it | [Iter 2486/3090] R0[2396/3000] | LR: 0.002426 | E: -62.983816 | E_var:     3.6788 E_err:   0.029969 | NF_loss: 20.116525
[2025-11-13 06:16:19] 328:56<79:45, 7.94s/it | [Iter 2487/3090] R0[2397/3000] | LR: 0.002418 | E: -62.974631 | E_var:     3.8418 E_err:   0.030626 | NF_loss: 21.210488
[2025-11-13 06:16:27] 329:04<79:37, 7.94s/it | [Iter 2488/3090] R0[2398/3000] | LR: 0.002411 | E: -62.968864 | E_var:     3.8770 E_err:   0.030766 | NF_loss: 15.302167
[2025-11-13 06:16:35] 329:12<79:29, 7.94s/it | [Iter 2489/3090] R0[2399/3000] | LR: 0.002403 | E: -62.961981 | E_var:     3.7839 E_err:   0.030394 | NF_loss: 16.577090
[2025-11-13 06:16:43] 329:19<79:21, 7.94s/it | [Iter 2490/3090] R0[2400/3000] | LR: 0.002395 | E: -62.942496 | E_var:     3.7987 E_err:   0.030454 | NF_loss: 20.326583
[2025-11-13 06:16:51] 329:27<79:13, 7.94s/it | [Iter 2491/3090] R0[2401/3000] | LR: 0.002387 | E: -62.796613 | E_var:     4.0114 E_err:   0.031295 | NF_loss: 18.744867
[2025-11-13 06:16:58] 329:35<79:05, 7.94s/it | [Iter 2492/3090] R0[2402/3000] | LR: 0.002380 | E: -62.868990 | E_var:     3.9515 E_err:   0.031060 | NF_loss: 18.075660
[2025-11-13 06:17:06] 329:43<78:57, 7.94s/it | [Iter 2493/3090] R0[2403/3000] | LR: 0.002372 | E: -62.929193 | E_var:     3.8572 E_err:   0.030687 | NF_loss: 18.322307
[2025-11-13 06:17:14] 329:51<78:49, 7.94s/it | [Iter 2494/3090] R0[2404/3000] | LR: 0.002364 | E: -62.948220 | E_var:     3.9095 E_err:   0.030894 | NF_loss: 18.670996
[2025-11-13 06:17:22] 329:58<78:41, 7.94s/it | [Iter 2495/3090] R0[2405/3000] | LR: 0.002357 | E: -62.919509 | E_var:     3.7920 E_err:   0.030427 | NF_loss: 13.515494
[2025-11-13 06:17:30] 330:06<78:33, 7.94s/it | [Iter 2496/3090] R0[2406/3000] | LR: 0.002349 | E: -62.966634 | E_var:     3.6695 E_err:   0.029931 | NF_loss: 16.788670
[2025-11-13 06:17:37] 330:14<78:25, 7.94s/it | [Iter 2497/3090] R0[2407/3000] | LR: 0.002341 | E: -62.923144 | E_var:     3.9466 E_err:   0.031041 | NF_loss: 17.180843
[2025-11-13 06:17:45] 330:22<78:17, 7.94s/it | [Iter 2498/3090] R0[2408/3000] | LR: 0.002334 | E: -62.990054 | E_var:     3.8366 E_err:   0.030605 | NF_loss: 20.236576
[2025-11-13 06:17:53] 330:29<78:09, 7.94s/it | [Iter 2499/3090] R0[2409/3000] | LR: 0.002326 | E: -62.870562 | E_var:     3.8591 E_err:   0.030695 | NF_loss: 20.324271
[2025-11-13 06:18:01] 330:37<78:01, 7.94s/it | [Iter 2500/3090] R0[2410/3000] | LR: 0.002319 | E: -62.790052 | E_var:     3.9894 E_err:   0.031209 | NF_loss: 17.902557
[2025-11-13 06:18:09] 330:45<77:53, 7.94s/it | [Iter 2501/3090] R0[2411/3000] | LR: 0.002311 | E: -62.802410 | E_var:     3.8760 E_err:   0.030762 | NF_loss: 15.557104
[2025-11-13 06:18:16] 330:53<77:45, 7.94s/it | [Iter 2502/3090] R0[2412/3000] | LR: 0.002303 | E: -62.825247 | E_var:     3.9432 E_err:   0.031027 | NF_loss: 17.314230
[2025-11-13 06:18:24] 331:01<77:37, 7.93s/it | [Iter 2503/3090] R0[2413/3000] | LR: 0.002296 | E: -62.763656 | E_var:     4.0461 E_err:   0.031430 | NF_loss: 14.383708
[2025-11-13 06:18:32] 331:08<77:29, 7.93s/it | [Iter 2504/3090] R0[2414/3000] | LR: 0.002288 | E: -62.953964 | E_var:     3.6469 E_err:   0.029839 | NF_loss: 17.626709
[2025-11-13 06:18:40] 331:16<77:21, 7.93s/it | [Iter 2505/3090] R0[2415/3000] | LR: 0.002281 | E: -63.044688 | E_var:     3.5106 E_err:   0.029276 | NF_loss: 14.890851
[2025-11-13 06:18:48] 331:24<77:13, 7.93s/it | [Iter 2506/3090] R0[2416/3000] | LR: 0.002273 | E: -63.034895 | E_var:     3.7233 E_err:   0.030150 | NF_loss: 18.783614
[2025-11-13 06:18:55] 331:32<77:05, 7.93s/it | [Iter 2507/3090] R0[2417/3000] | LR: 0.002266 | E: -63.004921 | E_var:     3.6332 E_err:   0.029783 | NF_loss: 16.769305
[2025-11-13 06:19:03] 331:40<76:57, 7.93s/it | [Iter 2508/3090] R0[2418/3000] | LR: 0.002258 | E: -62.994049 | E_var:     3.5122 E_err:   0.029283 | NF_loss: 19.610668
[2025-11-13 06:19:11] 331:47<76:50, 7.93s/it | [Iter 2509/3090] R0[2419/3000] | LR: 0.002251 | E: -63.025858 | E_var:     3.6462 E_err:   0.029836 | NF_loss: 18.095842
[2025-11-13 06:19:19] 331:55<76:42, 7.93s/it | [Iter 2510/3090] R0[2420/3000] | LR: 0.002243 | E: -63.039393 | E_var:     3.5634 E_err:   0.029495 | NF_loss: 17.924974
[2025-11-13 06:19:27] 332:03<76:34, 7.93s/it | [Iter 2511/3090] R0[2421/3000] | LR: 0.002236 | E: -63.037099 | E_var:     3.7035 E_err:   0.030070 | NF_loss: 18.257719
[2025-11-13 06:19:34] 332:11<76:26, 7.93s/it | [Iter 2512/3090] R0[2422/3000] | LR: 0.002228 | E: -63.065099 | E_var:     3.6844 E_err:   0.029992 | NF_loss: 17.277654
[2025-11-13 06:19:42] 332:19<76:18, 7.93s/it | [Iter 2513/3090] R0[2423/3000] | LR: 0.002221 | E: -63.091637 | E_var:     3.4586 E_err:   0.029058 | NF_loss: 17.139216
[2025-11-13 06:19:50] 332:27<76:10, 7.93s/it | [Iter 2514/3090] R0[2424/3000] | LR: 0.002213 | E: -63.056000 | E_var:     3.5569 E_err:   0.029468 | NF_loss: 16.405320
[2025-11-13 06:19:58] 332:34<76:02, 7.93s/it | [Iter 2515/3090] R0[2425/3000] | LR: 0.002206 | E: -63.080608 | E_var:     3.5004 E_err:   0.029233 | NF_loss: 15.106137
[2025-11-13 06:20:06] 332:42<75:54, 7.93s/it | [Iter 2516/3090] R0[2426/3000] | LR: 0.002199 | E: -63.021032 | E_var:     3.6020 E_err:   0.029655 | NF_loss: 16.383870
[2025-11-13 06:20:14] 332:50<75:46, 7.93s/it | [Iter 2517/3090] R0[2427/3000] | LR: 0.002191 | E: -63.042912 | E_var:     3.5345 E_err:   0.029375 | NF_loss: 14.951460
[2025-11-13 06:20:21] 332:58<75:38, 7.93s/it | [Iter 2518/3090] R0[2428/3000] | LR: 0.002184 | E: -63.086534 | E_var:     3.5764 E_err:   0.029549 | NF_loss: 15.899089
[2025-11-13 06:20:29] 333:06<75:30, 7.93s/it | [Iter 2519/3090] R0[2429/3000] | LR: 0.002176 | E: -63.032986 | E_var:     3.5312 E_err:   0.029362 | NF_loss: 15.035748
[2025-11-13 06:20:37] 333:14<75:22, 7.93s/it | [Iter 2520/3090] R0[2430/3000] | LR: 0.002169 | E: -63.036565 | E_var:     3.6171 E_err:   0.029717 | NF_loss: 17.403619
[2025-11-13 06:20:45] 333:21<75:14, 7.93s/it | [Iter 2521/3090] R0[2431/3000] | LR: 0.002162 | E: -63.039800 | E_var:     3.6733 E_err:   0.029946 | NF_loss: 21.207743
[2025-11-13 06:20:53] 333:29<75:06, 7.93s/it | [Iter 2522/3090] R0[2432/3000] | LR: 0.002154 | E: -63.001793 | E_var:     3.4650 E_err:   0.029085 | NF_loss: 19.368729
[2025-11-13 06:21:00] 333:37<74:58, 7.93s/it | [Iter 2523/3090] R0[2433/3000] | LR: 0.002147 | E: -63.019599 | E_var:     3.7978 E_err:   0.030450 | NF_loss: 16.614237
[2025-11-13 06:21:08] 333:45<74:50, 7.93s/it | [Iter 2524/3090] R0[2434/3000] | LR: 0.002140 | E: -63.052696 | E_var:     3.6558 E_err:   0.029875 | NF_loss: 15.118789
[2025-11-13 06:21:16] 333:53<74:42, 7.93s/it | [Iter 2525/3090] R0[2435/3000] | LR: 0.002132 | E: -63.063107 | E_var:     3.3962 E_err:   0.028795 | NF_loss: 17.316065
[2025-11-13 06:21:24] 334:00<74:34, 7.93s/it | [Iter 2526/3090] R0[2436/3000] | LR: 0.002125 | E: -63.002169 | E_var:     3.4900 E_err:   0.029190 | NF_loss: 16.963380
[2025-11-13 06:21:32] 334:08<74:26, 7.93s/it | [Iter 2527/3090] R0[2437/3000] | LR: 0.002118 | E: -63.034833 | E_var:     3.5000 E_err:   0.029232 | NF_loss: 15.418300
[2025-11-13 06:21:39] 334:16<74:18, 7.93s/it | [Iter 2528/3090] R0[2438/3000] | LR: 0.002110 | E: -63.100258 | E_var:     3.4232 E_err:   0.028909 | NF_loss: 16.070968
[2025-11-13 06:21:47] 334:24<74:10, 7.93s/it | [Iter 2529/3090] R0[2439/3000] | LR: 0.002103 | E: -63.067443 | E_var:     3.4251 E_err:   0.028917 | NF_loss: 13.602703
[2025-11-13 06:21:55] 334:32<74:02, 7.93s/it | [Iter 2530/3090] R0[2440/3000] | LR: 0.002096 | E: -63.051429 | E_var:     3.5586 E_err:   0.029475 | NF_loss: 12.862734
[2025-11-13 06:22:03] 334:39<73:54, 7.93s/it | [Iter 2531/3090] R0[2441/3000] | LR: 0.002089 | E: -62.964811 | E_var:     3.6124 E_err:   0.029697 | NF_loss: 15.641970
[2025-11-13 06:22:11] 334:47<73:46, 7.93s/it | [Iter 2532/3090] R0[2442/3000] | LR: 0.002081 | E: -63.008431 | E_var:     3.4865 E_err:   0.029175 | NF_loss: 15.020619
[2025-11-13 06:22:18] 334:55<73:38, 7.93s/it | [Iter 2533/3090] R0[2443/3000] | LR: 0.002074 | E: -62.952788 | E_var:     3.5237 E_err:   0.029331 | NF_loss: 14.330384
[2025-11-13 06:22:26] 335:03<73:30, 7.93s/it | [Iter 2534/3090] R0[2444/3000] | LR: 0.002067 | E: -62.945788 | E_var:     3.4992 E_err:   0.029228 | NF_loss: 15.079955
[2025-11-13 06:22:34] 335:11<73:23, 7.93s/it | [Iter 2535/3090] R0[2445/3000] | LR: 0.002060 | E: -62.970688 | E_var:     3.5488 E_err:   0.029435 | NF_loss: 14.036850
[2025-11-13 06:22:42] 335:18<73:15, 7.93s/it | [Iter 2536/3090] R0[2446/3000] | LR: 0.002052 | E: -62.971427 | E_var:     3.4360 E_err:   0.028963 | NF_loss: 16.495713
[2025-11-13 06:22:50] 335:26<73:07, 7.93s/it | [Iter 2537/3090] R0[2447/3000] | LR: 0.002045 | E: -62.940511 | E_var:     3.4873 E_err:   0.029179 | NF_loss: 16.632449
[2025-11-13 06:22:57] 335:34<72:59, 7.93s/it | [Iter 2538/3090] R0[2448/3000] | LR: 0.002038 | E: -62.937953 | E_var:     3.4668 E_err:   0.029093 | NF_loss: 16.735831
[2025-11-13 06:23:05] 335:42<72:51, 7.93s/it | [Iter 2539/3090] R0[2449/3000] | LR: 0.002031 | E: -62.989986 | E_var:     3.5641 E_err:   0.029498 | NF_loss: 15.831185
[2025-11-13 06:23:13] 335:50<72:43, 7.93s/it | [Iter 2540/3090] R0[2450/3000] | LR: 0.002024 | E: -62.945687 | E_var:     3.5521 E_err:   0.029449 | NF_loss: 16.068903
[2025-11-13 06:23:21] 335:57<72:35, 7.93s/it | [Iter 2541/3090] R0[2451/3000] | LR: 0.002017 | E: -63.033147 | E_var:     3.4892 E_err:   0.029187 | NF_loss: 19.480727
[2025-11-13 06:23:29] 336:05<72:27, 7.93s/it | [Iter 2542/3090] R0[2452/3000] | LR: 0.002010 | E: -63.031637 | E_var:     3.4405 E_err:   0.028982 | NF_loss: 15.638402
[2025-11-13 06:23:36] 336:13<72:19, 7.93s/it | [Iter 2543/3090] R0[2453/3000] | LR: 0.002002 | E: -62.970512 | E_var:     3.5304 E_err:   0.029358 | NF_loss: 16.307097
[2025-11-13 06:23:44] 336:21<72:11, 7.93s/it | [Iter 2544/3090] R0[2454/3000] | LR: 0.001995 | E: -63.015863 | E_var:     3.4167 E_err:   0.028882 | NF_loss: 18.432894
[2025-11-13 06:23:52] 336:28<72:03, 7.93s/it | [Iter 2545/3090] R0[2455/3000] | LR: 0.001988 | E: -63.004732 | E_var:     3.4157 E_err:   0.028877 | NF_loss: 16.806480
[2025-11-13 06:24:00] 336:36<71:55, 7.93s/it | [Iter 2546/3090] R0[2456/3000] | LR: 0.001981 | E: -62.981130 | E_var:     3.4389 E_err:   0.028975 | NF_loss: 12.745928
[2025-11-13 06:24:08] 336:44<71:47, 7.93s/it | [Iter 2547/3090] R0[2457/3000] | LR: 0.001974 | E: -62.977377 | E_var:     3.5137 E_err:   0.029289 | NF_loss: 16.506184
[2025-11-13 06:24:15] 336:52<71:39, 7.93s/it | [Iter 2548/3090] R0[2458/3000] | LR: 0.001967 | E: -62.867811 | E_var:     3.5361 E_err:   0.029382 | NF_loss: 20.973400
[2025-11-13 06:24:23] 337:00<71:31, 7.93s/it | [Iter 2549/3090] R0[2459/3000] | LR: 0.001960 | E: -62.940630 | E_var:     3.6390 E_err:   0.029806 | NF_loss: 16.102573
[2025-11-13 06:24:31] 337:08<71:23, 7.93s/it | [Iter 2550/3090] R0[2460/3000] | LR: 0.001953 | E: -62.939768 | E_var:     3.4067 E_err:   0.028839 | NF_loss: 15.327633
[2025-11-13 06:24:39] 337:15<71:15, 7.93s/it | [Iter 2551/3090] R0[2461/3000] | LR: 0.001946 | E: -62.965966 | E_var:     3.4644 E_err:   0.029083 | NF_loss: 17.725361
[2025-11-13 06:24:47] 337:23<71:07, 7.93s/it | [Iter 2552/3090] R0[2462/3000] | LR: 0.001939 | E: -63.039317 | E_var:     3.7240 E_err:   0.030153 | NF_loss: 17.509535
[2025-11-13 06:24:55] 337:31<70:59, 7.93s/it | [Iter 2553/3090] R0[2463/3000] | LR: 0.001932 | E: -63.012909 | E_var:     3.8155 E_err:   0.030521 | NF_loss: 14.708870
[2025-11-13 06:25:02] 337:39<70:51, 7.93s/it | [Iter 2554/3090] R0[2464/3000] | LR: 0.001925 | E: -63.098886 | E_var:     3.5010 E_err:   0.029236 | NF_loss: 13.141093
[2025-11-13 06:25:10] 337:47<70:43, 7.93s/it | [Iter 2555/3090] R0[2465/3000] | LR: 0.001918 | E: -63.043501 | E_var:     3.5313 E_err:   0.029362 | NF_loss: 13.907596
[2025-11-13 06:25:18] 337:54<70:35, 7.93s/it | [Iter 2556/3090] R0[2466/3000] | LR: 0.001911 | E: -63.062187 | E_var:     3.3661 E_err:   0.028667 | NF_loss: 14.390672
[2025-11-13 06:25:26] 338:02<70:27, 7.93s/it | [Iter 2557/3090] R0[2467/3000] | LR: 0.001904 | E: -63.065971 | E_var:     3.5679 E_err:   0.029514 | NF_loss: 16.978401
[2025-11-13 06:25:34] 338:10<70:19, 7.93s/it | [Iter 2558/3090] R0[2468/3000] | LR: 0.001897 | E: -63.098882 | E_var:     3.7816 E_err:   0.030385 | NF_loss: 14.263977
[2025-11-13 06:25:41] 338:18<70:11, 7.93s/it | [Iter 2559/3090] R0[2469/3000] | LR: 0.001890 | E: -63.108256 | E_var:     3.7428 E_err:   0.030229 | NF_loss: 16.463719
[2025-11-13 06:25:49] 338:26<70:04, 7.93s/it | [Iter 2560/3090] R0[2470/3000] | LR: 0.001883 | E: -63.127728 | E_var:     3.7158 E_err:   0.030119 | NF_loss: 20.641237
[2025-11-13 06:25:57] 338:33<69:56, 7.93s/it | [Iter 2561/3090] R0[2471/3000] | LR: 0.001876 | E: -63.115807 | E_var:     3.6507 E_err:   0.029854 | NF_loss: 16.631965
[2025-11-13 06:26:05] 338:41<69:48, 7.93s/it | [Iter 2562/3090] R0[2472/3000] | LR: 0.001870 | E: -63.095245 | E_var:     3.5663 E_err:   0.029507 | NF_loss: 12.269886
[2025-11-13 06:26:13] 338:49<69:40, 7.93s/it | [Iter 2563/3090] R0[2473/3000] | LR: 0.001863 | E: -63.096203 | E_var:     3.9405 E_err:   0.031017 | NF_loss: 20.520395
[2025-11-13 06:26:20] 338:57<69:32, 7.93s/it | [Iter 2564/3090] R0[2474/3000] | LR: 0.001856 | E: -63.055241 | E_var:     3.9900 E_err:   0.031211 | NF_loss: 21.936774
[2025-11-13 06:26:28] 339:05<69:24, 7.93s/it | [Iter 2565/3090] R0[2475/3000] | LR: 0.001849 | E: -63.119841 | E_var:     3.7909 E_err:   0.030422 | NF_loss: 17.597914
[2025-11-13 06:26:36] 339:12<69:16, 7.93s/it | [Iter 2566/3090] R0[2476/3000] | LR: 0.001842 | E: -63.101508 | E_var:     3.9552 E_err:   0.031075 | NF_loss: 17.444866
[2025-11-13 06:26:44] 339:20<69:08, 7.93s/it | [Iter 2567/3090] R0[2477/3000] | LR: 0.001835 | E: -63.089176 | E_var:     3.6923 E_err:   0.030024 | NF_loss: 14.264027
[2025-11-13 06:26:51] 339:28<69:00, 7.93s/it | [Iter 2568/3090] R0[2478/3000] | LR: 0.001828 | E: -63.103257 | E_var:     3.5825 E_err:   0.029574 | NF_loss: 21.532456
[2025-11-13 06:26:59] 339:36<68:52, 7.93s/it | [Iter 2569/3090] R0[2479/3000] | LR: 0.001822 | E: -63.083893 | E_var:     3.6639 E_err:   0.029908 | NF_loss: 17.427365
[2025-11-13 06:27:07] 339:44<68:44, 7.93s/it | [Iter 2570/3090] R0[2480/3000] | LR: 0.001815 | E: -62.976671 | E_var:     3.6514 E_err:   0.029857 | NF_loss: 14.232115
[2025-11-13 06:27:15] 339:51<68:36, 7.93s/it | [Iter 2571/3090] R0[2481/3000] | LR: 0.001808 | E: -63.008348 | E_var:     3.8056 E_err:   0.030481 | NF_loss: 16.939748
[2025-11-13 06:27:23] 339:59<68:28, 7.93s/it | [Iter 2572/3090] R0[2482/3000] | LR: 0.001801 | E: -62.975289 | E_var:     3.6005 E_err:   0.029648 | NF_loss: 16.788441
[2025-11-13 06:27:30] 340:07<68:20, 7.93s/it | [Iter 2573/3090] R0[2483/3000] | LR: 0.001795 | E: -62.985396 | E_var:     3.5597 E_err:   0.029480 | NF_loss: 20.055953
[2025-11-13 06:27:38] 340:15<68:12, 7.93s/it | [Iter 2574/3090] R0[2484/3000] | LR: 0.001788 | E: -62.970991 | E_var:     3.7017 E_err:   0.030062 | NF_loss: 19.780351
[2025-11-13 06:27:46] 340:23<68:04, 7.93s/it | [Iter 2575/3090] R0[2485/3000] | LR: 0.001781 | E: -62.891537 | E_var:     3.7342 E_err:   0.030194 | NF_loss: 15.629738
[2025-11-13 06:27:54] 340:30<67:56, 7.93s/it | [Iter 2576/3090] R0[2486/3000] | LR: 0.001774 | E: -62.901705 | E_var:     3.7766 E_err:   0.030365 | NF_loss: 19.006268
[2025-11-13 06:28:02] 340:38<67:48, 7.93s/it | [Iter 2577/3090] R0[2487/3000] | LR: 0.001768 | E: -62.993381 | E_var:     3.6849 E_err:   0.029994 | NF_loss: 15.982621
[2025-11-13 06:28:10] 340:46<67:40, 7.93s/it | [Iter 2578/3090] R0[2488/3000] | LR: 0.001761 | E: -63.002041 | E_var:     3.5220 E_err:   0.029323 | NF_loss: 15.883323
[2025-11-13 06:28:17] 340:54<67:32, 7.93s/it | [Iter 2579/3090] R0[2489/3000] | LR: 0.001754 | E: -62.946979 | E_var:     3.3528 E_err:   0.028610 | NF_loss: 22.998263
[2025-11-13 06:28:25] 341:02<67:24, 7.93s/it | [Iter 2580/3090] R0[2490/3000] | LR: 0.001747 | E: -63.048761 | E_var:     3.5745 E_err:   0.029541 | NF_loss: 17.771982
[2025-11-13 06:28:33] 341:10<67:16, 7.93s/it | [Iter 2581/3090] R0[2491/3000] | LR: 0.001741 | E: -63.018680 | E_var:     3.4516 E_err:   0.029029 | NF_loss: 14.098857
[2025-11-13 06:28:41] 341:17<67:08, 7.93s/it | [Iter 2582/3090] R0[2492/3000] | LR: 0.001734 | E: -63.096264 | E_var:     3.4249 E_err:   0.028916 | NF_loss: 18.197103
[2025-11-13 06:28:49] 341:25<67:00, 7.93s/it | [Iter 2583/3090] R0[2493/3000] | LR: 0.001728 | E: -63.086080 | E_var:     3.3846 E_err:   0.028746 | NF_loss: 13.733370
[2025-11-13 06:28:56] 341:33<66:53, 7.93s/it | [Iter 2584/3090] R0[2494/3000] | LR: 0.001721 | E: -63.010723 | E_var:     3.5903 E_err:   0.029607 | NF_loss: 15.947933
[2025-11-13 06:29:04] 341:41<66:45, 7.93s/it | [Iter 2585/3090] R0[2495/3000] | LR: 0.001714 | E: -63.018645 | E_var:     3.3879 E_err:   0.028760 | NF_loss: 18.407094
[2025-11-13 06:29:12] 341:49<66:37, 7.93s/it | [Iter 2586/3090] R0[2496/3000] | LR: 0.001708 | E: -62.994549 | E_var:     3.5609 E_err:   0.029485 | NF_loss: 17.960259
[2025-11-13 06:29:20] 341:56<66:29, 7.93s/it | [Iter 2587/3090] R0[2497/3000] | LR: 0.001701 | E: -63.032259 | E_var:     3.4199 E_err:   0.028895 | NF_loss: 16.324318
[2025-11-13 06:29:28] 342:04<66:21, 7.93s/it | [Iter 2588/3090] R0[2498/3000] | LR: 0.001694 | E: -63.066406 | E_var:     3.3752 E_err:   0.028706 | NF_loss: 16.944577
[2025-11-13 06:29:35] 342:12<66:13, 7.93s/it | [Iter 2589/3090] R0[2499/3000] | LR: 0.001688 | E: -63.050672 | E_var:     3.4403 E_err:   0.028981 | NF_loss: 16.937528
[2025-11-13 06:29:43] 342:20<66:05, 7.93s/it | [Iter 2590/3090] R0[2500/3000] | LR: 0.001681 | E: -63.084926 | E_var:     3.5689 E_err:   0.029518 | NF_loss: 16.033842
[2025-11-13 06:29:51] 342:28<65:57, 7.93s/it | [Iter 2591/3090] R0[2501/3000] | LR: 0.001675 | E: -63.005152 | E_var:     3.3613 E_err:   0.028647 | NF_loss: 21.083326
[2025-11-13 06:29:59] 342:35<65:49, 7.93s/it | [Iter 2592/3090] R0[2502/3000] | LR: 0.001668 | E: -63.027314 | E_var:     3.5061 E_err:   0.029257 | NF_loss: 18.869511
[2025-11-13 06:30:07] 342:43<65:41, 7.93s/it | [Iter 2593/3090] R0[2503/3000] | LR: 0.001662 | E: -63.002966 | E_var:     3.4683 E_err:   0.029099 | NF_loss: 17.655875
[2025-11-13 06:30:14] 342:51<65:33, 7.93s/it | [Iter 2594/3090] R0[2504/3000] | LR: 0.001655 | E: -63.014594 | E_var:     3.4290 E_err:   0.028934 | NF_loss: 17.996931
[2025-11-13 06:30:22] 342:59<65:25, 7.93s/it | [Iter 2595/3090] R0[2505/3000] | LR: 0.001649 | E: -62.988880 | E_var:     3.5356 E_err:   0.029380 | NF_loss: 13.898089
[2025-11-13 06:30:30] 343:07<65:17, 7.93s/it | [Iter 2596/3090] R0[2506/3000] | LR: 0.001642 | E: -62.860432 | E_var:     3.6153 E_err:   0.029709 | NF_loss: 16.398939
[2025-11-13 06:30:38] 343:14<65:09, 7.93s/it | [Iter 2597/3090] R0[2507/3000] | LR: 0.001636 | E: -62.912680 | E_var:     3.5888 E_err:   0.029600 | NF_loss: 16.582193
[2025-11-13 06:30:46] 343:22<65:01, 7.93s/it | [Iter 2598/3090] R0[2508/3000] | LR: 0.001629 | E: -62.980540 | E_var:     3.4392 E_err:   0.028977 | NF_loss: 18.455244
[2025-11-13 06:30:53] 343:30<64:53, 7.93s/it | [Iter 2599/3090] R0[2509/3000] | LR: 0.001623 | E: -62.996840 | E_var:     3.2693 E_err:   0.028252 | NF_loss: 15.778630
[2025-11-13 06:31:01] 343:38<64:45, 7.93s/it | [Iter 2600/3090] R0[2510/3000] | LR: 0.001616 | E: -63.028103 | E_var:     3.5122 E_err:   0.029283 | NF_loss: 17.895255
[2025-11-13 06:31:09] 343:46<64:37, 7.93s/it | [Iter 2601/3090] R0[2511/3000] | LR: 0.001610 | E: -63.045982 | E_var:     3.4297 E_err:   0.028937 | NF_loss: 18.513002
[2025-11-13 06:31:17] 343:53<64:29, 7.93s/it | [Iter 2602/3090] R0[2512/3000] | LR: 0.001604 | E: -63.043873 | E_var:     3.4704 E_err:   0.029108 | NF_loss: 20.111197
[2025-11-13 06:31:25] 344:01<64:21, 7.93s/it | [Iter 2603/3090] R0[2513/3000] | LR: 0.001597 | E: -63.124981 | E_var:     3.7832 E_err:   0.030391 | NF_loss: 15.358682
[2025-11-13 06:31:32] 344:09<64:13, 7.93s/it | [Iter 2604/3090] R0[2514/3000] | LR: 0.001591 | E: -63.034677 | E_var:     3.8451 E_err:   0.030639 | NF_loss: 16.897978
[2025-11-13 06:31:40] 344:17<64:05, 7.93s/it | [Iter 2605/3090] R0[2515/3000] | LR: 0.001584 | E: -63.074391 | E_var:     3.7508 E_err:   0.030261 | NF_loss: 17.659189
[2025-11-13 06:31:48] 344:25<63:58, 7.93s/it | [Iter 2606/3090] R0[2516/3000] | LR: 0.001578 | E: -63.105199 | E_var:     3.4815 E_err:   0.029154 | NF_loss: 17.389990
[2025-11-13 06:31:56] 344:32<63:50, 7.93s/it | [Iter 2607/3090] R0[2517/3000] | LR: 0.001572 | E: -63.118186 | E_var:     3.4276 E_err:   0.028928 | NF_loss: 17.557712
[2025-11-13 06:32:04] 344:40<63:42, 7.93s/it | [Iter 2608/3090] R0[2518/3000] | LR: 0.001565 | E: -63.122194 | E_var:     3.7281 E_err:   0.030169 | NF_loss: 18.981357
[2025-11-13 06:32:11] 344:48<63:34, 7.93s/it | [Iter 2609/3090] R0[2519/3000] | LR: 0.001559 | E: -63.039528 | E_var:     3.6967 E_err:   0.030042 | NF_loss: 17.642590
[2025-11-13 06:32:19] 344:56<63:26, 7.93s/it | [Iter 2610/3090] R0[2520/3000] | LR: 0.001553 | E: -63.097503 | E_var:     3.4550 E_err:   0.029043 | NF_loss: 19.835477
[2025-11-13 06:32:27] 345:04<63:18, 7.93s/it | [Iter 2611/3090] R0[2521/3000] | LR: 0.001546 | E: -63.106233 | E_var:     3.7077 E_err:   0.030086 | NF_loss: 16.599198
[2025-11-13 06:32:35] 345:11<63:10, 7.93s/it | [Iter 2612/3090] R0[2522/3000] | LR: 0.001540 | E: -63.069763 | E_var:     3.4798 E_err:   0.029147 | NF_loss: 13.892283
[2025-11-13 06:32:43] 345:19<63:02, 7.93s/it | [Iter 2613/3090] R0[2523/3000] | LR: 0.001534 | E: -63.083554 | E_var:     3.5066 E_err:   0.029259 | NF_loss: 14.977018
[2025-11-13 06:32:51] 345:27<62:54, 7.93s/it | [Iter 2614/3090] R0[2524/3000] | LR: 0.001527 | E: -63.068678 | E_var:     3.6304 E_err:   0.029771 | NF_loss: 15.447577
[2025-11-13 06:32:58] 345:35<62:46, 7.93s/it | [Iter 2615/3090] R0[2525/3000] | LR: 0.001521 | E: -63.082958 | E_var:     3.5313 E_err:   0.029362 | NF_loss: 18.306717
[2025-11-13 06:33:06] 345:43<62:38, 7.93s/it | [Iter 2616/3090] R0[2526/3000] | LR: 0.001515 | E: -63.077040 | E_var:     3.7659 E_err:   0.030322 | NF_loss: 18.286190
[2025-11-13 06:33:14] 345:51<62:30, 7.93s/it | [Iter 2617/3090] R0[2527/3000] | LR: 0.001509 | E: -63.012575 | E_var:     3.5234 E_err:   0.029329 | NF_loss: 15.267369
[2025-11-13 06:33:22] 345:58<62:22, 7.93s/it | [Iter 2618/3090] R0[2528/3000] | LR: 0.001502 | E: -62.888562 | E_var:     3.5923 E_err:   0.029614 | NF_loss: 12.412477
[2025-11-13 06:33:30] 346:06<62:14, 7.93s/it | [Iter 2619/3090] R0[2529/3000] | LR: 0.001496 | E: -63.022094 | E_var:     3.6675 E_err:   0.029923 | NF_loss: 14.566526
[2025-11-13 06:33:37] 346:14<62:06, 7.93s/it | [Iter 2620/3090] R0[2530/3000] | LR: 0.001490 | E: -62.978720 | E_var:     3.6299 E_err:   0.029769 | NF_loss: 18.254586
[2025-11-13 06:33:45] 346:22<61:58, 7.93s/it | [Iter 2621/3090] R0[2531/3000] | LR: 0.001484 | E: -62.991904 | E_var:     3.5957 E_err:   0.029629 | NF_loss: 16.934682
[2025-11-13 06:33:53] 346:30<61:50, 7.93s/it | [Iter 2622/3090] R0[2532/3000] | LR: 0.001478 | E: -62.974584 | E_var:     3.6229 E_err:   0.029741 | NF_loss: 16.440407
[2025-11-13 06:34:01] 346:37<61:42, 7.93s/it | [Iter 2623/3090] R0[2533/3000] | LR: 0.001471 | E: -63.072874 | E_var:     3.6641 E_err:   0.029909 | NF_loss: 13.118333
[2025-11-13 06:34:09] 346:45<61:34, 7.93s/it | [Iter 2624/3090] R0[2534/3000] | LR: 0.001465 | E: -63.000315 | E_var:     3.6526 E_err:   0.029862 | NF_loss: 11.612046
[2025-11-13 06:34:16] 346:53<61:26, 7.93s/it | [Iter 2625/3090] R0[2535/3000] | LR: 0.001459 | E: -62.921641 | E_var:     3.6062 E_err:   0.029672 | NF_loss: 14.383028
[2025-11-13 06:34:24] 347:01<61:18, 7.93s/it | [Iter 2626/3090] R0[2536/3000] | LR: 0.001453 | E: -62.831841 | E_var:     3.8091 E_err:   0.030495 | NF_loss: 11.948085
[2025-11-13 06:34:32] 347:09<61:11, 7.93s/it | [Iter 2627/3090] R0[2537/3000] | LR: 0.001447 | E: -62.821736 | E_var:     3.8050 E_err:   0.030479 | NF_loss: 18.620609
[2025-11-13 06:34:40] 347:16<61:03, 7.93s/it | [Iter 2628/3090] R0[2538/3000] | LR: 0.001441 | E: -62.895806 | E_var:     3.5623 E_err:   0.029491 | NF_loss: 14.526441
[2025-11-13 06:34:48] 347:24<60:55, 7.93s/it | [Iter 2629/3090] R0[2539/3000] | LR: 0.001435 | E: -63.028317 | E_var:     3.6716 E_err:   0.029940 | NF_loss: 12.896193
[2025-11-13 06:34:55] 347:32<60:47, 7.93s/it | [Iter 2630/3090] R0[2540/3000] | LR: 0.001429 | E: -63.057810 | E_var:     3.5596 E_err:   0.029479 | NF_loss: 16.840616
[2025-11-13 06:35:03] 347:40<60:39, 7.93s/it | [Iter 2631/3090] R0[2541/3000] | LR: 0.001423 | E: -63.038304 | E_var:     3.5033 E_err:   0.029245 | NF_loss: 14.455710
[2025-11-13 06:35:11] 347:48<60:31, 7.93s/it | [Iter 2632/3090] R0[2542/3000] | LR: 0.001416 | E: -63.059818 | E_var:     3.4784 E_err:   0.029142 | NF_loss: 12.867076
[2025-11-13 06:35:19] 347:55<60:23, 7.93s/it | [Iter 2633/3090] R0[2543/3000] | LR: 0.001410 | E: -63.070841 | E_var:     3.5081 E_err:   0.029265 | NF_loss: 13.540420
[2025-11-13 06:35:27] 348:03<60:15, 7.93s/it | [Iter 2634/3090] R0[2544/3000] | LR: 0.001404 | E: -63.072441 | E_var:     3.3946 E_err:   0.028788 | NF_loss: 11.409024
[2025-11-13 06:35:34] 348:11<60:07, 7.93s/it | [Iter 2635/3090] R0[2545/3000] | LR: 0.001398 | E: -63.075754 | E_var:     3.7596 E_err:   0.030296 | NF_loss: 15.138450
[2025-11-13 06:35:42] 348:19<59:59, 7.93s/it | [Iter 2636/3090] R0[2546/3000] | LR: 0.001392 | E: -63.047361 | E_var:     3.7422 E_err:   0.030226 | NF_loss: 19.661401
[2025-11-13 06:35:50] 348:27<59:51, 7.93s/it | [Iter 2637/3090] R0[2547/3000] | LR: 0.001386 | E: -62.990510 | E_var:     3.7705 E_err:   0.030340 | NF_loss: 15.882692
[2025-11-13 06:35:58] 348:34<59:43, 7.93s/it | [Iter 2638/3090] R0[2548/3000] | LR: 0.001380 | E: -63.051982 | E_var:     3.5962 E_err:   0.029631 | NF_loss: 17.691454
[2025-11-13 06:36:06] 348:42<59:35, 7.93s/it | [Iter 2639/3090] R0[2549/3000] | LR: 0.001374 | E: -63.048950 | E_var:     3.6450 E_err:   0.029831 | NF_loss: 17.184404
[2025-11-13 06:36:13] 348:50<59:27, 7.93s/it | [Iter 2640/3090] R0[2550/3000] | LR: 0.001368 | E: -63.030328 | E_var:     3.8230 E_err:   0.030551 | NF_loss: 19.829757
[2025-11-13 06:36:21] 348:58<59:19, 7.93s/it | [Iter 2641/3090] R0[2551/3000] | LR: 0.001363 | E: -63.022731 | E_var:     3.9610 E_err:   0.031097 | NF_loss: 10.895868
[2025-11-13 06:36:29] 349:06<59:11, 7.93s/it | [Iter 2642/3090] R0[2552/3000] | LR: 0.001357 | E: -63.071244 | E_var:     3.8533 E_err:   0.030672 | NF_loss: 17.400183
[2025-11-13 06:36:37] 349:13<59:03, 7.93s/it | [Iter 2643/3090] R0[2553/3000] | LR: 0.001351 | E: -63.042556 | E_var:     3.8970 E_err:   0.030845 | NF_loss: 16.898701
[2025-11-13 06:36:45] 349:21<58:55, 7.93s/it | [Iter 2644/3090] R0[2554/3000] | LR: 0.001345 | E: -63.029059 | E_var:     3.7733 E_err:   0.030352 | NF_loss: 16.028581
[2025-11-13 06:36:52] 349:29<58:47, 7.93s/it | [Iter 2645/3090] R0[2555/3000] | LR: 0.001339 | E: -63.099688 | E_var:     3.8148 E_err:   0.030518 | NF_loss: 16.070809
[2025-11-13 06:37:00] 349:37<58:40, 7.93s/it | [Iter 2646/3090] R0[2556/3000] | LR: 0.001333 | E: -63.115054 | E_var:     3.5848 E_err:   0.029584 | NF_loss: 16.072733
[2025-11-13 06:37:08] 349:45<58:32, 7.93s/it | [Iter 2647/3090] R0[2557/3000] | LR: 0.001327 | E: -63.079506 | E_var:     3.7024 E_err:   0.030065 | NF_loss: 19.019555
[2025-11-13 06:37:16] 349:53<58:24, 7.93s/it | [Iter 2648/3090] R0[2558/3000] | LR: 0.001321 | E: -63.115209 | E_var:     3.7298 E_err:   0.030176 | NF_loss: 19.167104
[2025-11-13 06:37:24] 350:00<58:16, 7.93s/it | [Iter 2649/3090] R0[2559/3000] | LR: 0.001315 | E: -63.094606 | E_var:     3.5017 E_err:   0.029239 | NF_loss: 19.629320
[2025-11-13 06:37:32] 350:08<58:08, 7.93s/it | [Iter 2650/3090] R0[2560/3000] | LR: 0.001310 | E: -63.139183 | E_var:     3.6832 E_err:   0.029987 | NF_loss: 15.483749
[2025-11-13 06:37:39] 350:16<58:00, 7.93s/it | [Iter 2651/3090] R0[2561/3000] | LR: 0.001304 | E: -63.110737 | E_var:     3.7079 E_err:   0.030087 | NF_loss: 18.826796
[2025-11-13 06:37:47] 350:24<57:52, 7.93s/it | [Iter 2652/3090] R0[2562/3000] | LR: 0.001298 | E: -63.128685 | E_var:     3.5634 E_err:   0.029495 | NF_loss: 16.416871
[2025-11-13 06:37:55] 350:32<57:44, 7.93s/it | [Iter 2653/3090] R0[2563/3000] | LR: 0.001292 | E: -63.072972 | E_var:     3.5567 E_err:   0.029467 | NF_loss: 18.394756
[2025-11-13 06:38:03] 350:39<57:36, 7.93s/it | [Iter 2654/3090] R0[2564/3000] | LR: 0.001286 | E: -63.062204 | E_var:     3.7262 E_err:   0.030162 | NF_loss: 20.740598
[2025-11-13 06:38:11] 350:47<57:28, 7.93s/it | [Iter 2655/3090] R0[2565/3000] | LR: 0.001281 | E: -63.072650 | E_var:     3.8013 E_err:   0.030464 | NF_loss: 15.931013
[2025-11-13 06:38:18] 350:55<57:20, 7.93s/it | [Iter 2656/3090] R0[2566/3000] | LR: 0.001275 | E: -63.069962 | E_var:     3.5997 E_err:   0.029645 | NF_loss: 15.785297
[2025-11-13 06:38:26] 351:03<57:12, 7.93s/it | [Iter 2657/3090] R0[2567/3000] | LR: 0.001269 | E: -63.101982 | E_var:     3.7492 E_err:   0.030255 | NF_loss: 16.828284
[2025-11-13 06:38:34] 351:11<57:04, 7.93s/it | [Iter 2658/3090] R0[2568/3000] | LR: 0.001263 | E: -63.086798 | E_var:     3.6542 E_err:   0.029869 | NF_loss: 14.544517
[2025-11-13 06:38:42] 351:18<56:56, 7.93s/it | [Iter 2659/3090] R0[2569/3000] | LR: 0.001258 | E: -63.067946 | E_var:     3.7941 E_err:   0.030435 | NF_loss: 15.593719
[2025-11-13 06:38:50] 351:26<56:48, 7.93s/it | [Iter 2660/3090] R0[2570/3000] | LR: 0.001252 | E: -62.994685 | E_var:     4.1780 E_err:   0.031938 | NF_loss: 15.528177
[2025-11-13 06:38:57] 351:34<56:40, 7.93s/it | [Iter 2661/3090] R0[2571/3000] | LR: 0.001246 | E: -63.105129 | E_var:     3.7511 E_err:   0.030262 | NF_loss: 14.737512
[2025-11-13 06:39:05] 351:42<56:32, 7.93s/it | [Iter 2662/3090] R0[2572/3000] | LR: 0.001240 | E: -63.120370 | E_var:     4.0118 E_err:   0.031296 | NF_loss: 17.551804
[2025-11-13 06:39:13] 351:50<56:24, 7.93s/it | [Iter 2663/3090] R0[2573/3000] | LR: 0.001235 | E: -63.092297 | E_var:     3.9895 E_err:   0.031209 | NF_loss: 15.469726
[2025-11-13 06:39:21] 351:57<56:16, 7.93s/it | [Iter 2664/3090] R0[2574/3000] | LR: 0.001229 | E: -63.108696 | E_var:     3.8611 E_err:   0.030703 | NF_loss: 14.435073
[2025-11-13 06:39:29] 352:05<56:09, 7.93s/it | [Iter 2665/3090] R0[2575/3000] | LR: 0.001223 | E: -63.083119 | E_var:     3.6563 E_err:   0.029877 | NF_loss: 17.096592
[2025-11-13 06:39:36] 352:13<56:01, 7.93s/it | [Iter 2666/3090] R0[2576/3000] | LR: 0.001218 | E: -63.091792 | E_var:     3.6051 E_err:   0.029667 | NF_loss: 18.042111
[2025-11-13 06:39:44] 352:21<55:53, 7.93s/it | [Iter 2667/3090] R0[2577/3000] | LR: 0.001212 | E: -63.133611 | E_var:     3.7240 E_err:   0.030153 | NF_loss: 18.521002
[2025-11-13 06:39:52] 352:29<55:45, 7.93s/it | [Iter 2668/3090] R0[2578/3000] | LR: 0.001207 | E: -63.090026 | E_var:     3.8769 E_err:   0.030766 | NF_loss: 17.400716
[2025-11-13 06:40:00] 352:36<55:37, 7.93s/it | [Iter 2669/3090] R0[2579/3000] | LR: 0.001201 | E: -63.103573 | E_var:     3.7645 E_err:   0.030316 | NF_loss: 13.769942
[2025-11-13 06:40:08] 352:44<55:29, 7.93s/it | [Iter 2670/3090] R0[2580/3000] | LR: 0.001195 | E: -63.103059 | E_var:     3.8733 E_err:   0.030751 | NF_loss: 15.307853
[2025-11-13 06:40:15] 352:52<55:21, 7.93s/it | [Iter 2671/3090] R0[2581/3000] | LR: 0.001190 | E: -63.043404 | E_var:     4.0941 E_err:   0.031615 | NF_loss: 13.093180
[2025-11-13 06:40:23] 353:00<55:13, 7.93s/it | [Iter 2672/3090] R0[2582/3000] | LR: 0.001184 | E: -63.060267 | E_var:     4.3730 E_err:   0.032675 | NF_loss: 12.709554
[2025-11-13 06:40:31] 353:08<55:05, 7.93s/it | [Iter 2673/3090] R0[2583/3000] | LR: 0.001179 | E: -63.042615 | E_var:     3.7409 E_err:   0.030221 | NF_loss: 16.175054
[2025-11-13 06:40:39] 353:15<54:57, 7.93s/it | [Iter 2674/3090] R0[2584/3000] | LR: 0.001173 | E: -63.022526 | E_var:     3.6490 E_err:   0.029847 | NF_loss: 18.444991
[2025-11-13 06:40:47] 353:23<54:49, 7.93s/it | [Iter 2675/3090] R0[2585/3000] | LR: 0.001168 | E: -62.880789 | E_var:     4.0057 E_err:   0.031272 | NF_loss: 14.858258
[2025-11-13 06:40:55] 353:31<54:41, 7.93s/it | [Iter 2676/3090] R0[2586/3000] | LR: 0.001162 | E: -62.978210 | E_var:     3.8522 E_err:   0.030667 | NF_loss: 11.842095
[2025-11-13 06:41:02] 353:39<54:33, 7.93s/it | [Iter 2677/3090] R0[2587/3000] | LR: 0.001157 | E: -63.042359 | E_var:     3.9607 E_err:   0.031096 | NF_loss: 11.636941
[2025-11-13 06:41:10] 353:47<54:25, 7.93s/it | [Iter 2678/3090] R0[2588/3000] | LR: 0.001151 | E: -63.017920 | E_var:     3.5195 E_err:   0.029313 | NF_loss: 16.161386
[2025-11-13 06:41:18] 353:55<54:17, 7.93s/it | [Iter 2679/3090] R0[2589/3000] | LR: 0.001146 | E: -63.075497 | E_var:     3.8082 E_err:   0.030491 | NF_loss: 16.502082
[2025-11-13 06:41:26] 354:02<54:09, 7.93s/it | [Iter 2680/3090] R0[2590/3000] | LR: 0.001140 | E: -62.976643 | E_var:     3.6902 E_err:   0.030015 | NF_loss: 14.640451
[2025-11-13 06:41:34] 354:10<54:01, 7.93s/it | [Iter 2681/3090] R0[2591/3000] | LR: 0.001135 | E: -63.033120 | E_var:     3.7257 E_err:   0.030159 | NF_loss: 15.179911
[2025-11-13 06:41:41] 354:18<53:53, 7.93s/it | [Iter 2682/3090] R0[2592/3000] | LR: 0.001129 | E: -63.035381 | E_var:     3.6569 E_err:   0.029880 | NF_loss: 11.976033
[2025-11-13 06:41:49] 354:26<53:45, 7.93s/it | [Iter 2683/3090] R0[2593/3000] | LR: 0.001124 | E: -63.088246 | E_var:     3.6266 E_err:   0.029756 | NF_loss: 11.740163
[2025-11-13 06:41:57] 354:34<53:38, 7.93s/it | [Iter 2684/3090] R0[2594/3000] | LR: 0.001118 | E: -63.046649 | E_var:     3.5760 E_err:   0.029548 | NF_loss: 16.274805
[2025-11-13 06:42:05] 354:41<53:30, 7.93s/it | [Iter 2685/3090] R0[2595/3000] | LR: 0.001113 | E: -63.135256 | E_var:     3.6965 E_err:   0.030041 | NF_loss: 15.997611
[2025-11-13 06:42:13] 354:49<53:22, 7.93s/it | [Iter 2686/3090] R0[2596/3000] | LR: 0.001108 | E: -63.109263 | E_var:     3.8908 E_err:   0.030821 | NF_loss: 12.822459
[2025-11-13 06:42:20] 354:57<53:14, 7.93s/it | [Iter 2687/3090] R0[2597/3000] | LR: 0.001102 | E: -63.121680 | E_var:     3.8613 E_err:   0.030703 | NF_loss: 13.635714
[2025-11-13 06:42:28] 355:05<53:06, 7.93s/it | [Iter 2688/3090] R0[2598/3000] | LR: 0.001097 | E: -63.083344 | E_var:     3.5166 E_err:   0.029301 | NF_loss: 16.486099
[2025-11-13 06:42:36] 355:13<52:58, 7.93s/it | [Iter 2689/3090] R0[2599/3000] | LR: 0.001091 | E: -63.123825 | E_var:     3.6126 E_err:   0.029698 | NF_loss: 11.453886
[2025-11-13 06:42:44] 355:20<52:50, 7.93s/it | [Iter 2690/3090] R0[2600/3000] | LR: 0.001086 | E: -63.101963 | E_var:     3.5947 E_err:   0.029625 | NF_loss: 18.621212
[2025-11-13 06:42:52] 355:28<52:42, 7.93s/it | [Iter 2691/3090] R0[2601/3000] | LR: 0.001081 | E: -63.121754 | E_var:     3.5002 E_err:   0.029233 | NF_loss: 13.797752
[2025-11-13 06:42:59] 355:36<52:34, 7.93s/it | [Iter 2692/3090] R0[2602/3000] | LR: 0.001075 | E: -63.062330 | E_var:     3.5712 E_err:   0.029528 | NF_loss: 14.954064
[2025-11-13 06:43:07] 355:44<52:26, 7.93s/it | [Iter 2693/3090] R0[2603/3000] | LR: 0.001070 | E: -63.080342 | E_var:     3.5390 E_err:   0.029394 | NF_loss: 14.034092
[2025-11-13 06:43:15] 355:52<52:18, 7.93s/it | [Iter 2694/3090] R0[2604/3000] | LR: 0.001065 | E: -63.087945 | E_var:     3.5134 E_err:   0.029288 | NF_loss: 14.674901
[2025-11-13 06:43:23] 355:59<52:10, 7.93s/it | [Iter 2695/3090] R0[2605/3000] | LR: 0.001060 | E: -63.131642 | E_var:     3.4755 E_err:   0.029129 | NF_loss: 15.149758
[2025-11-13 06:43:31] 356:07<52:02, 7.93s/it | [Iter 2696/3090] R0[2606/3000] | LR: 0.001054 | E: -63.136303 | E_var:     3.6757 E_err:   0.029956 | NF_loss: 18.019630
[2025-11-13 06:43:38] 356:15<51:54, 7.93s/it | [Iter 2697/3090] R0[2607/3000] | LR: 0.001049 | E: -63.135092 | E_var:     3.6596 E_err:   0.029891 | NF_loss: 13.481595
[2025-11-13 06:43:46] 356:23<51:46, 7.93s/it | [Iter 2698/3090] R0[2608/3000] | LR: 0.001044 | E: -63.090782 | E_var:     3.4908 E_err:   0.029193 | NF_loss: 16.266978
[2025-11-13 06:43:54] 356:30<51:38, 7.93s/it | [Iter 2699/3090] R0[2609/3000] | LR: 0.001039 | E: -63.095568 | E_var:     3.7303 E_err:   0.030178 | NF_loss: 14.632248
[2025-11-13 06:44:02] 356:38<51:30, 7.93s/it | [Iter 2700/3090] R0[2610/3000] | LR: 0.001033 | E: -63.065768 | E_var:     3.3660 E_err:   0.028667 | NF_loss: 14.743994
[2025-11-13 06:44:02] 保存checkpoint: hybrid_checkpoint_iter_002700.pkl
[2025-11-13 06:44:10] 356:46<51:23, 7.93s/it | [Iter 2701/3090] R0[2611/3000] | LR: 0.001028 | E: -63.067830 | E_var:     3.4511 E_err:   0.029027 | NF_loss: 15.614524
[2025-11-13 06:44:18] 356:54<51:15, 7.93s/it | [Iter 2702/3090] R0[2612/3000] | LR: 0.001023 | E: -63.075760 | E_var:     3.6276 E_err:   0.029760 | NF_loss: 14.588101
[2025-11-13 06:44:25] 357:02<51:07, 7.93s/it | [Iter 2703/3090] R0[2613/3000] | LR: 0.001018 | E: -63.108290 | E_var:     3.4332 E_err:   0.028951 | NF_loss: 16.254020
[2025-11-13 06:44:33] 357:10<50:59, 7.93s/it | [Iter 2704/3090] R0[2614/3000] | LR: 0.001013 | E: -63.046254 | E_var:     3.4000 E_err:   0.028811 | NF_loss: 13.175505
[2025-11-13 06:44:41] 357:18<50:51, 7.93s/it | [Iter 2705/3090] R0[2615/3000] | LR: 0.001007 | E: -63.010366 | E_var:     3.6112 E_err:   0.029692 | NF_loss: 17.593917
[2025-11-13 06:44:49] 357:25<50:43, 7.93s/it | [Iter 2706/3090] R0[2616/3000] | LR: 0.001002 | E: -62.996719 | E_var:     3.6800 E_err:   0.029974 | NF_loss: 15.534496
[2025-11-13 06:44:57] 357:33<50:35, 7.93s/it | [Iter 2707/3090] R0[2617/3000] | LR: 0.000997 | E: -63.014937 | E_var:     3.5595 E_err:   0.029479 | NF_loss: 13.346048
[2025-11-13 06:45:04] 357:41<50:27, 7.93s/it | [Iter 2708/3090] R0[2618/3000] | LR: 0.000992 | E: -63.046738 | E_var:     3.4882 E_err:   0.029183 | NF_loss: 15.188143
[2025-11-13 06:45:12] 357:49<50:19, 7.93s/it | [Iter 2709/3090] R0[2619/3000] | LR: 0.000987 | E: -63.103969 | E_var:     3.5387 E_err:   0.029393 | NF_loss: 13.540528
[2025-11-13 06:45:20] 357:57<50:11, 7.93s/it | [Iter 2710/3090] R0[2620/3000] | LR: 0.000982 | E: -63.098912 | E_var:     3.5215 E_err:   0.029321 | NF_loss: 16.294274
[2025-11-13 06:45:28] 358:05<50:03, 7.93s/it | [Iter 2711/3090] R0[2621/3000] | LR: 0.000977 | E: -63.101181 | E_var:     3.5400 E_err:   0.029398 | NF_loss: 16.065719
[2025-11-13 06:45:36] 358:12<49:55, 7.93s/it | [Iter 2712/3090] R0[2622/3000] | LR: 0.000972 | E: -63.111235 | E_var:     3.3582 E_err:   0.028634 | NF_loss: 13.390671
[2025-11-13 06:45:44] 358:20<49:47, 7.93s/it | [Iter 2713/3090] R0[2623/3000] | LR: 0.000967 | E: -63.068756 | E_var:     3.5572 E_err:   0.029470 | NF_loss: 18.835295
[2025-11-13 06:45:51] 358:28<49:39, 7.92s/it | [Iter 2714/3090] R0[2624/3000] | LR: 0.000962 | E: -63.086205 | E_var:     3.4078 E_err:   0.028844 | NF_loss: 14.982743
[2025-11-13 06:45:59] 358:36<49:31, 7.92s/it | [Iter 2715/3090] R0[2625/3000] | LR: 0.000957 | E: -63.039264 | E_var:     3.6246 E_err:   0.029748 | NF_loss: 13.695352
[2025-11-13 06:46:07] 358:44<49:23, 7.92s/it | [Iter 2716/3090] R0[2626/3000] | LR: 0.000952 | E: -62.959500 | E_var:     3.6274 E_err:   0.029759 | NF_loss: 14.568608
[2025-11-13 06:46:15] 358:51<49:15, 7.92s/it | [Iter 2717/3090] R0[2627/3000] | LR: 0.000947 | E: -62.881135 | E_var:     3.8432 E_err:   0.030631 | NF_loss: 15.225437
[2025-11-13 06:46:23] 358:59<49:08, 7.92s/it | [Iter 2718/3090] R0[2628/3000] | LR: 0.000942 | E: -62.981404 | E_var:     3.9919 E_err:   0.031219 | NF_loss: 11.425868
[2025-11-13 06:46:30] 359:07<49:00, 7.92s/it | [Iter 2719/3090] R0[2629/3000] | LR: 0.000937 | E: -63.031518 | E_var:     3.7848 E_err:   0.030398 | NF_loss: 18.446655
[2025-11-13 06:46:38] 359:15<48:52, 7.92s/it | [Iter 2720/3090] R0[2630/3000] | LR: 0.000932 | E: -63.079839 | E_var:     3.5387 E_err:   0.029393 | NF_loss: 12.073151
[2025-11-13 06:46:46] 359:23<48:44, 7.92s/it | [Iter 2721/3090] R0[2631/3000] | LR: 0.000927 | E: -63.084151 | E_var:     3.5072 E_err:   0.029262 | NF_loss: 14.685266
[2025-11-13 06:46:54] 359:30<48:36, 7.92s/it | [Iter 2722/3090] R0[2632/3000] | LR: 0.000922 | E: -63.099082 | E_var:     3.4808 E_err:   0.029152 | NF_loss: 17.108576
[2025-11-13 06:47:02] 359:38<48:28, 7.92s/it | [Iter 2723/3090] R0[2633/3000] | LR: 0.000917 | E: -63.074896 | E_var:     3.4048 E_err:   0.028832 | NF_loss: 11.960409
[2025-11-13 06:47:09] 359:46<48:20, 7.92s/it | [Iter 2724/3090] R0[2634/3000] | LR: 0.000912 | E: -63.100343 | E_var:     3.6231 E_err:   0.029741 | NF_loss: 10.359823
[2025-11-13 06:47:17] 359:54<48:12, 7.92s/it | [Iter 2725/3090] R0[2635/3000] | LR: 0.000907 | E: -63.091334 | E_var:     3.6386 E_err:   0.029805 | NF_loss: 14.451203
[2025-11-13 06:47:25] 360:02<48:04, 7.92s/it | [Iter 2726/3090] R0[2636/3000] | LR: 0.000902 | E: -63.103886 | E_var:     3.6038 E_err:   0.029662 | NF_loss: 14.562063
[2025-11-13 06:47:33] 360:09<47:56, 7.92s/it | [Iter 2727/3090] R0[2637/3000] | LR: 0.000897 | E: -63.133049 | E_var:     3.3629 E_err:   0.028653 | NF_loss: 13.322352
[2025-11-13 06:47:41] 360:17<47:48, 7.92s/it | [Iter 2728/3090] R0[2638/3000] | LR: 0.000892 | E: -63.125003 | E_var:     3.3156 E_err:   0.028451 | NF_loss: 15.846076
[2025-11-13 06:47:48] 360:25<47:40, 7.92s/it | [Iter 2729/3090] R0[2639/3000] | LR: 0.000888 | E: -63.043122 | E_var:     3.3133 E_err:   0.028441 | NF_loss: 13.128255
[2025-11-13 06:47:56] 360:33<47:32, 7.92s/it | [Iter 2730/3090] R0[2640/3000] | LR: 0.000883 | E: -62.995191 | E_var:     3.5247 E_err:   0.029335 | NF_loss: 13.066880
[2025-11-13 06:48:04] 360:41<47:24, 7.92s/it | [Iter 2731/3090] R0[2641/3000] | LR: 0.000878 | E: -62.902953 | E_var:     3.5594 E_err:   0.029479 | NF_loss: 14.099239
[2025-11-13 06:48:12] 360:48<47:16, 7.92s/it | [Iter 2732/3090] R0[2642/3000] | LR: 0.000873 | E: -62.745128 | E_var:     3.7846 E_err:   0.030397 | NF_loss: 16.307363
[2025-11-13 06:48:20] 360:56<47:08, 7.92s/it | [Iter 2733/3090] R0[2643/3000] | LR: 0.000868 | E: -62.786511 | E_var:     3.7108 E_err:   0.030099 | NF_loss: 14.410584
[2025-11-13 06:48:27] 361:04<47:00, 7.92s/it | [Iter 2734/3090] R0[2644/3000] | LR: 0.000863 | E: -62.926576 | E_var:     3.5050 E_err:   0.029253 | NF_loss: 12.957743
[2025-11-13 06:48:35] 361:12<46:53, 7.92s/it | [Iter 2735/3090] R0[2645/3000] | LR: 0.000859 | E: -63.030252 | E_var:     3.4751 E_err:   0.029128 | NF_loss: 13.044732
[2025-11-13 06:48:43] 361:20<46:45, 7.92s/it | [Iter 2736/3090] R0[2646/3000] | LR: 0.000854 | E: -62.983780 | E_var:     3.4484 E_err:   0.029015 | NF_loss: 15.773819
[2025-11-13 06:48:51] 361:27<46:37, 7.92s/it | [Iter 2737/3090] R0[2647/3000] | LR: 0.000849 | E: -62.958068 | E_var:     3.5341 E_err:   0.029374 | NF_loss: 12.867154
[2025-11-13 06:48:59] 361:35<46:29, 7.92s/it | [Iter 2738/3090] R0[2648/3000] | LR: 0.000844 | E: -62.986554 | E_var:     3.4991 E_err:   0.029228 | NF_loss: 14.324200
[2025-11-13 06:49:06] 361:43<46:21, 7.92s/it | [Iter 2739/3090] R0[2649/3000] | LR: 0.000840 | E: -63.010491 | E_var:     3.4595 E_err:   0.029062 | NF_loss: 13.328593
[2025-11-13 06:49:14] 361:51<46:13, 7.92s/it | [Iter 2740/3090] R0[2650/3000] | LR: 0.000835 | E: -63.031503 | E_var:     3.4732 E_err:   0.029120 | NF_loss: 13.556572
[2025-11-13 06:49:22] 361:59<46:05, 7.92s/it | [Iter 2741/3090] R0[2651/3000] | LR: 0.000830 | E: -63.075300 | E_var:     3.5085 E_err:   0.029267 | NF_loss: 13.854572
[2025-11-13 06:49:30] 362:06<45:57, 7.92s/it | [Iter 2742/3090] R0[2652/3000] | LR: 0.000826 | E: -63.045193 | E_var:     3.4619 E_err:   0.029072 | NF_loss: 15.998238
[2025-11-13 06:49:38] 362:14<45:49, 7.92s/it | [Iter 2743/3090] R0[2653/3000] | LR: 0.000821 | E: -63.071923 | E_var:     3.6164 E_err:   0.029714 | NF_loss: 13.733133
[2025-11-13 06:49:45] 362:22<45:41, 7.92s/it | [Iter 2744/3090] R0[2654/3000] | LR: 0.000816 | E: -63.055419 | E_var:     3.8502 E_err:   0.030659 | NF_loss: 15.710969
[2025-11-13 06:49:53] 362:30<45:33, 7.92s/it | [Iter 2745/3090] R0[2655/3000] | LR: 0.000812 | E: -63.138110 | E_var:     3.6232 E_err:   0.029742 | NF_loss: 14.888288
[2025-11-13 06:50:01] 362:38<45:25, 7.92s/it | [Iter 2746/3090] R0[2656/3000] | LR: 0.000807 | E: -63.149262 | E_var:     3.7367 E_err:   0.030204 | NF_loss: 13.361085
[2025-11-13 06:50:09] 362:45<45:17, 7.92s/it | [Iter 2747/3090] R0[2657/3000] | LR: 0.000802 | E: -63.116548 | E_var:     3.5871 E_err:   0.029593 | NF_loss: 17.587462
[2025-11-13 06:50:17] 362:53<45:09, 7.92s/it | [Iter 2748/3090] R0[2658/3000] | LR: 0.000798 | E: -63.073781 | E_var:     3.4495 E_err:   0.029020 | NF_loss: 13.820978
[2025-11-13 06:50:25] 363:01<45:01, 7.92s/it | [Iter 2749/3090] R0[2659/3000] | LR: 0.000793 | E: -63.046454 | E_var:     3.6122 E_err:   0.029697 | NF_loss: 13.578933
[2025-11-13 06:50:32] 363:09<44:53, 7.92s/it | [Iter 2750/3090] R0[2660/3000] | LR: 0.000789 | E: -63.096186 | E_var:     4.0256 E_err:   0.031350 | NF_loss: 13.702292
[2025-11-13 06:50:40] 363:17<44:46, 7.92s/it | [Iter 2751/3090] R0[2661/3000] | LR: 0.000784 | E: -63.149499 | E_var:     3.6590 E_err:   0.029888 | NF_loss: 14.139855
[2025-11-13 06:50:48] 363:25<44:38, 7.92s/it | [Iter 2752/3090] R0[2662/3000] | LR: 0.000780 | E: -63.079445 | E_var:     4.2092 E_err:   0.032057 | NF_loss: 12.680129
[2025-11-13 06:50:56] 363:32<44:30, 7.92s/it | [Iter 2753/3090] R0[2663/3000] | LR: 0.000775 | E: -63.082663 | E_var:     3.8585 E_err:   0.030692 | NF_loss: 17.033509
[2025-11-13 06:51:04] 363:40<44:22, 7.92s/it | [Iter 2754/3090] R0[2664/3000] | LR: 0.000770 | E: -63.071867 | E_var:     3.6221 E_err:   0.029737 | NF_loss: 14.841426
[2025-11-13 06:51:11] 363:48<44:14, 7.92s/it | [Iter 2755/3090] R0[2665/3000] | LR: 0.000766 | E: -63.099147 | E_var:     3.6208 E_err:   0.029732 | NF_loss: 14.753340
[2025-11-13 06:51:19] 363:56<44:06, 7.92s/it | [Iter 2756/3090] R0[2666/3000] | LR: 0.000761 | E: -63.099361 | E_var:     3.9152 E_err:   0.030917 | NF_loss: 13.592254
[2025-11-13 06:51:27] 364:04<43:58, 7.92s/it | [Iter 2757/3090] R0[2667/3000] | LR: 0.000757 | E: -63.129461 | E_var:     3.9221 E_err:   0.030944 | NF_loss: 13.769506
[2025-11-13 06:51:35] 364:11<43:50, 7.92s/it | [Iter 2758/3090] R0[2668/3000] | LR: 0.000752 | E: -63.111704 | E_var:     4.1043 E_err:   0.031655 | NF_loss: 14.043710
[2025-11-13 06:51:43] 364:19<43:42, 7.92s/it | [Iter 2759/3090] R0[2669/3000] | LR: 0.000748 | E: -63.151097 | E_var:     3.6242 E_err:   0.029746 | NF_loss: 15.676216
[2025-11-13 06:51:50] 364:27<43:34, 7.92s/it | [Iter 2760/3090] R0[2670/3000] | LR: 0.000744 | E: -63.069884 | E_var:     3.9316 E_err:   0.030982 | NF_loss: 14.469268
[2025-11-13 06:51:58] 364:35<43:26, 7.92s/it | [Iter 2761/3090] R0[2671/3000] | LR: 0.000739 | E: -63.043093 | E_var:     4.0572 E_err:   0.031473 | NF_loss: 12.177914
[2025-11-13 06:52:06] 364:43<43:18, 7.92s/it | [Iter 2762/3090] R0[2672/3000] | LR: 0.000735 | E: -63.107530 | E_var:     3.9707 E_err:   0.031135 | NF_loss: 13.755320
[2025-11-13 06:52:14] 364:50<43:10, 7.92s/it | [Iter 2763/3090] R0[2673/3000] | LR: 0.000730 | E: -63.077853 | E_var:     4.1830 E_err:   0.031957 | NF_loss: 14.786082
[2025-11-13 06:52:22] 364:58<43:02, 7.92s/it | [Iter 2764/3090] R0[2674/3000] | LR: 0.000726 | E: -62.996821 | E_var:     4.3458 E_err:   0.032573 | NF_loss: 11.655587
[2025-11-13 06:52:29] 365:06<42:54, 7.92s/it | [Iter 2765/3090] R0[2675/3000] | LR: 0.000721 | E: -62.908047 | E_var:     4.3437 E_err:   0.032565 | NF_loss: 12.639479
[2025-11-13 06:52:37] 365:14<42:46, 7.92s/it | [Iter 2766/3090] R0[2676/3000] | LR: 0.000717 | E: -62.993107 | E_var:     4.1379 E_err:   0.031784 | NF_loss: 16.016566
[2025-11-13 06:52:45] 365:22<42:39, 7.92s/it | [Iter 2767/3090] R0[2677/3000] | LR: 0.000713 | E: -62.906562 | E_var:     4.8846 E_err:   0.034533 | NF_loss: 14.164226
[2025-11-13 06:52:53] 365:29<42:31, 7.92s/it | [Iter 2768/3090] R0[2678/3000] | LR: 0.000708 | E: -62.938053 | E_var:     4.2763 E_err:   0.032311 | NF_loss: 11.828417
[2025-11-13 06:53:01] 365:37<42:23, 7.92s/it | [Iter 2769/3090] R0[2679/3000] | LR: 0.000704 | E: -62.953271 | E_var:     4.4712 E_err:   0.033040 | NF_loss: 19.061053
[2025-11-13 06:53:08] 365:45<42:15, 7.92s/it | [Iter 2770/3090] R0[2680/3000] | LR: 0.000700 | E: -62.938580 | E_var:     4.7926 E_err:   0.034206 | NF_loss: 10.703439
[2025-11-13 06:53:16] 365:53<42:07, 7.92s/it | [Iter 2771/3090] R0[2681/3000] | LR: 0.000695 | E: -62.929797 | E_var:     4.1835 E_err:   0.031959 | NF_loss: 12.979532
[2025-11-13 06:53:24] 366:01<41:59, 7.92s/it | [Iter 2772/3090] R0[2682/3000] | LR: 0.000691 | E: -63.047726 | E_var:     3.8739 E_err:   0.030754 | NF_loss: 14.137764
[2025-11-13 06:53:32] 366:08<41:51, 7.92s/it | [Iter 2773/3090] R0[2683/3000] | LR: 0.000687 | E: -63.061281 | E_var:     3.8265 E_err:   0.030565 | NF_loss: 18.183325
[2025-11-13 06:53:40] 366:16<41:43, 7.92s/it | [Iter 2774/3090] R0[2684/3000] | LR: 0.000683 | E: -63.098778 | E_var:     3.8388 E_err:   0.030614 | NF_loss: 15.368409
[2025-11-13 06:53:47] 366:24<41:35, 7.92s/it | [Iter 2775/3090] R0[2685/3000] | LR: 0.000678 | E: -63.133400 | E_var:     3.7094 E_err:   0.030094 | NF_loss: 18.709189
[2025-11-13 06:53:55] 366:32<41:27, 7.92s/it | [Iter 2776/3090] R0[2686/3000] | LR: 0.000674 | E: -63.152231 | E_var:     3.8727 E_err:   0.030749 | NF_loss: 17.949040
[2025-11-13 06:54:03] 366:40<41:19, 7.92s/it | [Iter 2777/3090] R0[2687/3000] | LR: 0.000670 | E: -63.064450 | E_var:     3.5995 E_err:   0.029644 | NF_loss: 17.248000
[2025-11-13 06:54:11] 366:47<41:11, 7.92s/it | [Iter 2778/3090] R0[2688/3000] | LR: 0.000666 | E: -63.004706 | E_var:     3.7759 E_err:   0.030362 | NF_loss: 17.102302
[2025-11-13 06:54:19] 366:55<41:03, 7.92s/it | [Iter 2779/3090] R0[2689/3000] | LR: 0.000661 | E: -62.999857 | E_var:     3.7161 E_err:   0.030121 | NF_loss: 12.765759
[2025-11-13 06:54:27] 367:03<40:55, 7.92s/it | [Iter 2780/3090] R0[2690/3000] | LR: 0.000657 | E: -63.054699 | E_var:     3.6801 E_err:   0.029974 | NF_loss: 12.252911
[2025-11-13 06:54:34] 367:11<40:47, 7.92s/it | [Iter 2781/3090] R0[2691/3000] | LR: 0.000653 | E: -63.022310 | E_var:     3.6854 E_err:   0.029996 | NF_loss: 14.952583
[2025-11-13 06:54:42] 367:19<40:40, 7.92s/it | [Iter 2782/3090] R0[2692/3000] | LR: 0.000649 | E: -63.054043 | E_var:     3.5830 E_err:   0.029576 | NF_loss: 17.355623
[2025-11-13 06:54:50] 367:27<40:32, 7.92s/it | [Iter 2783/3090] R0[2693/3000] | LR: 0.000645 | E: -63.107323 | E_var:     3.7838 E_err:   0.030394 | NF_loss: 11.427692
[2025-11-13 06:54:58] 367:34<40:24, 7.92s/it | [Iter 2784/3090] R0[2694/3000] | LR: 0.000641 | E: -63.137689 | E_var:     3.7689 E_err:   0.030334 | NF_loss: 14.158757
[2025-11-13 06:55:06] 367:42<40:16, 7.92s/it | [Iter 2785/3090] R0[2695/3000] | LR: 0.000636 | E: -63.144907 | E_var:     4.0298 E_err:   0.031366 | NF_loss: 11.950893
[2025-11-13 06:55:14] 367:50<40:08, 7.92s/it | [Iter 2786/3090] R0[2696/3000] | LR: 0.000632 | E: -63.121853 | E_var:     3.6018 E_err:   0.029654 | NF_loss: 14.130776
[2025-11-13 06:55:21] 367:58<40:00, 7.92s/it | [Iter 2787/3090] R0[2697/3000] | LR: 0.000628 | E: -63.141437 | E_var:     3.6473 E_err:   0.029840 | NF_loss: 11.695936
[2025-11-13 06:55:29] 368:06<39:52, 7.92s/it | [Iter 2788/3090] R0[2698/3000] | LR: 0.000624 | E: -63.056223 | E_var:     3.6366 E_err:   0.029797 | NF_loss: 12.979877
[2025-11-13 06:55:37] 368:13<39:44, 7.92s/it | [Iter 2789/3090] R0[2699/3000] | LR: 0.000620 | E: -63.030599 | E_var:     3.5983 E_err:   0.029640 | NF_loss: 11.705064
[2025-11-13 06:55:45] 368:21<39:36, 7.92s/it | [Iter 2790/3090] R0[2700/3000] | LR: 0.000616 | E: -63.044016 | E_var:     3.6518 E_err:   0.029859 | NF_loss: 12.714070
[2025-11-13 06:55:53] 368:29<39:28, 7.92s/it | [Iter 2791/3090] R0[2701/3000] | LR: 0.000612 | E: -63.042125 | E_var:     3.6783 E_err:   0.029967 | NF_loss: 15.651285
[2025-11-13 06:56:00] 368:37<39:20, 7.92s/it | [Iter 2792/3090] R0[2702/3000] | LR: 0.000608 | E: -63.099636 | E_var:     3.6983 E_err:   0.030048 | NF_loss: 13.414231
[2025-11-13 06:56:08] 368:45<39:12, 7.92s/it | [Iter 2793/3090] R0[2703/3000] | LR: 0.000604 | E: -63.120216 | E_var:     3.6879 E_err:   0.030006 | NF_loss: 18.600433
[2025-11-13 06:56:16] 368:52<39:04, 7.92s/it | [Iter 2794/3090] R0[2704/3000] | LR: 0.000600 | E: -63.115336 | E_var:     3.9445 E_err:   0.031033 | NF_loss: 13.129596
[2025-11-13 06:56:24] 369:00<38:56, 7.92s/it | [Iter 2795/3090] R0[2705/3000] | LR: 0.000596 | E: -63.102786 | E_var:     4.4934 E_err:   0.033121 | NF_loss: 14.804093
[2025-11-13 06:56:32] 369:08<38:48, 7.92s/it | [Iter 2796/3090] R0[2706/3000] | LR: 0.000592 | E: -63.028906 | E_var:     4.3556 E_err:   0.032609 | NF_loss: 15.186418
[2025-11-13 06:56:39] 369:16<38:40, 7.92s/it | [Iter 2797/3090] R0[2707/3000] | LR: 0.000588 | E: -62.970904 | E_var:     4.4191 E_err:   0.032846 | NF_loss: 13.132606
[2025-11-13 06:56:47] 369:24<38:33, 7.92s/it | [Iter 2798/3090] R0[2708/3000] | LR: 0.000584 | E: -63.059313 | E_var:     4.0131 E_err:   0.031301 | NF_loss: 14.129718
[2025-11-13 06:56:55] 369:31<38:25, 7.92s/it | [Iter 2799/3090] R0[2709/3000] | LR: 0.000580 | E: -63.097405 | E_var:     3.6106 E_err:   0.029690 | NF_loss: 14.360983
[2025-11-13 06:57:03] 369:39<38:17, 7.92s/it | [Iter 2800/3090] R0[2710/3000] | LR: 0.000576 | E: -63.127648 | E_var:     4.1361 E_err:   0.031777 | NF_loss: 17.860805
[2025-11-13 06:57:11] 369:47<38:09, 7.92s/it | [Iter 2801/3090] R0[2711/3000] | LR: 0.000572 | E: -63.052793 | E_var:     3.8876 E_err:   0.030808 | NF_loss: 12.913885
[2025-11-13 06:57:18] 369:55<38:01, 7.92s/it | [Iter 2802/3090] R0[2712/3000] | LR: 0.000568 | E: -63.135531 | E_var:     3.6164 E_err:   0.029714 | NF_loss: 14.919469
[2025-11-13 06:57:26] 370:03<37:53, 7.92s/it | [Iter 2803/3090] R0[2713/3000] | LR: 0.000564 | E: -63.111344 | E_var:     4.3664 E_err:   0.032650 | NF_loss: 20.208736
[2025-11-13 06:57:34] 370:10<37:45, 7.92s/it | [Iter 2804/3090] R0[2714/3000] | LR: 0.000560 | E: -63.080139 | E_var:     4.4919 E_err:   0.033116 | NF_loss: 16.446462
[2025-11-13 06:57:42] 370:18<37:37, 7.92s/it | [Iter 2805/3090] R0[2715/3000] | LR: 0.000557 | E: -63.035255 | E_var:     4.3516 E_err:   0.032595 | NF_loss: 16.674156
[2025-11-13 06:57:50] 370:26<37:29, 7.92s/it | [Iter 2806/3090] R0[2716/3000] | LR: 0.000553 | E: -63.050532 | E_var:     3.9876 E_err:   0.031202 | NF_loss: 14.325776
[2025-11-13 06:57:57] 370:34<37:21, 7.92s/it | [Iter 2807/3090] R0[2717/3000] | LR: 0.000549 | E: -63.080302 | E_var:     3.8125 E_err:   0.030509 | NF_loss: 17.389726
[2025-11-13 06:58:05] 370:42<37:13, 7.92s/it | [Iter 2808/3090] R0[2718/3000] | LR: 0.000545 | E: -63.115916 | E_var:     3.8923 E_err:   0.030826 | NF_loss: 19.277450
[2025-11-13 06:58:13] 370:50<37:05, 7.92s/it | [Iter 2809/3090] R0[2719/3000] | LR: 0.000541 | E: -63.107588 | E_var:     3.9371 E_err:   0.031003 | NF_loss: 15.456255
[2025-11-13 06:58:21] 370:57<36:57, 7.92s/it | [Iter 2810/3090] R0[2720/3000] | LR: 0.000537 | E: -63.108621 | E_var:     3.9370 E_err:   0.031003 | NF_loss: 14.925149
[2025-11-13 06:58:29] 371:05<36:49, 7.92s/it | [Iter 2811/3090] R0[2721/3000] | LR: 0.000534 | E: -63.082503 | E_var:     4.4559 E_err:   0.032983 | NF_loss: 16.759727
[2025-11-13 06:58:37] 371:13<36:42, 7.92s/it | [Iter 2812/3090] R0[2722/3000] | LR: 0.000530 | E: -63.054772 | E_var:     4.5016 E_err:   0.033152 | NF_loss: 17.978625
[2025-11-13 06:58:44] 371:21<36:34, 7.92s/it | [Iter 2813/3090] R0[2723/3000] | LR: 0.000526 | E: -63.100342 | E_var:     3.9049 E_err:   0.030876 | NF_loss: 14.300709
[2025-11-13 06:58:52] 371:29<36:26, 7.92s/it | [Iter 2814/3090] R0[2724/3000] | LR: 0.000522 | E: -63.052603 | E_var:     4.4059 E_err:   0.032797 | NF_loss: 12.568417
[2025-11-13 06:59:00] 371:36<36:18, 7.92s/it | [Iter 2815/3090] R0[2725/3000] | LR: 0.000519 | E: -62.998597 | E_var:     4.2288 E_err:   0.032131 | NF_loss: 11.265001
[2025-11-13 06:59:08] 371:44<36:10, 7.92s/it | [Iter 2816/3090] R0[2726/3000] | LR: 0.000515 | E: -63.002055 | E_var:     4.1268 E_err:   0.031741 | NF_loss: 12.896434
[2025-11-13 06:59:16] 371:52<36:02, 7.92s/it | [Iter 2817/3090] R0[2727/3000] | LR: 0.000511 | E: -63.027694 | E_var:     4.4082 E_err:   0.032806 | NF_loss: 17.875957
[2025-11-13 06:59:23] 372:00<35:54, 7.92s/it | [Iter 2818/3090] R0[2728/3000] | LR: 0.000507 | E: -63.070484 | E_var:     4.0729 E_err:   0.031534 | NF_loss: 17.015360
[2025-11-13 06:59:31] 372:08<35:46, 7.92s/it | [Iter 2819/3090] R0[2729/3000] | LR: 0.000504 | E: -63.073884 | E_var:     4.2422 E_err:   0.032182 | NF_loss: 13.392913
[2025-11-13 06:59:39] 372:15<35:38, 7.92s/it | [Iter 2820/3090] R0[2730/3000] | LR: 0.000500 | E: -63.046037 | E_var:     4.0022 E_err:   0.031258 | NF_loss: 14.326725
[2025-11-13 06:59:47] 372:23<35:30, 7.92s/it | [Iter 2821/3090] R0[2731/3000] | LR: 0.000496 | E: -62.966697 | E_var:     4.3821 E_err:   0.032709 | NF_loss: 14.632390
[2025-11-13 06:59:55] 372:31<35:22, 7.92s/it | [Iter 2822/3090] R0[2732/3000] | LR: 0.000493 | E: -62.969955 | E_var:     4.0488 E_err:   0.031440 | NF_loss: 14.971114
[2025-11-13 07:00:02] 372:39<35:14, 7.92s/it | [Iter 2823/3090] R0[2733/3000] | LR: 0.000489 | E: -63.035033 | E_var:     3.9897 E_err:   0.031210 | NF_loss: 14.730618
[2025-11-13 07:00:10] 372:47<35:06, 7.92s/it | [Iter 2824/3090] R0[2734/3000] | LR: 0.000486 | E: -63.038370 | E_var:     3.7317 E_err:   0.030184 | NF_loss: 12.274571
[2025-11-13 07:00:18] 372:54<34:58, 7.92s/it | [Iter 2825/3090] R0[2735/3000] | LR: 0.000482 | E: -63.084261 | E_var:     3.8892 E_err:   0.030814 | NF_loss: 14.394496
[2025-11-13 07:00:26] 373:02<34:50, 7.92s/it | [Iter 2826/3090] R0[2736/3000] | LR: 0.000478 | E: -63.096173 | E_var:     3.8695 E_err:   0.030736 | NF_loss: 15.341592
[2025-11-13 07:00:34] 373:10<34:43, 7.92s/it | [Iter 2827/3090] R0[2737/3000] | LR: 0.000475 | E: -63.036283 | E_var:     4.0155 E_err:   0.031311 | NF_loss: 12.652658
[2025-11-13 07:00:41] 373:18<34:35, 7.92s/it | [Iter 2828/3090] R0[2738/3000] | LR: 0.000471 | E: -63.013864 | E_var:     3.8833 E_err:   0.030791 | NF_loss: 12.315751
[2025-11-13 07:00:49] 373:26<34:27, 7.92s/it | [Iter 2829/3090] R0[2739/3000] | LR: 0.000468 | E: -63.033313 | E_var:     3.8355 E_err:   0.030601 | NF_loss: 12.354158
[2025-11-13 07:00:57] 373:33<34:19, 7.92s/it | [Iter 2830/3090] R0[2740/3000] | LR: 0.000464 | E: -63.073402 | E_var:     4.0079 E_err:   0.031281 | NF_loss: 13.506244
[2025-11-13 07:01:05] 373:41<34:11, 7.92s/it | [Iter 2831/3090] R0[2741/3000] | LR: 0.000461 | E: -63.094028 | E_var:     3.9405 E_err:   0.031017 | NF_loss: 14.867092
[2025-11-13 07:01:13] 373:49<34:03, 7.92s/it | [Iter 2832/3090] R0[2742/3000] | LR: 0.000457 | E: -63.126608 | E_var:     3.7533 E_err:   0.030271 | NF_loss: 12.412278
[2025-11-13 07:01:20] 373:57<33:55, 7.92s/it | [Iter 2833/3090] R0[2743/3000] | LR: 0.000454 | E: -63.131553 | E_var:     3.7326 E_err:   0.030187 | NF_loss: 14.970485
[2025-11-13 07:01:28] 374:05<33:47, 7.92s/it | [Iter 2834/3090] R0[2744/3000] | LR: 0.000450 | E: -63.115104 | E_var:     3.7451 E_err:   0.030238 | NF_loss: 13.256468
[2025-11-13 07:01:36] 374:12<33:39, 7.92s/it | [Iter 2835/3090] R0[2745/3000] | LR: 0.000447 | E: -63.137271 | E_var:     3.6933 E_err:   0.030028 | NF_loss: 13.130426
[2025-11-13 07:01:44] 374:20<33:31, 7.92s/it | [Iter 2836/3090] R0[2746/3000] | LR: 0.000443 | E: -63.054195 | E_var:     3.8099 E_err:   0.030499 | NF_loss: 13.628484
[2025-11-13 07:01:52] 374:28<33:23, 7.92s/it | [Iter 2837/3090] R0[2747/3000] | LR: 0.000440 | E: -63.081470 | E_var:     3.6656 E_err:   0.029915 | NF_loss: 11.491903
[2025-11-13 07:01:59] 374:36<33:15, 7.92s/it | [Iter 2838/3090] R0[2748/3000] | LR: 0.000436 | E: -63.110930 | E_var:     3.8330 E_err:   0.030591 | NF_loss: 15.640882
[2025-11-13 07:02:07] 374:44<33:07, 7.92s/it | [Iter 2839/3090] R0[2749/3000] | LR: 0.000433 | E: -63.056835 | E_var:     3.5685 E_err:   0.029516 | NF_loss: 16.090520
[2025-11-13 07:02:15] 374:51<32:59, 7.92s/it | [Iter 2840/3090] R0[2750/3000] | LR: 0.000429 | E: -63.104627 | E_var:     3.7289 E_err:   0.030173 | NF_loss: 12.128629
[2025-11-13 07:02:23] 374:59<32:51, 7.92s/it | [Iter 2841/3090] R0[2751/3000] | LR: 0.000426 | E: -63.049276 | E_var:     3.8278 E_err:   0.030570 | NF_loss: 18.795310
[2025-11-13 07:02:31] 375:07<32:44, 7.92s/it | [Iter 2842/3090] R0[2752/3000] | LR: 0.000423 | E: -63.076630 | E_var:     3.5861 E_err:   0.029589 | NF_loss: 12.686443
[2025-11-13 07:02:38] 375:15<32:36, 7.92s/it | [Iter 2843/3090] R0[2753/3000] | LR: 0.000419 | E: -63.137137 | E_var:     3.5624 E_err:   0.029491 | NF_loss: 17.719125
[2025-11-13 07:02:46] 375:23<32:28, 7.92s/it | [Iter 2844/3090] R0[2754/3000] | LR: 0.000416 | E: -63.139432 | E_var:     3.7134 E_err:   0.030110 | NF_loss: 17.237403
[2025-11-13 07:02:54] 375:31<32:20, 7.92s/it | [Iter 2845/3090] R0[2755/3000] | LR: 0.000413 | E: -63.124026 | E_var:     3.6220 E_err:   0.029737 | NF_loss: 12.884606
[2025-11-13 07:03:02] 375:38<32:12, 7.92s/it | [Iter 2846/3090] R0[2756/3000] | LR: 0.000409 | E: -63.091364 | E_var:     3.7581 E_err:   0.030290 | NF_loss: 13.713748
[2025-11-13 07:03:10] 375:46<32:04, 7.92s/it | [Iter 2847/3090] R0[2757/3000] | LR: 0.000406 | E: -63.038588 | E_var:     3.7766 E_err:   0.030365 | NF_loss: 15.844042
[2025-11-13 07:03:18] 375:54<31:56, 7.92s/it | [Iter 2848/3090] R0[2758/3000] | LR: 0.000403 | E: -63.030668 | E_var:     3.9551 E_err:   0.031074 | NF_loss: 13.736096
[2025-11-13 07:03:25] 376:02<31:48, 7.92s/it | [Iter 2849/3090] R0[2759/3000] | LR: 0.000399 | E: -63.017528 | E_var:     3.8239 E_err:   0.030555 | NF_loss: 14.927185
[2025-11-13 07:03:33] 376:10<31:40, 7.92s/it | [Iter 2850/3090] R0[2760/3000] | LR: 0.000396 | E: -63.063649 | E_var:     3.5944 E_err:   0.029623 | NF_loss: 16.852790
[2025-11-13 07:03:41] 376:17<31:32, 7.92s/it | [Iter 2851/3090] R0[2761/3000] | LR: 0.000393 | E: -63.063245 | E_var:     3.6862 E_err:   0.029999 | NF_loss: 14.756251
[2025-11-13 07:03:49] 376:25<31:24, 7.92s/it | [Iter 2852/3090] R0[2762/3000] | LR: 0.000390 | E: -63.026234 | E_var:     3.5642 E_err:   0.029499 | NF_loss: 15.063603
[2025-11-13 07:03:57] 376:33<31:16, 7.92s/it | [Iter 2853/3090] R0[2763/3000] | LR: 0.000386 | E: -62.971145 | E_var:     3.7502 E_err:   0.030258 | NF_loss: 14.521814
[2025-11-13 07:04:04] 376:41<31:08, 7.92s/it | [Iter 2854/3090] R0[2764/3000] | LR: 0.000383 | E: -62.998207 | E_var:     4.0071 E_err:   0.031278 | NF_loss: 18.062272
[2025-11-13 07:04:12] 376:49<31:01, 7.92s/it | [Iter 2855/3090] R0[2765/3000] | LR: 0.000380 | E: -63.068702 | E_var:     3.7039 E_err:   0.030071 | NF_loss: 17.168701
[2025-11-13 07:04:20] 376:56<30:53, 7.92s/it | [Iter 2856/3090] R0[2766/3000] | LR: 0.000377 | E: -63.039263 | E_var:     3.7716 E_err:   0.030345 | NF_loss: 15.340170
[2025-11-13 07:04:28] 377:04<30:45, 7.92s/it | [Iter 2857/3090] R0[2767/3000] | LR: 0.000374 | E: -63.014059 | E_var:     3.7092 E_err:   0.030093 | NF_loss: 24.137400
[2025-11-13 07:04:36] 377:12<30:37, 7.92s/it | [Iter 2858/3090] R0[2768/3000] | LR: 0.000370 | E: -62.970697 | E_var:     3.6431 E_err:   0.029823 | NF_loss: 16.471367
[2025-11-13 07:04:43] 377:20<30:29, 7.92s/it | [Iter 2859/3090] R0[2769/3000] | LR: 0.000367 | E: -63.040387 | E_var:     3.7295 E_err:   0.030175 | NF_loss: 14.777206
[2025-11-13 07:04:51] 377:28<30:21, 7.92s/it | [Iter 2860/3090] R0[2770/3000] | LR: 0.000364 | E: -63.046661 | E_var:     3.6666 E_err:   0.029919 | NF_loss: 9.768089
[2025-11-13 07:04:59] 377:35<30:13, 7.92s/it | [Iter 2861/3090] R0[2771/3000] | LR: 0.000361 | E: -63.021002 | E_var:     3.4043 E_err:   0.028829 | NF_loss: 15.325673
[2025-11-13 07:05:07] 377:43<30:05, 7.92s/it | [Iter 2862/3090] R0[2772/3000] | LR: 0.000358 | E: -63.100376 | E_var:     3.4592 E_err:   0.029061 | NF_loss: 21.616329
[2025-11-13 07:05:15] 377:51<29:57, 7.92s/it | [Iter 2863/3090] R0[2773/3000] | LR: 0.000355 | E: -63.045825 | E_var:     3.6110 E_err:   0.029692 | NF_loss: 17.651619
[2025-11-13 07:05:22] 377:59<29:49, 7.92s/it | [Iter 2864/3090] R0[2774/3000] | LR: 0.000352 | E: -63.051413 | E_var:     3.6461 E_err:   0.029836 | NF_loss: 14.576345
[2025-11-13 07:05:30] 378:07<29:41, 7.92s/it | [Iter 2865/3090] R0[2775/3000] | LR: 0.000349 | E: -63.040996 | E_var:     3.6414 E_err:   0.029816 | NF_loss: 15.903429
[2025-11-13 07:05:38] 378:14<29:33, 7.92s/it | [Iter 2866/3090] R0[2776/3000] | LR: 0.000345 | E: -63.082611 | E_var:     3.6992 E_err:   0.030052 | NF_loss: 18.919831
[2025-11-13 07:05:46] 378:22<29:25, 7.92s/it | [Iter 2867/3090] R0[2777/3000] | LR: 0.000342 | E: -63.011520 | E_var:     3.5008 E_err:   0.029235 | NF_loss: 12.830967
[2025-11-13 07:05:54] 378:30<29:17, 7.92s/it | [Iter 2868/3090] R0[2778/3000] | LR: 0.000339 | E: -63.124645 | E_var:     3.7655 E_err:   0.030320 | NF_loss: 14.868149
[2025-11-13 07:06:01] 378:38<29:10, 7.92s/it | [Iter 2869/3090] R0[2779/3000] | LR: 0.000336 | E: -63.141141 | E_var:     3.5680 E_err:   0.029514 | NF_loss: 20.701208
[2025-11-13 07:06:09] 378:46<29:02, 7.92s/it | [Iter 2870/3090] R0[2780/3000] | LR: 0.000333 | E: -62.945704 | E_var:     3.6115 E_err:   0.029694 | NF_loss: 19.723048
[2025-11-13 07:06:17] 378:53<28:54, 7.92s/it | [Iter 2871/3090] R0[2781/3000] | LR: 0.000330 | E: -62.950518 | E_var:     3.5530 E_err:   0.029452 | NF_loss: 15.425403
[2025-11-13 07:06:25] 379:01<28:46, 7.92s/it | [Iter 2872/3090] R0[2782/3000] | LR: 0.000327 | E: -62.966986 | E_var:     3.5796 E_err:   0.029562 | NF_loss: 20.574032
[2025-11-13 07:06:33] 379:09<28:38, 7.92s/it | [Iter 2873/3090] R0[2783/3000] | LR: 0.000324 | E: -62.981005 | E_var:     3.7694 E_err:   0.030336 | NF_loss: 15.528985
[2025-11-13 07:06:40] 379:17<28:30, 7.92s/it | [Iter 2874/3090] R0[2784/3000] | LR: 0.000321 | E: -62.947028 | E_var:     3.8776 E_err:   0.030768 | NF_loss: 17.814727
[2025-11-13 07:06:48] 379:25<28:22, 7.92s/it | [Iter 2875/3090] R0[2785/3000] | LR: 0.000319 | E: -62.966260 | E_var:     3.5861 E_err:   0.029589 | NF_loss: 16.927898
[2025-11-13 07:06:56] 379:32<28:14, 7.92s/it | [Iter 2876/3090] R0[2786/3000] | LR: 0.000316 | E: -62.982584 | E_var:     3.8322 E_err:   0.030588 | NF_loss: 18.994030
[2025-11-13 07:07:04] 379:40<28:06, 7.92s/it | [Iter 2877/3090] R0[2787/3000] | LR: 0.000313 | E: -63.025385 | E_var:     3.6733 E_err:   0.029946 | NF_loss: 18.588992
[2025-11-13 07:07:12] 379:48<27:58, 7.92s/it | [Iter 2878/3090] R0[2788/3000] | LR: 0.000310 | E: -62.945681 | E_var:     3.8694 E_err:   0.030736 | NF_loss: 14.132162
[2025-11-13 07:07:20] 379:56<27:50, 7.92s/it | [Iter 2879/3090] R0[2789/3000] | LR: 0.000307 | E: -62.952327 | E_var:     3.6926 E_err:   0.030025 | NF_loss: 14.328786
[2025-11-13 07:07:27] 380:04<27:42, 7.92s/it | [Iter 2880/3090] R0[2790/3000] | LR: 0.000304 | E: -62.999716 | E_var:     3.5954 E_err:   0.029627 | NF_loss: 16.773104
[2025-11-13 07:07:35] 380:12<27:34, 7.92s/it | [Iter 2881/3090] R0[2791/3000] | LR: 0.000301 | E: -63.059926 | E_var:     3.4490 E_err:   0.029018 | NF_loss: 20.017873
[2025-11-13 07:07:43] 380:19<27:26, 7.92s/it | [Iter 2882/3090] R0[2792/3000] | LR: 0.000298 | E: -63.164411 | E_var:     3.5981 E_err:   0.029639 | NF_loss: 15.415902
[2025-11-13 07:07:51] 380:27<27:19, 7.92s/it | [Iter 2883/3090] R0[2793/3000] | LR: 0.000295 | E: -63.162365 | E_var:     3.7367 E_err:   0.030204 | NF_loss: 14.569944
[2025-11-13 07:07:59] 380:35<27:11, 7.92s/it | [Iter 2884/3090] R0[2794/3000] | LR: 0.000293 | E: -63.028852 | E_var:     3.3429 E_err:   0.028568 | NF_loss: 15.097076
[2025-11-13 07:08:06] 380:43<27:03, 7.92s/it | [Iter 2885/3090] R0[2795/3000] | LR: 0.000290 | E: -63.000942 | E_var:     3.5209 E_err:   0.029319 | NF_loss: 12.260631
[2025-11-13 07:08:14] 380:51<26:55, 7.92s/it | [Iter 2886/3090] R0[2796/3000] | LR: 0.000287 | E: -63.034309 | E_var:     3.4631 E_err:   0.029077 | NF_loss: 14.917814
[2025-11-13 07:08:22] 380:58<26:47, 7.92s/it | [Iter 2887/3090] R0[2797/3000] | LR: 0.000284 | E: -63.111030 | E_var:     3.5875 E_err:   0.029595 | NF_loss: 17.256864
[2025-11-13 07:08:30] 381:06<26:39, 7.92s/it | [Iter 2888/3090] R0[2798/3000] | LR: 0.000281 | E: -63.067891 | E_var:     3.4747 E_err:   0.029126 | NF_loss: 19.617183
[2025-11-13 07:08:38] 381:14<26:31, 7.92s/it | [Iter 2889/3090] R0[2799/3000] | LR: 0.000279 | E: -63.081277 | E_var:     3.7564 E_err:   0.030284 | NF_loss: 16.004298
[2025-11-13 07:08:45] 381:22<26:23, 7.92s/it | [Iter 2890/3090] R0[2800/3000] | LR: 0.000276 | E: -63.102332 | E_var:     3.6358 E_err:   0.029793 | NF_loss: 14.845858
[2025-11-13 07:08:53] 381:30<26:15, 7.92s/it | [Iter 2891/3090] R0[2801/3000] | LR: 0.000273 | E: -63.136085 | E_var:     3.4841 E_err:   0.029165 | NF_loss: 15.367792
[2025-11-13 07:09:01] 381:37<26:07, 7.92s/it | [Iter 2892/3090] R0[2802/3000] | LR: 0.000271 | E: -63.125514 | E_var:     3.6612 E_err:   0.029897 | NF_loss: 17.669856
[2025-11-13 07:09:09] 381:45<25:59, 7.92s/it | [Iter 2893/3090] R0[2803/3000] | LR: 0.000268 | E: -63.133347 | E_var:     3.4826 E_err:   0.029159 | NF_loss: 14.882535
[2025-11-13 07:09:17] 381:53<25:51, 7.92s/it | [Iter 2894/3090] R0[2804/3000] | LR: 0.000265 | E: -63.128534 | E_var:     3.5736 E_err:   0.029537 | NF_loss: 13.455486
[2025-11-13 07:09:24] 382:01<25:43, 7.92s/it | [Iter 2895/3090] R0[2805/3000] | LR: 0.000262 | E: -63.109888 | E_var:     3.6070 E_err:   0.029675 | NF_loss: 13.555066
[2025-11-13 07:09:32] 382:09<25:36, 7.92s/it | [Iter 2896/3090] R0[2806/3000] | LR: 0.000260 | E: -63.163481 | E_var:     3.7637 E_err:   0.030313 | NF_loss: 16.422337
[2025-11-13 07:09:40] 382:16<25:28, 7.92s/it | [Iter 2897/3090] R0[2807/3000] | LR: 0.000257 | E: -63.110375 | E_var:     3.5115 E_err:   0.029280 | NF_loss: 14.211102
[2025-11-13 07:09:48] 382:24<25:20, 7.92s/it | [Iter 2898/3090] R0[2808/3000] | LR: 0.000255 | E: -63.139556 | E_var:     3.3926 E_err:   0.028780 | NF_loss: 17.291106
[2025-11-13 07:09:56] 382:32<25:12, 7.92s/it | [Iter 2899/3090] R0[2809/3000] | LR: 0.000252 | E: -63.174682 | E_var:     3.3141 E_err:   0.028445 | NF_loss: 14.231928
[2025-11-13 07:10:03] 382:40<25:04, 7.92s/it | [Iter 2900/3090] R0[2810/3000] | LR: 0.000249 | E: -63.173030 | E_var:     3.4049 E_err:   0.028832 | NF_loss: 22.618022
[2025-11-13 07:10:11] 382:48<24:56, 7.92s/it | [Iter 2901/3090] R0[2811/3000] | LR: 0.000247 | E: -63.127569 | E_var:     3.2219 E_err:   0.028046 | NF_loss: 17.781309
[2025-11-13 07:10:19] 382:55<24:48, 7.92s/it | [Iter 2902/3090] R0[2812/3000] | LR: 0.000244 | E: -63.143274 | E_var:     3.3277 E_err:   0.028503 | NF_loss: 15.590860
[2025-11-13 07:10:27] 383:03<24:40, 7.92s/it | [Iter 2903/3090] R0[2813/3000] | LR: 0.000242 | E: -63.128297 | E_var:     3.4307 E_err:   0.028941 | NF_loss: 16.017930
[2025-11-13 07:10:35] 383:11<24:32, 7.92s/it | [Iter 2904/3090] R0[2814/3000] | LR: 0.000239 | E: -63.168976 | E_var:     3.4681 E_err:   0.029098 | NF_loss: 15.040824
[2025-11-13 07:10:43] 383:19<24:24, 7.92s/it | [Iter 2905/3090] R0[2815/3000] | LR: 0.000236 | E: -63.138837 | E_var:     3.3310 E_err:   0.028517 | NF_loss: 16.868871
[2025-11-13 07:10:50] 383:27<24:16, 7.92s/it | [Iter 2906/3090] R0[2816/3000] | LR: 0.000234 | E: -63.100182 | E_var:     3.5449 E_err:   0.029419 | NF_loss: 15.535953
[2025-11-13 07:10:58] 383:35<24:08, 7.92s/it | [Iter 2907/3090] R0[2817/3000] | LR: 0.000231 | E: -63.058724 | E_var:     3.4288 E_err:   0.028933 | NF_loss: 14.317226
[2025-11-13 07:11:06] 383:42<24:00, 7.92s/it | [Iter 2908/3090] R0[2818/3000] | LR: 0.000229 | E: -63.137837 | E_var:     3.6442 E_err:   0.029828 | NF_loss: 15.229110
[2025-11-13 07:11:14] 383:50<23:52, 7.92s/it | [Iter 2909/3090] R0[2819/3000] | LR: 0.000226 | E: -63.198906 | E_var:     3.3453 E_err:   0.028578 | NF_loss: 13.822149
[2025-11-13 07:11:22] 383:58<23:45, 7.92s/it | [Iter 2910/3090] R0[2820/3000] | LR: 0.000224 | E: -63.126778 | E_var:     3.2127 E_err:   0.028006 | NF_loss: 16.033735
[2025-11-13 07:11:29] 384:06<23:37, 7.92s/it | [Iter 2911/3090] R0[2821/3000] | LR: 0.000222 | E: -63.111601 | E_var:     3.2789 E_err:   0.028293 | NF_loss: 17.077312
[2025-11-13 07:11:37] 384:14<23:29, 7.92s/it | [Iter 2912/3090] R0[2822/3000] | LR: 0.000219 | E: -63.152066 | E_var:     3.2971 E_err:   0.028372 | NF_loss: 14.645721
[2025-11-13 07:11:45] 384:21<23:21, 7.92s/it | [Iter 2913/3090] R0[2823/3000] | LR: 0.000217 | E: -63.146847 | E_var:     3.3925 E_err:   0.028779 | NF_loss: 18.624801
[2025-11-13 07:11:53] 384:29<23:13, 7.92s/it | [Iter 2914/3090] R0[2824/3000] | LR: 0.000214 | E: -63.057635 | E_var:     3.2312 E_err:   0.028087 | NF_loss: 15.606619
[2025-11-13 07:12:01] 384:37<23:05, 7.92s/it | [Iter 2915/3090] R0[2825/3000] | LR: 0.000212 | E: -63.029100 | E_var:     3.4005 E_err:   0.028813 | NF_loss: 16.852738
[2025-11-13 07:12:08] 384:45<22:57, 7.92s/it | [Iter 2916/3090] R0[2826/3000] | LR: 0.000209 | E: -63.055126 | E_var:     3.4965 E_err:   0.029217 | NF_loss: 17.650209
[2025-11-13 07:12:16] 384:53<22:49, 7.92s/it | [Iter 2917/3090] R0[2827/3000] | LR: 0.000207 | E: -62.838106 | E_var:     3.6924 E_err:   0.030024 | NF_loss: 20.443065
[2025-11-13 07:12:24] 385:00<22:41, 7.92s/it | [Iter 2918/3090] R0[2828/3000] | LR: 0.000205 | E: -62.801748 | E_var:     3.7825 E_err:   0.030389 | NF_loss: 19.047019
[2025-11-13 07:12:32] 385:08<22:33, 7.92s/it | [Iter 2919/3090] R0[2829/3000] | LR: 0.000202 | E: -62.838342 | E_var:     3.5439 E_err:   0.029414 | NF_loss: 16.583060
[2025-11-13 07:12:40] 385:16<22:25, 7.92s/it | [Iter 2920/3090] R0[2830/3000] | LR: 0.000200 | E: -62.939215 | E_var:     3.6041 E_err:   0.029663 | NF_loss: 15.693523
[2025-11-13 07:12:47] 385:24<22:17, 7.92s/it | [Iter 2921/3090] R0[2831/3000] | LR: 0.000198 | E: -62.921257 | E_var:     3.5931 E_err:   0.029618 | NF_loss: 17.476127
[2025-11-13 07:12:55] 385:32<22:09, 7.92s/it | [Iter 2922/3090] R0[2832/3000] | LR: 0.000195 | E: -62.756149 | E_var:     3.6880 E_err:   0.030007 | NF_loss: 17.515819
[2025-11-13 07:13:03] 385:39<22:02, 7.92s/it | [Iter 2923/3090] R0[2833/3000] | LR: 0.000193 | E: -62.691608 | E_var:     3.8708 E_err:   0.030741 | NF_loss: 15.578593
[2025-11-13 07:13:11] 385:47<21:54, 7.92s/it | [Iter 2924/3090] R0[2834/3000] | LR: 0.000191 | E: -62.733680 | E_var:     3.8782 E_err:   0.030770 | NF_loss: 16.564687
[2025-11-13 07:13:19] 385:55<21:46, 7.92s/it | [Iter 2925/3090] R0[2835/3000] | LR: 0.000188 | E: -63.011103 | E_var:     3.3818 E_err:   0.028734 | NF_loss: 16.398649
[2025-11-13 07:13:26] 386:03<21:38, 7.92s/it | [Iter 2926/3090] R0[2836/3000] | LR: 0.000186 | E: -62.973321 | E_var:     3.3435 E_err:   0.028571 | NF_loss: 16.811369
[2025-11-13 07:13:34] 386:11<21:30, 7.92s/it | [Iter 2927/3090] R0[2837/3000] | LR: 0.000184 | E: -63.038019 | E_var:     3.5024 E_err:   0.029242 | NF_loss: 16.152709
[2025-11-13 07:13:42] 386:18<21:22, 7.92s/it | [Iter 2928/3090] R0[2838/3000] | LR: 0.000182 | E: -62.967600 | E_var:     3.4140 E_err:   0.028870 | NF_loss: 16.008072
[2025-11-13 07:13:50] 386:26<21:14, 7.92s/it | [Iter 2929/3090] R0[2839/3000] | LR: 0.000180 | E: -63.021121 | E_var:     3.4347 E_err:   0.028958 | NF_loss: 11.980060
[2025-11-13 07:13:58] 386:34<21:06, 7.92s/it | [Iter 2930/3090] R0[2840/3000] | LR: 0.000177 | E: -63.036433 | E_var:     3.3966 E_err:   0.028797 | NF_loss: 15.135274
[2025-11-13 07:14:05] 386:42<20:58, 7.92s/it | [Iter 2931/3090] R0[2841/3000] | LR: 0.000175 | E: -63.025288 | E_var:     3.4912 E_err:   0.029195 | NF_loss: 13.644212
[2025-11-13 07:14:13] 386:50<20:50, 7.92s/it | [Iter 2932/3090] R0[2842/3000] | LR: 0.000173 | E: -63.042630 | E_var:     3.5258 E_err:   0.029339 | NF_loss: 12.925421
[2025-11-13 07:14:21] 386:57<20:42, 7.92s/it | [Iter 2933/3090] R0[2843/3000] | LR: 0.000171 | E: -62.962639 | E_var:     3.4199 E_err:   0.028895 | NF_loss: 14.976000
[2025-11-13 07:14:29] 387:05<20:34, 7.92s/it | [Iter 2934/3090] R0[2844/3000] | LR: 0.000169 | E: -62.935760 | E_var:     3.5438 E_err:   0.029414 | NF_loss: 14.025344
[2025-11-13 07:14:37] 387:13<20:26, 7.92s/it | [Iter 2935/3090] R0[2845/3000] | LR: 0.000167 | E: -62.847348 | E_var:     3.5957 E_err:   0.029629 | NF_loss: 13.991815
[2025-11-13 07:14:44] 387:21<20:19, 7.92s/it | [Iter 2936/3090] R0[2846/3000] | LR: 0.000164 | E: -62.882683 | E_var:     3.6370 E_err:   0.029798 | NF_loss: 14.970427
[2025-11-13 07:14:52] 387:29<20:11, 7.92s/it | [Iter 2937/3090] R0[2847/3000] | LR: 0.000162 | E: -62.871512 | E_var:     3.5331 E_err:   0.029370 | NF_loss: 13.420808
[2025-11-13 07:15:00] 387:36<20:03, 7.92s/it | [Iter 2938/3090] R0[2848/3000] | LR: 0.000160 | E: -62.773999 | E_var:     3.7784 E_err:   0.030372 | NF_loss: 15.642439
[2025-11-13 07:15:08] 387:44<19:55, 7.92s/it | [Iter 2939/3090] R0[2849/3000] | LR: 0.000158 | E: -62.733731 | E_var:     3.7457 E_err:   0.030240 | NF_loss: 17.477436
[2025-11-13 07:15:16] 387:52<19:47, 7.92s/it | [Iter 2940/3090] R0[2850/3000] | LR: 0.000156 | E: -62.571489 | E_var:     4.1318 E_err:   0.031761 | NF_loss: 16.467299
[2025-11-13 07:15:23] 388:00<19:39, 7.92s/it | [Iter 2941/3090] R0[2851/3000] | LR: 0.000154 | E: -62.739163 | E_var:     3.7878 E_err:   0.030410 | NF_loss: 13.354077
[2025-11-13 07:15:31] 388:08<19:31, 7.92s/it | [Iter 2942/3090] R0[2852/3000] | LR: 0.000152 | E: -62.843830 | E_var:     3.8111 E_err:   0.030503 | NF_loss: 15.696820
[2025-11-13 07:15:39] 388:16<19:23, 7.92s/it | [Iter 2943/3090] R0[2853/3000] | LR: 0.000150 | E: -62.643821 | E_var:     3.8274 E_err:   0.030568 | NF_loss: 14.257555
[2025-11-13 07:15:47] 388:23<19:15, 7.92s/it | [Iter 2944/3090] R0[2854/3000] | LR: 0.000148 | E: -62.681447 | E_var:     3.8352 E_err:   0.030599 | NF_loss: 15.264744
[2025-11-13 07:15:55] 388:31<19:07, 7.92s/it | [Iter 2945/3090] R0[2855/3000] | LR: 0.000146 | E: -62.800250 | E_var:     3.6553 E_err:   0.029873 | NF_loss: 19.455410
[2025-11-13 07:16:03] 388:39<18:59, 7.92s/it | [Iter 2946/3090] R0[2856/3000] | LR: 0.000144 | E: -62.778987 | E_var:     3.5345 E_err:   0.029376 | NF_loss: 16.975265
[2025-11-13 07:16:10] 388:47<18:51, 7.92s/it | [Iter 2947/3090] R0[2857/3000] | LR: 0.000142 | E: -62.723748 | E_var:     3.5540 E_err:   0.029456 | NF_loss: 14.158228
[2025-11-13 07:16:18] 388:55<18:44, 7.92s/it | [Iter 2948/3090] R0[2858/3000] | LR: 0.000140 | E: -62.923224 | E_var:     3.4957 E_err:   0.029214 | NF_loss: 15.235911
[2025-11-13 07:16:26] 389:02<18:36, 7.92s/it | [Iter 2949/3090] R0[2859/3000] | LR: 0.000138 | E: -62.804840 | E_var:     3.4750 E_err:   0.029127 | NF_loss: 12.365797
[2025-11-13 07:16:34] 389:10<18:28, 7.92s/it | [Iter 2950/3090] R0[2860/3000] | LR: 0.000136 | E: -62.923770 | E_var:     3.5420 E_err:   0.029406 | NF_loss: 21.445319
[2025-11-13 07:16:42] 389:18<18:20, 7.92s/it | [Iter 2951/3090] R0[2861/3000] | LR: 0.000134 | E: -63.022238 | E_var:     3.4877 E_err:   0.029180 | NF_loss: 15.622393
[2025-11-13 07:16:49] 389:26<18:12, 7.92s/it | [Iter 2952/3090] R0[2862/3000] | LR: 0.000132 | E: -62.973533 | E_var:     3.4660 E_err:   0.029089 | NF_loss: 12.763381
[2025-11-13 07:16:57] 389:34<18:04, 7.92s/it | [Iter 2953/3090] R0[2863/3000] | LR: 0.000130 | E: -62.945835 | E_var:     3.6659 E_err:   0.029917 | NF_loss: 14.923685
[2025-11-13 07:17:05] 389:42<17:56, 7.92s/it | [Iter 2954/3090] R0[2864/3000] | LR: 0.000129 | E: -62.943821 | E_var:     3.5658 E_err:   0.029505 | NF_loss: 14.952963
[2025-11-13 07:17:13] 389:49<17:48, 7.92s/it | [Iter 2955/3090] R0[2865/3000] | LR: 0.000127 | E: -62.945102 | E_var:     3.5246 E_err:   0.029334 | NF_loss: 19.520555
[2025-11-13 07:17:21] 389:57<17:40, 7.92s/it | [Iter 2956/3090] R0[2866/3000] | LR: 0.000125 | E: -63.062552 | E_var:     3.6503 E_err:   0.029853 | NF_loss: 18.203641
[2025-11-13 07:17:28] 390:05<17:32, 7.92s/it | [Iter 2957/3090] R0[2867/3000] | LR: 0.000123 | E: -63.052215 | E_var:     3.8007 E_err:   0.030462 | NF_loss: 15.175613
[2025-11-13 07:17:36] 390:13<17:24, 7.92s/it | [Iter 2958/3090] R0[2868/3000] | LR: 0.000121 | E: -63.051938 | E_var:     4.1997 E_err:   0.032021 | NF_loss: 18.132257
[2025-11-13 07:17:44] 390:20<17:16, 7.92s/it | [Iter 2959/3090] R0[2869/3000] | LR: 0.000119 | E: -62.965428 | E_var:     4.0682 E_err:   0.031515 | NF_loss: 14.137386
[2025-11-13 07:17:52] 390:28<17:08, 7.92s/it | [Iter 2960/3090] R0[2870/3000] | LR: 0.000118 | E: -62.837248 | E_var:     3.6528 E_err:   0.029863 | NF_loss: 16.332360
[2025-11-13 07:18:00] 390:36<17:01, 7.92s/it | [Iter 2961/3090] R0[2871/3000] | LR: 0.000116 | E: -62.996356 | E_var:     3.6292 E_err:   0.029766 | NF_loss: 14.275310
[2025-11-13 07:18:07] 390:44<16:53, 7.92s/it | [Iter 2962/3090] R0[2872/3000] | LR: 0.000114 | E: -63.023253 | E_var:     3.6675 E_err:   0.029923 | NF_loss: 15.605106
[2025-11-13 07:18:15] 390:52<16:45, 7.92s/it | [Iter 2963/3090] R0[2873/3000] | LR: 0.000112 | E: -63.025395 | E_var:     3.6145 E_err:   0.029706 | NF_loss: 17.968516
[2025-11-13 07:18:23] 391:00<16:37, 7.91s/it | [Iter 2964/3090] R0[2874/3000] | LR: 0.000110 | E: -63.075124 | E_var:     3.4640 E_err:   0.029081 | NF_loss: 16.372382
[2025-11-13 07:18:31] 391:07<16:29, 7.91s/it | [Iter 2965/3090] R0[2875/3000] | LR: 0.000109 | E: -63.053912 | E_var:     3.4618 E_err:   0.029072 | NF_loss: 14.762063
[2025-11-13 07:18:39] 391:15<16:21, 7.91s/it | [Iter 2966/3090] R0[2876/3000] | LR: 0.000107 | E: -63.054552 | E_var:     3.6306 E_err:   0.029772 | NF_loss: 15.755125
[2025-11-13 07:18:46] 391:23<16:13, 7.91s/it | [Iter 2967/3090] R0[2877/3000] | LR: 0.000105 | E: -63.111194 | E_var:     3.6156 E_err:   0.029710 | NF_loss: 20.024366
[2025-11-13 07:18:54] 391:31<16:05, 7.91s/it | [Iter 2968/3090] R0[2878/3000] | LR: 0.000104 | E: -63.049762 | E_var:     3.8233 E_err:   0.030552 | NF_loss: 16.620985
[2025-11-13 07:19:02] 391:38<15:57, 7.91s/it | [Iter 2969/3090] R0[2879/3000] | LR: 0.000102 | E: -63.063517 | E_var:     3.8068 E_err:   0.030486 | NF_loss: 22.581270
[2025-11-13 07:19:10] 391:46<15:49, 7.91s/it | [Iter 2970/3090] R0[2880/3000] | LR: 0.000100 | E: -63.040209 | E_var:     3.7354 E_err:   0.030199 | NF_loss: 17.507005
[2025-11-13 07:19:18] 391:54<15:41, 7.91s/it | [Iter 2971/3090] R0[2881/3000] | LR: 0.000099 | E: -63.103263 | E_var:     3.7275 E_err:   0.030167 | NF_loss: 14.835188
[2025-11-13 07:19:25] 392:02<15:33, 7.91s/it | [Iter 2972/3090] R0[2882/3000] | LR: 0.000097 | E: -63.098082 | E_var:     3.9791 E_err:   0.031168 | NF_loss: 18.824757
[2025-11-13 07:19:33] 392:10<15:26, 7.91s/it | [Iter 2973/3090] R0[2883/3000] | LR: 0.000095 | E: -63.121682 | E_var:     3.6919 E_err:   0.030022 | NF_loss: 19.254811
[2025-11-13 07:19:41] 392:18<15:18, 7.91s/it | [Iter 2974/3090] R0[2884/3000] | LR: 0.000094 | E: -63.141028 | E_var:     3.4482 E_err:   0.029015 | NF_loss: 14.920893
[2025-11-13 07:19:49] 392:25<15:10, 7.91s/it | [Iter 2975/3090] R0[2885/3000] | LR: 0.000092 | E: -63.133292 | E_var:     4.1382 E_err:   0.031785 | NF_loss: 14.225704
[2025-11-13 07:19:57] 392:33<15:02, 7.91s/it | [Iter 2976/3090] R0[2886/3000] | LR: 0.000091 | E: -63.165566 | E_var:     3.7731 E_err:   0.030351 | NF_loss: 17.093520
[2025-11-13 07:20:05] 392:41<14:54, 7.91s/it | [Iter 2977/3090] R0[2887/3000] | LR: 0.000089 | E: -63.119731 | E_var:     3.6267 E_err:   0.029756 | NF_loss: 20.639245
[2025-11-13 07:20:12] 392:49<14:46, 7.91s/it | [Iter 2978/3090] R0[2888/3000] | LR: 0.000088 | E: -63.141062 | E_var:     3.5828 E_err:   0.029575 | NF_loss: 13.634154
[2025-11-13 07:20:20] 392:57<14:38, 7.91s/it | [Iter 2979/3090] R0[2889/3000] | LR: 0.000086 | E: -63.098294 | E_var:     3.8344 E_err:   0.030596 | NF_loss: 16.276346
[2025-11-13 07:20:28] 393:04<14:30, 7.91s/it | [Iter 2980/3090] R0[2890/3000] | LR: 0.000084 | E: -63.122899 | E_var:     3.7469 E_err:   0.030245 | NF_loss: 19.203967
[2025-11-13 07:20:36] 393:12<14:22, 7.91s/it | [Iter 2981/3090] R0[2891/3000] | LR: 0.000083 | E: -63.222987 | E_var:     3.9945 E_err:   0.031228 | NF_loss: 17.075034
[2025-11-13 07:20:44] 393:20<14:14, 7.91s/it | [Iter 2982/3090] R0[2892/3000] | LR: 0.000081 | E: -63.100605 | E_var:     3.8376 E_err:   0.030609 | NF_loss: 16.826235
[2025-11-13 07:20:51] 393:28<14:06, 7.91s/it | [Iter 2983/3090] R0[2893/3000] | LR: 0.000080 | E: -63.103898 | E_var:     3.4202 E_err:   0.028896 | NF_loss: 15.659611
[2025-11-13 07:20:59] 393:36<13:58, 7.91s/it | [Iter 2984/3090] R0[2894/3000] | LR: 0.000078 | E: -63.168217 | E_var:     3.5583 E_err:   0.029474 | NF_loss: 16.108263
[2025-11-13 07:21:07] 393:43<13:50, 7.91s/it | [Iter 2985/3090] R0[2895/3000] | LR: 0.000077 | E: -63.186738 | E_var:     3.6930 E_err:   0.030027 | NF_loss: 19.009090
[2025-11-13 07:21:15] 393:51<13:43, 7.91s/it | [Iter 2986/3090] R0[2896/3000] | LR: 0.000076 | E: -63.180648 | E_var:     4.1377 E_err:   0.031783 | NF_loss: 15.312264
[2025-11-13 07:21:23] 393:59<13:35, 7.91s/it | [Iter 2987/3090] R0[2897/3000] | LR: 0.000074 | E: -63.187511 | E_var:     4.1142 E_err:   0.031693 | NF_loss: 15.521850
[2025-11-13 07:21:30] 394:07<13:27, 7.91s/it | [Iter 2988/3090] R0[2898/3000] | LR: 0.000073 | E: -63.156482 | E_var:     3.9803 E_err:   0.031173 | NF_loss: 18.424756
[2025-11-13 07:21:38] 394:15<13:19, 7.91s/it | [Iter 2989/3090] R0[2899/3000] | LR: 0.000071 | E: -63.139176 | E_var:     3.6312 E_err:   0.029774 | NF_loss: 19.012166
[2025-11-13 07:21:46] 394:22<13:11, 7.91s/it | [Iter 2990/3090] R0[2900/3000] | LR: 0.000070 | E: -63.146858 | E_var:     4.1886 E_err:   0.031978 | NF_loss: 17.873180
[2025-11-13 07:21:54] 394:30<13:03, 7.91s/it | [Iter 2991/3090] R0[2901/3000] | LR: 0.000069 | E: -63.127091 | E_var:     3.8210 E_err:   0.030543 | NF_loss: 19.969718
[2025-11-13 07:22:02] 394:38<12:55, 7.91s/it | [Iter 2992/3090] R0[2902/3000] | LR: 0.000067 | E: -63.214094 | E_var:     3.6628 E_err:   0.029904 | NF_loss: 19.943036
[2025-11-13 07:22:09] 394:46<12:47, 7.91s/it | [Iter 2993/3090] R0[2903/3000] | LR: 0.000066 | E: -63.158599 | E_var:     3.8045 E_err:   0.030477 | NF_loss: 15.933561
[2025-11-13 07:22:17] 394:54<12:39, 7.91s/it | [Iter 2994/3090] R0[2904/3000] | LR: 0.000065 | E: -63.169375 | E_var:     3.5269 E_err:   0.029344 | NF_loss: 15.604912
[2025-11-13 07:22:25] 395:01<12:31, 7.91s/it | [Iter 2995/3090] R0[2905/3000] | LR: 0.000063 | E: -63.223686 | E_var:     3.3175 E_err:   0.028460 | NF_loss: 15.046050
[2025-11-13 07:22:33] 395:09<12:23, 7.91s/it | [Iter 2996/3090] R0[2906/3000] | LR: 0.000062 | E: -63.148208 | E_var:     3.4904 E_err:   0.029192 | NF_loss: 13.108032
[2025-11-13 07:22:41] 395:17<12:15, 7.91s/it | [Iter 2997/3090] R0[2907/3000] | LR: 0.000061 | E: -63.143874 | E_var:     3.3097 E_err:   0.028426 | NF_loss: 16.305673
[2025-11-13 07:22:48] 395:25<12:08, 7.91s/it | [Iter 2998/3090] R0[2908/3000] | LR: 0.000059 | E: -63.132480 | E_var:     3.4196 E_err:   0.028894 | NF_loss: 13.967211
[2025-11-13 07:22:56] 395:33<12:00, 7.91s/it | [Iter 2999/3090] R0[2909/3000] | LR: 0.000058 | E: -63.151753 | E_var:     3.5541 E_err:   0.029457 | NF_loss: 15.817351
[2025-11-13 07:23:04] 395:40<11:52, 7.91s/it | [Iter 3000/3090] R0[2910/3000] | LR: 0.000057 | E: -63.129844 | E_var:     3.4274 E_err:   0.028927 | NF_loss: 15.158117
[2025-11-13 07:23:04] 保存checkpoint: hybrid_checkpoint_iter_003000.pkl
[2025-11-13 07:23:12] 395:49<11:44, 7.91s/it | [Iter 3001/3090] R0[2911/3000] | LR: 0.000056 | E: -63.145682 | E_var:     3.5790 E_err:   0.029560 | NF_loss: 12.544111
[2025-11-13 07:23:20] 395:57<11:36, 7.91s/it | [Iter 3002/3090] R0[2912/3000] | LR: 0.000054 | E: -63.139858 | E_var:     3.4089 E_err:   0.028849 | NF_loss: 15.289555
[2025-11-13 07:23:28] 396:04<11:28, 7.91s/it | [Iter 3003/3090] R0[2913/3000] | LR: 0.000053 | E: -63.176894 | E_var:     3.5517 E_err:   0.029447 | NF_loss: 11.910293
[2025-11-13 07:23:36] 396:12<11:20, 7.91s/it | [Iter 3004/3090] R0[2914/3000] | LR: 0.000052 | E: -63.181849 | E_var:     3.3700 E_err:   0.028684 | NF_loss: 15.779428
[2025-11-13 07:23:43] 396:20<11:12, 7.91s/it | [Iter 3005/3090] R0[2915/3000] | LR: 0.000051 | E: -63.187574 | E_var:     3.4879 E_err:   0.029181 | NF_loss: 12.677995
[2025-11-13 07:23:51] 396:28<11:04, 7.91s/it | [Iter 3006/3090] R0[2916/3000] | LR: 0.000050 | E: -63.107159 | E_var:     3.3128 E_err:   0.028439 | NF_loss: 14.915097
[2025-11-13 07:23:59] 396:36<10:56, 7.91s/it | [Iter 3007/3090] R0[2917/3000] | LR: 0.000048 | E: -63.141277 | E_var:     3.6259 E_err:   0.029753 | NF_loss: 15.522418
[2025-11-13 07:24:07] 396:43<10:48, 7.91s/it | [Iter 3008/3090] R0[2918/3000] | LR: 0.000047 | E: -63.115956 | E_var:     3.6903 E_err:   0.030016 | NF_loss: 15.713539
[2025-11-13 07:24:15] 396:51<10:40, 7.91s/it | [Iter 3009/3090] R0[2919/3000] | LR: 0.000046 | E: -63.163841 | E_var:     3.5430 E_err:   0.029411 | NF_loss: 13.379425
[2025-11-13 07:24:22] 396:59<10:33, 7.91s/it | [Iter 3010/3090] R0[2920/3000] | LR: 0.000045 | E: -63.155768 | E_var:     3.2670 E_err:   0.028242 | NF_loss: 14.596180
[2025-11-13 07:24:30] 397:07<10:25, 7.91s/it | [Iter 3011/3090] R0[2921/3000] | LR: 0.000044 | E: -63.110712 | E_var:     3.3377 E_err:   0.028546 | NF_loss: 14.450151
[2025-11-13 07:24:38] 397:15<10:17, 7.91s/it | [Iter 3012/3090] R0[2922/3000] | LR: 0.000043 | E: -63.055404 | E_var:     3.9683 E_err:   0.031126 | NF_loss: 16.122708
[2025-11-13 07:24:46] 397:22<10:09, 7.91s/it | [Iter 3013/3090] R0[2923/3000] | LR: 0.000042 | E: -63.055055 | E_var:     3.7903 E_err:   0.030420 | NF_loss: 16.892135
[2025-11-13 07:24:54] 397:30<10:01, 7.91s/it | [Iter 3014/3090] R0[2924/3000] | LR: 0.000041 | E: -62.941944 | E_var:     3.7190 E_err:   0.030132 | NF_loss: 18.108341
[2025-11-13 07:25:01] 397:38<9:53, 7.91s/it | [Iter 3015/3090] R0[2925/3000] | LR: 0.000040 | E: -62.966487 | E_var:     3.7704 E_err:   0.030340 | NF_loss: 18.315902
[2025-11-13 07:25:09] 397:46<9:45, 7.91s/it | [Iter 3016/3090] R0[2926/3000] | LR: 0.000039 | E: -62.882769 | E_var:     3.6433 E_err:   0.029824 | NF_loss: 17.779702
[2025-11-13 07:25:17] 397:54<9:37, 7.91s/it | [Iter 3017/3090] R0[2927/3000] | LR: 0.000038 | E: -63.034158 | E_var:     3.7467 E_err:   0.030244 | NF_loss: 15.810338
[2025-11-13 07:25:25] 398:01<9:29, 7.91s/it | [Iter 3018/3090] R0[2928/3000] | LR: 0.000037 | E: -63.122061 | E_var:     3.4978 E_err:   0.029222 | NF_loss: 17.247013
[2025-11-13 07:25:33] 398:09<9:21, 7.91s/it | [Iter 3019/3090] R0[2929/3000] | LR: 0.000036 | E: -63.151102 | E_var:     3.4954 E_err:   0.029212 | NF_loss: 16.535824
[2025-11-13 07:25:40] 398:17<9:13, 7.91s/it | [Iter 3020/3090] R0[2930/3000] | LR: 0.000035 | E: -63.151466 | E_var:     3.4111 E_err:   0.028858 | NF_loss: 17.861600
[2025-11-13 07:25:48] 398:25<9:05, 7.91s/it | [Iter 3021/3090] R0[2931/3000] | LR: 0.000034 | E: -63.156751 | E_var:     3.2627 E_err:   0.028223 | NF_loss: 20.589531
[2025-11-13 07:25:56] 398:33<8:58, 7.91s/it | [Iter 3022/3090] R0[2932/3000] | LR: 0.000033 | E: -63.213340 | E_var:     3.2218 E_err:   0.028046 | NF_loss: 14.565569
[2025-11-13 07:26:04] 398:40<8:50, 7.91s/it | [Iter 3023/3090] R0[2933/3000] | LR: 0.000032 | E: -63.192162 | E_var:     3.4186 E_err:   0.028890 | NF_loss: 16.359296
[2025-11-13 07:26:12] 398:48<8:42, 7.91s/it | [Iter 3024/3090] R0[2934/3000] | LR: 0.000031 | E: -63.195637 | E_var:     3.6923 E_err:   0.030024 | NF_loss: 17.863364
[2025-11-13 07:26:19] 398:56<8:34, 7.91s/it | [Iter 3025/3090] R0[2935/3000] | LR: 0.000030 | E: -63.148546 | E_var:     3.3473 E_err:   0.028587 | NF_loss: 22.322510
[2025-11-13 07:26:27] 399:04<8:26, 7.91s/it | [Iter 3026/3090] R0[2936/3000] | LR: 0.000029 | E: -63.120658 | E_var:     3.2937 E_err:   0.028357 | NF_loss: 17.734852
[2025-11-13 07:26:35] 399:12<8:18, 7.91s/it | [Iter 3027/3090] R0[2937/3000] | LR: 0.000028 | E: -63.019952 | E_var:     3.8018 E_err:   0.030466 | NF_loss: 19.330652
[2025-11-13 07:26:43] 399:19<8:10, 7.91s/it | [Iter 3028/3090] R0[2938/3000] | LR: 0.000027 | E: -63.024936 | E_var:     3.6957 E_err:   0.030038 | NF_loss: 20.416509
[2025-11-13 07:26:51] 399:27<8:02, 7.91s/it | [Iter 3029/3090] R0[2939/3000] | LR: 0.000026 | E: -63.088846 | E_var:     3.8815 E_err:   0.030784 | NF_loss: 18.875738
[2025-11-13 07:26:58] 399:35<7:54, 7.91s/it | [Iter 3030/3090] R0[2940/3000] | LR: 0.000026 | E: -62.926157 | E_var:     3.8278 E_err:   0.030570 | NF_loss: 17.338875
[2025-11-13 07:27:06] 399:43<7:46, 7.91s/it | [Iter 3031/3090] R0[2941/3000] | LR: 0.000025 | E: -63.069940 | E_var:     3.6733 E_err:   0.029947 | NF_loss: 16.843789
[2025-11-13 07:27:14] 399:51<7:38, 7.91s/it | [Iter 3032/3090] R0[2942/3000] | LR: 0.000024 | E: -63.187815 | E_var:     3.6692 E_err:   0.029930 | NF_loss: 16.175538
[2025-11-13 07:27:22] 399:58<7:31, 7.91s/it | [Iter 3033/3090] R0[2943/3000] | LR: 0.000023 | E: -63.159263 | E_var:     3.4041 E_err:   0.028828 | NF_loss: 15.228876
[2025-11-13 07:27:30] 400:06<7:23, 7.91s/it | [Iter 3034/3090] R0[2944/3000] | LR: 0.000022 | E: -63.110965 | E_var:     3.8707 E_err:   0.030741 | NF_loss: 18.055927
[2025-11-13 07:27:37] 400:14<7:15, 7.91s/it | [Iter 3035/3090] R0[2945/3000] | LR: 0.000022 | E: -63.212770 | E_var:     3.2908 E_err:   0.028345 | NF_loss: 16.186104
[2025-11-13 07:27:45] 400:22<7:07, 7.91s/it | [Iter 3036/3090] R0[2946/3000] | LR: 0.000021 | E: -63.157016 | E_var:     3.5591 E_err:   0.029477 | NF_loss: 21.389591
[2025-11-13 07:27:53] 400:30<6:59, 7.91s/it | [Iter 3037/3090] R0[2947/3000] | LR: 0.000020 | E: -63.177064 | E_var:     3.5651 E_err:   0.029502 | NF_loss: 14.339643
[2025-11-13 07:28:01] 400:37<6:51, 7.91s/it | [Iter 3038/3090] R0[2948/3000] | LR: 0.000019 | E: -63.207786 | E_var:     3.5176 E_err:   0.029305 | NF_loss: 15.767698
[2025-11-13 07:28:09] 400:45<6:43, 7.91s/it | [Iter 3039/3090] R0[2949/3000] | LR: 0.000019 | E: -63.150337 | E_var:     3.6330 E_err:   0.029782 | NF_loss: 16.807547
[2025-11-13 07:28:17] 400:53<6:35, 7.91s/it | [Iter 3040/3090] R0[2950/3000] | LR: 0.000018 | E: -63.056691 | E_var:     3.5184 E_err:   0.029308 | NF_loss: 16.299138
[2025-11-13 07:28:24] 401:01<6:27, 7.91s/it | [Iter 3041/3090] R0[2951/3000] | LR: 0.000017 | E: -63.176451 | E_var:     3.7980 E_err:   0.030451 | NF_loss: 15.127726
[2025-11-13 07:28:32] 401:09<6:19, 7.91s/it | [Iter 3042/3090] R0[2952/3000] | LR: 0.000017 | E: -63.082780 | E_var:     3.6574 E_err:   0.029882 | NF_loss: 19.054165
[2025-11-13 07:28:40] 401:17<6:11, 7.91s/it | [Iter 3043/3090] R0[2953/3000] | LR: 0.000016 | E: -63.025118 | E_var:     3.6770 E_err:   0.029962 | NF_loss: 16.550922
[2025-11-13 07:28:48] 401:24<6:03, 7.91s/it | [Iter 3044/3090] R0[2954/3000] | LR: 0.000015 | E: -63.043032 | E_var:     3.7409 E_err:   0.030221 | NF_loss: 14.751707
[2025-11-13 07:28:56] 401:32<5:56, 7.91s/it | [Iter 3045/3090] R0[2955/3000] | LR: 0.000015 | E: -63.063544 | E_var:     3.5661 E_err:   0.029507 | NF_loss: 15.712396
[2025-11-13 07:29:03] 401:40<5:48, 7.91s/it | [Iter 3046/3090] R0[2956/3000] | LR: 0.000014 | E: -63.117182 | E_var:     3.5261 E_err:   0.029341 | NF_loss: 14.137173
[2025-11-13 07:29:11] 401:48<5:40, 7.91s/it | [Iter 3047/3090] R0[2957/3000] | LR: 0.000013 | E: -63.173244 | E_var:     3.5905 E_err:   0.029607 | NF_loss: 14.943114
[2025-11-13 07:29:19] 401:56<5:32, 7.91s/it | [Iter 3048/3090] R0[2958/3000] | LR: 0.000013 | E: -63.166875 | E_var:     3.8020 E_err:   0.030467 | NF_loss: 15.656151
[2025-11-13 07:29:27] 402:03<5:24, 7.91s/it | [Iter 3049/3090] R0[2959/3000] | LR: 0.000012 | E: -63.200430 | E_var:     3.6022 E_err:   0.029655 | NF_loss: 13.547811
[2025-11-13 07:29:35] 402:11<5:16, 7.91s/it | [Iter 3050/3090] R0[2960/3000] | LR: 0.000012 | E: -63.167578 | E_var:     3.5810 E_err:   0.029568 | NF_loss: 13.741762
[2025-11-13 07:29:42] 402:19<5:08, 7.91s/it | [Iter 3051/3090] R0[2961/3000] | LR: 0.000011 | E: -63.197666 | E_var:     3.4757 E_err:   0.029130 | NF_loss: 15.379936
[2025-11-13 07:29:50] 402:27<5:00, 7.91s/it | [Iter 3052/3090] R0[2962/3000] | LR: 0.000011 | E: -63.201013 | E_var:     3.4793 E_err:   0.029145 | NF_loss: 13.585312
[2025-11-13 07:29:58] 402:35<4:52, 7.91s/it | [Iter 3053/3090] R0[2963/3000] | LR: 0.000010 | E: -63.162360 | E_var:     3.4714 E_err:   0.029112 | NF_loss: 13.268469
[2025-11-13 07:30:06] 402:42<4:44, 7.91s/it | [Iter 3054/3090] R0[2964/3000] | LR: 0.000009 | E: -63.120851 | E_var:     3.5395 E_err:   0.029396 | NF_loss: 15.590027
[2025-11-13 07:30:14] 402:50<4:36, 7.91s/it | [Iter 3055/3090] R0[2965/3000] | LR: 0.000009 | E: -63.107617 | E_var:     3.4911 E_err:   0.029194 | NF_loss: 13.515574
[2025-11-13 07:30:21] 402:58<4:29, 7.91s/it | [Iter 3056/3090] R0[2966/3000] | LR: 0.000008 | E: -63.017816 | E_var:     3.4158 E_err:   0.028878 | NF_loss: 14.995012
[2025-11-13 07:30:29] 403:06<4:21, 7.91s/it | [Iter 3057/3090] R0[2967/3000] | LR: 0.000008 | E: -63.105583 | E_var:     3.5452 E_err:   0.029420 | NF_loss: 14.037927
[2025-11-13 07:30:37] 403:14<4:13, 7.91s/it | [Iter 3058/3090] R0[2968/3000] | LR: 0.000008 | E: -63.025840 | E_var:     3.3378 E_err:   0.028546 | NF_loss: 14.270842
[2025-11-13 07:30:45] 403:21<4:05, 7.91s/it | [Iter 3059/3090] R0[2969/3000] | LR: 0.000007 | E: -63.049498 | E_var:     3.5029 E_err:   0.029244 | NF_loss: 12.935394
[2025-11-13 07:30:53] 403:29<3:57, 7.91s/it | [Iter 3060/3090] R0[2970/3000] | LR: 0.000007 | E: -62.953758 | E_var:     3.3495 E_err:   0.028596 | NF_loss: 14.046406
[2025-11-13 07:31:00] 403:37<3:49, 7.91s/it | [Iter 3061/3090] R0[2971/3000] | LR: 0.000006 | E: -62.860534 | E_var:     3.5552 E_err:   0.029461 | NF_loss: 12.616029
[2025-11-13 07:31:08] 403:45<3:41, 7.91s/it | [Iter 3062/3090] R0[2972/3000] | LR: 0.000006 | E: -62.939894 | E_var:     3.4281 E_err:   0.028930 | NF_loss: 12.772673
[2025-11-13 07:31:16] 403:53<3:33, 7.91s/it | [Iter 3063/3090] R0[2973/3000] | LR: 0.000005 | E: -63.114807 | E_var:     3.4196 E_err:   0.028894 | NF_loss: 14.909299
[2025-11-13 07:31:24] 404:00<3:25, 7.91s/it | [Iter 3064/3090] R0[2974/3000] | LR: 0.000005 | E: -63.130895 | E_var:     3.4473 E_err:   0.029011 | NF_loss: 12.343633
[2025-11-13 07:31:32] 404:08<3:17, 7.91s/it | [Iter 3065/3090] R0[2975/3000] | LR: 0.000005 | E: -63.057844 | E_var:     3.4500 E_err:   0.029022 | NF_loss: 13.319975
[2025-11-13 07:31:39] 404:16<3:09, 7.91s/it | [Iter 3066/3090] R0[2976/3000] | LR: 0.000004 | E: -63.145025 | E_var:     3.7344 E_err:   0.030195 | NF_loss: 17.947558
[2025-11-13 07:31:47] 404:24<3:01, 7.91s/it | [Iter 3067/3090] R0[2977/3000] | LR: 0.000004 | E: -63.151798 | E_var:     4.1650 E_err:   0.031888 | NF_loss: 10.884800
[2025-11-13 07:31:55] 404:32<2:54, 7.91s/it | [Iter 3068/3090] R0[2978/3000] | LR: 0.000004 | E: -63.140655 | E_var:     4.1332 E_err:   0.031766 | NF_loss: 13.362146
[2025-11-13 07:32:03] 404:39<2:46, 7.91s/it | [Iter 3069/3090] R0[2979/3000] | LR: 0.000003 | E: -63.146972 | E_var:     4.1926 E_err:   0.031994 | NF_loss: 12.687139
[2025-11-13 07:32:11] 404:47<2:38, 7.91s/it | [Iter 3070/3090] R0[2980/3000] | LR: 0.000003 | E: -63.080635 | E_var:     3.8475 E_err:   0.030648 | NF_loss: 17.795762
[2025-11-13 07:32:18] 404:55<2:30, 7.91s/it | [Iter 3071/3090] R0[2981/3000] | LR: 0.000003 | E: -63.140747 | E_var:     3.6153 E_err:   0.029709 | NF_loss: 10.915314
[2025-11-13 07:32:26] 405:03<2:22, 7.91s/it | [Iter 3072/3090] R0[2982/3000] | LR: 0.000003 | E: -63.156226 | E_var:     3.4488 E_err:   0.029017 | NF_loss: 17.176956
[2025-11-13 07:32:34] 405:11<2:14, 7.91s/it | [Iter 3073/3090] R0[2983/3000] | LR: 0.000002 | E: -63.145972 | E_var:     3.8980 E_err:   0.030849 | NF_loss: 13.936641
[2025-11-13 07:32:42] 405:19<2:06, 7.91s/it | [Iter 3074/3090] R0[2984/3000] | LR: 0.000002 | E: -63.070617 | E_var:     3.5149 E_err:   0.029294 | NF_loss: 15.933436
[2025-11-13 07:32:50] 405:26<1:58, 7.91s/it | [Iter 3075/3090] R0[2985/3000] | LR: 0.000002 | E: -63.133884 | E_var:     3.5240 E_err:   0.029332 | NF_loss: 14.207334
[2025-11-13 07:32:58] 405:34<1:50, 7.91s/it | [Iter 3076/3090] R0[2986/3000] | LR: 0.000002 | E: -63.109851 | E_var:     3.7959 E_err:   0.030442 | NF_loss: 15.017867
[2025-11-13 07:33:05] 405:42<1:42, 7.91s/it | [Iter 3077/3090] R0[2987/3000] | LR: 0.000001 | E: -63.132591 | E_var:     3.5004 E_err:   0.029234 | NF_loss: 20.452889
[2025-11-13 07:33:13] 405:50<1:34, 7.91s/it | [Iter 3078/3090] R0[2988/3000] | LR: 0.000001 | E: -63.122938 | E_var:     3.8025 E_err:   0.030469 | NF_loss: 13.053658
[2025-11-13 07:33:21] 405:58<1:27, 7.91s/it | [Iter 3079/3090] R0[2989/3000] | LR: 0.000001 | E: -63.187751 | E_var:     3.3830 E_err:   0.028739 | NF_loss: 14.029162
[2025-11-13 07:33:29] 406:05<1:19, 7.91s/it | [Iter 3080/3090] R0[2990/3000] | LR: 0.000001 | E: -63.129837 | E_var:     4.4296 E_err:   0.032885 | NF_loss: 14.930678
[2025-11-13 07:33:37] 406:13<1:11, 7.91s/it | [Iter 3081/3090] R0[2991/3000] | LR: 0.000001 | E: -63.123278 | E_var:     3.6193 E_err:   0.029726 | NF_loss: 15.221537
[2025-11-13 07:33:44] 406:21<1:03, 7.91s/it | [Iter 3082/3090] R0[2992/3000] | LR: 0.000001 | E: -63.176568 | E_var:     3.8956 E_err:   0.030840 | NF_loss: 13.572968
[2025-11-13 07:33:52] 406:29<0:55, 7.91s/it | [Iter 3083/3090] R0[2993/3000] | LR: 0.000001 | E: -63.172066 | E_var:     3.7473 E_err:   0.030247 | NF_loss: 13.747353
[2025-11-13 07:34:00] 406:37<0:47, 7.91s/it | [Iter 3084/3090] R0[2994/3000] | LR: 0.000000 | E: -63.044099 | E_var:     3.9125 E_err:   0.030906 | NF_loss: 11.450080
[2025-11-13 07:34:08] 406:44<0:39, 7.91s/it | [Iter 3085/3090] R0[2995/3000] | LR: 0.000000 | E: -62.974764 | E_var:     4.2347 E_err:   0.032154 | NF_loss: 10.337487
[2025-11-13 07:34:16] 406:52<0:31, 7.91s/it | [Iter 3086/3090] R0[2996/3000] | LR: 0.000000 | E: -63.077274 | E_var:     4.1023 E_err:   0.031647 | NF_loss: 15.645223
[2025-11-13 07:34:24] 407:00<0:23, 7.91s/it | [Iter 3087/3090] R0[2997/3000] | LR: 0.000000 | E: -63.116386 | E_var:     3.6259 E_err:   0.029753 | NF_loss: 12.678174
[2025-11-13 07:34:31] 407:08<0:15, 7.91s/it | [Iter 3088/3090] R0[2998/3000] | LR: 0.000000 | E: -63.094167 | E_var:     3.4962 E_err:   0.029216 | NF_loss: 13.363176
[2025-11-13 07:34:39] 407:16<0:07, 7.91s/it | [Iter 3089/3090] R0[2999/3000] | LR: 0.000000 | E: -63.102294 | E_var:     3.5654 E_err:   0.029503 | NF_loss: 15.052232
[2025-11-13 07:34:47] 407:23<0:00, 7.91s/it | [Iter 3090/3090] R0[3000/3000] | LR: 0.000000 | E: -63.087378 | E_var:     3.5303 E_err:   0.029358 | NF_loss: 15.404635
[2025-11-13 07:34:47] ======================================================================================================
[2025-11-13 07:34:47] Training completed | Runtime: 24444.0s

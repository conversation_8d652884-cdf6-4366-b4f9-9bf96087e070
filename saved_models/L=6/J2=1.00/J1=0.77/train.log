[2025-10-31 18:24:44] ✓ 创建部分等变ViT模型（嵌入层等变）
[2025-10-31 18:24:44]   对称性组大小: 8 (C4v点群)
[2025-10-31 18:24:44]   嵌入层使用等变权重共享，后续Transformer层使用普通结构
[2025-10-31 18:25:05] 🔥 预热编译: 执行模型前向传播以触发JIT编译...
[2025-10-31 18:25:12] ✓ 预热编译完成 | 耗时: 7.27s
[2025-10-31 18:25:12] ======================================================================================================
[2025-10-31 18:25:12] ViT for Shastry-Sutherland Model
[2025-10-31 18:25:12] ======================================================================================================
[2025-10-31 18:25:12] System Parameters:
[2025-10-31 18:25:12]   - Lattice size: L = 6
[2025-10-31 18:25:12]   - Total sites: N = 144
[2025-10-31 18:25:12]   - J1 coupling: 0.77
[2025-10-31 18:25:12]   - J2 coupling: 1.0
[2025-10-31 18:25:12]   - Q (4-spin): 0.0
[2025-10-31 18:25:12] ------------------------------------------------------------------------------------------------------
[2025-10-31 18:25:12] Model Parameters:
[2025-10-31 18:25:12]   ViT Architecture:
[2025-10-31 18:25:12]     • Layers: 4
[2025-10-31 18:25:12]     • Embedding dimension (d_model): 64
[2025-10-31 18:25:12]     • Attention heads: 8
[2025-10-31 18:25:12]     • Patch size: 2x2
[2025-10-31 18:25:12]     • Number of patches: 36
[2025-10-31 18:25:12]     • Config: [4, 64, 8, 2]
[2025-10-31 18:25:12]     • Total parameters: 155,680
[2025-10-31 18:25:12]   Regularization:
[2025-10-31 18:25:12]     • Relative Position Encoding (RPE): True
[2025-10-31 18:25:12]     • Dropout rate: 0.15
[2025-10-31 18:25:12]   Optimizer:
[2025-10-31 18:25:12]     • Diagonal shift (SR): 0.15
[2025-10-31 18:25:12]     • Gradient clipping: 1.0
[2025-10-31 18:25:12]   Mixed Precision Training: True
[2025-10-31 18:25:12]     • Parameters: float64 (high precision)
[2025-10-31 18:25:12]     • Computation: bfloat16 (accelerated)
[2025-10-31 18:25:12]     • Critical ops (LayerNorm/Softmax/Output): float64
[2025-10-31 18:25:12]     • Expected speedup: 1.5-2x, Memory reduction: ~50%
[2025-10-31 18:25:12] ------------------------------------------------------------------------------------------------------
[2025-10-31 18:25:12] Training Hyperparameters:
[2025-10-31 18:25:12]   Learning Rate Schedule:
[2025-10-31 18:25:12]     • Max LR: 0.05
[2025-10-31 18:25:12]     • Min LR: 1e-06
[2025-10-31 18:25:12]     • Annealing cycles: 4
[2025-10-31 18:25:12]     • Initial period: 150
[2025-10-31 18:25:12]     • Period multiplier: 2.0
[2025-10-31 18:25:12]     • Warm-up: 112 iterations (5.0%)
[2025-10-31 18:25:12]     • Total iterations: 2250 + 112 (warm-up) = 2362
[2025-10-31 18:25:12]   Sampling Parameters:
[2025-10-31 18:25:12]     • Samples (n_samples): 8192
[2025-10-31 18:25:12]     • Parallel chains (n_chains): 1024
[2025-10-31 18:25:12]     • Max exchange distance (d_max): 9.00 (auto from lattice)
[2025-10-31 18:25:12]     • Chunk size: 8192
[2025-10-31 18:25:12]     • Discarded samples per chain: 0
[2025-10-31 18:25:12]     • Parameter-to-sample ratio: 19.00
[2025-10-31 18:25:12]       ⚠ Moderate ratio (10-50), consider regularization
[2025-10-31 18:25:12] ------------------------------------------------------------------------------------------------------
[2025-10-31 18:25:12] Checkpoint Configuration:
[2025-10-31 18:25:12]   • Enabled: Yes
[2025-10-31 18:25:12]   • Save interval: 250 iterations
[2025-10-31 18:25:12]   • Keep history: True
[2025-10-31 18:25:12]   • Directory: saved_models/L=6/J2=1.00/J1=0.77/checkpoints
[2025-10-31 18:25:12] ------------------------------------------------------------------------------------------------------
[2025-10-31 18:25:12] Device Status:
[2025-10-31 18:25:12]   • Device type: H200
[2025-10-31 18:25:12]   • Number of devices: 1
[2025-10-31 18:25:12]   • Sharding enabled: True
[2025-10-31 18:25:13] ======================================================================================================
[2025-10-31 18:25:13] 🔥 Linear Warm-up: 112 iterations (5.0% of 2250) | LR: 0 -> 0.050000
[2025-10-31 18:25:13]    Total iterations: 112 (warm-up) + 2250 (training) = 2362
[2025-10-31 18:25:44] [Iter    1/2362] WARMUP[1/112] | LR: 0.000446 | E:    2.388178 | E_var:    70.3523 | E_err:   0.095716
[2025-10-31 18:26:01] [Iter    2/2362] WARMUP[2/112] | LR: 0.000893 | E:    0.068057 | E_var:    53.2185 | E_err:   0.082040
[2025-10-31 18:26:18] [Iter    3/2362] WARMUP[3/112] | LR: 0.001339 | E:   -0.687960 | E_var:    52.0691 | E_err:   0.081398
[2025-10-31 18:26:35] [Iter    4/2362] WARMUP[4/112] | LR: 0.001786 | E:   -1.273459 | E_var:    49.0037 | E_err:   0.082317
[2025-10-31 18:26:53] [Iter    5/2362] WARMUP[5/112] | LR: 0.002232 | E:   -1.723398 | E_var:    47.6770 | E_err:   0.080367
[2025-10-31 18:27:10] [Iter    6/2362] WARMUP[6/112] | LR: 0.002679 | E:   -2.326748 | E_var:    48.1083 | E_err:   0.080717
[2025-10-31 18:27:27] [Iter    7/2362] WARMUP[7/112] | LR: 0.003125 | E:   -2.615999 | E_var:    47.6258 | E_err:   0.079048
[2025-10-31 18:27:44] [Iter    8/2362] WARMUP[8/112] | LR: 0.003571 | E:   -3.083428 | E_var:    45.7217 | E_err:   0.077723
[2025-10-31 18:28:01] [Iter    9/2362] WARMUP[9/112] | LR: 0.004018 | E:   -3.790196 | E_var:    48.0911 | E_err:   0.078068
[2025-10-31 18:28:19] [Iter   10/2362] WARMUP[10/112] | LR: 0.004464 | E:   -4.331585 | E_var:    48.9564 | E_err:   0.081144
[2025-10-31 18:28:36] [Iter   11/2362] WARMUP[11/112] | LR: 0.004911 | E:   -4.932698 | E_var:    47.3042 | E_err:   0.077375
[2025-10-31 18:28:53] [Iter   12/2362] WARMUP[12/112] | LR: 0.005357 | E:   -5.478203 | E_var:    45.0058 | E_err:   0.077096
[2025-10-31 18:29:10] [Iter   13/2362] WARMUP[13/112] | LR: 0.005804 | E:   -6.002085 | E_var:    45.0499 | E_err:   0.076451
[2025-10-31 18:29:27] [Iter   14/2362] WARMUP[14/112] | LR: 0.006250 | E:   -6.836284 | E_var:    46.6363 | E_err:   0.079057
[2025-10-31 18:29:45] [Iter   15/2362] WARMUP[15/112] | LR: 0.006696 | E:   -7.544212 | E_var:    45.4774 | E_err:   0.078845
[2025-10-31 18:30:02] [Iter   16/2362] WARMUP[16/112] | LR: 0.007143 | E:   -8.491062 | E_var:    44.8944 | E_err:   0.075347
[2025-10-31 18:30:19] [Iter   17/2362] WARMUP[17/112] | LR: 0.007589 | E:   -9.337073 | E_var:    47.8004 | E_err:   0.080537
[2025-10-31 18:30:36] [Iter   18/2362] WARMUP[18/112] | LR: 0.008036 | E:  -10.511970 | E_var:    44.5553 | E_err:   0.077279
[2025-10-31 18:30:54] [Iter   19/2362] WARMUP[19/112] | LR: 0.008482 | E:  -11.384312 | E_var:    45.0065 | E_err:   0.077013
[2025-10-31 18:31:11] [Iter   20/2362] WARMUP[20/112] | LR: 0.008929 | E:  -12.833596 | E_var:    44.1733 | E_err:   0.076432
[2025-10-31 18:31:28] [Iter   21/2362] WARMUP[21/112] | LR: 0.009375 | E:  -13.961124 | E_var:    44.4581 | E_err:   0.076846
[2025-10-31 18:31:45] [Iter   22/2362] WARMUP[22/112] | LR: 0.009821 | E:  -14.759705 | E_var:    46.0662 | E_err:   0.077304
[2025-10-31 18:32:02] [Iter   23/2362] WARMUP[23/112] | LR: 0.010268 | E:  -15.928687 | E_var:    42.5542 | E_err:   0.078438
[2025-10-31 18:32:20] [Iter   24/2362] WARMUP[24/112] | LR: 0.010714 | E:  -17.031578 | E_var:    42.7259 | E_err:   0.076190
[2025-10-31 18:32:37] [Iter   25/2362] WARMUP[25/112] | LR: 0.011161 | E:  -18.302742 | E_var:    41.3137 | E_err:   0.073789
[2025-10-31 18:32:54] [Iter   26/2362] WARMUP[26/112] | LR: 0.011607 | E:  -19.801220 | E_var:    39.8234 | E_err:   0.072354
[2025-10-31 18:33:11] [Iter   27/2362] WARMUP[27/112] | LR: 0.012054 | E:  -21.025265 | E_var:    38.5703 | E_err:   0.072892
[2025-10-31 18:33:29] [Iter   28/2362] WARMUP[28/112] | LR: 0.012500 | E:  -22.399704 | E_var:    39.8451 | E_err:   0.073378
[2025-10-31 18:33:46] [Iter   29/2362] WARMUP[29/112] | LR: 0.012946 | E:  -23.663261 | E_var:    38.1753 | E_err:   0.072136
[2025-10-31 18:34:03] [Iter   30/2362] WARMUP[30/112] | LR: 0.013393 | E:  -24.814667 | E_var:    36.8173 | E_err:   0.069338
[2025-10-31 18:34:20] [Iter   31/2362] WARMUP[31/112] | LR: 0.013839 | E:  -25.810363 | E_var:    36.4603 | E_err:   0.070310
[2025-10-31 18:34:37] [Iter   32/2362] WARMUP[32/112] | LR: 0.014286 | E:  -26.855503 | E_var:    36.3539 | E_err:   0.073412
[2025-10-31 18:34:55] [Iter   33/2362] WARMUP[33/112] | LR: 0.014732 | E:  -27.922062 | E_var:    35.1081 | E_err:   0.069354
[2025-10-31 18:35:12] [Iter   34/2362] WARMUP[34/112] | LR: 0.015179 | E:  -29.008433 | E_var:    35.1226 | E_err:   0.068824
[2025-10-31 18:35:29] [Iter   35/2362] WARMUP[35/112] | LR: 0.015625 | E:  -29.986963 | E_var:    33.4382 | E_err:   0.067201
[2025-10-31 18:35:46] [Iter   36/2362] WARMUP[36/112] | LR: 0.016071 | E:  -31.154471 | E_var:    33.3204 | E_err:   0.068800
[2025-10-31 18:36:03] [Iter   37/2362] WARMUP[37/112] | LR: 0.016518 | E:  -31.878001 | E_var:    32.3043 | E_err:   0.066921
[2025-10-31 18:36:21] [Iter   38/2362] WARMUP[38/112] | LR: 0.016964 | E:  -32.806179 | E_var:    32.9467 | E_err:   0.067052
[2025-10-31 18:36:38] [Iter   39/2362] WARMUP[39/112] | LR: 0.017411 | E:  -33.517994 | E_var:    31.2429 | E_err:   0.065645
[2025-10-31 18:36:55] [Iter   40/2362] WARMUP[40/112] | LR: 0.017857 | E:  -34.410545 | E_var:    30.6540 | E_err:   0.066757
[2025-10-31 18:37:12] [Iter   41/2362] WARMUP[41/112] | LR: 0.018304 | E:  -34.795268 | E_var:    30.5896 | E_err:   0.064783
[2025-10-31 18:37:30] [Iter   42/2362] WARMUP[42/112] | LR: 0.018750 | E:  -35.567650 | E_var:    30.5238 | E_err:   0.065841
[2025-10-31 18:37:47] [Iter   43/2362] WARMUP[43/112] | LR: 0.019196 | E:  -36.112520 | E_var:    30.3391 | E_err:   0.067253
[2025-10-31 18:38:04] [Iter   44/2362] WARMUP[44/112] | LR: 0.019643 | E:  -36.732227 | E_var:    29.5834 | E_err:   0.065518
[2025-10-31 18:38:21] [Iter   45/2362] WARMUP[45/112] | LR: 0.020089 | E:  -37.295388 | E_var:    29.3827 | E_err:   0.067621
[2025-10-31 18:38:38] [Iter   46/2362] WARMUP[46/112] | LR: 0.020536 | E:  -37.577404 | E_var:    37.7228 | E_err:   0.081006
[2025-10-31 18:38:56] [Iter   47/2362] WARMUP[47/112] | LR: 0.020982 | E:  -38.106097 | E_var:    30.3011 | E_err:   0.066535
[2025-10-31 18:39:13] [Iter   48/2362] WARMUP[48/112] | LR: 0.021429 | E:  -38.500734 | E_var:    29.0295 | E_err:   0.063849
[2025-10-31 18:39:30] [Iter   49/2362] WARMUP[49/112] | LR: 0.021875 | E:  -38.655564 | E_var:    28.1143 | E_err:   0.063533
[2025-10-31 18:39:47] [Iter   50/2362] WARMUP[50/112] | LR: 0.022321 | E:  -39.218653 | E_var:    28.1163 | E_err:   0.064670
[2025-10-31 18:40:04] [Iter   51/2362] WARMUP[51/112] | LR: 0.022768 | E:  -39.781813 | E_var:    29.0983 | E_err:   0.065667
[2025-10-31 18:40:22] [Iter   52/2362] WARMUP[52/112] | LR: 0.023214 | E:  -39.921184 | E_var:    28.1223 | E_err:   0.063956
[2025-10-31 18:40:39] [Iter   53/2362] WARMUP[53/112] | LR: 0.023661 | E:  -40.200979 | E_var:    27.8528 | E_err:   0.064614
[2025-10-31 18:40:56] [Iter   54/2362] WARMUP[54/112] | LR: 0.024107 | E:  -40.515932 | E_var:    28.0155 | E_err:   0.064478
[2025-10-31 18:41:13] [Iter   55/2362] WARMUP[55/112] | LR: 0.024554 | E:  -40.824211 | E_var:    27.8121 | E_err:   0.065199
[2025-10-31 18:41:31] [Iter   56/2362] WARMUP[56/112] | LR: 0.025000 | E:  -41.310756 | E_var:    27.2658 | E_err:   0.062407
[2025-10-31 18:41:48] [Iter   57/2362] WARMUP[57/112] | LR: 0.025446 | E:  -41.666147 | E_var:    28.3064 | E_err:   0.067772
[2025-10-31 18:42:05] [Iter   58/2362] WARMUP[58/112] | LR: 0.025893 | E:  -41.921456 | E_var:    28.2068 | E_err:   0.065566
[2025-10-31 18:42:22] [Iter   59/2362] WARMUP[59/112] | LR: 0.026339 | E:  -42.007929 | E_var:    27.5467 | E_err:   0.065116
[2025-10-31 18:42:39] [Iter   60/2362] WARMUP[60/112] | LR: 0.026786 | E:  -42.477640 | E_var:    27.0655 | E_err:   0.064389
[2025-10-31 18:42:57] [Iter   61/2362] WARMUP[61/112] | LR: 0.027232 | E:  -42.558588 | E_var:    27.7332 | E_err:   0.065034
[2025-10-31 18:43:14] [Iter   62/2362] WARMUP[62/112] | LR: 0.027679 | E:  -42.753702 | E_var:    27.6238 | E_err:   0.063484
[2025-10-31 18:43:31] [Iter   63/2362] WARMUP[63/112] | LR: 0.028125 | E:  -42.785544 | E_var:    26.4704 | E_err:   0.061400
[2025-10-31 18:43:48] [Iter   64/2362] WARMUP[64/112] | LR: 0.028571 | E:  -42.948038 | E_var:    25.7081 | E_err:   0.061536
[2025-10-31 18:44:06] [Iter   65/2362] WARMUP[65/112] | LR: 0.029018 | E:  -43.423420 | E_var:    26.1038 | E_err:   0.061818
[2025-10-31 18:44:23] [Iter   66/2362] WARMUP[66/112] | LR: 0.029464 | E:  -43.592158 | E_var:    28.2739 | E_err:   0.067275
[2025-10-31 18:44:40] [Iter   67/2362] WARMUP[67/112] | LR: 0.029911 | E:  -43.987849 | E_var:    26.5876 | E_err:   0.062502
[2025-10-31 18:44:57] [Iter   68/2362] WARMUP[68/112] | LR: 0.030357 | E:  -44.330497 | E_var:    25.9116 | E_err:   0.061381
[2025-10-31 18:45:14] [Iter   69/2362] WARMUP[69/112] | LR: 0.030804 | E:  -44.493513 | E_var:    25.7653 | E_err:   0.060227
[2025-10-31 18:45:32] [Iter   70/2362] WARMUP[70/112] | LR: 0.031250 | E:  -44.761262 | E_var:    26.8426 | E_err:   0.062856
[2025-10-31 18:45:49] [Iter   71/2362] WARMUP[71/112] | LR: 0.031696 | E:  -44.999492 | E_var:    25.6876 | E_err:   0.061827
[2025-10-31 18:46:06] [Iter   72/2362] WARMUP[72/112] | LR: 0.032143 | E:  -45.183272 | E_var:    27.0652 | E_err:   0.063754
[2025-10-31 18:46:23] [Iter   73/2362] WARMUP[73/112] | LR: 0.032589 | E:  -45.273366 | E_var:    25.4648 | E_err:   0.059924
[2025-10-31 18:46:40] [Iter   74/2362] WARMUP[74/112] | LR: 0.033036 | E:  -45.514276 | E_var:    25.1997 | E_err:   0.061303
[2025-10-31 18:46:58] [Iter   75/2362] WARMUP[75/112] | LR: 0.033482 | E:  -45.137493 | E_var:    28.0332 | E_err:   0.068650
[2025-10-31 18:47:15] [Iter   76/2362] WARMUP[76/112] | LR: 0.033929 | E:  -45.562678 | E_var:    25.7540 | E_err:   0.061092
[2025-10-31 18:47:32] [Iter   77/2362] WARMUP[77/112] | LR: 0.034375 | E:  -45.918350 | E_var:    24.9602 | E_err:   0.060265
[2025-10-31 18:47:49] [Iter   78/2362] WARMUP[78/112] | LR: 0.034821 | E:  -46.270259 | E_var:    24.0952 | E_err:   0.058466
[2025-10-31 18:48:07] [Iter   79/2362] WARMUP[79/112] | LR: 0.035268 | E:  -46.336477 | E_var:    23.8578 | E_err:   0.058941
[2025-10-31 18:48:24] [Iter   80/2362] WARMUP[80/112] | LR: 0.035714 | E:  -46.607200 | E_var:    24.7853 | E_err:   0.059722
[2025-10-31 18:48:41] [Iter   81/2362] WARMUP[81/112] | LR: 0.036161 | E:  -46.958254 | E_var:    24.0518 | E_err:   0.057510
[2025-10-31 18:48:58] [Iter   82/2362] WARMUP[82/112] | LR: 0.036607 | E:  -47.130668 | E_var:    23.8999 | E_err:   0.058871
[2025-10-31 18:49:15] [Iter   83/2362] WARMUP[83/112] | LR: 0.037054 | E:  -47.339667 | E_var:    24.5940 | E_err:   0.060116
[2025-10-31 18:49:33] [Iter   84/2362] WARMUP[84/112] | LR: 0.037500 | E:  -47.407554 | E_var:    23.0509 | E_err:   0.056800
[2025-10-31 18:49:50] [Iter   85/2362] WARMUP[85/112] | LR: 0.037946 | E:  -47.423421 | E_var:    23.5654 | E_err:   0.058902
[2025-10-31 18:50:07] [Iter   86/2362] WARMUP[86/112] | LR: 0.038393 | E:  -47.764046 | E_var:    24.2984 | E_err:   0.060061
[2025-10-31 18:50:24] [Iter   87/2362] WARMUP[87/112] | LR: 0.038839 | E:  -47.666287 | E_var:    23.6203 | E_err:   0.059341
[2025-10-31 18:50:42] [Iter   88/2362] WARMUP[88/112] | LR: 0.039286 | E:  -48.169603 | E_var:    22.8437 | E_err:   0.056459
[2025-10-31 18:50:59] [Iter   89/2362] WARMUP[89/112] | LR: 0.039732 | E:  -48.332028 | E_var:    23.1714 | E_err:   0.057157
[2025-10-31 18:51:16] [Iter   90/2362] WARMUP[90/112] | LR: 0.040179 | E:  -48.273465 | E_var:    24.2999 | E_err:   0.059876
[2025-10-31 18:51:33] [Iter   91/2362] WARMUP[91/112] | LR: 0.040625 | E:  -48.506069 | E_var:    21.5241 | E_err:   0.054932
[2025-10-31 18:51:50] [Iter   92/2362] WARMUP[92/112] | LR: 0.041071 | E:  -48.776374 | E_var:    22.3213 | E_err:   0.056535
[2025-10-31 18:52:08] [Iter   93/2362] WARMUP[93/112] | LR: 0.041518 | E:  -49.125063 | E_var:    21.9374 | E_err:   0.053965
[2025-10-31 18:52:25] [Iter   94/2362] WARMUP[94/112] | LR: 0.041964 | E:  -49.401342 | E_var:    21.3144 | E_err:   0.054957
[2025-10-31 18:52:42] [Iter   95/2362] WARMUP[95/112] | LR: 0.042411 | E:  -49.591941 | E_var:    22.1915 | E_err:   0.055799
[2025-10-31 18:52:59] [Iter   96/2362] WARMUP[96/112] | LR: 0.042857 | E:  -49.620546 | E_var:    22.1760 | E_err:   0.058014
[2025-10-31 18:53:17] [Iter   97/2362] WARMUP[97/112] | LR: 0.043304 | E:  -49.814280 | E_var:    21.2760 | E_err:   0.054088
[2025-10-31 18:53:34] [Iter   98/2362] WARMUP[98/112] | LR: 0.043750 | E:  -49.809163 | E_var:    21.9957 | E_err:   0.057017
[2025-10-31 18:53:51] [Iter   99/2362] WARMUP[99/112] | LR: 0.044196 | E:  -50.099708 | E_var:    21.6781 | E_err:   0.055368
[2025-10-31 18:54:08] [Iter  100/2362] WARMUP[100/112] | LR: 0.044643 | E:  -50.154683 | E_var:    20.0216 | E_err:   0.052753
[2025-10-31 18:54:25] [Iter  101/2362] WARMUP[101/112] | LR: 0.045089 | E:  -50.515407 | E_var:    21.6615 | E_err:   0.053285
[2025-10-31 18:54:43] [Iter  102/2362] WARMUP[102/112] | LR: 0.045536 | E:  -50.182043 | E_var:    21.3599 | E_err:   0.057735
[2025-10-31 18:55:00] [Iter  103/2362] WARMUP[103/112] | LR: 0.045982 | E:  -50.168225 | E_var:    20.5459 | E_err:   0.054503
[2025-10-31 18:55:17] [Iter  104/2362] WARMUP[104/112] | LR: 0.046429 | E:  -50.193985 | E_var:    21.4031 | E_err:   0.056123
[2025-10-31 18:55:34] [Iter  105/2362] WARMUP[105/112] | LR: 0.046875 | E:  -50.622534 | E_var:    20.4707 | E_err:   0.052854
[2025-10-31 18:55:51] [Iter  106/2362] WARMUP[106/112] | LR: 0.047321 | E:  -50.447585 | E_var:    21.7734 | E_err:   0.061245
[2025-10-31 18:56:09] [Iter  107/2362] WARMUP[107/112] | LR: 0.047768 | E:  -50.297879 | E_var:    22.8943 | E_err:   0.066447
[2025-10-31 18:56:26] [Iter  108/2362] WARMUP[108/112] | LR: 0.048214 | E:  -51.324420 | E_var:    18.8979 | E_err:   0.050936
[2025-10-31 18:56:43] [Iter  109/2362] WARMUP[109/112] | LR: 0.048661 | E:  -51.611313 | E_var:    18.3398 | E_err:   0.050209
[2025-10-31 18:57:00] [Iter  110/2362] WARMUP[110/112] | LR: 0.049107 | E:  -51.969509 | E_var:    17.8219 | E_err:   0.049464
[2025-10-31 18:57:18] [Iter  111/2362] WARMUP[111/112] | LR: 0.049554 | E:  -51.991837 | E_var:    17.9100 | E_err:   0.049141
[2025-10-31 18:57:18] ✅ Warm-up completed | Starting cosine annealing from LR=0.050000
[2025-10-31 18:57:35] [Iter  112/2362] WARMUP[112/112] | LR: 0.050000 | E:  -52.053527 | E_var:    17.4503 | E_err:   0.048410
[2025-10-31 18:57:52] [Iter  113/2362] R0[0/150]     | LR: 0.050000 | E:  -52.274364 | E_var:    17.7875 | E_err:   0.048939
[2025-10-31 18:58:09] [Iter  114/2362] R0[1/150]     | LR: 0.049995 | E:  -52.270495 | E_var:    16.7999 | E_err:   0.049044
[2025-10-31 18:58:26] [Iter  115/2362] R0[2/150]     | LR: 0.049978 | E:  -52.275806 | E_var:    16.9807 | E_err:   0.048886
[2025-10-31 18:58:44] [Iter  116/2362] R0[3/150]     | LR: 0.049951 | E:  -52.735277 | E_var:    16.6000 | E_err:   0.046944
[2025-10-31 18:59:01] [Iter  117/2362] R0[4/150]     | LR: 0.049912 | E:  -52.867840 | E_var:    17.7441 | E_err:   0.050122
[2025-10-31 18:59:18] [Iter  118/2362] R0[5/150]     | LR: 0.049863 | E:  -53.144451 | E_var:    17.5879 | E_err:   0.050462
[2025-10-31 18:59:35] [Iter  119/2362] R0[6/150]     | LR: 0.049803 | E:  -52.922073 | E_var:    17.9405 | E_err:   0.048216
[2025-10-31 18:59:53] [Iter  120/2362] R0[7/150]     | LR: 0.049732 | E:  -52.571513 | E_var:    18.2768 | E_err:   0.054277
[2025-10-31 19:00:10] [Iter  121/2362] R0[8/150]     | LR: 0.049650 | E:  -52.128892 | E_var:    19.1612 | E_err:   0.055387
[2025-10-31 19:00:27] [Iter  122/2362] R0[9/150]     | LR: 0.049557 | E:  -52.795405 | E_var:    16.8966 | E_err:   0.049190
[2025-10-31 19:00:44] [Iter  123/2362] R0[10/150]    | LR: 0.049454 | E:  -53.069912 | E_var:    17.3182 | E_err:   0.048312
[2025-10-31 19:01:01] [Iter  124/2362] R0[11/150]    | LR: 0.049339 | E:  -53.397011 | E_var:    16.8936 | E_err:   0.047780
[2025-10-31 19:01:19] [Iter  125/2362] R0[12/150]    | LR: 0.049215 | E:  -53.403734 | E_var:    15.5030 | E_err:   0.046890
[2025-10-31 19:01:36] [Iter  126/2362] R0[13/150]    | LR: 0.049079 | E:  -53.627695 | E_var:    15.9746 | E_err:   0.048014
[2025-10-31 19:01:53] [Iter  127/2362] R0[14/150]    | LR: 0.048933 | E:  -53.715925 | E_var:    16.0427 | E_err:   0.047982
[2025-10-31 19:02:10] [Iter  128/2362] R0[15/150]    | LR: 0.048776 | E:  -53.850915 | E_var:    15.9041 | E_err:   0.048794
[2025-10-31 19:02:27] [Iter  129/2362] R0[16/150]    | LR: 0.048609 | E:  -54.086948 | E_var:    16.0682 | E_err:   0.046791
[2025-10-31 19:02:45] [Iter  130/2362] R0[17/150]    | LR: 0.048432 | E:  -54.270456 | E_var:    14.9715 | E_err:   0.045196
[2025-10-31 19:03:02] [Iter  131/2362] R0[18/150]    | LR: 0.048244 | E:  -54.330072 | E_var:    15.7773 | E_err:   0.046448
[2025-10-31 19:03:19] [Iter  132/2362] R0[19/150]    | LR: 0.048047 | E:  -54.476442 | E_var:    15.1964 | E_err:   0.045652
[2025-10-31 19:03:36] [Iter  133/2362] R0[20/150]    | LR: 0.047839 | E:  -54.468553 | E_var:    14.9006 | E_err:   0.046399
[2025-10-31 19:03:54] [Iter  134/2362] R0[21/150]    | LR: 0.047621 | E:  -54.476743 | E_var:    15.2475 | E_err:   0.045668
[2025-10-31 19:04:11] [Iter  135/2362] R0[22/150]    | LR: 0.047393 | E:  -54.517194 | E_var:    14.9478 | E_err:   0.045533
[2025-10-31 19:04:28] [Iter  136/2362] R0[23/150]    | LR: 0.047155 | E:  -54.694719 | E_var:    13.9694 | E_err:   0.045203
[2025-10-31 19:04:45] [Iter  137/2362] R0[24/150]    | LR: 0.046908 | E:  -54.597392 | E_var:    16.1782 | E_err:   0.048792
[2025-10-31 19:05:02] [Iter  138/2362] R0[25/150]    | LR: 0.046651 | E:  -54.571111 | E_var:    14.7443 | E_err:   0.045307
[2025-10-31 19:05:20] [Iter  139/2362] R0[26/150]    | LR: 0.046384 | E:  -54.910727 | E_var:    14.7202 | E_err:   0.045243
[2025-10-31 19:05:37] [Iter  140/2362] R0[27/150]    | LR: 0.046108 | E:  -55.068263 | E_var:    15.4036 | E_err:   0.046304
[2025-10-31 19:05:54] [Iter  141/2362] R0[28/150]    | LR: 0.045823 | E:  -55.014386 | E_var:    15.2194 | E_err:   0.046897
[2025-10-31 19:06:11] [Iter  142/2362] R0[29/150]    | LR: 0.045529 | E:  -55.174505 | E_var:    13.3980 | E_err:   0.044433
[2025-10-31 19:06:29] [Iter  143/2362] R0[30/150]    | LR: 0.045226 | E:  -55.140727 | E_var:    14.3086 | E_err:   0.046559
[2025-10-31 19:06:46] [Iter  144/2362] R0[31/150]    | LR: 0.044913 | E:  -55.194526 | E_var:    13.9014 | E_err:   0.044523
[2025-10-31 19:07:03] [Iter  145/2362] R0[32/150]    | LR: 0.044592 | E:  -55.414910 | E_var:    14.3859 | E_err:   0.043751
[2025-10-31 19:07:20] [Iter  146/2362] R0[33/150]    | LR: 0.044263 | E:  -55.340705 | E_var:    13.2459 | E_err:   0.044027
[2025-10-31 19:07:37] [Iter  147/2362] R0[34/150]    | LR: 0.043925 | E:  -55.401816 | E_var:    13.1023 | E_err:   0.043312
[2025-10-31 19:07:55] [Iter  148/2362] R0[35/150]    | LR: 0.043579 | E:  -55.255595 | E_var:    14.1591 | E_err:   0.046214
[2025-10-31 19:08:12] [Iter  149/2362] R0[36/150]    | LR: 0.043224 | E:  -55.198360 | E_var:    14.1370 | E_err:   0.044857
[2025-10-31 19:08:29] [Iter  150/2362] R0[37/150]    | LR: 0.042862 | E:  -55.465523 | E_var:    13.4590 | E_err:   0.043927
[2025-10-31 19:08:46] [Iter  151/2362] R0[38/150]    | LR: 0.042492 | E:  -55.556219 | E_var:    12.9788 | E_err:   0.042552
[2025-10-31 19:09:03] [Iter  152/2362] R0[39/150]    | LR: 0.042114 | E:  -55.610818 | E_var:    12.9626 | E_err:   0.043090
[2025-10-31 19:09:21] [Iter  153/2362] R0[40/150]    | LR: 0.041728 | E:  -54.585522 | E_var:    13.4487 | E_err:   0.044933
[2025-10-31 19:09:38] [Iter  154/2362] R0[41/150]    | LR: 0.041336 | E:  -55.079545 | E_var:    13.0298 | E_err:   0.042579
[2025-10-31 19:09:55] [Iter  155/2362] R0[42/150]    | LR: 0.040936 | E:  -55.410187 | E_var:    12.4911 | E_err:   0.042913
[2025-10-31 19:10:12] [Iter  156/2362] R0[43/150]    | LR: 0.040529 | E:  -54.923771 | E_var:    12.5002 | E_err:   0.042317
[2025-10-31 19:10:30] [Iter  157/2362] R0[44/150]    | LR: 0.040115 | E:  -55.243004 | E_var:    12.7843 | E_err:   0.043344
[2025-10-31 19:10:47] [Iter  158/2362] R0[45/150]    | LR: 0.039695 | E:  -55.495933 | E_var:    12.8143 | E_err:   0.042764
[2025-10-31 19:11:04] [Iter  159/2362] R0[46/150]    | LR: 0.039268 | E:  -55.834346 | E_var:    13.5975 | E_err:   0.042395
[2025-10-31 19:11:21] [Iter  160/2362] R0[47/150]    | LR: 0.038835 | E:  -55.805535 | E_var:    13.6153 | E_err:   0.044811
[2025-10-31 19:11:38] [Iter  161/2362] R0[48/150]    | LR: 0.038396 | E:  -55.906980 | E_var:    12.1632 | E_err:   0.041166
[2025-10-31 19:11:56] [Iter  162/2362] R0[49/150]    | LR: 0.037951 | E:  -55.961065 | E_var:    12.1814 | E_err:   0.041527
[2025-10-31 19:12:13] [Iter  163/2362] R0[50/150]    | LR: 0.037500 | E:  -55.970438 | E_var:    12.2930 | E_err:   0.040247
[2025-10-31 19:12:30] [Iter  164/2362] R0[51/150]    | LR: 0.037044 | E:  -56.011867 | E_var:    13.9699 | E_err:   0.044391
[2025-10-31 19:12:47] [Iter  165/2362] R0[52/150]    | LR: 0.036583 | E:  -55.838855 | E_var:    13.4627 | E_err:   0.044307
[2025-10-31 19:13:04] [Iter  166/2362] R0[53/150]    | LR: 0.036116 | E:  -55.926254 | E_var:    13.1067 | E_err:   0.040974
[2025-10-31 19:13:22] [Iter  167/2362] R0[54/150]    | LR: 0.035645 | E:  -56.081762 | E_var:    12.2552 | E_err:   0.040394
[2025-10-31 19:13:39] [Iter  168/2362] R0[55/150]    | LR: 0.035169 | E:  -55.904950 | E_var:    11.5870 | E_err:   0.039584
[2025-10-31 19:13:56] [Iter  169/2362] R0[56/150]    | LR: 0.034688 | E:  -55.293572 | E_var:    12.1674 | E_err:   0.041759
[2025-10-31 19:14:13] [Iter  170/2362] R0[57/150]    | LR: 0.034203 | E:  -55.573131 | E_var:    11.8668 | E_err:   0.039822
[2025-10-31 19:14:31] [Iter  171/2362] R0[58/150]    | LR: 0.033715 | E:  -55.339252 | E_var:    11.9348 | E_err:   0.041944
[2025-10-31 19:14:48] [Iter  172/2362] R0[59/150]    | LR: 0.033222 | E:  -55.468905 | E_var:    12.1766 | E_err:   0.041714
[2025-10-31 19:15:05] [Iter  173/2362] R0[60/150]    | LR: 0.032726 | E:  -56.055988 | E_var:    12.5292 | E_err:   0.042293
[2025-10-31 19:15:22] [Iter  174/2362] R0[61/150]    | LR: 0.032226 | E:  -56.104131 | E_var:    12.7624 | E_err:   0.041133
[2025-10-31 19:15:39] [Iter  175/2362] R0[62/150]    | LR: 0.031723 | E:  -56.106461 | E_var:    12.1994 | E_err:   0.041509
[2025-10-31 19:15:57] [Iter  176/2362] R0[63/150]    | LR: 0.031218 | E:  -56.248832 | E_var:    11.9002 | E_err:   0.040144
[2025-10-31 19:16:14] [Iter  177/2362] R0[64/150]    | LR: 0.030709 | E:  -56.155132 | E_var:    13.9010 | E_err:   0.044068
[2025-10-31 19:16:31] [Iter  178/2362] R0[65/150]    | LR: 0.030198 | E:  -56.250466 | E_var:    13.3777 | E_err:   0.043491
[2025-10-31 19:16:48] [Iter  179/2362] R0[66/150]    | LR: 0.029685 | E:  -56.135474 | E_var:    12.4937 | E_err:   0.042893
[2025-10-31 19:17:06] [Iter  180/2362] R0[67/150]    | LR: 0.029170 | E:  -56.239106 | E_var:    12.8525 | E_err:   0.042149
[2025-10-31 19:17:23] [Iter  181/2362] R0[68/150]    | LR: 0.028653 | E:  -56.132573 | E_var:    13.1846 | E_err:   0.044359
[2025-10-31 19:17:40] [Iter  182/2362] R0[69/150]    | LR: 0.028134 | E:  -56.063888 | E_var:    13.9486 | E_err:   0.045702
[2025-10-31 19:17:57] [Iter  183/2362] R0[70/150]    | LR: 0.027614 | E:  -56.344648 | E_var:    12.1899 | E_err:   0.042356
[2025-10-31 19:18:14] [Iter  184/2362] R0[71/150]    | LR: 0.027092 | E:  -56.269189 | E_var:    12.3307 | E_err:   0.040809
[2025-10-31 19:18:32] [Iter  185/2362] R0[72/150]    | LR: 0.026570 | E:  -56.001722 | E_var:    18.0032 | E_err:   0.069843
[2025-10-31 19:18:49] [Iter  186/2362] R0[73/150]    | LR: 0.026047 | E:  -55.875588 | E_var:    13.3489 | E_err:   0.044708
[2025-10-31 19:19:06] [Iter  187/2362] R0[74/150]    | LR: 0.025524 | E:  -55.770113 | E_var:    12.7719 | E_err:   0.046330
[2025-10-31 19:19:23] [Iter  188/2362] R0[75/150]    | LR: 0.025001 | E:  -55.726394 | E_var:    12.9233 | E_err:   0.045311
[2025-10-31 19:19:40] [Iter  189/2362] R0[76/150]    | LR: 0.024477 | E:  -55.684875 | E_var:    12.8916 | E_err:   0.045451
[2025-10-31 19:19:58] [Iter  190/2362] R0[77/150]    | LR: 0.023954 | E:  -56.104029 | E_var:    10.8737 | E_err:   0.040308
[2025-10-31 19:20:15] [Iter  191/2362] R0[78/150]    | LR: 0.023431 | E:  -56.329212 | E_var:    11.6317 | E_err:   0.040609
[2025-10-31 19:20:32] [Iter  192/2362] R0[79/150]    | LR: 0.022909 | E:  -56.361793 | E_var:    12.3497 | E_err:   0.042530
[2025-10-31 19:20:49] [Iter  193/2362] R0[80/150]    | LR: 0.022387 | E:  -56.376781 | E_var:    12.1265 | E_err:   0.040071
[2025-10-31 19:21:07] [Iter  194/2362] R0[81/150]    | LR: 0.021867 | E:  -56.289948 | E_var:    11.8493 | E_err:   0.040949
[2025-10-31 19:21:24] [Iter  195/2362] R0[82/150]    | LR: 0.021348 | E:  -56.293592 | E_var:    11.9760 | E_err:   0.042334
[2025-10-31 19:21:41] [Iter  196/2362] R0[83/150]    | LR: 0.020831 | E:  -56.412883 | E_var:    11.9708 | E_err:   0.041557
[2025-10-31 19:21:58] [Iter  197/2362] R0[84/150]    | LR: 0.020316 | E:  -56.472009 | E_var:    11.9311 | E_err:   0.040728
[2025-10-31 19:22:15] [Iter  198/2362] R0[85/150]    | LR: 0.019803 | E:  -56.464186 | E_var:    11.9193 | E_err:   0.040636
[2025-10-31 19:22:33] [Iter  199/2362] R0[86/150]    | LR: 0.019292 | E:  -56.548533 | E_var:    10.7602 | E_err:   0.039001
[2025-10-31 19:22:50] [Iter  200/2362] R0[87/150]    | LR: 0.018783 | E:  -56.247491 | E_var:    11.1112 | E_err:   0.039698
[2025-10-31 19:23:07] [Iter  201/2362] R0[88/150]    | LR: 0.018278 | E:  -56.091093 | E_var:    11.5536 | E_err:   0.041106
[2025-10-31 19:23:24] [Iter  202/2362] R0[89/150]    | LR: 0.017775 | E:  -56.402712 | E_var:    11.0921 | E_err:   0.039130
[2025-10-31 19:23:41] [Iter  203/2362] R0[90/150]    | LR: 0.017275 | E:  -56.551312 | E_var:    11.0655 | E_err:   0.038274
[2025-10-31 19:23:59] [Iter  204/2362] R0[91/150]    | LR: 0.016779 | E:  -56.689453 | E_var:    11.7282 | E_err:   0.040106
[2025-10-31 19:24:16] [Iter  205/2362] R0[92/150]    | LR: 0.016286 | E:  -56.661870 | E_var:    10.8536 | E_err:   0.039209
[2025-10-31 19:24:33] [Iter  206/2362] R0[93/150]    | LR: 0.015798 | E:  -56.618061 | E_var:    11.2765 | E_err:   0.040736
[2025-10-31 19:24:50] [Iter  207/2362] R0[94/150]    | LR: 0.015313 | E:  -56.708802 | E_var:    11.5741 | E_err:   0.040206
[2025-10-31 19:25:08] [Iter  208/2362] R0[95/150]    | LR: 0.014832 | E:  -56.705891 | E_var:    10.4940 | E_err:   0.038371
[2025-10-31 19:25:25] [Iter  209/2362] R0[96/150]    | LR: 0.014356 | E:  -56.614688 | E_var:    11.7607 | E_err:   0.040199
[2025-10-31 19:25:42] [Iter  210/2362] R0[97/150]    | LR: 0.013885 | E:  -56.714815 | E_var:    11.0526 | E_err:   0.038188
[2025-10-31 19:25:59] [Iter  211/2362] R0[98/150]    | LR: 0.013418 | E:  -56.733482 | E_var:    11.2024 | E_err:   0.039342
[2025-10-31 19:26:16] [Iter  212/2362] R0[99/150]    | LR: 0.012957 | E:  -56.763156 | E_var:    10.8621 | E_err:   0.037597
[2025-10-31 19:26:34] [Iter  213/2362] R0[100/150]   | LR: 0.012501 | E:  -56.760216 | E_var:    11.0619 | E_err:   0.038138
[2025-10-31 19:26:51] [Iter  214/2362] R0[101/150]   | LR: 0.012050 | E:  -56.760471 | E_var:    10.7353 | E_err:   0.038766
[2025-10-31 19:27:08] [Iter  215/2362] R0[102/150]   | LR: 0.011605 | E:  -56.686026 | E_var:    11.0877 | E_err:   0.039857
[2025-10-31 19:27:25] [Iter  216/2362] R0[103/150]   | LR: 0.011166 | E:  -56.594276 | E_var:    11.1199 | E_err:   0.039867
[2025-10-31 19:27:43] [Iter  217/2362] R0[104/150]   | LR: 0.010733 | E:  -56.784999 | E_var:    11.3266 | E_err:   0.039522
[2025-10-31 19:28:00] [Iter  218/2362] R0[105/150]   | LR: 0.010306 | E:  -56.715854 | E_var:    13.1977 | E_err:   0.043514
[2025-10-31 19:28:17] [Iter  219/2362] R0[106/150]   | LR: 0.009886 | E:  -56.660584 | E_var:    11.5831 | E_err:   0.041671
[2025-10-31 19:28:34] [Iter  220/2362] R0[107/150]   | LR: 0.009472 | E:  -56.650951 | E_var:    11.6760 | E_err:   0.041058
[2025-10-31 19:28:51] [Iter  221/2362] R0[108/150]   | LR: 0.009065 | E:  -56.800799 | E_var:    12.2913 | E_err:   0.042804
[2025-10-31 19:29:09] [Iter  222/2362] R0[109/150]   | LR: 0.008665 | E:  -56.847115 | E_var:    11.4993 | E_err:   0.040761
[2025-10-31 19:29:26] [Iter  223/2362] R0[110/150]   | LR: 0.008273 | E:  -56.824998 | E_var:    11.4795 | E_err:   0.038920
[2025-10-31 19:29:43] [Iter  224/2362] R0[111/150]   | LR: 0.007887 | E:  -56.894288 | E_var:    11.0355 | E_err:   0.039109
[2025-10-31 19:30:00] [Iter  225/2362] R0[112/150]   | LR: 0.007509 | E:  -56.861587 | E_var:    10.9705 | E_err:   0.038559
[2025-10-31 19:30:18] [Iter  226/2362] R0[113/150]   | LR: 0.007139 | E:  -56.917339 | E_var:    11.5855 | E_err:   0.039943
[2025-10-31 19:30:35] [Iter  227/2362] R0[114/150]   | LR: 0.006777 | E:  -56.861931 | E_var:    11.5549 | E_err:   0.040548
[2025-10-31 19:30:52] [Iter  228/2362] R0[115/150]   | LR: 0.006422 | E:  -56.912104 | E_var:    11.4267 | E_err:   0.039096
[2025-10-31 19:31:09] [Iter  229/2362] R0[116/150]   | LR: 0.006076 | E:  -56.716705 | E_var:    10.4040 | E_err:   0.038156
[2025-10-31 19:31:26] [Iter  230/2362] R0[117/150]   | LR: 0.005738 | E:  -56.853163 | E_var:    10.5050 | E_err:   0.038699
[2025-10-31 19:31:44] [Iter  231/2362] R0[118/150]   | LR: 0.005409 | E:  -56.780416 | E_var:    10.0795 | E_err:   0.038800
[2025-10-31 19:32:01] [Iter  232/2362] R0[119/150]   | LR: 0.005088 | E:  -56.898714 | E_var:    11.4212 | E_err:   0.038601
[2025-10-31 19:32:18] [Iter  233/2362] R0[120/150]   | LR: 0.004775 | E:  -56.910771 | E_var:    13.3242 | E_err:   0.043045
[2025-10-31 19:32:35] [Iter  234/2362] R0[121/150]   | LR: 0.004472 | E:  -56.898478 | E_var:    11.1690 | E_err:   0.039470
[2025-10-31 19:32:53] [Iter  235/2362] R0[122/150]   | LR: 0.004178 | E:  -56.776549 | E_var:    10.9277 | E_err:   0.039278
[2025-10-31 19:33:10] [Iter  236/2362] R0[123/150]   | LR: 0.003893 | E:  -56.874036 | E_var:    11.1080 | E_err:   0.038751
[2025-10-31 19:33:27] [Iter  237/2362] R0[124/150]   | LR: 0.003617 | E:  -56.663092 | E_var:    11.3826 | E_err:   0.041066
[2025-10-31 19:33:44] [Iter  238/2362] R0[125/150]   | LR: 0.003350 | E:  -56.936090 | E_var:    10.5185 | E_err:   0.038281
[2025-10-31 19:34:01] [Iter  239/2362] R0[126/150]   | LR: 0.003093 | E:  -56.808397 | E_var:    11.7087 | E_err:   0.041622
[2025-10-31 19:34:19] [Iter  240/2362] R0[127/150]   | LR: 0.002846 | E:  -56.814012 | E_var:    10.7346 | E_err:   0.039783
[2025-10-31 19:34:36] [Iter  241/2362] R0[128/150]   | LR: 0.002608 | E:  -56.944986 | E_var:    10.3013 | E_err:   0.038055
[2025-10-31 19:34:53] [Iter  242/2362] R0[129/150]   | LR: 0.002380 | E:  -56.988838 | E_var:    10.6495 | E_err:   0.038553
[2025-10-31 19:35:10] [Iter  243/2362] R0[130/150]   | LR: 0.002162 | E:  -57.008191 | E_var:    10.6214 | E_err:   0.039184
[2025-10-31 19:35:27] [Iter  244/2362] R0[131/150]   | LR: 0.001954 | E:  -56.980541 | E_var:    10.5228 | E_err:   0.037441
[2025-10-31 19:35:45] [Iter  245/2362] R0[132/150]   | LR: 0.001757 | E:  -56.899551 | E_var:    10.6148 | E_err:   0.038587
[2025-10-31 19:36:02] [Iter  246/2362] R0[133/150]   | LR: 0.001569 | E:  -56.981990 | E_var:    13.8885 | E_err:   0.043013
[2025-10-31 19:36:19] [Iter  247/2362] R0[134/150]   | LR: 0.001392 | E:  -56.974234 | E_var:    10.7389 | E_err:   0.038621
[2025-10-31 19:36:36] [Iter  248/2362] R0[135/150]   | LR: 0.001225 | E:  -56.988941 | E_var:    10.9783 | E_err:   0.039309
[2025-10-31 19:36:54] [Iter  249/2362] R0[136/150]   | LR: 0.001068 | E:  -57.044679 | E_var:    10.3829 | E_err:   0.038838
[2025-10-31 19:37:11] [Iter  250/2362] R0[137/150]   | LR: 0.000922 | E:  -57.139120 | E_var:    11.2151 | E_err:   0.039047
[2025-10-31 19:37:28] [Iter  251/2362] R0[138/150]   | LR: 0.000786 | E:  -56.987807 | E_var:    10.7475 | E_err:   0.039121
[2025-10-31 19:37:45] [Iter  252/2362] R0[139/150]   | LR: 0.000662 | E:  -57.051838 | E_var:    10.1532 | E_err:   0.036596
[2025-10-31 19:38:02] [Iter  253/2362] R0[140/150]   | LR: 0.000547 | E:  -57.086335 | E_var:     9.9720 | E_err:   0.037632
[2025-10-31 19:38:20] [Iter  254/2362] R0[141/150]   | LR: 0.000444 | E:  -57.069249 | E_var:    10.2075 | E_err:   0.037540
[2025-10-31 19:38:37] [Iter  255/2362] R0[142/150]   | LR: 0.000351 | E:  -56.996182 | E_var:    11.5505 | E_err:   0.039565
[2025-10-31 19:38:54] [Iter  256/2362] R0[143/150]   | LR: 0.000269 | E:  -56.904129 | E_var:     9.7437 | E_err:   0.036563
[2025-10-31 19:39:11] [Iter  257/2362] R0[144/150]   | LR: 0.000198 | E:  -56.915519 | E_var:    10.0309 | E_err:   0.036682
[2025-10-31 19:39:29] [Iter  258/2362] R0[145/150]   | LR: 0.000138 | E:  -56.985617 | E_var:    10.6805 | E_err:   0.037717
[2025-10-31 19:39:46] [Iter  259/2362] R0[146/150]   | LR: 0.000089 | E:  -57.101803 | E_var:    10.8833 | E_err:   0.038505
[2025-10-31 19:40:03] [Iter  260/2362] R0[147/150]   | LR: 0.000050 | E:  -57.124683 | E_var:    11.8086 | E_err:   0.039667
[2025-10-31 19:40:20] [Iter  261/2362] R0[148/150]   | LR: 0.000023 | E:  -57.201683 | E_var:    10.4160 | E_err:   0.038274
[2025-10-31 19:40:37] [Iter  262/2362] R0[149/150]   | LR: 0.000006 | E:  -57.106507 | E_var:    10.7988 | E_err:   0.038624
[2025-10-31 19:40:37] 🔄 RESTART #1 | Period: 300
[2025-10-31 19:40:55] [Iter  263/2362] R1[0/300]     | LR: 0.050000 | E:  -56.998888 | E_var:    10.3863 | E_err:   0.037847
[2025-10-31 19:41:12] [Iter  264/2362] R1[1/300]     | LR: 0.049999 | E:  -56.940122 | E_var:    10.9967 | E_err:   0.038579
[2025-10-31 19:41:29] [Iter  265/2362] R1[2/300]     | LR: 0.049995 | E:  -57.025095 | E_var:    11.3831 | E_err:   0.039481
[2025-10-31 19:41:46] [Iter  266/2362] R1[3/300]     | LR: 0.049988 | E:  -57.159204 | E_var:    11.3734 | E_err:   0.039349
[2025-10-31 19:42:03] [Iter  267/2362] R1[4/300]     | LR: 0.049978 | E:  -57.078487 | E_var:    11.4817 | E_err:   0.040439
[2025-10-31 19:42:21] [Iter  268/2362] R1[5/300]     | LR: 0.049966 | E:  -57.141872 | E_var:    10.4640 | E_err:   0.037331
[2025-10-31 19:42:38] [Iter  269/2362] R1[6/300]     | LR: 0.049951 | E:  -57.152213 | E_var:    10.0832 | E_err:   0.037931
[2025-10-31 19:42:55] [Iter  270/2362] R1[7/300]     | LR: 0.049933 | E:  -57.073671 | E_var:    11.0606 | E_err:   0.039252
[2025-10-31 19:43:12] [Iter  271/2362] R1[8/300]     | LR: 0.049912 | E:  -57.185655 | E_var:    10.7384 | E_err:   0.037485
[2025-10-31 19:43:30] [Iter  272/2362] R1[9/300]     | LR: 0.049889 | E:  -57.207540 | E_var:    10.7697 | E_err:   0.038418
[2025-10-31 19:43:47] [Iter  273/2362] R1[10/300]    | LR: 0.049863 | E:  -57.228928 | E_var:    10.1901 | E_err:   0.037856
[2025-10-31 19:44:04] [Iter  274/2362] R1[11/300]    | LR: 0.049834 | E:  -57.175980 | E_var:     9.6359 | E_err:   0.035975
[2025-10-31 19:44:21] [Iter  275/2362] R1[12/300]    | LR: 0.049803 | E:  -57.274872 | E_var:    10.7359 | E_err:   0.038205
[2025-10-31 19:44:38] [Iter  276/2362] R1[13/300]    | LR: 0.049769 | E:  -57.214304 | E_var:    12.0059 | E_err:   0.041296
[2025-10-31 19:44:56] [Iter  277/2362] R1[14/300]    | LR: 0.049732 | E:  -57.140787 | E_var:    10.8177 | E_err:   0.038948
[2025-10-31 19:45:13] [Iter  278/2362] R1[15/300]    | LR: 0.049692 | E:  -57.152337 | E_var:    11.8586 | E_err:   0.040227
[2025-10-31 19:45:30] [Iter  279/2362] R1[16/300]    | LR: 0.049650 | E:  -57.239181 | E_var:    10.4521 | E_err:   0.039245
[2025-10-31 19:45:47] [Iter  280/2362] R1[17/300]    | LR: 0.049605 | E:  -57.229888 | E_var:     9.5934 | E_err:   0.036303
[2025-10-31 19:46:04] [Iter  281/2362] R1[18/300]    | LR: 0.049557 | E:  -57.199951 | E_var:     9.8255 | E_err:   0.035821
[2025-10-31 19:46:22] [Iter  282/2362] R1[19/300]    | LR: 0.049507 | E:  -57.207438 | E_var:     9.8747 | E_err:   0.037294
[2025-10-31 19:46:39] [Iter  283/2362] R1[20/300]    | LR: 0.049454 | E:  -57.252065 | E_var:    10.1184 | E_err:   0.037294
[2025-10-31 19:46:56] [Iter  284/2362] R1[21/300]    | LR: 0.049398 | E:  -57.205669 | E_var:    10.5563 | E_err:   0.038189
[2025-10-31 19:47:13] [Iter  285/2362] R1[22/300]    | LR: 0.049339 | E:  -57.167942 | E_var:    10.5161 | E_err:   0.037952
[2025-10-31 19:47:31] [Iter  286/2362] R1[23/300]    | LR: 0.049278 | E:  -57.296052 | E_var:    11.1582 | E_err:   0.039595
[2025-10-31 19:47:48] [Iter  287/2362] R1[24/300]    | LR: 0.049215 | E:  -57.320592 | E_var:    10.0265 | E_err:   0.036317
[2025-10-31 19:48:05] [Iter  288/2362] R1[25/300]    | LR: 0.049148 | E:  -57.347642 | E_var:     9.8828 | E_err:   0.036549
[2025-10-31 19:48:22] [Iter  289/2362] R1[26/300]    | LR: 0.049079 | E:  -57.310319 | E_var:    10.4961 | E_err:   0.038424
[2025-10-31 19:48:39] [Iter  290/2362] R1[27/300]    | LR: 0.049007 | E:  -57.356795 | E_var:    10.2140 | E_err:   0.037623
[2025-10-31 19:48:57] [Iter  291/2362] R1[28/300]    | LR: 0.048933 | E:  -57.304822 | E_var:    10.1214 | E_err:   0.037540
[2025-10-31 19:49:14] [Iter  292/2362] R1[29/300]    | LR: 0.048856 | E:  -57.414276 | E_var:    10.3542 | E_err:   0.037086
[2025-10-31 19:49:31] [Iter  293/2362] R1[30/300]    | LR: 0.048776 | E:  -57.324989 | E_var:    10.2545 | E_err:   0.038448
[2025-10-31 19:49:48] [Iter  294/2362] R1[31/300]    | LR: 0.048694 | E:  -57.381268 | E_var:    10.0522 | E_err:   0.036054
[2025-10-31 19:50:06] [Iter  295/2362] R1[32/300]    | LR: 0.048609 | E:  -57.374889 | E_var:    10.5152 | E_err:   0.039634
[2025-10-31 19:50:23] [Iter  296/2362] R1[33/300]    | LR: 0.048522 | E:  -57.365607 | E_var:    10.8083 | E_err:   0.038481
[2025-10-31 19:50:40] [Iter  297/2362] R1[34/300]    | LR: 0.048432 | E:  -57.415486 | E_var:     9.4907 | E_err:   0.035361
[2025-10-31 19:50:57] [Iter  298/2362] R1[35/300]    | LR: 0.048340 | E:  -57.445073 | E_var:     9.7486 | E_err:   0.036779
[2025-10-31 19:51:14] [Iter  299/2362] R1[36/300]    | LR: 0.048244 | E:  -57.391153 | E_var:    10.5170 | E_err:   0.038256
[2025-10-31 19:51:32] [Iter  300/2362] R1[37/300]    | LR: 0.048147 | E:  -57.426171 | E_var:     9.4018 | E_err:   0.035124
[2025-10-31 19:51:49] [Iter  301/2362] R1[38/300]    | LR: 0.048047 | E:  -57.464045 | E_var:    10.6533 | E_err:   0.039219
[2025-10-31 19:52:06] [Iter  302/2362] R1[39/300]    | LR: 0.047944 | E:  -57.503964 | E_var:     9.5154 | E_err:   0.035607
[2025-10-31 19:52:23] [Iter  303/2362] R1[40/300]    | LR: 0.047839 | E:  -57.517694 | E_var:     9.2250 | E_err:   0.035547
[2025-10-31 19:52:41] [Iter  304/2362] R1[41/300]    | LR: 0.047731 | E:  -57.414826 | E_var:     9.5317 | E_err:   0.035744
[2025-10-31 19:52:58] [Iter  305/2362] R1[42/300]    | LR: 0.047621 | E:  -57.508461 | E_var:     9.5115 | E_err:   0.035540
[2025-10-31 19:53:15] [Iter  306/2362] R1[43/300]    | LR: 0.047508 | E:  -57.448539 | E_var:     9.5741 | E_err:   0.035630
[2025-10-31 19:53:32] [Iter  307/2362] R1[44/300]    | LR: 0.047393 | E:  -57.342218 | E_var:     9.6208 | E_err:   0.036380
[2025-10-31 19:53:49] [Iter  308/2362] R1[45/300]    | LR: 0.047275 | E:  -57.296812 | E_var:    10.8553 | E_err:   0.037958
[2025-10-31 19:54:07] [Iter  309/2362] R1[46/300]    | LR: 0.047155 | E:  -57.463033 | E_var:    10.4632 | E_err:   0.037832
[2025-10-31 19:54:24] [Iter  310/2362] R1[47/300]    | LR: 0.047033 | E:  -57.545686 | E_var:     9.8468 | E_err:   0.037333
[2025-10-31 19:54:41] [Iter  311/2362] R1[48/300]    | LR: 0.046908 | E:  -57.537126 | E_var:     9.3568 | E_err:   0.036029
[2025-10-31 19:54:58] [Iter  312/2362] R1[49/300]    | LR: 0.046780 | E:  -57.579558 | E_var:     9.7857 | E_err:   0.036166
[2025-10-31 19:55:15] [Iter  313/2362] R1[50/300]    | LR: 0.046651 | E:  -57.579047 | E_var:     9.9455 | E_err:   0.037238
[2025-10-31 19:55:33] [Iter  314/2362] R1[51/300]    | LR: 0.046519 | E:  -57.650974 | E_var:    10.0626 | E_err:   0.036364
[2025-10-31 19:55:50] [Iter  315/2362] R1[52/300]    | LR: 0.046384 | E:  -57.629250 | E_var:    10.3240 | E_err:   0.038270
[2025-10-31 19:56:07] [Iter  316/2362] R1[53/300]    | LR: 0.046247 | E:  -57.686640 | E_var:     9.4595 | E_err:   0.035635
[2025-10-31 19:56:24] [Iter  317/2362] R1[54/300]    | LR: 0.046108 | E:  -57.647576 | E_var:     9.6055 | E_err:   0.035746
[2025-10-31 19:56:42] [Iter  318/2362] R1[55/300]    | LR: 0.045967 | E:  -57.717143 | E_var:     9.7133 | E_err:   0.036934
[2025-10-31 19:56:59] [Iter  319/2362] R1[56/300]    | LR: 0.045823 | E:  -57.668684 | E_var:     9.6733 | E_err:   0.036200
[2025-10-31 19:57:16] [Iter  320/2362] R1[57/300]    | LR: 0.045677 | E:  -57.612869 | E_var:    10.2762 | E_err:   0.037906
[2025-10-31 19:57:33] [Iter  321/2362] R1[58/300]    | LR: 0.045529 | E:  -57.547760 | E_var:    10.0392 | E_err:   0.036322
[2025-10-31 19:57:50] [Iter  322/2362] R1[59/300]    | LR: 0.045378 | E:  -57.608835 | E_var:     9.8590 | E_err:   0.036030
[2025-10-31 19:58:08] [Iter  323/2362] R1[60/300]    | LR: 0.045226 | E:  -57.637726 | E_var:    10.0444 | E_err:   0.036411
[2025-10-31 19:58:25] [Iter  324/2362] R1[61/300]    | LR: 0.045071 | E:  -57.683334 | E_var:     9.4374 | E_err:   0.035341
[2025-10-31 19:58:42] [Iter  325/2362] R1[62/300]    | LR: 0.044913 | E:  -57.686689 | E_var:     9.7338 | E_err:   0.036184
[2025-10-31 19:58:59] [Iter  326/2362] R1[63/300]    | LR: 0.044754 | E:  -57.676820 | E_var:    10.3995 | E_err:   0.038071
[2025-10-31 19:59:16] [Iter  327/2362] R1[64/300]    | LR: 0.044592 | E:  -57.497394 | E_var:     9.9793 | E_err:   0.037975
[2025-10-31 19:59:34] [Iter  328/2362] R1[65/300]    | LR: 0.044429 | E:  -57.573872 | E_var:     9.1543 | E_err:   0.036257
[2025-10-31 19:59:51] [Iter  329/2362] R1[66/300]    | LR: 0.044263 | E:  -57.570484 | E_var:     9.8470 | E_err:   0.037211
[2025-10-31 20:00:08] [Iter  330/2362] R1[67/300]    | LR: 0.044095 | E:  -57.649328 | E_var:     9.5372 | E_err:   0.035865
[2025-10-31 20:00:25] [Iter  331/2362] R1[68/300]    | LR: 0.043925 | E:  -57.773450 | E_var:     8.9904 | E_err:   0.036038
[2025-10-31 20:00:43] [Iter  332/2362] R1[69/300]    | LR: 0.043753 | E:  -57.687263 | E_var:     9.3311 | E_err:   0.036020
[2025-10-31 20:01:00] [Iter  333/2362] R1[70/300]    | LR: 0.043579 | E:  -57.733606 | E_var:     9.4103 | E_err:   0.035464
[2025-10-31 20:01:17] [Iter  334/2362] R1[71/300]    | LR: 0.043403 | E:  -57.739034 | E_var:     9.1311 | E_err:   0.034931
[2025-10-31 20:01:34] [Iter  335/2362] R1[72/300]    | LR: 0.043224 | E:  -57.901055 | E_var:     9.4142 | E_err:   0.035302
[2025-10-31 20:01:51] [Iter  336/2362] R1[73/300]    | LR: 0.043044 | E:  -57.817467 | E_var:     9.3744 | E_err:   0.034938
[2025-10-31 20:02:09] [Iter  337/2362] R1[74/300]    | LR: 0.042862 | E:  -57.795868 | E_var:     9.4025 | E_err:   0.036567
[2025-10-31 20:02:26] [Iter  338/2362] R1[75/300]    | LR: 0.042678 | E:  -57.817407 | E_var:     9.1566 | E_err:   0.035016
[2025-10-31 20:02:43] [Iter  339/2362] R1[76/300]    | LR: 0.042492 | E:  -57.872653 | E_var:     9.4152 | E_err:   0.035274
[2025-10-31 20:03:00] [Iter  340/2362] R1[77/300]    | LR: 0.042304 | E:  -57.839778 | E_var:     9.6126 | E_err:   0.036074
[2025-10-31 20:03:18] [Iter  341/2362] R1[78/300]    | LR: 0.042114 | E:  -57.779166 | E_var:     9.8760 | E_err:   0.037107
[2025-10-31 20:03:35] [Iter  342/2362] R1[79/300]    | LR: 0.041922 | E:  -57.819413 | E_var:     9.2591 | E_err:   0.034106
[2025-10-31 20:03:52] [Iter  343/2362] R1[80/300]    | LR: 0.041728 | E:  -57.870701 | E_var:     9.4089 | E_err:   0.035266
[2025-10-31 20:04:09] [Iter  344/2362] R1[81/300]    | LR: 0.041533 | E:  -57.883408 | E_var:    11.8667 | E_err:   0.039028
[2025-10-31 20:04:26] [Iter  345/2362] R1[82/300]    | LR: 0.041336 | E:  -57.924638 | E_var:     9.5479 | E_err:   0.037106
[2025-10-31 20:04:44] [Iter  346/2362] R1[83/300]    | LR: 0.041137 | E:  -57.826677 | E_var:     9.2458 | E_err:   0.036360
[2025-10-31 20:05:01] [Iter  347/2362] R1[84/300]    | LR: 0.040936 | E:  -57.836218 | E_var:     9.2515 | E_err:   0.034444
[2025-10-31 20:05:18] [Iter  348/2362] R1[85/300]    | LR: 0.040733 | E:  -57.786180 | E_var:     9.2964 | E_err:   0.035776
[2025-10-31 20:05:35] [Iter  349/2362] R1[86/300]    | LR: 0.040529 | E:  -57.799482 | E_var:     8.7867 | E_err:   0.033842
[2025-10-31 20:05:53] [Iter  350/2362] R1[87/300]    | LR: 0.040323 | E:  -57.693494 | E_var:     8.7817 | E_err:   0.035342
[2025-10-31 20:06:10] [Iter  351/2362] R1[88/300]    | LR: 0.040115 | E:  -57.623327 | E_var:     8.9616 | E_err:   0.035320
[2025-10-31 20:06:27] [Iter  352/2362] R1[89/300]    | LR: 0.039906 | E:  -57.816587 | E_var:    10.5003 | E_err:   0.036907
[2025-10-31 20:06:44] [Iter  353/2362] R1[90/300]    | LR: 0.039695 | E:  -57.936324 | E_var:     9.1017 | E_err:   0.034073
[2025-10-31 20:07:01] [Iter  354/2362] R1[91/300]    | LR: 0.039482 | E:  -57.933540 | E_var:     9.2303 | E_err:   0.034674
[2025-10-31 20:07:19] [Iter  355/2362] R1[92/300]    | LR: 0.039268 | E:  -57.905445 | E_var:     9.1475 | E_err:   0.035289
[2025-10-31 20:07:36] [Iter  356/2362] R1[93/300]    | LR: 0.039052 | E:  -57.973530 | E_var:     9.6062 | E_err:   0.034774
[2025-10-31 20:07:53] [Iter  357/2362] R1[94/300]    | LR: 0.038835 | E:  -57.905477 | E_var:     9.5387 | E_err:   0.035632
[2025-10-31 20:08:10] [Iter  358/2362] R1[95/300]    | LR: 0.038616 | E:  -57.947549 | E_var:     9.3997 | E_err:   0.035061
[2025-10-31 20:08:27] [Iter  359/2362] R1[96/300]    | LR: 0.038396 | E:  -57.938815 | E_var:     9.5413 | E_err:   0.034657
[2025-10-31 20:08:45] [Iter  360/2362] R1[97/300]    | LR: 0.038174 | E:  -57.923100 | E_var:     9.0580 | E_err:   0.034600
[2025-10-31 20:09:02] [Iter  361/2362] R1[98/300]    | LR: 0.037951 | E:  -57.905437 | E_var:     8.7652 | E_err:   0.034416
[2025-10-31 20:09:19] [Iter  362/2362] R1[99/300]    | LR: 0.037726 | E:  -57.879251 | E_var:     8.7786 | E_err:   0.033860
[2025-10-31 20:09:19] ✓ Checkpoint saved: checkpoint_iter_000250.pkl
[2025-10-31 20:09:36] [Iter  363/2362] R1[100/300]   | LR: 0.037500 | E:  -58.025664 | E_var:     8.7312 | E_err:   0.033994
[2025-10-31 20:09:54] [Iter  364/2362] R1[101/300]   | LR: 0.037273 | E:  -58.061124 | E_var:     9.1621 | E_err:   0.034926
[2025-10-31 20:10:11] [Iter  365/2362] R1[102/300]   | LR: 0.037044 | E:  -58.006144 | E_var:     9.5116 | E_err:   0.034536
[2025-10-31 20:10:28] [Iter  366/2362] R1[103/300]   | LR: 0.036814 | E:  -58.039516 | E_var:     9.5600 | E_err:   0.035847
[2025-10-31 20:10:45] [Iter  367/2362] R1[104/300]   | LR: 0.036583 | E:  -57.959356 | E_var:     9.7262 | E_err:   0.035705
[2025-10-31 20:11:03] [Iter  368/2362] R1[105/300]   | LR: 0.036350 | E:  -58.065281 | E_var:     8.6253 | E_err:   0.034243
[2025-10-31 20:11:20] [Iter  369/2362] R1[106/300]   | LR: 0.036116 | E:  -58.053733 | E_var:     9.1418 | E_err:   0.035585
[2025-10-31 20:11:37] [Iter  370/2362] R1[107/300]   | LR: 0.035881 | E:  -58.088415 | E_var:     9.1249 | E_err:   0.034998
[2025-10-31 20:11:54] [Iter  371/2362] R1[108/300]   | LR: 0.035645 | E:  -58.105849 | E_var:     8.8362 | E_err:   0.032826
[2025-10-31 20:12:11] [Iter  372/2362] R1[109/300]   | LR: 0.035407 | E:  -58.046310 | E_var:     9.0366 | E_err:   0.034285
[2025-10-31 20:12:29] [Iter  373/2362] R1[110/300]   | LR: 0.035169 | E:  -58.071162 | E_var:     8.8477 | E_err:   0.034570
[2025-10-31 20:12:46] [Iter  374/2362] R1[111/300]   | LR: 0.034929 | E:  -58.112297 | E_var:     9.0512 | E_err:   0.035700
[2025-10-31 20:13:03] [Iter  375/2362] R1[112/300]   | LR: 0.034688 | E:  -58.154337 | E_var:     8.8407 | E_err:   0.033840
[2025-10-31 20:13:20] [Iter  376/2362] R1[113/300]   | LR: 0.034446 | E:  -58.016196 | E_var:    10.2292 | E_err:   0.038090
[2025-10-31 20:13:37] [Iter  377/2362] R1[114/300]   | LR: 0.034203 | E:  -57.965204 | E_var:     9.1705 | E_err:   0.035331
[2025-10-31 20:13:55] [Iter  378/2362] R1[115/300]   | LR: 0.033960 | E:  -58.092384 | E_var:     8.7521 | E_err:   0.033869
[2025-10-31 20:14:12] [Iter  379/2362] R1[116/300]   | LR: 0.033715 | E:  -58.146099 | E_var:     8.8541 | E_err:   0.034702
[2025-10-31 20:14:29] [Iter  380/2362] R1[117/300]   | LR: 0.033469 | E:  -58.136112 | E_var:     8.7054 | E_err:   0.033779
[2025-10-31 20:14:46] [Iter  381/2362] R1[118/300]   | LR: 0.033222 | E:  -58.201567 | E_var:     8.8001 | E_err:   0.033834
[2025-10-31 20:15:04] [Iter  382/2362] R1[119/300]   | LR: 0.032974 | E:  -58.177389 | E_var:     9.5828 | E_err:   0.035425
[2025-10-31 20:15:21] [Iter  383/2362] R1[120/300]   | LR: 0.032726 | E:  -58.212777 | E_var:     8.8206 | E_err:   0.034041
[2025-10-31 20:15:38] [Iter  384/2362] R1[121/300]   | LR: 0.032476 | E:  -58.257371 | E_var:     9.3451 | E_err:   0.035574
[2025-10-31 20:15:55] [Iter  385/2362] R1[122/300]   | LR: 0.032226 | E:  -58.241081 | E_var:     9.1626 | E_err:   0.034018
[2025-10-31 20:16:12] [Iter  386/2362] R1[123/300]   | LR: 0.031975 | E:  -58.270600 | E_var:     8.8385 | E_err:   0.033790
[2025-10-31 20:16:30] [Iter  387/2362] R1[124/300]   | LR: 0.031723 | E:  -58.194775 | E_var:     8.4213 | E_err:   0.032718
[2025-10-31 20:16:47] [Iter  388/2362] R1[125/300]   | LR: 0.031471 | E:  -58.217264 | E_var:     8.5024 | E_err:   0.033025
[2025-10-31 20:17:04] [Iter  389/2362] R1[126/300]   | LR: 0.031218 | E:  -58.220938 | E_var:     8.5752 | E_err:   0.033021
[2025-10-31 20:17:21] [Iter  390/2362] R1[127/300]   | LR: 0.030964 | E:  -58.286385 | E_var:     8.9079 | E_err:   0.034737
[2025-10-31 20:17:38] [Iter  391/2362] R1[128/300]   | LR: 0.030709 | E:  -58.253615 | E_var:     8.9012 | E_err:   0.033575
[2025-10-31 20:17:56] [Iter  392/2362] R1[129/300]   | LR: 0.030454 | E:  -58.250086 | E_var:     8.9246 | E_err:   0.033617
[2025-10-31 20:18:13] [Iter  393/2362] R1[130/300]   | LR: 0.030198 | E:  -58.262700 | E_var:     8.9468 | E_err:   0.034047
[2025-10-31 20:18:30] [Iter  394/2362] R1[131/300]   | LR: 0.029942 | E:  -58.243035 | E_var:     8.6347 | E_err:   0.033462
[2025-10-31 20:18:47] [Iter  395/2362] R1[132/300]   | LR: 0.029685 | E:  -58.258832 | E_var:     8.3014 | E_err:   0.033371
[2025-10-31 20:19:05] [Iter  396/2362] R1[133/300]   | LR: 0.029428 | E:  -58.190900 | E_var:     8.6504 | E_err:   0.033647
[2025-10-31 20:19:22] [Iter  397/2362] R1[134/300]   | LR: 0.029170 | E:  -58.252438 | E_var:     8.8077 | E_err:   0.034095
[2025-10-31 20:19:39] [Iter  398/2362] R1[135/300]   | LR: 0.028911 | E:  -58.231370 | E_var:     8.4616 | E_err:   0.033388
[2025-10-31 20:19:56] [Iter  399/2362] R1[136/300]   | LR: 0.028653 | E:  -58.281891 | E_var:     8.5240 | E_err:   0.033595
[2025-10-31 20:20:13] [Iter  400/2362] R1[137/300]   | LR: 0.028393 | E:  -58.303200 | E_var:     9.0188 | E_err:   0.034515
[2025-10-31 20:20:31] [Iter  401/2362] R1[138/300]   | LR: 0.028134 | E:  -58.359552 | E_var:     8.4627 | E_err:   0.033229
[2025-10-31 20:20:48] [Iter  402/2362] R1[139/300]   | LR: 0.027874 | E:  -58.223609 | E_var:     8.7635 | E_err:   0.033365
[2025-10-31 20:21:05] [Iter  403/2362] R1[140/300]   | LR: 0.027614 | E:  -58.348207 | E_var:     8.6756 | E_err:   0.033215
[2025-10-31 20:21:22] [Iter  404/2362] R1[141/300]   | LR: 0.027353 | E:  -58.404005 | E_var:     8.5974 | E_err:   0.033727
[2025-10-31 20:21:40] [Iter  405/2362] R1[142/300]   | LR: 0.027092 | E:  -58.346061 | E_var:     8.7751 | E_err:   0.034408
[2025-10-31 20:21:57] [Iter  406/2362] R1[143/300]   | LR: 0.026831 | E:  -58.312057 | E_var:     9.2214 | E_err:   0.035538
[2025-10-31 20:22:14] [Iter  407/2362] R1[144/300]   | LR: 0.026570 | E:  -58.294023 | E_var:     8.8609 | E_err:   0.033032
[2025-10-31 20:22:31] [Iter  408/2362] R1[145/300]   | LR: 0.026309 | E:  -58.299043 | E_var:     8.6188 | E_err:   0.033222
[2025-10-31 20:22:48] [Iter  409/2362] R1[146/300]   | LR: 0.026047 | E:  -58.403780 | E_var:     8.4921 | E_err:   0.033078
[2025-10-31 20:23:06] [Iter  410/2362] R1[147/300]   | LR: 0.025786 | E:  -58.391112 | E_var:     8.7828 | E_err:   0.033399
[2025-10-31 20:23:23] [Iter  411/2362] R1[148/300]   | LR: 0.025524 | E:  -58.462142 | E_var:     8.2543 | E_err:   0.032065
[2025-10-31 20:23:40] [Iter  412/2362] R1[149/300]   | LR: 0.025262 | E:  -58.486471 | E_var:     8.5899 | E_err:   0.032800
[2025-10-31 20:23:57] [Iter  413/2362] R1[150/300]   | LR: 0.025001 | E:  -58.472852 | E_var:     8.2932 | E_err:   0.033015
[2025-10-31 20:24:15] [Iter  414/2362] R1[151/300]   | LR: 0.024739 | E:  -58.543865 | E_var:     9.1683 | E_err:   0.034412
[2025-10-31 20:24:32] [Iter  415/2362] R1[152/300]   | LR: 0.024477 | E:  -58.553203 | E_var:     8.3901 | E_err:   0.032720
[2025-10-31 20:24:49] [Iter  416/2362] R1[153/300]   | LR: 0.024215 | E:  -58.554595 | E_var:     8.0591 | E_err:   0.032760
[2025-10-31 20:25:06] [Iter  417/2362] R1[154/300]   | LR: 0.023954 | E:  -58.596205 | E_var:     8.3777 | E_err:   0.033004
[2025-10-31 20:25:23] [Iter  418/2362] R1[155/300]   | LR: 0.023692 | E:  -58.499715 | E_var:     8.4235 | E_err:   0.033044
[2025-10-31 20:25:41] [Iter  419/2362] R1[156/300]   | LR: 0.023431 | E:  -58.565201 | E_var:     9.1973 | E_err:   0.034695
[2025-10-31 20:25:58] [Iter  420/2362] R1[157/300]   | LR: 0.023170 | E:  -58.587516 | E_var:     8.1804 | E_err:   0.033189
[2025-10-31 20:26:15] [Iter  421/2362] R1[158/300]   | LR: 0.022909 | E:  -58.578287 | E_var:     8.0271 | E_err:   0.032680
[2025-10-31 20:26:32] [Iter  422/2362] R1[159/300]   | LR: 0.022648 | E:  -58.573799 | E_var:     8.0595 | E_err:   0.032325
[2025-10-31 20:26:50] [Iter  423/2362] R1[160/300]   | LR: 0.022387 | E:  -58.584361 | E_var:     8.2535 | E_err:   0.032930
[2025-10-31 20:27:07] [Iter  424/2362] R1[161/300]   | LR: 0.022127 | E:  -58.659146 | E_var:     8.2758 | E_err:   0.033750
[2025-10-31 20:27:24] [Iter  425/2362] R1[162/300]   | LR: 0.021867 | E:  -58.658592 | E_var:     8.2448 | E_err:   0.032197
[2025-10-31 20:27:41] [Iter  426/2362] R1[163/300]   | LR: 0.021608 | E:  -58.661576 | E_var:     8.1852 | E_err:   0.031590
[2025-10-31 20:27:58] [Iter  427/2362] R1[164/300]   | LR: 0.021348 | E:  -58.703442 | E_var:     8.3669 | E_err:   0.033008
[2025-10-31 20:28:16] [Iter  428/2362] R1[165/300]   | LR: 0.021090 | E:  -58.666359 | E_var:     8.0084 | E_err:   0.031656
[2025-10-31 20:28:33] [Iter  429/2362] R1[166/300]   | LR: 0.020831 | E:  -58.732552 | E_var:     7.9669 | E_err:   0.032257
[2025-10-31 20:28:50] [Iter  430/2362] R1[167/300]   | LR: 0.020573 | E:  -58.736696 | E_var:     8.3194 | E_err:   0.033232
[2025-10-31 20:29:07] [Iter  431/2362] R1[168/300]   | LR: 0.020316 | E:  -58.725630 | E_var:     8.5246 | E_err:   0.033680
[2025-10-31 20:29:24] [Iter  432/2362] R1[169/300]   | LR: 0.020059 | E:  -58.683089 | E_var:     8.0102 | E_err:   0.031424
[2025-10-31 20:29:42] [Iter  433/2362] R1[170/300]   | LR: 0.019803 | E:  -58.762438 | E_var:     8.7135 | E_err:   0.032676
[2025-10-31 20:29:59] [Iter  434/2362] R1[171/300]   | LR: 0.019547 | E:  -58.751276 | E_var:     7.9622 | E_err:   0.032152
[2025-10-31 20:30:16] [Iter  435/2362] R1[172/300]   | LR: 0.019292 | E:  -58.757807 | E_var:     8.2691 | E_err:   0.032952
[2025-10-31 20:30:33] [Iter  436/2362] R1[173/300]   | LR: 0.019037 | E:  -58.746616 | E_var:     8.1783 | E_err:   0.031180
[2025-10-31 20:30:51] [Iter  437/2362] R1[174/300]   | LR: 0.018783 | E:  -58.767617 | E_var:     8.4140 | E_err:   0.032900
[2025-10-31 20:31:08] [Iter  438/2362] R1[175/300]   | LR: 0.018530 | E:  -58.742539 | E_var:     8.2821 | E_err:   0.033111
[2025-10-31 20:31:25] [Iter  439/2362] R1[176/300]   | LR: 0.018278 | E:  -58.770921 | E_var:     8.2378 | E_err:   0.031425
[2025-10-31 20:31:42] [Iter  440/2362] R1[177/300]   | LR: 0.018026 | E:  -58.834343 | E_var:     8.5376 | E_err:   0.034541
[2025-10-31 20:31:59] [Iter  441/2362] R1[178/300]   | LR: 0.017775 | E:  -58.810667 | E_var:     7.8005 | E_err:   0.032404
[2025-10-31 20:32:17] [Iter  442/2362] R1[179/300]   | LR: 0.017525 | E:  -58.867872 | E_var:     8.3850 | E_err:   0.034011
[2025-10-31 20:32:34] [Iter  443/2362] R1[180/300]   | LR: 0.017275 | E:  -58.836321 | E_var:     8.0295 | E_err:   0.032587
[2025-10-31 20:32:51] [Iter  444/2362] R1[181/300]   | LR: 0.017027 | E:  -58.867739 | E_var:     8.0961 | E_err:   0.031832
[2025-10-31 20:33:08] [Iter  445/2362] R1[182/300]   | LR: 0.016779 | E:  -58.843933 | E_var:     8.2889 | E_err:   0.033227
[2025-10-31 20:33:26] [Iter  446/2362] R1[183/300]   | LR: 0.016532 | E:  -58.891986 | E_var:     7.9805 | E_err:   0.032270
[2025-10-31 20:33:43] [Iter  447/2362] R1[184/300]   | LR: 0.016286 | E:  -58.887549 | E_var:     8.1839 | E_err:   0.031774
[2025-10-31 20:34:00] [Iter  448/2362] R1[185/300]   | LR: 0.016041 | E:  -58.824898 | E_var:     8.2272 | E_err:   0.032831
[2025-10-31 20:34:17] [Iter  449/2362] R1[186/300]   | LR: 0.015798 | E:  -58.850629 | E_var:     8.2529 | E_err:   0.032768
[2025-10-31 20:34:34] [Iter  450/2362] R1[187/300]   | LR: 0.015555 | E:  -58.809839 | E_var:     8.3685 | E_err:   0.033920
[2025-10-31 20:34:52] [Iter  451/2362] R1[188/300]   | LR: 0.015313 | E:  -58.919198 | E_var:     8.1134 | E_err:   0.032381
[2025-10-31 20:35:09] [Iter  452/2362] R1[189/300]   | LR: 0.015072 | E:  -58.943706 | E_var:     8.2710 | E_err:   0.032711
[2025-10-31 20:35:26] [Iter  453/2362] R1[190/300]   | LR: 0.014832 | E:  -58.958976 | E_var:     7.9127 | E_err:   0.032500
[2025-10-31 20:35:43] [Iter  454/2362] R1[191/300]   | LR: 0.014594 | E:  -58.993671 | E_var:     8.1040 | E_err:   0.032908
[2025-10-31 20:36:00] [Iter  455/2362] R1[192/300]   | LR: 0.014356 | E:  -59.005202 | E_var:     7.8468 | E_err:   0.031231
[2025-10-31 20:36:18] [Iter  456/2362] R1[193/300]   | LR: 0.014120 | E:  -59.002179 | E_var:     8.0870 | E_err:   0.032280
[2025-10-31 20:36:35] [Iter  457/2362] R1[194/300]   | LR: 0.013885 | E:  -58.959715 | E_var:     8.4278 | E_err:   0.032707
[2025-10-31 20:36:52] [Iter  458/2362] R1[195/300]   | LR: 0.013651 | E:  -59.029383 | E_var:     7.7568 | E_err:   0.031220
[2025-10-31 20:37:09] [Iter  459/2362] R1[196/300]   | LR: 0.013418 | E:  -59.039212 | E_var:     7.6028 | E_err:   0.032152
[2025-10-31 20:37:27] [Iter  460/2362] R1[197/300]   | LR: 0.013187 | E:  -59.058790 | E_var:     8.0704 | E_err:   0.032030
[2025-10-31 20:37:44] [Iter  461/2362] R1[198/300]   | LR: 0.012957 | E:  -59.026723 | E_var:     7.7636 | E_err:   0.031914
[2025-10-31 20:38:01] [Iter  462/2362] R1[199/300]   | LR: 0.012728 | E:  -59.026445 | E_var:     8.0408 | E_err:   0.032787
[2025-10-31 20:38:18] [Iter  463/2362] R1[200/300]   | LR: 0.012501 | E:  -58.910178 | E_var:     8.6501 | E_err:   0.033887
[2025-10-31 20:38:35] [Iter  464/2362] R1[201/300]   | LR: 0.012275 | E:  -59.071679 | E_var:     7.9270 | E_err:   0.032218
[2025-10-31 20:38:53] [Iter  465/2362] R1[202/300]   | LR: 0.012050 | E:  -58.932080 | E_var:     7.7035 | E_err:   0.031232
[2025-10-31 20:39:10] [Iter  466/2362] R1[203/300]   | LR: 0.011827 | E:  -58.918036 | E_var:     7.6570 | E_err:   0.031997
[2025-10-31 20:39:27] [Iter  467/2362] R1[204/300]   | LR: 0.011605 | E:  -58.805884 | E_var:     7.7190 | E_err:   0.031438
[2025-10-31 20:39:44] [Iter  468/2362] R1[205/300]   | LR: 0.011385 | E:  -58.867423 | E_var:     7.4126 | E_err:   0.030391
[2025-10-31 20:40:02] [Iter  469/2362] R1[206/300]   | LR: 0.011166 | E:  -59.000617 | E_var:     7.2803 | E_err:   0.030254
[2025-10-31 20:40:19] [Iter  470/2362] R1[207/300]   | LR: 0.010949 | E:  -59.098582 | E_var:     7.4103 | E_err:   0.030645
[2025-10-31 20:40:36] [Iter  471/2362] R1[208/300]   | LR: 0.010733 | E:  -58.867611 | E_var:     7.4308 | E_err:   0.032547
[2025-10-31 20:40:53] [Iter  472/2362] R1[209/300]   | LR: 0.010519 | E:  -58.977983 | E_var:     7.4607 | E_err:   0.031221
[2025-10-31 20:41:10] [Iter  473/2362] R1[210/300]   | LR: 0.010306 | E:  -59.054280 | E_var:     7.4457 | E_err:   0.031402
[2025-10-31 20:41:28] [Iter  474/2362] R1[211/300]   | LR: 0.010095 | E:  -59.034100 | E_var:     9.2699 | E_err:   0.035014
[2025-10-31 20:41:45] [Iter  475/2362] R1[212/300]   | LR: 0.009886 | E:  -58.965491 | E_var:     7.8109 | E_err:   0.032119
[2025-10-31 20:42:02] [Iter  476/2362] R1[213/300]   | LR: 0.009678 | E:  -59.111830 | E_var:     7.6096 | E_err:   0.032316
[2025-10-31 20:42:19] [Iter  477/2362] R1[214/300]   | LR: 0.009472 | E:  -59.082147 | E_var:     7.8626 | E_err:   0.030647
[2025-10-31 20:42:37] [Iter  478/2362] R1[215/300]   | LR: 0.009268 | E:  -59.041209 | E_var:     8.3160 | E_err:   0.033190
[2025-10-31 20:42:54] [Iter  479/2362] R1[216/300]   | LR: 0.009065 | E:  -59.091640 | E_var:     7.7857 | E_err:   0.031717
[2025-10-31 20:43:11] [Iter  480/2362] R1[217/300]   | LR: 0.008864 | E:  -58.984806 | E_var:     8.3754 | E_err:   0.034134
[2025-10-31 20:43:28] [Iter  481/2362] R1[218/300]   | LR: 0.008665 | E:  -58.967653 | E_var:     8.2443 | E_err:   0.033496
[2025-10-31 20:43:45] [Iter  482/2362] R1[219/300]   | LR: 0.008468 | E:  -58.942359 | E_var:     8.1912 | E_err:   0.033212
[2025-10-31 20:44:03] [Iter  483/2362] R1[220/300]   | LR: 0.008273 | E:  -59.044448 | E_var:     7.4479 | E_err:   0.030331
[2025-10-31 20:44:20] [Iter  484/2362] R1[221/300]   | LR: 0.008079 | E:  -59.143295 | E_var:     7.5737 | E_err:   0.030618
[2025-10-31 20:44:37] [Iter  485/2362] R1[222/300]   | LR: 0.007887 | E:  -59.168267 | E_var:     7.7612 | E_err:   0.031690
[2025-10-31 20:44:54] [Iter  486/2362] R1[223/300]   | LR: 0.007697 | E:  -59.116201 | E_var:     7.7852 | E_err:   0.032042
[2025-10-31 20:45:11] [Iter  487/2362] R1[224/300]   | LR: 0.007509 | E:  -59.022181 | E_var:     8.5444 | E_err:   0.033340
[2025-10-31 20:45:29] [Iter  488/2362] R1[225/300]   | LR: 0.007323 | E:  -59.105608 | E_var:     7.7908 | E_err:   0.031428
[2025-10-31 20:45:46] [Iter  489/2362] R1[226/300]   | LR: 0.007139 | E:  -59.064708 | E_var:     7.9515 | E_err:   0.031670
[2025-10-31 20:46:03] [Iter  490/2362] R1[227/300]   | LR: 0.006957 | E:  -59.097692 | E_var:     8.0476 | E_err:   0.032670
[2025-10-31 20:46:20] [Iter  491/2362] R1[228/300]   | LR: 0.006777 | E:  -59.018773 | E_var:     8.1405 | E_err:   0.033931
[2025-10-31 20:46:38] [Iter  492/2362] R1[229/300]   | LR: 0.006598 | E:  -59.108278 | E_var:     7.2877 | E_err:   0.030082
[2025-10-31 20:46:55] [Iter  493/2362] R1[230/300]   | LR: 0.006422 | E:  -59.100744 | E_var:     7.5861 | E_err:   0.030485
[2025-10-31 20:47:12] [Iter  494/2362] R1[231/300]   | LR: 0.006248 | E:  -59.009031 | E_var:     7.5791 | E_err:   0.031605
[2025-10-31 20:47:29] [Iter  495/2362] R1[232/300]   | LR: 0.006076 | E:  -59.106592 | E_var:     7.7694 | E_err:   0.031626
[2025-10-31 20:47:46] [Iter  496/2362] R1[233/300]   | LR: 0.005906 | E:  -59.175204 | E_var:     7.2715 | E_err:   0.030059
[2025-10-31 20:48:04] [Iter  497/2362] R1[234/300]   | LR: 0.005738 | E:  -59.156090 | E_var:     7.3824 | E_err:   0.029837
[2025-10-31 20:48:21] [Iter  498/2362] R1[235/300]   | LR: 0.005572 | E:  -59.171416 | E_var:     7.5227 | E_err:   0.030949
[2025-10-31 20:48:38] [Iter  499/2362] R1[236/300]   | LR: 0.005409 | E:  -59.159014 | E_var:     7.2922 | E_err:   0.031126
[2025-10-31 20:48:55] [Iter  500/2362] R1[237/300]   | LR: 0.005247 | E:  -59.308565 | E_var:     7.1910 | E_err:   0.029028
[2025-10-31 20:49:13] [Iter  501/2362] R1[238/300]   | LR: 0.005088 | E:  -59.177861 | E_var:     7.3592 | E_err:   0.030847
[2025-10-31 20:49:30] [Iter  502/2362] R1[239/300]   | LR: 0.004930 | E:  -59.203262 | E_var:     7.6896 | E_err:   0.030638
[2025-10-31 20:49:47] [Iter  503/2362] R1[240/300]   | LR: 0.004775 | E:  -59.136407 | E_var:     7.8867 | E_err:   0.032044
[2025-10-31 20:50:04] [Iter  504/2362] R1[241/300]   | LR: 0.004623 | E:  -59.203696 | E_var:     7.2672 | E_err:   0.030820
[2025-10-31 20:50:21] [Iter  505/2362] R1[242/300]   | LR: 0.004472 | E:  -59.242240 | E_var:     7.4828 | E_err:   0.030290
[2025-10-31 20:50:39] [Iter  506/2362] R1[243/300]   | LR: 0.004324 | E:  -59.191058 | E_var:     8.1818 | E_err:   0.033696
[2025-10-31 20:50:56] [Iter  507/2362] R1[244/300]   | LR: 0.004178 | E:  -59.108890 | E_var:     7.9692 | E_err:   0.031376
[2025-10-31 20:51:13] [Iter  508/2362] R1[245/300]   | LR: 0.004034 | E:  -59.113924 | E_var:     7.3804 | E_err:   0.031034
[2025-10-31 20:51:30] [Iter  509/2362] R1[246/300]   | LR: 0.003893 | E:  -59.096834 | E_var:     7.5688 | E_err:   0.030852
[2025-10-31 20:51:48] [Iter  510/2362] R1[247/300]   | LR: 0.003754 | E:  -59.209649 | E_var:     7.2542 | E_err:   0.030390
[2025-10-31 20:52:05] [Iter  511/2362] R1[248/300]   | LR: 0.003617 | E:  -59.264821 | E_var:     7.2646 | E_err:   0.030654
[2025-10-31 20:52:22] [Iter  512/2362] R1[249/300]   | LR: 0.003482 | E:  -59.189252 | E_var:     7.4322 | E_err:   0.030856
[2025-10-31 20:52:39] [Iter  513/2362] R1[250/300]   | LR: 0.003350 | E:  -59.254382 | E_var:     7.4868 | E_err:   0.031077
[2025-10-31 20:52:56] [Iter  514/2362] R1[251/300]   | LR: 0.003221 | E:  -59.237736 | E_var:     7.3690 | E_err:   0.031199
[2025-10-31 20:53:14] [Iter  515/2362] R1[252/300]   | LR: 0.003093 | E:  -59.279699 | E_var:     7.2020 | E_err:   0.029777
[2025-10-31 20:53:31] [Iter  516/2362] R1[253/300]   | LR: 0.002968 | E:  -59.287231 | E_var:     7.5817 | E_err:   0.031235
[2025-10-31 20:53:48] [Iter  517/2362] R1[254/300]   | LR: 0.002846 | E:  -59.303538 | E_var:     7.0940 | E_err:   0.030563
[2025-10-31 20:54:05] [Iter  518/2362] R1[255/300]   | LR: 0.002726 | E:  -59.165765 | E_var:     7.8487 | E_err:   0.031943
[2025-10-31 20:54:23] [Iter  519/2362] R1[256/300]   | LR: 0.002608 | E:  -59.236790 | E_var:     7.3245 | E_err:   0.031660
[2025-10-31 20:54:40] [Iter  520/2362] R1[257/300]   | LR: 0.002493 | E:  -59.250639 | E_var:     7.1736 | E_err:   0.030777
[2025-10-31 20:54:57] [Iter  521/2362] R1[258/300]   | LR: 0.002380 | E:  -59.306811 | E_var:     7.2605 | E_err:   0.030810
[2025-10-31 20:55:14] [Iter  522/2362] R1[259/300]   | LR: 0.002270 | E:  -59.229394 | E_var:     7.6094 | E_err:   0.030590
[2025-10-31 20:55:31] [Iter  523/2362] R1[260/300]   | LR: 0.002162 | E:  -59.298720 | E_var:     7.4546 | E_err:   0.032436
[2025-10-31 20:55:49] [Iter  524/2362] R1[261/300]   | LR: 0.002057 | E:  -59.121736 | E_var:     7.8371 | E_err:   0.031848
[2025-10-31 20:56:06] [Iter  525/2362] R1[262/300]   | LR: 0.001954 | E:  -59.213588 | E_var:     7.7258 | E_err:   0.031631
[2025-10-31 20:56:23] [Iter  526/2362] R1[263/300]   | LR: 0.001854 | E:  -59.144036 | E_var:     8.0478 | E_err:   0.032280
[2025-10-31 20:56:40] [Iter  527/2362] R1[264/300]   | LR: 0.001757 | E:  -59.296300 | E_var:     7.1231 | E_err:   0.029138
[2025-10-31 20:56:58] [Iter  528/2362] R1[265/300]   | LR: 0.001661 | E:  -59.272754 | E_var:     7.5658 | E_err:   0.030937
[2025-10-31 20:57:15] [Iter  529/2362] R1[266/300]   | LR: 0.001569 | E:  -59.280353 | E_var:     7.1285 | E_err:   0.031039
[2025-10-31 20:57:32] [Iter  530/2362] R1[267/300]   | LR: 0.001479 | E:  -59.248175 | E_var:     7.4258 | E_err:   0.031338
[2025-10-31 20:57:49] [Iter  531/2362] R1[268/300]   | LR: 0.001392 | E:  -59.255317 | E_var:     7.8883 | E_err:   0.032171
[2025-10-31 20:58:06] [Iter  532/2362] R1[269/300]   | LR: 0.001307 | E:  -59.110528 | E_var:     8.1073 | E_err:   0.032270
[2025-10-31 20:58:24] [Iter  533/2362] R1[270/300]   | LR: 0.001225 | E:  -59.188968 | E_var:     7.5812 | E_err:   0.031840
[2025-10-31 20:58:41] [Iter  534/2362] R1[271/300]   | LR: 0.001145 | E:  -59.269050 | E_var:     7.4663 | E_err:   0.031648
[2025-10-31 20:58:58] [Iter  535/2362] R1[272/300]   | LR: 0.001068 | E:  -59.173306 | E_var:     7.5326 | E_err:   0.031156
[2025-10-31 20:59:15] [Iter  536/2362] R1[273/300]   | LR: 0.000994 | E:  -59.232680 | E_var:     7.5972 | E_err:   0.031339
[2025-10-31 20:59:33] [Iter  537/2362] R1[274/300]   | LR: 0.000922 | E:  -59.192952 | E_var:     7.3444 | E_err:   0.030568
[2025-10-31 20:59:50] [Iter  538/2362] R1[275/300]   | LR: 0.000853 | E:  -59.317890 | E_var:     7.1731 | E_err:   0.030346
[2025-10-31 21:00:07] [Iter  539/2362] R1[276/300]   | LR: 0.000786 | E:  -59.303173 | E_var:     7.1798 | E_err:   0.031231
[2025-10-31 21:00:24] [Iter  540/2362] R1[277/300]   | LR: 0.000723 | E:  -59.345204 | E_var:     7.3349 | E_err:   0.030256
[2025-10-31 21:00:41] [Iter  541/2362] R1[278/300]   | LR: 0.000662 | E:  -59.308459 | E_var:     6.9126 | E_err:   0.029351
[2025-10-31 21:00:59] [Iter  542/2362] R1[279/300]   | LR: 0.000603 | E:  -59.288190 | E_var:     7.1604 | E_err:   0.030064
[2025-10-31 21:01:16] [Iter  543/2362] R1[280/300]   | LR: 0.000547 | E:  -59.253196 | E_var:     7.2914 | E_err:   0.030804
[2025-10-31 21:01:33] [Iter  544/2362] R1[281/300]   | LR: 0.000494 | E:  -59.331354 | E_var:     7.5197 | E_err:   0.031594
[2025-10-31 21:01:50] [Iter  545/2362] R1[282/300]   | LR: 0.000444 | E:  -59.131008 | E_var:    15.1773 | E_err:   0.074745
[2025-10-31 21:02:07] [Iter  546/2362] R1[283/300]   | LR: 0.000396 | E:  -59.272536 | E_var:     7.1855 | E_err:   0.030621
[2025-10-31 21:02:25] [Iter  547/2362] R1[284/300]   | LR: 0.000351 | E:  -59.296067 | E_var:     7.3059 | E_err:   0.030175
[2025-10-31 21:02:42] [Iter  548/2362] R1[285/300]   | LR: 0.000309 | E:  -59.259974 | E_var:     7.6391 | E_err:   0.030725
[2025-10-31 21:02:59] [Iter  549/2362] R1[286/300]   | LR: 0.000269 | E:  -59.276675 | E_var:     7.2156 | E_err:   0.031269
[2025-10-31 21:03:16] [Iter  550/2362] R1[287/300]   | LR: 0.000232 | E:  -59.249791 | E_var:     7.2491 | E_err:   0.030746
[2025-10-31 21:03:34] [Iter  551/2362] R1[288/300]   | LR: 0.000198 | E:  -59.095801 | E_var:     7.2124 | E_err:   0.031401
[2025-10-31 21:03:51] [Iter  552/2362] R1[289/300]   | LR: 0.000167 | E:  -58.975637 | E_var:     7.4715 | E_err:   0.032450
[2025-10-31 21:04:08] [Iter  553/2362] R1[290/300]   | LR: 0.000138 | E:  -59.031782 | E_var:     7.1165 | E_err:   0.031025
[2025-10-31 21:04:25] [Iter  554/2362] R1[291/300]   | LR: 0.000112 | E:  -59.246790 | E_var:     8.6630 | E_err:   0.035252
[2025-10-31 21:04:42] [Iter  555/2362] R1[292/300]   | LR: 0.000089 | E:  -59.188103 | E_var:     7.1546 | E_err:   0.030389
[2025-10-31 21:05:00] [Iter  556/2362] R1[293/300]   | LR: 0.000068 | E:  -59.199190 | E_var:     7.1385 | E_err:   0.031057
[2025-10-31 21:05:17] [Iter  557/2362] R1[294/300]   | LR: 0.000050 | E:  -59.275009 | E_var:     7.1745 | E_err:   0.028879
[2025-10-31 21:05:34] [Iter  558/2362] R1[295/300]   | LR: 0.000035 | E:  -59.219128 | E_var:     7.0888 | E_err:   0.028549
[2025-10-31 21:05:51] [Iter  559/2362] R1[296/300]   | LR: 0.000023 | E:  -59.223649 | E_var:     7.0727 | E_err:   0.030090
[2025-10-31 21:06:09] [Iter  560/2362] R1[297/300]   | LR: 0.000013 | E:  -59.053515 | E_var:     7.0008 | E_err:   0.030104
[2025-10-31 21:06:26] [Iter  561/2362] R1[298/300]   | LR: 0.000006 | E:  -58.867603 | E_var:     7.0604 | E_err:   0.031154
[2025-10-31 21:06:43] [Iter  562/2362] R1[299/300]   | LR: 0.000002 | E:  -58.982969 | E_var:     7.0117 | E_err:   0.030604
[2025-10-31 21:06:43] 🔄 RESTART #2 | Period: 600
[2025-10-31 21:07:00] [Iter  563/2362] R2[0/600]     | LR: 0.050000 | E:  -59.086878 | E_var:     6.7912 | E_err:   0.029673
[2025-10-31 21:07:17] [Iter  564/2362] R2[1/600]     | LR: 0.050000 | E:  -59.241606 | E_var:     7.0954 | E_err:   0.029806
[2025-10-31 21:07:35] [Iter  565/2362] R2[2/600]     | LR: 0.049999 | E:  -59.268952 | E_var:     6.9133 | E_err:   0.028755
[2025-10-31 21:07:52] [Iter  566/2362] R2[3/600]     | LR: 0.049997 | E:  -59.289471 | E_var:     7.1221 | E_err:   0.030780
[2025-10-31 21:08:09] [Iter  567/2362] R2[4/600]     | LR: 0.049995 | E:  -59.380280 | E_var:     6.7040 | E_err:   0.029159
[2025-10-31 21:08:26] [Iter  568/2362] R2[5/600]     | LR: 0.049991 | E:  -59.273869 | E_var:     6.8716 | E_err:   0.028914
[2025-10-31 21:08:43] [Iter  569/2362] R2[6/600]     | LR: 0.049988 | E:  -59.361751 | E_var:     6.4725 | E_err:   0.028461
[2025-10-31 21:09:01] [Iter  570/2362] R2[7/600]     | LR: 0.049983 | E:  -59.297327 | E_var:     6.6878 | E_err:   0.029941
[2025-10-31 21:09:18] [Iter  571/2362] R2[8/600]     | LR: 0.049978 | E:  -59.371278 | E_var:     6.6050 | E_err:   0.029477
[2025-10-31 21:09:35] [Iter  572/2362] R2[9/600]     | LR: 0.049972 | E:  -59.389855 | E_var:     6.7145 | E_err:   0.029364
[2025-10-31 21:09:52] [Iter  573/2362] R2[10/600]    | LR: 0.049966 | E:  -59.393311 | E_var:     6.6602 | E_err:   0.030324
[2025-10-31 21:10:10] [Iter  574/2362] R2[11/600]    | LR: 0.049959 | E:  -59.382214 | E_var:     6.8504 | E_err:   0.029257
[2025-10-31 21:10:27] [Iter  575/2362] R2[12/600]    | LR: 0.049951 | E:  -59.427175 | E_var:     6.7128 | E_err:   0.028990
[2025-10-31 21:10:44] [Iter  576/2362] R2[13/600]    | LR: 0.049942 | E:  -59.449927 | E_var:     7.1711 | E_err:   0.030310
[2025-10-31 21:11:01] [Iter  577/2362] R2[14/600]    | LR: 0.049933 | E:  -59.332181 | E_var:     6.7482 | E_err:   0.029572
[2025-10-31 21:11:18] [Iter  578/2362] R2[15/600]    | LR: 0.049923 | E:  -59.349738 | E_var:     6.5563 | E_err:   0.029315
[2025-10-31 21:11:36] [Iter  579/2362] R2[16/600]    | LR: 0.049912 | E:  -59.361866 | E_var:     6.8188 | E_err:   0.028258
[2025-10-31 21:11:53] [Iter  580/2362] R2[17/600]    | LR: 0.049901 | E:  -59.401990 | E_var:     6.8353 | E_err:   0.029477
[2025-10-31 21:12:10] [Iter  581/2362] R2[18/600]    | LR: 0.049889 | E:  -59.401629 | E_var:     7.0027 | E_err:   0.028908
[2025-10-31 21:12:27] [Iter  582/2362] R2[19/600]    | LR: 0.049876 | E:  -59.381194 | E_var:     7.0596 | E_err:   0.030602
[2025-10-31 21:12:44] [Iter  583/2362] R2[20/600]    | LR: 0.049863 | E:  -59.414188 | E_var:     6.4347 | E_err:   0.028000
[2025-10-31 21:13:02] [Iter  584/2362] R2[21/600]    | LR: 0.049849 | E:  -59.447949 | E_var:     6.6605 | E_err:   0.029011
[2025-10-31 21:13:19] [Iter  585/2362] R2[22/600]    | LR: 0.049834 | E:  -59.408379 | E_var:     6.8415 | E_err:   0.029732
[2025-10-31 21:13:36] [Iter  586/2362] R2[23/600]    | LR: 0.049819 | E:  -59.450507 | E_var:     6.8197 | E_err:   0.028866
[2025-10-31 21:13:53] [Iter  587/2362] R2[24/600]    | LR: 0.049803 | E:  -59.410703 | E_var:     6.7919 | E_err:   0.029637
[2025-10-31 21:14:11] [Iter  588/2362] R2[25/600]    | LR: 0.049786 | E:  -59.417080 | E_var:     6.5915 | E_err:   0.027881
[2025-10-31 21:14:28] [Iter  589/2362] R2[26/600]    | LR: 0.049769 | E:  -59.417351 | E_var:     6.8564 | E_err:   0.030217
[2025-10-31 21:14:45] [Iter  590/2362] R2[27/600]    | LR: 0.049751 | E:  -59.367835 | E_var:     6.6277 | E_err:   0.029823
[2025-10-31 21:15:02] [Iter  591/2362] R2[28/600]    | LR: 0.049732 | E:  -59.416867 | E_var:     6.6886 | E_err:   0.028993
[2025-10-31 21:15:19] [Iter  592/2362] R2[29/600]    | LR: 0.049712 | E:  -59.469667 | E_var:     7.1644 | E_err:   0.029884
[2025-10-31 21:15:37] [Iter  593/2362] R2[30/600]    | LR: 0.049692 | E:  -59.427564 | E_var:     6.4886 | E_err:   0.027806
[2025-10-31 21:15:54] [Iter  594/2362] R2[31/600]    | LR: 0.049671 | E:  -59.241639 | E_var:     6.8670 | E_err:   0.030876
[2025-10-31 21:16:11] [Iter  595/2362] R2[32/600]    | LR: 0.049650 | E:  -59.425914 | E_var:     6.6549 | E_err:   0.030302
[2025-10-31 21:16:28] [Iter  596/2362] R2[33/600]    | LR: 0.049628 | E:  -59.409131 | E_var:     6.9122 | E_err:   0.030056
[2025-10-31 21:16:46] [Iter  597/2362] R2[34/600]    | LR: 0.049605 | E:  -59.426358 | E_var:     6.9442 | E_err:   0.029885
[2025-10-31 21:17:03] [Iter  598/2362] R2[35/600]    | LR: 0.049581 | E:  -59.363068 | E_var:     6.8907 | E_err:   0.029423
[2025-10-31 21:17:20] [Iter  599/2362] R2[36/600]    | LR: 0.049557 | E:  -59.388965 | E_var:     6.5608 | E_err:   0.028613
[2025-10-31 21:17:37] [Iter  600/2362] R2[37/600]    | LR: 0.049532 | E:  -59.377694 | E_var:     6.7535 | E_err:   0.029145
[2025-10-31 21:17:54] [Iter  601/2362] R2[38/600]    | LR: 0.049507 | E:  -59.485828 | E_var:     6.5255 | E_err:   0.029151
[2025-10-31 21:18:12] [Iter  602/2362] R2[39/600]    | LR: 0.049481 | E:  -59.447669 | E_var:     6.9457 | E_err:   0.029636
[2025-10-31 21:18:29] [Iter  603/2362] R2[40/600]    | LR: 0.049454 | E:  -59.417623 | E_var:     7.0564 | E_err:   0.029913
[2025-10-31 21:18:46] [Iter  604/2362] R2[41/600]    | LR: 0.049426 | E:  -59.479603 | E_var:     6.6081 | E_err:   0.028998
[2025-10-31 21:19:03] [Iter  605/2362] R2[42/600]    | LR: 0.049398 | E:  -59.410526 | E_var:     6.6890 | E_err:   0.028642
[2025-10-31 21:19:21] [Iter  606/2362] R2[43/600]    | LR: 0.049369 | E:  -59.450321 | E_var:     6.7094 | E_err:   0.029859
[2025-10-31 21:19:38] [Iter  607/2362] R2[44/600]    | LR: 0.049339 | E:  -59.443330 | E_var:     6.6463 | E_err:   0.029264
[2025-10-31 21:19:55] [Iter  608/2362] R2[45/600]    | LR: 0.049309 | E:  -59.458715 | E_var:     6.9399 | E_err:   0.029087
[2025-10-31 21:20:12] [Iter  609/2362] R2[46/600]    | LR: 0.049278 | E:  -59.426949 | E_var:     7.0600 | E_err:   0.030212
[2025-10-31 21:20:29] [Iter  610/2362] R2[47/600]    | LR: 0.049247 | E:  -59.343239 | E_var:     7.6147 | E_err:   0.032224
[2025-10-31 21:20:47] [Iter  611/2362] R2[48/600]    | LR: 0.049215 | E:  -59.180135 | E_var:     8.0765 | E_err:   0.033719
[2025-10-31 21:21:04] [Iter  612/2362] R2[49/600]    | LR: 0.049182 | E:  -59.236573 | E_var:     7.3134 | E_err:   0.032311
[2025-10-31 21:21:04] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-31 21:21:21] [Iter  613/2362] R2[50/600]    | LR: 0.049148 | E:  -59.391893 | E_var:     6.9021 | E_err:   0.029703
[2025-10-31 21:21:38] [Iter  614/2362] R2[51/600]    | LR: 0.049114 | E:  -59.416656 | E_var:     6.7952 | E_err:   0.029957
[2025-10-31 21:21:56] [Iter  615/2362] R2[52/600]    | LR: 0.049079 | E:  -59.429363 | E_var:     6.4088 | E_err:   0.029215
[2025-10-31 21:22:13] [Iter  616/2362] R2[53/600]    | LR: 0.049044 | E:  -59.463717 | E_var:     6.6273 | E_err:   0.030684
[2025-10-31 21:22:30] [Iter  617/2362] R2[54/600]    | LR: 0.049007 | E:  -59.477154 | E_var:     6.3930 | E_err:   0.027152
[2025-10-31 21:22:47] [Iter  618/2362] R2[55/600]    | LR: 0.048971 | E:  -59.493325 | E_var:     6.3605 | E_err:   0.028708
[2025-10-31 21:23:04] [Iter  619/2362] R2[56/600]    | LR: 0.048933 | E:  -59.469381 | E_var:     6.5829 | E_err:   0.028995
[2025-10-31 21:23:22] [Iter  620/2362] R2[57/600]    | LR: 0.048895 | E:  -59.534212 | E_var:     6.3380 | E_err:   0.027559
[2025-10-31 21:23:39] [Iter  621/2362] R2[58/600]    | LR: 0.048856 | E:  -59.442089 | E_var:     6.7387 | E_err:   0.029970
[2025-10-31 21:23:56] [Iter  622/2362] R2[59/600]    | LR: 0.048817 | E:  -59.533829 | E_var:     6.6075 | E_err:   0.028686
[2025-10-31 21:24:13] [Iter  623/2362] R2[60/600]    | LR: 0.048776 | E:  -59.475255 | E_var:     6.3158 | E_err:   0.027916
[2025-10-31 21:24:30] [Iter  624/2362] R2[61/600]    | LR: 0.048736 | E:  -59.494536 | E_var:     6.5576 | E_err:   0.028410
[2025-10-31 21:24:48] [Iter  625/2362] R2[62/600]    | LR: 0.048694 | E:  -59.529255 | E_var:     6.5601 | E_err:   0.027893
[2025-10-31 21:25:05] [Iter  626/2362] R2[63/600]    | LR: 0.048652 | E:  -59.513777 | E_var:     6.3543 | E_err:   0.028418
[2025-10-31 21:25:22] [Iter  627/2362] R2[64/600]    | LR: 0.048609 | E:  -59.510198 | E_var:     6.8775 | E_err:   0.030531
[2025-10-31 21:25:39] [Iter  628/2362] R2[65/600]    | LR: 0.048566 | E:  -59.461896 | E_var:     6.4454 | E_err:   0.029253
[2025-10-31 21:25:57] [Iter  629/2362] R2[66/600]    | LR: 0.048522 | E:  -59.443966 | E_var:     6.3905 | E_err:   0.028749
[2025-10-31 21:26:14] [Iter  630/2362] R2[67/600]    | LR: 0.048477 | E:  -59.461828 | E_var:     6.3204 | E_err:   0.028373
[2025-10-31 21:26:31] [Iter  631/2362] R2[68/600]    | LR: 0.048432 | E:  -59.490227 | E_var:     6.4373 | E_err:   0.029455
[2025-10-31 21:26:48] [Iter  632/2362] R2[69/600]    | LR: 0.048386 | E:  -59.368846 | E_var:     6.4027 | E_err:   0.028662
[2025-10-31 21:27:05] [Iter  633/2362] R2[70/600]    | LR: 0.048340 | E:  -59.474534 | E_var:     6.2626 | E_err:   0.028624
[2025-10-31 21:27:23] [Iter  634/2362] R2[71/600]    | LR: 0.048292 | E:  -59.588081 | E_var:     6.7556 | E_err:   0.029533
[2025-10-31 21:27:40] [Iter  635/2362] R2[72/600]    | LR: 0.048244 | E:  -59.514649 | E_var:     6.8503 | E_err:   0.029289
[2025-10-31 21:27:57] [Iter  636/2362] R2[73/600]    | LR: 0.048196 | E:  -59.463464 | E_var:     6.4368 | E_err:   0.027676
[2025-10-31 21:28:14] [Iter  637/2362] R2[74/600]    | LR: 0.048147 | E:  -59.485324 | E_var:     6.3852 | E_err:   0.028295
[2025-10-31 21:28:32] [Iter  638/2362] R2[75/600]    | LR: 0.048097 | E:  -59.487953 | E_var:     6.6162 | E_err:   0.029035
[2025-10-31 21:28:49] [Iter  639/2362] R2[76/600]    | LR: 0.048047 | E:  -59.494647 | E_var:     6.4800 | E_err:   0.027767
[2025-10-31 21:29:06] [Iter  640/2362] R2[77/600]    | LR: 0.047996 | E:  -59.484842 | E_var:     6.5107 | E_err:   0.029675
[2025-10-31 21:29:23] [Iter  641/2362] R2[78/600]    | LR: 0.047944 | E:  -59.495421 | E_var:     6.5858 | E_err:   0.028571
[2025-10-31 21:29:40] [Iter  642/2362] R2[79/600]    | LR: 0.047892 | E:  -59.493783 | E_var:     6.5010 | E_err:   0.028502
[2025-10-31 21:29:58] [Iter  643/2362] R2[80/600]    | LR: 0.047839 | E:  -59.466076 | E_var:     6.4355 | E_err:   0.028770
[2025-10-31 21:30:15] [Iter  644/2362] R2[81/600]    | LR: 0.047785 | E:  -59.455407 | E_var:     6.5944 | E_err:   0.029029
[2025-10-31 21:30:32] [Iter  645/2362] R2[82/600]    | LR: 0.047731 | E:  -59.503781 | E_var:     6.3809 | E_err:   0.028727
[2025-10-31 21:30:49] [Iter  646/2362] R2[83/600]    | LR: 0.047676 | E:  -59.492710 | E_var:     6.8450 | E_err:   0.029834
[2025-10-31 21:31:06] [Iter  647/2362] R2[84/600]    | LR: 0.047621 | E:  -59.556271 | E_var:     6.7882 | E_err:   0.029148
[2025-10-31 21:31:24] [Iter  648/2362] R2[85/600]    | LR: 0.047565 | E:  -59.518792 | E_var:     6.2777 | E_err:   0.027945
[2025-10-31 21:31:41] [Iter  649/2362] R2[86/600]    | LR: 0.047508 | E:  -59.524049 | E_var:     6.4173 | E_err:   0.027761
[2025-10-31 21:31:58] [Iter  650/2362] R2[87/600]    | LR: 0.047451 | E:  -59.495132 | E_var:     6.6547 | E_err:   0.028564
[2025-10-31 21:32:15] [Iter  651/2362] R2[88/600]    | LR: 0.047393 | E:  -59.474424 | E_var:     6.8882 | E_err:   0.029959
[2025-10-31 21:32:33] [Iter  652/2362] R2[89/600]    | LR: 0.047334 | E:  -59.517969 | E_var:     6.9132 | E_err:   0.029690
[2025-10-31 21:32:50] [Iter  653/2362] R2[90/600]    | LR: 0.047275 | E:  -59.548516 | E_var:     6.6905 | E_err:   0.029581
[2025-10-31 21:33:07] [Iter  654/2362] R2[91/600]    | LR: 0.047215 | E:  -59.530176 | E_var:     6.6524 | E_err:   0.028515
[2025-10-31 21:33:24] [Iter  655/2362] R2[92/600]    | LR: 0.047155 | E:  -59.570074 | E_var:     6.7526 | E_err:   0.029541
[2025-10-31 21:33:41] [Iter  656/2362] R2[93/600]    | LR: 0.047094 | E:  -59.521301 | E_var:     6.5104 | E_err:   0.029168
[2025-10-31 21:33:59] [Iter  657/2362] R2[94/600]    | LR: 0.047033 | E:  -59.458146 | E_var:     6.2384 | E_err:   0.028381
[2025-10-31 21:34:16] [Iter  658/2362] R2[95/600]    | LR: 0.046970 | E:  -59.524585 | E_var:     6.3601 | E_err:   0.027487
[2025-10-31 21:34:33] [Iter  659/2362] R2[96/600]    | LR: 0.046908 | E:  -59.567926 | E_var:     6.4770 | E_err:   0.027824
[2025-10-31 21:34:50] [Iter  660/2362] R2[97/600]    | LR: 0.046844 | E:  -59.479153 | E_var:     6.7210 | E_err:   0.029201
[2025-10-31 21:35:07] [Iter  661/2362] R2[98/600]    | LR: 0.046780 | E:  -59.515724 | E_var:     6.7314 | E_err:   0.029135
[2025-10-31 21:35:25] [Iter  662/2362] R2[99/600]    | LR: 0.046716 | E:  -59.442359 | E_var:     7.1168 | E_err:   0.030813
[2025-10-31 21:35:42] [Iter  663/2362] R2[100/600]   | LR: 0.046651 | E:  -59.535086 | E_var:     6.3700 | E_err:   0.028832
[2025-10-31 21:35:59] [Iter  664/2362] R2[101/600]   | LR: 0.046585 | E:  -59.521570 | E_var:     6.3505 | E_err:   0.027889
[2025-10-31 21:36:16] [Iter  665/2362] R2[102/600]   | LR: 0.046519 | E:  -59.556485 | E_var:     6.4023 | E_err:   0.028804
[2025-10-31 21:36:34] [Iter  666/2362] R2[103/600]   | LR: 0.046452 | E:  -59.537225 | E_var:     6.1604 | E_err:   0.028402
[2025-10-31 21:36:51] [Iter  667/2362] R2[104/600]   | LR: 0.046384 | E:  -59.546420 | E_var:     6.6650 | E_err:   0.029771
[2025-10-31 21:37:08] [Iter  668/2362] R2[105/600]   | LR: 0.046316 | E:  -59.539732 | E_var:     6.0862 | E_err:   0.027742
[2025-10-31 21:37:25] [Iter  669/2362] R2[106/600]   | LR: 0.046247 | E:  -59.556847 | E_var:     6.2187 | E_err:   0.027485
[2025-10-31 21:37:42] [Iter  670/2362] R2[107/600]   | LR: 0.046178 | E:  -59.551508 | E_var:     6.1838 | E_err:   0.028038
[2025-10-31 21:38:00] [Iter  671/2362] R2[108/600]   | LR: 0.046108 | E:  -59.602039 | E_var:     6.3880 | E_err:   0.028514
[2025-10-31 21:38:17] [Iter  672/2362] R2[109/600]   | LR: 0.046038 | E:  -59.616369 | E_var:     6.4091 | E_err:   0.028177
[2025-10-31 21:38:34] [Iter  673/2362] R2[110/600]   | LR: 0.045967 | E:  -59.597179 | E_var:     6.2493 | E_err:   0.028016
[2025-10-31 21:38:51] [Iter  674/2362] R2[111/600]   | LR: 0.045895 | E:  -59.606123 | E_var:     6.2626 | E_err:   0.027984
[2025-10-31 21:39:08] [Iter  675/2362] R2[112/600]   | LR: 0.045823 | E:  -59.624743 | E_var:     6.3673 | E_err:   0.029371
[2025-10-31 21:39:26] [Iter  676/2362] R2[113/600]   | LR: 0.045750 | E:  -59.607470 | E_var:     6.3037 | E_err:   0.028379
[2025-10-31 21:39:43] [Iter  677/2362] R2[114/600]   | LR: 0.045677 | E:  -59.577047 | E_var:     6.4323 | E_err:   0.028581
[2025-10-31 21:40:00] [Iter  678/2362] R2[115/600]   | LR: 0.045603 | E:  -59.605800 | E_var:     6.6208 | E_err:   0.028931
[2025-10-31 21:40:17] [Iter  679/2362] R2[116/600]   | LR: 0.045529 | E:  -59.603231 | E_var:     6.4623 | E_err:   0.029039
[2025-10-31 21:40:35] [Iter  680/2362] R2[117/600]   | LR: 0.045454 | E:  -59.580607 | E_var:     6.0724 | E_err:   0.027819
[2025-10-31 21:40:52] [Iter  681/2362] R2[118/600]   | LR: 0.045378 | E:  -59.571112 | E_var:     6.6962 | E_err:   0.030353
[2025-10-31 21:41:09] [Iter  682/2362] R2[119/600]   | LR: 0.045302 | E:  -59.574688 | E_var:     6.1735 | E_err:   0.026652
[2025-10-31 21:41:26] [Iter  683/2362] R2[120/600]   | LR: 0.045226 | E:  -59.614940 | E_var:     6.3523 | E_err:   0.028937
[2025-10-31 21:41:43] [Iter  684/2362] R2[121/600]   | LR: 0.045148 | E:  -59.600249 | E_var:     6.4954 | E_err:   0.028276
[2025-10-31 21:42:01] [Iter  685/2362] R2[122/600]   | LR: 0.045071 | E:  -59.606008 | E_var:     6.3136 | E_err:   0.029130
[2025-10-31 21:42:18] [Iter  686/2362] R2[123/600]   | LR: 0.044992 | E:  -59.526218 | E_var:     6.5181 | E_err:   0.029138
[2025-10-31 21:42:35] [Iter  687/2362] R2[124/600]   | LR: 0.044913 | E:  -59.552026 | E_var:     6.5053 | E_err:   0.029741
[2025-10-31 21:42:52] [Iter  688/2362] R2[125/600]   | LR: 0.044834 | E:  -59.571972 | E_var:     6.6143 | E_err:   0.028347
[2025-10-31 21:43:09] [Iter  689/2362] R2[126/600]   | LR: 0.044754 | E:  -59.606593 | E_var:     6.4707 | E_err:   0.028579
[2025-10-31 21:43:27] [Iter  690/2362] R2[127/600]   | LR: 0.044673 | E:  -59.526296 | E_var:     6.3300 | E_err:   0.028888
[2025-10-31 21:43:44] [Iter  691/2362] R2[128/600]   | LR: 0.044592 | E:  -59.542849 | E_var:     6.2571 | E_err:   0.027500
[2025-10-31 21:44:01] [Iter  692/2362] R2[129/600]   | LR: 0.044511 | E:  -59.533695 | E_var:     6.1479 | E_err:   0.028037
[2025-10-31 21:44:18] [Iter  693/2362] R2[130/600]   | LR: 0.044429 | E:  -59.465040 | E_var:     6.3122 | E_err:   0.029107
[2025-10-31 21:44:36] [Iter  694/2362] R2[131/600]   | LR: 0.044346 | E:  -59.570786 | E_var:     6.5493 | E_err:   0.028724
[2025-10-31 21:44:53] [Iter  695/2362] R2[132/600]   | LR: 0.044263 | E:  -59.588254 | E_var:     6.6576 | E_err:   0.028810
[2025-10-31 21:45:10] [Iter  696/2362] R2[133/600]   | LR: 0.044179 | E:  -59.536915 | E_var:     6.4981 | E_err:   0.028963
[2025-10-31 21:45:27] [Iter  697/2362] R2[134/600]   | LR: 0.044095 | E:  -59.617204 | E_var:     6.1735 | E_err:   0.027927
[2025-10-31 21:45:44] [Iter  698/2362] R2[135/600]   | LR: 0.044010 | E:  -59.598174 | E_var:     6.5542 | E_err:   0.029214
[2025-10-31 21:46:02] [Iter  699/2362] R2[136/600]   | LR: 0.043925 | E:  -59.565741 | E_var:     6.1330 | E_err:   0.028231
[2025-10-31 21:46:19] [Iter  700/2362] R2[137/600]   | LR: 0.043839 | E:  -59.554343 | E_var:     6.1087 | E_err:   0.028570
[2025-10-31 21:46:36] [Iter  701/2362] R2[138/600]   | LR: 0.043753 | E:  -59.572007 | E_var:     6.1747 | E_err:   0.028209
[2025-10-31 21:46:53] [Iter  702/2362] R2[139/600]   | LR: 0.043666 | E:  -59.601034 | E_var:     6.1699 | E_err:   0.028456
[2025-10-31 21:47:11] [Iter  703/2362] R2[140/600]   | LR: 0.043579 | E:  -59.565786 | E_var:     6.0255 | E_err:   0.026971
[2025-10-31 21:47:28] [Iter  704/2362] R2[141/600]   | LR: 0.043491 | E:  -59.576843 | E_var:     6.1410 | E_err:   0.027662
[2025-10-31 21:47:45] [Iter  705/2362] R2[142/600]   | LR: 0.043403 | E:  -59.580320 | E_var:     6.0373 | E_err:   0.027083
[2025-10-31 21:48:02] [Iter  706/2362] R2[143/600]   | LR: 0.043314 | E:  -59.608018 | E_var:     5.8849 | E_err:   0.026823
[2025-10-31 21:48:19] [Iter  707/2362] R2[144/600]   | LR: 0.043224 | E:  -59.590269 | E_var:     6.1174 | E_err:   0.027979
[2025-10-31 21:48:37] [Iter  708/2362] R2[145/600]   | LR: 0.043134 | E:  -59.581291 | E_var:     6.2028 | E_err:   0.027090
[2025-10-31 21:48:54] [Iter  709/2362] R2[146/600]   | LR: 0.043044 | E:  -59.552113 | E_var:     6.3516 | E_err:   0.028112
[2025-10-31 21:49:11] [Iter  710/2362] R2[147/600]   | LR: 0.042953 | E:  -59.655787 | E_var:     6.1472 | E_err:   0.028491
[2025-10-31 21:49:28] [Iter  711/2362] R2[148/600]   | LR: 0.042862 | E:  -59.580931 | E_var:     6.0906 | E_err:   0.028711
[2025-10-31 21:49:46] [Iter  712/2362] R2[149/600]   | LR: 0.042770 | E:  -59.632460 | E_var:     6.4899 | E_err:   0.028614
[2025-10-31 21:50:03] [Iter  713/2362] R2[150/600]   | LR: 0.042678 | E:  -59.623070 | E_var:     5.9977 | E_err:   0.028153
[2025-10-31 21:50:20] [Iter  714/2362] R2[151/600]   | LR: 0.042585 | E:  -59.609326 | E_var:     6.1399 | E_err:   0.028163
[2025-10-31 21:50:37] [Iter  715/2362] R2[152/600]   | LR: 0.042492 | E:  -59.653121 | E_var:     6.2654 | E_err:   0.027924
[2025-10-31 21:50:54] [Iter  716/2362] R2[153/600]   | LR: 0.042398 | E:  -59.588121 | E_var:     6.5024 | E_err:   0.028901
[2025-10-31 21:51:12] [Iter  717/2362] R2[154/600]   | LR: 0.042304 | E:  -59.569294 | E_var:     6.3115 | E_err:   0.028225
[2025-10-31 21:51:29] [Iter  718/2362] R2[155/600]   | LR: 0.042209 | E:  -59.617449 | E_var:     6.4516 | E_err:   0.027699
[2025-10-31 21:51:46] [Iter  719/2362] R2[156/600]   | LR: 0.042114 | E:  -59.621150 | E_var:     6.2279 | E_err:   0.028192
[2025-10-31 21:52:03] [Iter  720/2362] R2[157/600]   | LR: 0.042018 | E:  -59.637623 | E_var:     6.2580 | E_err:   0.028037
[2025-10-31 21:52:20] [Iter  721/2362] R2[158/600]   | LR: 0.041922 | E:  -59.598545 | E_var:     6.1045 | E_err:   0.027706
[2025-10-31 21:52:38] [Iter  722/2362] R2[159/600]   | LR: 0.041825 | E:  -59.565199 | E_var:     6.4103 | E_err:   0.028597
[2025-10-31 21:52:55] [Iter  723/2362] R2[160/600]   | LR: 0.041728 | E:  -59.618035 | E_var:     6.3549 | E_err:   0.028253
[2025-10-31 21:53:12] [Iter  724/2362] R2[161/600]   | LR: 0.041631 | E:  -59.605613 | E_var:     6.7237 | E_err:   0.028834
[2025-10-31 21:53:29] [Iter  725/2362] R2[162/600]   | LR: 0.041533 | E:  -59.587390 | E_var:     6.9284 | E_err:   0.029460
[2025-10-31 21:53:47] [Iter  726/2362] R2[163/600]   | LR: 0.041435 | E:  -59.611101 | E_var:     6.4485 | E_err:   0.028145
[2025-10-31 21:54:04] [Iter  727/2362] R2[164/600]   | LR: 0.041336 | E:  -59.575783 | E_var:     6.4717 | E_err:   0.028791
[2025-10-31 21:54:21] [Iter  728/2362] R2[165/600]   | LR: 0.041236 | E:  -59.551532 | E_var:     6.7351 | E_err:   0.029833
[2025-10-31 21:54:38] [Iter  729/2362] R2[166/600]   | LR: 0.041137 | E:  -59.593514 | E_var:     6.0665 | E_err:   0.027390
[2025-10-31 21:54:55] [Iter  730/2362] R2[167/600]   | LR: 0.041036 | E:  -59.589872 | E_var:     6.0780 | E_err:   0.028211
[2025-10-31 21:55:13] [Iter  731/2362] R2[168/600]   | LR: 0.040936 | E:  -59.565404 | E_var:     5.9815 | E_err:   0.027540
[2025-10-31 21:55:30] [Iter  732/2362] R2[169/600]   | LR: 0.040835 | E:  -59.594976 | E_var:     5.8769 | E_err:   0.027315
[2025-10-31 21:55:47] [Iter  733/2362] R2[170/600]   | LR: 0.040733 | E:  -59.518317 | E_var:     5.8524 | E_err:   0.026939
[2025-10-31 21:56:04] [Iter  734/2362] R2[171/600]   | LR: 0.040631 | E:  -59.598899 | E_var:     6.2351 | E_err:   0.028143
[2025-10-31 21:56:22] [Iter  735/2362] R2[172/600]   | LR: 0.040529 | E:  -59.671954 | E_var:     6.2381 | E_err:   0.028552
[2025-10-31 21:56:39] [Iter  736/2362] R2[173/600]   | LR: 0.040426 | E:  -59.660166 | E_var:     6.1160 | E_err:   0.028043
[2025-10-31 21:56:56] [Iter  737/2362] R2[174/600]   | LR: 0.040323 | E:  -59.663570 | E_var:     6.2173 | E_err:   0.028602
[2025-10-31 21:57:13] [Iter  738/2362] R2[175/600]   | LR: 0.040219 | E:  -59.625034 | E_var:     6.0579 | E_err:   0.027172
[2025-10-31 21:57:30] [Iter  739/2362] R2[176/600]   | LR: 0.040115 | E:  -59.630005 | E_var:     6.3285 | E_err:   0.028182
[2025-10-31 21:57:48] [Iter  740/2362] R2[177/600]   | LR: 0.040011 | E:  -59.609076 | E_var:     6.3437 | E_err:   0.028770
[2025-10-31 21:58:05] [Iter  741/2362] R2[178/600]   | LR: 0.039906 | E:  -59.570892 | E_var:     6.3803 | E_err:   0.028630
[2025-10-31 21:58:22] [Iter  742/2362] R2[179/600]   | LR: 0.039801 | E:  -59.664925 | E_var:     6.6085 | E_err:   0.028051
[2025-10-31 21:58:39] [Iter  743/2362] R2[180/600]   | LR: 0.039695 | E:  -59.584905 | E_var:     6.7601 | E_err:   0.028882
[2025-10-31 21:58:56] [Iter  744/2362] R2[181/600]   | LR: 0.039589 | E:  -59.502849 | E_var:     6.8758 | E_err:   0.029730
[2025-10-31 21:59:14] [Iter  745/2362] R2[182/600]   | LR: 0.039482 | E:  -59.591385 | E_var:     6.4718 | E_err:   0.028796
[2025-10-31 21:59:31] [Iter  746/2362] R2[183/600]   | LR: 0.039375 | E:  -59.628635 | E_var:     6.6025 | E_err:   0.030187
[2025-10-31 21:59:48] [Iter  747/2362] R2[184/600]   | LR: 0.039268 | E:  -59.614261 | E_var:     6.5593 | E_err:   0.028591
[2025-10-31 22:00:05] [Iter  748/2362] R2[185/600]   | LR: 0.039160 | E:  -59.591778 | E_var:     6.4336 | E_err:   0.028334
[2025-10-31 22:00:23] [Iter  749/2362] R2[186/600]   | LR: 0.039052 | E:  -59.637075 | E_var:     6.5588 | E_err:   0.029003
[2025-10-31 22:00:40] [Iter  750/2362] R2[187/600]   | LR: 0.038944 | E:  -59.644619 | E_var:     6.2274 | E_err:   0.028534
[2025-10-31 22:00:57] [Iter  751/2362] R2[188/600]   | LR: 0.038835 | E:  -59.662307 | E_var:     6.0949 | E_err:   0.028362
[2025-10-31 22:01:14] [Iter  752/2362] R2[189/600]   | LR: 0.038726 | E:  -59.663868 | E_var:     5.9396 | E_err:   0.027373
[2025-10-31 22:01:31] [Iter  753/2362] R2[190/600]   | LR: 0.038616 | E:  -59.588983 | E_var:     6.2817 | E_err:   0.027765
[2025-10-31 22:01:49] [Iter  754/2362] R2[191/600]   | LR: 0.038506 | E:  -59.620061 | E_var:     6.0146 | E_err:   0.027390
[2025-10-31 22:02:06] [Iter  755/2362] R2[192/600]   | LR: 0.038396 | E:  -59.662964 | E_var:     5.9898 | E_err:   0.027510
[2025-10-31 22:02:23] [Iter  756/2362] R2[193/600]   | LR: 0.038285 | E:  -59.658723 | E_var:     6.2330 | E_err:   0.028943
[2025-10-31 22:02:40] [Iter  757/2362] R2[194/600]   | LR: 0.038174 | E:  -59.714925 | E_var:     5.9724 | E_err:   0.028267
[2025-10-31 22:02:58] [Iter  758/2362] R2[195/600]   | LR: 0.038063 | E:  -59.659955 | E_var:     6.4469 | E_err:   0.028738
[2025-10-31 22:03:15] [Iter  759/2362] R2[196/600]   | LR: 0.037951 | E:  -59.664682 | E_var:     6.4831 | E_err:   0.028204
[2025-10-31 22:03:32] [Iter  760/2362] R2[197/600]   | LR: 0.037839 | E:  -59.678990 | E_var:     6.0322 | E_err:   0.026944
[2025-10-31 22:03:49] [Iter  761/2362] R2[198/600]   | LR: 0.037726 | E:  -59.666148 | E_var:     6.0237 | E_err:   0.027563
[2025-10-31 22:04:06] [Iter  762/2362] R2[199/600]   | LR: 0.037613 | E:  -59.689458 | E_var:     6.0838 | E_err:   0.027389
[2025-10-31 22:04:24] [Iter  763/2362] R2[200/600]   | LR: 0.037500 | E:  -59.640882 | E_var:     5.6518 | E_err:   0.026522
[2025-10-31 22:04:41] [Iter  764/2362] R2[201/600]   | LR: 0.037387 | E:  -59.672011 | E_var:     5.8179 | E_err:   0.027599
[2025-10-31 22:04:58] [Iter  765/2362] R2[202/600]   | LR: 0.037273 | E:  -59.639263 | E_var:     6.0015 | E_err:   0.026308
[2025-10-31 22:05:15] [Iter  766/2362] R2[203/600]   | LR: 0.037159 | E:  -59.668153 | E_var:     5.8803 | E_err:   0.027672
[2025-10-31 22:05:32] [Iter  767/2362] R2[204/600]   | LR: 0.037044 | E:  -59.701071 | E_var:     6.0698 | E_err:   0.027255
[2025-10-31 22:05:50] [Iter  768/2362] R2[205/600]   | LR: 0.036929 | E:  -59.712623 | E_var:     6.0758 | E_err:   0.027125
[2025-10-31 22:06:07] [Iter  769/2362] R2[206/600]   | LR: 0.036814 | E:  -59.665843 | E_var:     6.4789 | E_err:   0.027984
[2025-10-31 22:06:24] [Iter  770/2362] R2[207/600]   | LR: 0.036699 | E:  -59.689562 | E_var:     6.1507 | E_err:   0.028418
[2025-10-31 22:06:41] [Iter  771/2362] R2[208/600]   | LR: 0.036583 | E:  -59.697324 | E_var:     6.1665 | E_err:   0.028222
[2025-10-31 22:06:59] [Iter  772/2362] R2[209/600]   | LR: 0.036467 | E:  -59.670127 | E_var:     6.2354 | E_err:   0.028020
[2025-10-31 22:07:16] [Iter  773/2362] R2[210/600]   | LR: 0.036350 | E:  -59.663555 | E_var:     5.9541 | E_err:   0.027083
[2025-10-31 22:07:33] [Iter  774/2362] R2[211/600]   | LR: 0.036233 | E:  -59.637811 | E_var:     6.3992 | E_err:   0.029106
[2025-10-31 22:07:50] [Iter  775/2362] R2[212/600]   | LR: 0.036116 | E:  -59.648508 | E_var:     6.3207 | E_err:   0.027628
[2025-10-31 22:08:07] [Iter  776/2362] R2[213/600]   | LR: 0.035999 | E:  -59.709834 | E_var:     6.2251 | E_err:   0.028369
[2025-10-31 22:08:25] [Iter  777/2362] R2[214/600]   | LR: 0.035881 | E:  -59.674956 | E_var:     6.7002 | E_err:   0.028925
[2025-10-31 22:08:42] [Iter  778/2362] R2[215/600]   | LR: 0.035763 | E:  -59.678139 | E_var:     6.0197 | E_err:   0.028693
[2025-10-31 22:08:59] [Iter  779/2362] R2[216/600]   | LR: 0.035645 | E:  -59.647788 | E_var:     6.0738 | E_err:   0.027547
[2025-10-31 22:09:16] [Iter  780/2362] R2[217/600]   | LR: 0.035526 | E:  -59.620777 | E_var:     6.2801 | E_err:   0.028462
[2025-10-31 22:09:33] [Iter  781/2362] R2[218/600]   | LR: 0.035407 | E:  -59.662607 | E_var:     6.2793 | E_err:   0.028010
[2025-10-31 22:09:51] [Iter  782/2362] R2[219/600]   | LR: 0.035288 | E:  -59.691590 | E_var:     5.9771 | E_err:   0.027674
[2025-10-31 22:10:08] [Iter  783/2362] R2[220/600]   | LR: 0.035169 | E:  -59.710785 | E_var:     5.8273 | E_err:   0.026994
[2025-10-31 22:10:25] [Iter  784/2362] R2[221/600]   | LR: 0.035049 | E:  -59.695567 | E_var:     6.0966 | E_err:   0.028364
[2025-10-31 22:10:42] [Iter  785/2362] R2[222/600]   | LR: 0.034929 | E:  -59.693841 | E_var:     5.8033 | E_err:   0.026970
[2025-10-31 22:11:00] [Iter  786/2362] R2[223/600]   | LR: 0.034809 | E:  -59.695999 | E_var:     5.9474 | E_err:   0.027131
[2025-10-31 22:11:17] [Iter  787/2362] R2[224/600]   | LR: 0.034688 | E:  -59.660297 | E_var:     6.0734 | E_err:   0.027773
[2025-10-31 22:11:34] [Iter  788/2362] R2[225/600]   | LR: 0.034567 | E:  -59.738017 | E_var:     6.1258 | E_err:   0.027602
[2025-10-31 22:11:51] [Iter  789/2362] R2[226/600]   | LR: 0.034446 | E:  -59.686307 | E_var:     6.0615 | E_err:   0.028134
[2025-10-31 22:12:08] [Iter  790/2362] R2[227/600]   | LR: 0.034325 | E:  -59.710439 | E_var:     6.3196 | E_err:   0.028307
[2025-10-31 22:12:26] [Iter  791/2362] R2[228/600]   | LR: 0.034203 | E:  -59.638300 | E_var:     6.3753 | E_err:   0.027760
[2025-10-31 22:12:43] [Iter  792/2362] R2[229/600]   | LR: 0.034082 | E:  -59.689113 | E_var:     6.2344 | E_err:   0.028470
[2025-10-31 22:13:00] [Iter  793/2362] R2[230/600]   | LR: 0.033960 | E:  -59.672520 | E_var:     6.0944 | E_err:   0.028230
[2025-10-31 22:13:17] [Iter  794/2362] R2[231/600]   | LR: 0.033837 | E:  -59.681344 | E_var:     6.1142 | E_err:   0.027965
[2025-10-31 22:13:34] [Iter  795/2362] R2[232/600]   | LR: 0.033715 | E:  -59.685510 | E_var:     6.0818 | E_err:   0.026970
[2025-10-31 22:13:52] [Iter  796/2362] R2[233/600]   | LR: 0.033592 | E:  -59.747956 | E_var:     6.0905 | E_err:   0.028103
[2025-10-31 22:14:09] [Iter  797/2362] R2[234/600]   | LR: 0.033469 | E:  -59.709706 | E_var:     6.1264 | E_err:   0.028752
[2025-10-31 22:14:26] [Iter  798/2362] R2[235/600]   | LR: 0.033346 | E:  -59.731470 | E_var:     6.1059 | E_err:   0.028095
[2025-10-31 22:14:43] [Iter  799/2362] R2[236/600]   | LR: 0.033222 | E:  -59.693653 | E_var:     5.8520 | E_err:   0.027736
[2025-10-31 22:15:01] [Iter  800/2362] R2[237/600]   | LR: 0.033098 | E:  -59.699720 | E_var:     5.6714 | E_err:   0.026419
[2025-10-31 22:15:18] [Iter  801/2362] R2[238/600]   | LR: 0.032974 | E:  -59.697633 | E_var:     5.7334 | E_err:   0.027555
[2025-10-31 22:15:35] [Iter  802/2362] R2[239/600]   | LR: 0.032850 | E:  -59.656726 | E_var:     5.9895 | E_err:   0.027594
[2025-10-31 22:15:52] [Iter  803/2362] R2[240/600]   | LR: 0.032726 | E:  -59.699315 | E_var:     5.8749 | E_err:   0.026482
[2025-10-31 22:16:09] [Iter  804/2362] R2[241/600]   | LR: 0.032601 | E:  -59.702577 | E_var:     5.8129 | E_err:   0.027057
[2025-10-31 22:16:27] [Iter  805/2362] R2[242/600]   | LR: 0.032476 | E:  -59.686784 | E_var:     5.6690 | E_err:   0.026012
[2025-10-31 22:16:44] [Iter  806/2362] R2[243/600]   | LR: 0.032351 | E:  -59.721648 | E_var:     5.9176 | E_err:   0.027117
[2025-10-31 22:17:01] [Iter  807/2362] R2[244/600]   | LR: 0.032226 | E:  -59.688288 | E_var:     5.7145 | E_err:   0.026520
[2025-10-31 22:17:18] [Iter  808/2362] R2[245/600]   | LR: 0.032101 | E:  -59.686448 | E_var:     5.9534 | E_err:   0.028241
[2025-10-31 22:17:36] [Iter  809/2362] R2[246/600]   | LR: 0.031975 | E:  -59.734762 | E_var:     5.8851 | E_err:   0.027505
[2025-10-31 22:17:53] [Iter  810/2362] R2[247/600]   | LR: 0.031849 | E:  -59.678697 | E_var:     5.9498 | E_err:   0.027505
[2025-10-31 22:18:10] [Iter  811/2362] R2[248/600]   | LR: 0.031723 | E:  -59.703286 | E_var:     5.7287 | E_err:   0.027101
[2025-10-31 22:18:27] [Iter  812/2362] R2[249/600]   | LR: 0.031597 | E:  -59.751012 | E_var:     6.0226 | E_err:   0.027046
[2025-10-31 22:18:44] [Iter  813/2362] R2[250/600]   | LR: 0.031471 | E:  -59.734933 | E_var:     5.7915 | E_err:   0.026914
[2025-10-31 22:19:02] [Iter  814/2362] R2[251/600]   | LR: 0.031344 | E:  -59.703507 | E_var:     5.8897 | E_err:   0.026693
[2025-10-31 22:19:19] [Iter  815/2362] R2[252/600]   | LR: 0.031218 | E:  -59.717773 | E_var:     6.1732 | E_err:   0.028023
[2025-10-31 22:19:36] [Iter  816/2362] R2[253/600]   | LR: 0.031091 | E:  -59.619658 | E_var:     5.7152 | E_err:   0.027080
[2025-10-31 22:19:53] [Iter  817/2362] R2[254/600]   | LR: 0.030964 | E:  -59.695897 | E_var:     5.7601 | E_err:   0.027161
[2025-10-31 22:20:11] [Iter  818/2362] R2[255/600]   | LR: 0.030837 | E:  -59.752830 | E_var:     5.8919 | E_err:   0.026897
[2025-10-31 22:20:28] [Iter  819/2362] R2[256/600]   | LR: 0.030709 | E:  -59.681924 | E_var:     5.8543 | E_err:   0.026926
[2025-10-31 22:20:45] [Iter  820/2362] R2[257/600]   | LR: 0.030582 | E:  -59.728958 | E_var:     6.1578 | E_err:   0.027892
[2025-10-31 22:21:02] [Iter  821/2362] R2[258/600]   | LR: 0.030454 | E:  -59.707392 | E_var:     6.3059 | E_err:   0.028584
[2025-10-31 22:21:19] [Iter  822/2362] R2[259/600]   | LR: 0.030326 | E:  -59.755246 | E_var:     6.1459 | E_err:   0.027213
[2025-10-31 22:21:37] [Iter  823/2362] R2[260/600]   | LR: 0.030198 | E:  -59.733389 | E_var:     6.0180 | E_err:   0.027575
[2025-10-31 22:21:54] [Iter  824/2362] R2[261/600]   | LR: 0.030070 | E:  -59.751939 | E_var:     5.9758 | E_err:   0.027340
[2025-10-31 22:22:11] [Iter  825/2362] R2[262/600]   | LR: 0.029942 | E:  -59.747024 | E_var:     5.6668 | E_err:   0.026266
[2025-10-31 22:22:28] [Iter  826/2362] R2[263/600]   | LR: 0.029813 | E:  -59.722532 | E_var:     5.8555 | E_err:   0.026711
[2025-10-31 22:22:45] [Iter  827/2362] R2[264/600]   | LR: 0.029685 | E:  -59.650022 | E_var:     5.9260 | E_err:   0.028606
[2025-10-31 22:23:03] [Iter  828/2362] R2[265/600]   | LR: 0.029556 | E:  -59.762599 | E_var:     5.7935 | E_err:   0.027826
[2025-10-31 22:23:20] [Iter  829/2362] R2[266/600]   | LR: 0.029428 | E:  -59.724470 | E_var:     5.8018 | E_err:   0.026965
[2025-10-31 22:23:37] [Iter  830/2362] R2[267/600]   | LR: 0.029299 | E:  -59.747268 | E_var:     5.8311 | E_err:   0.027417
[2025-10-31 22:23:54] [Iter  831/2362] R2[268/600]   | LR: 0.029170 | E:  -59.674512 | E_var:     5.8402 | E_err:   0.026944
[2025-10-31 22:24:12] [Iter  832/2362] R2[269/600]   | LR: 0.029041 | E:  -59.733195 | E_var:     5.8832 | E_err:   0.027653
[2025-10-31 22:24:29] [Iter  833/2362] R2[270/600]   | LR: 0.028911 | E:  -59.657728 | E_var:     5.9989 | E_err:   0.028413
[2025-10-31 22:24:46] [Iter  834/2362] R2[271/600]   | LR: 0.028782 | E:  -59.738970 | E_var:     5.7718 | E_err:   0.027072
[2025-10-31 22:25:03] [Iter  835/2362] R2[272/600]   | LR: 0.028653 | E:  -59.725448 | E_var:     6.1216 | E_err:   0.028025
[2025-10-31 22:25:20] [Iter  836/2362] R2[273/600]   | LR: 0.028523 | E:  -59.775191 | E_var:     6.0770 | E_err:   0.027043
[2025-10-31 22:25:38] [Iter  837/2362] R2[274/600]   | LR: 0.028393 | E:  -59.769820 | E_var:     5.9587 | E_err:   0.027161
[2025-10-31 22:25:55] [Iter  838/2362] R2[275/600]   | LR: 0.028264 | E:  -59.762338 | E_var:     5.6060 | E_err:   0.026487
[2025-10-31 22:26:12] [Iter  839/2362] R2[276/600]   | LR: 0.028134 | E:  -59.714347 | E_var:     6.0376 | E_err:   0.026906
[2025-10-31 22:26:29] [Iter  840/2362] R2[277/600]   | LR: 0.028004 | E:  -59.761240 | E_var:     6.0033 | E_err:   0.028133
[2025-10-31 22:26:46] [Iter  841/2362] R2[278/600]   | LR: 0.027874 | E:  -59.757524 | E_var:     5.8565 | E_err:   0.027127
[2025-10-31 22:27:04] [Iter  842/2362] R2[279/600]   | LR: 0.027744 | E:  -59.735278 | E_var:     5.9849 | E_err:   0.027997
[2025-10-31 22:27:21] [Iter  843/2362] R2[280/600]   | LR: 0.027614 | E:  -59.697304 | E_var:     5.7690 | E_err:   0.026732
[2025-10-31 22:27:38] [Iter  844/2362] R2[281/600]   | LR: 0.027483 | E:  -59.760133 | E_var:     5.9154 | E_err:   0.027549
[2025-10-31 22:27:55] [Iter  845/2362] R2[282/600]   | LR: 0.027353 | E:  -59.766152 | E_var:     6.0632 | E_err:   0.027781
[2025-10-31 22:28:12] [Iter  846/2362] R2[283/600]   | LR: 0.027223 | E:  -59.669817 | E_var:     6.6213 | E_err:   0.029555
[2025-10-31 22:28:30] [Iter  847/2362] R2[284/600]   | LR: 0.027092 | E:  -59.702828 | E_var:     6.2960 | E_err:   0.028851
[2025-10-31 22:28:47] [Iter  848/2362] R2[285/600]   | LR: 0.026962 | E:  -59.710424 | E_var:     5.7792 | E_err:   0.026801
[2025-10-31 22:29:04] [Iter  849/2362] R2[286/600]   | LR: 0.026831 | E:  -59.739921 | E_var:     6.1910 | E_err:   0.028257
[2025-10-31 22:29:21] [Iter  850/2362] R2[287/600]   | LR: 0.026701 | E:  -59.747341 | E_var:     6.0146 | E_err:   0.027637
[2025-10-31 22:29:39] [Iter  851/2362] R2[288/600]   | LR: 0.026570 | E:  -59.721286 | E_var:     6.2862 | E_err:   0.028350
[2025-10-31 22:29:56] [Iter  852/2362] R2[289/600]   | LR: 0.026440 | E:  -59.699317 | E_var:     6.1650 | E_err:   0.028343
[2025-10-31 22:30:13] [Iter  853/2362] R2[290/600]   | LR: 0.026309 | E:  -59.740176 | E_var:     6.1233 | E_err:   0.028405
[2025-10-31 22:30:30] [Iter  854/2362] R2[291/600]   | LR: 0.026178 | E:  -59.698659 | E_var:     5.9413 | E_err:   0.027739
[2025-10-31 22:30:47] [Iter  855/2362] R2[292/600]   | LR: 0.026047 | E:  -59.727698 | E_var:     6.0869 | E_err:   0.027245
[2025-10-31 22:31:05] [Iter  856/2362] R2[293/600]   | LR: 0.025917 | E:  -59.745439 | E_var:     6.0092 | E_err:   0.027474
[2025-10-31 22:31:22] [Iter  857/2362] R2[294/600]   | LR: 0.025786 | E:  -59.704694 | E_var:     5.9297 | E_err:   0.027017
[2025-10-31 22:31:39] [Iter  858/2362] R2[295/600]   | LR: 0.025655 | E:  -59.766158 | E_var:     5.8447 | E_err:   0.027504
[2025-10-31 22:31:56] [Iter  859/2362] R2[296/600]   | LR: 0.025524 | E:  -59.732642 | E_var:     5.8947 | E_err:   0.028066
[2025-10-31 22:32:13] [Iter  860/2362] R2[297/600]   | LR: 0.025393 | E:  -59.727035 | E_var:     5.7062 | E_err:   0.026904
[2025-10-31 22:32:31] [Iter  861/2362] R2[298/600]   | LR: 0.025262 | E:  -59.750602 | E_var:     5.6440 | E_err:   0.027066
[2025-10-31 22:32:48] [Iter  862/2362] R2[299/600]   | LR: 0.025131 | E:  -59.795878 | E_var:     5.8178 | E_err:   0.027040
[2025-10-31 22:32:48] ✓ Checkpoint saved: checkpoint_iter_000750.pkl
[2025-10-31 22:33:05] [Iter  863/2362] R2[300/600]   | LR: 0.025001 | E:  -59.781713 | E_var:     5.8303 | E_err:   0.028426
[2025-10-31 22:33:22] [Iter  864/2362] R2[301/600]   | LR: 0.024870 | E:  -59.735095 | E_var:     5.5592 | E_err:   0.025913
[2025-10-31 22:33:40] [Iter  865/2362] R2[302/600]   | LR: 0.024739 | E:  -59.728674 | E_var:     5.7960 | E_err:   0.026735
[2025-10-31 22:33:57] [Iter  866/2362] R2[303/600]   | LR: 0.024608 | E:  -59.724388 | E_var:     6.2766 | E_err:   0.028870
[2025-10-31 22:34:14] [Iter  867/2362] R2[304/600]   | LR: 0.024477 | E:  -59.726647 | E_var:     6.3113 | E_err:   0.028866
[2025-10-31 22:34:31] [Iter  868/2362] R2[305/600]   | LR: 0.024346 | E:  -59.679485 | E_var:     6.0034 | E_err:   0.027628
[2025-10-31 22:34:48] [Iter  869/2362] R2[306/600]   | LR: 0.024215 | E:  -59.785195 | E_var:     5.7370 | E_err:   0.026764
[2025-10-31 22:35:06] [Iter  870/2362] R2[307/600]   | LR: 0.024084 | E:  -59.736451 | E_var:     5.9447 | E_err:   0.027560
[2025-10-31 22:35:23] [Iter  871/2362] R2[308/600]   | LR: 0.023954 | E:  -59.751464 | E_var:     5.7514 | E_err:   0.026779
[2025-10-31 22:35:40] [Iter  872/2362] R2[309/600]   | LR: 0.023823 | E:  -59.727683 | E_var:     5.7082 | E_err:   0.026803
[2025-10-31 22:35:57] [Iter  873/2362] R2[310/600]   | LR: 0.023692 | E:  -59.779440 | E_var:     6.1446 | E_err:   0.028028
[2025-10-31 22:36:15] [Iter  874/2362] R2[311/600]   | LR: 0.023561 | E:  -59.809479 | E_var:     5.8903 | E_err:   0.026284
[2025-10-31 22:36:32] [Iter  875/2362] R2[312/600]   | LR: 0.023431 | E:  -59.737898 | E_var:     5.9398 | E_err:   0.026299
[2025-10-31 22:36:49] [Iter  876/2362] R2[313/600]   | LR: 0.023300 | E:  -59.803386 | E_var:     6.1403 | E_err:   0.027945
[2025-10-31 22:37:06] [Iter  877/2362] R2[314/600]   | LR: 0.023170 | E:  -59.723933 | E_var:     5.9348 | E_err:   0.027732
[2025-10-31 22:37:23] [Iter  878/2362] R2[315/600]   | LR: 0.023039 | E:  -59.737836 | E_var:     5.9263 | E_err:   0.027528
[2025-10-31 22:37:41] [Iter  879/2362] R2[316/600]   | LR: 0.022909 | E:  -59.760019 | E_var:     5.9238 | E_err:   0.027501
[2025-10-31 22:37:58] [Iter  880/2362] R2[317/600]   | LR: 0.022778 | E:  -59.757196 | E_var:     5.9427 | E_err:   0.027895
[2025-10-31 22:38:15] [Iter  881/2362] R2[318/600]   | LR: 0.022648 | E:  -59.767647 | E_var:     5.6344 | E_err:   0.027089
[2025-10-31 22:38:32] [Iter  882/2362] R2[319/600]   | LR: 0.022518 | E:  -59.840348 | E_var:     5.8873 | E_err:   0.028016
[2025-10-31 22:38:50] [Iter  883/2362] R2[320/600]   | LR: 0.022387 | E:  -59.753510 | E_var:     5.7265 | E_err:   0.027224
[2025-10-31 22:39:07] [Iter  884/2362] R2[321/600]   | LR: 0.022257 | E:  -59.722821 | E_var:     5.8668 | E_err:   0.027166
[2025-10-31 22:39:24] [Iter  885/2362] R2[322/600]   | LR: 0.022127 | E:  -59.780950 | E_var:     5.5693 | E_err:   0.026230
[2025-10-31 22:39:41] [Iter  886/2362] R2[323/600]   | LR: 0.021997 | E:  -59.792341 | E_var:     5.8315 | E_err:   0.027613
[2025-10-31 22:39:58] [Iter  887/2362] R2[324/600]   | LR: 0.021867 | E:  -59.753599 | E_var:     5.9696 | E_err:   0.027318
[2025-10-31 22:40:16] [Iter  888/2362] R2[325/600]   | LR: 0.021737 | E:  -59.799067 | E_var:     5.7870 | E_err:   0.028096
[2025-10-31 22:40:33] [Iter  889/2362] R2[326/600]   | LR: 0.021608 | E:  -59.777780 | E_var:     5.8304 | E_err:   0.026780
[2025-10-31 22:40:50] [Iter  890/2362] R2[327/600]   | LR: 0.021478 | E:  -59.782364 | E_var:     5.5720 | E_err:   0.025987
[2025-10-31 22:41:07] [Iter  891/2362] R2[328/600]   | LR: 0.021348 | E:  -59.782677 | E_var:     5.8557 | E_err:   0.027087
[2025-10-31 22:41:24] [Iter  892/2362] R2[329/600]   | LR: 0.021219 | E:  -59.750016 | E_var:     5.7087 | E_err:   0.027357
[2025-10-31 22:41:42] [Iter  893/2362] R2[330/600]   | LR: 0.021090 | E:  -59.768161 | E_var:     5.5936 | E_err:   0.025979
[2025-10-31 22:41:59] [Iter  894/2362] R2[331/600]   | LR: 0.020960 | E:  -59.791737 | E_var:     5.7245 | E_err:   0.026872
[2025-10-31 22:42:16] [Iter  895/2362] R2[332/600]   | LR: 0.020831 | E:  -59.801760 | E_var:     5.8193 | E_err:   0.027056
[2025-10-31 22:42:33] [Iter  896/2362] R2[333/600]   | LR: 0.020702 | E:  -59.807610 | E_var:     5.6882 | E_err:   0.027277
[2025-10-31 22:42:51] [Iter  897/2362] R2[334/600]   | LR: 0.020573 | E:  -59.777731 | E_var:     5.7465 | E_err:   0.026968
[2025-10-31 22:43:08] [Iter  898/2362] R2[335/600]   | LR: 0.020445 | E:  -59.778399 | E_var:     6.0221 | E_err:   0.028822
[2025-10-31 22:43:25] [Iter  899/2362] R2[336/600]   | LR: 0.020316 | E:  -59.687838 | E_var:     6.1967 | E_err:   0.028908
[2025-10-31 22:43:42] [Iter  900/2362] R2[337/600]   | LR: 0.020188 | E:  -59.767921 | E_var:     5.7082 | E_err:   0.026997
[2025-10-31 22:43:59] [Iter  901/2362] R2[338/600]   | LR: 0.020059 | E:  -59.802740 | E_var:     5.7331 | E_err:   0.026771
[2025-10-31 22:44:17] [Iter  902/2362] R2[339/600]   | LR: 0.019931 | E:  -59.817377 | E_var:     5.8242 | E_err:   0.027768
[2025-10-31 22:44:34] [Iter  903/2362] R2[340/600]   | LR: 0.019803 | E:  -59.738183 | E_var:     5.8720 | E_err:   0.027688
[2025-10-31 22:44:51] [Iter  904/2362] R2[341/600]   | LR: 0.019675 | E:  -59.811851 | E_var:     5.7701 | E_err:   0.026227
[2025-10-31 22:45:08] [Iter  905/2362] R2[342/600]   | LR: 0.019547 | E:  -59.786677 | E_var:     5.6464 | E_err:   0.027207
[2025-10-31 22:45:25] [Iter  906/2362] R2[343/600]   | LR: 0.019419 | E:  -59.771182 | E_var:     5.6263 | E_err:   0.026814
[2025-10-31 22:45:43] [Iter  907/2362] R2[344/600]   | LR: 0.019292 | E:  -59.782088 | E_var:     5.5364 | E_err:   0.026589
[2025-10-31 22:46:00] [Iter  908/2362] R2[345/600]   | LR: 0.019164 | E:  -59.783558 | E_var:     5.6123 | E_err:   0.025435
[2025-10-31 22:46:17] [Iter  909/2362] R2[346/600]   | LR: 0.019037 | E:  -59.784658 | E_var:     5.5636 | E_err:   0.026512
[2025-10-31 22:46:34] [Iter  910/2362] R2[347/600]   | LR: 0.018910 | E:  -59.759154 | E_var:     5.7885 | E_err:   0.027335
[2025-10-31 22:46:52] [Iter  911/2362] R2[348/600]   | LR: 0.018783 | E:  -59.806613 | E_var:     5.7348 | E_err:   0.026204
[2025-10-31 22:47:09] [Iter  912/2362] R2[349/600]   | LR: 0.018657 | E:  -59.790391 | E_var:     5.7938 | E_err:   0.027483
[2025-10-31 22:47:26] [Iter  913/2362] R2[350/600]   | LR: 0.018530 | E:  -59.788665 | E_var:     5.7298 | E_err:   0.026888
[2025-10-31 22:47:43] [Iter  914/2362] R2[351/600]   | LR: 0.018404 | E:  -59.817402 | E_var:     5.6033 | E_err:   0.026586
[2025-10-31 22:48:00] [Iter  915/2362] R2[352/600]   | LR: 0.018278 | E:  -59.782962 | E_var:     5.6684 | E_err:   0.027257
[2025-10-31 22:48:18] [Iter  916/2362] R2[353/600]   | LR: 0.018152 | E:  -59.776009 | E_var:     5.6679 | E_err:   0.027541
[2025-10-31 22:48:35] [Iter  917/2362] R2[354/600]   | LR: 0.018026 | E:  -59.824944 | E_var:     5.7150 | E_err:   0.026237
[2025-10-31 22:48:52] [Iter  918/2362] R2[355/600]   | LR: 0.017900 | E:  -59.826873 | E_var:     5.5233 | E_err:   0.027336
[2025-10-31 22:49:09] [Iter  919/2362] R2[356/600]   | LR: 0.017775 | E:  -59.747772 | E_var:     5.7787 | E_err:   0.028250
[2025-10-31 22:49:26] [Iter  920/2362] R2[357/600]   | LR: 0.017650 | E:  -59.755975 | E_var:     5.5193 | E_err:   0.025623
[2025-10-31 22:49:44] [Iter  921/2362] R2[358/600]   | LR: 0.017525 | E:  -59.749612 | E_var:     5.6101 | E_err:   0.026176
[2025-10-31 22:50:01] [Iter  922/2362] R2[359/600]   | LR: 0.017400 | E:  -59.767626 | E_var:     5.5453 | E_err:   0.026598
[2025-10-31 22:50:18] [Iter  923/2362] R2[360/600]   | LR: 0.017275 | E:  -59.786571 | E_var:     5.5947 | E_err:   0.027269
[2025-10-31 22:50:35] [Iter  924/2362] R2[361/600]   | LR: 0.017151 | E:  -59.741603 | E_var:     5.6248 | E_err:   0.026528
[2025-10-31 22:50:53] [Iter  925/2362] R2[362/600]   | LR: 0.017027 | E:  -59.780052 | E_var:     5.7766 | E_err:   0.026976
[2025-10-31 22:51:10] [Iter  926/2362] R2[363/600]   | LR: 0.016903 | E:  -59.755022 | E_var:     5.6973 | E_err:   0.026314
[2025-10-31 22:51:27] [Iter  927/2362] R2[364/600]   | LR: 0.016779 | E:  -59.785216 | E_var:     5.5680 | E_err:   0.027204
[2025-10-31 22:51:44] [Iter  928/2362] R2[365/600]   | LR: 0.016655 | E:  -59.781566 | E_var:     5.5697 | E_err:   0.027552
[2025-10-31 22:52:01] [Iter  929/2362] R2[366/600]   | LR: 0.016532 | E:  -59.795063 | E_var:     5.7495 | E_err:   0.027270
[2025-10-31 22:52:19] [Iter  930/2362] R2[367/600]   | LR: 0.016409 | E:  -59.760711 | E_var:     5.7414 | E_err:   0.027269
[2025-10-31 22:52:36] [Iter  931/2362] R2[368/600]   | LR: 0.016286 | E:  -59.743516 | E_var:     5.9837 | E_err:   0.026910
[2025-10-31 22:52:53] [Iter  932/2362] R2[369/600]   | LR: 0.016164 | E:  -59.812694 | E_var:     5.5651 | E_err:   0.026759
[2025-10-31 22:53:10] [Iter  933/2362] R2[370/600]   | LR: 0.016041 | E:  -59.779481 | E_var:     5.5100 | E_err:   0.025888
[2025-10-31 22:53:28] [Iter  934/2362] R2[371/600]   | LR: 0.015919 | E:  -59.735630 | E_var:     5.7452 | E_err:   0.027184
[2025-10-31 22:53:45] [Iter  935/2362] R2[372/600]   | LR: 0.015798 | E:  -59.764865 | E_var:     5.3666 | E_err:   0.026425
[2025-10-31 22:54:02] [Iter  936/2362] R2[373/600]   | LR: 0.015676 | E:  -59.785335 | E_var:     5.5997 | E_err:   0.026530
[2025-10-31 22:54:19] [Iter  937/2362] R2[374/600]   | LR: 0.015555 | E:  -59.796115 | E_var:     5.4900 | E_err:   0.025864
[2025-10-31 22:54:36] [Iter  938/2362] R2[375/600]   | LR: 0.015434 | E:  -59.741618 | E_var:     5.3460 | E_err:   0.026270
[2025-10-31 22:54:54] [Iter  939/2362] R2[376/600]   | LR: 0.015313 | E:  -59.808579 | E_var:     5.6299 | E_err:   0.027472
[2025-10-31 22:55:11] [Iter  940/2362] R2[377/600]   | LR: 0.015192 | E:  -59.800869 | E_var:     5.6266 | E_err:   0.026126
[2025-10-31 22:55:28] [Iter  941/2362] R2[378/600]   | LR: 0.015072 | E:  -59.764811 | E_var:     5.8802 | E_err:   0.027701
[2025-10-31 22:55:45] [Iter  942/2362] R2[379/600]   | LR: 0.014952 | E:  -59.767249 | E_var:     5.7312 | E_err:   0.027576
[2025-10-31 22:56:03] [Iter  943/2362] R2[380/600]   | LR: 0.014832 | E:  -59.769670 | E_var:     5.8917 | E_err:   0.026928
[2025-10-31 22:56:20] [Iter  944/2362] R2[381/600]   | LR: 0.014713 | E:  -59.699843 | E_var:     5.7518 | E_err:   0.027706
[2025-10-31 22:56:37] [Iter  945/2362] R2[382/600]   | LR: 0.014594 | E:  -59.740216 | E_var:     5.7151 | E_err:   0.026248
[2025-10-31 22:56:54] [Iter  946/2362] R2[383/600]   | LR: 0.014475 | E:  -59.727009 | E_var:     6.0141 | E_err:   0.027532
[2025-10-31 22:57:11] [Iter  947/2362] R2[384/600]   | LR: 0.014356 | E:  -59.746384 | E_var:     6.0411 | E_err:   0.027904
[2025-10-31 22:57:29] [Iter  948/2362] R2[385/600]   | LR: 0.014238 | E:  -59.716182 | E_var:     6.0024 | E_err:   0.028404
[2025-10-31 22:57:46] [Iter  949/2362] R2[386/600]   | LR: 0.014120 | E:  -59.767569 | E_var:     5.8873 | E_err:   0.027408
[2025-10-31 22:58:03] [Iter  950/2362] R2[387/600]   | LR: 0.014002 | E:  -59.723085 | E_var:     6.0788 | E_err:   0.027911
[2025-10-31 22:58:20] [Iter  951/2362] R2[388/600]   | LR: 0.013885 | E:  -59.782681 | E_var:     5.9775 | E_err:   0.027811
[2025-10-31 22:58:37] [Iter  952/2362] R2[389/600]   | LR: 0.013768 | E:  -59.728898 | E_var:     6.1532 | E_err:   0.028441
[2025-10-31 22:58:55] [Iter  953/2362] R2[390/600]   | LR: 0.013651 | E:  -59.781839 | E_var:     6.1391 | E_err:   0.028516
[2025-10-31 22:59:12] [Iter  954/2362] R2[391/600]   | LR: 0.013534 | E:  -59.716513 | E_var:     5.9123 | E_err:   0.027384
[2025-10-31 22:59:29] [Iter  955/2362] R2[392/600]   | LR: 0.013418 | E:  -59.819478 | E_var:     5.6145 | E_err:   0.026106
[2025-10-31 22:59:46] [Iter  956/2362] R2[393/600]   | LR: 0.013302 | E:  -59.840038 | E_var:     5.5121 | E_err:   0.026523
[2025-10-31 23:00:04] [Iter  957/2362] R2[394/600]   | LR: 0.013187 | E:  -59.833875 | E_var:     5.5525 | E_err:   0.027011
[2025-10-31 23:00:21] [Iter  958/2362] R2[395/600]   | LR: 0.013072 | E:  -59.793384 | E_var:     5.7292 | E_err:   0.026791
[2025-10-31 23:00:38] [Iter  959/2362] R2[396/600]   | LR: 0.012957 | E:  -59.810464 | E_var:     5.4655 | E_err:   0.026456
[2025-10-31 23:00:55] [Iter  960/2362] R2[397/600]   | LR: 0.012842 | E:  -59.841387 | E_var:     5.6678 | E_err:   0.027034
[2025-10-31 23:01:12] [Iter  961/2362] R2[398/600]   | LR: 0.012728 | E:  -59.825764 | E_var:     5.5746 | E_err:   0.026870
[2025-10-31 23:01:30] [Iter  962/2362] R2[399/600]   | LR: 0.012614 | E:  -59.849332 | E_var:     6.1458 | E_err:   0.027420
[2025-10-31 23:01:47] [Iter  963/2362] R2[400/600]   | LR: 0.012501 | E:  -59.775533 | E_var:     5.5946 | E_err:   0.026389
[2025-10-31 23:02:04] [Iter  964/2362] R2[401/600]   | LR: 0.012388 | E:  -59.781307 | E_var:     5.5664 | E_err:   0.026744
[2025-10-31 23:02:21] [Iter  965/2362] R2[402/600]   | LR: 0.012275 | E:  -59.801771 | E_var:     5.5774 | E_err:   0.026490
[2025-10-31 23:02:39] [Iter  966/2362] R2[403/600]   | LR: 0.012162 | E:  -59.821081 | E_var:     5.7451 | E_err:   0.026981
[2025-10-31 23:02:56] [Iter  967/2362] R2[404/600]   | LR: 0.012050 | E:  -59.787440 | E_var:     5.3636 | E_err:   0.025588
[2025-10-31 23:03:13] [Iter  968/2362] R2[405/600]   | LR: 0.011938 | E:  -59.809354 | E_var:     5.5198 | E_err:   0.026152
[2025-10-31 23:03:30] [Iter  969/2362] R2[406/600]   | LR: 0.011827 | E:  -59.780293 | E_var:     5.3552 | E_err:   0.025724
[2025-10-31 23:03:47] [Iter  970/2362] R2[407/600]   | LR: 0.011716 | E:  -59.848256 | E_var:     5.3084 | E_err:   0.025986
[2025-10-31 23:04:05] [Iter  971/2362] R2[408/600]   | LR: 0.011605 | E:  -59.738391 | E_var:     5.3617 | E_err:   0.026684
[2025-10-31 23:04:22] [Iter  972/2362] R2[409/600]   | LR: 0.011495 | E:  -59.704794 | E_var:     5.2996 | E_err:   0.025830
[2025-10-31 23:04:39] [Iter  973/2362] R2[410/600]   | LR: 0.011385 | E:  -59.838026 | E_var:     5.6395 | E_err:   0.026731
[2025-10-31 23:04:56] [Iter  974/2362] R2[411/600]   | LR: 0.011275 | E:  -59.797049 | E_var:     5.4265 | E_err:   0.026965
[2025-10-31 23:05:14] [Iter  975/2362] R2[412/600]   | LR: 0.011166 | E:  -59.795932 | E_var:     5.5660 | E_err:   0.026870
[2025-10-31 23:05:31] [Iter  976/2362] R2[413/600]   | LR: 0.011057 | E:  -59.793715 | E_var:     5.4644 | E_err:   0.027247
[2025-10-31 23:05:48] [Iter  977/2362] R2[414/600]   | LR: 0.010949 | E:  -59.804638 | E_var:     5.3788 | E_err:   0.026387
[2025-10-31 23:06:05] [Iter  978/2362] R2[415/600]   | LR: 0.010841 | E:  -59.785841 | E_var:     5.5861 | E_err:   0.026650
[2025-10-31 23:06:22] [Iter  979/2362] R2[416/600]   | LR: 0.010733 | E:  -59.792335 | E_var:     5.6183 | E_err:   0.026515
[2025-10-31 23:06:40] [Iter  980/2362] R2[417/600]   | LR: 0.010626 | E:  -59.780409 | E_var:     5.6334 | E_err:   0.025822
[2025-10-31 23:06:57] [Iter  981/2362] R2[418/600]   | LR: 0.010519 | E:  -59.854447 | E_var:     5.4929 | E_err:   0.026713
[2025-10-31 23:07:14] [Iter  982/2362] R2[419/600]   | LR: 0.010412 | E:  -59.846475 | E_var:     5.6676 | E_err:   0.026620
[2025-10-31 23:07:31] [Iter  983/2362] R2[420/600]   | LR: 0.010306 | E:  -59.823304 | E_var:     5.6631 | E_err:   0.027140
[2025-10-31 23:07:49] [Iter  984/2362] R2[421/600]   | LR: 0.010200 | E:  -59.820961 | E_var:     5.8656 | E_err:   0.027309
[2025-10-31 23:08:06] [Iter  985/2362] R2[422/600]   | LR: 0.010095 | E:  -59.792006 | E_var:     5.6840 | E_err:   0.027223
[2025-10-31 23:08:23] [Iter  986/2362] R2[423/600]   | LR: 0.009990 | E:  -59.879722 | E_var:     5.7569 | E_err:   0.027295
[2025-10-31 23:08:40] [Iter  987/2362] R2[424/600]   | LR: 0.009886 | E:  -59.748523 | E_var:     5.7208 | E_err:   0.026506
[2025-10-31 23:08:57] [Iter  988/2362] R2[425/600]   | LR: 0.009782 | E:  -59.818713 | E_var:     5.8200 | E_err:   0.027351
[2025-10-31 23:09:15] [Iter  989/2362] R2[426/600]   | LR: 0.009678 | E:  -59.820476 | E_var:     5.6846 | E_err:   0.027066
[2025-10-31 23:09:32] [Iter  990/2362] R2[427/600]   | LR: 0.009575 | E:  -59.814918 | E_var:     5.7605 | E_err:   0.026808
[2025-10-31 23:09:49] [Iter  991/2362] R2[428/600]   | LR: 0.009472 | E:  -59.821764 | E_var:     5.6474 | E_err:   0.026805
[2025-10-31 23:10:06] [Iter  992/2362] R2[429/600]   | LR: 0.009370 | E:  -59.816602 | E_var:     5.7006 | E_err:   0.027601
[2025-10-31 23:10:24] [Iter  993/2362] R2[430/600]   | LR: 0.009268 | E:  -59.799775 | E_var:     5.4856 | E_err:   0.025934
[2025-10-31 23:10:41] [Iter  994/2362] R2[431/600]   | LR: 0.009166 | E:  -59.832704 | E_var:     5.9674 | E_err:   0.028538
[2025-10-31 23:10:58] [Iter  995/2362] R2[432/600]   | LR: 0.009065 | E:  -59.773029 | E_var:     5.5135 | E_err:   0.026573
[2025-10-31 23:11:15] [Iter  996/2362] R2[433/600]   | LR: 0.008965 | E:  -59.836490 | E_var:     5.5083 | E_err:   0.026888
[2025-10-31 23:11:32] [Iter  997/2362] R2[434/600]   | LR: 0.008864 | E:  -59.828214 | E_var:     5.6659 | E_err:   0.026472
[2025-10-31 23:11:50] [Iter  998/2362] R2[435/600]   | LR: 0.008765 | E:  -59.809321 | E_var:     6.0078 | E_err:   0.028191
[2025-10-31 23:12:07] [Iter  999/2362] R2[436/600]   | LR: 0.008665 | E:  -59.842449 | E_var:     5.5809 | E_err:   0.026331
[2025-10-31 23:12:24] [Iter 1000/2362] R2[437/600]   | LR: 0.008566 | E:  -59.828462 | E_var:     5.6913 | E_err:   0.026907
[2025-10-31 23:12:41] [Iter 1001/2362] R2[438/600]   | LR: 0.008468 | E:  -59.827537 | E_var:     5.6859 | E_err:   0.025836
[2025-10-31 23:12:58] [Iter 1002/2362] R2[439/600]   | LR: 0.008370 | E:  -59.835290 | E_var:     5.6504 | E_err:   0.025651
[2025-10-31 23:13:16] [Iter 1003/2362] R2[440/600]   | LR: 0.008273 | E:  -59.830236 | E_var:     5.6125 | E_err:   0.026821
[2025-10-31 23:13:33] [Iter 1004/2362] R2[441/600]   | LR: 0.008176 | E:  -59.822180 | E_var:     5.6359 | E_err:   0.026513
[2025-10-31 23:13:50] [Iter 1005/2362] R2[442/600]   | LR: 0.008079 | E:  -59.890640 | E_var:     5.7630 | E_err:   0.027427
[2025-10-31 23:14:07] [Iter 1006/2362] R2[443/600]   | LR: 0.007983 | E:  -59.832942 | E_var:     5.9914 | E_err:   0.028136
[2025-10-31 23:14:25] [Iter 1007/2362] R2[444/600]   | LR: 0.007887 | E:  -59.786431 | E_var:     5.7460 | E_err:   0.026263
[2025-10-31 23:14:42] [Iter 1008/2362] R2[445/600]   | LR: 0.007792 | E:  -59.911377 | E_var:     5.5125 | E_err:   0.027334
[2025-10-31 23:14:59] [Iter 1009/2362] R2[446/600]   | LR: 0.007697 | E:  -59.848471 | E_var:     5.4237 | E_err:   0.025902
[2025-10-31 23:15:16] [Iter 1010/2362] R2[447/600]   | LR: 0.007603 | E:  -59.881161 | E_var:     5.4136 | E_err:   0.026168
[2025-10-31 23:15:33] [Iter 1011/2362] R2[448/600]   | LR: 0.007509 | E:  -59.846869 | E_var:     5.4915 | E_err:   0.025445
[2025-10-31 23:15:51] [Iter 1012/2362] R2[449/600]   | LR: 0.007416 | E:  -59.867598 | E_var:     5.9867 | E_err:   0.027190
[2025-10-31 23:16:08] [Iter 1013/2362] R2[450/600]   | LR: 0.007323 | E:  -59.863658 | E_var:     5.4810 | E_err:   0.026853
[2025-10-31 23:16:25] [Iter 1014/2362] R2[451/600]   | LR: 0.007231 | E:  -59.838416 | E_var:     6.2107 | E_err:   0.028070
[2025-10-31 23:16:42] [Iter 1015/2362] R2[452/600]   | LR: 0.007139 | E:  -59.833860 | E_var:     5.5569 | E_err:   0.026221
[2025-10-31 23:17:00] [Iter 1016/2362] R2[453/600]   | LR: 0.007048 | E:  -59.825048 | E_var:     5.5964 | E_err:   0.026389
[2025-10-31 23:17:17] [Iter 1017/2362] R2[454/600]   | LR: 0.006957 | E:  -59.847221 | E_var:     5.5848 | E_err:   0.026362
[2025-10-31 23:17:34] [Iter 1018/2362] R2[455/600]   | LR: 0.006867 | E:  -59.852492 | E_var:     5.5863 | E_err:   0.026772
[2025-10-31 23:17:51] [Iter 1019/2362] R2[456/600]   | LR: 0.006777 | E:  -59.835466 | E_var:     5.6293 | E_err:   0.026818
[2025-10-31 23:18:08] [Iter 1020/2362] R2[457/600]   | LR: 0.006687 | E:  -59.845587 | E_var:     5.6291 | E_err:   0.027442
[2025-10-31 23:18:26] [Iter 1021/2362] R2[458/600]   | LR: 0.006598 | E:  -59.792373 | E_var:     5.4414 | E_err:   0.026264
[2025-10-31 23:18:43] [Iter 1022/2362] R2[459/600]   | LR: 0.006510 | E:  -59.811877 | E_var:     5.5268 | E_err:   0.027000
[2025-10-31 23:19:00] [Iter 1023/2362] R2[460/600]   | LR: 0.006422 | E:  -59.741497 | E_var:     5.4471 | E_err:   0.026487
[2025-10-31 23:19:17] [Iter 1024/2362] R2[461/600]   | LR: 0.006335 | E:  -59.745118 | E_var:     5.4052 | E_err:   0.026921
[2025-10-31 23:19:34] [Iter 1025/2362] R2[462/600]   | LR: 0.006248 | E:  -59.731063 | E_var:     5.3295 | E_err:   0.026090
[2025-10-31 23:19:52] [Iter 1026/2362] R2[463/600]   | LR: 0.006162 | E:  -59.826945 | E_var:     5.4316 | E_err:   0.026140
[2025-10-31 23:20:09] [Iter 1027/2362] R2[464/600]   | LR: 0.006076 | E:  -59.832291 | E_var:     5.3345 | E_err:   0.025587
[2025-10-31 23:20:26] [Iter 1028/2362] R2[465/600]   | LR: 0.005991 | E:  -59.838659 | E_var:     5.4257 | E_err:   0.026819
[2025-10-31 23:20:43] [Iter 1029/2362] R2[466/600]   | LR: 0.005906 | E:  -59.820753 | E_var:     5.4838 | E_err:   0.026282
[2025-10-31 23:21:01] [Iter 1030/2362] R2[467/600]   | LR: 0.005822 | E:  -59.808738 | E_var:     5.4430 | E_err:   0.027437
[2025-10-31 23:21:18] [Iter 1031/2362] R2[468/600]   | LR: 0.005738 | E:  -59.826431 | E_var:     5.4677 | E_err:   0.026449
[2025-10-31 23:21:35] [Iter 1032/2362] R2[469/600]   | LR: 0.005655 | E:  -59.850712 | E_var:     5.5199 | E_err:   0.026277
[2025-10-31 23:21:52] [Iter 1033/2362] R2[470/600]   | LR: 0.005572 | E:  -59.858155 | E_var:     5.6357 | E_err:   0.026873
[2025-10-31 23:22:09] [Iter 1034/2362] R2[471/600]   | LR: 0.005490 | E:  -59.811769 | E_var:     5.4542 | E_err:   0.026326
[2025-10-31 23:22:27] [Iter 1035/2362] R2[472/600]   | LR: 0.005409 | E:  -59.845047 | E_var:     5.4422 | E_err:   0.026359
[2025-10-31 23:22:44] [Iter 1036/2362] R2[473/600]   | LR: 0.005328 | E:  -59.874663 | E_var:     5.7554 | E_err:   0.026379
[2025-10-31 23:23:01] [Iter 1037/2362] R2[474/600]   | LR: 0.005247 | E:  -59.876375 | E_var:     5.7058 | E_err:   0.026095
[2025-10-31 23:23:18] [Iter 1038/2362] R2[475/600]   | LR: 0.005167 | E:  -59.835996 | E_var:     6.0492 | E_err:   0.027024
[2025-10-31 23:23:36] [Iter 1039/2362] R2[476/600]   | LR: 0.005088 | E:  -59.839432 | E_var:     5.6466 | E_err:   0.026868
[2025-10-31 23:23:53] [Iter 1040/2362] R2[477/600]   | LR: 0.005009 | E:  -59.847240 | E_var:     5.8550 | E_err:   0.027128
[2025-10-31 23:24:10] [Iter 1041/2362] R2[478/600]   | LR: 0.004930 | E:  -59.765168 | E_var:     6.1622 | E_err:   0.028478
[2025-10-31 23:24:27] [Iter 1042/2362] R2[479/600]   | LR: 0.004853 | E:  -59.842158 | E_var:     5.7023 | E_err:   0.026837
[2025-10-31 23:24:44] [Iter 1043/2362] R2[480/600]   | LR: 0.004775 | E:  -59.898474 | E_var:     5.6635 | E_err:   0.026943
[2025-10-31 23:25:02] [Iter 1044/2362] R2[481/600]   | LR: 0.004699 | E:  -59.840742 | E_var:     5.6470 | E_err:   0.026784
[2025-10-31 23:25:19] [Iter 1045/2362] R2[482/600]   | LR: 0.004623 | E:  -59.810548 | E_var:     5.4966 | E_err:   0.026368
[2025-10-31 23:25:36] [Iter 1046/2362] R2[483/600]   | LR: 0.004547 | E:  -59.863429 | E_var:     5.4349 | E_err:   0.026188
[2025-10-31 23:25:53] [Iter 1047/2362] R2[484/600]   | LR: 0.004472 | E:  -59.826149 | E_var:     5.7992 | E_err:   0.027075
[2025-10-31 23:26:10] [Iter 1048/2362] R2[485/600]   | LR: 0.004398 | E:  -59.870538 | E_var:     5.8694 | E_err:   0.028012
[2025-10-31 23:26:28] [Iter 1049/2362] R2[486/600]   | LR: 0.004324 | E:  -59.782080 | E_var:     5.8360 | E_err:   0.027416
[2025-10-31 23:26:45] [Iter 1050/2362] R2[487/600]   | LR: 0.004251 | E:  -59.762645 | E_var:     5.6433 | E_err:   0.027317
[2025-10-31 23:27:02] [Iter 1051/2362] R2[488/600]   | LR: 0.004178 | E:  -59.834413 | E_var:     5.5297 | E_err:   0.027127
[2025-10-31 23:27:19] [Iter 1052/2362] R2[489/600]   | LR: 0.004106 | E:  -59.830278 | E_var:     5.4572 | E_err:   0.026511
[2025-10-31 23:27:37] [Iter 1053/2362] R2[490/600]   | LR: 0.004034 | E:  -59.850442 | E_var:     5.7688 | E_err:   0.026806
[2025-10-31 23:27:54] [Iter 1054/2362] R2[491/600]   | LR: 0.003963 | E:  -59.855140 | E_var:     5.4987 | E_err:   0.025917
[2025-10-31 23:28:11] [Iter 1055/2362] R2[492/600]   | LR: 0.003893 | E:  -59.856521 | E_var:     5.3575 | E_err:   0.026055
[2025-10-31 23:28:28] [Iter 1056/2362] R2[493/600]   | LR: 0.003823 | E:  -59.882734 | E_var:     5.4412 | E_err:   0.025261
[2025-10-31 23:28:45] [Iter 1057/2362] R2[494/600]   | LR: 0.003754 | E:  -59.902795 | E_var:     5.5823 | E_err:   0.026296
[2025-10-31 23:29:03] [Iter 1058/2362] R2[495/600]   | LR: 0.003685 | E:  -59.904142 | E_var:     5.5107 | E_err:   0.025590
[2025-10-31 23:29:20] [Iter 1059/2362] R2[496/600]   | LR: 0.003617 | E:  -59.934415 | E_var:     5.3658 | E_err:   0.025768
[2025-10-31 23:29:37] [Iter 1060/2362] R2[497/600]   | LR: 0.003549 | E:  -59.865809 | E_var:     5.4595 | E_err:   0.026279
[2025-10-31 23:29:54] [Iter 1061/2362] R2[498/600]   | LR: 0.003482 | E:  -59.847251 | E_var:     5.5265 | E_err:   0.025920
[2025-10-31 23:30:12] [Iter 1062/2362] R2[499/600]   | LR: 0.003416 | E:  -59.913559 | E_var:     5.8299 | E_err:   0.027473
[2025-10-31 23:30:29] [Iter 1063/2362] R2[500/600]   | LR: 0.003350 | E:  -59.850485 | E_var:     5.5595 | E_err:   0.026155
[2025-10-31 23:30:46] [Iter 1064/2362] R2[501/600]   | LR: 0.003285 | E:  -59.843569 | E_var:     5.6947 | E_err:   0.026525
[2025-10-31 23:31:03] [Iter 1065/2362] R2[502/600]   | LR: 0.003221 | E:  -59.862680 | E_var:     5.7374 | E_err:   0.026194
[2025-10-31 23:31:20] [Iter 1066/2362] R2[503/600]   | LR: 0.003157 | E:  -59.895539 | E_var:     5.5425 | E_err:   0.026153
[2025-10-31 23:31:38] [Iter 1067/2362] R2[504/600]   | LR: 0.003093 | E:  -59.900537 | E_var:     5.5447 | E_err:   0.026561
[2025-10-31 23:31:55] [Iter 1068/2362] R2[505/600]   | LR: 0.003031 | E:  -59.877102 | E_var:     5.3896 | E_err:   0.025843
[2025-10-31 23:32:12] [Iter 1069/2362] R2[506/600]   | LR: 0.002968 | E:  -59.876384 | E_var:     5.2196 | E_err:   0.025572
[2025-10-31 23:32:29] [Iter 1070/2362] R2[507/600]   | LR: 0.002907 | E:  -59.912248 | E_var:     5.5352 | E_err:   0.025100
[2025-10-31 23:32:47] [Iter 1071/2362] R2[508/600]   | LR: 0.002846 | E:  -59.826236 | E_var:     5.4129 | E_err:   0.026298
[2025-10-31 23:33:04] [Iter 1072/2362] R2[509/600]   | LR: 0.002786 | E:  -59.896451 | E_var:     5.8688 | E_err:   0.027388
[2025-10-31 23:33:21] [Iter 1073/2362] R2[510/600]   | LR: 0.002726 | E:  -59.843055 | E_var:     5.5406 | E_err:   0.026950
[2025-10-31 23:33:38] [Iter 1074/2362] R2[511/600]   | LR: 0.002667 | E:  -59.886196 | E_var:     5.3867 | E_err:   0.025512
[2025-10-31 23:33:55] [Iter 1075/2362] R2[512/600]   | LR: 0.002608 | E:  -59.831573 | E_var:     5.5382 | E_err:   0.026115
[2025-10-31 23:34:13] [Iter 1076/2362] R2[513/600]   | LR: 0.002550 | E:  -59.852293 | E_var:     5.6792 | E_err:   0.026620
[2025-10-31 23:34:30] [Iter 1077/2362] R2[514/600]   | LR: 0.002493 | E:  -59.865889 | E_var:     5.8180 | E_err:   0.027739
[2025-10-31 23:34:47] [Iter 1078/2362] R2[515/600]   | LR: 0.002436 | E:  -59.809665 | E_var:     5.6615 | E_err:   0.026826
[2025-10-31 23:35:04] [Iter 1079/2362] R2[516/600]   | LR: 0.002380 | E:  -59.859908 | E_var:     5.5818 | E_err:   0.026449
[2025-10-31 23:35:21] [Iter 1080/2362] R2[517/600]   | LR: 0.002325 | E:  -59.803300 | E_var:     5.4632 | E_err:   0.026576
[2025-10-31 23:35:39] [Iter 1081/2362] R2[518/600]   | LR: 0.002270 | E:  -59.781515 | E_var:     5.3936 | E_err:   0.026437
[2025-10-31 23:35:56] [Iter 1082/2362] R2[519/600]   | LR: 0.002216 | E:  -59.860916 | E_var:     5.3371 | E_err:   0.026600
[2025-10-31 23:36:13] [Iter 1083/2362] R2[520/600]   | LR: 0.002162 | E:  -59.867432 | E_var:     5.4275 | E_err:   0.026533
[2025-10-31 23:36:30] [Iter 1084/2362] R2[521/600]   | LR: 0.002109 | E:  -59.852606 | E_var:     5.3949 | E_err:   0.026141
[2025-10-31 23:36:48] [Iter 1085/2362] R2[522/600]   | LR: 0.002057 | E:  -59.850287 | E_var:     5.4483 | E_err:   0.026140
[2025-10-31 23:37:05] [Iter 1086/2362] R2[523/600]   | LR: 0.002005 | E:  -59.887090 | E_var:     5.6300 | E_err:   0.027289
[2025-10-31 23:37:22] [Iter 1087/2362] R2[524/600]   | LR: 0.001954 | E:  -59.887080 | E_var:     5.5003 | E_err:   0.027051
[2025-10-31 23:37:39] [Iter 1088/2362] R2[525/600]   | LR: 0.001904 | E:  -59.881310 | E_var:     5.7008 | E_err:   0.027240
[2025-10-31 23:37:56] [Iter 1089/2362] R2[526/600]   | LR: 0.001854 | E:  -59.899771 | E_var:     5.5780 | E_err:   0.026880
[2025-10-31 23:38:14] [Iter 1090/2362] R2[527/600]   | LR: 0.001805 | E:  -59.878840 | E_var:     5.6958 | E_err:   0.027546
[2025-10-31 23:38:31] [Iter 1091/2362] R2[528/600]   | LR: 0.001757 | E:  -59.857806 | E_var:     5.4630 | E_err:   0.025507
[2025-10-31 23:38:48] [Iter 1092/2362] R2[529/600]   | LR: 0.001709 | E:  -59.897745 | E_var:     5.3933 | E_err:   0.025347
[2025-10-31 23:39:05] [Iter 1093/2362] R2[530/600]   | LR: 0.001661 | E:  -59.890186 | E_var:     5.6278 | E_err:   0.027227
[2025-10-31 23:39:23] [Iter 1094/2362] R2[531/600]   | LR: 0.001615 | E:  -59.893706 | E_var:     5.5786 | E_err:   0.026023
[2025-10-31 23:39:40] [Iter 1095/2362] R2[532/600]   | LR: 0.001569 | E:  -59.885570 | E_var:     5.6353 | E_err:   0.025864
[2025-10-31 23:39:57] [Iter 1096/2362] R2[533/600]   | LR: 0.001524 | E:  -59.842722 | E_var:     5.7366 | E_err:   0.026503
[2025-10-31 23:40:14] [Iter 1097/2362] R2[534/600]   | LR: 0.001479 | E:  -59.853619 | E_var:     5.9445 | E_err:   0.027932
[2025-10-31 23:40:31] [Iter 1098/2362] R2[535/600]   | LR: 0.001435 | E:  -59.886846 | E_var:     5.3741 | E_err:   0.025742
[2025-10-31 23:40:49] [Iter 1099/2362] R2[536/600]   | LR: 0.001392 | E:  -59.867662 | E_var:     5.5609 | E_err:   0.027056
[2025-10-31 23:41:06] [Iter 1100/2362] R2[537/600]   | LR: 0.001349 | E:  -59.826528 | E_var:     5.6436 | E_err:   0.026899
[2025-10-31 23:41:23] [Iter 1101/2362] R2[538/600]   | LR: 0.001307 | E:  -59.864646 | E_var:     5.3826 | E_err:   0.026099
[2025-10-31 23:41:40] [Iter 1102/2362] R2[539/600]   | LR: 0.001265 | E:  -59.916173 | E_var:     5.3119 | E_err:   0.024687
[2025-10-31 23:41:58] [Iter 1103/2362] R2[540/600]   | LR: 0.001225 | E:  -59.878723 | E_var:     5.4230 | E_err:   0.026330
[2025-10-31 23:42:15] [Iter 1104/2362] R2[541/600]   | LR: 0.001184 | E:  -59.852973 | E_var:     5.2835 | E_err:   0.025250
[2025-10-31 23:42:32] [Iter 1105/2362] R2[542/600]   | LR: 0.001145 | E:  -59.902625 | E_var:     5.3750 | E_err:   0.026523
[2025-10-31 23:42:49] [Iter 1106/2362] R2[543/600]   | LR: 0.001106 | E:  -59.887663 | E_var:     5.6081 | E_err:   0.026339
[2025-10-31 23:43:06] [Iter 1107/2362] R2[544/600]   | LR: 0.001068 | E:  -59.832126 | E_var:     5.3003 | E_err:   0.025497
[2025-10-31 23:43:24] [Iter 1108/2362] R2[545/600]   | LR: 0.001030 | E:  -59.882981 | E_var:     5.7382 | E_err:   0.026914
[2025-10-31 23:43:41] [Iter 1109/2362] R2[546/600]   | LR: 0.000994 | E:  -59.876899 | E_var:     5.4613 | E_err:   0.025901
[2025-10-31 23:43:58] [Iter 1110/2362] R2[547/600]   | LR: 0.000957 | E:  -59.857887 | E_var:     5.4771 | E_err:   0.026934
[2025-10-31 23:44:15] [Iter 1111/2362] R2[548/600]   | LR: 0.000922 | E:  -59.877257 | E_var:     5.5091 | E_err:   0.025943
[2025-10-31 23:44:33] [Iter 1112/2362] R2[549/600]   | LR: 0.000887 | E:  -59.866312 | E_var:     5.3911 | E_err:   0.026274
[2025-10-31 23:44:33] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-31 23:44:50] [Iter 1113/2362] R2[550/600]   | LR: 0.000853 | E:  -59.843760 | E_var:     5.8547 | E_err:   0.027135
[2025-10-31 23:45:07] [Iter 1114/2362] R2[551/600]   | LR: 0.000819 | E:  -59.853007 | E_var:     5.7552 | E_err:   0.026941
[2025-10-31 23:45:24] [Iter 1115/2362] R2[552/600]   | LR: 0.000786 | E:  -59.888690 | E_var:     5.6709 | E_err:   0.027029
[2025-10-31 23:45:41] [Iter 1116/2362] R2[553/600]   | LR: 0.000754 | E:  -59.847611 | E_var:     5.8763 | E_err:   0.027614
[2025-10-31 23:45:59] [Iter 1117/2362] R2[554/600]   | LR: 0.000723 | E:  -59.892769 | E_var:     5.7294 | E_err:   0.026635
[2025-10-31 23:46:16] [Iter 1118/2362] R2[555/600]   | LR: 0.000692 | E:  -59.761012 | E_var:     6.2272 | E_err:   0.029220
[2025-10-31 23:46:33] [Iter 1119/2362] R2[556/600]   | LR: 0.000662 | E:  -59.799899 | E_var:     5.9330 | E_err:   0.027861
[2025-10-31 23:46:50] [Iter 1120/2362] R2[557/600]   | LR: 0.000632 | E:  -59.757331 | E_var:     5.8352 | E_err:   0.027644
[2025-10-31 23:47:08] [Iter 1121/2362] R2[558/600]   | LR: 0.000603 | E:  -59.849358 | E_var:     5.6867 | E_err:   0.027179
[2025-10-31 23:47:25] [Iter 1122/2362] R2[559/600]   | LR: 0.000575 | E:  -59.856333 | E_var:     6.0482 | E_err:   0.028213
[2025-10-31 23:47:42] [Iter 1123/2362] R2[560/600]   | LR: 0.000547 | E:  -59.845672 | E_var:     5.8541 | E_err:   0.026959
[2025-10-31 23:47:59] [Iter 1124/2362] R2[561/600]   | LR: 0.000520 | E:  -59.796179 | E_var:     5.5091 | E_err:   0.026044
[2025-10-31 23:48:16] [Iter 1125/2362] R2[562/600]   | LR: 0.000494 | E:  -59.866554 | E_var:     5.5603 | E_err:   0.026697
[2025-10-31 23:48:34] [Iter 1126/2362] R2[563/600]   | LR: 0.000469 | E:  -59.922085 | E_var:     5.6811 | E_err:   0.027288
[2025-10-31 23:48:51] [Iter 1127/2362] R2[564/600]   | LR: 0.000444 | E:  -59.910320 | E_var:     5.7128 | E_err:   0.026375
[2025-10-31 23:49:08] [Iter 1128/2362] R2[565/600]   | LR: 0.000420 | E:  -59.898599 | E_var:     5.7008 | E_err:   0.026636
[2025-10-31 23:49:25] [Iter 1129/2362] R2[566/600]   | LR: 0.000396 | E:  -59.885330 | E_var:     5.3769 | E_err:   0.025615
[2025-10-31 23:49:43] [Iter 1130/2362] R2[567/600]   | LR: 0.000373 | E:  -59.902656 | E_var:     5.1900 | E_err:   0.025250
[2025-10-31 23:50:00] [Iter 1131/2362] R2[568/600]   | LR: 0.000351 | E:  -59.915649 | E_var:     5.4742 | E_err:   0.027127
[2025-10-31 23:50:17] [Iter 1132/2362] R2[569/600]   | LR: 0.000330 | E:  -59.871321 | E_var:     5.5986 | E_err:   0.027536
[2025-10-31 23:50:34] [Iter 1133/2362] R2[570/600]   | LR: 0.000309 | E:  -59.908283 | E_var:     5.7340 | E_err:   0.027711
[2025-10-31 23:50:51] [Iter 1134/2362] R2[571/600]   | LR: 0.000289 | E:  -59.866136 | E_var:     5.9523 | E_err:   0.027617
[2025-10-31 23:51:09] [Iter 1135/2362] R2[572/600]   | LR: 0.000269 | E:  -59.880608 | E_var:     5.3841 | E_err:   0.025756
[2025-10-31 23:51:26] [Iter 1136/2362] R2[573/600]   | LR: 0.000250 | E:  -59.899947 | E_var:     5.4193 | E_err:   0.026596
[2025-10-31 23:51:43] [Iter 1137/2362] R2[574/600]   | LR: 0.000232 | E:  -59.904474 | E_var:     5.5355 | E_err:   0.027010
[2025-10-31 23:52:00] [Iter 1138/2362] R2[575/600]   | LR: 0.000215 | E:  -59.927523 | E_var:     5.5191 | E_err:   0.026821
[2025-10-31 23:52:17] [Iter 1139/2362] R2[576/600]   | LR: 0.000198 | E:  -59.856115 | E_var:     5.5607 | E_err:   0.027190
[2025-10-31 23:52:35] [Iter 1140/2362] R2[577/600]   | LR: 0.000182 | E:  -59.879331 | E_var:     5.3836 | E_err:   0.026332
[2025-10-31 23:52:52] [Iter 1141/2362] R2[578/600]   | LR: 0.000167 | E:  -59.768286 | E_var:     5.2490 | E_err:   0.026285
[2025-10-31 23:53:09] [Iter 1142/2362] R2[579/600]   | LR: 0.000152 | E:  -59.892338 | E_var:     5.2315 | E_err:   0.026024
[2025-10-31 23:53:26] [Iter 1143/2362] R2[580/600]   | LR: 0.000138 | E:  -59.904875 | E_var:     5.5937 | E_err:   0.027065
[2025-10-31 23:53:44] [Iter 1144/2362] R2[581/600]   | LR: 0.000125 | E:  -59.872162 | E_var:     5.4273 | E_err:   0.026723
[2025-10-31 23:54:01] [Iter 1145/2362] R2[582/600]   | LR: 0.000112 | E:  -59.899668 | E_var:     5.3944 | E_err:   0.026183
[2025-10-31 23:54:18] [Iter 1146/2362] R2[583/600]   | LR: 0.000100 | E:  -59.912498 | E_var:     5.5273 | E_err:   0.026770
[2025-10-31 23:54:35] [Iter 1147/2362] R2[584/600]   | LR: 0.000089 | E:  -59.904346 | E_var:     5.5660 | E_err:   0.026517
[2025-10-31 23:54:52] [Iter 1148/2362] R2[585/600]   | LR: 0.000078 | E:  -59.923193 | E_var:     5.4672 | E_err:   0.026189
[2025-10-31 23:55:10] [Iter 1149/2362] R2[586/600]   | LR: 0.000068 | E:  -59.914183 | E_var:     5.4912 | E_err:   0.027016
[2025-10-31 23:55:27] [Iter 1150/2362] R2[587/600]   | LR: 0.000059 | E:  -59.945969 | E_var:     5.3790 | E_err:   0.026577
[2025-10-31 23:55:44] [Iter 1151/2362] R2[588/600]   | LR: 0.000050 | E:  -59.962574 | E_var:     5.4699 | E_err:   0.026196
[2025-10-31 23:56:01] [Iter 1152/2362] R2[589/600]   | LR: 0.000042 | E:  -59.957993 | E_var:     5.8521 | E_err:   0.027150
[2025-10-31 23:56:19] [Iter 1153/2362] R2[590/600]   | LR: 0.000035 | E:  -59.910652 | E_var:     5.6265 | E_err:   0.025929
[2025-10-31 23:56:36] [Iter 1154/2362] R2[591/600]   | LR: 0.000029 | E:  -59.938188 | E_var:     5.5136 | E_err:   0.026742
[2025-10-31 23:56:53] [Iter 1155/2362] R2[592/600]   | LR: 0.000023 | E:  -59.927586 | E_var:     5.3716 | E_err:   0.026354
[2025-10-31 23:57:10] [Iter 1156/2362] R2[593/600]   | LR: 0.000018 | E:  -59.891192 | E_var:     5.4084 | E_err:   0.026371
[2025-10-31 23:57:27] [Iter 1157/2362] R2[594/600]   | LR: 0.000013 | E:  -59.831677 | E_var:     5.8014 | E_err:   0.027776
[2025-10-31 23:57:45] [Iter 1158/2362] R2[595/600]   | LR: 0.000010 | E:  -59.788232 | E_var:     6.0972 | E_err:   0.028407
[2025-10-31 23:58:02] [Iter 1159/2362] R2[596/600]   | LR: 0.000006 | E:  -59.843953 | E_var:     5.6887 | E_err:   0.026954
[2025-10-31 23:58:19] [Iter 1160/2362] R2[597/600]   | LR: 0.000004 | E:  -59.862385 | E_var:     5.6195 | E_err:   0.026732
[2025-10-31 23:58:36] [Iter 1161/2362] R2[598/600]   | LR: 0.000002 | E:  -59.851293 | E_var:     5.7841 | E_err:   0.027586
[2025-10-31 23:58:53] [Iter 1162/2362] R2[599/600]   | LR: 0.000001 | E:  -59.848528 | E_var:     5.7568 | E_err:   0.026801
[2025-10-31 23:58:53] 🔄 RESTART #3 | Period: 1200
[2025-10-31 23:59:11] [Iter 1163/2362] R3[0/1200]    | LR: 0.050000 | E:  -59.863903 | E_var:     5.5852 | E_err:   0.027015
[2025-10-31 23:59:28] [Iter 1164/2362] R3[1/1200]    | LR: 0.050000 | E:  -59.872409 | E_var:     5.2855 | E_err:   0.025948
[2025-10-31 23:59:45] [Iter 1165/2362] R3[2/1200]    | LR: 0.050000 | E:  -59.946685 | E_var:     5.5182 | E_err:   0.026962
[2025-11-01 00:00:02] [Iter 1166/2362] R3[3/1200]    | LR: 0.049999 | E:  -59.880415 | E_var:     5.7508 | E_err:   0.027782
[2025-11-01 00:00:20] [Iter 1167/2362] R3[4/1200]    | LR: 0.049999 | E:  -59.882242 | E_var:     5.2993 | E_err:   0.025925
[2025-11-01 00:00:37] [Iter 1168/2362] R3[5/1200]    | LR: 0.049998 | E:  -59.860445 | E_var:     5.4670 | E_err:   0.026566
[2025-11-01 00:00:54] [Iter 1169/2362] R3[6/1200]    | LR: 0.049997 | E:  -59.887710 | E_var:     5.2844 | E_err:   0.025569
[2025-11-01 00:01:11] [Iter 1170/2362] R3[7/1200]    | LR: 0.049996 | E:  -59.832640 | E_var:     5.4772 | E_err:   0.026704
[2025-11-01 00:01:28] [Iter 1171/2362] R3[8/1200]    | LR: 0.049995 | E:  -59.918595 | E_var:     5.3120 | E_err:   0.025766
[2025-11-01 00:01:46] [Iter 1172/2362] R3[9/1200]    | LR: 0.049993 | E:  -59.916062 | E_var:     5.4319 | E_err:   0.025639
[2025-11-01 00:02:03] [Iter 1173/2362] R3[10/1200]   | LR: 0.049991 | E:  -59.920454 | E_var:     5.0930 | E_err:   0.025082
[2025-11-01 00:02:20] [Iter 1174/2362] R3[11/1200]   | LR: 0.049990 | E:  -59.915401 | E_var:     5.2402 | E_err:   0.025140
[2025-11-01 00:02:37] [Iter 1175/2362] R3[12/1200]   | LR: 0.049988 | E:  -59.918572 | E_var:     5.4122 | E_err:   0.027636
[2025-11-01 00:02:54] [Iter 1176/2362] R3[13/1200]   | LR: 0.049986 | E:  -59.897513 | E_var:     5.4031 | E_err:   0.026363
[2025-11-01 00:03:12] [Iter 1177/2362] R3[14/1200]   | LR: 0.049983 | E:  -59.917706 | E_var:     5.2450 | E_err:   0.024901
[2025-11-01 00:03:29] [Iter 1178/2362] R3[15/1200]   | LR: 0.049981 | E:  -59.879410 | E_var:     5.4687 | E_err:   0.026699
[2025-11-01 00:03:46] [Iter 1179/2362] R3[16/1200]   | LR: 0.049978 | E:  -59.884366 | E_var:     5.3048 | E_err:   0.025832
[2025-11-01 00:04:03] [Iter 1180/2362] R3[17/1200]   | LR: 0.049975 | E:  -59.873293 | E_var:     4.8391 | E_err:   0.025071
[2025-11-01 00:04:21] [Iter 1181/2362] R3[18/1200]   | LR: 0.049972 | E:  -59.825263 | E_var:     5.1538 | E_err:   0.026231
[2025-11-01 00:04:38] [Iter 1182/2362] R3[19/1200]   | LR: 0.049969 | E:  -59.840691 | E_var:     5.0730 | E_err:   0.025320
[2025-11-01 00:04:55] [Iter 1183/2362] R3[20/1200]   | LR: 0.049966 | E:  -59.890542 | E_var:     5.2832 | E_err:   0.026181
[2025-11-01 00:05:12] [Iter 1184/2362] R3[21/1200]   | LR: 0.049962 | E:  -59.875767 | E_var:     5.2321 | E_err:   0.026036
[2025-11-01 00:05:29] [Iter 1185/2362] R3[22/1200]   | LR: 0.049959 | E:  -59.782610 | E_var:     5.2111 | E_err:   0.025816
[2025-11-01 00:05:47] [Iter 1186/2362] R3[23/1200]   | LR: 0.049955 | E:  -59.776610 | E_var:     5.2198 | E_err:   0.026920
[2025-11-01 00:06:04] [Iter 1187/2362] R3[24/1200]   | LR: 0.049951 | E:  -59.897523 | E_var:     5.2562 | E_err:   0.026676
[2025-11-01 00:06:21] [Iter 1188/2362] R3[25/1200]   | LR: 0.049946 | E:  -59.857383 | E_var:     5.2403 | E_err:   0.026008
[2025-11-01 00:06:38] [Iter 1189/2362] R3[26/1200]   | LR: 0.049942 | E:  -59.945488 | E_var:     5.3172 | E_err:   0.025379
[2025-11-01 00:06:56] [Iter 1190/2362] R3[27/1200]   | LR: 0.049938 | E:  -59.917658 | E_var:     5.5497 | E_err:   0.026501
[2025-11-01 00:07:13] [Iter 1191/2362] R3[28/1200]   | LR: 0.049933 | E:  -59.933221 | E_var:     5.5279 | E_err:   0.026412
[2025-11-01 00:07:30] [Iter 1192/2362] R3[29/1200]   | LR: 0.049928 | E:  -59.887455 | E_var:     5.6780 | E_err:   0.027497
[2025-11-01 00:07:47] [Iter 1193/2362] R3[30/1200]   | LR: 0.049923 | E:  -59.945090 | E_var:     5.5790 | E_err:   0.026614
[2025-11-01 00:08:04] [Iter 1194/2362] R3[31/1200]   | LR: 0.049918 | E:  -59.864958 | E_var:     5.4617 | E_err:   0.026866
[2025-11-01 00:08:22] [Iter 1195/2362] R3[32/1200]   | LR: 0.049912 | E:  -59.784046 | E_var:     5.2507 | E_err:   0.026152
[2025-11-01 00:08:39] [Iter 1196/2362] R3[33/1200]   | LR: 0.049907 | E:  -59.837594 | E_var:     5.3915 | E_err:   0.026578
[2025-11-01 00:08:56] [Iter 1197/2362] R3[34/1200]   | LR: 0.049901 | E:  -59.824079 | E_var:     5.2675 | E_err:   0.026516
[2025-11-01 00:09:13] [Iter 1198/2362] R3[35/1200]   | LR: 0.049895 | E:  -59.819057 | E_var:     5.1144 | E_err:   0.025317
[2025-11-01 00:09:31] [Iter 1199/2362] R3[36/1200]   | LR: 0.049889 | E:  -59.732704 | E_var:     5.2724 | E_err:   0.026215
[2025-11-01 00:09:48] [Iter 1200/2362] R3[37/1200]   | LR: 0.049883 | E:  -59.775920 | E_var:     5.2306 | E_err:   0.026136
[2025-11-01 00:10:05] [Iter 1201/2362] R3[38/1200]   | LR: 0.049876 | E:  -59.855647 | E_var:     5.5421 | E_err:   0.026759
[2025-11-01 00:10:22] [Iter 1202/2362] R3[39/1200]   | LR: 0.049870 | E:  -59.930203 | E_var:     5.6562 | E_err:   0.027559
[2025-11-01 00:10:39] [Iter 1203/2362] R3[40/1200]   | LR: 0.049863 | E:  -59.921136 | E_var:     5.3277 | E_err:   0.025411
[2025-11-01 00:10:57] [Iter 1204/2362] R3[41/1200]   | LR: 0.049856 | E:  -59.895273 | E_var:     5.4185 | E_err:   0.026469
[2025-11-01 00:11:14] [Iter 1205/2362] R3[42/1200]   | LR: 0.049849 | E:  -59.934336 | E_var:     5.4942 | E_err:   0.025832
[2025-11-01 00:11:31] [Iter 1206/2362] R3[43/1200]   | LR: 0.049842 | E:  -59.948166 | E_var:     5.6467 | E_err:   0.026156
[2025-11-01 00:11:48] [Iter 1207/2362] R3[44/1200]   | LR: 0.049834 | E:  -59.911362 | E_var:     5.2428 | E_err:   0.026432
[2025-11-01 00:12:05] [Iter 1208/2362] R3[45/1200]   | LR: 0.049827 | E:  -59.968812 | E_var:     5.3347 | E_err:   0.026026
[2025-11-01 00:12:23] [Iter 1209/2362] R3[46/1200]   | LR: 0.049819 | E:  -59.881602 | E_var:     5.4360 | E_err:   0.025982
[2025-11-01 00:12:40] [Iter 1210/2362] R3[47/1200]   | LR: 0.049811 | E:  -59.910901 | E_var:     5.4697 | E_err:   0.026173
[2025-11-01 00:12:57] [Iter 1211/2362] R3[48/1200]   | LR: 0.049803 | E:  -59.894905 | E_var:     5.3628 | E_err:   0.026789
[2025-11-01 00:13:14] [Iter 1212/2362] R3[49/1200]   | LR: 0.049795 | E:  -59.885558 | E_var:     5.3644 | E_err:   0.026840
[2025-11-01 00:13:32] [Iter 1213/2362] R3[50/1200]   | LR: 0.049786 | E:  -59.856019 | E_var:     5.3461 | E_err:   0.026536
[2025-11-01 00:13:49] [Iter 1214/2362] R3[51/1200]   | LR: 0.049777 | E:  -59.922532 | E_var:     5.2666 | E_err:   0.026315
[2025-11-01 00:14:06] [Iter 1215/2362] R3[52/1200]   | LR: 0.049769 | E:  -59.890765 | E_var:     5.2133 | E_err:   0.025620
[2025-11-01 00:14:23] [Iter 1216/2362] R3[53/1200]   | LR: 0.049760 | E:  -59.864590 | E_var:     5.2927 | E_err:   0.027293
[2025-11-01 00:14:40] [Iter 1217/2362] R3[54/1200]   | LR: 0.049751 | E:  -59.875425 | E_var:     5.1660 | E_err:   0.025392
[2025-11-01 00:14:58] [Iter 1218/2362] R3[55/1200]   | LR: 0.049741 | E:  -59.908934 | E_var:     5.2745 | E_err:   0.026090
[2025-11-01 00:15:15] [Iter 1219/2362] R3[56/1200]   | LR: 0.049732 | E:  -59.925406 | E_var:     5.4063 | E_err:   0.025143
[2025-11-01 00:15:32] [Iter 1220/2362] R3[57/1200]   | LR: 0.049722 | E:  -59.879576 | E_var:     5.3952 | E_err:   0.025645
[2025-11-01 00:15:49] [Iter 1221/2362] R3[58/1200]   | LR: 0.049712 | E:  -59.913006 | E_var:     5.3709 | E_err:   0.026511
[2025-11-01 00:16:07] [Iter 1222/2362] R3[59/1200]   | LR: 0.049702 | E:  -59.882669 | E_var:     5.2709 | E_err:   0.025842
[2025-11-01 00:16:24] [Iter 1223/2362] R3[60/1200]   | LR: 0.049692 | E:  -59.864372 | E_var:     5.2499 | E_err:   0.026055
[2025-11-01 00:16:41] [Iter 1224/2362] R3[61/1200]   | LR: 0.049682 | E:  -59.835697 | E_var:     5.1563 | E_err:   0.024963
[2025-11-01 00:16:58] [Iter 1225/2362] R3[62/1200]   | LR: 0.049671 | E:  -59.901280 | E_var:     5.3863 | E_err:   0.026108
[2025-11-01 00:17:15] [Iter 1226/2362] R3[63/1200]   | LR: 0.049661 | E:  -59.894993 | E_var:     5.2909 | E_err:   0.026840
[2025-11-01 00:17:33] [Iter 1227/2362] R3[64/1200]   | LR: 0.049650 | E:  -59.937760 | E_var:     5.2823 | E_err:   0.026585
[2025-11-01 00:17:50] [Iter 1228/2362] R3[65/1200]   | LR: 0.049639 | E:  -59.897594 | E_var:     5.2513 | E_err:   0.025976
[2025-11-01 00:18:07] [Iter 1229/2362] R3[66/1200]   | LR: 0.049628 | E:  -59.863520 | E_var:     5.1935 | E_err:   0.025534
[2025-11-01 00:18:24] [Iter 1230/2362] R3[67/1200]   | LR: 0.049616 | E:  -59.851978 | E_var:     5.1106 | E_err:   0.025883
[2025-11-01 00:18:41] [Iter 1231/2362] R3[68/1200]   | LR: 0.049605 | E:  -59.953760 | E_var:     5.2184 | E_err:   0.025654
[2025-11-01 00:18:59] [Iter 1232/2362] R3[69/1200]   | LR: 0.049593 | E:  -59.914311 | E_var:     5.5884 | E_err:   0.026945
[2025-11-01 00:19:16] [Iter 1233/2362] R3[70/1200]   | LR: 0.049581 | E:  -59.941444 | E_var:     5.2217 | E_err:   0.025436
[2025-11-01 00:19:33] [Iter 1234/2362] R3[71/1200]   | LR: 0.049569 | E:  -59.883734 | E_var:     5.5724 | E_err:   0.027524
[2025-11-01 00:19:50] [Iter 1235/2362] R3[72/1200]   | LR: 0.049557 | E:  -59.913986 | E_var:     5.5720 | E_err:   0.026065
[2025-11-01 00:20:08] [Iter 1236/2362] R3[73/1200]   | LR: 0.049545 | E:  -59.923352 | E_var:     5.5312 | E_err:   0.026882
[2025-11-01 00:20:25] [Iter 1237/2362] R3[74/1200]   | LR: 0.049532 | E:  -59.925746 | E_var:     5.2381 | E_err:   0.025633
[2025-11-01 00:20:42] [Iter 1238/2362] R3[75/1200]   | LR: 0.049520 | E:  -59.906928 | E_var:     5.2754 | E_err:   0.026143
[2025-11-01 00:20:59] [Iter 1239/2362] R3[76/1200]   | LR: 0.049507 | E:  -59.940068 | E_var:     5.6993 | E_err:   0.026809
[2025-11-01 00:21:16] [Iter 1240/2362] R3[77/1200]   | LR: 0.049494 | E:  -59.952449 | E_var:     5.8015 | E_err:   0.026794
[2025-11-01 00:21:34] [Iter 1241/2362] R3[78/1200]   | LR: 0.049481 | E:  -59.859875 | E_var:     5.9166 | E_err:   0.027969
[2025-11-01 00:21:51] [Iter 1242/2362] R3[79/1200]   | LR: 0.049467 | E:  -59.893551 | E_var:     5.8259 | E_err:   0.027017
[2025-11-01 00:22:08] [Iter 1243/2362] R3[80/1200]   | LR: 0.049454 | E:  -59.912374 | E_var:     5.7041 | E_err:   0.027589
[2025-11-01 00:22:25] [Iter 1244/2362] R3[81/1200]   | LR: 0.049440 | E:  -59.922689 | E_var:     5.2951 | E_err:   0.025641
[2025-11-01 00:22:43] [Iter 1245/2362] R3[82/1200]   | LR: 0.049426 | E:  -59.964769 | E_var:     5.4858 | E_err:   0.026098
[2025-11-01 00:23:00] [Iter 1246/2362] R3[83/1200]   | LR: 0.049412 | E:  -59.959087 | E_var:     5.3318 | E_err:   0.025986
[2025-11-01 00:23:17] [Iter 1247/2362] R3[84/1200]   | LR: 0.049398 | E:  -59.936312 | E_var:     5.3082 | E_err:   0.025240
[2025-11-01 00:23:34] [Iter 1248/2362] R3[85/1200]   | LR: 0.049384 | E:  -59.953051 | E_var:     5.3884 | E_err:   0.026199
[2025-11-01 00:23:51] [Iter 1249/2362] R3[86/1200]   | LR: 0.049369 | E:  -59.920688 | E_var:     5.5235 | E_err:   0.025435
[2025-11-01 00:24:09] [Iter 1250/2362] R3[87/1200]   | LR: 0.049354 | E:  -59.948151 | E_var:     5.4724 | E_err:   0.027306
[2025-11-01 00:24:26] [Iter 1251/2362] R3[88/1200]   | LR: 0.049339 | E:  -59.950981 | E_var:     5.4984 | E_err:   0.026912
[2025-11-01 00:24:43] [Iter 1252/2362] R3[89/1200]   | LR: 0.049324 | E:  -59.900098 | E_var:     5.2004 | E_err:   0.025909
[2025-11-01 00:25:00] [Iter 1253/2362] R3[90/1200]   | LR: 0.049309 | E:  -59.965447 | E_var:     4.9738 | E_err:   0.025234
[2025-11-01 00:25:18] [Iter 1254/2362] R3[91/1200]   | LR: 0.049294 | E:  -59.933064 | E_var:     5.2430 | E_err:   0.025192
[2025-11-01 00:25:35] [Iter 1255/2362] R3[92/1200]   | LR: 0.049278 | E:  -59.959199 | E_var:     5.2866 | E_err:   0.026794
[2025-11-01 00:25:52] [Iter 1256/2362] R3[93/1200]   | LR: 0.049263 | E:  -59.932715 | E_var:     5.4169 | E_err:   0.026265
[2025-11-01 00:26:09] [Iter 1257/2362] R3[94/1200]   | LR: 0.049247 | E:  -59.938420 | E_var:     5.1326 | E_err:   0.024779
[2025-11-01 00:26:26] [Iter 1258/2362] R3[95/1200]   | LR: 0.049231 | E:  -59.934985 | E_var:     5.2663 | E_err:   0.026653
[2025-11-01 00:26:44] [Iter 1259/2362] R3[96/1200]   | LR: 0.049215 | E:  -59.887531 | E_var:     5.1620 | E_err:   0.026155
[2025-11-01 00:27:01] [Iter 1260/2362] R3[97/1200]   | LR: 0.049198 | E:  -59.917444 | E_var:     5.2281 | E_err:   0.026093
[2025-11-01 00:27:18] [Iter 1261/2362] R3[98/1200]   | LR: 0.049182 | E:  -59.929199 | E_var:     5.2692 | E_err:   0.026292
[2025-11-01 00:27:35] [Iter 1262/2362] R3[99/1200]   | LR: 0.049165 | E:  -59.897271 | E_var:     5.2158 | E_err:   0.026083
[2025-11-01 00:27:52] [Iter 1263/2362] R3[100/1200]  | LR: 0.049148 | E:  -59.980095 | E_var:     5.4477 | E_err:   0.026926
[2025-11-01 00:28:10] [Iter 1264/2362] R3[101/1200]  | LR: 0.049131 | E:  -59.962719 | E_var:     5.2084 | E_err:   0.025097
[2025-11-01 00:28:27] [Iter 1265/2362] R3[102/1200]  | LR: 0.049114 | E:  -59.895582 | E_var:     5.2973 | E_err:   0.026913
[2025-11-01 00:28:44] [Iter 1266/2362] R3[103/1200]  | LR: 0.049097 | E:  -59.944887 | E_var:     5.0946 | E_err:   0.024732
[2025-11-01 00:29:01] [Iter 1267/2362] R3[104/1200]  | LR: 0.049079 | E:  -59.942129 | E_var:     5.1713 | E_err:   0.025448
[2025-11-01 00:29:19] [Iter 1268/2362] R3[105/1200]  | LR: 0.049061 | E:  -59.898226 | E_var:     5.0487 | E_err:   0.025834
[2025-11-01 00:29:36] [Iter 1269/2362] R3[106/1200]  | LR: 0.049044 | E:  -59.877264 | E_var:     5.1043 | E_err:   0.025276
[2025-11-01 00:29:53] [Iter 1270/2362] R3[107/1200]  | LR: 0.049026 | E:  -59.906049 | E_var:     5.1993 | E_err:   0.026549
[2025-11-01 00:30:10] [Iter 1271/2362] R3[108/1200]  | LR: 0.049007 | E:  -59.884082 | E_var:     5.1934 | E_err:   0.025436
[2025-11-01 00:30:27] [Iter 1272/2362] R3[109/1200]  | LR: 0.048989 | E:  -59.888877 | E_var:     5.1056 | E_err:   0.025470
[2025-11-01 00:30:45] [Iter 1273/2362] R3[110/1200]  | LR: 0.048971 | E:  -59.797822 | E_var:     5.1073 | E_err:   0.025861
[2025-11-01 00:31:02] [Iter 1274/2362] R3[111/1200]  | LR: 0.048952 | E:  -59.870244 | E_var:     5.2391 | E_err:   0.026205
[2025-11-01 00:31:19] [Iter 1275/2362] R3[112/1200]  | LR: 0.048933 | E:  -59.894983 | E_var:     5.0434 | E_err:   0.024950
[2025-11-01 00:31:36] [Iter 1276/2362] R3[113/1200]  | LR: 0.048914 | E:  -59.914911 | E_var:     5.4308 | E_err:   0.026055
[2025-11-01 00:31:53] [Iter 1277/2362] R3[114/1200]  | LR: 0.048895 | E:  -59.922893 | E_var:     5.0822 | E_err:   0.025690
[2025-11-01 00:32:11] [Iter 1278/2362] R3[115/1200]  | LR: 0.048876 | E:  -59.868528 | E_var:     5.2550 | E_err:   0.025769
[2025-11-01 00:32:28] [Iter 1279/2362] R3[116/1200]  | LR: 0.048856 | E:  -59.965902 | E_var:     5.1597 | E_err:   0.025641
[2025-11-01 00:32:45] [Iter 1280/2362] R3[117/1200]  | LR: 0.048836 | E:  -59.883443 | E_var:     5.0670 | E_err:   0.025737
[2025-11-01 00:33:02] [Iter 1281/2362] R3[118/1200]  | LR: 0.048817 | E:  -59.911708 | E_var:     5.1981 | E_err:   0.025175
[2025-11-01 00:33:20] [Iter 1282/2362] R3[119/1200]  | LR: 0.048797 | E:  -59.940668 | E_var:     5.0689 | E_err:   0.025123
[2025-11-01 00:33:37] [Iter 1283/2362] R3[120/1200]  | LR: 0.048776 | E:  -59.879848 | E_var:     5.2057 | E_err:   0.025561
[2025-11-01 00:33:54] [Iter 1284/2362] R3[121/1200]  | LR: 0.048756 | E:  -59.974971 | E_var:     5.3602 | E_err:   0.026623
[2025-11-01 00:34:11] [Iter 1285/2362] R3[122/1200]  | LR: 0.048736 | E:  -59.844286 | E_var:     5.5965 | E_err:   0.026354
[2025-11-01 00:34:28] [Iter 1286/2362] R3[123/1200]  | LR: 0.048715 | E:  -59.890803 | E_var:     5.0999 | E_err:   0.025213
[2025-11-01 00:34:46] [Iter 1287/2362] R3[124/1200]  | LR: 0.048694 | E:  -59.840494 | E_var:     5.0077 | E_err:   0.026606
[2025-11-01 00:35:03] [Iter 1288/2362] R3[125/1200]  | LR: 0.048673 | E:  -59.835014 | E_var:     5.0719 | E_err:   0.026124
[2025-11-01 00:35:20] [Iter 1289/2362] R3[126/1200]  | LR: 0.048652 | E:  -59.937328 | E_var:     5.1144 | E_err:   0.025308
[2025-11-01 00:35:37] [Iter 1290/2362] R3[127/1200]  | LR: 0.048631 | E:  -59.947464 | E_var:     5.1969 | E_err:   0.025848
[2025-11-01 00:35:55] [Iter 1291/2362] R3[128/1200]  | LR: 0.048609 | E:  -59.924429 | E_var:     5.1505 | E_err:   0.025203
[2025-11-01 00:36:12] [Iter 1292/2362] R3[129/1200]  | LR: 0.048588 | E:  -59.887445 | E_var:     4.9497 | E_err:   0.024903
[2025-11-01 00:36:29] [Iter 1293/2362] R3[130/1200]  | LR: 0.048566 | E:  -59.893608 | E_var:     5.2927 | E_err:   0.025403
[2025-11-01 00:36:46] [Iter 1294/2362] R3[131/1200]  | LR: 0.048544 | E:  -59.893089 | E_var:     5.1986 | E_err:   0.025703
[2025-11-01 00:37:03] [Iter 1295/2362] R3[132/1200]  | LR: 0.048522 | E:  -59.911074 | E_var:     5.2081 | E_err:   0.025199
[2025-11-01 00:37:21] [Iter 1296/2362] R3[133/1200]  | LR: 0.048500 | E:  -59.942708 | E_var:     5.1300 | E_err:   0.024707
[2025-11-01 00:37:38] [Iter 1297/2362] R3[134/1200]  | LR: 0.048477 | E:  -59.913148 | E_var:     5.2940 | E_err:   0.026043
[2025-11-01 00:37:55] [Iter 1298/2362] R3[135/1200]  | LR: 0.048455 | E:  -59.928574 | E_var:     4.9086 | E_err:   0.025116
[2025-11-01 00:38:12] [Iter 1299/2362] R3[136/1200]  | LR: 0.048432 | E:  -59.905647 | E_var:     5.1821 | E_err:   0.025763
[2025-11-01 00:38:29] [Iter 1300/2362] R3[137/1200]  | LR: 0.048409 | E:  -59.939430 | E_var:     5.1381 | E_err:   0.025267
[2025-11-01 00:38:47] [Iter 1301/2362] R3[138/1200]  | LR: 0.048386 | E:  -59.922148 | E_var:     5.2327 | E_err:   0.025802
[2025-11-01 00:39:04] [Iter 1302/2362] R3[139/1200]  | LR: 0.048363 | E:  -59.940127 | E_var:     5.2597 | E_err:   0.025401
[2025-11-01 00:39:21] [Iter 1303/2362] R3[140/1200]  | LR: 0.048340 | E:  -59.996575 | E_var:     5.1673 | E_err:   0.026020
[2025-11-01 00:39:38] [Iter 1304/2362] R3[141/1200]  | LR: 0.048316 | E:  -59.972021 | E_var:     5.0075 | E_err:   0.025792
[2025-11-01 00:39:56] [Iter 1305/2362] R3[142/1200]  | LR: 0.048292 | E:  -59.944175 | E_var:     5.0042 | E_err:   0.024918
[2025-11-01 00:40:13] [Iter 1306/2362] R3[143/1200]  | LR: 0.048268 | E:  -59.882754 | E_var:     5.1437 | E_err:   0.026034
[2025-11-01 00:40:30] [Iter 1307/2362] R3[144/1200]  | LR: 0.048244 | E:  -59.881367 | E_var:     4.9587 | E_err:   0.025333
[2025-11-01 00:40:47] [Iter 1308/2362] R3[145/1200]  | LR: 0.048220 | E:  -59.937499 | E_var:     5.1842 | E_err:   0.025402
[2025-11-01 00:41:04] [Iter 1309/2362] R3[146/1200]  | LR: 0.048196 | E:  -59.972861 | E_var:     5.4541 | E_err:   0.025840
[2025-11-01 00:41:22] [Iter 1310/2362] R3[147/1200]  | LR: 0.048171 | E:  -59.951663 | E_var:     5.2172 | E_err:   0.025843
[2025-11-01 00:41:39] [Iter 1311/2362] R3[148/1200]  | LR: 0.048147 | E:  -60.012768 | E_var:     5.3335 | E_err:   0.025910
[2025-11-01 00:41:56] [Iter 1312/2362] R3[149/1200]  | LR: 0.048122 | E:  -59.947312 | E_var:     5.3215 | E_err:   0.025962
[2025-11-01 00:42:13] [Iter 1313/2362] R3[150/1200]  | LR: 0.048097 | E:  -59.951114 | E_var:     5.1558 | E_err:   0.026060
[2025-11-01 00:42:30] [Iter 1314/2362] R3[151/1200]  | LR: 0.048072 | E:  -59.938213 | E_var:     5.2462 | E_err:   0.026192
[2025-11-01 00:42:48] [Iter 1315/2362] R3[152/1200]  | LR: 0.048047 | E:  -59.930939 | E_var:     5.4151 | E_err:   0.026898
[2025-11-01 00:43:05] [Iter 1316/2362] R3[153/1200]  | LR: 0.048021 | E:  -59.965538 | E_var:     5.4397 | E_err:   0.026641
[2025-11-01 00:43:22] [Iter 1317/2362] R3[154/1200]  | LR: 0.047996 | E:  -59.931077 | E_var:     5.1692 | E_err:   0.026283
[2025-11-01 00:43:39] [Iter 1318/2362] R3[155/1200]  | LR: 0.047970 | E:  -59.966163 | E_var:     5.0842 | E_err:   0.024477
[2025-11-01 00:43:57] [Iter 1319/2362] R3[156/1200]  | LR: 0.047944 | E:  -59.927746 | E_var:     5.1909 | E_err:   0.026257
[2025-11-01 00:44:14] [Iter 1320/2362] R3[157/1200]  | LR: 0.047918 | E:  -59.980409 | E_var:     5.1142 | E_err:   0.025038
[2025-11-01 00:44:31] [Iter 1321/2362] R3[158/1200]  | LR: 0.047892 | E:  -59.949162 | E_var:     5.3087 | E_err:   0.025701
[2025-11-01 00:44:48] [Iter 1322/2362] R3[159/1200]  | LR: 0.047865 | E:  -59.935111 | E_var:     5.4044 | E_err:   0.025886
[2025-11-01 00:45:05] [Iter 1323/2362] R3[160/1200]  | LR: 0.047839 | E:  -59.996113 | E_var:     5.6662 | E_err:   0.026828
[2025-11-01 00:45:23] [Iter 1324/2362] R3[161/1200]  | LR: 0.047812 | E:  -59.999319 | E_var:     5.3072 | E_err:   0.025615
[2025-11-01 00:45:40] [Iter 1325/2362] R3[162/1200]  | LR: 0.047785 | E:  -59.939451 | E_var:     5.4762 | E_err:   0.026285
[2025-11-01 00:45:57] [Iter 1326/2362] R3[163/1200]  | LR: 0.047758 | E:  -59.972962 | E_var:     5.4381 | E_err:   0.026823
[2025-11-01 00:46:14] [Iter 1327/2362] R3[164/1200]  | LR: 0.047731 | E:  -59.974942 | E_var:     5.2809 | E_err:   0.025091
[2025-11-01 00:46:32] [Iter 1328/2362] R3[165/1200]  | LR: 0.047704 | E:  -59.968202 | E_var:     5.2372 | E_err:   0.025429
[2025-11-01 00:46:49] [Iter 1329/2362] R3[166/1200]  | LR: 0.047676 | E:  -60.004976 | E_var:     5.4477 | E_err:   0.026632
[2025-11-01 00:47:06] [Iter 1330/2362] R3[167/1200]  | LR: 0.047649 | E:  -59.938951 | E_var:     5.3311 | E_err:   0.026510
[2025-11-01 00:47:23] [Iter 1331/2362] R3[168/1200]  | LR: 0.047621 | E:  -59.947409 | E_var:     5.1971 | E_err:   0.026056
[2025-11-01 00:47:40] [Iter 1332/2362] R3[169/1200]  | LR: 0.047593 | E:  -59.940786 | E_var:     5.2124 | E_err:   0.025771
[2025-11-01 00:47:58] [Iter 1333/2362] R3[170/1200]  | LR: 0.047565 | E:  -59.971860 | E_var:     5.2289 | E_err:   0.025998
[2025-11-01 00:48:15] [Iter 1334/2362] R3[171/1200]  | LR: 0.047536 | E:  -60.023866 | E_var:     5.2223 | E_err:   0.025897
[2025-11-01 00:48:32] [Iter 1335/2362] R3[172/1200]  | LR: 0.047508 | E:  -59.934527 | E_var:     5.3196 | E_err:   0.025814
[2025-11-01 00:48:49] [Iter 1336/2362] R3[173/1200]  | LR: 0.047479 | E:  -59.973822 | E_var:     5.2957 | E_err:   0.026144
[2025-11-01 00:49:06] [Iter 1337/2362] R3[174/1200]  | LR: 0.047451 | E:  -59.981621 | E_var:     5.3880 | E_err:   0.026371
[2025-11-01 00:49:24] [Iter 1338/2362] R3[175/1200]  | LR: 0.047422 | E:  -59.981336 | E_var:     5.5855 | E_err:   0.027308
[2025-11-01 00:49:41] [Iter 1339/2362] R3[176/1200]  | LR: 0.047393 | E:  -59.996471 | E_var:     5.3710 | E_err:   0.026653
[2025-11-01 00:49:58] [Iter 1340/2362] R3[177/1200]  | LR: 0.047364 | E:  -59.987858 | E_var:     5.4144 | E_err:   0.026462
[2025-11-01 00:50:15] [Iter 1341/2362] R3[178/1200]  | LR: 0.047334 | E:  -59.928701 | E_var:     5.1602 | E_err:   0.025827
[2025-11-01 00:50:33] [Iter 1342/2362] R3[179/1200]  | LR: 0.047305 | E:  -59.936917 | E_var:     5.2656 | E_err:   0.025572
[2025-11-01 00:50:50] [Iter 1343/2362] R3[180/1200]  | LR: 0.047275 | E:  -59.988020 | E_var:     5.1592 | E_err:   0.025392
[2025-11-01 00:51:07] [Iter 1344/2362] R3[181/1200]  | LR: 0.047245 | E:  -59.984463 | E_var:     5.0846 | E_err:   0.025646
[2025-11-01 00:51:24] [Iter 1345/2362] R3[182/1200]  | LR: 0.047215 | E:  -59.965200 | E_var:     5.3740 | E_err:   0.026764
[2025-11-01 00:51:41] [Iter 1346/2362] R3[183/1200]  | LR: 0.047185 | E:  -59.961641 | E_var:     5.1673 | E_err:   0.025994
[2025-11-01 00:51:59] [Iter 1347/2362] R3[184/1200]  | LR: 0.047155 | E:  -59.934356 | E_var:     5.0201 | E_err:   0.025508
[2025-11-01 00:52:16] [Iter 1348/2362] R3[185/1200]  | LR: 0.047125 | E:  -59.928129 | E_var:     5.0017 | E_err:   0.025670
[2025-11-01 00:52:33] [Iter 1349/2362] R3[186/1200]  | LR: 0.047094 | E:  -59.957027 | E_var:     5.1757 | E_err:   0.025648
[2025-11-01 00:52:50] [Iter 1350/2362] R3[187/1200]  | LR: 0.047063 | E:  -59.962287 | E_var:     5.2600 | E_err:   0.025892
[2025-11-01 00:53:07] [Iter 1351/2362] R3[188/1200]  | LR: 0.047033 | E:  -59.997611 | E_var:     5.2981 | E_err:   0.025955
[2025-11-01 00:53:25] [Iter 1352/2362] R3[189/1200]  | LR: 0.047002 | E:  -59.944920 | E_var:     5.0033 | E_err:   0.025004
[2025-11-01 00:53:42] [Iter 1353/2362] R3[190/1200]  | LR: 0.046970 | E:  -59.916515 | E_var:     5.0202 | E_err:   0.025665
[2025-11-01 00:53:59] [Iter 1354/2362] R3[191/1200]  | LR: 0.046939 | E:  -59.952541 | E_var:     5.1505 | E_err:   0.024883
[2025-11-01 00:54:16] [Iter 1355/2362] R3[192/1200]  | LR: 0.046908 | E:  -59.998973 | E_var:     5.1724 | E_err:   0.025420
[2025-11-01 00:54:34] [Iter 1356/2362] R3[193/1200]  | LR: 0.046876 | E:  -59.971082 | E_var:     5.1130 | E_err:   0.025226
[2025-11-01 00:54:51] [Iter 1357/2362] R3[194/1200]  | LR: 0.046844 | E:  -59.954246 | E_var:     5.0235 | E_err:   0.024640
[2025-11-01 00:55:08] [Iter 1358/2362] R3[195/1200]  | LR: 0.046812 | E:  -59.954235 | E_var:     5.2406 | E_err:   0.026256
[2025-11-01 00:55:25] [Iter 1359/2362] R3[196/1200]  | LR: 0.046780 | E:  -60.006402 | E_var:     5.2428 | E_err:   0.026092
[2025-11-01 00:55:42] [Iter 1360/2362] R3[197/1200]  | LR: 0.046748 | E:  -59.971096 | E_var:     5.6257 | E_err:   0.026272
[2025-11-01 00:56:00] [Iter 1361/2362] R3[198/1200]  | LR: 0.046716 | E:  -59.902726 | E_var:     5.8541 | E_err:   0.028070
[2025-11-01 00:56:17] [Iter 1362/2362] R3[199/1200]  | LR: 0.046683 | E:  -59.979238 | E_var:     5.3081 | E_err:   0.025519
[2025-11-01 00:56:17] ✓ Checkpoint saved: checkpoint_iter_001250.pkl
[2025-11-01 00:56:34] [Iter 1363/2362] R3[200/1200]  | LR: 0.046651 | E:  -59.976573 | E_var:     5.3847 | E_err:   0.025812
[2025-11-01 00:56:51] [Iter 1364/2362] R3[201/1200]  | LR: 0.046618 | E:  -59.972819 | E_var:     5.1538 | E_err:   0.025814
[2025-11-01 00:57:09] [Iter 1365/2362] R3[202/1200]  | LR: 0.046585 | E:  -59.918175 | E_var:     5.0404 | E_err:   0.025437
[2025-11-01 00:57:26] [Iter 1366/2362] R3[203/1200]  | LR: 0.046552 | E:  -59.956881 | E_var:     5.1876 | E_err:   0.025805
[2025-11-01 00:57:43] [Iter 1367/2362] R3[204/1200]  | LR: 0.046519 | E:  -59.917500 | E_var:     5.0562 | E_err:   0.025741
[2025-11-01 00:58:00] [Iter 1368/2362] R3[205/1200]  | LR: 0.046485 | E:  -60.028446 | E_var:     5.1835 | E_err:   0.025483
[2025-11-01 00:58:18] [Iter 1369/2362] R3[206/1200]  | LR: 0.046452 | E:  -60.002999 | E_var:     5.1933 | E_err:   0.024991
[2025-11-01 00:58:35] [Iter 1370/2362] R3[207/1200]  | LR: 0.046418 | E:  -59.957989 | E_var:     4.9522 | E_err:   0.024536
[2025-11-01 00:58:52] [Iter 1371/2362] R3[208/1200]  | LR: 0.046384 | E:  -59.981052 | E_var:     5.2598 | E_err:   0.025885
[2025-11-01 00:59:09] [Iter 1372/2362] R3[209/1200]  | LR: 0.046350 | E:  -59.967801 | E_var:     5.1146 | E_err:   0.026233
[2025-11-01 00:59:26] [Iter 1373/2362] R3[210/1200]  | LR: 0.046316 | E:  -59.962493 | E_var:     4.9992 | E_err:   0.024801
[2025-11-01 00:59:44] [Iter 1374/2362] R3[211/1200]  | LR: 0.046282 | E:  -59.959372 | E_var:     5.3239 | E_err:   0.026771
[2025-11-01 01:00:01] [Iter 1375/2362] R3[212/1200]  | LR: 0.046247 | E:  -59.985665 | E_var:     5.2280 | E_err:   0.025581
[2025-11-01 01:00:18] [Iter 1376/2362] R3[213/1200]  | LR: 0.046213 | E:  -59.946102 | E_var:     5.1124 | E_err:   0.025822
[2025-11-01 01:00:35] [Iter 1377/2362] R3[214/1200]  | LR: 0.046178 | E:  -59.972370 | E_var:     5.4004 | E_err:   0.026097
[2025-11-01 01:00:52] [Iter 1378/2362] R3[215/1200]  | LR: 0.046143 | E:  -59.940293 | E_var:     5.3343 | E_err:   0.026614
[2025-11-01 01:01:10] [Iter 1379/2362] R3[216/1200]  | LR: 0.046108 | E:  -60.000143 | E_var:     5.5192 | E_err:   0.026066
[2025-11-01 01:01:27] [Iter 1380/2362] R3[217/1200]  | LR: 0.046073 | E:  -59.946241 | E_var:     5.1997 | E_err:   0.025695
[2025-11-01 01:01:44] [Iter 1381/2362] R3[218/1200]  | LR: 0.046038 | E:  -59.969552 | E_var:     4.9686 | E_err:   0.024963
[2025-11-01 01:02:01] [Iter 1382/2362] R3[219/1200]  | LR: 0.046002 | E:  -59.994939 | E_var:     5.1165 | E_err:   0.024630
[2025-11-01 01:02:19] [Iter 1383/2362] R3[220/1200]  | LR: 0.045967 | E:  -59.976706 | E_var:     5.3992 | E_err:   0.025131
[2025-11-01 01:02:36] [Iter 1384/2362] R3[221/1200]  | LR: 0.045931 | E:  -60.011408 | E_var:     5.3793 | E_err:   0.026000
[2025-11-01 01:02:53] [Iter 1385/2362] R3[222/1200]  | LR: 0.045895 | E:  -59.936551 | E_var:     5.3475 | E_err:   0.026568
[2025-11-01 01:03:10] [Iter 1386/2362] R3[223/1200]  | LR: 0.045859 | E:  -59.926692 | E_var:     5.2053 | E_err:   0.025361
[2025-11-01 01:03:27] [Iter 1387/2362] R3[224/1200]  | LR: 0.045823 | E:  -59.965889 | E_var:     5.3774 | E_err:   0.025319
[2025-11-01 01:03:45] [Iter 1388/2362] R3[225/1200]  | LR: 0.045787 | E:  -60.004552 | E_var:     5.5340 | E_err:   0.027202
[2025-11-01 01:04:02] [Iter 1389/2362] R3[226/1200]  | LR: 0.045750 | E:  -59.971416 | E_var:     5.5040 | E_err:   0.026633
[2025-11-01 01:04:19] [Iter 1390/2362] R3[227/1200]  | LR: 0.045714 | E:  -59.937798 | E_var:     5.6482 | E_err:   0.027190
[2025-11-01 01:04:36] [Iter 1391/2362] R3[228/1200]  | LR: 0.045677 | E:  -59.919606 | E_var:     5.7184 | E_err:   0.027037
[2025-11-01 01:04:53] [Iter 1392/2362] R3[229/1200]  | LR: 0.045640 | E:  -59.981344 | E_var:     5.4597 | E_err:   0.025559
[2025-11-01 01:05:11] [Iter 1393/2362] R3[230/1200]  | LR: 0.045603 | E:  -59.962811 | E_var:     5.3694 | E_err:   0.026307
[2025-11-01 01:05:28] [Iter 1394/2362] R3[231/1200]  | LR: 0.045566 | E:  -59.945275 | E_var:     5.5410 | E_err:   0.026007
[2025-11-01 01:05:45] [Iter 1395/2362] R3[232/1200]  | LR: 0.045529 | E:  -59.986067 | E_var:     5.2173 | E_err:   0.025955
[2025-11-01 01:06:02] [Iter 1396/2362] R3[233/1200]  | LR: 0.045491 | E:  -59.960193 | E_var:     5.6033 | E_err:   0.026307
[2025-11-01 01:06:20] [Iter 1397/2362] R3[234/1200]  | LR: 0.045454 | E:  -59.980822 | E_var:     5.5105 | E_err:   0.025677
[2025-11-01 01:06:37] [Iter 1398/2362] R3[235/1200]  | LR: 0.045416 | E:  -59.987710 | E_var:     5.4246 | E_err:   0.026821
[2025-11-01 01:06:54] [Iter 1399/2362] R3[236/1200]  | LR: 0.045378 | E:  -59.954046 | E_var:     5.1444 | E_err:   0.025721
[2025-11-01 01:07:11] [Iter 1400/2362] R3[237/1200]  | LR: 0.045340 | E:  -59.975890 | E_var:     5.4386 | E_err:   0.025603
[2025-11-01 01:07:28] [Iter 1401/2362] R3[238/1200]  | LR: 0.045302 | E:  -60.005866 | E_var:     5.2713 | E_err:   0.025467
[2025-11-01 01:07:46] [Iter 1402/2362] R3[239/1200]  | LR: 0.045264 | E:  -59.992859 | E_var:     5.3706 | E_err:   0.026318
[2025-11-01 01:08:03] [Iter 1403/2362] R3[240/1200]  | LR: 0.045226 | E:  -59.981507 | E_var:     5.4025 | E_err:   0.026237
[2025-11-01 01:08:20] [Iter 1404/2362] R3[241/1200]  | LR: 0.045187 | E:  -59.986338 | E_var:     5.3413 | E_err:   0.025868
[2025-11-01 01:08:37] [Iter 1405/2362] R3[242/1200]  | LR: 0.045148 | E:  -59.985948 | E_var:     5.6000 | E_err:   0.026767
[2025-11-01 01:08:55] [Iter 1406/2362] R3[243/1200]  | LR: 0.045109 | E:  -59.980823 | E_var:     5.3752 | E_err:   0.026049
[2025-11-01 01:09:12] [Iter 1407/2362] R3[244/1200]  | LR: 0.045071 | E:  -59.992806 | E_var:     5.1108 | E_err:   0.026134
[2025-11-01 01:09:29] [Iter 1408/2362] R3[245/1200]  | LR: 0.045031 | E:  -59.967185 | E_var:     5.3771 | E_err:   0.026773
[2025-11-01 01:09:46] [Iter 1409/2362] R3[246/1200]  | LR: 0.044992 | E:  -59.935287 | E_var:     5.1725 | E_err:   0.025494
[2025-11-01 01:10:03] [Iter 1410/2362] R3[247/1200]  | LR: 0.044953 | E:  -59.964952 | E_var:     5.0857 | E_err:   0.025079
[2025-11-01 01:10:21] [Iter 1411/2362] R3[248/1200]  | LR: 0.044913 | E:  -59.962318 | E_var:     5.2773 | E_err:   0.025112
[2025-11-01 01:10:38] [Iter 1412/2362] R3[249/1200]  | LR: 0.044874 | E:  -59.926918 | E_var:     5.2118 | E_err:   0.025823
[2025-11-01 01:10:55] [Iter 1413/2362] R3[250/1200]  | LR: 0.044834 | E:  -59.946379 | E_var:     5.3732 | E_err:   0.025899
[2025-11-01 01:11:12] [Iter 1414/2362] R3[251/1200]  | LR: 0.044794 | E:  -59.962742 | E_var:     5.2909 | E_err:   0.025703
[2025-11-01 01:11:30] [Iter 1415/2362] R3[252/1200]  | LR: 0.044754 | E:  -59.925701 | E_var:     5.0670 | E_err:   0.025321
[2025-11-01 01:11:47] [Iter 1416/2362] R3[253/1200]  | LR: 0.044714 | E:  -59.952493 | E_var:     5.0847 | E_err:   0.025483
[2025-11-01 01:12:04] [Iter 1417/2362] R3[254/1200]  | LR: 0.044673 | E:  -60.000634 | E_var:     5.3446 | E_err:   0.025870
[2025-11-01 01:12:21] [Iter 1418/2362] R3[255/1200]  | LR: 0.044633 | E:  -59.983020 | E_var:     5.2228 | E_err:   0.026279
[2025-11-01 01:12:38] [Iter 1419/2362] R3[256/1200]  | LR: 0.044592 | E:  -59.974522 | E_var:     5.1152 | E_err:   0.025605
[2025-11-01 01:12:56] [Iter 1420/2362] R3[257/1200]  | LR: 0.044552 | E:  -59.939369 | E_var:     5.0954 | E_err:   0.026028
[2025-11-01 01:13:13] [Iter 1421/2362] R3[258/1200]  | LR: 0.044511 | E:  -59.952950 | E_var:     5.1337 | E_err:   0.025272
[2025-11-01 01:13:30] [Iter 1422/2362] R3[259/1200]  | LR: 0.044470 | E:  -59.941277 | E_var:     5.1488 | E_err:   0.025367
[2025-11-01 01:13:47] [Iter 1423/2362] R3[260/1200]  | LR: 0.044429 | E:  -59.953159 | E_var:     5.5487 | E_err:   0.026454
[2025-11-01 01:14:04] [Iter 1424/2362] R3[261/1200]  | LR: 0.044388 | E:  -59.938841 | E_var:     5.0865 | E_err:   0.024678
[2025-11-01 01:14:22] [Iter 1425/2362] R3[262/1200]  | LR: 0.044346 | E:  -59.979665 | E_var:     5.2398 | E_err:   0.025812
[2025-11-01 01:14:39] [Iter 1426/2362] R3[263/1200]  | LR: 0.044305 | E:  -59.988331 | E_var:     5.2043 | E_err:   0.026161
[2025-11-01 01:14:56] [Iter 1427/2362] R3[264/1200]  | LR: 0.044263 | E:  -59.980413 | E_var:     4.9694 | E_err:   0.024314
[2025-11-01 01:15:13] [Iter 1428/2362] R3[265/1200]  | LR: 0.044221 | E:  -59.956664 | E_var:     5.1105 | E_err:   0.025769
[2025-11-01 01:15:31] [Iter 1429/2362] R3[266/1200]  | LR: 0.044179 | E:  -60.023460 | E_var:     5.4034 | E_err:   0.026958
[2025-11-01 01:15:48] [Iter 1430/2362] R3[267/1200]  | LR: 0.044137 | E:  -59.976303 | E_var:     5.3819 | E_err:   0.026088
[2025-11-01 01:16:05] [Iter 1431/2362] R3[268/1200]  | LR: 0.044095 | E:  -59.993283 | E_var:     5.1385 | E_err:   0.025451
[2025-11-01 01:16:22] [Iter 1432/2362] R3[269/1200]  | LR: 0.044053 | E:  -59.936313 | E_var:     5.1743 | E_err:   0.026035
[2025-11-01 01:16:39] [Iter 1433/2362] R3[270/1200]  | LR: 0.044010 | E:  -59.925295 | E_var:     5.1917 | E_err:   0.026424
[2025-11-01 01:16:57] [Iter 1434/2362] R3[271/1200]  | LR: 0.043968 | E:  -59.906268 | E_var:     5.1118 | E_err:   0.024460
[2025-11-01 01:17:14] [Iter 1435/2362] R3[272/1200]  | LR: 0.043925 | E:  -59.854350 | E_var:     5.0793 | E_err:   0.026231
[2025-11-01 01:17:31] [Iter 1436/2362] R3[273/1200]  | LR: 0.043882 | E:  -59.845295 | E_var:     5.1967 | E_err:   0.025386
[2025-11-01 01:17:48] [Iter 1437/2362] R3[274/1200]  | LR: 0.043839 | E:  -59.864938 | E_var:     5.0530 | E_err:   0.026202
[2025-11-01 01:18:06] [Iter 1438/2362] R3[275/1200]  | LR: 0.043796 | E:  -59.987693 | E_var:     5.2221 | E_err:   0.025276
[2025-11-01 01:18:23] [Iter 1439/2362] R3[276/1200]  | LR: 0.043753 | E:  -59.994522 | E_var:     5.3359 | E_err:   0.026026
[2025-11-01 01:18:40] [Iter 1440/2362] R3[277/1200]  | LR: 0.043710 | E:  -60.008208 | E_var:     5.3359 | E_err:   0.026265
[2025-11-01 01:18:57] [Iter 1441/2362] R3[278/1200]  | LR: 0.043666 | E:  -59.998348 | E_var:     5.2377 | E_err:   0.025913
[2025-11-01 01:19:14] [Iter 1442/2362] R3[279/1200]  | LR: 0.043622 | E:  -59.964776 | E_var:     5.3784 | E_err:   0.025879
[2025-11-01 01:19:32] [Iter 1443/2362] R3[280/1200]  | LR: 0.043579 | E:  -59.987077 | E_var:     5.2076 | E_err:   0.025081
[2025-11-01 01:19:49] [Iter 1444/2362] R3[281/1200]  | LR: 0.043535 | E:  -60.002389 | E_var:     5.1258 | E_err:   0.025343
[2025-11-01 01:20:06] [Iter 1445/2362] R3[282/1200]  | LR: 0.043491 | E:  -59.996791 | E_var:     5.0012 | E_err:   0.025609
[2025-11-01 01:20:23] [Iter 1446/2362] R3[283/1200]  | LR: 0.043447 | E:  -59.973843 | E_var:     5.2012 | E_err:   0.025503
[2025-11-01 01:20:41] [Iter 1447/2362] R3[284/1200]  | LR: 0.043403 | E:  -59.976710 | E_var:     5.0208 | E_err:   0.026343
[2025-11-01 01:20:58] [Iter 1448/2362] R3[285/1200]  | LR: 0.043358 | E:  -59.975609 | E_var:     5.2577 | E_err:   0.025855
[2025-11-01 01:21:15] [Iter 1449/2362] R3[286/1200]  | LR: 0.043314 | E:  -59.991106 | E_var:     5.4139 | E_err:   0.026446
[2025-11-01 01:21:32] [Iter 1450/2362] R3[287/1200]  | LR: 0.043269 | E:  -59.980924 | E_var:     5.0496 | E_err:   0.024552
[2025-11-01 01:21:49] [Iter 1451/2362] R3[288/1200]  | LR: 0.043224 | E:  -59.985027 | E_var:     5.1453 | E_err:   0.025026
[2025-11-01 01:22:07] [Iter 1452/2362] R3[289/1200]  | LR: 0.043179 | E:  -59.967057 | E_var:     5.3991 | E_err:   0.025697
[2025-11-01 01:22:24] [Iter 1453/2362] R3[290/1200]  | LR: 0.043134 | E:  -59.986354 | E_var:     5.1734 | E_err:   0.025803
[2025-11-01 01:22:41] [Iter 1454/2362] R3[291/1200]  | LR: 0.043089 | E:  -59.998203 | E_var:     5.1000 | E_err:   0.025007
[2025-11-01 01:22:58] [Iter 1455/2362] R3[292/1200]  | LR: 0.043044 | E:  -59.975547 | E_var:     5.2606 | E_err:   0.026137
[2025-11-01 01:23:16] [Iter 1456/2362] R3[293/1200]  | LR: 0.042999 | E:  -59.894613 | E_var:     4.9831 | E_err:   0.025577
[2025-11-01 01:23:33] [Iter 1457/2362] R3[294/1200]  | LR: 0.042953 | E:  -59.953281 | E_var:     5.1562 | E_err:   0.025384
[2025-11-01 01:23:50] [Iter 1458/2362] R3[295/1200]  | LR: 0.042908 | E:  -59.962098 | E_var:     5.0312 | E_err:   0.025472
[2025-11-01 01:24:07] [Iter 1459/2362] R3[296/1200]  | LR: 0.042862 | E:  -59.995028 | E_var:     5.3868 | E_err:   0.025934
[2025-11-01 01:24:24] [Iter 1460/2362] R3[297/1200]  | LR: 0.042816 | E:  -60.025296 | E_var:     5.2516 | E_err:   0.024518
[2025-11-01 01:24:42] [Iter 1461/2362] R3[298/1200]  | LR: 0.042770 | E:  -60.004246 | E_var:     5.1506 | E_err:   0.025797
[2025-11-01 01:24:59] [Iter 1462/2362] R3[299/1200]  | LR: 0.042724 | E:  -59.982780 | E_var:     5.0203 | E_err:   0.025477
[2025-11-01 01:25:16] [Iter 1463/2362] R3[300/1200]  | LR: 0.042678 | E:  -60.000354 | E_var:     5.1526 | E_err:   0.025990
[2025-11-01 01:25:33] [Iter 1464/2362] R3[301/1200]  | LR: 0.042631 | E:  -59.957839 | E_var:     5.1899 | E_err:   0.025761
[2025-11-01 01:25:50] [Iter 1465/2362] R3[302/1200]  | LR: 0.042585 | E:  -60.004907 | E_var:     5.0186 | E_err:   0.025861
[2025-11-01 01:26:08] [Iter 1466/2362] R3[303/1200]  | LR: 0.042538 | E:  -59.979743 | E_var:     5.2255 | E_err:   0.025585
[2025-11-01 01:26:25] [Iter 1467/2362] R3[304/1200]  | LR: 0.042492 | E:  -59.963671 | E_var:     5.2389 | E_err:   0.025942
[2025-11-01 01:26:42] [Iter 1468/2362] R3[305/1200]  | LR: 0.042445 | E:  -60.026371 | E_var:     4.9497 | E_err:   0.025593
[2025-11-01 01:26:59] [Iter 1469/2362] R3[306/1200]  | LR: 0.042398 | E:  -59.983289 | E_var:     5.2911 | E_err:   0.025790
[2025-11-01 01:27:17] [Iter 1470/2362] R3[307/1200]  | LR: 0.042351 | E:  -59.957251 | E_var:     4.8579 | E_err:   0.024827
[2025-11-01 01:27:34] [Iter 1471/2362] R3[308/1200]  | LR: 0.042304 | E:  -59.985945 | E_var:     4.9550 | E_err:   0.024687
[2025-11-01 01:27:51] [Iter 1472/2362] R3[309/1200]  | LR: 0.042256 | E:  -60.005380 | E_var:     4.9492 | E_err:   0.025013
[2025-11-01 01:28:08] [Iter 1473/2362] R3[310/1200]  | LR: 0.042209 | E:  -59.998589 | E_var:     4.9324 | E_err:   0.026000
[2025-11-01 01:28:25] [Iter 1474/2362] R3[311/1200]  | LR: 0.042161 | E:  -60.018046 | E_var:     4.9290 | E_err:   0.024582
[2025-11-01 01:28:43] [Iter 1475/2362] R3[312/1200]  | LR: 0.042114 | E:  -60.022439 | E_var:     5.2087 | E_err:   0.025029
[2025-11-01 01:29:00] [Iter 1476/2362] R3[313/1200]  | LR: 0.042066 | E:  -59.967432 | E_var:     4.8664 | E_err:   0.024451
[2025-11-01 01:29:17] [Iter 1477/2362] R3[314/1200]  | LR: 0.042018 | E:  -60.004947 | E_var:     5.0675 | E_err:   0.025299
[2025-11-01 01:29:34] [Iter 1478/2362] R3[315/1200]  | LR: 0.041970 | E:  -60.018575 | E_var:     5.0943 | E_err:   0.024880
[2025-11-01 01:29:52] [Iter 1479/2362] R3[316/1200]  | LR: 0.041922 | E:  -60.000302 | E_var:     5.3158 | E_err:   0.026078
[2025-11-01 01:30:09] [Iter 1480/2362] R3[317/1200]  | LR: 0.041874 | E:  -60.029753 | E_var:     5.1783 | E_err:   0.024928
[2025-11-01 01:30:26] [Iter 1481/2362] R3[318/1200]  | LR: 0.041825 | E:  -59.992584 | E_var:     5.0425 | E_err:   0.024420
[2025-11-01 01:30:43] [Iter 1482/2362] R3[319/1200]  | LR: 0.041777 | E:  -59.977883 | E_var:     5.1538 | E_err:   0.026267
[2025-11-01 01:31:00] [Iter 1483/2362] R3[320/1200]  | LR: 0.041728 | E:  -59.963656 | E_var:     5.1167 | E_err:   0.025557
[2025-11-01 01:31:18] [Iter 1484/2362] R3[321/1200]  | LR: 0.041680 | E:  -59.962069 | E_var:     5.1590 | E_err:   0.025310
[2025-11-01 01:31:35] [Iter 1485/2362] R3[322/1200]  | LR: 0.041631 | E:  -59.881047 | E_var:     5.0851 | E_err:   0.026233
[2025-11-01 01:31:52] [Iter 1486/2362] R3[323/1200]  | LR: 0.041582 | E:  -59.932205 | E_var:     5.0267 | E_err:   0.025304
[2025-11-01 01:32:09] [Iter 1487/2362] R3[324/1200]  | LR: 0.041533 | E:  -59.989857 | E_var:     5.4155 | E_err:   0.026140
[2025-11-01 01:32:27] [Iter 1488/2362] R3[325/1200]  | LR: 0.041484 | E:  -60.018767 | E_var:     5.5801 | E_err:   0.026437
[2025-11-01 01:32:44] [Iter 1489/2362] R3[326/1200]  | LR: 0.041435 | E:  -59.998766 | E_var:     5.2860 | E_err:   0.025970
[2025-11-01 01:33:01] [Iter 1490/2362] R3[327/1200]  | LR: 0.041385 | E:  -59.999246 | E_var:     5.1963 | E_err:   0.025448
[2025-11-01 01:33:18] [Iter 1491/2362] R3[328/1200]  | LR: 0.041336 | E:  -59.990547 | E_var:     5.0324 | E_err:   0.024575
[2025-11-01 01:33:35] [Iter 1492/2362] R3[329/1200]  | LR: 0.041286 | E:  -59.950435 | E_var:     5.2554 | E_err:   0.026424
[2025-11-01 01:33:53] [Iter 1493/2362] R3[330/1200]  | LR: 0.041236 | E:  -59.972625 | E_var:     5.0798 | E_err:   0.025384
[2025-11-01 01:34:10] [Iter 1494/2362] R3[331/1200]  | LR: 0.041187 | E:  -59.981553 | E_var:     5.1868 | E_err:   0.025051
[2025-11-01 01:34:27] [Iter 1495/2362] R3[332/1200]  | LR: 0.041137 | E:  -60.015592 | E_var:     5.2548 | E_err:   0.025794
[2025-11-01 01:34:44] [Iter 1496/2362] R3[333/1200]  | LR: 0.041087 | E:  -60.026850 | E_var:     5.4386 | E_err:   0.026586
[2025-11-01 01:35:02] [Iter 1497/2362] R3[334/1200]  | LR: 0.041036 | E:  -60.041876 | E_var:     5.0962 | E_err:   0.025281
[2025-11-01 01:35:19] [Iter 1498/2362] R3[335/1200]  | LR: 0.040986 | E:  -60.017479 | E_var:     5.1777 | E_err:   0.025487
[2025-11-01 01:35:36] [Iter 1499/2362] R3[336/1200]  | LR: 0.040936 | E:  -60.026733 | E_var:     5.1879 | E_err:   0.025449
[2025-11-01 01:35:53] [Iter 1500/2362] R3[337/1200]  | LR: 0.040885 | E:  -59.994981 | E_var:     4.8563 | E_err:   0.024442
[2025-11-01 01:36:10] [Iter 1501/2362] R3[338/1200]  | LR: 0.040835 | E:  -60.017044 | E_var:     4.8818 | E_err:   0.024717
[2025-11-01 01:36:28] [Iter 1502/2362] R3[339/1200]  | LR: 0.040784 | E:  -60.020012 | E_var:     5.0723 | E_err:   0.024422
[2025-11-01 01:36:45] [Iter 1503/2362] R3[340/1200]  | LR: 0.040733 | E:  -60.011602 | E_var:     5.2333 | E_err:   0.025634
[2025-11-01 01:37:02] [Iter 1504/2362] R3[341/1200]  | LR: 0.040682 | E:  -59.980485 | E_var:     5.0750 | E_err:   0.025500
[2025-11-01 01:37:19] [Iter 1505/2362] R3[342/1200]  | LR: 0.040631 | E:  -60.016895 | E_var:     5.4574 | E_err:   0.026320
[2025-11-01 01:37:37] [Iter 1506/2362] R3[343/1200]  | LR: 0.040580 | E:  -60.031495 | E_var:     4.9852 | E_err:   0.025210
[2025-11-01 01:37:54] [Iter 1507/2362] R3[344/1200]  | LR: 0.040529 | E:  -59.995330 | E_var:     4.8525 | E_err:   0.024189
[2025-11-01 01:38:11] [Iter 1508/2362] R3[345/1200]  | LR: 0.040478 | E:  -60.047238 | E_var:     5.0120 | E_err:   0.024625
[2025-11-01 01:38:28] [Iter 1509/2362] R3[346/1200]  | LR: 0.040426 | E:  -60.010435 | E_var:     4.8383 | E_err:   0.024825
[2025-11-01 01:38:45] [Iter 1510/2362] R3[347/1200]  | LR: 0.040375 | E:  -60.006888 | E_var:     4.9679 | E_err:   0.024824
[2025-11-01 01:39:03] [Iter 1511/2362] R3[348/1200]  | LR: 0.040323 | E:  -59.968620 | E_var:     4.9382 | E_err:   0.025448
[2025-11-01 01:39:20] [Iter 1512/2362] R3[349/1200]  | LR: 0.040271 | E:  -59.971354 | E_var:     4.9019 | E_err:   0.024727
[2025-11-01 01:39:37] [Iter 1513/2362] R3[350/1200]  | LR: 0.040219 | E:  -60.009394 | E_var:     5.1481 | E_err:   0.025230
[2025-11-01 01:39:54] [Iter 1514/2362] R3[351/1200]  | LR: 0.040167 | E:  -59.984949 | E_var:     5.1910 | E_err:   0.025754
[2025-11-01 01:40:11] [Iter 1515/2362] R3[352/1200]  | LR: 0.040115 | E:  -60.053371 | E_var:     5.2598 | E_err:   0.025160
[2025-11-01 01:40:29] [Iter 1516/2362] R3[353/1200]  | LR: 0.040063 | E:  -60.011346 | E_var:     4.8678 | E_err:   0.024621
[2025-11-01 01:40:46] [Iter 1517/2362] R3[354/1200]  | LR: 0.040011 | E:  -59.990333 | E_var:     5.1909 | E_err:   0.025627
[2025-11-01 01:41:03] [Iter 1518/2362] R3[355/1200]  | LR: 0.039958 | E:  -60.005491 | E_var:     5.0364 | E_err:   0.026120
[2025-11-01 01:41:20] [Iter 1519/2362] R3[356/1200]  | LR: 0.039906 | E:  -59.983192 | E_var:     5.0316 | E_err:   0.025423
[2025-11-01 01:41:38] [Iter 1520/2362] R3[357/1200]  | LR: 0.039853 | E:  -59.997705 | E_var:     5.3139 | E_err:   0.025746
[2025-11-01 01:41:55] [Iter 1521/2362] R3[358/1200]  | LR: 0.039801 | E:  -60.017035 | E_var:     5.1461 | E_err:   0.025389
[2025-11-01 01:42:12] [Iter 1522/2362] R3[359/1200]  | LR: 0.039748 | E:  -60.001513 | E_var:     5.1060 | E_err:   0.025360
[2025-11-01 01:42:29] [Iter 1523/2362] R3[360/1200]  | LR: 0.039695 | E:  -59.971553 | E_var:     5.1682 | E_err:   0.025328
[2025-11-01 01:42:46] [Iter 1524/2362] R3[361/1200]  | LR: 0.039642 | E:  -59.961946 | E_var:     5.4410 | E_err:   0.025840
[2025-11-01 01:43:04] [Iter 1525/2362] R3[362/1200]  | LR: 0.039589 | E:  -59.984751 | E_var:     5.5420 | E_err:   0.026667
[2025-11-01 01:43:21] [Iter 1526/2362] R3[363/1200]  | LR: 0.039536 | E:  -60.030758 | E_var:     5.1777 | E_err:   0.026154
[2025-11-01 01:43:38] [Iter 1527/2362] R3[364/1200]  | LR: 0.039482 | E:  -59.977640 | E_var:     5.1735 | E_err:   0.025829
[2025-11-01 01:43:55] [Iter 1528/2362] R3[365/1200]  | LR: 0.039429 | E:  -60.008137 | E_var:     5.2146 | E_err:   0.025935
[2025-11-01 01:44:12] [Iter 1529/2362] R3[366/1200]  | LR: 0.039375 | E:  -60.061302 | E_var:     5.1840 | E_err:   0.025782
[2025-11-01 01:44:30] [Iter 1530/2362] R3[367/1200]  | LR: 0.039322 | E:  -60.030654 | E_var:     5.5419 | E_err:   0.026984
[2025-11-01 01:44:47] [Iter 1531/2362] R3[368/1200]  | LR: 0.039268 | E:  -60.002449 | E_var:     5.0281 | E_err:   0.024787
[2025-11-01 01:45:04] [Iter 1532/2362] R3[369/1200]  | LR: 0.039214 | E:  -60.003180 | E_var:     5.0414 | E_err:   0.024775
[2025-11-01 01:45:21] [Iter 1533/2362] R3[370/1200]  | LR: 0.039160 | E:  -60.013342 | E_var:     5.3153 | E_err:   0.025440
[2025-11-01 01:45:38] [Iter 1534/2362] R3[371/1200]  | LR: 0.039106 | E:  -60.002972 | E_var:     5.2952 | E_err:   0.025681
[2025-11-01 01:45:56] [Iter 1535/2362] R3[372/1200]  | LR: 0.039052 | E:  -60.045453 | E_var:     5.3524 | E_err:   0.025726
[2025-11-01 01:46:13] [Iter 1536/2362] R3[373/1200]  | LR: 0.038998 | E:  -60.034601 | E_var:     5.3429 | E_err:   0.025539
[2025-11-01 01:46:30] [Iter 1537/2362] R3[374/1200]  | LR: 0.038944 | E:  -60.031040 | E_var:     5.0506 | E_err:   0.025037
[2025-11-01 01:46:47] [Iter 1538/2362] R3[375/1200]  | LR: 0.038889 | E:  -60.020575 | E_var:     5.3656 | E_err:   0.025861
[2025-11-01 01:47:05] [Iter 1539/2362] R3[376/1200]  | LR: 0.038835 | E:  -60.007880 | E_var:     5.2615 | E_err:   0.026651
[2025-11-01 01:47:22] [Iter 1540/2362] R3[377/1200]  | LR: 0.038780 | E:  -59.997935 | E_var:     5.4024 | E_err:   0.026466
[2025-11-01 01:47:39] [Iter 1541/2362] R3[378/1200]  | LR: 0.038726 | E:  -60.006294 | E_var:     5.0827 | E_err:   0.025344
[2025-11-01 01:47:56] [Iter 1542/2362] R3[379/1200]  | LR: 0.038671 | E:  -59.989824 | E_var:     4.9814 | E_err:   0.024786
[2025-11-01 01:48:13] [Iter 1543/2362] R3[380/1200]  | LR: 0.038616 | E:  -60.039406 | E_var:     5.1347 | E_err:   0.025271
[2025-11-01 01:48:31] [Iter 1544/2362] R3[381/1200]  | LR: 0.038561 | E:  -60.053324 | E_var:     4.8847 | E_err:   0.024445
[2025-11-01 01:48:48] [Iter 1545/2362] R3[382/1200]  | LR: 0.038506 | E:  -60.003702 | E_var:     4.9758 | E_err:   0.024586
[2025-11-01 01:49:05] [Iter 1546/2362] R3[383/1200]  | LR: 0.038451 | E:  -60.008052 | E_var:     4.9546 | E_err:   0.025694
[2025-11-01 01:49:22] [Iter 1547/2362] R3[384/1200]  | LR: 0.038396 | E:  -60.001696 | E_var:     5.2396 | E_err:   0.025669
[2025-11-01 01:49:39] [Iter 1548/2362] R3[385/1200]  | LR: 0.038341 | E:  -60.016817 | E_var:     4.9562 | E_err:   0.025372
[2025-11-01 01:49:57] [Iter 1549/2362] R3[386/1200]  | LR: 0.038285 | E:  -60.051748 | E_var:     5.0381 | E_err:   0.025163
[2025-11-01 01:50:14] [Iter 1550/2362] R3[387/1200]  | LR: 0.038230 | E:  -59.974972 | E_var:     5.2581 | E_err:   0.025826
[2025-11-01 01:50:31] [Iter 1551/2362] R3[388/1200]  | LR: 0.038174 | E:  -60.013138 | E_var:     5.2224 | E_err:   0.025944
[2025-11-01 01:50:48] [Iter 1552/2362] R3[389/1200]  | LR: 0.038118 | E:  -60.017457 | E_var:     5.1461 | E_err:   0.025349
[2025-11-01 01:51:06] [Iter 1553/2362] R3[390/1200]  | LR: 0.038063 | E:  -59.980458 | E_var:     5.0888 | E_err:   0.025412
[2025-11-01 01:51:23] [Iter 1554/2362] R3[391/1200]  | LR: 0.038007 | E:  -59.970244 | E_var:     4.8622 | E_err:   0.025769
[2025-11-01 01:51:40] [Iter 1555/2362] R3[392/1200]  | LR: 0.037951 | E:  -59.984350 | E_var:     5.1671 | E_err:   0.026301
[2025-11-01 01:51:57] [Iter 1556/2362] R3[393/1200]  | LR: 0.037895 | E:  -59.944001 | E_var:     5.3298 | E_err:   0.025858
[2025-11-01 01:52:14] [Iter 1557/2362] R3[394/1200]  | LR: 0.037839 | E:  -60.001359 | E_var:     5.0339 | E_err:   0.025655
[2025-11-01 01:52:32] [Iter 1558/2362] R3[395/1200]  | LR: 0.037783 | E:  -59.991819 | E_var:     5.1810 | E_err:   0.026018
[2025-11-01 01:52:49] [Iter 1559/2362] R3[396/1200]  | LR: 0.037726 | E:  -60.044528 | E_var:     4.9459 | E_err:   0.024605
[2025-11-01 01:53:06] [Iter 1560/2362] R3[397/1200]  | LR: 0.037670 | E:  -59.993071 | E_var:     5.0285 | E_err:   0.025055
[2025-11-01 01:53:23] [Iter 1561/2362] R3[398/1200]  | LR: 0.037613 | E:  -60.032104 | E_var:     5.2740 | E_err:   0.026387
[2025-11-01 01:53:41] [Iter 1562/2362] R3[399/1200]  | LR: 0.037557 | E:  -59.970639 | E_var:     5.0060 | E_err:   0.024830
[2025-11-01 01:53:58] [Iter 1563/2362] R3[400/1200]  | LR: 0.037500 | E:  -59.980221 | E_var:     4.9825 | E_err:   0.025458
[2025-11-01 01:54:15] [Iter 1564/2362] R3[401/1200]  | LR: 0.037444 | E:  -60.017052 | E_var:     5.0493 | E_err:   0.025573
[2025-11-01 01:54:32] [Iter 1565/2362] R3[402/1200]  | LR: 0.037387 | E:  -60.005096 | E_var:     5.3335 | E_err:   0.025846
[2025-11-01 01:54:49] [Iter 1566/2362] R3[403/1200]  | LR: 0.037330 | E:  -59.979262 | E_var:     5.5356 | E_err:   0.027278
[2025-11-01 01:55:07] [Iter 1567/2362] R3[404/1200]  | LR: 0.037273 | E:  -59.980537 | E_var:     5.1636 | E_err:   0.025008
[2025-11-01 01:55:24] [Iter 1568/2362] R3[405/1200]  | LR: 0.037216 | E:  -59.980232 | E_var:     5.1638 | E_err:   0.026104
[2025-11-01 01:55:41] [Iter 1569/2362] R3[406/1200]  | LR: 0.037159 | E:  -60.037634 | E_var:     5.0034 | E_err:   0.025617
[2025-11-01 01:55:58] [Iter 1570/2362] R3[407/1200]  | LR: 0.037101 | E:  -59.995509 | E_var:     5.0087 | E_err:   0.024548
[2025-11-01 01:56:15] [Iter 1571/2362] R3[408/1200]  | LR: 0.037044 | E:  -60.008010 | E_var:     5.2481 | E_err:   0.025426
[2025-11-01 01:56:33] [Iter 1572/2362] R3[409/1200]  | LR: 0.036987 | E:  -60.021530 | E_var:     5.0158 | E_err:   0.025411
[2025-11-01 01:56:50] [Iter 1573/2362] R3[410/1200]  | LR: 0.036929 | E:  -59.985808 | E_var:     4.8448 | E_err:   0.024194
[2025-11-01 01:57:07] [Iter 1574/2362] R3[411/1200]  | LR: 0.036872 | E:  -60.005478 | E_var:     4.8310 | E_err:   0.024401
[2025-11-01 01:57:24] [Iter 1575/2362] R3[412/1200]  | LR: 0.036814 | E:  -60.004162 | E_var:     5.1456 | E_err:   0.026002
[2025-11-01 01:57:42] [Iter 1576/2362] R3[413/1200]  | LR: 0.036756 | E:  -60.015074 | E_var:     5.1809 | E_err:   0.026300
[2025-11-01 01:57:59] [Iter 1577/2362] R3[414/1200]  | LR: 0.036699 | E:  -60.037242 | E_var:     5.0653 | E_err:   0.025491
[2025-11-01 01:58:16] [Iter 1578/2362] R3[415/1200]  | LR: 0.036641 | E:  -60.010975 | E_var:     5.0860 | E_err:   0.024951
[2025-11-01 01:58:33] [Iter 1579/2362] R3[416/1200]  | LR: 0.036583 | E:  -60.073201 | E_var:     5.0114 | E_err:   0.024141
[2025-11-01 01:58:50] [Iter 1580/2362] R3[417/1200]  | LR: 0.036525 | E:  -60.037521 | E_var:     4.8613 | E_err:   0.023916
[2025-11-01 01:59:08] [Iter 1581/2362] R3[418/1200]  | LR: 0.036467 | E:  -60.024523 | E_var:     5.2641 | E_err:   0.025664
[2025-11-01 01:59:25] [Iter 1582/2362] R3[419/1200]  | LR: 0.036408 | E:  -60.026611 | E_var:     5.1231 | E_err:   0.025643
[2025-11-01 01:59:42] [Iter 1583/2362] R3[420/1200]  | LR: 0.036350 | E:  -59.959688 | E_var:     4.9548 | E_err:   0.025178
[2025-11-01 01:59:59] [Iter 1584/2362] R3[421/1200]  | LR: 0.036292 | E:  -60.042047 | E_var:     5.1298 | E_err:   0.026024
[2025-11-01 02:00:17] [Iter 1585/2362] R3[422/1200]  | LR: 0.036233 | E:  -59.996821 | E_var:     4.8550 | E_err:   0.024502
[2025-11-01 02:00:34] [Iter 1586/2362] R3[423/1200]  | LR: 0.036175 | E:  -59.986719 | E_var:     5.0442 | E_err:   0.024934
[2025-11-01 02:00:51] [Iter 1587/2362] R3[424/1200]  | LR: 0.036116 | E:  -60.016966 | E_var:     5.1984 | E_err:   0.026037
[2025-11-01 02:01:08] [Iter 1588/2362] R3[425/1200]  | LR: 0.036057 | E:  -59.983571 | E_var:     5.1765 | E_err:   0.024847
[2025-11-01 02:01:25] [Iter 1589/2362] R3[426/1200]  | LR: 0.035999 | E:  -59.982438 | E_var:     5.1353 | E_err:   0.025999
[2025-11-01 02:01:43] [Iter 1590/2362] R3[427/1200]  | LR: 0.035940 | E:  -59.979590 | E_var:     5.1867 | E_err:   0.025144
[2025-11-01 02:02:00] [Iter 1591/2362] R3[428/1200]  | LR: 0.035881 | E:  -59.970712 | E_var:     5.1005 | E_err:   0.025627
[2025-11-01 02:02:17] [Iter 1592/2362] R3[429/1200]  | LR: 0.035822 | E:  -59.979458 | E_var:     5.1103 | E_err:   0.025492
[2025-11-01 02:02:34] [Iter 1593/2362] R3[430/1200]  | LR: 0.035763 | E:  -59.952866 | E_var:     5.0509 | E_err:   0.025583
[2025-11-01 02:02:52] [Iter 1594/2362] R3[431/1200]  | LR: 0.035704 | E:  -60.000908 | E_var:     5.1695 | E_err:   0.025821
[2025-11-01 02:03:09] [Iter 1595/2362] R3[432/1200]  | LR: 0.035645 | E:  -60.024777 | E_var:     5.1603 | E_err:   0.025633
[2025-11-01 02:03:26] [Iter 1596/2362] R3[433/1200]  | LR: 0.035586 | E:  -60.050629 | E_var:     5.1664 | E_err:   0.026177
[2025-11-01 02:03:43] [Iter 1597/2362] R3[434/1200]  | LR: 0.035526 | E:  -60.029453 | E_var:     5.1062 | E_err:   0.025726
[2025-11-01 02:04:00] [Iter 1598/2362] R3[435/1200]  | LR: 0.035467 | E:  -60.029736 | E_var:     4.9629 | E_err:   0.025130
[2025-11-01 02:04:18] [Iter 1599/2362] R3[436/1200]  | LR: 0.035407 | E:  -60.046206 | E_var:     4.8928 | E_err:   0.025075
[2025-11-01 02:04:35] [Iter 1600/2362] R3[437/1200]  | LR: 0.035348 | E:  -59.970448 | E_var:     4.9764 | E_err:   0.026043
[2025-11-01 02:04:52] [Iter 1601/2362] R3[438/1200]  | LR: 0.035288 | E:  -59.976105 | E_var:     4.9192 | E_err:   0.025570
[2025-11-01 02:05:09] [Iter 1602/2362] R3[439/1200]  | LR: 0.035228 | E:  -59.988530 | E_var:     4.8025 | E_err:   0.024164
[2025-11-01 02:05:27] [Iter 1603/2362] R3[440/1200]  | LR: 0.035169 | E:  -60.009117 | E_var:     4.8551 | E_err:   0.023945
[2025-11-01 02:05:44] [Iter 1604/2362] R3[441/1200]  | LR: 0.035109 | E:  -60.044474 | E_var:     5.2770 | E_err:   0.026244
[2025-11-01 02:06:01] [Iter 1605/2362] R3[442/1200]  | LR: 0.035049 | E:  -60.025206 | E_var:     5.0285 | E_err:   0.025632
[2025-11-01 02:06:18] [Iter 1606/2362] R3[443/1200]  | LR: 0.034989 | E:  -59.989265 | E_var:     5.0542 | E_err:   0.026074
[2025-11-01 02:06:35] [Iter 1607/2362] R3[444/1200]  | LR: 0.034929 | E:  -59.943612 | E_var:     4.9586 | E_err:   0.025277
[2025-11-01 02:06:53] [Iter 1608/2362] R3[445/1200]  | LR: 0.034869 | E:  -59.927323 | E_var:     4.9610 | E_err:   0.024955
[2025-11-01 02:07:10] [Iter 1609/2362] R3[446/1200]  | LR: 0.034809 | E:  -59.956234 | E_var:     4.9276 | E_err:   0.024924
[2025-11-01 02:07:27] [Iter 1610/2362] R3[447/1200]  | LR: 0.034748 | E:  -59.994242 | E_var:     4.8769 | E_err:   0.025582
[2025-11-01 02:07:44] [Iter 1611/2362] R3[448/1200]  | LR: 0.034688 | E:  -59.947585 | E_var:     4.9127 | E_err:   0.024346
[2025-11-01 02:08:01] [Iter 1612/2362] R3[449/1200]  | LR: 0.034628 | E:  -60.055188 | E_var:     5.0167 | E_err:   0.025345
[2025-11-01 02:08:02] ✓ Checkpoint saved: checkpoint_iter_001500.pkl
[2025-11-01 02:08:19] [Iter 1613/2362] R3[450/1200]  | LR: 0.034567 | E:  -60.073638 | E_var:     5.0253 | E_err:   0.025117
[2025-11-01 02:08:36] [Iter 1614/2362] R3[451/1200]  | LR: 0.034507 | E:  -60.049459 | E_var:     5.0609 | E_err:   0.025766
[2025-11-01 02:08:53] [Iter 1615/2362] R3[452/1200]  | LR: 0.034446 | E:  -60.005247 | E_var:     5.3025 | E_err:   0.025424
[2025-11-01 02:09:10] [Iter 1616/2362] R3[453/1200]  | LR: 0.034386 | E:  -60.009230 | E_var:     5.2044 | E_err:   0.025052
[2025-11-01 02:09:28] [Iter 1617/2362] R3[454/1200]  | LR: 0.034325 | E:  -59.986141 | E_var:     5.4321 | E_err:   0.026086
[2025-11-01 02:09:45] [Iter 1618/2362] R3[455/1200]  | LR: 0.034264 | E:  -59.959452 | E_var:     5.5849 | E_err:   0.027309
[2025-11-01 02:10:02] [Iter 1619/2362] R3[456/1200]  | LR: 0.034203 | E:  -60.018004 | E_var:     5.1471 | E_err:   0.025228
[2025-11-01 02:10:19] [Iter 1620/2362] R3[457/1200]  | LR: 0.034143 | E:  -59.990107 | E_var:     5.0185 | E_err:   0.025098
[2025-11-01 02:10:36] [Iter 1621/2362] R3[458/1200]  | LR: 0.034082 | E:  -59.985733 | E_var:     5.1342 | E_err:   0.025244
[2025-11-01 02:10:54] [Iter 1622/2362] R3[459/1200]  | LR: 0.034021 | E:  -59.960483 | E_var:     5.0843 | E_err:   0.025621
[2025-11-01 02:11:11] [Iter 1623/2362] R3[460/1200]  | LR: 0.033960 | E:  -60.038179 | E_var:     5.2162 | E_err:   0.025206
[2025-11-01 02:11:28] [Iter 1624/2362] R3[461/1200]  | LR: 0.033898 | E:  -60.010678 | E_var:     4.9341 | E_err:   0.025437
[2025-11-01 02:11:45] [Iter 1625/2362] R3[462/1200]  | LR: 0.033837 | E:  -60.010005 | E_var:     5.1025 | E_err:   0.025738
[2025-11-01 02:12:03] [Iter 1626/2362] R3[463/1200]  | LR: 0.033776 | E:  -59.984712 | E_var:     4.9142 | E_err:   0.024420
[2025-11-01 02:12:20] [Iter 1627/2362] R3[464/1200]  | LR: 0.033715 | E:  -60.021671 | E_var:     5.2387 | E_err:   0.025864
[2025-11-01 02:12:37] [Iter 1628/2362] R3[465/1200]  | LR: 0.033653 | E:  -60.054330 | E_var:     5.2435 | E_err:   0.026277
[2025-11-01 02:12:54] [Iter 1629/2362] R3[466/1200]  | LR: 0.033592 | E:  -60.033470 | E_var:     4.9293 | E_err:   0.025346
[2025-11-01 02:13:11] [Iter 1630/2362] R3[467/1200]  | LR: 0.033530 | E:  -60.075013 | E_var:     5.0490 | E_err:   0.025048
[2025-11-01 02:13:29] [Iter 1631/2362] R3[468/1200]  | LR: 0.033469 | E:  -60.009264 | E_var:     5.1866 | E_err:   0.025043
[2025-11-01 02:13:46] [Iter 1632/2362] R3[469/1200]  | LR: 0.033407 | E:  -60.037163 | E_var:     5.0599 | E_err:   0.024643
[2025-11-01 02:14:03] [Iter 1633/2362] R3[470/1200]  | LR: 0.033346 | E:  -60.030382 | E_var:     4.8703 | E_err:   0.023887
[2025-11-01 02:14:20] [Iter 1634/2362] R3[471/1200]  | LR: 0.033284 | E:  -60.040794 | E_var:     4.8920 | E_err:   0.025116
[2025-11-01 02:14:38] [Iter 1635/2362] R3[472/1200]  | LR: 0.033222 | E:  -60.061510 | E_var:     4.9704 | E_err:   0.025424
[2025-11-01 02:14:55] [Iter 1636/2362] R3[473/1200]  | LR: 0.033160 | E:  -60.041852 | E_var:     4.9129 | E_err:   0.025202
[2025-11-01 02:15:12] [Iter 1637/2362] R3[474/1200]  | LR: 0.033098 | E:  -60.037933 | E_var:     4.8942 | E_err:   0.025409
[2025-11-01 02:15:29] [Iter 1638/2362] R3[475/1200]  | LR: 0.033036 | E:  -60.042448 | E_var:     4.9457 | E_err:   0.025074
[2025-11-01 02:15:46] [Iter 1639/2362] R3[476/1200]  | LR: 0.032974 | E:  -60.028617 | E_var:     4.9039 | E_err:   0.024697
[2025-11-01 02:16:04] [Iter 1640/2362] R3[477/1200]  | LR: 0.032912 | E:  -60.039967 | E_var:     4.8320 | E_err:   0.025567
[2025-11-01 02:16:21] [Iter 1641/2362] R3[478/1200]  | LR: 0.032850 | E:  -60.006069 | E_var:     4.7927 | E_err:   0.024983
[2025-11-01 02:16:38] [Iter 1642/2362] R3[479/1200]  | LR: 0.032788 | E:  -60.029644 | E_var:     4.9245 | E_err:   0.024565
[2025-11-01 02:16:55] [Iter 1643/2362] R3[480/1200]  | LR: 0.032726 | E:  -60.079702 | E_var:     4.8240 | E_err:   0.025227
[2025-11-01 02:17:12] [Iter 1644/2362] R3[481/1200]  | LR: 0.032663 | E:  -60.072838 | E_var:     4.9045 | E_err:   0.025748
[2025-11-01 02:17:30] [Iter 1645/2362] R3[482/1200]  | LR: 0.032601 | E:  -60.054036 | E_var:     5.0860 | E_err:   0.025139
[2025-11-01 02:17:47] [Iter 1646/2362] R3[483/1200]  | LR: 0.032539 | E:  -60.034861 | E_var:     5.2415 | E_err:   0.026235
[2025-11-01 02:18:04] [Iter 1647/2362] R3[484/1200]  | LR: 0.032476 | E:  -60.027724 | E_var:     5.4730 | E_err:   0.026149
[2025-11-01 02:18:21] [Iter 1648/2362] R3[485/1200]  | LR: 0.032414 | E:  -60.076395 | E_var:     5.1727 | E_err:   0.025769
[2025-11-01 02:18:39] [Iter 1649/2362] R3[486/1200]  | LR: 0.032351 | E:  -60.034867 | E_var:     5.2707 | E_err:   0.025724
[2025-11-01 02:18:56] [Iter 1650/2362] R3[487/1200]  | LR: 0.032289 | E:  -60.092197 | E_var:     5.0839 | E_err:   0.026087
[2025-11-01 02:19:13] [Iter 1651/2362] R3[488/1200]  | LR: 0.032226 | E:  -60.020147 | E_var:     5.0823 | E_err:   0.025249
[2025-11-01 02:19:30] [Iter 1652/2362] R3[489/1200]  | LR: 0.032163 | E:  -59.986069 | E_var:     5.2269 | E_err:   0.026426
[2025-11-01 02:19:47] [Iter 1653/2362] R3[490/1200]  | LR: 0.032101 | E:  -59.973351 | E_var:     5.0791 | E_err:   0.025271
[2025-11-01 02:20:05] [Iter 1654/2362] R3[491/1200]  | LR: 0.032038 | E:  -60.009084 | E_var:     4.9477 | E_err:   0.024912
[2025-11-01 02:20:22] [Iter 1655/2362] R3[492/1200]  | LR: 0.031975 | E:  -59.976741 | E_var:     4.9172 | E_err:   0.025267
[2025-11-01 02:20:39] [Iter 1656/2362] R3[493/1200]  | LR: 0.031912 | E:  -60.054401 | E_var:     4.8448 | E_err:   0.024348
[2025-11-01 02:20:56] [Iter 1657/2362] R3[494/1200]  | LR: 0.031849 | E:  -59.969435 | E_var:     4.7632 | E_err:   0.024474
[2025-11-01 02:21:14] [Iter 1658/2362] R3[495/1200]  | LR: 0.031786 | E:  -60.030639 | E_var:     4.8977 | E_err:   0.025116
[2025-11-01 02:21:31] [Iter 1659/2362] R3[496/1200]  | LR: 0.031723 | E:  -60.042788 | E_var:     4.9678 | E_err:   0.025068
[2025-11-01 02:21:48] [Iter 1660/2362] R3[497/1200]  | LR: 0.031660 | E:  -60.004429 | E_var:     4.9111 | E_err:   0.024488
[2025-11-01 02:22:05] [Iter 1661/2362] R3[498/1200]  | LR: 0.031597 | E:  -60.059721 | E_var:     4.6734 | E_err:   0.024875
[2025-11-01 02:22:22] [Iter 1662/2362] R3[499/1200]  | LR: 0.031534 | E:  -60.067931 | E_var:     4.8922 | E_err:   0.024976
[2025-11-01 02:22:40] [Iter 1663/2362] R3[500/1200]  | LR: 0.031471 | E:  -60.102413 | E_var:     5.1083 | E_err:   0.025250
[2025-11-01 02:22:57] [Iter 1664/2362] R3[501/1200]  | LR: 0.031408 | E:  -60.082047 | E_var:     5.0628 | E_err:   0.025492
[2025-11-01 02:23:14] [Iter 1665/2362] R3[502/1200]  | LR: 0.031344 | E:  -60.017259 | E_var:     4.9545 | E_err:   0.025241
[2025-11-01 02:23:31] [Iter 1666/2362] R3[503/1200]  | LR: 0.031281 | E:  -60.008686 | E_var:     5.0469 | E_err:   0.025529
[2025-11-01 02:23:48] [Iter 1667/2362] R3[504/1200]  | LR: 0.031218 | E:  -59.992676 | E_var:     5.3961 | E_err:   0.025844
[2025-11-01 02:24:06] [Iter 1668/2362] R3[505/1200]  | LR: 0.031154 | E:  -59.995880 | E_var:     5.2864 | E_err:   0.025725
[2025-11-01 02:24:23] [Iter 1669/2362] R3[506/1200]  | LR: 0.031091 | E:  -60.033664 | E_var:     5.1350 | E_err:   0.025708
[2025-11-01 02:24:40] [Iter 1670/2362] R3[507/1200]  | LR: 0.031027 | E:  -60.033415 | E_var:     5.0955 | E_err:   0.025008
[2025-11-01 02:24:57] [Iter 1671/2362] R3[508/1200]  | LR: 0.030964 | E:  -60.008238 | E_var:     5.0426 | E_err:   0.025394
[2025-11-01 02:25:15] [Iter 1672/2362] R3[509/1200]  | LR: 0.030900 | E:  -59.998285 | E_var:     5.1845 | E_err:   0.024887
[2025-11-01 02:25:32] [Iter 1673/2362] R3[510/1200]  | LR: 0.030837 | E:  -59.953683 | E_var:     5.0456 | E_err:   0.025956
[2025-11-01 02:25:49] [Iter 1674/2362] R3[511/1200]  | LR: 0.030773 | E:  -60.034137 | E_var:     5.2106 | E_err:   0.025682
[2025-11-01 02:26:06] [Iter 1675/2362] R3[512/1200]  | LR: 0.030709 | E:  -60.011110 | E_var:     5.2928 | E_err:   0.026452
[2025-11-01 02:26:23] [Iter 1676/2362] R3[513/1200]  | LR: 0.030645 | E:  -60.023842 | E_var:     5.1748 | E_err:   0.025323
[2025-11-01 02:26:41] [Iter 1677/2362] R3[514/1200]  | LR: 0.030582 | E:  -60.064381 | E_var:     5.1165 | E_err:   0.025683
[2025-11-01 02:26:58] [Iter 1678/2362] R3[515/1200]  | LR: 0.030518 | E:  -60.034700 | E_var:     5.2255 | E_err:   0.025327
[2025-11-01 02:27:15] [Iter 1679/2362] R3[516/1200]  | LR: 0.030454 | E:  -60.055715 | E_var:     5.1598 | E_err:   0.025476
[2025-11-01 02:27:32] [Iter 1680/2362] R3[517/1200]  | LR: 0.030390 | E:  -60.076711 | E_var:     5.1989 | E_err:   0.026086
[2025-11-01 02:27:49] [Iter 1681/2362] R3[518/1200]  | LR: 0.030326 | E:  -60.053881 | E_var:     5.2284 | E_err:   0.025490
[2025-11-01 02:28:07] [Iter 1682/2362] R3[519/1200]  | LR: 0.030262 | E:  -60.062990 | E_var:     5.3924 | E_err:   0.025629
[2025-11-01 02:28:24] [Iter 1683/2362] R3[520/1200]  | LR: 0.030198 | E:  -60.042273 | E_var:     5.0377 | E_err:   0.025379
[2025-11-01 02:28:41] [Iter 1684/2362] R3[521/1200]  | LR: 0.030134 | E:  -60.044225 | E_var:     5.1243 | E_err:   0.026337
[2025-11-01 02:28:58] [Iter 1685/2362] R3[522/1200]  | LR: 0.030070 | E:  -60.043635 | E_var:     5.2609 | E_err:   0.026456
[2025-11-01 02:29:16] [Iter 1686/2362] R3[523/1200]  | LR: 0.030006 | E:  -60.043888 | E_var:     4.8903 | E_err:   0.023673
[2025-11-01 02:29:33] [Iter 1687/2362] R3[524/1200]  | LR: 0.029942 | E:  -60.049457 | E_var:     4.8101 | E_err:   0.024090
[2025-11-01 02:29:50] [Iter 1688/2362] R3[525/1200]  | LR: 0.029878 | E:  -60.058869 | E_var:     4.7891 | E_err:   0.023856
[2025-11-01 02:30:07] [Iter 1689/2362] R3[526/1200]  | LR: 0.029813 | E:  -60.066885 | E_var:     5.2837 | E_err:   0.025509
[2025-11-01 02:30:24] [Iter 1690/2362] R3[527/1200]  | LR: 0.029749 | E:  -60.059220 | E_var:     4.8780 | E_err:   0.024616
[2025-11-01 02:30:42] [Iter 1691/2362] R3[528/1200]  | LR: 0.029685 | E:  -60.041794 | E_var:     4.9387 | E_err:   0.024093
[2025-11-01 02:30:59] [Iter 1692/2362] R3[529/1200]  | LR: 0.029621 | E:  -60.040372 | E_var:     4.9797 | E_err:   0.025013
[2025-11-01 02:31:16] [Iter 1693/2362] R3[530/1200]  | LR: 0.029556 | E:  -60.043006 | E_var:     4.9232 | E_err:   0.025111
[2025-11-01 02:31:33] [Iter 1694/2362] R3[531/1200]  | LR: 0.029492 | E:  -60.073655 | E_var:     4.8416 | E_err:   0.025181
[2025-11-01 02:31:50] [Iter 1695/2362] R3[532/1200]  | LR: 0.029428 | E:  -60.037447 | E_var:     4.8077 | E_err:   0.024303
[2025-11-01 02:32:08] [Iter 1696/2362] R3[533/1200]  | LR: 0.029363 | E:  -60.017396 | E_var:     4.7821 | E_err:   0.025047
[2025-11-01 02:32:25] [Iter 1697/2362] R3[534/1200]  | LR: 0.029299 | E:  -60.078532 | E_var:     4.9476 | E_err:   0.024809
[2025-11-01 02:32:42] [Iter 1698/2362] R3[535/1200]  | LR: 0.029234 | E:  -60.038506 | E_var:     4.8787 | E_err:   0.025210
[2025-11-01 02:32:59] [Iter 1699/2362] R3[536/1200]  | LR: 0.029170 | E:  -60.057409 | E_var:     5.0301 | E_err:   0.024857
[2025-11-01 02:33:17] [Iter 1700/2362] R3[537/1200]  | LR: 0.029105 | E:  -60.031657 | E_var:     4.9279 | E_err:   0.025792
[2025-11-01 02:33:34] [Iter 1701/2362] R3[538/1200]  | LR: 0.029041 | E:  -60.058617 | E_var:     4.7959 | E_err:   0.024101
[2025-11-01 02:33:51] [Iter 1702/2362] R3[539/1200]  | LR: 0.028976 | E:  -59.994280 | E_var:     4.8459 | E_err:   0.024220
[2025-11-01 02:34:08] [Iter 1703/2362] R3[540/1200]  | LR: 0.028911 | E:  -60.083430 | E_var:     4.9174 | E_err:   0.025460
[2025-11-01 02:34:25] [Iter 1704/2362] R3[541/1200]  | LR: 0.028847 | E:  -60.063662 | E_var:     4.8860 | E_err:   0.024797
[2025-11-01 02:34:43] [Iter 1705/2362] R3[542/1200]  | LR: 0.028782 | E:  -60.064792 | E_var:     4.9019 | E_err:   0.024214
[2025-11-01 02:35:00] [Iter 1706/2362] R3[543/1200]  | LR: 0.028717 | E:  -60.097163 | E_var:     4.9622 | E_err:   0.024792
[2025-11-01 02:35:17] [Iter 1707/2362] R3[544/1200]  | LR: 0.028653 | E:  -60.058675 | E_var:     4.9063 | E_err:   0.024680
[2025-11-01 02:35:34] [Iter 1708/2362] R3[545/1200]  | LR: 0.028588 | E:  -59.997330 | E_var:     4.5641 | E_err:   0.023945
[2025-11-01 02:35:51] [Iter 1709/2362] R3[546/1200]  | LR: 0.028523 | E:  -60.021275 | E_var:     4.7163 | E_err:   0.024225
[2025-11-01 02:36:09] [Iter 1710/2362] R3[547/1200]  | LR: 0.028458 | E:  -60.043691 | E_var:     4.9610 | E_err:   0.025300
[2025-11-01 02:36:26] [Iter 1711/2362] R3[548/1200]  | LR: 0.028393 | E:  -60.061094 | E_var:     4.9629 | E_err:   0.024620
[2025-11-01 02:36:43] [Iter 1712/2362] R3[549/1200]  | LR: 0.028328 | E:  -60.031915 | E_var:     4.7394 | E_err:   0.023966
[2025-11-01 02:37:00] [Iter 1713/2362] R3[550/1200]  | LR: 0.028264 | E:  -60.023945 | E_var:     4.9264 | E_err:   0.025258
[2025-11-01 02:37:18] [Iter 1714/2362] R3[551/1200]  | LR: 0.028199 | E:  -60.010099 | E_var:     4.8644 | E_err:   0.024019
[2025-11-01 02:37:35] [Iter 1715/2362] R3[552/1200]  | LR: 0.028134 | E:  -60.079243 | E_var:     5.1035 | E_err:   0.025180
[2025-11-01 02:37:52] [Iter 1716/2362] R3[553/1200]  | LR: 0.028069 | E:  -60.039053 | E_var:     4.9150 | E_err:   0.025493
[2025-11-01 02:38:09] [Iter 1717/2362] R3[554/1200]  | LR: 0.028004 | E:  -60.034562 | E_var:     4.7856 | E_err:   0.024184
[2025-11-01 02:38:26] [Iter 1718/2362] R3[555/1200]  | LR: 0.027939 | E:  -60.054535 | E_var:     5.0056 | E_err:   0.025297
[2025-11-01 02:38:44] [Iter 1719/2362] R3[556/1200]  | LR: 0.027874 | E:  -60.098505 | E_var:     4.9589 | E_err:   0.024420
[2025-11-01 02:39:01] [Iter 1720/2362] R3[557/1200]  | LR: 0.027809 | E:  -60.054658 | E_var:     5.1452 | E_err:   0.025734
[2025-11-01 02:39:18] [Iter 1721/2362] R3[558/1200]  | LR: 0.027744 | E:  -60.012081 | E_var:     5.1838 | E_err:   0.025922
[2025-11-01 02:39:35] [Iter 1722/2362] R3[559/1200]  | LR: 0.027679 | E:  -60.065004 | E_var:     5.3523 | E_err:   0.025438
[2025-11-01 02:39:52] [Iter 1723/2362] R3[560/1200]  | LR: 0.027614 | E:  -60.036883 | E_var:     5.2737 | E_err:   0.026349
[2025-11-01 02:40:10] [Iter 1724/2362] R3[561/1200]  | LR: 0.027549 | E:  -60.025304 | E_var:     5.0329 | E_err:   0.025516
[2025-11-01 02:40:27] [Iter 1725/2362] R3[562/1200]  | LR: 0.027483 | E:  -60.031251 | E_var:     4.9017 | E_err:   0.024473
[2025-11-01 02:40:44] [Iter 1726/2362] R3[563/1200]  | LR: 0.027418 | E:  -59.991954 | E_var:     4.8701 | E_err:   0.024830
[2025-11-01 02:41:01] [Iter 1727/2362] R3[564/1200]  | LR: 0.027353 | E:  -60.142305 | E_var:     4.8708 | E_err:   0.025729
[2025-11-01 02:41:18] [Iter 1728/2362] R3[565/1200]  | LR: 0.027288 | E:  -60.085663 | E_var:     4.9662 | E_err:   0.025878
[2025-11-01 02:41:36] [Iter 1729/2362] R3[566/1200]  | LR: 0.027223 | E:  -60.026123 | E_var:     4.8630 | E_err:   0.024242
[2025-11-01 02:41:53] [Iter 1730/2362] R3[567/1200]  | LR: 0.027158 | E:  -60.070790 | E_var:     4.8621 | E_err:   0.024310
[2025-11-01 02:42:10] [Iter 1731/2362] R3[568/1200]  | LR: 0.027092 | E:  -60.085257 | E_var:     4.7546 | E_err:   0.023115
[2025-11-01 02:42:27] [Iter 1732/2362] R3[569/1200]  | LR: 0.027027 | E:  -60.080388 | E_var:     4.8426 | E_err:   0.024759
[2025-11-01 02:42:45] [Iter 1733/2362] R3[570/1200]  | LR: 0.026962 | E:  -60.046041 | E_var:     4.8998 | E_err:   0.024528
[2025-11-01 02:43:02] [Iter 1734/2362] R3[571/1200]  | LR: 0.026897 | E:  -60.049153 | E_var:     4.8796 | E_err:   0.024808
[2025-11-01 02:43:19] [Iter 1735/2362] R3[572/1200]  | LR: 0.026831 | E:  -60.071455 | E_var:     4.9388 | E_err:   0.025287
[2025-11-01 02:43:36] [Iter 1736/2362] R3[573/1200]  | LR: 0.026766 | E:  -60.103186 | E_var:     4.8545 | E_err:   0.025138
[2025-11-01 02:43:53] [Iter 1737/2362] R3[574/1200]  | LR: 0.026701 | E:  -60.042480 | E_var:     4.9346 | E_err:   0.024026
[2025-11-01 02:44:11] [Iter 1738/2362] R3[575/1200]  | LR: 0.026636 | E:  -60.057451 | E_var:     5.3197 | E_err:   0.027096
[2025-11-01 02:44:28] [Iter 1739/2362] R3[576/1200]  | LR: 0.026570 | E:  -60.063088 | E_var:     4.8702 | E_err:   0.025556
[2025-11-01 02:44:45] [Iter 1740/2362] R3[577/1200]  | LR: 0.026505 | E:  -60.049449 | E_var:     5.1934 | E_err:   0.024388
[2025-11-01 02:45:02] [Iter 1741/2362] R3[578/1200]  | LR: 0.026440 | E:  -60.035939 | E_var:     4.9691 | E_err:   0.025524
[2025-11-01 02:45:19] [Iter 1742/2362] R3[579/1200]  | LR: 0.026374 | E:  -60.041632 | E_var:     4.9591 | E_err:   0.025513
[2025-11-01 02:45:37] [Iter 1743/2362] R3[580/1200]  | LR: 0.026309 | E:  -60.116719 | E_var:     4.8615 | E_err:   0.024949
[2025-11-01 02:45:54] [Iter 1744/2362] R3[581/1200]  | LR: 0.026244 | E:  -60.064497 | E_var:     4.8277 | E_err:   0.024162
[2025-11-01 02:46:11] [Iter 1745/2362] R3[582/1200]  | LR: 0.026178 | E:  -60.036551 | E_var:     4.9463 | E_err:   0.025047
[2025-11-01 02:46:28] [Iter 1746/2362] R3[583/1200]  | LR: 0.026113 | E:  -60.059658 | E_var:     5.0622 | E_err:   0.024363
[2025-11-01 02:46:46] [Iter 1747/2362] R3[584/1200]  | LR: 0.026047 | E:  -60.059746 | E_var:     4.9957 | E_err:   0.025716
[2025-11-01 02:47:03] [Iter 1748/2362] R3[585/1200]  | LR: 0.025982 | E:  -60.082600 | E_var:     4.9910 | E_err:   0.024770
[2025-11-01 02:47:20] [Iter 1749/2362] R3[586/1200]  | LR: 0.025917 | E:  -60.070071 | E_var:     5.1620 | E_err:   0.025565
[2025-11-01 02:47:37] [Iter 1750/2362] R3[587/1200]  | LR: 0.025851 | E:  -60.073785 | E_var:     4.8718 | E_err:   0.025935
[2025-11-01 02:47:54] [Iter 1751/2362] R3[588/1200]  | LR: 0.025786 | E:  -60.055694 | E_var:     5.2067 | E_err:   0.024815
[2025-11-01 02:48:12] [Iter 1752/2362] R3[589/1200]  | LR: 0.025720 | E:  -60.106376 | E_var:     5.0194 | E_err:   0.024628
[2025-11-01 02:48:29] [Iter 1753/2362] R3[590/1200]  | LR: 0.025655 | E:  -60.077053 | E_var:     5.1555 | E_err:   0.024793
[2025-11-01 02:48:46] [Iter 1754/2362] R3[591/1200]  | LR: 0.025589 | E:  -60.053553 | E_var:     4.9326 | E_err:   0.024823
[2025-11-01 02:49:03] [Iter 1755/2362] R3[592/1200]  | LR: 0.025524 | E:  -60.055237 | E_var:     4.8406 | E_err:   0.024031
[2025-11-01 02:49:20] [Iter 1756/2362] R3[593/1200]  | LR: 0.025459 | E:  -60.118016 | E_var:     5.0317 | E_err:   0.025393
[2025-11-01 02:49:38] [Iter 1757/2362] R3[594/1200]  | LR: 0.025393 | E:  -60.061693 | E_var:     4.7590 | E_err:   0.024169
[2025-11-01 02:49:55] [Iter 1758/2362] R3[595/1200]  | LR: 0.025328 | E:  -60.038649 | E_var:     4.7043 | E_err:   0.024211
[2025-11-01 02:50:12] [Iter 1759/2362] R3[596/1200]  | LR: 0.025262 | E:  -60.105077 | E_var:     4.8330 | E_err:   0.023991
[2025-11-01 02:50:29] [Iter 1760/2362] R3[597/1200]  | LR: 0.025197 | E:  -60.068590 | E_var:     4.7527 | E_err:   0.025194
[2025-11-01 02:50:46] [Iter 1761/2362] R3[598/1200]  | LR: 0.025131 | E:  -60.035893 | E_var:     4.7690 | E_err:   0.024253
[2025-11-01 02:51:04] [Iter 1762/2362] R3[599/1200]  | LR: 0.025066 | E:  -60.068246 | E_var:     4.9192 | E_err:   0.025472
[2025-11-01 02:51:21] [Iter 1763/2362] R3[600/1200]  | LR: 0.025001 | E:  -60.095964 | E_var:     4.9252 | E_err:   0.024934
[2025-11-01 02:51:38] [Iter 1764/2362] R3[601/1200]  | LR: 0.024935 | E:  -60.077069 | E_var:     4.9704 | E_err:   0.026258
[2025-11-01 02:51:55] [Iter 1765/2362] R3[602/1200]  | LR: 0.024870 | E:  -60.044899 | E_var:     4.8534 | E_err:   0.024147
[2025-11-01 02:52:12] [Iter 1766/2362] R3[603/1200]  | LR: 0.024804 | E:  -60.053336 | E_var:     4.9447 | E_err:   0.024808
[2025-11-01 02:52:30] [Iter 1767/2362] R3[604/1200]  | LR: 0.024739 | E:  -60.076973 | E_var:     4.9675 | E_err:   0.024857
[2025-11-01 02:52:47] [Iter 1768/2362] R3[605/1200]  | LR: 0.024673 | E:  -60.090460 | E_var:     5.1072 | E_err:   0.025817
[2025-11-01 02:53:04] [Iter 1769/2362] R3[606/1200]  | LR: 0.024608 | E:  -60.089602 | E_var:     5.0153 | E_err:   0.025671
[2025-11-01 02:53:21] [Iter 1770/2362] R3[607/1200]  | LR: 0.024542 | E:  -60.016354 | E_var:     4.8677 | E_err:   0.024547
[2025-11-01 02:53:39] [Iter 1771/2362] R3[608/1200]  | LR: 0.024477 | E:  -60.057004 | E_var:     5.0854 | E_err:   0.025385
[2025-11-01 02:53:56] [Iter 1772/2362] R3[609/1200]  | LR: 0.024412 | E:  -60.056000 | E_var:     4.7880 | E_err:   0.024747
[2025-11-01 02:54:13] [Iter 1773/2362] R3[610/1200]  | LR: 0.024346 | E:  -60.035607 | E_var:     4.8185 | E_err:   0.025083
[2025-11-01 02:54:30] [Iter 1774/2362] R3[611/1200]  | LR: 0.024281 | E:  -60.024957 | E_var:     5.0111 | E_err:   0.024664
[2025-11-01 02:54:47] [Iter 1775/2362] R3[612/1200]  | LR: 0.024215 | E:  -60.054070 | E_var:     4.9147 | E_err:   0.025088
[2025-11-01 02:55:05] [Iter 1776/2362] R3[613/1200]  | LR: 0.024150 | E:  -60.057186 | E_var:     4.9308 | E_err:   0.025228
[2025-11-01 02:55:22] [Iter 1777/2362] R3[614/1200]  | LR: 0.024084 | E:  -60.100434 | E_var:     5.0350 | E_err:   0.024106
[2025-11-01 02:55:39] [Iter 1778/2362] R3[615/1200]  | LR: 0.024019 | E:  -60.048861 | E_var:     4.8663 | E_err:   0.024080
[2025-11-01 02:55:56] [Iter 1779/2362] R3[616/1200]  | LR: 0.023954 | E:  -60.038710 | E_var:     4.8107 | E_err:   0.023802
[2025-11-01 02:56:13] [Iter 1780/2362] R3[617/1200]  | LR: 0.023888 | E:  -59.988245 | E_var:     4.6794 | E_err:   0.024269
[2025-11-01 02:56:31] [Iter 1781/2362] R3[618/1200]  | LR: 0.023823 | E:  -60.016660 | E_var:     4.7026 | E_err:   0.024652
[2025-11-01 02:56:48] [Iter 1782/2362] R3[619/1200]  | LR: 0.023757 | E:  -60.082046 | E_var:     4.9326 | E_err:   0.024827
[2025-11-01 02:57:05] [Iter 1783/2362] R3[620/1200]  | LR: 0.023692 | E:  -60.017211 | E_var:     4.7848 | E_err:   0.025406
[2025-11-01 02:57:22] [Iter 1784/2362] R3[621/1200]  | LR: 0.023627 | E:  -60.053457 | E_var:     4.9189 | E_err:   0.025037
[2025-11-01 02:57:40] [Iter 1785/2362] R3[622/1200]  | LR: 0.023561 | E:  -60.075892 | E_var:     4.7486 | E_err:   0.024212
[2025-11-01 02:57:57] [Iter 1786/2362] R3[623/1200]  | LR: 0.023496 | E:  -60.040282 | E_var:     4.9148 | E_err:   0.024329
[2025-11-01 02:58:14] [Iter 1787/2362] R3[624/1200]  | LR: 0.023431 | E:  -60.098323 | E_var:     4.9025 | E_err:   0.024892
[2025-11-01 02:58:31] [Iter 1788/2362] R3[625/1200]  | LR: 0.023365 | E:  -60.064313 | E_var:     5.1478 | E_err:   0.025787
[2025-11-01 02:58:48] [Iter 1789/2362] R3[626/1200]  | LR: 0.023300 | E:  -60.018684 | E_var:     5.2626 | E_err:   0.026755
[2025-11-01 02:59:06] [Iter 1790/2362] R3[627/1200]  | LR: 0.023235 | E:  -59.999719 | E_var:     5.5271 | E_err:   0.027338
[2025-11-01 02:59:23] [Iter 1791/2362] R3[628/1200]  | LR: 0.023170 | E:  -59.974340 | E_var:     5.4292 | E_err:   0.026386
[2025-11-01 02:59:40] [Iter 1792/2362] R3[629/1200]  | LR: 0.023104 | E:  -60.019158 | E_var:     4.9855 | E_err:   0.025218
[2025-11-01 02:59:57] [Iter 1793/2362] R3[630/1200]  | LR: 0.023039 | E:  -59.994358 | E_var:     4.9414 | E_err:   0.026010
[2025-11-01 03:00:14] [Iter 1794/2362] R3[631/1200]  | LR: 0.022974 | E:  -59.967644 | E_var:     4.8965 | E_err:   0.025679
[2025-11-01 03:00:32] [Iter 1795/2362] R3[632/1200]  | LR: 0.022909 | E:  -60.017985 | E_var:     5.0378 | E_err:   0.025608
[2025-11-01 03:00:49] [Iter 1796/2362] R3[633/1200]  | LR: 0.022843 | E:  -60.051093 | E_var:     4.9096 | E_err:   0.023973
[2025-11-01 03:01:06] [Iter 1797/2362] R3[634/1200]  | LR: 0.022778 | E:  -59.993990 | E_var:     4.8959 | E_err:   0.025011
[2025-11-01 03:01:23] [Iter 1798/2362] R3[635/1200]  | LR: 0.022713 | E:  -60.079942 | E_var:     5.0104 | E_err:   0.025228
[2025-11-01 03:01:41] [Iter 1799/2362] R3[636/1200]  | LR: 0.022648 | E:  -60.049703 | E_var:     4.8528 | E_err:   0.025380
[2025-11-01 03:01:58] [Iter 1800/2362] R3[637/1200]  | LR: 0.022583 | E:  -60.062658 | E_var:     4.8026 | E_err:   0.024828
[2025-11-01 03:02:15] [Iter 1801/2362] R3[638/1200]  | LR: 0.022518 | E:  -60.056584 | E_var:     5.0101 | E_err:   0.025488
[2025-11-01 03:02:32] [Iter 1802/2362] R3[639/1200]  | LR: 0.022452 | E:  -60.016780 | E_var:     5.1674 | E_err:   0.025403
[2025-11-01 03:02:49] [Iter 1803/2362] R3[640/1200]  | LR: 0.022387 | E:  -60.062829 | E_var:     4.8325 | E_err:   0.024458
[2025-11-01 03:03:07] [Iter 1804/2362] R3[641/1200]  | LR: 0.022322 | E:  -60.035059 | E_var:     5.0074 | E_err:   0.024715
[2025-11-01 03:03:24] [Iter 1805/2362] R3[642/1200]  | LR: 0.022257 | E:  -60.056216 | E_var:     5.1695 | E_err:   0.025487
[2025-11-01 03:03:41] [Iter 1806/2362] R3[643/1200]  | LR: 0.022192 | E:  -60.023568 | E_var:     5.2354 | E_err:   0.026696
[2025-11-01 03:03:58] [Iter 1807/2362] R3[644/1200]  | LR: 0.022127 | E:  -60.037464 | E_var:     5.4664 | E_err:   0.026704
[2025-11-01 03:04:15] [Iter 1808/2362] R3[645/1200]  | LR: 0.022062 | E:  -59.993022 | E_var:     5.1580 | E_err:   0.026211
[2025-11-01 03:04:33] [Iter 1809/2362] R3[646/1200]  | LR: 0.021997 | E:  -60.013670 | E_var:     5.0300 | E_err:   0.025298
[2025-11-01 03:04:50] [Iter 1810/2362] R3[647/1200]  | LR: 0.021932 | E:  -60.076484 | E_var:     5.1705 | E_err:   0.026209
[2025-11-01 03:05:07] [Iter 1811/2362] R3[648/1200]  | LR: 0.021867 | E:  -60.042262 | E_var:     5.1481 | E_err:   0.025400
[2025-11-01 03:05:24] [Iter 1812/2362] R3[649/1200]  | LR: 0.021802 | E:  -60.043665 | E_var:     4.9783 | E_err:   0.026274
[2025-11-01 03:05:42] [Iter 1813/2362] R3[650/1200]  | LR: 0.021737 | E:  -60.054255 | E_var:     4.9187 | E_err:   0.024592
[2025-11-01 03:05:59] [Iter 1814/2362] R3[651/1200]  | LR: 0.021673 | E:  -60.122124 | E_var:     5.0134 | E_err:   0.024834
[2025-11-01 03:06:16] [Iter 1815/2362] R3[652/1200]  | LR: 0.021608 | E:  -60.094172 | E_var:     4.9142 | E_err:   0.025574
[2025-11-01 03:06:33] [Iter 1816/2362] R3[653/1200]  | LR: 0.021543 | E:  -60.059431 | E_var:     5.1863 | E_err:   0.025790
[2025-11-01 03:06:50] [Iter 1817/2362] R3[654/1200]  | LR: 0.021478 | E:  -60.068234 | E_var:     5.1316 | E_err:   0.025761
[2025-11-01 03:07:08] [Iter 1818/2362] R3[655/1200]  | LR: 0.021413 | E:  -60.119701 | E_var:     4.9501 | E_err:   0.025604
[2025-11-01 03:07:25] [Iter 1819/2362] R3[656/1200]  | LR: 0.021348 | E:  -60.108281 | E_var:     4.8538 | E_err:   0.025133
[2025-11-01 03:07:42] [Iter 1820/2362] R3[657/1200]  | LR: 0.021284 | E:  -60.091330 | E_var:     4.9226 | E_err:   0.024909
[2025-11-01 03:07:59] [Iter 1821/2362] R3[658/1200]  | LR: 0.021219 | E:  -60.053658 | E_var:     4.9856 | E_err:   0.025495
[2025-11-01 03:08:16] [Iter 1822/2362] R3[659/1200]  | LR: 0.021154 | E:  -60.059428 | E_var:     4.9728 | E_err:   0.025168
[2025-11-01 03:08:34] [Iter 1823/2362] R3[660/1200]  | LR: 0.021090 | E:  -60.051311 | E_var:     4.8289 | E_err:   0.024817
[2025-11-01 03:08:51] [Iter 1824/2362] R3[661/1200]  | LR: 0.021025 | E:  -60.008186 | E_var:     5.0471 | E_err:   0.025431
[2025-11-01 03:09:08] [Iter 1825/2362] R3[662/1200]  | LR: 0.020960 | E:  -60.056194 | E_var:     4.8388 | E_err:   0.024780
[2025-11-01 03:09:25] [Iter 1826/2362] R3[663/1200]  | LR: 0.020896 | E:  -59.994561 | E_var:     5.0443 | E_err:   0.024942
[2025-11-01 03:09:43] [Iter 1827/2362] R3[664/1200]  | LR: 0.020831 | E:  -59.984216 | E_var:     5.0872 | E_err:   0.025772
[2025-11-01 03:10:00] [Iter 1828/2362] R3[665/1200]  | LR: 0.020767 | E:  -60.072596 | E_var:     4.9989 | E_err:   0.025690
[2025-11-01 03:10:17] [Iter 1829/2362] R3[666/1200]  | LR: 0.020702 | E:  -59.993819 | E_var:     4.8770 | E_err:   0.024424
[2025-11-01 03:10:34] [Iter 1830/2362] R3[667/1200]  | LR: 0.020638 | E:  -59.996151 | E_var:     5.0143 | E_err:   0.025206
[2025-11-01 03:10:51] [Iter 1831/2362] R3[668/1200]  | LR: 0.020573 | E:  -60.057316 | E_var:     5.2804 | E_err:   0.026375
[2025-11-01 03:11:09] [Iter 1832/2362] R3[669/1200]  | LR: 0.020509 | E:  -60.007567 | E_var:     4.9537 | E_err:   0.025237
[2025-11-01 03:11:26] [Iter 1833/2362] R3[670/1200]  | LR: 0.020445 | E:  -60.005205 | E_var:     5.1435 | E_err:   0.025783
[2025-11-01 03:11:43] [Iter 1834/2362] R3[671/1200]  | LR: 0.020380 | E:  -60.094555 | E_var:     5.1400 | E_err:   0.026946
[2025-11-01 03:12:00] [Iter 1835/2362] R3[672/1200]  | LR: 0.020316 | E:  -60.030219 | E_var:     4.8430 | E_err:   0.025176
[2025-11-01 03:12:17] [Iter 1836/2362] R3[673/1200]  | LR: 0.020252 | E:  -60.036612 | E_var:     5.0149 | E_err:   0.025560
[2025-11-01 03:12:35] [Iter 1837/2362] R3[674/1200]  | LR: 0.020188 | E:  -60.050643 | E_var:     5.0586 | E_err:   0.025105
[2025-11-01 03:12:52] [Iter 1838/2362] R3[675/1200]  | LR: 0.020123 | E:  -60.054228 | E_var:     4.9667 | E_err:   0.025085
[2025-11-01 03:13:09] [Iter 1839/2362] R3[676/1200]  | LR: 0.020059 | E:  -60.057096 | E_var:     5.0774 | E_err:   0.025078
[2025-11-01 03:13:26] [Iter 1840/2362] R3[677/1200]  | LR: 0.019995 | E:  -60.052256 | E_var:     5.0265 | E_err:   0.025239
[2025-11-01 03:13:44] [Iter 1841/2362] R3[678/1200]  | LR: 0.019931 | E:  -60.081096 | E_var:     4.9120 | E_err:   0.025574
[2025-11-01 03:14:01] [Iter 1842/2362] R3[679/1200]  | LR: 0.019867 | E:  -60.035367 | E_var:     5.0795 | E_err:   0.025495
[2025-11-01 03:14:18] [Iter 1843/2362] R3[680/1200]  | LR: 0.019803 | E:  -60.039588 | E_var:     4.8259 | E_err:   0.024338
[2025-11-01 03:14:35] [Iter 1844/2362] R3[681/1200]  | LR: 0.019739 | E:  -60.050907 | E_var:     5.2218 | E_err:   0.026102
[2025-11-01 03:14:52] [Iter 1845/2362] R3[682/1200]  | LR: 0.019675 | E:  -60.110472 | E_var:     5.2198 | E_err:   0.025450
[2025-11-01 03:15:10] [Iter 1846/2362] R3[683/1200]  | LR: 0.019611 | E:  -60.053500 | E_var:     5.2009 | E_err:   0.025421
[2025-11-01 03:15:27] [Iter 1847/2362] R3[684/1200]  | LR: 0.019547 | E:  -60.015173 | E_var:     5.2903 | E_err:   0.025268
[2025-11-01 03:15:44] [Iter 1848/2362] R3[685/1200]  | LR: 0.019483 | E:  -60.110085 | E_var:     4.8790 | E_err:   0.024262
[2025-11-01 03:16:01] [Iter 1849/2362] R3[686/1200]  | LR: 0.019419 | E:  -60.058926 | E_var:     4.8130 | E_err:   0.025355
[2025-11-01 03:16:18] [Iter 1850/2362] R3[687/1200]  | LR: 0.019356 | E:  -60.019394 | E_var:     4.9975 | E_err:   0.024853
[2025-11-01 03:16:36] [Iter 1851/2362] R3[688/1200]  | LR: 0.019292 | E:  -60.110273 | E_var:     4.8505 | E_err:   0.025204
[2025-11-01 03:16:53] [Iter 1852/2362] R3[689/1200]  | LR: 0.019228 | E:  -60.098905 | E_var:     4.9611 | E_err:   0.024951
[2025-11-01 03:17:10] [Iter 1853/2362] R3[690/1200]  | LR: 0.019164 | E:  -60.061129 | E_var:     5.2951 | E_err:   0.026022
[2025-11-01 03:17:27] [Iter 1854/2362] R3[691/1200]  | LR: 0.019101 | E:  -60.062793 | E_var:     5.1663 | E_err:   0.025960
[2025-11-01 03:17:45] [Iter 1855/2362] R3[692/1200]  | LR: 0.019037 | E:  -60.079347 | E_var:     5.1689 | E_err:   0.025732
[2025-11-01 03:18:02] [Iter 1856/2362] R3[693/1200]  | LR: 0.018974 | E:  -60.071006 | E_var:     5.0215 | E_err:   0.024929
[2025-11-01 03:18:19] [Iter 1857/2362] R3[694/1200]  | LR: 0.018910 | E:  -60.058335 | E_var:     6.1154 | E_err:   0.032438
[2025-11-01 03:18:36] [Iter 1858/2362] R3[695/1200]  | LR: 0.018847 | E:  -60.053327 | E_var:     5.0432 | E_err:   0.025090
[2025-11-01 03:18:53] [Iter 1859/2362] R3[696/1200]  | LR: 0.018783 | E:  -60.050400 | E_var:     5.1218 | E_err:   0.025963
[2025-11-01 03:19:11] [Iter 1860/2362] R3[697/1200]  | LR: 0.018720 | E:  -60.063840 | E_var:     5.1224 | E_err:   0.025144
[2025-11-01 03:19:28] [Iter 1861/2362] R3[698/1200]  | LR: 0.018657 | E:  -60.053528 | E_var:     5.4137 | E_err:   0.026114
[2025-11-01 03:19:45] [Iter 1862/2362] R3[699/1200]  | LR: 0.018593 | E:  -60.063414 | E_var:     5.1265 | E_err:   0.026301
[2025-11-01 03:19:45] ✓ Checkpoint saved: checkpoint_iter_001750.pkl
[2025-11-01 03:20:02] [Iter 1863/2362] R3[700/1200]  | LR: 0.018530 | E:  -60.076941 | E_var:     5.1570 | E_err:   0.026071
[2025-11-01 03:20:20] [Iter 1864/2362] R3[701/1200]  | LR: 0.018467 | E:  -60.040616 | E_var:     5.3546 | E_err:   0.026232
[2025-11-01 03:20:37] [Iter 1865/2362] R3[702/1200]  | LR: 0.018404 | E:  -60.013115 | E_var:     5.2038 | E_err:   0.026120
[2025-11-01 03:20:54] [Iter 1866/2362] R3[703/1200]  | LR: 0.018341 | E:  -60.055177 | E_var:     5.0386 | E_err:   0.026229
[2025-11-01 03:21:11] [Iter 1867/2362] R3[704/1200]  | LR: 0.018278 | E:  -60.037877 | E_var:     5.2460 | E_err:   0.025928
[2025-11-01 03:21:28] [Iter 1868/2362] R3[705/1200]  | LR: 0.018215 | E:  -60.079065 | E_var:     5.0131 | E_err:   0.025324
[2025-11-01 03:21:46] [Iter 1869/2362] R3[706/1200]  | LR: 0.018152 | E:  -60.095063 | E_var:     5.1672 | E_err:   0.025079
[2025-11-01 03:22:03] [Iter 1870/2362] R3[707/1200]  | LR: 0.018089 | E:  -60.058711 | E_var:     5.0843 | E_err:   0.025209
[2025-11-01 03:22:20] [Iter 1871/2362] R3[708/1200]  | LR: 0.018026 | E:  -60.063102 | E_var:     4.7501 | E_err:   0.025194
[2025-11-01 03:22:37] [Iter 1872/2362] R3[709/1200]  | LR: 0.017963 | E:  -60.054438 | E_var:     4.7522 | E_err:   0.024584
[2025-11-01 03:22:55] [Iter 1873/2362] R3[710/1200]  | LR: 0.017900 | E:  -60.053376 | E_var:     4.8829 | E_err:   0.023696
[2025-11-01 03:23:12] [Iter 1874/2362] R3[711/1200]  | LR: 0.017838 | E:  -60.049988 | E_var:     4.6304 | E_err:   0.024443
[2025-11-01 03:23:29] [Iter 1875/2362] R3[712/1200]  | LR: 0.017775 | E:  -60.030894 | E_var:     4.7086 | E_err:   0.023668
[2025-11-01 03:23:46] [Iter 1876/2362] R3[713/1200]  | LR: 0.017712 | E:  -60.115750 | E_var:     4.9124 | E_err:   0.024518
[2025-11-01 03:24:03] [Iter 1877/2362] R3[714/1200]  | LR: 0.017650 | E:  -60.083635 | E_var:     4.7960 | E_err:   0.024152
[2025-11-01 03:24:21] [Iter 1878/2362] R3[715/1200]  | LR: 0.017587 | E:  -60.103008 | E_var:     4.8821 | E_err:   0.024728
[2025-11-01 03:24:38] [Iter 1879/2362] R3[716/1200]  | LR: 0.017525 | E:  -60.057910 | E_var:     4.7334 | E_err:   0.024957
[2025-11-01 03:24:55] [Iter 1880/2362] R3[717/1200]  | LR: 0.017462 | E:  -60.051463 | E_var:     4.7132 | E_err:   0.025080
[2025-11-01 03:25:12] [Iter 1881/2362] R3[718/1200]  | LR: 0.017400 | E:  -60.025745 | E_var:     4.7979 | E_err:   0.024743
[2025-11-01 03:25:29] [Iter 1882/2362] R3[719/1200]  | LR: 0.017338 | E:  -60.068157 | E_var:     4.7920 | E_err:   0.023843
[2025-11-01 03:25:47] [Iter 1883/2362] R3[720/1200]  | LR: 0.017275 | E:  -60.088455 | E_var:     4.7883 | E_err:   0.023921
[2025-11-01 03:26:04] [Iter 1884/2362] R3[721/1200]  | LR: 0.017213 | E:  -60.092185 | E_var:     5.0611 | E_err:   0.025214
[2025-11-01 03:26:21] [Iter 1885/2362] R3[722/1200]  | LR: 0.017151 | E:  -60.096943 | E_var:     4.9351 | E_err:   0.025235
[2025-11-01 03:26:38] [Iter 1886/2362] R3[723/1200]  | LR: 0.017089 | E:  -60.084677 | E_var:     4.8962 | E_err:   0.024451
[2025-11-01 03:26:56] [Iter 1887/2362] R3[724/1200]  | LR: 0.017027 | E:  -60.078905 | E_var:     4.7374 | E_err:   0.024827
[2025-11-01 03:27:13] [Iter 1888/2362] R3[725/1200]  | LR: 0.016965 | E:  -60.043780 | E_var:     4.8292 | E_err:   0.024657
[2025-11-01 03:27:30] [Iter 1889/2362] R3[726/1200]  | LR: 0.016903 | E:  -60.062621 | E_var:     4.7280 | E_err:   0.024497
[2025-11-01 03:27:47] [Iter 1890/2362] R3[727/1200]  | LR: 0.016841 | E:  -60.060830 | E_var:     4.8302 | E_err:   0.024760
[2025-11-01 03:28:04] [Iter 1891/2362] R3[728/1200]  | LR: 0.016779 | E:  -60.099773 | E_var:     4.8773 | E_err:   0.023771
[2025-11-01 03:28:22] [Iter 1892/2362] R3[729/1200]  | LR: 0.016717 | E:  -60.039120 | E_var:     4.6959 | E_err:   0.024357
[2025-11-01 03:28:39] [Iter 1893/2362] R3[730/1200]  | LR: 0.016655 | E:  -60.025268 | E_var:     5.1168 | E_err:   0.026766
[2025-11-01 03:28:56] [Iter 1894/2362] R3[731/1200]  | LR: 0.016594 | E:  -60.011678 | E_var:     4.7554 | E_err:   0.024113
[2025-11-01 03:29:13] [Iter 1895/2362] R3[732/1200]  | LR: 0.016532 | E:  -60.033727 | E_var:     4.8334 | E_err:   0.024646
[2025-11-01 03:29:31] [Iter 1896/2362] R3[733/1200]  | LR: 0.016471 | E:  -60.031491 | E_var:     4.8127 | E_err:   0.024731
[2025-11-01 03:29:48] [Iter 1897/2362] R3[734/1200]  | LR: 0.016409 | E:  -60.028720 | E_var:     5.0737 | E_err:   0.026150
[2025-11-01 03:30:05] [Iter 1898/2362] R3[735/1200]  | LR: 0.016348 | E:  -60.092487 | E_var:     4.9820 | E_err:   0.025547
[2025-11-01 03:30:22] [Iter 1899/2362] R3[736/1200]  | LR: 0.016286 | E:  -60.063200 | E_var:     4.7721 | E_err:   0.025433
[2025-11-01 03:30:39] [Iter 1900/2362] R3[737/1200]  | LR: 0.016225 | E:  -60.077723 | E_var:     5.1374 | E_err:   0.026027
[2025-11-01 03:30:57] [Iter 1901/2362] R3[738/1200]  | LR: 0.016164 | E:  -60.140624 | E_var:     5.0087 | E_err:   0.024659
[2025-11-01 03:31:14] [Iter 1902/2362] R3[739/1200]  | LR: 0.016103 | E:  -60.097724 | E_var:     4.9001 | E_err:   0.025126
[2025-11-01 03:31:31] [Iter 1903/2362] R3[740/1200]  | LR: 0.016041 | E:  -60.091478 | E_var:     4.7810 | E_err:   0.024041
[2025-11-01 03:31:48] [Iter 1904/2362] R3[741/1200]  | LR: 0.015980 | E:  -60.136922 | E_var:     4.9895 | E_err:   0.025303
[2025-11-01 03:32:05] [Iter 1905/2362] R3[742/1200]  | LR: 0.015919 | E:  -60.105423 | E_var:     5.0092 | E_err:   0.025103
[2025-11-01 03:32:23] [Iter 1906/2362] R3[743/1200]  | LR: 0.015858 | E:  -60.090562 | E_var:     4.8419 | E_err:   0.024592
[2025-11-01 03:32:40] [Iter 1907/2362] R3[744/1200]  | LR: 0.015798 | E:  -60.085234 | E_var:     5.0035 | E_err:   0.024368
[2025-11-01 03:32:57] [Iter 1908/2362] R3[745/1200]  | LR: 0.015737 | E:  -60.052762 | E_var:     4.7601 | E_err:   0.025424
[2025-11-01 03:33:14] [Iter 1909/2362] R3[746/1200]  | LR: 0.015676 | E:  -60.042029 | E_var:     4.8836 | E_err:   0.025280
[2025-11-01 03:33:31] [Iter 1910/2362] R3[747/1200]  | LR: 0.015615 | E:  -60.104391 | E_var:     4.8454 | E_err:   0.024869
[2025-11-01 03:33:49] [Iter 1911/2362] R3[748/1200]  | LR: 0.015555 | E:  -60.029207 | E_var:     4.6697 | E_err:   0.024039
[2025-11-01 03:34:06] [Iter 1912/2362] R3[749/1200]  | LR: 0.015494 | E:  -60.086904 | E_var:     4.6920 | E_err:   0.025615
[2025-11-01 03:34:23] [Iter 1913/2362] R3[750/1200]  | LR: 0.015434 | E:  -60.031101 | E_var:     4.5477 | E_err:   0.023835
[2025-11-01 03:34:40] [Iter 1914/2362] R3[751/1200]  | LR: 0.015373 | E:  -60.093286 | E_var:     4.8723 | E_err:   0.024679
[2025-11-01 03:34:58] [Iter 1915/2362] R3[752/1200]  | LR: 0.015313 | E:  -60.056435 | E_var:     4.9051 | E_err:   0.024474
[2025-11-01 03:35:15] [Iter 1916/2362] R3[753/1200]  | LR: 0.015253 | E:  -60.106646 | E_var:     5.0833 | E_err:   0.026309
[2025-11-01 03:35:32] [Iter 1917/2362] R3[754/1200]  | LR: 0.015192 | E:  -60.094008 | E_var:     5.0081 | E_err:   0.025579
[2025-11-01 03:35:49] [Iter 1918/2362] R3[755/1200]  | LR: 0.015132 | E:  -60.062093 | E_var:     5.6071 | E_err:   0.027310
[2025-11-01 03:36:06] [Iter 1919/2362] R3[756/1200]  | LR: 0.015072 | E:  -60.087583 | E_var:     5.0100 | E_err:   0.025033
[2025-11-01 03:36:24] [Iter 1920/2362] R3[757/1200]  | LR: 0.015012 | E:  -60.111930 | E_var:     5.0520 | E_err:   0.025767
[2025-11-01 03:36:41] [Iter 1921/2362] R3[758/1200]  | LR: 0.014952 | E:  -60.133164 | E_var:     5.1326 | E_err:   0.025528
[2025-11-01 03:36:58] [Iter 1922/2362] R3[759/1200]  | LR: 0.014892 | E:  -60.090098 | E_var:     5.1208 | E_err:   0.025449
[2025-11-01 03:37:15] [Iter 1923/2362] R3[760/1200]  | LR: 0.014832 | E:  -60.067423 | E_var:     4.9867 | E_err:   0.025535
[2025-11-01 03:37:32] [Iter 1924/2362] R3[761/1200]  | LR: 0.014773 | E:  -60.106967 | E_var:     5.0743 | E_err:   0.025460
[2025-11-01 03:37:50] [Iter 1925/2362] R3[762/1200]  | LR: 0.014713 | E:  -60.074188 | E_var:     4.8630 | E_err:   0.024078
[2025-11-01 03:38:07] [Iter 1926/2362] R3[763/1200]  | LR: 0.014653 | E:  -60.096493 | E_var:     5.0017 | E_err:   0.025885
[2025-11-01 03:38:24] [Iter 1927/2362] R3[764/1200]  | LR: 0.014594 | E:  -60.059157 | E_var:     4.6155 | E_err:   0.024328
[2025-11-01 03:38:41] [Iter 1928/2362] R3[765/1200]  | LR: 0.014534 | E:  -60.120298 | E_var:     4.7943 | E_err:   0.025837
[2025-11-01 03:38:59] [Iter 1929/2362] R3[766/1200]  | LR: 0.014475 | E:  -60.102124 | E_var:     4.7137 | E_err:   0.024109
[2025-11-01 03:39:16] [Iter 1930/2362] R3[767/1200]  | LR: 0.014415 | E:  -60.090950 | E_var:     4.8208 | E_err:   0.024231
[2025-11-01 03:39:33] [Iter 1931/2362] R3[768/1200]  | LR: 0.014356 | E:  -60.090443 | E_var:     4.8149 | E_err:   0.025147
[2025-11-01 03:39:50] [Iter 1932/2362] R3[769/1200]  | LR: 0.014297 | E:  -60.125926 | E_var:     4.9597 | E_err:   0.024817
[2025-11-01 03:40:07] [Iter 1933/2362] R3[770/1200]  | LR: 0.014238 | E:  -60.105300 | E_var:     4.7413 | E_err:   0.024386
[2025-11-01 03:40:25] [Iter 1934/2362] R3[771/1200]  | LR: 0.014179 | E:  -60.140770 | E_var:     4.9334 | E_err:   0.024765
[2025-11-01 03:40:42] [Iter 1935/2362] R3[772/1200]  | LR: 0.014120 | E:  -60.081117 | E_var:     4.9341 | E_err:   0.024829
[2025-11-01 03:40:59] [Iter 1936/2362] R3[773/1200]  | LR: 0.014061 | E:  -60.087128 | E_var:     4.8913 | E_err:   0.024658
[2025-11-01 03:41:16] [Iter 1937/2362] R3[774/1200]  | LR: 0.014002 | E:  -60.085824 | E_var:     4.8024 | E_err:   0.024818
[2025-11-01 03:41:34] [Iter 1938/2362] R3[775/1200]  | LR: 0.013944 | E:  -60.129308 | E_var:     4.9725 | E_err:   0.025103
[2025-11-01 03:41:51] [Iter 1939/2362] R3[776/1200]  | LR: 0.013885 | E:  -60.129786 | E_var:     4.7568 | E_err:   0.024577
[2025-11-01 03:42:08] [Iter 1940/2362] R3[777/1200]  | LR: 0.013826 | E:  -60.104409 | E_var:     4.9543 | E_err:   0.024920
[2025-11-01 03:42:25] [Iter 1941/2362] R3[778/1200]  | LR: 0.013768 | E:  -60.075400 | E_var:     5.2152 | E_err:   0.026301
[2025-11-01 03:42:42] [Iter 1942/2362] R3[779/1200]  | LR: 0.013709 | E:  -60.048242 | E_var:     4.9929 | E_err:   0.025113
[2025-11-01 03:43:00] [Iter 1943/2362] R3[780/1200]  | LR: 0.013651 | E:  -60.090403 | E_var:     4.7492 | E_err:   0.024639
[2025-11-01 03:43:17] [Iter 1944/2362] R3[781/1200]  | LR: 0.013593 | E:  -60.103513 | E_var:     4.8237 | E_err:   0.024367
[2025-11-01 03:43:34] [Iter 1945/2362] R3[782/1200]  | LR: 0.013534 | E:  -60.112211 | E_var:     5.0579 | E_err:   0.025626
[2025-11-01 03:43:51] [Iter 1946/2362] R3[783/1200]  | LR: 0.013476 | E:  -60.070895 | E_var:     4.9285 | E_err:   0.024402
[2025-11-01 03:44:08] [Iter 1947/2362] R3[784/1200]  | LR: 0.013418 | E:  -60.105004 | E_var:     4.9945 | E_err:   0.025808
[2025-11-01 03:44:26] [Iter 1948/2362] R3[785/1200]  | LR: 0.013360 | E:  -60.130655 | E_var:     4.7136 | E_err:   0.023643
[2025-11-01 03:44:43] [Iter 1949/2362] R3[786/1200]  | LR: 0.013302 | E:  -60.084880 | E_var:     5.0651 | E_err:   0.025381
[2025-11-01 03:45:00] [Iter 1950/2362] R3[787/1200]  | LR: 0.013245 | E:  -60.145820 | E_var:     4.9098 | E_err:   0.024713
[2025-11-01 03:45:17] [Iter 1951/2362] R3[788/1200]  | LR: 0.013187 | E:  -60.114311 | E_var:     4.8116 | E_err:   0.023991
[2025-11-01 03:45:35] [Iter 1952/2362] R3[789/1200]  | LR: 0.013129 | E:  -60.089623 | E_var:     4.7766 | E_err:   0.024342
[2025-11-01 03:45:52] [Iter 1953/2362] R3[790/1200]  | LR: 0.013072 | E:  -60.108116 | E_var:     4.7014 | E_err:   0.024302
[2025-11-01 03:46:09] [Iter 1954/2362] R3[791/1200]  | LR: 0.013014 | E:  -60.098501 | E_var:     4.7216 | E_err:   0.025193
[2025-11-01 03:46:26] [Iter 1955/2362] R3[792/1200]  | LR: 0.012957 | E:  -60.089181 | E_var:     4.8275 | E_err:   0.025390
[2025-11-01 03:46:43] [Iter 1956/2362] R3[793/1200]  | LR: 0.012900 | E:  -60.040879 | E_var:     4.8482 | E_err:   0.025510
[2025-11-01 03:47:01] [Iter 1957/2362] R3[794/1200]  | LR: 0.012842 | E:  -60.079781 | E_var:     4.7393 | E_err:   0.023668
[2025-11-01 03:47:18] [Iter 1958/2362] R3[795/1200]  | LR: 0.012785 | E:  -60.095026 | E_var:     4.7768 | E_err:   0.024075
[2025-11-01 03:47:35] [Iter 1959/2362] R3[796/1200]  | LR: 0.012728 | E:  -60.063454 | E_var:     4.9771 | E_err:   0.025363
[2025-11-01 03:47:52] [Iter 1960/2362] R3[797/1200]  | LR: 0.012671 | E:  -60.091542 | E_var:     4.7083 | E_err:   0.024498
[2025-11-01 03:48:10] [Iter 1961/2362] R3[798/1200]  | LR: 0.012614 | E:  -60.103085 | E_var:     4.8307 | E_err:   0.024185
[2025-11-01 03:48:27] [Iter 1962/2362] R3[799/1200]  | LR: 0.012557 | E:  -60.116442 | E_var:     4.8663 | E_err:   0.024057
[2025-11-01 03:48:44] [Iter 1963/2362] R3[800/1200]  | LR: 0.012501 | E:  -60.106976 | E_var:     4.9041 | E_err:   0.025085
[2025-11-01 03:49:01] [Iter 1964/2362] R3[801/1200]  | LR: 0.012444 | E:  -60.090794 | E_var:     4.8128 | E_err:   0.024513
[2025-11-01 03:49:18] [Iter 1965/2362] R3[802/1200]  | LR: 0.012388 | E:  -60.115839 | E_var:     5.1156 | E_err:   0.025739
[2025-11-01 03:49:36] [Iter 1966/2362] R3[803/1200]  | LR: 0.012331 | E:  -60.102519 | E_var:     4.7339 | E_err:   0.024537
[2025-11-01 03:49:53] [Iter 1967/2362] R3[804/1200]  | LR: 0.012275 | E:  -60.111992 | E_var:     4.8369 | E_err:   0.025302
[2025-11-01 03:50:10] [Iter 1968/2362] R3[805/1200]  | LR: 0.012218 | E:  -60.062465 | E_var:     4.6549 | E_err:   0.024101
[2025-11-01 03:50:27] [Iter 1969/2362] R3[806/1200]  | LR: 0.012162 | E:  -60.140015 | E_var:     4.8375 | E_err:   0.025409
[2025-11-01 03:50:44] [Iter 1970/2362] R3[807/1200]  | LR: 0.012106 | E:  -60.139894 | E_var:     4.8364 | E_err:   0.023806
[2025-11-01 03:51:02] [Iter 1971/2362] R3[808/1200]  | LR: 0.012050 | E:  -60.110514 | E_var:     5.2689 | E_err:   0.025275
[2025-11-01 03:51:19] [Iter 1972/2362] R3[809/1200]  | LR: 0.011994 | E:  -60.058068 | E_var:     5.3265 | E_err:   0.026004
[2025-11-01 03:51:36] [Iter 1973/2362] R3[810/1200]  | LR: 0.011938 | E:  -60.128527 | E_var:     5.3017 | E_err:   0.026630
[2025-11-01 03:51:53] [Iter 1974/2362] R3[811/1200]  | LR: 0.011883 | E:  -60.103446 | E_var:     4.9391 | E_err:   0.025586
[2025-11-01 03:52:11] [Iter 1975/2362] R3[812/1200]  | LR: 0.011827 | E:  -60.117995 | E_var:     4.9131 | E_err:   0.024443
[2025-11-01 03:52:28] [Iter 1976/2362] R3[813/1200]  | LR: 0.011771 | E:  -60.105565 | E_var:     4.8962 | E_err:   0.025076
[2025-11-01 03:52:45] [Iter 1977/2362] R3[814/1200]  | LR: 0.011716 | E:  -60.150743 | E_var:     4.8519 | E_err:   0.025027
[2025-11-01 03:53:02] [Iter 1978/2362] R3[815/1200]  | LR: 0.011660 | E:  -60.141307 | E_var:     4.8392 | E_err:   0.025241
[2025-11-01 03:53:19] [Iter 1979/2362] R3[816/1200]  | LR: 0.011605 | E:  -60.123325 | E_var:     4.8777 | E_err:   0.024767
[2025-11-01 03:53:37] [Iter 1980/2362] R3[817/1200]  | LR: 0.011550 | E:  -60.119009 | E_var:     5.0391 | E_err:   0.024353
[2025-11-01 03:53:54] [Iter 1981/2362] R3[818/1200]  | LR: 0.011495 | E:  -60.087944 | E_var:     4.8195 | E_err:   0.025110
[2025-11-01 03:54:11] [Iter 1982/2362] R3[819/1200]  | LR: 0.011440 | E:  -60.140169 | E_var:     4.9579 | E_err:   0.024910
[2025-11-01 03:54:28] [Iter 1983/2362] R3[820/1200]  | LR: 0.011385 | E:  -60.092543 | E_var:     4.9535 | E_err:   0.024686
[2025-11-01 03:54:45] [Iter 1984/2362] R3[821/1200]  | LR: 0.011330 | E:  -60.139827 | E_var:     4.7252 | E_err:   0.023828
[2025-11-01 03:55:03] [Iter 1985/2362] R3[822/1200]  | LR: 0.011275 | E:  -60.084050 | E_var:     4.8095 | E_err:   0.025104
[2025-11-01 03:55:20] [Iter 1986/2362] R3[823/1200]  | LR: 0.011221 | E:  -60.096930 | E_var:     4.6548 | E_err:   0.023957
[2025-11-01 03:55:37] [Iter 1987/2362] R3[824/1200]  | LR: 0.011166 | E:  -60.137878 | E_var:     4.7892 | E_err:   0.025280
[2025-11-01 03:55:54] [Iter 1988/2362] R3[825/1200]  | LR: 0.011112 | E:  -60.118513 | E_var:     4.9886 | E_err:   0.025209
[2025-11-01 03:56:12] [Iter 1989/2362] R3[826/1200]  | LR: 0.011057 | E:  -60.100188 | E_var:     4.9115 | E_err:   0.025091
[2025-11-01 03:56:29] [Iter 1990/2362] R3[827/1200]  | LR: 0.011003 | E:  -60.132206 | E_var:     4.9397 | E_err:   0.025282
[2025-11-01 03:56:46] [Iter 1991/2362] R3[828/1200]  | LR: 0.010949 | E:  -60.133653 | E_var:     4.5423 | E_err:   0.023608
[2025-11-01 03:57:03] [Iter 1992/2362] R3[829/1200]  | LR: 0.010895 | E:  -60.125203 | E_var:     4.8777 | E_err:   0.024605
[2025-11-01 03:57:20] [Iter 1993/2362] R3[830/1200]  | LR: 0.010841 | E:  -60.130993 | E_var:     4.9199 | E_err:   0.024012
[2025-11-01 03:57:38] [Iter 1994/2362] R3[831/1200]  | LR: 0.010787 | E:  -60.097494 | E_var:     5.2256 | E_err:   0.026148
[2025-11-01 03:57:55] [Iter 1995/2362] R3[832/1200]  | LR: 0.010733 | E:  -60.085754 | E_var:     4.9271 | E_err:   0.024992
[2025-11-01 03:58:12] [Iter 1996/2362] R3[833/1200]  | LR: 0.010679 | E:  -60.084459 | E_var:     4.7260 | E_err:   0.025131
[2025-11-01 03:58:29] [Iter 1997/2362] R3[834/1200]  | LR: 0.010626 | E:  -60.090114 | E_var:     4.9932 | E_err:   0.025429
[2025-11-01 03:58:47] [Iter 1998/2362] R3[835/1200]  | LR: 0.010572 | E:  -60.077687 | E_var:     4.8571 | E_err:   0.023786
[2025-11-01 03:59:04] [Iter 1999/2362] R3[836/1200]  | LR: 0.010519 | E:  -60.096317 | E_var:     4.8867 | E_err:   0.025255
[2025-11-01 03:59:21] [Iter 2000/2362] R3[837/1200]  | LR: 0.010465 | E:  -60.082016 | E_var:     4.7653 | E_err:   0.024555
[2025-11-01 03:59:38] [Iter 2001/2362] R3[838/1200]  | LR: 0.010412 | E:  -60.095192 | E_var:     4.8251 | E_err:   0.023961
[2025-11-01 03:59:55] [Iter 2002/2362] R3[839/1200]  | LR: 0.010359 | E:  -60.037500 | E_var:     4.7943 | E_err:   0.024802
[2025-11-01 04:00:13] [Iter 2003/2362] R3[840/1200]  | LR: 0.010306 | E:  -60.138546 | E_var:     4.6662 | E_err:   0.025175
[2025-11-01 04:00:30] [Iter 2004/2362] R3[841/1200]  | LR: 0.010253 | E:  -60.126762 | E_var:     4.8521 | E_err:   0.024093
[2025-11-01 04:00:47] [Iter 2005/2362] R3[842/1200]  | LR: 0.010200 | E:  -60.138625 | E_var:     4.8230 | E_err:   0.024741
[2025-11-01 04:01:04] [Iter 2006/2362] R3[843/1200]  | LR: 0.010148 | E:  -60.086463 | E_var:     4.8138 | E_err:   0.024730
[2025-11-01 04:01:21] [Iter 2007/2362] R3[844/1200]  | LR: 0.010095 | E:  -60.087894 | E_var:     4.7301 | E_err:   0.024274
[2025-11-01 04:01:39] [Iter 2008/2362] R3[845/1200]  | LR: 0.010043 | E:  -60.092537 | E_var:     4.6058 | E_err:   0.023859
[2025-11-01 04:01:56] [Iter 2009/2362] R3[846/1200]  | LR: 0.009990 | E:  -60.149772 | E_var:     4.8014 | E_err:   0.023630
[2025-11-01 04:02:13] [Iter 2010/2362] R3[847/1200]  | LR: 0.009938 | E:  -60.135313 | E_var:     4.8007 | E_err:   0.024659
[2025-11-01 04:02:30] [Iter 2011/2362] R3[848/1200]  | LR: 0.009886 | E:  -60.161440 | E_var:     4.9299 | E_err:   0.025004
[2025-11-01 04:02:48] [Iter 2012/2362] R3[849/1200]  | LR: 0.009834 | E:  -60.109086 | E_var:     4.6772 | E_err:   0.024756
[2025-11-01 04:03:05] [Iter 2013/2362] R3[850/1200]  | LR: 0.009782 | E:  -60.132463 | E_var:     4.7297 | E_err:   0.024686
[2025-11-01 04:03:22] [Iter 2014/2362] R3[851/1200]  | LR: 0.009730 | E:  -60.143370 | E_var:     4.7758 | E_err:   0.024272
[2025-11-01 04:03:39] [Iter 2015/2362] R3[852/1200]  | LR: 0.009678 | E:  -60.099459 | E_var:     4.5189 | E_err:   0.024679
[2025-11-01 04:03:56] [Iter 2016/2362] R3[853/1200]  | LR: 0.009626 | E:  -60.110207 | E_var:     4.8060 | E_err:   0.024586
[2025-11-01 04:04:14] [Iter 2017/2362] R3[854/1200]  | LR: 0.009575 | E:  -60.064844 | E_var:     4.8486 | E_err:   0.025104
[2025-11-01 04:04:31] [Iter 2018/2362] R3[855/1200]  | LR: 0.009523 | E:  -60.130479 | E_var:     4.7807 | E_err:   0.024139
[2025-11-01 04:04:48] [Iter 2019/2362] R3[856/1200]  | LR: 0.009472 | E:  -60.096948 | E_var:     4.8709 | E_err:   0.024611
[2025-11-01 04:05:05] [Iter 2020/2362] R3[857/1200]  | LR: 0.009421 | E:  -60.094812 | E_var:     4.7323 | E_err:   0.024408
[2025-11-01 04:05:22] [Iter 2021/2362] R3[858/1200]  | LR: 0.009370 | E:  -60.122504 | E_var:     4.7388 | E_err:   0.024176
[2025-11-01 04:05:40] [Iter 2022/2362] R3[859/1200]  | LR: 0.009319 | E:  -60.110627 | E_var:     4.8443 | E_err:   0.024235
[2025-11-01 04:05:57] [Iter 2023/2362] R3[860/1200]  | LR: 0.009268 | E:  -60.136686 | E_var:     4.7501 | E_err:   0.023796
[2025-11-01 04:06:14] [Iter 2024/2362] R3[861/1200]  | LR: 0.009217 | E:  -60.130524 | E_var:     4.9419 | E_err:   0.024444
[2025-11-01 04:06:31] [Iter 2025/2362] R3[862/1200]  | LR: 0.009166 | E:  -60.108708 | E_var:     4.6619 | E_err:   0.024513
[2025-11-01 04:06:49] [Iter 2026/2362] R3[863/1200]  | LR: 0.009116 | E:  -60.137594 | E_var:     4.6889 | E_err:   0.024566
[2025-11-01 04:07:06] [Iter 2027/2362] R3[864/1200]  | LR: 0.009065 | E:  -60.139762 | E_var:     4.8688 | E_err:   0.025482
[2025-11-01 04:07:23] [Iter 2028/2362] R3[865/1200]  | LR: 0.009015 | E:  -60.130569 | E_var:     4.8201 | E_err:   0.024297
[2025-11-01 04:07:40] [Iter 2029/2362] R3[866/1200]  | LR: 0.008965 | E:  -60.110235 | E_var:     5.0761 | E_err:   0.025726
[2025-11-01 04:07:57] [Iter 2030/2362] R3[867/1200]  | LR: 0.008914 | E:  -60.108749 | E_var:     5.2473 | E_err:   0.025885
[2025-11-01 04:08:15] [Iter 2031/2362] R3[868/1200]  | LR: 0.008864 | E:  -60.138478 | E_var:     5.0470 | E_err:   0.025813
[2025-11-01 04:08:32] [Iter 2032/2362] R3[869/1200]  | LR: 0.008814 | E:  -60.130075 | E_var:     5.0022 | E_err:   0.024971
[2025-11-01 04:08:49] [Iter 2033/2362] R3[870/1200]  | LR: 0.008765 | E:  -60.103922 | E_var:     4.9235 | E_err:   0.025669
[2025-11-01 04:09:06] [Iter 2034/2362] R3[871/1200]  | LR: 0.008715 | E:  -60.134816 | E_var:     4.7365 | E_err:   0.024902
[2025-11-01 04:09:24] [Iter 2035/2362] R3[872/1200]  | LR: 0.008665 | E:  -60.137373 | E_var:     4.8743 | E_err:   0.025006
[2025-11-01 04:09:41] [Iter 2036/2362] R3[873/1200]  | LR: 0.008616 | E:  -60.097654 | E_var:     4.7996 | E_err:   0.024904
[2025-11-01 04:09:58] [Iter 2037/2362] R3[874/1200]  | LR: 0.008566 | E:  -60.150262 | E_var:     4.8364 | E_err:   0.025125
[2025-11-01 04:10:15] [Iter 2038/2362] R3[875/1200]  | LR: 0.008517 | E:  -60.124334 | E_var:     4.7786 | E_err:   0.024386
[2025-11-01 04:10:32] [Iter 2039/2362] R3[876/1200]  | LR: 0.008468 | E:  -60.122055 | E_var:     4.6024 | E_err:   0.024189
[2025-11-01 04:10:50] [Iter 2040/2362] R3[877/1200]  | LR: 0.008419 | E:  -60.139616 | E_var:     4.6842 | E_err:   0.023888
[2025-11-01 04:11:07] [Iter 2041/2362] R3[878/1200]  | LR: 0.008370 | E:  -60.111320 | E_var:     5.0229 | E_err:   0.024487
[2025-11-01 04:11:24] [Iter 2042/2362] R3[879/1200]  | LR: 0.008321 | E:  -60.137329 | E_var:     4.8929 | E_err:   0.024936
[2025-11-01 04:11:41] [Iter 2043/2362] R3[880/1200]  | LR: 0.008273 | E:  -60.125852 | E_var:     4.7435 | E_err:   0.024607
[2025-11-01 04:11:58] [Iter 2044/2362] R3[881/1200]  | LR: 0.008224 | E:  -60.132528 | E_var:     4.6843 | E_err:   0.024421
[2025-11-01 04:12:16] [Iter 2045/2362] R3[882/1200]  | LR: 0.008176 | E:  -60.085244 | E_var:     4.5485 | E_err:   0.024126
[2025-11-01 04:12:33] [Iter 2046/2362] R3[883/1200]  | LR: 0.008127 | E:  -60.096030 | E_var:     4.6485 | E_err:   0.024665
[2025-11-01 04:12:50] [Iter 2047/2362] R3[884/1200]  | LR: 0.008079 | E:  -60.124802 | E_var:     4.6766 | E_err:   0.023929
[2025-11-01 04:13:07] [Iter 2048/2362] R3[885/1200]  | LR: 0.008031 | E:  -60.116863 | E_var:     5.0428 | E_err:   0.025450
[2025-11-01 04:13:25] [Iter 2049/2362] R3[886/1200]  | LR: 0.007983 | E:  -60.121526 | E_var:     4.8535 | E_err:   0.024684
[2025-11-01 04:13:42] [Iter 2050/2362] R3[887/1200]  | LR: 0.007935 | E:  -60.128006 | E_var:     4.8480 | E_err:   0.024963
[2025-11-01 04:13:59] [Iter 2051/2362] R3[888/1200]  | LR: 0.007887 | E:  -60.112335 | E_var:     4.5779 | E_err:   0.023873
[2025-11-01 04:14:16] [Iter 2052/2362] R3[889/1200]  | LR: 0.007840 | E:  -60.118258 | E_var:     4.8188 | E_err:   0.025307
[2025-11-01 04:14:33] [Iter 2053/2362] R3[890/1200]  | LR: 0.007792 | E:  -60.092892 | E_var:     4.9096 | E_err:   0.024759
[2025-11-01 04:14:51] [Iter 2054/2362] R3[891/1200]  | LR: 0.007745 | E:  -60.121008 | E_var:     4.8681 | E_err:   0.025402
[2025-11-01 04:15:08] [Iter 2055/2362] R3[892/1200]  | LR: 0.007697 | E:  -60.131788 | E_var:     4.7443 | E_err:   0.024530
[2025-11-01 04:15:25] [Iter 2056/2362] R3[893/1200]  | LR: 0.007650 | E:  -60.106364 | E_var:     4.8393 | E_err:   0.024638
[2025-11-01 04:15:42] [Iter 2057/2362] R3[894/1200]  | LR: 0.007603 | E:  -60.160398 | E_var:     4.7889 | E_err:   0.024170
[2025-11-01 04:15:59] [Iter 2058/2362] R3[895/1200]  | LR: 0.007556 | E:  -60.106174 | E_var:     4.8089 | E_err:   0.024946
[2025-11-01 04:16:17] [Iter 2059/2362] R3[896/1200]  | LR: 0.007509 | E:  -60.171165 | E_var:     5.0493 | E_err:   0.025015
[2025-11-01 04:16:34] [Iter 2060/2362] R3[897/1200]  | LR: 0.007463 | E:  -60.152322 | E_var:     4.9799 | E_err:   0.024873
[2025-11-01 04:16:51] [Iter 2061/2362] R3[898/1200]  | LR: 0.007416 | E:  -60.122333 | E_var:     4.9450 | E_err:   0.024080
[2025-11-01 04:17:08] [Iter 2062/2362] R3[899/1200]  | LR: 0.007370 | E:  -60.124643 | E_var:     4.9734 | E_err:   0.024996
[2025-11-01 04:17:26] [Iter 2063/2362] R3[900/1200]  | LR: 0.007323 | E:  -60.096602 | E_var:     4.9927 | E_err:   0.024661
[2025-11-01 04:17:43] [Iter 2064/2362] R3[901/1200]  | LR: 0.007277 | E:  -60.116991 | E_var:     5.1812 | E_err:   0.024967
[2025-11-01 04:18:00] [Iter 2065/2362] R3[902/1200]  | LR: 0.007231 | E:  -60.135140 | E_var:     4.9513 | E_err:   0.025092
[2025-11-01 04:18:17] [Iter 2066/2362] R3[903/1200]  | LR: 0.007185 | E:  -60.078838 | E_var:     5.0611 | E_err:   0.025418
[2025-11-01 04:18:34] [Iter 2067/2362] R3[904/1200]  | LR: 0.007139 | E:  -60.143870 | E_var:     4.7851 | E_err:   0.024321
[2025-11-01 04:18:52] [Iter 2068/2362] R3[905/1200]  | LR: 0.007093 | E:  -60.176949 | E_var:     4.6985 | E_err:   0.024457
[2025-11-01 04:19:09] [Iter 2069/2362] R3[906/1200]  | LR: 0.007048 | E:  -60.112791 | E_var:     4.8968 | E_err:   0.025382
[2025-11-01 04:19:26] [Iter 2070/2362] R3[907/1200]  | LR: 0.007002 | E:  -60.097923 | E_var:     4.8870 | E_err:   0.024783
[2025-11-01 04:19:43] [Iter 2071/2362] R3[908/1200]  | LR: 0.006957 | E:  -60.106409 | E_var:     4.6552 | E_err:   0.024220
[2025-11-01 04:20:01] [Iter 2072/2362] R3[909/1200]  | LR: 0.006912 | E:  -60.083996 | E_var:     4.6649 | E_err:   0.025221
[2025-11-01 04:20:18] [Iter 2073/2362] R3[910/1200]  | LR: 0.006867 | E:  -60.162655 | E_var:     4.8640 | E_err:   0.024620
[2025-11-01 04:20:35] [Iter 2074/2362] R3[911/1200]  | LR: 0.006822 | E:  -60.146547 | E_var:     4.7463 | E_err:   0.024810
[2025-11-01 04:20:52] [Iter 2075/2362] R3[912/1200]  | LR: 0.006777 | E:  -60.132049 | E_var:     4.6773 | E_err:   0.024885
[2025-11-01 04:21:09] [Iter 2076/2362] R3[913/1200]  | LR: 0.006732 | E:  -60.161998 | E_var:     4.7777 | E_err:   0.024612
[2025-11-01 04:21:27] [Iter 2077/2362] R3[914/1200]  | LR: 0.006687 | E:  -60.103723 | E_var:     4.7399 | E_err:   0.024949
[2025-11-01 04:21:44] [Iter 2078/2362] R3[915/1200]  | LR: 0.006643 | E:  -60.107681 | E_var:     4.7046 | E_err:   0.024221
[2025-11-01 04:22:01] [Iter 2079/2362] R3[916/1200]  | LR: 0.006598 | E:  -60.115554 | E_var:     5.0359 | E_err:   0.025871
[2025-11-01 04:22:18] [Iter 2080/2362] R3[917/1200]  | LR: 0.006554 | E:  -60.129846 | E_var:     4.7942 | E_err:   0.025125
[2025-11-01 04:22:35] [Iter 2081/2362] R3[918/1200]  | LR: 0.006510 | E:  -60.115274 | E_var:     4.7736 | E_err:   0.024785
[2025-11-01 04:22:53] [Iter 2082/2362] R3[919/1200]  | LR: 0.006466 | E:  -60.117900 | E_var:     4.9052 | E_err:   0.024902
[2025-11-01 04:23:10] [Iter 2083/2362] R3[920/1200]  | LR: 0.006422 | E:  -60.105188 | E_var:     4.6976 | E_err:   0.024666
[2025-11-01 04:23:27] [Iter 2084/2362] R3[921/1200]  | LR: 0.006379 | E:  -60.127283 | E_var:     4.6851 | E_err:   0.023890
[2025-11-01 04:23:44] [Iter 2085/2362] R3[922/1200]  | LR: 0.006335 | E:  -60.161731 | E_var:     4.8922 | E_err:   0.023998
[2025-11-01 04:24:02] [Iter 2086/2362] R3[923/1200]  | LR: 0.006291 | E:  -60.092075 | E_var:     4.6796 | E_err:   0.024276
[2025-11-01 04:24:19] [Iter 2087/2362] R3[924/1200]  | LR: 0.006248 | E:  -60.139977 | E_var:     4.7525 | E_err:   0.025448
[2025-11-01 04:24:36] [Iter 2088/2362] R3[925/1200]  | LR: 0.006205 | E:  -60.121455 | E_var:     4.7659 | E_err:   0.023922
[2025-11-01 04:24:53] [Iter 2089/2362] R3[926/1200]  | LR: 0.006162 | E:  -60.155735 | E_var:     4.8422 | E_err:   0.024844
[2025-11-01 04:25:10] [Iter 2090/2362] R3[927/1200]  | LR: 0.006119 | E:  -60.128832 | E_var:     4.8316 | E_err:   0.025424
[2025-11-01 04:25:28] [Iter 2091/2362] R3[928/1200]  | LR: 0.006076 | E:  -60.116560 | E_var:     4.6757 | E_err:   0.023350
[2025-11-01 04:25:45] [Iter 2092/2362] R3[929/1200]  | LR: 0.006033 | E:  -60.065328 | E_var:     4.8590 | E_err:   0.024685
[2025-11-01 04:26:02] [Iter 2093/2362] R3[930/1200]  | LR: 0.005991 | E:  -60.143449 | E_var:     4.9103 | E_err:   0.025134
[2025-11-01 04:26:19] [Iter 2094/2362] R3[931/1200]  | LR: 0.005948 | E:  -60.137674 | E_var:     4.8321 | E_err:   0.024585
[2025-11-01 04:26:36] [Iter 2095/2362] R3[932/1200]  | LR: 0.005906 | E:  -60.153616 | E_var:     4.7333 | E_err:   0.024438
[2025-11-01 04:26:54] [Iter 2096/2362] R3[933/1200]  | LR: 0.005864 | E:  -60.095388 | E_var:     5.0927 | E_err:   0.025946
[2025-11-01 04:27:11] [Iter 2097/2362] R3[934/1200]  | LR: 0.005822 | E:  -60.105422 | E_var:     4.7209 | E_err:   0.023689
[2025-11-01 04:27:28] [Iter 2098/2362] R3[935/1200]  | LR: 0.005780 | E:  -60.107482 | E_var:     4.7478 | E_err:   0.023875
[2025-11-01 04:27:45] [Iter 2099/2362] R3[936/1200]  | LR: 0.005738 | E:  -60.057758 | E_var:     4.5348 | E_err:   0.024354
[2025-11-01 04:28:03] [Iter 2100/2362] R3[937/1200]  | LR: 0.005696 | E:  -60.106516 | E_var:     4.6701 | E_err:   0.023619
[2025-11-01 04:28:20] [Iter 2101/2362] R3[938/1200]  | LR: 0.005655 | E:  -60.103650 | E_var:     4.5640 | E_err:   0.024284
[2025-11-01 04:28:37] [Iter 2102/2362] R3[939/1200]  | LR: 0.005613 | E:  -60.037688 | E_var:     4.6518 | E_err:   0.024638
[2025-11-01 04:28:54] [Iter 2103/2362] R3[940/1200]  | LR: 0.005572 | E:  -60.044064 | E_var:     5.2494 | E_err:   0.026308
[2025-11-01 04:29:11] [Iter 2104/2362] R3[941/1200]  | LR: 0.005531 | E:  -60.054090 | E_var:     4.8338 | E_err:   0.025162
[2025-11-01 04:29:29] [Iter 2105/2362] R3[942/1200]  | LR: 0.005490 | E:  -60.079662 | E_var:     4.9036 | E_err:   0.024886
[2025-11-01 04:29:46] [Iter 2106/2362] R3[943/1200]  | LR: 0.005449 | E:  -60.137781 | E_var:     4.8051 | E_err:   0.024563
[2025-11-01 04:30:03] [Iter 2107/2362] R3[944/1200]  | LR: 0.005409 | E:  -60.143216 | E_var:     4.8533 | E_err:   0.025320
[2025-11-01 04:30:20] [Iter 2108/2362] R3[945/1200]  | LR: 0.005368 | E:  -60.111995 | E_var:     4.8251 | E_err:   0.024829
[2025-11-01 04:30:37] [Iter 2109/2362] R3[946/1200]  | LR: 0.005328 | E:  -60.129918 | E_var:     4.7250 | E_err:   0.024291
[2025-11-01 04:30:55] [Iter 2110/2362] R3[947/1200]  | LR: 0.005287 | E:  -60.138841 | E_var:     4.6636 | E_err:   0.023998
[2025-11-01 04:31:12] [Iter 2111/2362] R3[948/1200]  | LR: 0.005247 | E:  -60.126943 | E_var:     4.8854 | E_err:   0.025006
[2025-11-01 04:31:29] [Iter 2112/2362] R3[949/1200]  | LR: 0.005207 | E:  -60.143761 | E_var:     5.1076 | E_err:   0.025298
[2025-11-01 04:31:29] ✓ Checkpoint saved: checkpoint_iter_002000.pkl
[2025-11-01 04:31:46] [Iter 2113/2362] R3[950/1200]  | LR: 0.005167 | E:  -60.091394 | E_var:     4.6634 | E_err:   0.024266
[2025-11-01 04:32:04] [Iter 2114/2362] R3[951/1200]  | LR: 0.005127 | E:  -60.116009 | E_var:     4.8042 | E_err:   0.024463
[2025-11-01 04:32:21] [Iter 2115/2362] R3[952/1200]  | LR: 0.005088 | E:  -60.109515 | E_var:     4.9377 | E_err:   0.025089
[2025-11-01 04:32:38] [Iter 2116/2362] R3[953/1200]  | LR: 0.005048 | E:  -60.127714 | E_var:     4.7718 | E_err:   0.024332
[2025-11-01 04:32:55] [Iter 2117/2362] R3[954/1200]  | LR: 0.005009 | E:  -60.138917 | E_var:     4.7605 | E_err:   0.024661
[2025-11-01 04:33:13] [Iter 2118/2362] R3[955/1200]  | LR: 0.004970 | E:  -60.128116 | E_var:     4.7309 | E_err:   0.024418
[2025-11-01 04:33:30] [Iter 2119/2362] R3[956/1200]  | LR: 0.004930 | E:  -60.158890 | E_var:     4.7535 | E_err:   0.024360
[2025-11-01 04:33:47] [Iter 2120/2362] R3[957/1200]  | LR: 0.004892 | E:  -60.137936 | E_var:     4.7498 | E_err:   0.024787
[2025-11-01 04:34:04] [Iter 2121/2362] R3[958/1200]  | LR: 0.004853 | E:  -60.094145 | E_var:     4.8135 | E_err:   0.024134
[2025-11-01 04:34:21] [Iter 2122/2362] R3[959/1200]  | LR: 0.004814 | E:  -60.087323 | E_var:     4.7336 | E_err:   0.024344
[2025-11-01 04:34:39] [Iter 2123/2362] R3[960/1200]  | LR: 0.004775 | E:  -60.114944 | E_var:     4.7087 | E_err:   0.024049
[2025-11-01 04:34:56] [Iter 2124/2362] R3[961/1200]  | LR: 0.004737 | E:  -60.132656 | E_var:     4.9281 | E_err:   0.026529
[2025-11-01 04:35:13] [Iter 2125/2362] R3[962/1200]  | LR: 0.004699 | E:  -60.136167 | E_var:     4.9536 | E_err:   0.025538
[2025-11-01 04:35:30] [Iter 2126/2362] R3[963/1200]  | LR: 0.004661 | E:  -60.110538 | E_var:     4.8967 | E_err:   0.024876
[2025-11-01 04:35:48] [Iter 2127/2362] R3[964/1200]  | LR: 0.004623 | E:  -60.140016 | E_var:     4.6784 | E_err:   0.024756
[2025-11-01 04:36:05] [Iter 2128/2362] R3[965/1200]  | LR: 0.004585 | E:  -60.147130 | E_var:     4.9656 | E_err:   0.024466
[2025-11-01 04:36:22] [Iter 2129/2362] R3[966/1200]  | LR: 0.004547 | E:  -60.120885 | E_var:     4.6672 | E_err:   0.025107
[2025-11-01 04:36:39] [Iter 2130/2362] R3[967/1200]  | LR: 0.004510 | E:  -60.126736 | E_var:     4.9180 | E_err:   0.024644
[2025-11-01 04:36:56] [Iter 2131/2362] R3[968/1200]  | LR: 0.004472 | E:  -60.143200 | E_var:     4.6655 | E_err:   0.024682
[2025-11-01 04:37:14] [Iter 2132/2362] R3[969/1200]  | LR: 0.004435 | E:  -60.128057 | E_var:     5.0127 | E_err:   0.024900
[2025-11-01 04:37:31] [Iter 2133/2362] R3[970/1200]  | LR: 0.004398 | E:  -60.108496 | E_var:     4.9221 | E_err:   0.025541
[2025-11-01 04:37:48] [Iter 2134/2362] R3[971/1200]  | LR: 0.004361 | E:  -60.112941 | E_var:     4.8730 | E_err:   0.024641
[2025-11-01 04:38:05] [Iter 2135/2362] R3[972/1200]  | LR: 0.004324 | E:  -60.160283 | E_var:     4.7689 | E_err:   0.024031
[2025-11-01 04:38:22] [Iter 2136/2362] R3[973/1200]  | LR: 0.004287 | E:  -60.172533 | E_var:     4.7942 | E_err:   0.025060
[2025-11-01 04:38:40] [Iter 2137/2362] R3[974/1200]  | LR: 0.004251 | E:  -60.156879 | E_var:     4.6557 | E_err:   0.024377
[2025-11-01 04:38:57] [Iter 2138/2362] R3[975/1200]  | LR: 0.004214 | E:  -60.142322 | E_var:     4.5754 | E_err:   0.024328
[2025-11-01 04:39:14] [Iter 2139/2362] R3[976/1200]  | LR: 0.004178 | E:  -60.142800 | E_var:     4.6615 | E_err:   0.024424
[2025-11-01 04:39:31] [Iter 2140/2362] R3[977/1200]  | LR: 0.004142 | E:  -60.087309 | E_var:     4.6241 | E_err:   0.024138
[2025-11-01 04:39:49] [Iter 2141/2362] R3[978/1200]  | LR: 0.004106 | E:  -60.159725 | E_var:     4.7943 | E_err:   0.023874
[2025-11-01 04:40:06] [Iter 2142/2362] R3[979/1200]  | LR: 0.004070 | E:  -60.083810 | E_var:     4.6741 | E_err:   0.024286
[2025-11-01 04:40:23] [Iter 2143/2362] R3[980/1200]  | LR: 0.004034 | E:  -60.119879 | E_var:     4.6397 | E_err:   0.024770
[2025-11-01 04:40:40] [Iter 2144/2362] R3[981/1200]  | LR: 0.003999 | E:  -60.115170 | E_var:     4.6989 | E_err:   0.024983
[2025-11-01 04:40:57] [Iter 2145/2362] R3[982/1200]  | LR: 0.003963 | E:  -60.164769 | E_var:     4.8412 | E_err:   0.024262
[2025-11-01 04:41:15] [Iter 2146/2362] R3[983/1200]  | LR: 0.003928 | E:  -60.137381 | E_var:     5.2490 | E_err:   0.026507
[2025-11-01 04:41:32] [Iter 2147/2362] R3[984/1200]  | LR: 0.003893 | E:  -60.130095 | E_var:     5.0361 | E_err:   0.025615
[2025-11-01 04:41:49] [Iter 2148/2362] R3[985/1200]  | LR: 0.003858 | E:  -60.150017 | E_var:     4.7592 | E_err:   0.024885
[2025-11-01 04:42:06] [Iter 2149/2362] R3[986/1200]  | LR: 0.003823 | E:  -60.102958 | E_var:     4.9724 | E_err:   0.024719
[2025-11-01 04:42:23] [Iter 2150/2362] R3[987/1200]  | LR: 0.003788 | E:  -60.110239 | E_var:     5.1449 | E_err:   0.025065
[2025-11-01 04:42:41] [Iter 2151/2362] R3[988/1200]  | LR: 0.003754 | E:  -60.155014 | E_var:     4.7570 | E_err:   0.023750
[2025-11-01 04:42:58] [Iter 2152/2362] R3[989/1200]  | LR: 0.003719 | E:  -60.161197 | E_var:     4.8394 | E_err:   0.024055
[2025-11-01 04:43:15] [Iter 2153/2362] R3[990/1200]  | LR: 0.003685 | E:  -60.166444 | E_var:     4.8377 | E_err:   0.025548
[2025-11-01 04:43:32] [Iter 2154/2362] R3[991/1200]  | LR: 0.003651 | E:  -60.173603 | E_var:     4.7828 | E_err:   0.024553
[2025-11-01 04:43:50] [Iter 2155/2362] R3[992/1200]  | LR: 0.003617 | E:  -60.157650 | E_var:     6.4780 | E_err:   0.029476
[2025-11-01 04:44:07] [Iter 2156/2362] R3[993/1200]  | LR: 0.003583 | E:  -60.190793 | E_var:     4.8114 | E_err:   0.025362
[2025-11-01 04:44:24] [Iter 2157/2362] R3[994/1200]  | LR: 0.003549 | E:  -60.166153 | E_var:     4.7905 | E_err:   0.025171
[2025-11-01 04:44:41] [Iter 2158/2362] R3[995/1200]  | LR: 0.003516 | E:  -60.135815 | E_var:     4.7822 | E_err:   0.024264
[2025-11-01 04:44:58] [Iter 2159/2362] R3[996/1200]  | LR: 0.003482 | E:  -60.099078 | E_var:     4.5931 | E_err:   0.024079
[2025-11-01 04:45:16] [Iter 2160/2362] R3[997/1200]  | LR: 0.003449 | E:  -60.127168 | E_var:     4.6876 | E_err:   0.024112
[2025-11-01 04:45:33] [Iter 2161/2362] R3[998/1200]  | LR: 0.003416 | E:  -60.148954 | E_var:     4.6830 | E_err:   0.024126
[2025-11-01 04:45:50] [Iter 2162/2362] R3[999/1200]  | LR: 0.003383 | E:  -60.122092 | E_var:     4.8002 | E_err:   0.023989
[2025-11-01 04:46:07] [Iter 2163/2362] R3[1000/1200] | LR: 0.003350 | E:  -60.147983 | E_var:     4.7884 | E_err:   0.024229
[2025-11-01 04:46:24] [Iter 2164/2362] R3[1001/1200] | LR: 0.003318 | E:  -60.167713 | E_var:     4.9103 | E_err:   0.024670
[2025-11-01 04:46:42] [Iter 2165/2362] R3[1002/1200] | LR: 0.003285 | E:  -60.144276 | E_var:     4.7357 | E_err:   0.024410
[2025-11-01 04:46:59] [Iter 2166/2362] R3[1003/1200] | LR: 0.003253 | E:  -60.108355 | E_var:     4.5583 | E_err:   0.023945
[2025-11-01 04:47:16] [Iter 2167/2362] R3[1004/1200] | LR: 0.003221 | E:  -60.068447 | E_var:     4.4849 | E_err:   0.023375
[2025-11-01 04:47:33] [Iter 2168/2362] R3[1005/1200] | LR: 0.003189 | E:  -60.164730 | E_var:     4.5585 | E_err:   0.023794
[2025-11-01 04:47:51] [Iter 2169/2362] R3[1006/1200] | LR: 0.003157 | E:  -60.144858 | E_var:     4.5332 | E_err:   0.024562
[2025-11-01 04:48:08] [Iter 2170/2362] R3[1007/1200] | LR: 0.003125 | E:  -60.111333 | E_var:     4.6198 | E_err:   0.024175
[2025-11-01 04:48:25] [Iter 2171/2362] R3[1008/1200] | LR: 0.003093 | E:  -60.093563 | E_var:     4.5221 | E_err:   0.024465
[2025-11-01 04:48:42] [Iter 2172/2362] R3[1009/1200] | LR: 0.003062 | E:  -60.148414 | E_var:     4.5342 | E_err:   0.023655
[2025-11-01 04:48:59] [Iter 2173/2362] R3[1010/1200] | LR: 0.003031 | E:  -60.093515 | E_var:     4.6976 | E_err:   0.024669
[2025-11-01 04:49:17] [Iter 2174/2362] R3[1011/1200] | LR: 0.002999 | E:  -60.113254 | E_var:     4.7397 | E_err:   0.024026
[2025-11-01 04:49:34] [Iter 2175/2362] R3[1012/1200] | LR: 0.002968 | E:  -60.083990 | E_var:     4.4871 | E_err:   0.024025
[2025-11-01 04:49:51] [Iter 2176/2362] R3[1013/1200] | LR: 0.002938 | E:  -60.109860 | E_var:     4.7895 | E_err:   0.023935
[2025-11-01 04:50:08] [Iter 2177/2362] R3[1014/1200] | LR: 0.002907 | E:  -60.099012 | E_var:     4.5898 | E_err:   0.024718
[2025-11-01 04:50:25] [Iter 2178/2362] R3[1015/1200] | LR: 0.002876 | E:  -60.097996 | E_var:     4.4550 | E_err:   0.024566
[2025-11-01 04:50:43] [Iter 2179/2362] R3[1016/1200] | LR: 0.002846 | E:  -60.138361 | E_var:     4.6989 | E_err:   0.023843
[2025-11-01 04:51:00] [Iter 2180/2362] R3[1017/1200] | LR: 0.002816 | E:  -60.122501 | E_var:     4.6147 | E_err:   0.023740
[2025-11-01 04:51:17] [Iter 2181/2362] R3[1018/1200] | LR: 0.002786 | E:  -60.069236 | E_var:     4.6469 | E_err:   0.024219
[2025-11-01 04:51:34] [Iter 2182/2362] R3[1019/1200] | LR: 0.002756 | E:  -60.144480 | E_var:     4.5406 | E_err:   0.023810
[2025-11-01 04:51:52] [Iter 2183/2362] R3[1020/1200] | LR: 0.002726 | E:  -60.133952 | E_var:     4.6181 | E_err:   0.025082
[2025-11-01 04:52:09] [Iter 2184/2362] R3[1021/1200] | LR: 0.002696 | E:  -60.074972 | E_var:     4.6276 | E_err:   0.023490
[2025-11-01 04:52:26] [Iter 2185/2362] R3[1022/1200] | LR: 0.002667 | E:  -60.118736 | E_var:     4.6042 | E_err:   0.024498
[2025-11-01 04:52:43] [Iter 2186/2362] R3[1023/1200] | LR: 0.002637 | E:  -60.107638 | E_var:     4.7606 | E_err:   0.025010
[2025-11-01 04:53:00] [Iter 2187/2362] R3[1024/1200] | LR: 0.002608 | E:  -60.161238 | E_var:     4.9741 | E_err:   0.024650
[2025-11-01 04:53:18] [Iter 2188/2362] R3[1025/1200] | LR: 0.002579 | E:  -60.092240 | E_var:     4.7534 | E_err:   0.024234
[2025-11-01 04:53:35] [Iter 2189/2362] R3[1026/1200] | LR: 0.002550 | E:  -60.111865 | E_var:     4.8049 | E_err:   0.024763
[2025-11-01 04:53:52] [Iter 2190/2362] R3[1027/1200] | LR: 0.002522 | E:  -60.188110 | E_var:     4.7678 | E_err:   0.024939
[2025-11-01 04:54:09] [Iter 2191/2362] R3[1028/1200] | LR: 0.002493 | E:  -60.174636 | E_var:     4.5933 | E_err:   0.023418
[2025-11-01 04:54:26] [Iter 2192/2362] R3[1029/1200] | LR: 0.002465 | E:  -60.190757 | E_var:     4.6265 | E_err:   0.023823
[2025-11-01 04:54:44] [Iter 2193/2362] R3[1030/1200] | LR: 0.002436 | E:  -60.161605 | E_var:     4.7693 | E_err:   0.023932
[2025-11-01 04:55:01] [Iter 2194/2362] R3[1031/1200] | LR: 0.002408 | E:  -60.205279 | E_var:     4.9500 | E_err:   0.025094
[2025-11-01 04:55:18] [Iter 2195/2362] R3[1032/1200] | LR: 0.002380 | E:  -60.129694 | E_var:     4.7582 | E_err:   0.024930
[2025-11-01 04:55:35] [Iter 2196/2362] R3[1033/1200] | LR: 0.002352 | E:  -60.131175 | E_var:     4.9009 | E_err:   0.024354
[2025-11-01 04:55:53] [Iter 2197/2362] R3[1034/1200] | LR: 0.002325 | E:  -60.158307 | E_var:     4.9308 | E_err:   0.025551
[2025-11-01 04:56:10] [Iter 2198/2362] R3[1035/1200] | LR: 0.002297 | E:  -60.144546 | E_var:     5.0006 | E_err:   0.025541
[2025-11-01 04:56:27] [Iter 2199/2362] R3[1036/1200] | LR: 0.002270 | E:  -60.157019 | E_var:     4.5929 | E_err:   0.024295
[2025-11-01 04:56:44] [Iter 2200/2362] R3[1037/1200] | LR: 0.002243 | E:  -60.114243 | E_var:     4.7010 | E_err:   0.024625
[2025-11-01 04:57:01] [Iter 2201/2362] R3[1038/1200] | LR: 0.002216 | E:  -60.131191 | E_var:     4.5767 | E_err:   0.024052
[2025-11-01 04:57:19] [Iter 2202/2362] R3[1039/1200] | LR: 0.002189 | E:  -60.127542 | E_var:     4.5749 | E_err:   0.023412
[2025-11-01 04:57:36] [Iter 2203/2362] R3[1040/1200] | LR: 0.002162 | E:  -60.111265 | E_var:     4.6071 | E_err:   0.023369
[2025-11-01 04:57:53] [Iter 2204/2362] R3[1041/1200] | LR: 0.002136 | E:  -60.122334 | E_var:     4.7414 | E_err:   0.025217
[2025-11-01 04:58:10] [Iter 2205/2362] R3[1042/1200] | LR: 0.002109 | E:  -60.102951 | E_var:     4.6291 | E_err:   0.023592
[2025-11-01 04:58:27] [Iter 2206/2362] R3[1043/1200] | LR: 0.002083 | E:  -60.170920 | E_var:     4.6577 | E_err:   0.024260
[2025-11-01 04:58:45] [Iter 2207/2362] R3[1044/1200] | LR: 0.002057 | E:  -60.129772 | E_var:     4.8320 | E_err:   0.024752
[2025-11-01 04:59:02] [Iter 2208/2362] R3[1045/1200] | LR: 0.002031 | E:  -60.150333 | E_var:     4.8154 | E_err:   0.023741
[2025-11-01 04:59:19] [Iter 2209/2362] R3[1046/1200] | LR: 0.002005 | E:  -60.115106 | E_var:     4.7947 | E_err:   0.025394
[2025-11-01 04:59:36] [Iter 2210/2362] R3[1047/1200] | LR: 0.001980 | E:  -60.176095 | E_var:     4.5411 | E_err:   0.024119
[2025-11-01 04:59:54] [Iter 2211/2362] R3[1048/1200] | LR: 0.001954 | E:  -60.174538 | E_var:     4.7676 | E_err:   0.023343
[2025-11-01 05:00:11] [Iter 2212/2362] R3[1049/1200] | LR: 0.001929 | E:  -60.169619 | E_var:     4.6742 | E_err:   0.023817
[2025-11-01 05:00:28] [Iter 2213/2362] R3[1050/1200] | LR: 0.001904 | E:  -60.138876 | E_var:     4.4923 | E_err:   0.023676
[2025-11-01 05:00:45] [Iter 2214/2362] R3[1051/1200] | LR: 0.001879 | E:  -60.142068 | E_var:     4.5235 | E_err:   0.024505
[2025-11-01 05:01:02] [Iter 2215/2362] R3[1052/1200] | LR: 0.001854 | E:  -60.110694 | E_var:     4.9200 | E_err:   0.025362
[2025-11-01 05:01:20] [Iter 2216/2362] R3[1053/1200] | LR: 0.001830 | E:  -60.162734 | E_var:     4.8011 | E_err:   0.024743
[2025-11-01 05:01:37] [Iter 2217/2362] R3[1054/1200] | LR: 0.001805 | E:  -60.149770 | E_var:     4.8947 | E_err:   0.024467
[2025-11-01 05:01:54] [Iter 2218/2362] R3[1055/1200] | LR: 0.001781 | E:  -60.150975 | E_var:     4.8415 | E_err:   0.024673
[2025-11-01 05:02:11] [Iter 2219/2362] R3[1056/1200] | LR: 0.001757 | E:  -60.128494 | E_var:     4.7035 | E_err:   0.024773
[2025-11-01 05:02:28] [Iter 2220/2362] R3[1057/1200] | LR: 0.001733 | E:  -60.176939 | E_var:     4.7790 | E_err:   0.024614
[2025-11-01 05:02:46] [Iter 2221/2362] R3[1058/1200] | LR: 0.001709 | E:  -60.142957 | E_var:     4.7208 | E_err:   0.023434
[2025-11-01 05:03:03] [Iter 2222/2362] R3[1059/1200] | LR: 0.001685 | E:  -60.161330 | E_var:     4.7914 | E_err:   0.025238
[2025-11-01 05:03:20] [Iter 2223/2362] R3[1060/1200] | LR: 0.001661 | E:  -60.117715 | E_var:     4.6597 | E_err:   0.024541
[2025-11-01 05:03:37] [Iter 2224/2362] R3[1061/1200] | LR: 0.001638 | E:  -60.130743 | E_var:     4.8637 | E_err:   0.024559
[2025-11-01 05:03:54] [Iter 2225/2362] R3[1062/1200] | LR: 0.001615 | E:  -60.142157 | E_var:     4.8330 | E_err:   0.024885
[2025-11-01 05:04:12] [Iter 2226/2362] R3[1063/1200] | LR: 0.001592 | E:  -60.143257 | E_var:     4.8339 | E_err:   0.024233
[2025-11-01 05:04:29] [Iter 2227/2362] R3[1064/1200] | LR: 0.001569 | E:  -60.138049 | E_var:     4.9019 | E_err:   0.024916
[2025-11-01 05:04:46] [Iter 2228/2362] R3[1065/1200] | LR: 0.001546 | E:  -60.177514 | E_var:     4.6525 | E_err:   0.024444
[2025-11-01 05:05:03] [Iter 2229/2362] R3[1066/1200] | LR: 0.001524 | E:  -60.144537 | E_var:     4.8255 | E_err:   0.024278
[2025-11-01 05:05:21] [Iter 2230/2362] R3[1067/1200] | LR: 0.001501 | E:  -60.119215 | E_var:     4.5455 | E_err:   0.025048
[2025-11-01 05:05:38] [Iter 2231/2362] R3[1068/1200] | LR: 0.001479 | E:  -60.112797 | E_var:     4.5995 | E_err:   0.025186
[2025-11-01 05:05:55] [Iter 2232/2362] R3[1069/1200] | LR: 0.001457 | E:  -60.122526 | E_var:     4.6336 | E_err:   0.024669
[2025-11-01 05:06:12] [Iter 2233/2362] R3[1070/1200] | LR: 0.001435 | E:  -60.138877 | E_var:     4.6276 | E_err:   0.024658
[2025-11-01 05:06:29] [Iter 2234/2362] R3[1071/1200] | LR: 0.001413 | E:  -60.131298 | E_var:     4.5977 | E_err:   0.024127
[2025-11-01 05:06:47] [Iter 2235/2362] R3[1072/1200] | LR: 0.001392 | E:  -60.114667 | E_var:     4.5311 | E_err:   0.023764
[2025-11-01 05:07:04] [Iter 2236/2362] R3[1073/1200] | LR: 0.001370 | E:  -60.152583 | E_var:     4.6743 | E_err:   0.024931
[2025-11-01 05:07:21] [Iter 2237/2362] R3[1074/1200] | LR: 0.001349 | E:  -60.154888 | E_var:     4.7763 | E_err:   0.024744
[2025-11-01 05:07:38] [Iter 2238/2362] R3[1075/1200] | LR: 0.001328 | E:  -60.127559 | E_var:     4.7812 | E_err:   0.024380
[2025-11-01 05:07:56] [Iter 2239/2362] R3[1076/1200] | LR: 0.001307 | E:  -60.141347 | E_var:     4.6112 | E_err:   0.023912
[2025-11-01 05:08:13] [Iter 2240/2362] R3[1077/1200] | LR: 0.001286 | E:  -60.139035 | E_var:     4.6413 | E_err:   0.024986
[2025-11-01 05:08:30] [Iter 2241/2362] R3[1078/1200] | LR: 0.001265 | E:  -60.120070 | E_var:     4.8280 | E_err:   0.024385
[2025-11-01 05:08:47] [Iter 2242/2362] R3[1079/1200] | LR: 0.001245 | E:  -60.102808 | E_var:     4.7915 | E_err:   0.024434
[2025-11-01 05:09:04] [Iter 2243/2362] R3[1080/1200] | LR: 0.001225 | E:  -60.193408 | E_var:     4.7824 | E_err:   0.024047
[2025-11-01 05:09:22] [Iter 2244/2362] R3[1081/1200] | LR: 0.001204 | E:  -60.177000 | E_var:     4.7740 | E_err:   0.024586
[2025-11-01 05:09:39] [Iter 2245/2362] R3[1082/1200] | LR: 0.001184 | E:  -60.171905 | E_var:     4.6252 | E_err:   0.023997
[2025-11-01 05:09:56] [Iter 2246/2362] R3[1083/1200] | LR: 0.001165 | E:  -60.114577 | E_var:     4.6110 | E_err:   0.023959
[2025-11-01 05:10:13] [Iter 2247/2362] R3[1084/1200] | LR: 0.001145 | E:  -60.157071 | E_var:     4.7245 | E_err:   0.024445
[2025-11-01 05:10:30] [Iter 2248/2362] R3[1085/1200] | LR: 0.001125 | E:  -60.183865 | E_var:     4.6356 | E_err:   0.023884
[2025-11-01 05:10:48] [Iter 2249/2362] R3[1086/1200] | LR: 0.001106 | E:  -60.161996 | E_var:     4.8901 | E_err:   0.024704
[2025-11-01 05:11:05] [Iter 2250/2362] R3[1087/1200] | LR: 0.001087 | E:  -60.142064 | E_var:     5.1214 | E_err:   0.026949
[2025-11-01 05:11:22] [Iter 2251/2362] R3[1088/1200] | LR: 0.001068 | E:  -60.168515 | E_var:     5.1743 | E_err:   0.025681
[2025-11-01 05:11:39] [Iter 2252/2362] R3[1089/1200] | LR: 0.001049 | E:  -60.157644 | E_var:     5.1184 | E_err:   0.025152
[2025-11-01 05:11:57] [Iter 2253/2362] R3[1090/1200] | LR: 0.001030 | E:  -60.121543 | E_var:     4.8231 | E_err:   0.024580
[2025-11-01 05:12:14] [Iter 2254/2362] R3[1091/1200] | LR: 0.001012 | E:  -60.078761 | E_var:     4.8690 | E_err:   0.025013
[2025-11-01 05:12:31] [Iter 2255/2362] R3[1092/1200] | LR: 0.000994 | E:  -60.151768 | E_var:     4.7466 | E_err:   0.024164
[2025-11-01 05:12:48] [Iter 2256/2362] R3[1093/1200] | LR: 0.000975 | E:  -60.167583 | E_var:     4.8713 | E_err:   0.024314
[2025-11-01 05:13:05] [Iter 2257/2362] R3[1094/1200] | LR: 0.000957 | E:  -60.149685 | E_var:     4.8491 | E_err:   0.024974
[2025-11-01 05:13:23] [Iter 2258/2362] R3[1095/1200] | LR: 0.000940 | E:  -60.153710 | E_var:     4.8280 | E_err:   0.024323
[2025-11-01 05:13:40] [Iter 2259/2362] R3[1096/1200] | LR: 0.000922 | E:  -60.190716 | E_var:     4.7552 | E_err:   0.024739
[2025-11-01 05:13:57] [Iter 2260/2362] R3[1097/1200] | LR: 0.000904 | E:  -60.166348 | E_var:     4.8245 | E_err:   0.025170
[2025-11-01 05:14:14] [Iter 2261/2362] R3[1098/1200] | LR: 0.000887 | E:  -60.137840 | E_var:     4.7463 | E_err:   0.024547
[2025-11-01 05:14:31] [Iter 2262/2362] R3[1099/1200] | LR: 0.000870 | E:  -60.159593 | E_var:     4.7123 | E_err:   0.024344
[2025-11-01 05:14:49] [Iter 2263/2362] R3[1100/1200] | LR: 0.000853 | E:  -60.135547 | E_var:     4.7679 | E_err:   0.024809
[2025-11-01 05:15:06] [Iter 2264/2362] R3[1101/1200] | LR: 0.000836 | E:  -60.196419 | E_var:     4.9122 | E_err:   0.025101
[2025-11-01 05:15:23] [Iter 2265/2362] R3[1102/1200] | LR: 0.000819 | E:  -60.135453 | E_var:     5.1579 | E_err:   0.025960
[2025-11-01 05:15:40] [Iter 2266/2362] R3[1103/1200] | LR: 0.000803 | E:  -60.162488 | E_var:     5.0437 | E_err:   0.024912
[2025-11-01 05:15:58] [Iter 2267/2362] R3[1104/1200] | LR: 0.000786 | E:  -60.174111 | E_var:     4.6838 | E_err:   0.024738
[2025-11-01 05:16:15] [Iter 2268/2362] R3[1105/1200] | LR: 0.000770 | E:  -60.121736 | E_var:     4.8819 | E_err:   0.024953
[2025-11-01 05:16:32] [Iter 2269/2362] R3[1106/1200] | LR: 0.000754 | E:  -60.099883 | E_var:     5.0882 | E_err:   0.025939
[2025-11-01 05:16:49] [Iter 2270/2362] R3[1107/1200] | LR: 0.000738 | E:  -60.127061 | E_var:     4.9196 | E_err:   0.024922
[2025-11-01 05:17:06] [Iter 2271/2362] R3[1108/1200] | LR: 0.000723 | E:  -60.098108 | E_var:     4.9369 | E_err:   0.026127
[2025-11-01 05:17:24] [Iter 2272/2362] R3[1109/1200] | LR: 0.000707 | E:  -60.127779 | E_var:     5.1613 | E_err:   0.024536
[2025-11-01 05:17:41] [Iter 2273/2362] R3[1110/1200] | LR: 0.000692 | E:  -60.085879 | E_var:     4.7798 | E_err:   0.025401
[2025-11-01 05:17:58] [Iter 2274/2362] R3[1111/1200] | LR: 0.000677 | E:  -60.121802 | E_var:     4.9635 | E_err:   0.024715
[2025-11-01 05:18:15] [Iter 2275/2362] R3[1112/1200] | LR: 0.000662 | E:  -60.076575 | E_var:     4.8832 | E_err:   0.024803
[2025-11-01 05:18:33] [Iter 2276/2362] R3[1113/1200] | LR: 0.000647 | E:  -60.155524 | E_var:     4.5691 | E_err:   0.024577
[2025-11-01 05:18:50] [Iter 2277/2362] R3[1114/1200] | LR: 0.000632 | E:  -60.150257 | E_var:     4.5847 | E_err:   0.024280
[2025-11-01 05:19:07] [Iter 2278/2362] R3[1115/1200] | LR: 0.000617 | E:  -60.149640 | E_var:     4.7178 | E_err:   0.025000
[2025-11-01 05:19:24] [Iter 2279/2362] R3[1116/1200] | LR: 0.000603 | E:  -60.125836 | E_var:     4.8767 | E_err:   0.024905
[2025-11-01 05:19:41] [Iter 2280/2362] R3[1117/1200] | LR: 0.000589 | E:  -60.161199 | E_var:     4.7329 | E_err:   0.024190
[2025-11-01 05:19:59] [Iter 2281/2362] R3[1118/1200] | LR: 0.000575 | E:  -60.136311 | E_var:     5.0952 | E_err:   0.026196
[2025-11-01 05:20:16] [Iter 2282/2362] R3[1119/1200] | LR: 0.000561 | E:  -60.150024 | E_var:     4.7716 | E_err:   0.025289
[2025-11-01 05:20:33] [Iter 2283/2362] R3[1120/1200] | LR: 0.000547 | E:  -60.161644 | E_var:     4.8407 | E_err:   0.024384
[2025-11-01 05:20:50] [Iter 2284/2362] R3[1121/1200] | LR: 0.000534 | E:  -60.132698 | E_var:     4.9090 | E_err:   0.026026
[2025-11-01 05:21:07] [Iter 2285/2362] R3[1122/1200] | LR: 0.000520 | E:  -60.163961 | E_var:     4.6651 | E_err:   0.024222
[2025-11-01 05:21:25] [Iter 2286/2362] R3[1123/1200] | LR: 0.000507 | E:  -60.099797 | E_var:     4.6695 | E_err:   0.024945
[2025-11-01 05:21:42] [Iter 2287/2362] R3[1124/1200] | LR: 0.000494 | E:  -60.133044 | E_var:     4.7047 | E_err:   0.025103
[2025-11-01 05:21:59] [Iter 2288/2362] R3[1125/1200] | LR: 0.000481 | E:  -60.127779 | E_var:     4.6908 | E_err:   0.024680
[2025-11-01 05:22:16] [Iter 2289/2362] R3[1126/1200] | LR: 0.000469 | E:  -60.170740 | E_var:     4.7393 | E_err:   0.024315
[2025-11-01 05:22:34] [Iter 2290/2362] R3[1127/1200] | LR: 0.000456 | E:  -60.175062 | E_var:     4.8527 | E_err:   0.024203
[2025-11-01 05:22:51] [Iter 2291/2362] R3[1128/1200] | LR: 0.000444 | E:  -60.169421 | E_var:     4.7517 | E_err:   0.025391
[2025-11-01 05:23:08] [Iter 2292/2362] R3[1129/1200] | LR: 0.000432 | E:  -60.158635 | E_var:     4.8225 | E_err:   0.024218
[2025-11-01 05:23:25] [Iter 2293/2362] R3[1130/1200] | LR: 0.000420 | E:  -60.146515 | E_var:     4.6510 | E_err:   0.024619
[2025-11-01 05:23:42] [Iter 2294/2362] R3[1131/1200] | LR: 0.000408 | E:  -60.149734 | E_var:     4.8824 | E_err:   0.024712
[2025-11-01 05:24:00] [Iter 2295/2362] R3[1132/1200] | LR: 0.000396 | E:  -60.107059 | E_var:     4.5694 | E_err:   0.023423
[2025-11-01 05:24:17] [Iter 2296/2362] R3[1133/1200] | LR: 0.000385 | E:  -60.141766 | E_var:     4.5836 | E_err:   0.024761
[2025-11-01 05:24:34] [Iter 2297/2362] R3[1134/1200] | LR: 0.000373 | E:  -60.149167 | E_var:     4.7075 | E_err:   0.023442
[2025-11-01 05:24:51] [Iter 2298/2362] R3[1135/1200] | LR: 0.000362 | E:  -60.126404 | E_var:     4.5368 | E_err:   0.024323
[2025-11-01 05:25:08] [Iter 2299/2362] R3[1136/1200] | LR: 0.000351 | E:  -60.121196 | E_var:     4.5193 | E_err:   0.023713
[2025-11-01 05:25:26] [Iter 2300/2362] R3[1137/1200] | LR: 0.000340 | E:  -60.199737 | E_var:     4.6237 | E_err:   0.024263
[2025-11-01 05:25:43] [Iter 2301/2362] R3[1138/1200] | LR: 0.000330 | E:  -60.135963 | E_var:     4.6277 | E_err:   0.024968
[2025-11-01 05:26:00] [Iter 2302/2362] R3[1139/1200] | LR: 0.000319 | E:  -60.178060 | E_var:     4.9502 | E_err:   0.024929
[2025-11-01 05:26:17] [Iter 2303/2362] R3[1140/1200] | LR: 0.000309 | E:  -60.180783 | E_var:     4.5820 | E_err:   0.024275
[2025-11-01 05:26:35] [Iter 2304/2362] R3[1141/1200] | LR: 0.000299 | E:  -60.214247 | E_var:     4.8062 | E_err:   0.024480
[2025-11-01 05:26:52] [Iter 2305/2362] R3[1142/1200] | LR: 0.000289 | E:  -60.161241 | E_var:     4.7494 | E_err:   0.024340
[2025-11-01 05:27:09] [Iter 2306/2362] R3[1143/1200] | LR: 0.000279 | E:  -60.155000 | E_var:     4.6546 | E_err:   0.024482
[2025-11-01 05:27:26] [Iter 2307/2362] R3[1144/1200] | LR: 0.000269 | E:  -60.165649 | E_var:     4.7454 | E_err:   0.024099
[2025-11-01 05:27:43] [Iter 2308/2362] R3[1145/1200] | LR: 0.000260 | E:  -60.164549 | E_var:     5.1152 | E_err:   0.024984
[2025-11-01 05:28:01] [Iter 2309/2362] R3[1146/1200] | LR: 0.000250 | E:  -60.141562 | E_var:     4.6834 | E_err:   0.024634
[2025-11-01 05:28:18] [Iter 2310/2362] R3[1147/1200] | LR: 0.000241 | E:  -60.189948 | E_var:     4.5458 | E_err:   0.024114
[2025-11-01 05:28:35] [Iter 2311/2362] R3[1148/1200] | LR: 0.000232 | E:  -60.191355 | E_var:     4.6409 | E_err:   0.023344
[2025-11-01 05:28:52] [Iter 2312/2362] R3[1149/1200] | LR: 0.000224 | E:  -60.140762 | E_var:     4.6733 | E_err:   0.024152
[2025-11-01 05:29:09] [Iter 2313/2362] R3[1150/1200] | LR: 0.000215 | E:  -60.195580 | E_var:     4.7040 | E_err:   0.024734
[2025-11-01 05:29:27] [Iter 2314/2362] R3[1151/1200] | LR: 0.000206 | E:  -60.157460 | E_var:     4.6958 | E_err:   0.024989
[2025-11-01 05:29:44] [Iter 2315/2362] R3[1152/1200] | LR: 0.000198 | E:  -60.151951 | E_var:     4.7154 | E_err:   0.024054
[2025-11-01 05:30:01] [Iter 2316/2362] R3[1153/1200] | LR: 0.000190 | E:  -60.185702 | E_var:     4.8819 | E_err:   0.024124
[2025-11-01 05:30:18] [Iter 2317/2362] R3[1154/1200] | LR: 0.000182 | E:  -60.192479 | E_var:     4.5131 | E_err:   0.023798
[2025-11-01 05:30:35] [Iter 2318/2362] R3[1155/1200] | LR: 0.000174 | E:  -60.174277 | E_var:     4.6082 | E_err:   0.024031
[2025-11-01 05:30:53] [Iter 2319/2362] R3[1156/1200] | LR: 0.000167 | E:  -60.183804 | E_var:     4.5912 | E_err:   0.024082
[2025-11-01 05:31:10] [Iter 2320/2362] R3[1157/1200] | LR: 0.000159 | E:  -60.180080 | E_var:     4.6882 | E_err:   0.023589
[2025-11-01 05:31:27] [Iter 2321/2362] R3[1158/1200] | LR: 0.000152 | E:  -60.154042 | E_var:     4.6828 | E_err:   0.024026
[2025-11-01 05:31:44] [Iter 2322/2362] R3[1159/1200] | LR: 0.000145 | E:  -60.160425 | E_var:     4.6652 | E_err:   0.023956
[2025-11-01 05:32:02] [Iter 2323/2362] R3[1160/1200] | LR: 0.000138 | E:  -60.172613 | E_var:     4.6717 | E_err:   0.023132
[2025-11-01 05:32:19] [Iter 2324/2362] R3[1161/1200] | LR: 0.000131 | E:  -60.185614 | E_var:     4.5955 | E_err:   0.023589
[2025-11-01 05:32:36] [Iter 2325/2362] R3[1162/1200] | LR: 0.000125 | E:  -60.186880 | E_var:     4.6464 | E_err:   0.023827
[2025-11-01 05:32:53] [Iter 2326/2362] R3[1163/1200] | LR: 0.000118 | E:  -60.152451 | E_var:     4.8311 | E_err:   0.024834
[2025-11-01 05:33:11] [Iter 2327/2362] R3[1164/1200] | LR: 0.000112 | E:  -60.175404 | E_var:     4.8486 | E_err:   0.024564
[2025-11-01 05:33:28] [Iter 2328/2362] R3[1165/1200] | LR: 0.000106 | E:  -60.157465 | E_var:     4.7265 | E_err:   0.024052
[2025-11-01 05:33:45] [Iter 2329/2362] R3[1166/1200] | LR: 0.000100 | E:  -60.188879 | E_var:     4.7247 | E_err:   0.024248
[2025-11-01 05:34:02] [Iter 2330/2362] R3[1167/1200] | LR: 0.000094 | E:  -60.161749 | E_var:     4.7817 | E_err:   0.024630
[2025-11-01 05:34:19] [Iter 2331/2362] R3[1168/1200] | LR: 0.000089 | E:  -60.181311 | E_var:     4.9949 | E_err:   0.025225
[2025-11-01 05:34:37] [Iter 2332/2362] R3[1169/1200] | LR: 0.000083 | E:  -60.121291 | E_var:     4.8409 | E_err:   0.025176
[2025-11-01 05:34:54] [Iter 2333/2362] R3[1170/1200] | LR: 0.000078 | E:  -60.158467 | E_var:     4.7779 | E_err:   0.024815
[2025-11-01 05:35:11] [Iter 2334/2362] R3[1171/1200] | LR: 0.000073 | E:  -60.155114 | E_var:     4.5737 | E_err:   0.024867
[2025-11-01 05:35:28] [Iter 2335/2362] R3[1172/1200] | LR: 0.000068 | E:  -60.155449 | E_var:     4.6844 | E_err:   0.023982
[2025-11-01 05:35:45] [Iter 2336/2362] R3[1173/1200] | LR: 0.000063 | E:  -60.210372 | E_var:     4.4777 | E_err:   0.023641
[2025-11-01 05:36:03] [Iter 2337/2362] R3[1174/1200] | LR: 0.000059 | E:  -60.150077 | E_var:     4.6423 | E_err:   0.024111
[2025-11-01 05:36:20] [Iter 2338/2362] R3[1175/1200] | LR: 0.000055 | E:  -60.189221 | E_var:     4.5833 | E_err:   0.023958
[2025-11-01 05:36:37] [Iter 2339/2362] R3[1176/1200] | LR: 0.000050 | E:  -60.128365 | E_var:     4.5191 | E_err:   0.023346
[2025-11-01 05:36:54] [Iter 2340/2362] R3[1177/1200] | LR: 0.000046 | E:  -60.119049 | E_var:     4.3815 | E_err:   0.024480
[2025-11-01 05:37:12] [Iter 2341/2362] R3[1178/1200] | LR: 0.000042 | E:  -60.162399 | E_var:     4.4812 | E_err:   0.023778
[2025-11-01 05:37:29] [Iter 2342/2362] R3[1179/1200] | LR: 0.000039 | E:  -60.154178 | E_var:     4.8165 | E_err:   0.024582
[2025-11-01 05:37:46] [Iter 2343/2362] R3[1180/1200] | LR: 0.000035 | E:  -60.203586 | E_var:     4.7384 | E_err:   0.023861
[2025-11-01 05:38:03] [Iter 2344/2362] R3[1181/1200] | LR: 0.000032 | E:  -60.173672 | E_var:     4.5751 | E_err:   0.024202
[2025-11-01 05:38:20] [Iter 2345/2362] R3[1182/1200] | LR: 0.000029 | E:  -60.143999 | E_var:     4.4403 | E_err:   0.024499
[2025-11-01 05:38:38] [Iter 2346/2362] R3[1183/1200] | LR: 0.000026 | E:  -60.174821 | E_var:     4.6894 | E_err:   0.024300
[2025-11-01 05:38:55] [Iter 2347/2362] R3[1184/1200] | LR: 0.000023 | E:  -60.131030 | E_var:     4.4026 | E_err:   0.023397
[2025-11-01 05:39:12] [Iter 2348/2362] R3[1185/1200] | LR: 0.000020 | E:  -60.141940 | E_var:     4.4750 | E_err:   0.025104
[2025-11-01 05:39:29] [Iter 2349/2362] R3[1186/1200] | LR: 0.000018 | E:  -60.177703 | E_var:     4.5740 | E_err:   0.023961
[2025-11-01 05:39:46] [Iter 2350/2362] R3[1187/1200] | LR: 0.000015 | E:  -60.134117 | E_var:     4.4628 | E_err:   0.023981
[2025-11-01 05:40:04] [Iter 2351/2362] R3[1188/1200] | LR: 0.000013 | E:  -60.157309 | E_var:     4.7230 | E_err:   0.025089
[2025-11-01 05:40:21] [Iter 2352/2362] R3[1189/1200] | LR: 0.000011 | E:  -60.131785 | E_var:     4.5878 | E_err:   0.023363
[2025-11-01 05:40:38] [Iter 2353/2362] R3[1190/1200] | LR: 0.000010 | E:  -60.176477 | E_var:     4.5778 | E_err:   0.023806
[2025-11-01 05:40:55] [Iter 2354/2362] R3[1191/1200] | LR: 0.000008 | E:  -60.175466 | E_var:     4.6279 | E_err:   0.024441
[2025-11-01 05:41:13] [Iter 2355/2362] R3[1192/1200] | LR: 0.000006 | E:  -60.223102 | E_var:     4.7519 | E_err:   0.024183
[2025-11-01 05:41:30] [Iter 2356/2362] R3[1193/1200] | LR: 0.000005 | E:  -60.184593 | E_var:     4.8916 | E_err:   0.024938
[2025-11-01 05:41:47] [Iter 2357/2362] R3[1194/1200] | LR: 0.000004 | E:  -60.141741 | E_var:     4.8293 | E_err:   0.024737
[2025-11-01 05:42:04] [Iter 2358/2362] R3[1195/1200] | LR: 0.000003 | E:  -60.198709 | E_var:     4.7755 | E_err:   0.024407
[2025-11-01 05:42:21] [Iter 2359/2362] R3[1196/1200] | LR: 0.000002 | E:  -60.155939 | E_var:     4.8866 | E_err:   0.024887
[2025-11-01 05:42:39] [Iter 2360/2362] R3[1197/1200] | LR: 0.000002 | E:  -60.138800 | E_var:     4.9701 | E_err:   0.025039
[2025-11-01 05:42:56] [Iter 2361/2362] R3[1198/1200] | LR: 0.000001 | E:  -60.148648 | E_var:     4.8702 | E_err:   0.025598
[2025-11-01 05:43:13] [Iter 2362/2362] R3[1199/1200] | LR: 0.000001 | E:  -60.140239 | E_var:     4.8793 | E_err:   0.023804
[2025-11-01 05:43:13] ✓ Checkpoint saved: checkpoint_iter_002250.pkl
[2025-11-01 05:43:13] ======================================================================================================
[2025-11-01 05:43:13] ✅ Training completed successfully
[2025-11-01 05:43:13] Total restarts: 3
[2025-11-01 05:43:18] Final Energy: -60.14023873 ± 0.02380358
[2025-11-01 05:43:18] Final Variance: 4.879251
[2025-11-01 05:43:18] ======================================================================================================
[2025-11-01 05:43:18] ======================================================================================================
[2025-11-01 05:43:18] Training completed | Runtime: 40685.6s

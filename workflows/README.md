# Workflows - 统一工作流管理系统

本目录提供了Shastry-Sutherland NQS项目的统一工作流管理系统，支持在本地和HPC环境中执行训练、分析和后处理任务。

## 📁 目录结构

```
workflows/
├── configs/                 # 配置文件目录
│   ├── train.yaml          # 训练配置
│   ├── analyze.yaml        # 分析配置
│   ├── infidelity.yaml     # Infidelity分析配置
│   └── postprocess.yaml    # 后处理配置
├── hpc/                    # HPC提交脚本
│   ├── submit_train.pbs    # 训练PBS脚本
│   ├── submit_analyze.pbs  # 分析PBS脚本
│   └── submit_infidelity.pbs  # Infidelity分析PBS脚本
├── local/                  # 本地执行脚本
│   ├── run_train.sh        # 本地训练脚本
│   ├── run_analyze.sh      # 本地分析脚本
│   └── run_postprocess.sh  # 本地后处理脚本
├── pipeline.py             # 统一流水线管理器
└── README.md               # 本文档
```

## 🚀 快速开始

### 方式1: 使用统一流水线管理器（推荐）

```bash
# 查看流水线状态和可用阶段
python workflows/pipeline.py --status

# 提交训练到HPC
python workflows/pipeline.py --stage train --env hpc

# 提交分析到HPC
python workflows/pipeline.py --stage analyze --env hpc
python workflows/pipeline.py --stage infidelity --env hpc

# 在本地运行后处理
python workflows/pipeline.py --stage postprocess --env local

# 运行完整流水线（自动化）
python workflows/pipeline.py --run-all
```

### 方式2: 直接使用脚本

#### HPC环境

```bash
# 提交训练任务
cd /path/to/Shastry-Sutherland_Extra
qsub workflows/hpc/submit_train.pbs

# 提交分析任务
qsub workflows/hpc/submit_analyze.pbs

# 提交Infidelity分析任务
qsub workflows/hpc/submit_infidelity.pbs

# 查看作业状态
qstat -u $USER
```

#### 本地环境

```bash
# 运行训练（小规模测试）
./workflows/local/run_train.sh

# 运行分析
./workflows/local/run_analyze.sh

# 运行后处理
./workflows/local/run_postprocess.sh

# 运行特定的后处理任务
./workflows/local/run_postprocess.sh --task order_analysis
./workflows/local/run_postprocess.sh --task energy_analysis
```

## ⚙️ 配置文件说明

所有配置文件使用YAML格式，位于`workflows/configs/`目录。

### train.yaml - 训练配置

```yaml
system:
  L_values: [6]           # 晶格尺寸
  J2_values: [1.00]       # J2耦合强度
  J1_values: [0.76]       # J1耦合强度

model:
  configs: [[4, 60, 10, 2]]  # [num_layers, d_model, n_heads, patch_size]
  diag_shift: 0.15
  grad_clip: 1.0
  transl_invariant: true  # 是否使用平移不变性

training:
  max_lr: 0.03
  min_lr: 0.005
  n_cycles: 4
  n_samples: 8192
  chunk_size: 4096

checkpoint:
  enable: true
  save_interval: 225
  keep_history: true
  resume_from: ""

hpc:
  queue: "gpu_h200_pinaki"
  walltime: "1440:00:00"
  project: "gs_spms_psengupta"
  conda_env: "netket"
```

### analyze.yaml - 分析配置

```yaml
analysis_params:
  - [4, 0.00, 0.77]      # [L, J2, J1]
  - [4, 0.00, 0.78]
  # ... 更多参数组合

model:
  configs: [[4, 60, 10, 2]]  # [num_layers, d_model, n_heads, patch_size]

sampling:
  n_samples: 1048576     # 2^20

checkpoint:
  mode: "last_n"         # "all", "final", "last_n"
  last_n: 4
```

### infidelity.yaml - Infidelity分析配置

```yaml
scan_configs:
  - L: 6
    J2: 1.00
    J1_values: [0.76, 0.77, 0.78, 0.79, 0.80, 0.81, 0.82, 0.83, 0.84]

model:
  configs: [[4, 60, 10, 2]]  # [num_layers, d_model, n_heads, patch_size]

sampling:
  n_samples: 8192        # 较小的采样数（QGT内存限制）

checkpoint:
  name: ""               # 空字符串表示自动选择最新checkpoint
```

### postprocess.yaml - 后处理配置

```yaml
data_collection:
  results_dir: "results"
  output_dir: "results/figures"
  L_values: [4, 5, 6]
  J2_values: [0.00, 0.05, 1.00]

order_analysis:
  enabled: true
  save_dir: "results/figures/order_parameters"
  plots:
    - "neel_ratio"
    - "dimer_ratio"
    - "af_order_parameter"

energy_analysis:
  enabled: true
  output_prefix: "results/figures/energy/energy_plot"
  show_plot: false
```

## 📊 工作流阶段说明

### 1. TRAIN - 训练阶段

**功能**: 训练Neural Quantum State (NQS)模型

**输入**: 
- 系统参数 (L, J2, J1)
- 模型配置 (层数, 特征数)
- 训练超参数

**输出**:
- 训练好的模型checkpoint
- 训练日志 (train.log)
- 能量收敛曲线

**典型运行时间**: 
- HPC (H200 GPU): 数小时到数天
- 本地 (CPU): 不推荐（太慢）

### 2. ANALYZE - 结构因子分析阶段

**功能**: 计算结构因子和关联函数

**输入**:
- 训练好的模型checkpoint
- 采样参数

**输出**:
- 结构因子数据 (structure_factor.pkl)
- 关联函数数据
- 可视化图表

**典型运行时间**:
- HPC: 数分钟到数小时
- 本地: 可行（取决于采样数）

### 3. INFIDELITY - 相变点检测阶段

**功能**: 使用Infidelity方法检测量子相变点

**输入**:
- 训练好的模型checkpoint
- J1扫描范围

**输出**:
- Infidelity数据
- 相变点位置
- 可视化图表

**典型运行时间**:
- HPC: 数小时
- 本地: 不推荐（QGT计算需要GPU）

### 4. POSTPROCESS - 后处理阶段

**功能**: 数据整合、可视化和报告生成

**输入**:
- 所有分析结果
- 后处理配置

**输出**:
- 序参量分析图表
- 能量分析图表
- 综合报告

**典型运行时间**:
- 本地: 数分钟

## 🔄 典型工作流

### 完整研究流程

```bash
# 1. 修改训练配置
vim workflows/configs/train.yaml

# 2. 提交训练到HPC
python workflows/pipeline.py --stage train --env hpc
# 或直接: qsub workflows/hpc/submit_train.pbs

# 3. 等待训练完成，检查作业状态
qstat -u $USER

# 4. 训练完成后，提交分析任务
python workflows/pipeline.py --stage analyze --env hpc
python workflows/pipeline.py --stage infidelity --env hpc

# 5. 等待分析完成

# 6. 在本地运行后处理
python workflows/pipeline.py --stage postprocess --env local

# 7. 查看结果
ls -lh results/figures/
```

### 快速测试流程（本地）

```bash
# 1. 修改配置为小规模参数
vim workflows/configs/train.yaml
# 设置: L_values=[4], n_samples=1024, n_cycles=1

# 2. 本地运行训练
./workflows/local/run_train.sh

# 3. 本地运行分析
./workflows/local/run_analyze.sh

# 4. 本地运行后处理
./workflows/local/run_postprocess.sh
```

## 🛠️ 高级用法

### 自定义配置

创建自定义配置文件：

```bash
# 复制现有配置
cp workflows/configs/train.yaml workflows/configs/train_custom.yaml

# 编辑配置
vim workflows/configs/train_custom.yaml

# 修改PBS脚本使用自定义配置
vim workflows/hpc/submit_train.pbs
# 修改: CONFIG_FILE="workflows/configs/train_custom.yaml"
```

### 批量提交任务

```bash
# 创建批量提交脚本
cat > submit_batch.sh << 'EOF'
#!/bin/bash
for J1 in 0.76 0.77 0.78 0.79 0.80; do
    # 修改配置文件中的J1值
    sed -i "s/J1_values: .*/J1_values: [$J1]/" workflows/configs/train.yaml
    
    # 提交任务
    qsub workflows/hpc/submit_train.pbs
    
    # 等待一段时间避免同时提交太多任务
    sleep 5
done
EOF

chmod +x submit_batch.sh
./submit_batch.sh
```

### 监控任务进度

```bash
# 查看所有作业
qstat -u $USER

# 查看特定作业详情
qstat -f <job_id>

# 实时查看训练日志
tail -f results/L=6/J2=1.00/J1=0.76/model_L4D60H10P2/training/train.log

# 监控能量收敛
watch -n 10 'tail -20 results/L=6/J2=1.00/J1=0.76/model_L4D60H10P2/training/train.log | grep "Energy"'
```

## 📝 注意事项

### HPC使用

1. **资源限制**: 注意walltime和GPU资源配置
2. **队列选择**: 根据任务类型选择合适的队列
3. **数据备份**: 定期备份results目录
4. **磁盘配额**: 注意checkpoint文件占用空间

### 本地使用

1. **环境配置**: 确保conda环境正确安装
2. **计算资源**: 本地训练仅适合小规模测试
3. **GPU支持**: Infidelity分析需要GPU支持
4. **内存限制**: 注意大规模采样的内存需求

### 配置建议

1. **训练阶段**:
   - 小系统 (L≤4): n_samples=4096-8192
   - 中等系统 (L=5-6): n_samples=8192-16384
   - 大系统 (L≥7): n_samples=16384+

2. **分析阶段**:
   - 结构因子: n_samples=1048576 (2^20)
   - Infidelity: n_samples=8192 (受QGT内存限制)

3. **Checkpoint管理**:
   - 训练: 每225步保存，保留历史
   - 分析: 使用last_n模式，分析最后4个checkpoint

## 🐛 故障排除

### 常见问题

**问题1**: PBS脚本提交失败
```bash
# 检查脚本语法
bash -n workflows/hpc/submit_train.pbs

# 检查PBS指令
qsub -W x=GRES:gpu workflows/hpc/submit_train.pbs
```

**问题2**: Conda环境未激活
```bash
# 检查conda配置
conda info --envs

# 手动激活环境
conda activate netket
```

**问题3**: 配置文件解析错误
```bash
# 验证YAML语法
python -c "import yaml; yaml.safe_load(open('workflows/configs/train.yaml'))"
```

**问题4**: Checkpoint文件未找到
```bash
# 检查checkpoint目录
ls -lh results/L=6/J2=1.00/J1=0.76/model_L4D60H10P2/training/checkpoints/

# 检查checkpoint模式配置
grep "checkpoint" workflows/configs/structure_factor_measurement.yaml
```

## 📚 相关文档

- [项目主README](../README.md)
- [Scripts目录说明](../scripts/README.md)
- [Notebooks使用指南](../notebooks/README.md)
- [NetKet文档](https://www.netket.org/)

## 🤝 贡献

如果你发现问题或有改进建议，请：
1. 创建Issue描述问题
2. 提交Pull Request
3. 更新相关文档

## 📄 许可证

本项目遵循MIT许可证。


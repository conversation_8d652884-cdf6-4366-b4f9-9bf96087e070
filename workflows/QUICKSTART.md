# 快速开始指南

本指南帮助你快速上手使用新的workflows系统。

## 🎯 你的典型工作流

### 之前的方式（已废弃）

```bash
# 旧方式 - 不要再使用
cd jobs
vim config/train.conf
qsub submit.pbs

cd ../notebooks
python order_analysis.py
python energy_plot_L_J2.py
```

### 现在的方式（推荐）

```bash
# 新方式 - 统一的workflows入口
cd workflows

# 1. 修改配置
vim configs/train.yaml

# 2. 提交训练到HPC
python pipeline.py --stage train --env hpc
# 或直接: qsub hpc/submit_train.pbs

# 3. 提交分析到HPC
python pipeline.py --stage analyze --env hpc
python pipeline.py --stage infidelity --env hpc

# 4. 在本地运行后处理
python pipeline.py --stage postprocess --env local
```

## 📋 常用命令速查

### 查看系统状态

```bash
# 查看可用的工作流阶段
python workflows/pipeline.py --status

# 验证所有配置和脚本
python workflows/verify_setup.py
```

### HPC任务提交

```bash
# 提交训练
qsub workflows/hpc/submit_train.pbs

# 提交分析
qsub workflows/hpc/submit_analyze.pbs

# 提交Infidelity分析
qsub workflows/hpc/submit_infidelity.pbs

# 查看作业状态
qstat -u $USER

# 查看作业详情
qstat -f <job_id>
```

### 本地执行

```bash
# 本地训练（小规模测试）
./workflows/local/run_train.sh

# 本地分析
./workflows/local/run_analyze.sh

# 本地后处理
./workflows/local/run_postprocess.sh

# 只运行序参量分析
./workflows/local/run_postprocess.sh --task order_analysis

# 只运行能量分析
./workflows/local/run_postprocess.sh --task energy_analysis
```

### 使用流水线管理器

```bash
# 提交训练到HPC
python workflows/pipeline.py --stage train --env hpc

# 在本地运行后处理
python workflows/pipeline.py --stage postprocess --env local

# 运行完整流水线（自动化）
python workflows/pipeline.py --run-all
```

## ⚙️ 配置文件快速修改

### 修改训练参数

```bash
vim workflows/configs/train.yaml
```

关键参数：
- `system.L_values`: 晶格尺寸，如 `[6]`
- `system.J2_values`: J2耦合强度，如 `[1.00]`
- `system.J1_values`: J1耦合强度，如 `[0.76]`
- `model.configs`: ViT模型配置 `[[num_layers, d_model, n_heads, patch_size]]`，如 `[[4, 60, 10, 2]]`
  - `num_layers`: Transformer层数（推荐4-6）
  - `d_model`: 嵌入维度（推荐60-128）
  - `n_heads`: 注意力头数（推荐8-16）
  - `patch_size`: patch大小（推荐2）
- `training.n_samples`: 采样数，如 `8192`
- `training.n_cycles`: 训练周期数，如 `4`

### 修改分析参数

```bash
vim workflows/configs/analyze.yaml
```

关键参数：
- `analysis_params`: 要分析的参数组合列表
- `sampling.n_samples`: 采样数（建议 `1048576`）
- `checkpoint.mode`: checkpoint模式 (`all`, `final`, `last_n`)

### 修改Infidelity扫描

```bash
vim workflows/configs/infidelity.yaml
```

关键参数：
- `scan_configs`: 扫描配置列表
- `sampling.n_samples`: 采样数（建议 `8192`，受QGT内存限制）

## 🔍 监控和调试

### 查看训练日志

```bash
# 实时查看训练日志
tail -f results/L=6/J2=1.00/J1=0.76/model_L4D60H10P2/training/train.log

# 监控能量收敛
watch -n 10 'tail -20 results/L=6/J2=1.00/J1=0.76/model_L4D60H10P2/training/train.log | grep "Energy"'
```

### 查看分析结果

```bash
# 查看结构因子数据
ls -lh results/L=6/J2=1.00/J1=0.76/model_L4D60H10P2/analysis/

# 查看生成的图表
ls -lh results/figures/
```

### 检查磁盘使用

```bash
# 查看results目录大小
du -sh results/

# 查看各个子目录大小
du -h --max-depth=2 results/
```

## 🚨 常见问题

### 问题1: PBS作业提交失败

```bash
# 检查PBS脚本语法
bash -n workflows/hpc/submit_train.pbs

# 检查队列状态
qstat -Q

# 检查项目配额
qstat -u $USER
```

### 问题2: Conda环境问题

```bash
# 检查可用环境
conda info --envs

# 激活netket环境
conda activate netket

# 验证环境
python -c "import netket; print(netket.__version__)"
```

### 问题3: 配置文件错误

```bash
# 验证YAML语法
python -c "import yaml; yaml.safe_load(open('workflows/configs/train.yaml'))"

# 运行完整验证
python workflows/verify_setup.py
```

### 问题4: Checkpoint未找到

```bash
# 检查checkpoint目录
ls -lh results/L=6/J2=1.00/J1=0.76/model_L4D60H10P2/training/checkpoints/

# 查看最新的checkpoint
ls -lt results/L=6/J2=1.00/J1=0.76/model_L4D60H10P2/training/checkpoints/ | head -5
```

## 📊 结果文件位置

### 训练结果

```
results/L={L}/J2={J2}/J1={J1}/model_L{layers}D{d_model}H{n_heads}P{patch_size}/
├── training/
│   ├── train.log              # 训练日志
│   ├── checkpoints/           # 模型checkpoint
│   │   ├── checkpoint_iter_000225.pkl
│   │   ├── checkpoint_iter_000450.pkl
│   │   └── ...
│   └── final_state.pkl        # 最终状态
```

### 分析结果

```
results/L={L}/J2={J2}/J1={J1}/model_L{layers}D{d_model}H{n_heads}P{patch_size}/
├── analysis/
│   ├── structure_factor_checkpoint_iter_002250.pkl
│   ├── correlations_checkpoint_iter_002250.pkl
│   └── ...
└── infidelity/
    ├── infidelity_scan.pkl
    └── infidelity_plot.png
```

### 后处理结果

```
results/figures/
├── order_parameters/          # 序参量分析图表
│   ├── neel_ratio_J2_1.00.png
│   ├── dimer_ratio_J2_1.00.png
│   └── ...
└── energy/                    # 能量分析图表
    ├── energy_plot_L6_J2_1.00.png
    └── ...
```

## 🎓 进阶使用

### 批量提交任务

创建批量提交脚本：

```bash
cat > submit_batch.sh << 'EOF'
#!/bin/bash
for J1 in 0.76 0.77 0.78 0.79 0.80; do
    sed -i "s/J1_values: .*/J1_values: [$J1]/" workflows/configs/train.yaml
    qsub workflows/hpc/submit_train.pbs
    sleep 5
done
EOF

chmod +x submit_batch.sh
./submit_batch.sh
```

### 自定义配置

```bash
# 复制配置文件
cp workflows/configs/train.yaml workflows/configs/train_custom.yaml

# 编辑自定义配置
vim workflows/configs/train_custom.yaml

# 修改PBS脚本使用自定义配置
vim workflows/hpc/submit_train.pbs
# 修改: CONFIG_FILE="workflows/configs/train_custom.yaml"
```

### 并行分析多个checkpoint

修改 `workflows/configs/analyze.yaml`:

```yaml
checkpoint:
  mode: "all"  # 分析所有checkpoint
```

或只分析最后N个：

```yaml
checkpoint:
  mode: "last_n"
  last_n: 4  # 只分析最后4个checkpoint
```

## 📚 更多信息

- 详细文档: [workflows/README.md](README.md)
- 项目主页: [../README.md](../README.md)
- NetKet文档: https://www.netket.org/

## 💡 提示

1. **先在本地测试**: 使用小参数在本地测试配置是否正确
2. **定期备份**: 定期备份 `results/` 目录
3. **监控资源**: 注意HPC的磁盘配额和walltime限制
4. **保存配置**: 每次实验保存一份配置文件的副本
5. **记录实验**: 在实验日志中记录重要的参数和结果

## 🤝 获取帮助

如果遇到问题：

1. 运行验证脚本: `python workflows/verify_setup.py`
2. 查看详细文档: `workflows/README.md`
3. 检查日志文件: `results/*/training/train.log`
4. 提交Issue或联系维护者

---

**祝你研究顺利！** 🚀


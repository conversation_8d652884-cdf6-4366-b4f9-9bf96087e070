#!/bin/sh

#PBS -q gpu_h200_pinaki
#PBS -l select=1:ngpus=1
#PBS -l walltime=720:00:00
#PBS -P gs_spms_psengupta
#PBS -N ss-hybrid-train
#PBS -j oe

# ==================== Shastry-Sutherland NQS+NF 混合训练任务 ====================
# 此脚本用于在HPC上提交混合训练任务
# 配置文件：workflows/configs/train_hybrid.yaml

# 进入工作目录
cd $PBS_O_WORKDIR || exit $?

# 记录作业开始时间和节点信息
echo "==================== NQS+NF 混合训练任务 ===================="
echo "Job start at: $(date)"
echo "Running on node: $(hostname)"
echo "Working directory: $(pwd)"
echo "GPU Information:"
nvidia-smi

# ==================== 加载Python解析YAML配置 ====================
CONFIG_FILE="workflows/configs/train_hybrid.yaml"

if [ ! -f "$CONFIG_FILE" ]; then
    echo "错误: 配置文件 $CONFIG_FILE 不存在！"
    exit 1
fi

echo "读取配置文件: $CONFIG_FILE"

# 使用Python解析YAML配置
read -r -d '' PYTHON_PARSE_CONFIG << 'EOF'
import yaml
import sys

with open(sys.argv[1], 'r') as f:
    config = yaml.safe_load(f)

# 输出为shell变量格式
print(f"L_VALUES=\"{' '.join(map(str, config['system']['L_values']))}\"")
print(f"J2_VALUES=\"{' '.join(map(str, config['system']['J2_values']))}\"")
print(f"J1_VALUES=\"{' '.join(map(str, config['system']['J1_values']))}\"")

# NQS模型配置
model_configs = ' '.join([f"{layers},{d_model},{n_heads},{patch_size}" for layers, d_model, n_heads, patch_size in config['nqs_model']['configs']])
print(f"MODEL_CONFIGS=\"{model_configs}\"")

# NQS模型参数
print(f"USE_RPE={str(config['nqs_model'].get('use_rpe', True)).lower()}")
print(f"USE_MIXED_PRECISION={str(config['nqs_model'].get('use_mixed_precision', True)).lower()}")
print(f"USE_SYMMETRY={str(config['nqs_model'].get('use_symmetry', False)).lower()}")
character_id_value = config['nqs_model'].get('character_id', None)
if character_id_value is None:
    print(f"CHARACTER_ID=")
else:
    print(f"CHARACTER_ID={character_id_value}")
print(f"USE_PARTIAL_EQUIVARIANCE={str(config['nqs_model'].get('use_partial_equivariance', False)).lower()}")

# NF采样器参数
print(f"NF_LAYERS={config['nf_sampler']['n_layers']}")
print(f"NF_HIDDEN_DIMS=\"{' '.join(map(str, config['nf_sampler']['hidden_dims']))}\"")

# 训练参数（学习率调度）
# SR参数（NQS优化器）
print(f"MAX_LR={config['training']['max_lr']}")
print(f"MIN_LR={config['training']['min_lr']}")
print(f"INITIAL_PERIOD={config['training']['initial_period']}")
print(f"PERIOD_MULT={config['training']['period_mult']}")
print(f"N_CYCLES={config['training']['n_cycles']}")
print(f"WARMUP_RATIO={config['training']['warmup_ratio']}")
print(f"DIAG_SHIFT={config['training']['diag_shift']}")
# NF优化器参数
print(f"NF_LR={config['training']['nf_lr']}")
print(f"NF_OPTIMIZER=\"{config['training']['nf_optimizer']}\"")
print(f"NF_WEIGHT={config['training']['nf_weight']}")

print(f"N_SAMPLES={config['training']['n_samples']}")
# 注意：n_chains, n_discard_per_chain, d_max, chunk_size 在混合训练中不使用
# 因为NF采样器直接生成样本，不使用MCMC采样器

# Checkpoint配置
print(f"ENABLE_CHECKPOINT={str(config['checkpoint']['enable']).lower()}")
print(f"CHECKPOINT_INTERVAL={config['checkpoint']['save_interval']}")
print(f"RESUME_FROM=\"{config['checkpoint']['resume_from']}\"")
print(f"KEEP_HISTORY={str(config['checkpoint']['keep_history']).lower()}")

# 日志配置
print(f"LOG_INTERVAL={config['logging']['log_interval']}")

# HPC配置
print(f"CONDA_ENV=\"{config['hpc']['conda_env']}\"")
print(f"CUDA_VISIBLE_DEVICES=\"{config['hpc']['cuda_visible_devices']}\"")

# 其他参数
print(f"SEED={config['misc']['seed']}")
EOF

# 执行Python脚本并source结果
eval $(python3 -c "$PYTHON_PARSE_CONFIG" "$CONFIG_FILE")

# ==================== 环境配置 ====================
echo "Loading modules..."
module load anaconda2025/2025
module unload cuda/12.2 2>/dev/null || true

# 初始化conda并激活环境
eval "$(/usr/local/anaconda2025/bin/conda shell.bash hook)"
conda activate $CONDA_ENV

# 设置GPU设备
export CUDA_VISIBLE_DEVICES

echo "Using GPU device: $CUDA_VISIBLE_DEVICES"
echo "Python path: $(which python)"
echo "Python version: $(python --version)"
echo "Current conda environment: $CONDA_DEFAULT_ENV"

# ==================== 构建checkpoint参数 ====================
build_checkpoint_args() {
    local enable=$1
    local interval=$2
    local keep_history=$3
    local resume_from=$4
    
    local args=""
    if [ "$enable" = "true" ]; then
        args="--enable_checkpoint --save_interval $interval"
        
        if [ "$keep_history" = "true" ]; then
            args="$args --keep_history"
        fi
        
        if [ -n "$resume_from" ]; then
            args="$args --resume_from $resume_from"
        fi
    fi
    
    echo "$args"
}

CHECKPOINT_ARGS=$(build_checkpoint_args "$ENABLE_CHECKPOINT" "$CHECKPOINT_INTERVAL" "$KEEP_HISTORY" "$RESUME_FROM")

# ==================== 执行混合训练任务 ====================
echo "==================== 开始混合训练任务 ===================="
echo "训练参数配置:"
echo "L values: $L_VALUES"
echo "J2 values: $J2_VALUES"
echo "J1 values: $J1_VALUES"
echo "Model configs: $MODEL_CONFIGS"
echo "NQS optimizer: SR (Stochastic Reconfiguration)"
echo "  SR max LR: $MAX_LR"
echo "  SR min LR: $MIN_LR"
echo "  SR diag shift: $DIAG_SHIFT"
echo "NF optimizer: $NF_OPTIMIZER"
echo "NF learning rate: $NF_LR"
echo "NF weight: $NF_WEIGHT"
echo "Annealing cycles: $N_CYCLES"
echo "Samples: $N_SAMPLES"
echo "Mixed precision: $USE_MIXED_PRECISION"
echo "Checkpoint args: $CHECKPOINT_ARGS"

# 将模型配置转换为数组
model_configs_array=($MODEL_CONFIGS)

for model_config in "${model_configs_array[@]}"; do
    IFS=',' read -r num_layers d_model n_heads patch_size <<< "$model_config"

    echo "==================== 模型: Layers=$num_layers, d_model=$d_model, n_heads=$n_heads, patch_size=$patch_size ===================="

    for L in $L_VALUES; do
        for J2 in $J2_VALUES; do
            for J1 in $J1_VALUES; do
                echo "Starting hybrid training L=$L, J2=$J2, J1=$J1, Layers=$num_layers, d_model=$d_model at: $(date)"

                # 构建混合精度参数
                MIXED_PRECISION_ARG=""
                if [ "$USE_MIXED_PRECISION" = "true" ]; then
                    MIXED_PRECISION_ARG="--use_mixed_precision"
                fi

                # d_max 参数在混合训练中不使用

                # 构建对称性参数
                SYMMETRY_ARG=""
                if [ "$USE_SYMMETRY" = "false" ]; then
                    SYMMETRY_ARG="--no_symmetry"
                fi

                CHARACTER_ID_ARG=""
                if [ -n "$CHARACTER_ID" ]; then
                    CHARACTER_ID_ARG="--character_id $CHARACTER_ID"
                fi

                # 构建部分等变性参数
                PARTIAL_EQUIVARIANCE_ARG=""
                if [ "$USE_PARTIAL_EQUIVARIANCE" = "true" ]; then
                    PARTIAL_EQUIVARIANCE_ARG="--use_partial_equivariance"
                fi

                # 构建RPE参数
                RPE_ARG=""
                if [ "$USE_RPE" = "true" ]; then
                    RPE_ARG="--use_rpe"
                fi

                python scripts/train_hybrid.py $L $J2 $J1 \
                    --num_layers $num_layers \
                    --d_model $d_model \
                    --n_heads $n_heads \
                    --patch_size $patch_size \
                    $RPE_ARG \
                    $SYMMETRY_ARG \
                    $CHARACTER_ID_ARG \
                    $PARTIAL_EQUIVARIANCE_ARG \
                    $MIXED_PRECISION_ARG \
                    --nf_layers $NF_LAYERS \
                    --nf_hidden_dims $NF_HIDDEN_DIMS \
                    --n_cycles $N_CYCLES \
                    --max_lr $MAX_LR \
                    --min_lr $MIN_LR \
                    --initial_period $INITIAL_PERIOD \
                    --period_mult $PERIOD_MULT \
                    --warmup_ratio $WARMUP_RATIO \
                    --diag_shift $DIAG_SHIFT \
                    --nf_lr $NF_LR \
                    --nf_optimizer $NF_OPTIMIZER \
                    --nf_weight $NF_WEIGHT \
                    --n_samples $N_SAMPLES \
                    --seed $SEED \
                    --log_interval $LOG_INTERVAL \
                    $CHECKPOINT_ARGS

                echo "Completed hybrid training L=$L, J2=$J2, J1=$J1 at: $(date)"
            done
        done
    done
done

echo "==================== 所有混合训练任务完成 ===================="
echo "Job finished at: $(date)"

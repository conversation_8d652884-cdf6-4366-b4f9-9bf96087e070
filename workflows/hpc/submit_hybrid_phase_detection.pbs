#!/bin/sh

#PBS -q gpu_h200_pinaki
#PBS -l select=1:ngpus=1
#PBS -l walltime=24:00:00
#PBS -P gs_spms_psengupta
#PBS -N ss-vit-hybrid-phase
#PBS -j oe

# ==================== Shastry-Sutherland NQS 表征学习相变检测任务 ====================
# 此脚本用于在HPC上提交表征学习相变检测任务
# 基于VAE表征学习进行相变点检测

# 进入工作目录
cd $PBS_O_WORKDIR || exit $?

# 记录作业开始时间和节点信息
echo "==================== NQS 表征学习相变检测任务 ===================="
echo "Job start at: $(date)"
echo "Running on node: $(hostname)"
echo "Working directory: $(pwd)"
echo "GPU Information:"
nvidia-smi

# ==================== 环境配置 ====================
echo "Loading modules..."
module load anaconda2025/2025
module unload cuda/12.2 2>/dev/null || true

# 初始化conda并激活环境
eval "$(/usr/local/anaconda2025/bin/conda shell.bash hook)"
conda activate netket

# 设置GPU设备
export CUDA_VISIBLE_DEVICES="0"
export XLA_FLAGS="--xla_gpu_cuda_data_dir=/usr/local/cuda"

echo "Using GPU device: $CUDA_VISIBLE_DEVICES"
echo "Python path: $(which python)"
echo "Python version: $(python --version)"
echo "Current conda environment: $CONDA_DEFAULT_ENV"

# ==================== 读取配置文件 ====================
CONFIG_FILE="workflows/configs/hybrid_phase_detection.yaml"

if [ ! -f "$CONFIG_FILE" ]; then
    echo "错误: 配置文件 $CONFIG_FILE 不存在！"
    exit 1
fi

echo "读取配置文件: $CONFIG_FILE"

# 使用Python解析YAML配置
read -r -d '' PYTHON_PARSE_CONFIG << 'EOF'
import yaml
import sys

with open(sys.argv[1], 'r') as f:
    config = yaml.safe_load(f)

# 从第一个扫描配置中读取默认参数
scan = config['scan_configs'][0]
print(f"L={scan['L']}")
print(f"J2={scan['J2']:.2f}")
print(f"J1_MIN={scan['J1_min']:.2f}")
print(f"J1_MAX={scan['J1_max']:.2f}")
print(f"J1_STEP={scan['J1_step']:.2f}")

# 模型配置
model_config = config['model']['configs'][0]
print(f"NUM_LAYERS={model_config[0]}")
print(f"D_MODEL={model_config[1]}")
print(f"N_HEADS={model_config[2]}")
print(f"PATCH_SIZE={model_config[3]}")

# Checkpoint配置
checkpoint_name = config['checkpoint']['name']
print(f"CHECKPOINT={checkpoint_name}")

# 表征学习配置
rl_config = config['representation_learning']
print(f"VAE_LATENT_DIM={rl_config['vae_latent_dim']}")
print(f"VAE_EPOCHS={rl_config['vae_epochs']}")
print(f"FUSION_WEIGHTS_PARAM={rl_config['fusion_weights'][0]:.1f}")
print(f"FUSION_WEIGHTS_VAE={rl_config['fusion_weights'][1]:.1f}")

# 采样配置
sampling_config = config['sampling']
n_chains = sampling_config.get('n_chains', None)
d_max = sampling_config.get('d_max', None)
print(f"N_SAMPLES={sampling_config['n_samples']}")
print(f"N_CHAINS={n_chains if n_chains is not None else ''}")
print(f"D_MAX={d_max if d_max is not None else ''}")
EOF

# 执行Python脚本并source结果
eval $(python3 -c "$PYTHON_PARSE_CONFIG" "$CONFIG_FILE")

# 组合融合权重
FUSION_WEIGHTS="$FUSION_WEIGHTS_PARAM $FUSION_WEIGHTS_VAE"

# ==================== 执行混合相变检测任务 ====================
echo "==================== 开始混合相变检测任务 ===================="

echo "System Configuration:"
echo "  L=$L, J2=$J2"
echo "  J1 range: $J1_MIN to $J1_MAX (step $J1_STEP)"
echo "  Model: $NUM_LAYERS layers, d_model=$D_MODEL, n_heads=$N_HEADS, patch_size=$PATCH_SIZE"
echo "  Checkpoint: $CHECKPOINT"
echo "  VAE latent dim: $VAE_LATENT_DIM"

# 创建输出目录
OUTPUT_DIR="measurements/representation_learning/L=${L}/J2=${J2}"
mkdir -p "$OUTPUT_DIR"

echo ""
echo "==================== 表征学习相变检测 ===================="
echo "运行表征学习相变检测脚本..."

# 构建命令
cmd="python scripts/run_hybrid_analysis.py \
    --L $L \
    --J2 $J2 \
    --J1_min $J1_MIN \
    --J1_max $J1_MAX \
    --J1_step $J1_STEP \
    --num_layers $NUM_LAYERS \
    --d_model $D_MODEL \
    --n_heads $N_HEADS \
    --patch_size $PATCH_SIZE \
    --checkpoint $CHECKPOINT \
    --vae_latent_dim $VAE_LATENT_DIM \
    --vae_epochs $VAE_EPOCHS \
    --fusion_weights $FUSION_WEIGHTS \
    --n_samples $N_SAMPLES \
    --output_dir \"$OUTPUT_DIR\""

# 添加可选参数
if [ -n "$N_CHAINS" ]; then
    cmd="$cmd --n_chains $N_CHAINS"
fi
if [ -n "$D_MAX" ]; then
    cmd="$cmd --d_max $D_MAX"
fi

# 执行命令
eval $cmd

if [ $? -eq 0 ]; then
    echo "✓ 表征学习相变检测完成"
else
    echo "✗ 表征学习相变检测失败"
    exit 1
fi

# 记录磁盘使用情况
echo ""
echo "==================== 结果统计 ===================="
echo "Disk usage for measurements:"
du -sh measurements/

echo ""
echo "Output files in $OUTPUT_DIR:"
ls -lh "$OUTPUT_DIR"

echo ""
echo "==================== 所有任务完成 ===================="
echo "Job finished at: $(date)"
echo "Results saved to: $OUTPUT_DIR"


#!/bin/sh

#PBS -q gpu_h200_pinaki
#PBS -l select=1:ngpus=1
#PBS -l walltime=1440:00:00
#PBS -P gs_spms_psengupta
#PBS -N ss-vit-infidelity
#PBS -j oe

# ==================== Shastry-Sutherland NQS Infidelity 测量任务 ====================
# 此脚本用于在HPC上提交Infidelity测量任务（相变点检测）
# 配置文件：workflows/configs/infidelity_measurement.yaml

# 进入工作目录
cd $PBS_O_WORKDIR || exit $?

# 记录作业开始时间和节点信息
echo "==================== NQS Infidelity 分析任务 ===================="
echo "Job start at: $(date)"
echo "Running on node: $(hostname)"
echo "Working directory: $(pwd)"
echo "GPU Information:"
nvidia-smi

# ==================== 加载Python解析YAML配置 ====================
CONFIG_FILE="workflows/configs/infidelity_measurement.yaml"

if [ ! -f "$CONFIG_FILE" ]; then
    echo "错误: 配置文件 $CONFIG_FILE 不存在！"
    exit 1
fi

echo "读取配置文件: $CONFIG_FILE"

# 使用Python解析YAML配置
read -r -d '' PYTHON_PARSE_CONFIG << 'EOF'
import yaml
import sys

with open(sys.argv[1], 'r') as f:
    config = yaml.safe_load(f)

# 输出扫描配置
print("SCAN_CONFIGS=(")
for scan in config['scan_configs']:
    L = scan['L']
    J2 = scan['J2']
    J1_values = ' '.join(map(str, scan['J1_values']))
    print(f'  "{L} {J2:.2f} {J1_values}"')
print(")")

# 模型配置 (ViT: num_layers, d_model, n_heads, patch_size)
model_configs = ' '.join([f"{layers},{d_model},{n_heads},{patch_size}" for layers, d_model, n_heads, patch_size in config['model']['configs']])
print(f"MODEL_CONFIGS=\"{model_configs}\"")

# Checkpoint配置
print(f"CHECKPOINT_NAME=\"{config['checkpoint']['name']}\"")

# 采样配置
print(f"N_SAMPLES={config['sampling']['n_samples']}")
n_chains = config['sampling'].get('n_chains', None)
d_max = config['sampling'].get('d_max', None)
print(f"N_CHAINS={n_chains if n_chains is not None else ''}")
print(f"D_MAX={d_max if d_max is not None else ''}")

# HPC配置
print(f"CONDA_ENV=\"{config['hpc']['conda_env']}\"")
print(f"CUDA_VISIBLE_DEVICES=\"{config['hpc']['cuda_visible_devices']}\"")
EOF

# 执行Python脚本并source结果
eval $(python3 -c "$PYTHON_PARSE_CONFIG" "$CONFIG_FILE")

# ==================== 环境配置 ====================
echo "Loading modules..."
module load anaconda2025/2025
module unload cuda/12.2 2>/dev/null || true

# 初始化conda并激活环境
eval "$(/usr/local/anaconda2025/bin/conda shell.bash hook)"
conda activate $CONDA_ENV

# 设置GPU设备
export CUDA_VISIBLE_DEVICES

echo "Using GPU device: $CUDA_VISIBLE_DEVICES"
echo "Python path: $(which python)"
echo "Python version: $(python --version)"
echo "Current conda environment: $CONDA_DEFAULT_ENV"

# ==================== 执行Infidelity分析任务 ====================
echo "==================== 开始 Infidelity 分析任务 ===================="
echo "扫描配置数量: ${#SCAN_CONFIGS[@]}"
echo "模型配置: $MODEL_CONFIGS"
echo "Checkpoint: $CHECKPOINT_NAME"
echo "采样数: $N_SAMPLES"

# 将模型配置转换为数组
model_configs_array=($MODEL_CONFIGS)

# 遍历所有扫描配置
for scan_config in "${SCAN_CONFIGS[@]}"; do
    # 解析扫描配置
    read -ra config_parts <<< "$scan_config"
    L=${config_parts[0]}
    J2=${config_parts[1]}
    # 剩余的都是 J1 值
    J1_values="${config_parts[@]:2}"
    
    echo "==================== 扫描配置 ===================="
    echo "L=$L, J2=$J2"
    echo "J1 值: $J1_values"
    
    # 遍历所有模型配置
    for model_config in "${model_configs_array[@]}"; do
        IFS=',' read -r num_layers d_model n_heads patch_size <<< "$model_config"

        echo "==================== 模型: Layers=$num_layers, d_model=$d_model, n_heads=$n_heads, patch_size=$patch_size ===================="

        # 构建checkpoint参数
        checkpoint_arg=""
        if [ -n "$CHECKPOINT_NAME" ]; then
            checkpoint_arg="--checkpoint $CHECKPOINT_NAME"
        fi

        # 运行 Infidelity 分析
        echo "运行 Infidelity 分析脚本..."

        # 构建命令
        cmd="python scripts/infidelity_measurement.py \
            --L $L \
            --J2 $J2 \
            --J1_values $J1_values \
            --d_model $d_model \
            --n_heads $n_heads \
            --patch_size $patch_size \
            --num_layers $num_layers \
            $checkpoint_arg \
            --n_samples $N_SAMPLES"

        # 添加可选参数
        if [ -n "$N_CHAINS" ]; then
            cmd="$cmd --n_chains $N_CHAINS"
        fi
        if [ -n "$D_MAX" ]; then
            cmd="$cmd --d_max $D_MAX"
        fi

        # 执行命令
        eval $cmd

        if [ $? -eq 0 ]; then
            echo "✓ Infidelity 分析完成: L=$L, J2=$J2, Layers=$num_layers, d_model=$d_model"
        else
            echo "✗ Infidelity 分析失败: L=$L, J2=$J2, Layers=$num_layers, d_model=$d_model"
        fi
    done
done

# 记录磁盘使用情况
echo "Disk usage for results:"
du -sh results/

echo "==================== 所有 Infidelity 分析任务完成 ===================="
echo "Job finished at: $(date)"


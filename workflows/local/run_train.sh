#!/bin/bash

# ==================== Shastry-Sutherland NQS 本地训练脚本 ====================
# 此脚本用于在本地环境运行训练任务
# 配置文件：workflows/configs/train.yaml

set -e  # 遇到错误立即退出

# 获取脚本所在目录的父目录（项目根目录）
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

cd "$PROJECT_ROOT" || exit 1

echo "==================== NQS 本地训练任务 ===================="
echo "Job start at: $(date)"
echo "Working directory: $(pwd)"

# ==================== 环境检测和激活 ====================
echo "检测并激活conda环境..."

if command -v conda &> /dev/null; then
    # 初始化conda
    eval "$(conda shell.bash hook)"
    
    # 从配置文件读取环境名称
    CONDA_ENV=$(python3 -c "import yaml; print(yaml.safe_load(open('workflows/configs/train.yaml'))['hpc']['conda_env'])")
    
    # 激活环境
    conda activate "$CONDA_ENV"
    echo "✓ Conda环境已激活: $CONDA_ENV"
else
    echo "警告: 未找到conda，使用系统Python环境"
fi

echo "Python path: $(which python)"
echo "Python version: $(python --version)"

# ==================== 加载配置 ====================
CONFIG_FILE="workflows/configs/train.yaml"

if [ ! -f "$CONFIG_FILE" ]; then
    echo "错误: 配置文件 $CONFIG_FILE 不存在！"
    exit 1
fi

echo "读取配置文件: $CONFIG_FILE"

# 使用Python解析YAML配置
read -r -d '' PYTHON_PARSE_CONFIG << 'EOF'
import yaml
import sys

with open(sys.argv[1], 'r') as f:
    config = yaml.safe_load(f)

# 输出为shell变量格式
print(f"L_VALUES=\"{' '.join(map(str, config['system']['L_values']))}\"")
print(f"J2_VALUES=\"{' '.join(map(str, config['system']['J2_values']))}\"")
print(f"J1_VALUES=\"{' '.join(map(str, config['system']['J1_values']))}\"")

# 模型配置 (ViT: num_layers, d_model, n_heads, patch_size)
model_configs = ' '.join([f"{layers},{d_model},{n_heads},{patch_size}" for layers, d_model, n_heads, patch_size in config['model']['configs']])
print(f"MODEL_CONFIGS=\"{model_configs}\"")
print(f"DIAG_SHIFT={config['model']['diag_shift']}")
# grad_clip parameter removed - no longer using gradient clipping

# 训练参数
print(f"MAX_LR={config['training']['max_lr']}")
print(f"MIN_LR={config['training']['min_lr']}")
print(f"INITIAL_PERIOD={config['training']['initial_period']}")
print(f"PERIOD_MULT={config['training']['period_mult']}")
print(f"N_CYCLES={config['training']['n_cycles']}")
print(f"WARMUP_RATIO={config['training'].get('warmup_ratio', 0.0)}")
print(f"N_SAMPLES={config['training']['n_samples']}")
print(f"N_CHAINS={config['training'].get('n_chains', config['training']['n_samples'])}")
print(f"N_DISCARD_PER_CHAIN={config['training'].get('n_discard_per_chain', 0)}")
# 处理 d_max: 如果是 None/null，不传递参数；否则传递数值
d_max_value = config['training'].get('d_max', None)
if d_max_value is None:
    print(f"D_MAX=")  # 空值，后续不传递此参数
else:
    print(f"D_MAX={d_max_value}")
print(f"CHUNK_SIZE={config['training']['chunk_size']}")

# 模型参数
print(f"DROPOUT_RATE={config['model'].get('dropout_rate', 0.0)}")
print(f"MLP_RATIO={config['model'].get('mlp_ratio', 4.0)}")
print(f"USE_MIXED_PRECISION={str(config['model'].get('use_mixed_precision', True)).lower()}")

# 对称性参数
print(f"USE_SYMMETRY={str(config['model'].get('use_symmetry', True)).lower()}")
character_id_value = config['model'].get('character_id', None)
if character_id_value is None:
    print(f"CHARACTER_ID=")  # 空值，后续不传递此参数
else:
    print(f"CHARACTER_ID={character_id_value}")

# Checkpoint配置
print(f"ENABLE_CHECKPOINT={str(config['checkpoint']['enable']).lower()}")
print(f"CHECKPOINT_INTERVAL={config['checkpoint']['save_interval']}")
print(f"RESUME_FROM=\"{config['checkpoint']['resume_from']}\"")
print(f"KEEP_HISTORY={str(config['checkpoint']['keep_history']).lower()}")
EOF

# 执行Python脚本并source结果
eval $(python3 -c "$PYTHON_PARSE_CONFIG" "$CONFIG_FILE")

# ==================== 构建checkpoint参数 ====================
build_checkpoint_args() {
    local enable=$1
    local interval=$2
    local keep_history=$3
    local resume_from=$4
    
    local args=""
    if [ "$enable" = "true" ]; then
        args="--enable_checkpoint --save_interval $interval"
        
        if [ "$keep_history" = "true" ]; then
            args="$args --keep_history"
        fi
        
        if [ -n "$resume_from" ]; then
            args="$args --resume_from $resume_from"
        fi
    fi
    
    echo "$args"
}

CHECKPOINT_ARGS=$(build_checkpoint_args "$ENABLE_CHECKPOINT" "$CHECKPOINT_INTERVAL" "$KEEP_HISTORY" "$RESUME_FROM")

# ==================== 执行训练任务 ====================
echo "==================== 开始训练任务 ===================="
echo "训练参数配置:"
echo "L values: $L_VALUES"
echo "J2 values: $J2_VALUES"
echo "J1 values: $J1_VALUES"
echo "Model configs: $MODEL_CONFIGS"
echo "Learning rate: [$MIN_LR, $MAX_LR]"
echo "Samples: $N_SAMPLES"
echo "Checkpoint args: $CHECKPOINT_ARGS"

# 将模型配置转换为数组
model_configs_array=($MODEL_CONFIGS)

for model_config in "${model_configs_array[@]}"; do
    IFS=',' read -r num_layers d_model n_heads patch_size <<< "$model_config"

    echo "==================== 模型: Layers=$num_layers, d_model=$d_model, n_heads=$n_heads, patch_size=$patch_size ===================="

    for L in $L_VALUES; do
        for J2 in $J2_VALUES; do
            for J1 in $J1_VALUES; do
                echo "Starting training L=$L, J2=$J2, J1=$J1, Layers=$num_layers, d_model=$d_model at: $(date)"

                # 构建 d_max 参数（只在有值时添加）
                D_MAX_ARG=""
                if [ -n "$D_MAX" ]; then
                    D_MAX_ARG="--d_max $D_MAX"
                fi

                # 构建混合精度参数
                MIXED_PRECISION_ARG=""
                if [ "$USE_MIXED_PRECISION" = "true" ]; then
                    MIXED_PRECISION_ARG="--use_mixed_precision"
                fi

                # 构建对称性参数
                SYMMETRY_ARG=""
                if [ "$USE_SYMMETRY" = "false" ]; then
                    SYMMETRY_ARG="--no_symmetry"
                fi

                CHARACTER_ID_ARG=""
                if [ -n "$CHARACTER_ID" ]; then
                    CHARACTER_ID_ARG="--character_id $CHARACTER_ID"
                fi

                python scripts/train.py $L $J2 $J1 \
                    --n_samples $N_SAMPLES \
                    --n_chains $N_CHAINS \
                    --n_discard_per_chain $N_DISCARD_PER_CHAIN \
                    $D_MAX_ARG \
                    --chunk_size $CHUNK_SIZE \
                    --n_cycles $N_CYCLES \
                    --initial_period $INITIAL_PERIOD \
                    --period_mult $PERIOD_MULT \
                    --max_lr $MAX_LR \
                    --min_lr $MIN_LR \
                    --warmup_ratio $WARMUP_RATIO \
                    --d_model $d_model \
                    --n_heads $n_heads \
                    --patch_size $patch_size \
                    --num_layers $num_layers \
                    --dropout_rate $DROPOUT_RATE \
                    --mlp_ratio $MLP_RATIO \
                    --diag_shift $DIAG_SHIFT \
                    $MIXED_PRECISION_ARG \
                    $SYMMETRY_ARG \
                    $CHARACTER_ID_ARG \
                    $CHECKPOINT_ARGS

                echo "Completed training L=$L, J2=$J2, J1=$J1 at: $(date)"
            done
        done
    done
done

echo "==================== 所有训练任务完成 ===================="
echo "Job finished at: $(date)"


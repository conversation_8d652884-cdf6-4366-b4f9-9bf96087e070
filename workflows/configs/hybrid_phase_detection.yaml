# 表征学习相变检测配置文件
# 用于基于VAE表征学习进行相变点检测

# 扫描配置
scan_configs:
  - L: 6
    J2: 1.00
    J1_min: 0.76
    J1_max: 0.84
    J1_step: 0.01

# 模型配置
model:
  configs:
    - [4, 60, 10, 2]  # [num_layers, d_model, n_heads, patch_size]

# Checkpoint配置
checkpoint:
  name: "latest"  # 使用特定checkpoint，或设为"latest"自动选择最新

# 表征学习配置
representation_learning:
  vae_latent_dim: 16
  vae_epochs: 50
  vae_batch_size: 32
  vae_learning_rate: 0.001
  fusion_weights: [0.5, 0.5]  # [param_features_weight, vae_latent_weight]

# 采样配置
sampling:
  n_samples: 1000
  n_chains: null  # 并行链数（null表示使用默认值：n_samples的1/128）
  d_max: null     # 最大交换距离（null表示使用晶格最大距离）

# HPC配置
hpc:
  conda_env: "netket"
  cuda_visible_devices: "0"
  walltime: "24:00:00"
  queue: "gpu_h200_pinaki"
  project: "gs_spms_psengupta"

# 输出配置
output:
  base_dir: "measurements/representation_learning"
  save_plots: true
  save_results_pkl: true
  save_summary: true


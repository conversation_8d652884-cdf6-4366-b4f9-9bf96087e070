# ==================== 训练任务配置文件 ====================
# 此文件包含基础训练任务的所有参数配置

experiment:
  name: "ss_nqs_training"
  description: "Shastry-Sutherland模型NQS训练"

# ==================== 系统参数配置 ====================
system:
  L_values: [6]           # 晶格尺寸
  J2_values: [1.00]       # J2耦合强度
  J1_values: [0.80]       # J1耦合强度（训练任务）

# ==================== 模型参数 ====================
model:
  # ViT模型参数配置（针对6x6x4系统优化）
  # 格式：[num_layers, d_model, n_heads, patch_size]
  # 6x6x4 = 144个自旋，patch_size=2 -> 72个patches
  # d_model=128确保足够的表达能力，n_heads=8实现多头注意力
  configs:
    - [4, 32, 4, 2]  # 更深的网络，更大的嵌入维度

  # 优化器参数
  diag_shift: 0.005          # SR对角位移
  use_rpe: true             # 使用相对位置编码(自动保持平移不变性)
  dropout_rate: 0         # Dropout比率（0表示不使用dropout，推荐0.1-0.2用于正则化）
  mlp_ratio: 4.0            # MLP隐藏层维度相对于d_model的倍数（标准Transformer使用4.0）

  # 混合精度训练配置
  use_mixed_precision: true # 启用混合精度训练（参数float64，计算bfloat16）
                            # 优势：1) 减少约50%内存占用 2) 加速训练1.5-2x
                            # 策略：参数保持float64，前向/反向传播使用bfloat16
                            #      关键操作(LayerNorm/Softmax/输出)保持float64精度

  # 对称性约束配置
  # 注意：use_symmetry和use_partial_equivariance不能同时为true
  use_symmetry: false        # 使用对称性约束（SymmExpSum包装整个模型）
                            # 使用shastry_sutherland_point_symmetries获取点群对称性
                            # 只包含C4v点群对称性（8个操作），不包含平移对称性
  character_id: null        # 对称性特征标ID（null表示全对称表示，推荐）
                            # 可选值：0, 1, 2, ... 用于指定特定不可约表示

  # 部分等变性配置（推荐使用）
  use_partial_equivariance: true  # 使用部分等变性（嵌入层等变，后续层普通）
                                  # 在嵌入阶段使用共享/对称权重（旋转/平移共享）
                                  # 使用shastry_sutherland_point_symmetries定义权重共享模式
                                  # 优势：在嵌入层引入对称性归纳偏置，同时保持模型表达能力

# ==================== 训练超参数 ====================
training:
  # 学习率调度（Linear Warm-up + 余弦退火 + 热重启）
  max_lr: 0.05             # 最大学习率（针对大模型降低）
  min_lr: 1e-7             # 最小学习率
  initial_period: 100       # 初始退火周期长度
  period_mult: 2.0          # 周期倍增因子
  n_cycles: 3               # 重启周期数
  warmup_ratio: 0.03        # Warm-up阶段占总训练步数的百分比（从3%增加到5%，更平稳过渡）
                            # 标准ViT使用5-10%的warm-up比例

  # 采样参数（针对6x6x4系统）
  n_samples: 8192            # 样本总数（MCState中的n_samples）
  n_chains: 8192            # 并行链数（MetropolisExchange中的n_chains），与样本数相等以最大化并行度
                            # 默认 sweep_size = N_sites，每个样本前会做整格 sweep，混合充足
  n_discard_per_chain: 0   # 每次参数更新后丢弃的样本数（用于再热化）
                            # 参数更新后目标分布改变，需要丢弃若干步让链适应新分布
                            # 建议值：50-100，平衡混合质量和计算效率
  d_max: null                  # 最大交换距离（改为2以提高接受率并保持局部探索，配合大并行更稳）
                            # null表示使用晶格最大距离（通常过大，接受率低）
                            # 建议值：2（快速采样）或 3（平衡采样质量和速度）
  chunk_size: 8192          # 批处理大小（模型前向传播的批次大小）
                            # 建议：chunk_size <= n_samples，平衡内存和速度

# ==================== Checkpoint配置 ====================
checkpoint:
  enable: true
  save_interval: 100        # 必须能整除总迭代数
                            # 总迭代数 = period1(200) + period2(400) + period3(800) = 1400
  resume_from: "/home/<USER>/Repositories/Shastry-Sutherland_ViT/saved_models/L=6/J2=1.00/J1=0.80/checkpoints/checkpoint_iter_002400.pkl"           # 留空表示从头开始训练
  keep_history: true        # 保留所有checkpoint历史

# ==================== HPC配置 ====================
hpc:
  queue: "gpu_h200_pinaki"
  ngpus: 1
  walltime: "1440:00:00"
  project: "gs_spms_psengupta"
  job_name: "ss-vit-train"
  
  # 环境配置
  conda_env: "netket"
  anaconda_module: "anaconda2025/2025"
  unload_cuda_module: "cuda/12.2"
  
  # GPU设备
  cuda_visible_devices: "0"


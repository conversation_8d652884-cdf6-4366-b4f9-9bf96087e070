# ==================== 混合训练任务配置文件 ====================
# 此文件包含NQS+NF混合训练任务的所有参数配置

experiment:
  name: "ss_nqs_nf_hybrid_training"
  description: "Shastry-Sutherland模型NQS+NF混合训练"

# ==================== 系统参数配置 ====================
system:
  L_values: [6]           # 晶格尺寸
  J2_values: [1.00]       # J2耦合强度
  J1_values: [0.80]       # J1耦合强度（训练任务）

# ==================== NQS模型参数 ====================
nqs_model:
  # ViT模型参数配置（针对6x6x4系统优化）
  # 格式：[num_layers, d_model, n_heads, patch_size]
  configs:
    - [4, 96, 4, 2]  # 更深的网络，更大的嵌入维度

  # 模型配置
  use_rpe: true             # 使用相对位置编码(自动保持平移不变性)
  use_mixed_precision: true # 启用混合精度训练

  # 对称性约束配置
  use_symmetry: false        # 使用对称性约束（SymmExpSum包装整个模型）
  character_id: null        # 对称性特征标ID（null表示全对称表示）

  # 部分等变性配置（推荐使用）
  use_partial_equivariance: true  # 使用部分等变性（嵌入层等变，后续层普通）

# ==================== NF采样器参数 ====================
nf_sampler:
  n_layers: 8               # 归一化流层数
  hidden_dims: [64, 64]     # MLP隐藏层维度
  use_mixed_precision: true # 使用混合精度

# ==================== 训练超参数 ====================
training:
  # 学习率调度（Linear Warm-up + 余弦退火 + 热重启）
  max_lr: 0.025              # SR最大学习率
  min_lr: 0.0075              # SR最小学习率
  initial_period: 3000       # SR初始周期
  period_mult: 1.0          # SR周期倍增因子
  n_cycles: 1               # 重启周期数
  warmup_ratio: 0.03        # SR warm-up比例
  diag_shift: 0.005         # SR对角位移
  
  # NF优化器：Adam
  nf_lr: 0.001              # NF采样器学习率
  nf_optimizer: "adam"      # NF优化器（固定为adam）
  
  # 损失权重
  nf_weight: 1.0            # NF损失权重 (λ in L = L_energy + λ * L_NF)

  # 采样参数（针对6x6x4系统）
  n_samples: 4096           # 每步采样数量（NF采样器生成的样本数）

# ==================== Checkpoint配置 ====================
checkpoint:
  enable: true
  save_interval: 300        # 保存间隔
  resume_from: ""           # 从checkpoint恢复（留空表示从头开始）
  keep_history: true        # 保留所有checkpoint历史

# ==================== 日志配置 ====================
logging:
  log_interval: 1           # 日志输出间隔（每步都写入）

# ==================== HPC配置 ====================
hpc:
  queue: "gpu_h200_pinaki"
  ngpus: 1
  walltime: "720:00:00"     # 12小时（混合训练可能需要更长时间）
  project: "gs_spms_psengupta"
  job_name: "ss-hybrid-train"
  
  # 环境配置
  conda_env: "netket"
  anaconda_module: "anaconda2025/2025"
  
  # GPU设备
  cuda_visible_devices: "0"

# ==================== 其他参数 ====================
misc:
  seed: 42                  # 随机种子

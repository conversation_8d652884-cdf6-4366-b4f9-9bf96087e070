#!/usr/bin/env python3
"""
Shastry-Sutherland模型训练脚本 - 使用SSRunner框架
"""

# 设置环境变量
import os
import sys
import argparse

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

# ==================== JAX编译缓存配置 ====================
# 启用JAX持久化编译缓存以加速后续运行
# 首次运行会编译并缓存，后续运行直接加载缓存，节省5-15分钟
cache_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), ".jax_cache")
os.makedirs(cache_dir, exist_ok=True)
os.environ["JAX_COMPILATION_CACHE_DIR"] = cache_dir
os.environ["JAX_PERSISTENT_CACHE_MIN_ENTRY_SIZE_BYTES"] = "0"  # 缓存所有编译结果
os.environ["JAX_PERSISTENT_CACHE_MIN_COMPILE_TIME_SECS"] = "1"  # 缓存编译时间>1s的函数

# ==================== H200 GPU优化配置 ====================
# H200 Hopper架构特定优化标志
# 注意：仅使用当前XLA版本支持的标志
os.environ["XLA_FLAGS"] = (
    "--xla_gpu_cuda_data_dir=/usr/local/cuda "
    "--xla_gpu_enable_triton_gemm=true "  # 启用Triton GEMM优化
    "--xla_gpu_enable_cudnn_fmha=true "   # 启用cuDNN Flash Attention
    "--xla_gpu_autotune_level=4 "  # 最高级别自动调优
    # H200异步优化标志（仅使用验证过的标志）
    "--xla_gpu_enable_latency_hiding_scheduler=true "  # 延迟隐藏调度器
    "--xla_gpu_enable_highest_priority_async_stream=true "  # 高优先级异步流
    # 注意：以下标志在当前XLA版本中可能不支持，已注释
    # "--xla_gpu_enable_async_all_gather=true "  # 异步all-gather通信
    # "--xla_gpu_enable_async_all_reduce=true "  # 异步all-reduce归约
    # "--xla_gpu_enable_pipelined_all_reduce=true "  # 流水线all-reduce
)

# Transformer特定优化
os.environ["XLA_PYTHON_CLIENT_MEM_FRACTION"] = "0.95"  # 使用95%的GPU内存
os.environ["NETKET_EXPERIMENTAL_SHARDING"] = "1"  # 启用NetKet的分片功能
os.environ["XLA_PYTHON_CLIENT_ALLOCATOR"] = "platform"  # 使用平台特定的内存分配器
os.environ["XLA_PYTHON_CLIENT_PREALLOCATE"] = "false"  # 禁用预分配

import jax
import netket as nk

# 导入自定义模块
from src.runner import SSRunner

def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(description='Shastry-Sutherland模型训练')

    # 系统参数
    parser.add_argument('L', type=int, help='晶格尺寸')
    parser.add_argument('J2', type=float, help='J2耦合强度')
    parser.add_argument('J1', type=float, help='J1耦合强度')

    # 采样参数
    parser.add_argument('--n_samples', type=int, default=16384, help='样本数量')
    parser.add_argument('--n_chains', type=int, default=None, help='并行链数（默认等于n_samples，增加可加速采样）')
    parser.add_argument('--n_discard_per_chain', type=int, default=0, help='每次参数更新后丢弃的样本数（用于再热化）')
    parser.add_argument('--d_max', type=int, default=None, help='最大交换距离（None表示使用晶格最大距离，2-3推荐）')
    parser.add_argument('--chunk_size', type=int, default=4096, help='批处理大小')

    # 学习率调度参数
    parser.add_argument('--max_lr', type=float, default=0.02, help='最大学习率')
    parser.add_argument('--min_lr', type=float, default=0.002, help='最小学习率')
    parser.add_argument('--warmup_ratio', type=float, default=0.0, help='Warm-up阶段占总训练步数的百分比')

    # 退火参数
    parser.add_argument('--n_cycles', type=int, default=3, help='退火周期数')
    parser.add_argument('--initial_period', type=int, default=200, help='初始周期长度')
    parser.add_argument('--period_mult', type=float, default=2.0, help='周期倍数')

    # ViT模型参数
    parser.add_argument('--d_model', type=int, default=128, help='嵌入维度')
    parser.add_argument('--num_layers', type=int, default=6, help='Transformer层数')
    parser.add_argument('--n_heads', type=int, default=8, help='注意力头数')
    parser.add_argument('--patch_size', type=int, default=2, help='patch大小')
    parser.add_argument('--use_rpe', action='store_true', default=True, help='使用相对位置编码')
    parser.add_argument('--dropout_rate', type=float, default=0.0, help='Dropout比率')
    parser.add_argument('--mlp_ratio', type=float, default=4.0, help='MLP隐藏层维度相对于d_model的倍数')
    parser.add_argument('--use_mixed_precision', action='store_true', default=True, help='使用混合精度训练')

    # 对称性参数
    parser.add_argument('--use_symmetry', action='store_true', default=True, help='使用点群对称性约束（默认启用）')
    parser.add_argument('--no_symmetry', action='store_true', help='禁用对称性约束')
    parser.add_argument('--character_id', type=int, default=None, help='对称性特征标ID（默认None表示全对称表示）')
    parser.add_argument('--use_partial_equivariance', action='store_true', help='使用部分等变性（嵌入层等变，后续层普通）')

    # Checkpoint参数
    parser.add_argument('--enable_checkpoint', action='store_true', help='启用checkpoint')
    parser.add_argument('--save_interval', type=int, default=200, help='checkpoint保存间隔')
    parser.add_argument('--resume_from', type=str, default=None, help='从checkpoint恢复')
    parser.add_argument('--keep_history', action='store_true', default=True, help='保留checkpoint历史')

    # 优化器参数
    parser.add_argument('--diag_shift', type=float, default=0.01, help='SR对角位移')
    parser.add_argument('--grad_clip', type=float, default=1.0, help='梯度裁剪')

    return parser


def run_vit_simulation(args):
    """
    利用Vision Transformer (ViT)进行变分量子态优化模拟，
    使用新的SSRunner框架。
    """
    # 创建配置对象
    # 处理对称性参数：如果指定了--no_symmetry，则禁用对称性
    use_symmetry = not args.no_symmetry if args.no_symmetry else args.use_symmetry

    model_config = {
        'd_model': args.d_model,
        'num_layers': args.num_layers,
        'n_heads': args.n_heads,
        'patch_size': args.patch_size,
        'use_rpe': args.use_rpe,
        'dropout_rate': args.dropout_rate,
        'mlp_ratio': args.mlp_ratio,
        'use_mixed_precision': args.use_mixed_precision,
        'use_symmetry': use_symmetry,
        'character_id': args.character_id,
        'use_partial_equivariance': args.use_partial_equivariance
    }

    training_config = {
        'n_cycles': args.n_cycles,
        'initial_period': args.initial_period,
        'period_mult': args.period_mult,
        'max_lr': args.max_lr,
        'min_lr': args.min_lr,
        'warmup_ratio': args.warmup_ratio,
        'n_samples': args.n_samples,
        'n_chains': args.n_chains if args.n_chains is not None else args.n_samples,
        'd_max': args.d_max,
        'n_discard_per_chain': args.n_discard_per_chain,
        'chunk_size': args.chunk_size,
        'diag_shift': args.diag_shift,
        'grad_clip': args.grad_clip
    }

    checkpoint_config = {
        'enable': args.enable_checkpoint,
        'save_interval': args.save_interval,
        'resume_from': args.resume_from,
        'keep_history': args.keep_history
    }

    # 创建运行器实例
    runner = SSRunner(
        L=args.L,
        J1=args.J1,
        J2=args.J2,
        model_class="ViT",
        model_config=model_config,
        training_config=training_config,
        checkpoint_config=checkpoint_config
    )

    # 设置模型并运行
    runner.setup_model()
    runner.run()

    return runner


def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()

    print("="*60)
    print("Shastry-Sutherland模型训练 (ViT + MinSR)")
    print("="*60)
    print(f"系统参数: L={args.L}, J1={args.J1}, J2={args.J2}")
    print(f"ViT模型: d_model={args.d_model}, layers={args.num_layers}, heads={args.n_heads}, patch={args.patch_size}")
    print(f"优化器: diag_shift={args.diag_shift}, grad_clip={args.grad_clip}")
    print(f"学习率调度: CosineAnnealing([{args.min_lr}, {args.max_lr}])")
    print(f"退火参数: 周期数={args.n_cycles}, 初始周期={args.initial_period}, 倍增={args.period_mult}")
    print(f"Checkpoint: {'启用' if args.enable_checkpoint else '禁用'}")
    print(f"Sharding: {nk.config.netket_experimental_sharding}")
    print(f"设备: {jax.devices()}")
    print("="*60)

    try:
        run_vit_simulation(args)
        print("="*60)
        print("训练完成！")
        print("="*60)
    except Exception as e:
        print(f"L={args.L}, J2={args.J2}, J1={args.J1} 的模拟失败: {str(e)}")
        raise


if __name__ == "__main__":
    main()

"""
运行混合方法分析的完整工作流
"""

import os
import sys
import argparse
import numpy as np
import pickle
import re
from pathlib import Path

# 添加脚本目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# GPU配置
os.environ["XLA_FLAGS"] = "--xla_gpu_cuda_data_dir=/usr/local/cuda"
os.environ["NETKET_EXPERIMENTAL_SHARDING"] = "1"
os.environ["XLA_PYTHON_CLIENT_ALLOCATOR"] = "platform"
os.environ["XLA_PYTHON_CLIENT_PREALLOCATE"] = "false"

import jax.numpy as jnp
import netket as nk

project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.models.vit_state import create_quantum_state
from hybrid_phase_detection import (
    hybrid_phase_detection,
    plot_hybrid_results,
    plot_vae_analysis
)


class FeatureExtractor:
    """特征提取器"""

    def __init__(self, L, J2, num_layers=4, d_model=60, n_heads=10, patch_size=2, checkpoint='latest',
                 n_samples=1000, n_chains=None, d_max=None):
        self.L = L
        self.J2 = J2
        self.num_layers = num_layers
        self.d_model = d_model
        self.n_heads = n_heads
        self.patch_size = patch_size
        self.checkpoint = checkpoint
        self.n_samples = n_samples
        self.n_chains = n_chains
        self.d_max = d_max
        self._checkpoint_cache = {}

    def _resolve_checkpoint_path(self, J1):
        """解析checkpoint路径"""
        checkpoint_dir = f"saved_models/L={self.L}/J2={self.J2:.2f}/J1={J1:.2f}/training/checkpoints"

        if self.checkpoint != 'latest':
            candidate_path = os.path.join(checkpoint_dir, f"{self.checkpoint}.pkl")
            return candidate_path if os.path.exists(candidate_path) else None

        pattern = re.compile(r"checkpoint_iter_(\d{6})\.pkl$")
        best = None
        best_iter = -1
        if not os.path.isdir(checkpoint_dir):
            return None
        for fname in os.listdir(checkpoint_dir):
            m = pattern.match(fname)
            if not m:
                continue
            it = int(m.group(1))
            if it > best_iter:
                best_iter = it
                best = os.path.join(checkpoint_dir, fname)
        return best

    def _load_checkpoint(self, checkpoint_path):
        """加载checkpoint"""
        with open(checkpoint_path, 'rb') as f:
            return pickle.load(f)

    def _extract_param_features(self, params):
        """从参数提取特征"""
        features = {}
        layer_idx = 0

        for key, value in params.items():
            if isinstance(value, dict):
                for subkey, subvalue in value.items():
                    if isinstance(subvalue, jnp.ndarray):
                        prefix = f'layer{layer_idx}_{subkey}'
                        features[f'{prefix}_mean_real'] = float(jnp.mean(jnp.real(subvalue)))
                        features[f'{prefix}_std_real'] = float(jnp.std(jnp.real(subvalue)))
                        features[f'{prefix}_mean_imag'] = float(jnp.mean(jnp.imag(subvalue)))
                        features[f'{prefix}_std_imag'] = float(jnp.std(jnp.imag(subvalue)))
                        features[f'{prefix}_norm'] = float(jnp.linalg.norm(subvalue.flatten()))
                        layer_idx += 1
            elif isinstance(value, jnp.ndarray):
                prefix = f'param_{key}'
                features[f'{prefix}_mean_real'] = float(jnp.mean(jnp.real(value)))
                features[f'{prefix}_std_real'] = float(jnp.std(jnp.real(value)))
                features[f'{prefix}_mean_imag'] = float(jnp.mean(jnp.imag(value)))
                features[f'{prefix}_std_imag'] = float(jnp.std(jnp.imag(value)))
                features[f'{prefix}_norm'] = float(jnp.linalg.norm(value.flatten()))

        return features

    def extract_features(self, J1):
        """提取特征"""
        checkpoint_path = self._resolve_checkpoint_path(J1)
        if checkpoint_path is None or not os.path.exists(checkpoint_path):
            raise FileNotFoundError(f"Checkpoint not found for J1={J1:.4f}")

        # 创建量子态
        vqs, _, _, _ = create_quantum_state(
            self.L, self.J2, J1,
            n_samples=2**12,
            n_chains=self.n_chains,
            n_discard=0,
            chunk_size=2**10,
            d_max=self.d_max,
            num_layers=self.num_layers,
            d_model=self.d_model,
            n_heads=self.n_heads,
            patch_size=self.patch_size
        )

        # 加载参数
        checkpoint_data = self._load_checkpoint(checkpoint_path)
        params = checkpoint_data['parameters']
        vqs.parameters = params

        # 提取特征
        features = self._extract_param_features(params)

        # 转换为数组
        feature_keys = sorted(features.keys())
        feature_array = np.array([features[k] for k in feature_keys])

        return feature_array

    def get_samples(self, J1, n_samples=None):
        """获取采样"""
        checkpoint_path = self._resolve_checkpoint_path(J1)
        if checkpoint_path is None or not os.path.exists(checkpoint_path):
            raise FileNotFoundError(f"Checkpoint not found for J1={J1:.4f}")

        # 使用实例的n_samples如果没有指定
        if n_samples is None:
            n_samples = self.n_samples

        # 创建量子态
        vqs, _, _, _ = create_quantum_state(
            self.L, self.J2, J1,
            n_samples=n_samples,
            n_chains=self.n_chains,
            n_discard=0,
            chunk_size=2**10,
            d_max=self.d_max,
            num_layers=self.num_layers,
            d_model=self.d_model,
            n_heads=self.n_heads,
            patch_size=self.patch_size
        )

        # 加载参数
        checkpoint_data = self._load_checkpoint(checkpoint_path)
        params = checkpoint_data['parameters']
        vqs.parameters = params

        # 采样
        vqs.sample()
        samples = np.array(vqs.samples)

        return samples


def load_or_extract_features(L: int, J2: float, J1_min: float, J1_max: float,
                            J1_step: float, checkpoint: str = 'latest',
                            num_layers: int = 4, d_model: int = 60, n_heads: int = 10, patch_size: int = 2,
                            n_samples: int = 1000, n_chains: int = None, d_max: float = None) -> tuple:
    """
    加载或提取特征和样本

    Returns:
        (param_features_dict, samples_dict, J1_values)
    """
    print("\n" + "="*80)
    print("STEP 1: Feature Extraction")
    print("="*80)

    extractor = FeatureExtractor(
        L=L, J2=J2, num_layers=num_layers, d_model=d_model, n_heads=n_heads, patch_size=patch_size,
        checkpoint=checkpoint, n_samples=n_samples, n_chains=n_chains, d_max=d_max
    )

    J1_values = np.arange(J1_min, J1_max + J1_step/2, J1_step)
    param_features_dict = {}
    samples_dict = {}

    for J1 in J1_values:
        print(f"\nProcessing J1={J1:.4f}...")

        try:
            features = extractor.extract_features(J1)
            samples = extractor.get_samples(J1)
            
            param_features_dict[J1] = features
            samples_dict[J1] = samples
            
            print(f"  ✓ Features shape: {features.shape}")
            print(f"  ✓ Samples shape: {samples.shape}")
            
        except Exception as e:
            print(f"  ✗ Error: {e}")
            continue
    
    print(f"\n✓ Extracted features for {len(param_features_dict)} J1 values")
    
    return param_features_dict, samples_dict, list(J1_values)


def main():
    parser = argparse.ArgumentParser(description='Run hybrid phase detection analysis')
    
    # 系统参数
    parser.add_argument('--L', type=int, default=5, help='System size')
    parser.add_argument('--J2', type=float, default=0.00, help='J2 coupling')
    parser.add_argument('--J1_min', type=float, default=0.00, help='Minimum J1')
    parser.add_argument('--J1_max', type=float, default=0.10, help='Maximum J1')
    parser.add_argument('--J1_step', type=float, default=0.01, help='J1 step')
    
    # ViT模型参数
    parser.add_argument('--num_layers', type=int, default=4, help='Number of transformer layers')
    parser.add_argument('--d_model', type=int, default=60, help='Embedding dimension')
    parser.add_argument('--n_heads', type=int, default=10, help='Number of attention heads')
    parser.add_argument('--patch_size', type=int, default=2, help='Patch size')
    parser.add_argument('--checkpoint', type=str, default='latest', help='Checkpoint to use')
    
    # 表征学习参数
    parser.add_argument('--vae_latent_dim', type=int, default=16, help='VAE latent dimension')
    parser.add_argument('--vae_epochs', type=int, default=50, help='VAE training epochs')
    parser.add_argument('--fusion_weights', type=float, nargs=2, default=[0.5, 0.5],
                       help='Fusion weights for [param_features, vae_latent]')

    # 采样参数
    parser.add_argument('--n_samples', type=int, default=1000, help='Number of samples')
    parser.add_argument('--n_chains', type=int, default=None, help='Number of parallel chains (None for default)')
    parser.add_argument('--d_max', type=float, default=None, help='Maximum exchange distance (None for lattice max)')

    # 输出参数
    parser.add_argument('--output_dir', type=str,
                       default='measurements/representation_learning/L=5/J2=0.00',
                       help='Output directory')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    print("\n" + "="*80)
    print("HYBRID PHASE DETECTION WORKFLOW")
    print("="*80)
    print(f"System: L={args.L}, J2={args.J2}")
    print(f"J1 range: {args.J1_min} to {args.J1_max} (step {args.J1_step})")
    print(f"VAE latent dim: {args.vae_latent_dim}")
    print(f"Fusion weights: param={args.fusion_weights[0]}, vae={args.fusion_weights[1]}")
    print(f"Output: {args.output_dir}")
    print("="*80)
    
    # 步骤1：提取特征
    param_features_dict, samples_dict, J1_values = load_or_extract_features(
        L=args.L,
        J2=args.J2,
        J1_min=args.J1_min,
        J1_max=args.J1_max,
        J1_step=args.J1_step,
        checkpoint=args.checkpoint,
        num_layers=args.num_layers,
        d_model=args.d_model,
        n_heads=args.n_heads,
        patch_size=args.patch_size,
        n_samples=args.n_samples,
        n_chains=args.n_chains,
        d_max=args.d_max
    )
    
    if len(param_features_dict) < 2:
        print("✗ Not enough data for analysis")
        return
    
    # 步骤2：混合方法分析
    print("\n" + "="*80)
    print("STEP 2: Hybrid Phase Detection")
    print("="*80)
    
    results = hybrid_phase_detection(
        param_features_dict=param_features_dict,
        samples_dict=samples_dict,
        J1_values=J1_values,
        output_dir=args.output_dir,
        vae_latent_dim=args.vae_latent_dim,
        fusion_weights=tuple(args.fusion_weights)
    )
    
    # 步骤3：绘制结果
    print("\n" + "="*80)
    print("STEP 3: Visualization")
    print("="*80)
    
    plot_hybrid_results(results, args.output_dir)
    plot_vae_analysis(results['vae_analysis'], args.output_dir)
    
    # 步骤4：生成总结报告
    print("\n" + "="*80)
    print("STEP 4: Summary Report")
    print("="*80)
    
    summary = f"""
HYBRID PHASE DETECTION ANALYSIS REPORT
{'='*80}

System Configuration:
  - System size (L): {args.L}
  - J2 coupling: {args.J2}
  - J1 range: {args.J1_min} to {args.J1_max}
  - Number of J1 values: {len(J1_values)}

Representation Learning:
  - VAE latent dimension: {args.vae_latent_dim}
  - Parameter features dimension: {results['param_features'].shape[0] if results['param_features'].ndim == 1 else results['param_features'].shape[1]}
  - Fused features dimension: {results['fused_features'].shape[1]}

Clustering Results:
  - Optimal number of clusters: {results['n_clusters']}
  - Silhouette score: {results['silhouette_score']:.4f}

Phase Boundaries Detected (from VAE):
  - Number of boundaries: {len(results['phase_boundaries_vae'])}
  - Boundaries at J1: {[f'{b:.4f}' for b in results['phase_boundaries_vae']]}

Reconstruction Error Analysis:
  - Min error: {min(results['vae_analysis']['recon_errors'].values()):.6f}
  - Max error: {max(results['vae_analysis']['recon_errors'].values()):.6f}
  - Mean error: {np.mean(list(results['vae_analysis']['recon_errors'].values())):.6f}

Output Files:
  - hybrid_results.pkl: Complete results dictionary
  - hybrid_results.png: Visualization of all results
  - vae_analysis.png: VAE-specific analysis plots

{'='*80}
"""
    
    print(summary)
    
    # 保存报告
    with open(os.path.join(args.output_dir, 'hybrid_summary.txt'), 'w') as f:
        f.write(summary)
    
    print(f"✓ Summary saved to {args.output_dir}/hybrid_summary.txt")
    print("\n✓ Hybrid phase detection analysis completed!")


if __name__ == '__main__':
    main()


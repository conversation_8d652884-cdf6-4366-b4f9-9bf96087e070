#!/usr/bin/env python3
"""
分析脚本,用于分析Shastry-Sutherland模型的ViT量子态。
"""

import os
import sys
import pickle
import argparse
from pathlib import Path

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

# 设置环境变量
os.environ["XLA_FLAGS"] = "--xla_gpu_cuda_data_dir=/usr/local/cuda"
os.environ["NETKET_EXPERIMENTAL_SHARDING"] = "1"  # 保留NetKet的分片功能
os.environ["XLA_PYTHON_CLIENT_ALLOCATOR"] = "platform"
os.environ["XLA_PYTHON_CLIENT_PREALLOCATE"] = "false"
os.environ["JAX_PLATFORM_NAME"] = "gpu"

import argparse
import traceback
import numpy as np
import pickle

# 导入自定义模块
from src.utils.logging import log_message
from src.utils.plotting import plot_structure_factor
from src.models.vit_state import create_quantum_state
from src.analysis.structure_factors import (
    calculate_spin_structure_factor,
    calculate_plaquette_structure_factor,
    calculate_dimer_structure_factor,
    calculate_diag_dimer_structure_factor,
    generate_shared_samples,
)

def load_quantum_state_from_checkpoint(file_path, L, J2, J1, n_samples=2**14, n_chains=None,
                                       n_discard=0, chunk_size=2**10, d_max=None,
                                       num_layers=4, d_model=60, n_heads=10, patch_size=2, log_file=None):
    """
    从checkpoint格式加载量子态参数

    参数:
    file_path: checkpoint文件路径
    L: 晶格大小
    J2: J2耦合强度
    J1: J1耦合强度
    n_samples: 采样数量
    n_chains: 并行链数（如果为None则使用默认值）
    n_discard: 丢弃的样本数
    chunk_size: 批处理大小
    d_max: 最大交换距离（如果为None则使用晶格最大距离）
    num_layers: Transformer层数
    d_model: 嵌入维度
    n_heads: 注意力头数
    patch_size: patch大小
    log_file: 日志文件路径（可选）

    返回:
    vqs: 变分量子态
    lattice: 晶格
    hilbert: 希尔伯特空间
    hamiltonian: 哈密顿量
    """
    # 创建量子态
    vqs, lattice, hilbert, hamiltonian = create_quantum_state(
        L, J2, J1,
        n_samples=n_samples,
        n_chains=n_chains,
        n_discard=n_discard,
        chunk_size=chunk_size,
        d_max=d_max,
        num_layers=num_layers,
        d_model=d_model,
        n_heads=n_heads,
        patch_size=patch_size
    )
    
    # 加载checkpoint数据
    with open(file_path, "rb") as f:
        checkpoint_data = pickle.load(f)
    
    # 从checkpoint数据中提取参数
    if not isinstance(checkpoint_data, dict) or 'parameters' not in checkpoint_data:
        raise ValueError(f"无效的checkpoint格式: {file_path}")
    
    parameters = checkpoint_data['parameters']
    if log_file:
        log_message(log_file, f"✓ 从checkpoint加载参数: {checkpoint_data.get('iteration', 'unknown')}")
        if checkpoint_data.get('energy'):
            energy = checkpoint_data['energy']
            log_message(log_file, f"  - 能量: {energy['mean']:.6f} ± {energy['error']:.6f}")
    
    # 设置参数
    vqs.parameters = parameters
    
    return vqs, lattice, hilbert, hamiltonian

def find_latest_checkpoint(checkpoint_dir):
    """查找最新的checkpoint文件"""
    checkpoint_path = Path(checkpoint_dir)
    if not checkpoint_path.exists():
        return None
    
    # 查找所有checkpoint_iter_*.pkl文件
    checkpoint_files = list(checkpoint_path.glob("checkpoint_iter_*.pkl"))
    if not checkpoint_files:
        return None
    
    # 按文件名排序，返回最新的
    checkpoint_files.sort()
    latest_checkpoint = checkpoint_files[-1]
    return latest_checkpoint.stem  # 返回不含.pkl的名称

def main(args=None):
    """主函数,接受命令行参数L, J2, J1, num_layers, d_model, n_heads, patch_size, checkpoint"""
    # 设置命令行参数解析
    parser = argparse.ArgumentParser(description='分析量子态结构因子')
    parser.add_argument('--L', type=int, required=True, help='晶格大小')
    parser.add_argument('--J2', type=float, required=True, help='J2耦合强度')
    parser.add_argument('--J1', type=float, required=True, help='J1耦合强度')
    parser.add_argument('--num_layers', type=int, default=4, help='Transformer层数')
    parser.add_argument('--d_model', type=int, default=60, help='嵌入维度')
    parser.add_argument('--n_heads', type=int, default=10, help='注意力头数')
    parser.add_argument('--patch_size', type=int, default=2, help='patch大小')
    parser.add_argument('--checkpoint', type=str, default=None, help='checkpoint文件名（不含.pkl扩展名），默认使用最新的checkpoint')
    parser.add_argument('--n_samples', type=int, default=1048576, help='采样数目（默认2^20=1048576）')
    parser.add_argument('--n_chains', type=int, default=None, help='并行链数（默认为None，使用n_samples的1/128）')
    parser.add_argument('--d_max', type=float, default=None, help='最大交换距离（默认为None，使用晶格最大距离）')
    args = parser.parse_args(args)

    # 获取参数
    L = args.L
    J2 = args.J2
    J1 = args.J1
    num_layers = args.num_layers
    d_model = args.d_model
    n_heads = args.n_heads
    patch_size = args.patch_size
    checkpoint_name = args.checkpoint
    n_samples = args.n_samples
    n_chains = args.n_chains
    d_max = args.d_max

    # 处理空字符串的情况（从配置文件或命令行传递的空值）
    if checkpoint_name == "":
        checkpoint_name = None

    # 模型保存在saved_models目录
    model_dir = f"saved_models/L={L}/J2={J2:.2f}/J1={J1:.2f}"

    # 如果未指定checkpoint，自动查找最新的
    if checkpoint_name is None:
        checkpoint_dir = os.path.join(model_dir, "checkpoints")
        checkpoint_name = find_latest_checkpoint(checkpoint_dir)
        if checkpoint_name is None:
            print(f"错误: 在 {checkpoint_dir} 中未找到checkpoint文件")
            return
        print(f"自动选择最新checkpoint: {checkpoint_name}")

    # 结构因子测量结果保存到measurements/structure_factor
    measurement_dir = f"measurements/structure_factor/L={L}/J2={J2:.2f}/J1={J1:.2f}/{checkpoint_name}"
    os.makedirs(measurement_dir, exist_ok=True)

    # 创建日志文件
    analyze_log = os.path.join(measurement_dir, f"structure_factor_L={L}_J2={J2:.2f}_J1={J1:.2f}_{checkpoint_name}.log")

    try:
        # 构建checkpoint文件路径
        checkpoint_file = os.path.join(model_dir, "checkpoints", f"{checkpoint_name}.pkl")
        
        # 检查文件存在性
        if not os.path.exists(checkpoint_file):
            log_message(analyze_log, f"错误: 未找到checkpoint文件 {checkpoint_file}")
            return

        log_message(analyze_log, f"使用checkpoint文件: {checkpoint_file}")

        # 加载量子态 - 使用较小的采样数初始化
        vqs, lattice, _, _ = load_quantum_state_from_checkpoint(
            checkpoint_file, L, J2, J1,
            n_samples=2**12,  # 初始使用较小的采样数
            n_chains=n_chains,
            n_discard=0,
            chunk_size=2**10,  # 使用较小的chunk_size
            d_max=d_max,
            num_layers=num_layers,
            d_model=d_model,
            n_heads=n_heads,
            patch_size=patch_size,
            log_file=analyze_log
        )

        # 在加载量子态后立即生成共享样本,供后续所有结构因子计算使用
        log_message(analyze_log, "="*80)
        log_message(analyze_log, f"加载量子态: L={L}, J2={J2:.2f}, J1={J1:.2f}, checkpoint={checkpoint_name}")
        log_message(analyze_log, f"使用采样数目: {n_samples}")
        generate_shared_samples(vqs, n_samples=n_samples, log_file=analyze_log)

        # 创建子目录用于不同类型的结构因子
        spin_dir = os.path.join(measurement_dir, "spin")
        plaquette_dir = os.path.join(measurement_dir, "plaquette")
        dimer_dir = os.path.join(measurement_dir, "dimer")
        diag_dimer_dir = os.path.join(measurement_dir, "diag_dimer")

        os.makedirs(spin_dir, exist_ok=True)
        os.makedirs(plaquette_dir, exist_ok=True)
        os.makedirs(dimer_dir, exist_ok=True)
        os.makedirs(diag_dimer_dir, exist_ok=True)

        # 计算自旋因子
        log_message(analyze_log, "="*80)
        k_points_tuple, (spin_sf_real, spin_sf_imag) = calculate_spin_structure_factor(vqs, lattice, L, spin_dir, analyze_log)
        plot_structure_factor(k_points_tuple, spin_sf_real, L, J2, J1, "Spin", spin_dir)
        # 绘制虚部结构因子  
        spin_data_storage = np.load(os.path.join(spin_dir, "spin_data.npy"), allow_pickle=True).item()
        # 使用新的数据结构读取误差数据
        errors_data = spin_data_storage['correlations']['errors']
        # 计算复数误差的幅度
        error_magnitudes = np.abs(errors_data)
        log_message(analyze_log, f"自旋相关函数平均误差: {np.mean(error_magnitudes):.6f}")
                
        # # 计算二聚体结构因子
        # log_message(analyze_log, "="*80)
        # k_points_tuple, (dimer_sf_real, dimer_sf_imag) = calculate_dimer_structure_factor(vqs, lattice, L, dimer_dir, analyze_log)
        # plot_structure_factor(k_points_tuple, dimer_sf_real, L, J2, J1, "Dimer", dimer_dir)
        # # 从存储结构中加载数据
        # dimer_data_storage = np.load(os.path.join(dimer_dir, "dimer_data.npy"), allow_pickle=True).item()
        # # 使用新的数据结构读取误差数据
        # dimer_errors = dimer_data_storage['correlations']['errors']
        # dimer_error_magnitudes = np.abs(dimer_errors)
        # log_message(analyze_log, f"二聚体相关函数平均误差: {np.mean(dimer_error_magnitudes):.6f}")

        # # 计算对角二聚体结构因子
        # log_message(analyze_log, "="*80)
        # k_points_tuple, (diag_dimer_sf_real, diag_dimer_sf_imag) = calculate_diag_dimer_structure_factor(vqs, lattice, L, diag_dimer_dir, analyze_log)
        # plot_structure_factor(k_points_tuple, diag_dimer_sf_real, L, J2, J1, "Diag Dimer", diag_dimer_dir)
        # # 从存储结构中加载数据
        # diag_dimer_data_storage = np.load(os.path.join(diag_dimer_dir, "diag_dimer_data.npy"), allow_pickle=True).item()
        # # 使用新的数据结构读取误差数据
        # diag_dimer_errors = diag_dimer_data_storage['correlations']['errors']
        # diag_dimer_error_magnitudes = np.abs(diag_dimer_errors)
        # log_message(analyze_log, f"对角二聚体相关函数平均误差: {np.mean(diag_dimer_error_magnitudes):.6f}")
        
        # # 计算简盘因子
        # log_message(analyze_log, "="*80)
        # k_points_tuple, (plaq_sf_real, plaq_sf_imag) = calculate_plaquette_structure_factor(vqs, lattice, L, plaquette_dir, analyze_log)
        # plot_structure_factor(k_points_tuple, plaq_sf_real, L, J2, J1, "Plaquette", plaquette_dir)
        # # 从存储结构中加载数据
        # plaquette_data_storage = np.load(os.path.join(plaquette_dir, "plaquette_data.npy"), allow_pickle=True).item()
        # # 使用新的数据结构读取误差数据
        # plaquette_errors = plaquette_data_storage['correlations']['errors']
        # plaquette_error_magnitudes = np.abs(plaquette_errors)
        # log_message(analyze_log, f"简盘相关函数平均误差: {np.mean(plaquette_error_magnitudes):.6f}")

        # log_message(analyze_log, "="*80)
        # log_message(analyze_log, "所有分析完成")

    except Exception as e:
        log_message(analyze_log, "!"*80)
        log_message(analyze_log, f"处理 L={L}, J2={J2:.2f}, J1={J1:.2f} 时出错: {str(e)}")
        log_message(analyze_log, traceback.format_exc())
        log_message(analyze_log, "!"*80)

if __name__ == "__main__":
    main()

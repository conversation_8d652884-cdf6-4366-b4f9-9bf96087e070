#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
混合训练脚本：NQS + 归一化流采样器

实现NQS波函数和归一化流采样器的联合训练，
使用解耦梯度优化两个模型的参数。
"""

import argparse
import os
import sys
import time
import pickle
from datetime import datetime
import pytz

import jax
import jax.numpy as jnp
import netket as nk
import optax
from jax import random

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.models.vit_state import create_quantum_state
from src.models.nf_sampler import create_nf_sampler
from src.training.hybrid_vmc import (
    create_hybrid_vmc_state, 
    train_hybrid_vmc,
    hybrid_training_step
)
from src.utils.logging import log_message


def create_output_directories(L, J2, J1):
    """创建输出目录结构"""
    base_dir = "saved_models"
    model_dir = os.path.join(base_dir, f"L={L}", f"J2={J2:.2f}", f"J1={J1:.2f}")
    
    # 创建主目录和子目录
    os.makedirs(model_dir, exist_ok=True)
    os.makedirs(os.path.join(model_dir, "checkpoints"), exist_ok=True)
    os.makedirs(os.path.join(model_dir, "logs"), exist_ok=True)
    
    return model_dir


def setup_logging(model_dir):
    """设置日志文件"""
    log_dir = os.path.join(model_dir, "logs")
    timestamp = datetime.now(pytz.timezone('Asia/Singapore')).strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"hybrid_training_{timestamp}.log")
    return log_file


def save_hybrid_checkpoint(model_dir, iteration, hybrid_state, aux_data, keep_history=True):
    """保存混合训练的checkpoint"""
    checkpoint_dir = os.path.join(model_dir, "checkpoints")
    
    # 创建checkpoint数据
    checkpoint_data = {
        'iteration': iteration,
        'timestamp': datetime.now(pytz.timezone('Asia/Singapore')).isoformat(),
        'nqs_params': hybrid_state.nqs_vqs.parameters,  # 从MCState获取参数
        'nf_params': hybrid_state.nf_params,
        'nf_opt_state': hybrid_state.nf_opt_state,  # 只有NF优化器状态
        'energy_history': hybrid_state.energy_history,
        'nf_loss_history': hybrid_state.nf_loss_history,
        'total_loss_history': hybrid_state.total_loss_history,
        'energy': {
            'mean': float(aux_data.get('energy_loss', 0)),
            'variance': float(aux_data.get('energy_var', 0)),
            'std': float(aux_data.get('energy_std', 0))
        },
        'nf_loss': float(aux_data.get('nf_loss', 0)),
        'total_loss': float(aux_data.get('total_loss', 0))
    }
    
    # 保存checkpoint
    if keep_history:
        checkpoint_file = os.path.join(checkpoint_dir, f"hybrid_checkpoint_iter_{iteration:06d}.pkl")
    else:
        checkpoint_file = os.path.join(checkpoint_dir, "hybrid_checkpoint_latest.pkl")
    
    with open(checkpoint_file, 'wb') as f:
        pickle.dump(checkpoint_data, f)
    
    return checkpoint_file


def load_hybrid_checkpoint(checkpoint_path):
    """加载混合训练的checkpoint"""
    if not os.path.exists(checkpoint_path):
        return None
    
    try:
        with open(checkpoint_path, 'rb') as f:
            checkpoint_data = pickle.load(f)
        return checkpoint_data
    except Exception as e:
        print(f"加载checkpoint失败: {e}")
        return None


def run_hybrid_training(args):
    """运行混合训练"""
    print("="*80)
    print("混合NQS+NF训练")
    print("="*80)
    
    # 创建输出目录
    model_dir = create_output_directories(args.L, args.J2, args.J1)
    log_file = setup_logging(model_dir)
    
    print(f"模型目录: {model_dir}")
    print(f"日志文件: {log_file}")
    
    # 1. 创建量子系统和NQS模型
    print("\n1. 创建量子系统和NQS模型...")
    # 在混合训练中，chunk_size用于NetKet SR计算时的批处理
    # 直接设置为n_samples，避免整除问题（虽然不使用MCMC采样器，但SR计算仍需要chunk_size）
    chunk_size = args.n_samples
    vqs, lattice, hilbert, hamiltonian = create_quantum_state(
        L=args.L,
        J2=args.J2,
        J1=args.J1,
        n_samples=args.n_samples,
        n_chains=args.n_chains,
        n_discard=args.n_discard_per_chain,
        chunk_size=chunk_size,
        d_max=args.d_max,
        num_layers=args.num_layers,
        d_model=args.d_model,
        n_heads=args.n_heads,
        patch_size=args.patch_size,
        use_rpe=args.use_rpe,
        use_symmetry=not args.no_symmetry,
        character_id=args.character_id,
        use_partial_equivariance=args.use_partial_equivariance
    )
    
    N_spins = hilbert.size
    print(f"✓ 创建了 {N_spins} 自旋的量子系统")
    print(f"✓ NQS模型参数数量: {sum(p.size for p in jax.tree_util.tree_leaves(vqs.parameters))}")
    
    # 2. 创建归一化流采样器
    print("\n2. 创建归一化流采样器...")
    nf_sampler = create_nf_sampler(
        n_spins=N_spins,
        n_layers=args.nf_layers,
        hidden_dims=tuple(args.nf_hidden_dims),
        use_mixed_precision=args.use_mixed_precision
    )
    
    # 初始化NF参数
    key = random.PRNGKey(args.seed)
    key_nf, key_train = random.split(key)
    dummy_key = random.PRNGKey(0)
    nf_params_full = nf_sampler.init(key_nf, dummy_key, args.n_samples)
    # 提取params层级，因为init返回的是{'params': ...}结构
    nf_params = nf_params_full['params']
    
    nf_param_count = sum(p.size for p in jax.tree_util.tree_leaves(nf_params))
    print(f"✓ NF采样器参数数量: {nf_param_count}")
    
    # 3. 创建混合VMC状态
    print("\n3. 创建混合VMC状态...")
    print("   NQS使用SR优化，NF使用Adam优化")
    
    # 获取SR参数（从args或使用默认值）
    initial_period = getattr(args, 'initial_period', 100)
    period_mult = getattr(args, 'period_mult', 2.0)
    max_lr = getattr(args, 'max_lr', 0.01)
    min_lr = getattr(args, 'min_lr', 1e-7)
    warmup_ratio = getattr(args, 'warmup_ratio', 0.0)
    diag_shift = getattr(args, 'diag_shift', 0.005)
    
    hybrid_state = create_hybrid_vmc_state(
        nqs_vqs=vqs,  # 使用MCState而不是单独的model和params
        hamiltonian=hamiltonian,
        nf_sampler=nf_sampler,
        nf_params=nf_params,
        nf_learning_rate=args.nf_lr,
        nf_optimizer_name=args.nf_optimizer,
        # SR参数
        initial_period=initial_period,
        period_mult=period_mult,
        max_lr=max_lr,
        min_lr=min_lr,
        warmup_ratio=warmup_ratio,
        diag_shift=diag_shift
    )
    
    print(f"✓ NQS优化器: SR (Stochastic Reconfiguration)")
    print(f"   SR最大学习率: {max_lr}")
    print(f"   SR最小学习率: {min_lr}")
    print(f"   SR对角位移: {diag_shift}")
    print(f"✓ NF优化器: {args.nf_optimizer}")
    print(f"✓ NF学习率: {args.nf_lr}")
    print(f"✓ NF损失权重: {args.nf_weight}")
    
    # 4. 检查是否从checkpoint恢复
    start_step = 0
    if args.resume_from:
        print(f"\n4. 从checkpoint恢复: {args.resume_from}")
        checkpoint_data = load_hybrid_checkpoint(args.resume_from)
        if checkpoint_data:
            # 恢复NQS参数（通过MCState）
            hybrid_state.nqs_vqs.parameters = checkpoint_data['nqs_params']
            # 恢复NF参数
            hybrid_state.nf_params = checkpoint_data['nf_params']
            # 恢复NF优化器状态
            hybrid_state.nf_opt_state = checkpoint_data['nf_opt_state']
            # 恢复历史记录
            hybrid_state.energy_history = checkpoint_data.get('energy_history', [])
            hybrid_state.nf_loss_history = checkpoint_data.get('nf_loss_history', [])
            hybrid_state.total_loss_history = checkpoint_data.get('total_loss_history', [])
            hybrid_state.step = checkpoint_data['iteration']
            start_step = checkpoint_data['iteration']
            print(f"✓ 从迭代 {start_step} 恢复")
        else:
            print("⚠️ 无法加载checkpoint，从头开始训练")
    
    # 5. 训练循环
    # 计算总迭代数（基于n_cycles）
    total_iters = 0
    current_period = args.initial_period
    for cycle in range(args.n_cycles):
        total_iters += int(current_period * (args.period_mult ** cycle))
    
    print(f"\n5. 开始混合训练 ({total_iters} 步，{args.n_cycles} 个退火周期)...")
    
    def training_callback(state, aux_data):
        """训练回调函数"""
        step = state.step
        
        # 保存checkpoint
        if args.enable_checkpoint and step % args.save_interval == 0:
            checkpoint_file = save_hybrid_checkpoint(
                model_dir, step, state, aux_data, args.keep_history
            )
            log_message(log_file, f"保存checkpoint: {os.path.basename(checkpoint_file)}")
        
        # 记录到日志文件（每步都写入，格式与标准训练一致）
        # 注意：主要日志已经在train_hybrid_vmc中输出，这里只记录额外的checkpoint信息
        # 如果需要，可以在这里添加额外的日志信息
    
    # 执行训练
    hybrid_state = train_hybrid_vmc(
        state=hybrid_state,
        n_cycles=args.n_cycles,
        initial_period=args.initial_period,
        period_mult=args.period_mult,
        warmup_ratio=args.warmup_ratio,
        n_samples=args.n_samples,
        nf_weight=args.nf_weight,
        key=key_train,
        log_interval=args.log_interval,
        callback=training_callback,
        energy_log=log_file
    )
    
    # 6. 保存最终结果
    print("\n6. 保存最终结果...")
    final_aux = {
        'energy_loss': hybrid_state.energy_history[-1] if hybrid_state.energy_history else 0,
        'nf_loss': hybrid_state.nf_loss_history[-1] if hybrid_state.nf_loss_history else 0,
        'total_loss': hybrid_state.total_loss_history[-1] if hybrid_state.total_loss_history else 0
    }
    
    final_checkpoint = save_hybrid_checkpoint(
        model_dir, hybrid_state.step, hybrid_state, final_aux, True
    )
    
    print(f"✓ 最终checkpoint: {final_checkpoint}")
    print(f"✓ 最终能量: {final_aux['energy_loss']:.8f}")
    print(f"✓ 最终NF损失: {final_aux['nf_loss']:.6f}")
    
    print("\n混合训练完成！")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="混合NQS+NF训练")
    
    # 系统参数
    parser.add_argument("L", type=int, help="晶格尺寸")
    parser.add_argument("J2", type=float, help="J2耦合强度")
    parser.add_argument("J1", type=float, help="J1耦合强度")
    
    # NQS模型参数
    parser.add_argument("--num_layers", type=int, default=4, help="Transformer层数")
    parser.add_argument("--d_model", type=int, default=96, help="嵌入维度")
    parser.add_argument("--n_heads", type=int, default=4, help="注意力头数")
    parser.add_argument("--patch_size", type=int, default=2, help="patch大小")
    parser.add_argument("--use_rpe", action="store_true", default=True, help="使用相对位置编码")
    parser.add_argument("--no_symmetry", action="store_true", help="禁用对称性约束")
    parser.add_argument("--character_id", type=int, help="对称性特征标ID")
    parser.add_argument("--use_partial_equivariance", action="store_true", help="使用部分等变性")
    parser.add_argument("--use_mixed_precision", action="store_true", default=True, help="使用混合精度")
    
    # NF采样器参数
    parser.add_argument("--nf_layers", type=int, default=8, help="NF层数")
    parser.add_argument("--nf_hidden_dims", type=int, nargs="+", default=[64, 64], help="NF隐藏层维度")
    
    # 训练参数（已废弃，使用n_cycles代替）
    # parser.add_argument("--n_steps", type=int, default=1000, help="训练步数（已废弃，使用n_cycles）")
    parser.add_argument("--n_samples", type=int, default=8192, help="每步采样数量")
    parser.add_argument("--n_chains", type=int, help="并行链数")
    parser.add_argument("--n_discard_per_chain", type=int, default=0, help="每链丢弃样本数")
    # chunk_size参数在混合训练中不使用（自动设置为n_samples）
    # parser.add_argument("--chunk_size", type=int, default=None, help="批处理大小（混合训练中自动设置为n_samples）")
    parser.add_argument("--d_max", type=float, help="最大交换距离")
    
    # SR参数（NQS优化器）
    parser.add_argument("--max_lr", type=float, default=0.01, help="SR最大学习率")
    parser.add_argument("--min_lr", type=float, default=1e-7, help="SR最小学习率")
    parser.add_argument("--initial_period", type=int, default=200, help="SR初始周期")
    parser.add_argument("--period_mult", type=float, default=2.0, help="SR周期倍增因子")
    parser.add_argument("--n_cycles", type=int, default=3, help="退火周期数")
    parser.add_argument("--warmup_ratio", type=float, default=0.03, help="SR warm-up比例")
    parser.add_argument("--diag_shift", type=float, default=0.005, help="SR对角位移")
    
    # NF优化器参数
    parser.add_argument("--nf_lr", type=float, default=1e-3, help="NF学习率")
    parser.add_argument("--nf_optimizer", type=str, default="adam", choices=["adam"], help="NF优化器（固定为adam）")
    parser.add_argument("--nf_weight", type=float, default=1.0, help="NF损失权重")
    
    # Checkpoint参数
    parser.add_argument("--enable_checkpoint", action="store_true", help="启用checkpoint")
    parser.add_argument("--save_interval", type=int, default=100, help="保存间隔")
    parser.add_argument("--resume_from", type=str, help="从checkpoint恢复")
    parser.add_argument("--keep_history", action="store_true", default=True, help="保留checkpoint历史")
    
    # 其他参数
    parser.add_argument("--seed", type=int, default=42, help="随机种子")
    parser.add_argument("--log_interval", type=int, default=10, help="日志输出间隔")
    
    args = parser.parse_args()
    
    # 设置默认值
    if args.n_chains is None:
        args.n_chains = args.n_samples
    
    # 运行训练
    run_hybrid_training(args)


if __name__ == "__main__":
    main()

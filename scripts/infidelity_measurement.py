#!/usr/bin/env python3
"""
Infidelity 分析脚本

用于计算相邻参数点之间波函数的 Infidelity，以判断相变点的位置。

基于论文 "Fidelity approach to quantum phase transitions" 的理论。

使用示例:
# 自动选择最新checkpoint（推荐）
python scripts/infidelity_measurement.py --L 4 --J2 1.0 --num_layers 4 --d_model 60 --n_heads 10 --patch_size 2 \
    --J1_values 0.76 0.77 0.78 0.79 0.80 --n_samples 1048576

# 或手动指定checkpoint
python scripts/infidelity_measurement.py --L 4 --J2 1.0 --num_layers 4 --d_model 60 --n_heads 10 --patch_size 2 \
    --J1_values 0.76 0.77 0.78 0.79 0.80 --checkpoint checkpoint_iter_002250 --n_samples 1048576
"""

import os
import sys
import pickle
import argparse
from pathlib import Path

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

# 设置环境变量
os.environ["XLA_FLAGS"] = "--xla_gpu_cuda_data_dir=/usr/local/cuda"
os.environ["NETKET_EXPERIMENTAL_SHARDING"] = "1"
os.environ["XLA_PYTHON_CLIENT_ALLOCATOR"] = "platform"
os.environ["XLA_PYTHON_CLIENT_PREALLOCATE"] = "false"
os.environ["JAX_PLATFORM_NAME"] = "gpu"

import argparse
import traceback
import numpy as np
import pickle

# 导入自定义模块
from src.utils.logging import log_message
from src.models.vit_state import create_quantum_state
from src.analysis.infidelity import (
    calculate_infidelity_scan,
    plot_infidelity_scan,
)


def load_quantum_state_from_checkpoint(file_path, L, J2, J1, n_samples=2**14, n_chains=None,
                                       n_discard=0, chunk_size=2**10, d_max=None,
                                       num_layers=4, d_model=60, n_heads=10, patch_size=2, log_file=None):
    """
    从checkpoint格式加载量子态参数

    参数:
    file_path: checkpoint文件路径
    L: 晶格大小
    J2: J2耦合强度
    J1: J1耦合强度
    n_samples: 采样数量
    n_chains: 并行链数（如果为None则使用默认值）
    n_discard: 丢弃的样本数
    chunk_size: 批处理大小
    d_max: 最大交换距离（如果为None则使用晶格最大距离）
    num_layers: Transformer层数
    d_model: 嵌入维度
    n_heads: 注意力头数
    patch_size: patch大小
    log_file: 日志文件路径（可选）

    返回:
    vqs: 变分量子态
    lattice: 晶格
    hilbert: 希尔伯特空间
    hamiltonian: 哈密顿量
    """
    # 创建量子态
    vqs, lattice, hilbert, hamiltonian = create_quantum_state(
        L, J2, J1,
        n_samples=n_samples,
        n_chains=n_chains,
        n_discard=n_discard,
        chunk_size=chunk_size,
        d_max=d_max,
        num_layers=num_layers,
        d_model=d_model,
        n_heads=n_heads,
        patch_size=patch_size
    )
    
    # 加载checkpoint数据
    with open(file_path, "rb") as f:
        checkpoint_data = pickle.load(f)
    
    # 从checkpoint数据中提取参数
    if not isinstance(checkpoint_data, dict) or 'parameters' not in checkpoint_data:
        raise ValueError(f"无效的checkpoint格式: {file_path}")
    
    parameters = checkpoint_data['parameters']
    if log_file:
        log_message(log_file, f"✓ 从checkpoint加载参数: {checkpoint_data.get('iteration', 'unknown')}")
        if checkpoint_data.get('energy'):
            energy = checkpoint_data['energy']
            log_message(log_file, f"  - 能量: {energy['mean']:.6f} ± {energy['error']:.6f}")
    
    # 设置参数
    vqs.parameters = parameters
    
    return vqs, lattice, hilbert, hamiltonian


def find_latest_checkpoint(checkpoint_dir):
    """查找最新的checkpoint文件"""
    checkpoint_path = Path(checkpoint_dir)
    if not checkpoint_path.exists():
        return None
    
    # 查找所有checkpoint_iter_*.pkl文件
    checkpoint_files = list(checkpoint_path.glob("checkpoint_iter_*.pkl"))
    if not checkpoint_files:
        return None
    
    # 按文件名排序，返回最新的
    checkpoint_files.sort()
    latest_checkpoint = checkpoint_files[-1]
    return latest_checkpoint.stem  # 返回不含.pkl的名称

def main(args=None):
    """主函数"""
    # 设置命令行参数解析
    parser = argparse.ArgumentParser(description='Infidelity 分析：计算相邻参数点之间的 Infidelity')
    parser.add_argument('--L', type=int, required=True, help='晶格大小')
    parser.add_argument('--J2', type=float, required=True, help='J2耦合强度')
    parser.add_argument('--J1_values', type=float, nargs='+', required=True,
                        help='J1值列表（空格分隔），例如: 0.76 0.77 0.78 0.79 0.80')
    parser.add_argument('--num_layers', type=int, default=4, help='Transformer层数')
    parser.add_argument('--d_model', type=int, default=60, help='嵌入维度')
    parser.add_argument('--n_heads', type=int, default=10, help='注意力头数')
    parser.add_argument('--patch_size', type=int, default=2, help='patch大小')
    parser.add_argument('--checkpoint', type=str, default=None,
                        help='checkpoint文件名（不含.pkl扩展名），默认使用最新的checkpoint')
    parser.add_argument('--n_samples', type=int, default=1048576,
                        help='采样数目（默认2^20=1048576）')
    parser.add_argument('--n_chains', type=int, default=None,
                        help='并行链数（默认为None，使用n_samples的1/128）')
    parser.add_argument('--d_max', type=float, default=None,
                        help='最大交换距离（默认为None，使用晶格最大距离）')
    args = parser.parse_args(args)

    # 获取参数
    L = args.L
    J2 = args.J2
    J1_values = sorted(args.J1_values)  # 确保参数值是排序的
    num_layers = args.num_layers
    d_model = args.d_model
    n_heads = args.n_heads
    patch_size = args.patch_size
    checkpoint_name = args.checkpoint
    n_samples = args.n_samples
    n_chains = args.n_chains
    d_max = args.d_max

    # 处理空字符串的情况（从配置文件传递的空值）
    if checkpoint_name == "":
        checkpoint_name = None
    
    # 如果未指定checkpoint，将在load_parameters_func中为每个J1值动态查找
    if checkpoint_name is None:
        print("将自动为每个J1值查找最新的checkpoint")

    # Infidelity测量结果保存到measurements/infidelity
    infidelity_dir = f"measurements/infidelity/L={L}/J2={J2:.2f}"
    os.makedirs(infidelity_dir, exist_ok=True)

    # 创建日志文件
    log_file = os.path.join(infidelity_dir,
                           f"infidelity_L={L}_J2={J2:.2f}_L{num_layers}D{d_model}H{n_heads}P{patch_size}.log")

    try:
        log_message(log_file, "="*80)
        log_message(log_file, "Infidelity 分析")
        log_message(log_file, "="*80)
        log_message(log_file, f"晶格大小: L={L}")
        log_message(log_file, f"J2 耦合强度: {J2:.2f}")
        log_message(log_file, f"J1 值列表: {J1_values}")
        log_message(log_file, f"ViT模型配置: {num_layers} 层, d_model={d_model}, n_heads={n_heads}, patch_size={patch_size}")
        log_message(log_file, f"Checkpoint: {checkpoint_name if checkpoint_name else '自动查找最新'}")
        log_message(log_file, f"采样数: {n_samples}")
        log_message(log_file, "="*80)

        # 定义加载参数的函数（而不是加载完整的量子态）
        def load_parameters_func(J1):
            """加载指定 J1 值的模型参数"""
            # 从saved_models加载checkpoint
            model_dir = f"saved_models/L={L}/J2={J2:.2f}/J1={J1:.2f}"
            checkpoint_dir = os.path.join(model_dir, "checkpoints")
            
            # 如果未指定checkpoint，为这个J1值查找最新的
            if checkpoint_name is None:
                latest_checkpoint = find_latest_checkpoint(checkpoint_dir)
                if latest_checkpoint is None:
                    raise FileNotFoundError(f"在 {checkpoint_dir} 中未找到checkpoint文件")
                checkpoint_file = os.path.join(checkpoint_dir, f"{latest_checkpoint}.pkl")
                log_message(log_file, f"J1={J1:.2f} 自动选择checkpoint: {latest_checkpoint}")
            else:
                checkpoint_file = os.path.join(checkpoint_dir, f"{checkpoint_name}.pkl")
            
            # 检查文件存在性
            if not os.path.exists(checkpoint_file):
                raise FileNotFoundError(f"未找到checkpoint文件: {checkpoint_file}")
            
            log_message(log_file, f"加载 J1={J1:.2f} 的参数: {checkpoint_file}")
            
            # 加载checkpoint数据
            with open(checkpoint_file, "rb") as f:
                checkpoint_data = pickle.load(f)
            
            # 从checkpoint数据中提取参数
            if not isinstance(checkpoint_data, dict) or 'parameters' not in checkpoint_data:
                raise ValueError(f"无效的checkpoint格式: {checkpoint_file}")
            
            parameters = checkpoint_data['parameters']
            if checkpoint_data.get('energy'):
                energy = checkpoint_data['energy']
                log_message(log_file, f"  ✓ 加载参数: {checkpoint_data.get('iteration', 'unknown')}")
                log_message(log_file, f"    - 能量: {energy['mean']:.6f} ± {energy['error']:.6f}")
            
            return parameters
        
        # 创建共享的模型和希尔伯特空间
        # 关键：使用 Infidelity_SR 时，两个 MCState 必须共享相同的 model 实例！
        log_message(log_file, "创建共享的模型用于 Infidelity 计算...")
        vqs_template, lattice, hilbert, hamiltonian = create_quantum_state(
            L, J2, J1_values[0],  # 使用第一个 J1 值作为模板
            n_samples=2**12,
            n_chains=n_chains,
            n_discard=0,
            chunk_size=2**10,
            d_max=d_max,
            num_layers=num_layers,
            d_model=d_model,
            n_heads=n_heads,
            patch_size=patch_size
        )
        
        # 提取共享的组件
        model = vqs_template.model
        
        log_message(log_file, f"✓ 共享模型创建完成")
        log_message(log_file, f"  - 模型类型: {type(model)}")
        log_message(log_file, f"  - Hilbert 空间: {hilbert}")

        # 执行 Infidelity 扫描
        results = calculate_infidelity_scan(
            parameter_values=J1_values,
            load_params_func=load_parameters_func,
            model=model,
            hilbert=hilbert,
            n_samples=n_samples,
            save_dir=infidelity_dir,
            log_file=log_file
        )

        # 绘制结果
        plot_infidelity_scan(results, save_dir=infidelity_dir, log_file=log_file)

        log_message(log_file, "="*80)
        log_message(log_file, "Infidelity 分析完成")
        log_message(log_file, "="*80)

    except Exception as e:
        log_message(log_file, "!"*80)
        log_message(log_file, f"分析过程中出错: {str(e)}")
        log_message(log_file, traceback.format_exc())
        log_message(log_file, "!"*80)
        raise


if __name__ == "__main__":
    main()

